package com.topsec.crm.contract.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

import com.topsec.crm.framework.common.web.domain.BaseEntity;
import lombok.Data;

/**
 * 合同评审-质保金条款表
 * @TableName crm_contract_review_retention_money
 */
@TableName(value ="crm_contract_review_retention_money")
@Data
public class CrmContractReviewRetentionMoney extends BaseEntity implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 主表id
     */
    private String contractReviewMainId;

    /**
     * 默认确认 0-按比例 1-按金额
     */
    private Integer retentionType;

    /**
     * 质保金金额
     */
    private BigDecimal retentionMoney;

    /**
     * 质保金比例
     */
    private BigDecimal retentionRatio;

    /**
     * 到期日
     */
    private LocalDate expiringDate;

    /**
     * 条款描述
     */
    private String description;

    /**
     * 标记删除 0-未删除 1-已删除
     */
    private Boolean delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}