package com.topsec.crm.contract.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.account.api.client.RemoteAccountService;
import com.topsec.crm.contract.api.entity.CrmContractBaseInfoVO;
import com.topsec.crm.contract.api.entity.CrmContractProductOwnVO;
import com.topsec.crm.contract.api.entity.keyindustryproduct.ReturnChangePageVO;
import com.topsec.crm.contract.api.entity.keyindustryproduct.ReturnChangeQuery;
import com.topsec.crm.contract.api.entity.request.CrmProductSnInQuery;
import com.topsec.crm.contract.api.entity.response.CrmProductSnInDTO;
import com.topsec.crm.contract.core.entity.CrmContractExecute;
import com.topsec.crm.contract.core.entity.CrmContractReviewMain;
import com.topsec.crm.contract.core.entity.CrmContractReviewProductOwn;
import com.topsec.crm.contract.core.entity.CrmContractReviewProductSnIn;
import com.topsec.crm.contract.core.mapper.CrmContractReviewProductSnInMapper;
import com.topsec.crm.contract.core.service.CrmContractExecuteService;
import com.topsec.crm.contract.core.service.CrmContractReviewMainService;
import com.topsec.crm.contract.core.service.CrmContractReviewProductOwnService;
import com.topsec.crm.contract.core.service.CrmContractReviewProductSnInService;
import com.topsec.crm.flow.api.RemoteContractReviewFlowService;
import com.topsec.crm.flow.api.dto.contractreview.sninfo.ContractProductSnVO;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.sql.SqlUtil;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.process.DetailBaseVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@RequiredArgsConstructor
public class CrmContractReviewProductSnInServiceImpl extends ServiceImpl<CrmContractReviewProductSnInMapper, CrmContractReviewProductSnIn>
    implements CrmContractReviewProductSnInService{

    private final CrmContractReviewProductOwnService crmContractReviewProductOwnService;
    private final CrmContractReviewMainService crmContractReviewMainService;
    private final RemoteAccountService remoteAccountService;
    private final TosEmployeeClient tosEmployeeClient;
    private final CrmContractExecuteService contractExecuteService;
    private final RemoteContractReviewFlowService remoteContractReviewFlowService;

    @Override
    public PageUtils<CrmProductSnInDTO> pageProductSnInByParams(CrmProductSnInQuery query) {
        // 创建分页对象
        Integer pageNum = query.getPageNum();
        Integer pageSize = query.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(query.getOrderBy());
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personList = dataScopeParam != null ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;
        // 这个对象是分页后的
        List<CrmProductSnInDTO> crmProductSnInDTO = baseMapper.pageProductSnInByParams(query,personList);
        PageUtils<CrmProductSnInDTO> page = new PageUtils<>();
        page.setTotalCount((int) (new PageInfo<>(crmProductSnInDTO).getTotal()));
        page.setList(crmProductSnInDTO);

        if (CollectionUtils.isEmpty(crmProductSnInDTO)) {
            return page;
        }

        // 拼产品的信息进去
        List<String> contractRecordIds = crmProductSnInDTO.stream().map(CrmProductSnInDTO::getContractRecordId).collect(Collectors.toList());
        List<CrmContractReviewProductOwn> list = crmContractReviewProductOwnService.list(new LambdaQueryWrapper<CrmContractReviewProductOwn>()
                .eq(CrmContractReviewProductOwn::getDelFlag, false)
                .in(CrmContractReviewProductOwn::getId, contractRecordIds));
        Map<String, CrmContractReviewProductOwn> productOwnMap = ListUtils.emptyIfNull(list).stream().collect(Collectors.toMap(CrmContractReviewProductOwn::getId, item -> item, (oldV, newV) -> newV));
        page.getList().forEach(item -> {
            CrmContractReviewProductOwn productOwn = productOwnMap.get(item.getContractRecordId());
            if (productOwn != null) {
                item.setStuffCode(productOwn.getStuffCode());
                item.setProductCategory(productOwn.getProductCategory());
                item.setPnCode(productOwn.getPnCode());
                item.setProductLic(productOwn.getProductLic());
                item.setDealPrice(productOwn.getDealPrice());
            }
        });
        return page;
    }

    @Override
    public Boolean setAssignState(String id) {
        CrmContractReviewProductSnIn snIn = getById(id);
        if (snIn != null){
            snIn.setState(true);
            return updateById(snIn);
        }
        return false;
    }

    @Override
    @Transactional
    public Boolean setAssignStateBatch(Set<String> sns) {
        return lambdaUpdate()
                .in(CrmContractReviewProductSnIn::getSn, sns)
                .set(CrmContractReviewProductSnIn::getState, true)
                .update();
    }

    @Override
    public Boolean removeAssignState(String id) {
        CrmContractReviewProductSnIn snIn = getById(id);
        if (snIn != null){
            snIn.setState(false);
            return updateById(snIn);
        }
        return false;
    }

    @Override
    @Transactional
    public Boolean removeAssignStateBatch(List<String> ids) {
        return lambdaUpdate()
                .in(CrmContractReviewProductSnIn::getId, ids)
                .set(CrmContractReviewProductSnIn::getState, false)
                .update();
    }

    @Override
    public List<CrmProductSnInDTO> getAllNotAssignedSn(CrmProductSnInQuery snIn) {
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personList = dataScopeParam != null ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;
        List<CrmContractReviewProductSnIn> productSnIns = baseMapper.selectList(new LambdaQueryWrapper<CrmContractReviewProductSnIn>()
                .eq(CrmContractReviewProductSnIn::getDelFlag, false)
                .eq(CrmContractReviewProductSnIn::getState, false)
                .like(StringUtils.isNotBlank(snIn.getSn()), CrmContractReviewProductSnIn::getSn, snIn.getSn())
                .last("limit 50"));
        // 查询合同销售人员
        Map<String, CrmContractBaseInfoVO> contractInfoByIds = getContractInfoByIds(productSnIns.stream().map(CrmContractReviewProductSnIn::getContractId).toList());
        // 权限点
        if (CollectionUtils.isNotEmpty(personList)){
            productSnIns = productSnIns.stream()
                    .filter(item -> {
                        assert contractInfoByIds != null;
                        return personList.contains(contractInfoByIds.get(item.getContractId()).getSaleId());
                    }).toList();
        }
        return HyperBeanUtils.copyListProperties(productSnIns, CrmProductSnInDTO::new);
    }


    @Override
    public List<CrmProductSnInDTO> getBySnList(List<String> sns) {
        List<CrmContractReviewProductSnIn> list = Optional.ofNullable(sns)
                .filter(snList -> !snList.isEmpty())
                .map(snList -> list(new LambdaQueryWrapper<CrmContractReviewProductSnIn>()
                        .eq(CrmContractReviewProductSnIn::getDelFlag, false)
                        .in(CrmContractReviewProductSnIn::getSn, snList)))
                .orElse(Collections.emptyList());

        return ListUtils.emptyIfNull(list).stream().map(snIn -> {
            CrmProductSnInDTO crmProductSnInDTO = new CrmProductSnInDTO();
            crmProductSnInDTO.setSn(snIn.getSn());
            crmProductSnInDTO.setContractCompanyId(snIn.getContractCompanyId());
            crmProductSnInDTO.setContractCompanyName(snIn.getContractCompanyName());
            return crmProductSnInDTO;
        }).toList();
    }

    @Override
    public CrmProductSnInDTO getBySn(String sn) {
        CrmContractReviewProductSnIn contractReviewProductSnIn = baseMapper.selectOne(new LambdaQueryWrapper<CrmContractReviewProductSnIn>()
                .eq(CrmContractReviewProductSnIn::getSn, sn)
                .eq(CrmContractReviewProductSnIn::getDelFlag, false)
                .last("limit 1"));
        CrmProductSnInDTO productSnIn = HyperBeanUtils.copyProperties(contractReviewProductSnIn, CrmProductSnInDTO::new);
        CrmContractProductOwnVO productOwnVO = crmContractReviewProductOwnService.getById(productSnIn.getContractRecordId());
        CrmContractReviewMain contractReviewMain = crmContractReviewMainService.getById(productOwnVO.getContractReviewId());
        productSnIn.setProductName(productOwnVO.getProductName());
        productSnIn.setProductCategory(productOwnVO.getProductCategory());
        productSnIn.setPnCode(productOwnVO.getPnCode());
        productSnIn.setStuffCode(productOwnVO.getStuffCode());
        productSnIn.setDealPrice(productOwnVO.getDealPrice());
        productSnIn.setProductLic(productOwnVO.getProductLic());
        productSnIn.setProductLic(productOwnVO.getProductLic());
        productSnIn.setProductSpecification(productOwnVO.getProductSpecification());
        productSnIn.setContractNumber(contractReviewMain.getContractNumber());
        productSnIn.setSaleName(contractReviewMain.getSaleName());
        productSnIn.setSaleId(contractReviewMain.getSaleId());
        productSnIn.setSaleDeptName(contractReviewMain.getSaleDeptName());
        productSnIn.setValidDate(contractReviewMain.getValidDate());
        return productSnIn;
    }

    @Override
    public List<CrmProductSnInDTO> productListByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        List<CrmContractReviewProductSnIn> productSnIns = baseMapper.selectList(
                new LambdaQueryWrapper<CrmContractReviewProductSnIn>()
                        .in(CrmContractReviewProductSnIn::getId, ids)
                        .eq(CrmContractReviewProductSnIn::getDelFlag, false)
        );
        List<CrmProductSnInDTO> snInList = HyperBeanUtils.copyListProperties(productSnIns, CrmProductSnInDTO::new);
        Map<String, CrmContractReviewProductOwn> productInfoByIds = getProductInfoByIds(productSnIns.stream().map(CrmContractReviewProductSnIn::getContractRecordId).toList());
        snInList.forEach(item ->{
            assert productInfoByIds != null;
            item.setProductLic(productInfoByIds.get(item.getContractRecordId()).getProductLic());
            item.setDealPrice(productInfoByIds.get(item.getContractRecordId()).getDealPrice());
            item.setProductSpecification(productInfoByIds.get(item.getContractRecordId()).getProductSpecification());
            item.setPnCode(productInfoByIds.get(item.getContractRecordId()).getPnCode());
            item.setStuffCode(productInfoByIds.get(item.getContractRecordId()).getStuffCode());
            item.setProductName(productInfoByIds.get(item.getContractRecordId()).getProductName());
        });
        Map<String, CrmContractBaseInfoVO> contractInfoByIds = getContractInfoByIds(productSnIns.stream().map(CrmContractReviewProductSnIn::getContractId).toList());
        snInList.forEach(item ->{
            assert contractInfoByIds != null;
            CrmContractBaseInfoVO contractBaseInfoVO = contractInfoByIds.get(item.getContractId());
            if (contractBaseInfoVO != null) {
                item.setSaleName(contractBaseInfoVO.getSaleName());
                item.setSaleId(contractBaseInfoVO.getSaleId());
                item.setSaleDeptName(contractBaseInfoVO.getSaleDeptName());
                item.setContractNumber(contractBaseInfoVO.getContractNumber());
            }
        });
        return snInList;
    }

    @Override
    public PageUtils<ReturnChangePageVO> getReturnChangeSnPage(ReturnChangeQuery req) {

        String approvePersonId = req.getApprovePersonId();
        if (StringUtils.isBlank(approvePersonId)) {
            throw new CrmException("审批人为空");
        }
        if (StringUtils.isBlank(req.getSigningCompanyId())) {
            throw new CrmException("签订公司ID为空");
        }

        // 获取一级部门ID
        String deptId = Optional.ofNullable(tosEmployeeClient.findById(approvePersonId))
                .map(JsonObject::getObjEntity)
                .map(DetailBaseVO::getDept)
                .map(dept -> dept.getDeptIdByLevel(1)).orElseThrow();

        // 获取部门所有人员ID
        List<String> personIds = Optional.ofNullable(remoteAccountService.findEmployeeByDeptId(deptId))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream()
                .map(DetailBaseVO::getUuid).toList();

        if (personIds.isEmpty()) {
            return new PageUtils<>();
        }

        // 审批人对应的一级平台下所有与退换货合同的签订公司一致的合同
        List<String> contractNumbers = contractExecuteService
                .lambdaQuery()
                .eq(CrmContractExecute::getDelFlag, false)
                .in(CrmContractExecute::getContractOwnerId, personIds)
                .eq(CrmContractExecute::getSigningCompany, req.getSigningCompanyId())
                .list().stream().map(CrmContractExecute::getContractNumber).toList();

        if (contractNumbers.isEmpty()){
            return new PageUtils<>();
        }

        // 获取所有合同
        List<CrmContractReviewMain> contractList = crmContractReviewMainService
                .lambdaQuery()
                .eq(CrmContractReviewMain::getDelFlag, false)
                .in(CrmContractReviewMain::getContractNumber, contractNumbers).list();

        // 查询合同下所有序列号
        List<String> contractIds = contractList.stream().map(CrmContractReviewMain::getId).toList();
        List<CrmContractReviewProductSnIn> snInList = lambdaQuery()
                .in(CrmContractReviewProductSnIn::getContractId, contractIds)
                .eq(CrmContractReviewProductSnIn::getDelFlag, false)
                .list();

        List<ReturnChangePageVO> result = HyperBeanUtils.copyListProperties(snInList, ReturnChangePageVO::new);

        // 序列号状态
        Map<String, Integer> snStateMap = remoteContractReviewFlowService
                .queryStateBySn(result.stream().map(ReturnChangePageVO::getSn).toList()).getObjEntity();

        // 只取未出货的序列号
        result.removeIf(item -> ContractProductSnVO.SnTypeEnum.IN_CONTRACT.getCode()
                .equals(snStateMap.get(item.getSn())));

        // 产品信息
        Map<String, CrmContractReviewProductOwn> reviewProductOwnMap = getProductInfoByIds(
                result.stream().map(ReturnChangePageVO::getContractRecordId).toList());

        // 合同信息
        Map<String, CrmContractReviewMain> reviewMainMap = contractList.stream()
                .collect(Collectors.toMap(CrmContractReviewMain::getId, item -> item));

        // 填充其他字段
        result.forEach(item -> {
            assert reviewProductOwnMap != null;
            CrmContractReviewProductOwn productOwn = reviewProductOwnMap.get(item.getContractRecordId());
            if (productOwn != null) {
                item.setProductName(productOwn.getProductName());
                item.setPnCode(productOwn.getPnCode());
            }
            CrmContractReviewMain main = reviewMainMap.get(item.getContractId());
            if (main != null) {
                item.setContactNumber(main.getContractNumber());
            }
        });

        // 根据参数二次过滤（前端传参）
        result = result.stream()
                .filter(item -> {
                    boolean match = false;
                    if (StringUtils.isNotBlank(req.getProductName())) {
                        match |= item.getProductName() != null &&
                                item.getProductName().contains(req.getProductName());
                    }
                    if (StringUtils.isNotBlank(req.getSn())) {
                        match |= item.getSn() != null && item.getSn().contains(req.getSn());
                    }
                    if (StringUtils.isNotBlank(req.getContractNumber())){
                        match |= item.getContactNumber() != null &&
                                item.getContactNumber().contains(req.getContractNumber());
                    }
                    return match;
                }).toList();

        // 返回分页结果
        PageUtils<ReturnChangePageVO> page = new PageUtils<>();
        page.setTotalCount((int) new PageInfo<>(result).getTotal());
        page.setList(result);
        return page;
    }

    @Override
    public List<CrmProductSnInDTO> getReturnChangeSnList(Set<String> contractIds) {
        List<CrmContractReviewProductSnIn> productSnIns = baseMapper.selectList(new LambdaQueryWrapper<CrmContractReviewProductSnIn>()
                .in(CrmContractReviewProductSnIn::getContractId, contractIds)
                .eq(CrmContractReviewProductSnIn::getDelFlag, false)
        );
        return HyperBeanUtils.copyListProperties(productSnIns, CrmProductSnInDTO::new);
    }

    /**
     * 根据合同-产品行ID获取产品信息
     * 批量
     * @param ids contractRecordId
     * @return 产品进货信息
     */
    private Map<String, CrmContractReviewProductOwn> getProductInfoByIds(List<String> ids) {
        if (ids.isEmpty()){
            return null;
        }
        return crmContractReviewProductOwnService.list(new LambdaQueryWrapper<CrmContractReviewProductOwn>()
                        .eq(CrmContractReviewProductOwn::getDelFlag, false)
                        .in(CrmContractReviewProductOwn::getId, ids))
                .stream().collect(Collectors.toMap(CrmContractReviewProductOwn::getId, item -> item));
    }

    /**
     * 合同信息
     */
    private Map<String, CrmContractBaseInfoVO> getContractInfoByIds(List<String> ids) {
        if (ids.isEmpty()){
            return null;
        }
        return crmContractReviewMainService.getByContractIdBatch(ids)
                .stream().collect(Collectors.toMap(CrmContractBaseInfoVO::getId, item -> item));
    }
}




