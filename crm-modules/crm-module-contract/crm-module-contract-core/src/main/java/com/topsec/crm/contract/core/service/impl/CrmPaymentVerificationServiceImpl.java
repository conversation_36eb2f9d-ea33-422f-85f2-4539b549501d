package com.topsec.crm.contract.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteUpdateVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.paymentcollection.*;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.contract.core.entity.CrmContractExecute;
import com.topsec.crm.contract.core.entity.CrmPaymentCollection;
import com.topsec.crm.contract.core.entity.CrmPaymentVerification;
import com.topsec.crm.contract.core.mapper.CrmPaymentVerificationMapper;
import com.topsec.crm.contract.core.service.CrmContractExecuteService;
import com.topsec.crm.contract.core.service.CrmPaymentCollectionExtendService;
import com.topsec.crm.contract.core.service.CrmPaymentVerificationService;
import com.topsec.crm.erp.api.RemoteAuditRecordService;
import com.topsec.crm.file.api.RemoteFsmDocService;
import com.topsec.crm.flow.api.RemoteFlowBorrowForProbationService;
import com.topsec.crm.flow.api.RemoteFlowPaymentVerificationService;
import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowFlowDeviceVO;
import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowFlowPartVO;
import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowFlowQuery;
import com.topsec.crm.flow.api.dto.paymentverification.VerificationConvertGoodsDTO;
import com.topsec.crm.flow.api.dto.paymentverification.VerificationRefundDTO;
import com.topsec.crm.framework.common.bean.CrmFsmDoc;
import com.topsec.crm.framework.common.bean.ProcessExtensionInfoVO;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.operation.api.RemoteContractReviewConfigService;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
@Slf4j
public class CrmPaymentVerificationServiceImpl extends ServiceImpl<CrmPaymentVerificationMapper, CrmPaymentVerification>
        implements CrmPaymentVerificationService {

    private final RemoteAuditRecordService auditRecordService;

    private final CrmPaymentCollectionExtendService paymentCollectionExtendService;

    private final TfsNodeClient tfsNodeClient;

    private final CrmContractExecuteService contractExecuteService;

    private final RemoteFlowBorrowForProbationService flowBorrowForProbationService;

    private final RemoteContractReviewConfigService remoteContractReviewConfigService;

    private final RemoteFsmDocService fsmDocService;

    private final RemoteFlowPaymentVerificationService flowPaymentVerificationService;

    @Override
    public List<PaymentVerificationVO> getListByPaymentCollectionId(String paymentCollectionId,boolean isVerification) {

        // 预付款页面获取核销列表
        if (!isVerification){
            PaymentCollectionVO paymentCollectionVO = paymentCollectionExtendService.getPaymentCollectionById(paymentCollectionId);
            // paymentCollectionVO.getVerifiedAmount()>0，提示无权限
            if (paymentCollectionVO.getVerifiedAmount().compareTo(BigDecimal.ZERO) > 0){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }

        List<CrmPaymentVerification> crmPaymentVerifications = baseMapper.selectList(new LambdaQueryWrapper<CrmPaymentVerification>()
                .eq(CrmPaymentVerification::getDelFlag, false)
                .eq(CrmPaymentVerification::getCollectionId, paymentCollectionId)
        );
        if (crmPaymentVerifications == null || crmPaymentVerifications.isEmpty()) {
            return Collections.emptyList();
        }
        List<PaymentVerificationVO> list = HyperBeanUtils.copyListProperties(crmPaymentVerifications, PaymentVerificationVO::new);

        // 权限过滤
        List<PaymentVerificationVO> voList = authFilter(list);
        if (CollectionUtils.isEmpty(voList)){
            return Collections.emptyList();
        }

        // 可操作状态
        List<DataOperationCheckVO> operationCheckList = getOperationCheckList(voList);

        // 已核销金额
        List<String> businessNumbers = voList.stream().map(PaymentVerificationVO::getBusinessNumber).toList();
        Map<String, BigDecimal> verifiedAmountsMapByBusinessNumbers = getVerifiedAmountsMapByBusinessNumbers(businessNumbers);

        // 签约单位/付款对象
        Map<Integer, List<String>> verificationTypeMap = voList.stream()
                .collect(Collectors.groupingBy(PaymentVerificationVO::getVerificationType,
                        Collectors.mapping(PaymentVerificationVO::getBusinessNumber, Collectors.toList())));
        Map<String, String> payerMapByBusinessNumbers = getPayerMapByBusinessNumbers(verificationTypeMap);

        // 填充列表数据
        return voList.stream().peek(vo -> {
            operationCheckList.stream()
                    .filter(check -> check.getDataId().equals(vo.getId()))
                    .findFirst()
                    .ifPresent(vo::setDataOperationCheck);
            // 只有押金 货款 保证金才计算欠款金额
            if (Objects.equals(vo.getVerificationType(), PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode()) ||
                Objects.equals(vo.getVerificationType(), PaymentVerificationVO.VerificationTypeEnum.DEPOSIT.getCode()) ||
                Objects.equals(vo.getVerificationType(), PaymentVerificationVO.VerificationTypeEnum.SECURITY_FUND.getCode())
            ) {
                vo.setUnVerifiedAmount(vo.getBusinessAmount().subtract(verifiedAmountsMapByBusinessNumbers.getOrDefault(vo.getBusinessNumber(), BigDecimal.ZERO)));
            }
            vo.setPayer(payerMapByBusinessNumbers.getOrDefault(vo.getBusinessNumber(), null));
        }).collect(Collectors.toList());
    }

    // 权限过滤
    private List<PaymentVerificationVO> authFilter(List<PaymentVerificationVO> list){

        if (CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }

        Set<String> personIdList = PreAuthorizeAspect.getDataScopeParam().getPersonIdList();
        // null 为最大权限
        if(CollectionUtils.isEmpty(personIdList)){
            return list;
        }
        // 借试用
        CompletableFuture<List<String>> borrowNumberFuture = CompletableFuture.supplyAsync(() -> {
            BorrowFlowQuery borrowFlowQuery = new BorrowFlowQuery();
            borrowFlowQuery.setBorrowerPersonIds(personIdList);
            return flowBorrowForProbationService.borrowForProbationList(borrowFlowQuery)
                    .getObjEntity()
                    .parallelStream()
                    .map(BorrowFlowPartVO::getProcessNumber)
                    .toList();
        });

        // 合同
        CompletableFuture<List<String>> contractNumberFuture = CompletableFuture.supplyAsync(() -> {
            CrmContractAfterQuery crmContractAfterQuery = new CrmContractAfterQuery();
            CrmContractAfterQuery.BaseQuery baseQuery = new CrmContractAfterQuery.BaseQuery();
            baseQuery.setContractOwnerIds(personIdList.stream().toList());
            crmContractAfterQuery.setBaseQuery(baseQuery);
            return contractExecuteService.pageByCondition(crmContractAfterQuery)
                    .getList()
                    .parallelStream()
                    .map(CrmContractExecuteVO::getContractNumber)
                    .toList();
        });

        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(borrowNumberFuture, contractNumberFuture);
        try {
            combinedFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("数据获取失败", e);
        }

        List<String> borrowNumber = borrowNumberFuture.join();
        List<String> contractNumber = contractNumberFuture.join();
        // TODO 借款

        // 预付款 不过滤
        // 借试用 过滤掉不在borrowNumber
        // 货款 过滤掉不在contractNumber中的
        // 退款 过滤掉createUser不在personIdList中的
        return list.stream()
                .filter(item -> {
                    if (item.getVerificationType().equals(PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode())) {
                        return true;
                    }
                    if (item.getVerificationType().equals(PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode())) {
                        return contractNumber.contains(item.getBusinessNumber());
                    }
                    if (item.getVerificationType().equals(PaymentVerificationVO.VerificationTypeEnum.DEPOSIT.getCode())) {
                        return borrowNumber.contains(item.getBusinessNumber());
                    }
                    if (item.getVerificationType().equals(PaymentVerificationVO.VerificationTypeEnum.REFUND.getCode())) {
                        return borrowNumber.contains(item.getBusinessNumber());
                    }
                    return false;
                }).toList();
    }

    /**
     * 根据业务编号获取签约单位/付款对象
     */
    private Map<String, String> getPayerMapByBusinessNumbers(Map<Integer, List<String>> businessNumberMap) {
        // 获取不同类型的业务编号
        Set<String> contractNumberSet = new HashSet<>(
                businessNumberMap.get(PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode()) != null
                        ? businessNumberMap.get(PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode())
                        : Collections.emptySet()
        );
        List<String> depositNumbers = businessNumberMap.get(PaymentVerificationVO.VerificationTypeEnum.DEPOSIT.getCode());
        List<String> loanNumbers = businessNumberMap.get(PaymentVerificationVO.VerificationTypeEnum.SECURITY_FUND.getCode());

        // 合同
        Map<String, String> contractMap = Optional.ofNullable(contractExecuteService.getByContractNumberBatch(contractNumberSet))
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(CrmContractExecuteVO::getContractNumber, CrmContractExecuteVO::getContractCompanyName));

        // 押金
        Map<String, String> depositMap = new HashMap<>();
        if (depositNumbers != null && !depositNumbers.isEmpty()) {
            BorrowFlowQuery borrowFlowQuery = new BorrowFlowQuery();
            List<BorrowFlowPartVO> borrowFlows = flowBorrowForProbationService.borrowForProbationList(borrowFlowQuery).getObjEntity();
            Map<String, String> tempDepositMap = borrowFlows.stream()
                    .collect(Collectors.toMap(
                            BorrowFlowPartVO::getProcessNumber,
                            BorrowFlowPartVO::getMiddlemanName
                    ));
            depositNumbers.forEach(depositNumber -> depositMap.put(depositNumber, tempDepositMap.getOrDefault(depositNumber, "")));
        }

        // TODO 借款
        Map<String, String> loanMap = new HashMap<>();

        // 合并 contractMap, depositMap, loanMap 并返回
        return Stream.of(contractMap, depositMap, loanMap)
                .filter(map -> !map.isEmpty())
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (value1, value2) -> value1
                ));
    }

    @Override
    public List<PaymentVerificationVO> getListByPaymentCollectionIds(List<String> paymentCollectionIds) {
        if (paymentCollectionIds == null || paymentCollectionIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<CrmPaymentVerification> crmPaymentVerifications = baseMapper.selectList(new LambdaQueryWrapper<CrmPaymentVerification>()
                .eq(CrmPaymentVerification::getDelFlag, false)
                .in(CrmPaymentVerification::getCollectionId, paymentCollectionIds)
        );
        return HyperBeanUtils.copyListProperties(crmPaymentVerifications, PaymentVerificationVO::new);
    }

    /**
     * 根据预付款IDs获取预付款已核销金额Map
     * @param paymentCollectionIds 预付款IDs
     */
    @Override
    @Transactional
    public Map<String, BigDecimal> getVerifiedAmountsMapByPaymentCollectionIds(List<String> paymentCollectionIds) {

        if (paymentCollectionIds == null || paymentCollectionIds.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, BigDecimal> collect = baseMapper.selectList(new LambdaQueryWrapper<CrmPaymentVerification>()
                .eq(CrmPaymentVerification::getDelFlag, false)
                .ne(CrmPaymentVerification::getVerificationType, PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode())
                .in(CrmPaymentVerification::getCollectionId, paymentCollectionIds)
        ).stream().collect(Collectors.groupingBy(
                CrmPaymentVerification::getCollectionId,
                Collectors.reducing(BigDecimal.ZERO, CrmPaymentVerification::getVerificationAmount, BigDecimal::add)
        ));
        return paymentCollectionIds.stream()
                .collect(Collectors.toMap(
                        id -> id,
                        id -> collect.getOrDefault(id, BigDecimal.ZERO)
                ));
    }

    @Override
    public Map<String, List<PaymentVerificationVO>> getVerificationMapByCollectionIds(List<String> paymentCollectionIds, boolean isVerification) {

        if (paymentCollectionIds == null || paymentCollectionIds.isEmpty()) {
            return Collections.emptyMap();
        }

        List<PaymentVerificationVO> paymentVerificationVOS = Optional.ofNullable(baseMapper.selectList(new LambdaQueryWrapper<CrmPaymentVerification>()
                        .eq(CrmPaymentVerification::getDelFlag, false)
                        .in(CrmPaymentVerification::getCollectionId, paymentCollectionIds)
                        .eq(!isVerification, CrmPaymentVerification::getVerificationType, PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode())
                )).map(list -> HyperBeanUtils.copyListProperties(list, PaymentVerificationVO::new))
                .orElse(List.of());
        List<PaymentVerificationVO> list = authFilter(paymentVerificationVOS);
        return paymentCollectionIds.stream()
                .collect(Collectors.toMap(
                        id -> id,
                        id -> list.stream().filter(paymentVerificationVO -> paymentVerificationVO.getCollectionId().equals(id))
                                .collect(Collectors.toList())
                ));

    }

    @Override
    public List<PaymentVerificationProcessVO> getPaymentVerificationProcessList(String paymentCollectionId,Boolean isVerification) {

        // 预付款页面获取流程列表
        if (!isVerification){
            PaymentCollectionVO paymentCollectionVO = paymentCollectionExtendService.getPaymentCollectionById(paymentCollectionId);
            if (paymentCollectionVO.getVerifiedAmount().compareTo(BigDecimal.ZERO) > 0){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }

        List<PaymentVerificationVO> verificationVOList = getListByPaymentCollectionIds(List.of(paymentCollectionId));
        if (verificationVOList.isEmpty()){
            return Collections.emptyList();
        }
        // 权限过滤
        List<PaymentVerificationVO> authFilterList = authFilter(verificationVOList);
        if (CollectionUtils.isEmpty(authFilterList)){
            return Collections.emptyList();
        }

        List<String> verificationIds = authFilterList.stream().map(PaymentVerificationVO::getId).toList();
        List<ProcessExtensionInfoVO> refundFlowLaunchDTOS = flowPaymentVerificationService.getRefundLaunchInfoList(verificationIds).getObjEntity();
        List<ProcessExtensionInfoVO> convertGoodsFlowLaunchDTOS = flowPaymentVerificationService.getConvertGoodsLaunchInfoList(verificationIds).getObjEntity();
        ArrayList<PaymentVerificationProcessVO> result = new ArrayList<>();
        result.addAll(HyperBeanUtils.copyListProperties(refundFlowLaunchDTOS, PaymentVerificationProcessVO::new));
        result.addAll(HyperBeanUtils.copyListProperties(convertGoodsFlowLaunchDTOS, PaymentVerificationProcessVO::new));

        if (result.isEmpty()){
            return Collections.emptyList();
        }

        // 流程节点信息
        Map<String, Set<ApproveNode>> flowMap = tfsNodeClient.queryNodeByProcessInstanceIdList(result.stream().map(PaymentVerificationProcessVO::getProcessInstanceId).collect(Collectors.toList())).getObjEntity();
        result.forEach(item -> {
            item.setCreateUserName(NameUtils.getName(item.getCreateUser()));
            if (item.getProcessDefinitionKey().equals("refund")){
                item.setProcessName("退款审批");
            }
            if (item.getProcessDefinitionKey().equals("depositToPayment")){
                item.setProcessName("押金转货款");
            }
            Set<ApproveNode> approveNodes = flowMap.get(item.getProcessInstanceId());
            if (approveNodes != null) {
                item.setProcessStep(approveNodes.stream()
                        .map(ApproveNode::getNodeName)
                        .collect(Collectors.joining(",")));
            } else {
                // 处理 approveNodes 为 null 的情况
                item.setProcessStep("流程已办结");
            }
        });
        return result;
    }

    @Override
    @Transactional
    public void savePaymentVerification(List<PaymentVerificationVO> req,String collectionId,BigDecimal collectionAmount) {

        List<CrmPaymentVerification> verifications = HyperBeanUtils.copyListProperties(req, CrmPaymentVerification::new);

        // 去掉预付款类型的传参，后端自动构建
        verifications = verifications.stream()
                .filter(item -> !item.getVerificationType().equals(PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode())).toList();

        // 保存核销数据
        saveBatch(verifications);

        // 更新预付款
        updateAdvanceVerification(collectionId, collectionAmount);

        // 计算每个合同已核销金额，并按合同号分组求和
        List<String> contractNumber = verifications.stream()
                .filter(item -> item.getVerificationType().equals(PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode()))
                .map(CrmPaymentVerification::getBusinessNumber).toList();
        Map<String, BigDecimal> contractMap = getVerifiedAmountsMapByBusinessNumbers(contractNumber);
        // 更新合同执行回款金额
        if (!contractMap.isEmpty()) {
            contractMap.forEach(this::updateContractExecuteAmount);
        }
        updatePaymentCollection(req.get(0).getCollectionId());
    }

    @Override
    @Transactional
    public Boolean updatePaymentVerification(String id, BigDecimal verificationAmount,String remark) {
        CrmPaymentVerification verification = getById(id);
        if (verification == null) {
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
        PaymentVerificationVO verificationVO = HyperBeanUtils.copyProperties(verification, PaymentVerificationVO::new);

        // 检查是否可修改
        DataOperationCheckVO checkVO = createOperationCheckVO(id, verification);
        DataOperationCheckVO modify = isCanModify(checkVO,verificationVO);
        if (!modify.getIsCanModify()) {
            throw new CrmException(modify.getModifyRejectedReason());
        }

        // 修改后的核销金额不能大于当前核销金额
        BigDecimal canOperationAmount = getCanOperationAmount(verification.getId());
        if (verificationAmount.compareTo(canOperationAmount) > 0) {
            throw new CrmException("修改后的核销金额不能大于核销金额");
        }

        // 获取预付款金额
        BigDecimal collectionAmount = paymentCollectionExtendService.getPaymentCollectionById(verification.getCollectionId()).getCollectionAmount();

        verification.setVerificationAmount(verificationAmount);
        verification.setRemark(remark);

        // 更新核销金额,
         updateById(verification);
        // 更新预付款核销金额
         updateAdvanceVerification(verification.getCollectionId(),collectionAmount);
        // 如果为货款，更新合同执行回款
        if (PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode().equals(verification.getVerificationType())) {
            Map<String, BigDecimal> contractMap = getVerifiedAmountsMapByBusinessNumbers(List.of(verification.getBusinessNumber()));
            updateContractExecuteAmount(verification.getBusinessNumber(),contractMap.get(verification.getBusinessNumber()));
        }
        updatePaymentCollection(verification.getCollectionId());
        return true;
    }

    @Override
    @Transactional
    public Boolean deletePaymentVerification(String id) {
        CrmPaymentVerification verification = getById(id);
        if (verification == null) {
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
        PaymentVerificationVO verificationVO = HyperBeanUtils.copyProperties(verification, PaymentVerificationVO::new);

        // 检查是否可删除
        DataOperationCheckVO checkVO = createOperationCheckVO(id, verification);
        DataOperationCheckVO canDelete = isCanDelete(checkVO,verificationVO);
        if (!canDelete.getIsCanDelete()) {
            throw new CrmException(canDelete.getDeleteRejectedReason());
        }

        // 标记为删除 更新预付款
        verification.setDelFlag(true);

        // TODO CompletableFuture编排3个任务，同时成功才返回true
        // 删除
        updateById(verification);
        // 更新预付款核销金额
        updateAdvanceVerification(verification.getCollectionId(),paymentCollectionExtendService.getPaymentCollectionById(verification.getCollectionId()).getCollectionAmount());
        // 如果为货款，更新合同执行回款
        if (PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode().equals(verification.getVerificationType())) {
            Map<String, BigDecimal> contractMap = getVerifiedAmountsMapByBusinessNumbers(List.of(verification.getBusinessNumber()));
            updateContractExecuteAmount(verification.getBusinessNumber(),contractMap.get(verification.getBusinessNumber()));
        }
        updatePaymentCollection(verification.getCollectionId());
        return true;
    }

    @Override
    @Transactional
    public Boolean rollbackPaymentVerification(String id, BigDecimal rollbackAmount,String remark) {

        CrmPaymentVerification verification = getById(id);
        if (verification == null) {
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
        PaymentVerificationVO verificationVO = HyperBeanUtils.copyProperties(verification, PaymentVerificationVO::new);
        // 检查是否可回冲
        DataOperationCheckVO checkVO = createOperationCheckVO(id, verification);
        DataOperationCheckVO rollback = isCanRollback(checkVO,verificationVO);
        if (!rollback.getIsCanRollback()) {
            throw new CrmException(rollback.getRollbackRejectedReason());
        }

        // 回冲金额应 > 0
        if (rollbackAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new CrmException("回冲金额应 > 0");
        }

        // 计算可回冲金额
        BigDecimal canRollbackAmount = getCanOperationAmount(verification.getId());
        if (rollbackAmount.compareTo(canRollbackAmount) > 0) {
            throw new CrmException("回冲金额应 <= " + canRollbackAmount);
        }

        // 增加负行
        CrmPaymentVerification rollBackVerification = new CrmPaymentVerification();
        rollBackVerification.setCollectionId(verification.getCollectionId());
        rollBackVerification.setVerificationType(verification.getVerificationType());
        rollBackVerification.setBusinessNumber(verification.getBusinessNumber());
        rollBackVerification.setBusinessAmount(verification.getBusinessAmount());
        rollBackVerification.setVerificationAmount(rollbackAmount.negate());
        rollBackVerification.setBusinessType(verification.getBusinessType());
        rollBackVerification.setRemark(remark);
        rollBackVerification.setParentId(verification.getId());
        save(rollBackVerification);

        // 更新预付款
        updateAdvanceVerification(verification.getCollectionId(),paymentCollectionExtendService.getPaymentCollectionById(verification.getCollectionId()).getCollectionAmount());

        // 如果为货款，更新合同执行回款
        if (PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode().equals(verification.getVerificationType())) {
            Map<String, BigDecimal> contractMap = getVerifiedAmountsMapByBusinessNumbers(List.of(verification.getBusinessNumber()));
            updateContractExecuteAmount(verification.getBusinessNumber(),contractMap.get(verification.getBusinessNumber()));
        }
        updatePaymentCollection(verification.getCollectionId());
        return true;
    }

    private BigDecimal getCanOperationAmount(String verificationId){
        // 当前行
        CrmPaymentVerification verification = getById(verificationId);
        // 当前行的下级负行
        BigDecimal reduce = baseMapper.selectList(new LambdaQueryWrapper<CrmPaymentVerification>()
                .eq(CrmPaymentVerification::getParentId, verificationId)
                .eq(CrmPaymentVerification::getDelFlag, false)
        ).stream().map(CrmPaymentVerification::getVerificationAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        return verification.getVerificationAmount().add(reduce);
    }

    /**
     * 更新合同执行回款金额
     * @param contractNumber 合同号
     * @param verifiedAmount 已核销金额
     */
    private void updateContractExecuteAmount(String contractNumber,BigDecimal verifiedAmount) {
        CrmContractExecuteVO contractExecuteVO = contractExecuteService.getByContractNumber(contractNumber);
        if (contractExecuteVO == null) {
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
        CrmContractExecuteUpdateVO updateVO = new CrmContractExecuteUpdateVO();
        updateVO.setReturnedAmount(verifiedAmount);
        updateVO.setDebtAmount(contractExecuteVO.getContractAmount().subtract(verifiedAmount));
        updateVO.setContractNumber(contractExecuteVO.getContractNumber());
        contractExecuteService.updateContractExecuteByContractNumber(updateVO);
    }

    // 更新预付款核销金额，金额明细
    private void updatePaymentCollection(String collectionId){
        List<BigDecimal> verificationList = baseMapper.selectList(
                new LambdaQueryWrapper<CrmPaymentVerification>()
                        .eq(CrmPaymentVerification::getCollectionId, collectionId)
                        .ne(CrmPaymentVerification::getVerificationType, PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode())
                        .eq(CrmPaymentVerification::getDelFlag, false)
        ).stream().map(CrmPaymentVerification::getVerificationAmount).toList();
        BigDecimal verificationAmount = verificationList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        paymentCollectionExtendService.updatePaymentCollectionById(collectionId, verificationAmount,verificationList);
    }

    /**
     * 更新预付款记录 有则更新，没有新增
     *
     * @param collectionId     预付款ID
     * @param collectionAmount 预付款金额
     */
    @Transactional
    protected void updateAdvanceVerification(String collectionId, BigDecimal collectionAmount){
        // 是否有预付款明细
        CrmPaymentVerification verification = baseMapper.selectOne(new LambdaQueryWrapper<CrmPaymentVerification>()
                .eq(CrmPaymentVerification::getCollectionId, collectionId)
                .eq(CrmPaymentVerification::getDelFlag, false)
                .eq(CrmPaymentVerification::getVerificationType, PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode()));
        // 已有预付款则更新，没有则新增
        if (verification != null){
            // 查询已核销金额
            BigDecimal verifiedAmount = getVerifiedAmountsMapByPaymentCollectionIds(List.of(collectionId)).get(collectionId);
            verification.setVerificationAmount(collectionAmount.subtract(verifiedAmount));
            updateById(verification);
        } else {
            BigDecimal verifiedAmount = getVerifiedAmountsMapByPaymentCollectionIds(List.of(collectionId)).get(collectionId);
            // 如果已核销金额 = 预付款金额，直接返回，否则新增一条
            if (verifiedAmount.compareTo(collectionAmount) == 0) {
                return;
            }
            verification = new CrmPaymentVerification();
            verification.setCollectionId(collectionId);
            verification.setVerificationType(PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode());
            verification.setVerificationAmount(collectionAmount.subtract(verifiedAmount));
            save(verification);
        }
        // 查询所有核销明细
        List<BigDecimal> verificationList = baseMapper.selectList(
                new LambdaQueryWrapper<CrmPaymentVerification>()
                        .eq(CrmPaymentVerification::getCollectionId, collectionId)
                        .ne(CrmPaymentVerification::getVerificationType, PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode())
                        .eq(CrmPaymentVerification::getDelFlag, false)
        ).stream().map(CrmPaymentVerification::getVerificationAmount).toList();
        BigDecimal verificationAmount = verificationList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        paymentCollectionExtendService.updatePaymentCollectionById(collectionId, verificationAmount,verificationList);
    }

    /**
     * 根据业务编号列表批量查询已核销金额Map
     * @param businessNumbers 业务编号列表
     * @return 业务编号与已核销金额的映射
     */
    @Override
    public Map<String, BigDecimal> getVerifiedAmountsMapByBusinessNumbers(List<String> businessNumbers) {
        if (businessNumbers == null || businessNumbers.isEmpty()) {
            return Collections.emptyMap();
        }

        // 查询所有符合条件的核销记录
        List<CrmPaymentVerification> verifications = baseMapper.selectList(new LambdaQueryWrapper<CrmPaymentVerification>()
                .in(CrmPaymentVerification::getBusinessNumber, businessNumbers)
                .ne(CrmPaymentVerification::getVerificationType, PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode())
                .eq(CrmPaymentVerification::getDelFlag, false)
        );
        // 按业务编号分组并计算已核销金额
        return verifications.stream()
                .collect(Collectors.groupingBy(
                        CrmPaymentVerification::getBusinessNumber, // 按业务编号分组
                        Collectors.reducing(BigDecimal.ZERO, CrmPaymentVerification::getVerificationAmount, BigDecimal::add) // 计算每组的总金额
                ));
    }

    @Override
    public PaymentVerificationProcessVO getRefundProcessInfo(String verificationId) {
        PaymentVerificationProcessVO processVO = new PaymentVerificationProcessVO();
        if (StringUtils.isNotBlank(verificationId)) {
            CrmPaymentVerification verification = getById(verificationId);
            if (verification == null || verification.getDelFlag()) {
                throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
            }
            // 预付款信息
            PaymentCollectionVO collection = paymentCollectionExtendService.getPaymentCollectionById(verification.getCollectionId());
            PaymentVerificationProcessVO.PaymentCollectionInfo collectionInfo = new PaymentVerificationProcessVO.PaymentCollectionInfo();
            HyperBeanUtils.copyProperties(collection, collectionInfo);
            String companyName = remoteContractReviewConfigService.getBySignCompanyId(collection.getAccountingCompanyId()).getObjEntity().getCompanyName();
            collectionInfo.setAccountingCompanyName(companyName);
            collectionInfo.setUnVerifiedAmount(collection.getCollectionAmount().subtract(collection.getVerifiedAmount()));
            processVO.setPaymentCollectionInfo(collectionInfo);
            // 退款信息
            PaymentVerificationProcessVO.RefundInfo refundInfo = new PaymentVerificationProcessVO.RefundInfo();
            HyperBeanUtils.copyProperties(verification, refundInfo);
            if (!PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode().equals(verification.getVerificationType())){
                // 业务欠款金额
                BigDecimal verifiedAmount = getVerifiedAmountsMapByBusinessNumbers(List.of(verification.getBusinessNumber())).get(verification.getBusinessNumber());
                refundInfo.setUnVerifiedAmount(verification.getBusinessAmount().subtract(verifiedAmount));
            }
            // 核销剩余金额
            refundInfo.setRemainVerifiedAmount(getCanOperationAmount(verificationId));
            processVO.setRefundInfo(refundInfo);
        }
        return processVO;
    }

    @Override
    public PaymentVerificationProcessVO getConvertProcessInfo(String verificationId) {
        PaymentVerificationProcessVO processVO = new PaymentVerificationProcessVO();
        if (StringUtils.isNotBlank(verificationId)) {
            CrmPaymentVerification verification = getById(verificationId);
            if (verification == null || verification.getDelFlag()) {
                throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
            }
            // 预付款信息
            PaymentCollectionVO collection = paymentCollectionExtendService.getPaymentCollectionById(verification.getCollectionId());
            PaymentVerificationProcessVO.PaymentCollectionInfo collectionInfo = new PaymentVerificationProcessVO.PaymentCollectionInfo();
            HyperBeanUtils.copyProperties(collection, collectionInfo);
            String companyName = remoteContractReviewConfigService.getBySignCompanyId(collection.getAccountingCompanyId()).getObjEntity().getCompanyName();
            collectionInfo.setAccountingCompanyName(companyName);
            collectionInfo.setUnVerifiedAmount(collection.getCollectionAmount().subtract(collection.getVerifiedAmount()));
            processVO.setPaymentCollectionInfo(collectionInfo);
            // 押金转货款信息
            PaymentVerificationProcessVO.ConvertInfo convertInfo = new PaymentVerificationProcessVO.ConvertInfo();
            HyperBeanUtils.copyProperties(verification, convertInfo);
            // 欠款金额
            BigDecimal verifiedAmount = getVerifiedAmountsMapByBusinessNumbers(List.of(verification.getBusinessNumber())).get(verification.getBusinessNumber());
            convertInfo.setUnVerifiedAmount(verification.getBusinessAmount().subtract(verifiedAmount));
            convertInfo.setRemainVerifiedAmount(getCanOperationAmount(verificationId));
            convertInfo.setPayer(collection.getPayer());
            // 借试用列表
            List<BorrowFlowDeviceVO> deviceList = flowBorrowForProbationService.getBorrowForProbationDevice(verification.getBusinessNumber()).getObjEntity();
            convertInfo.setDeviceList(deviceList);
            // TODO 借转销列表
            processVO.setConvertInfo(convertInfo);
        }
        return processVO;
    }

    @Override
    public List<String> queryContractNumber(String contractNumber) {
        List<CrmContractExecute> list = contractExecuteService.lambdaQuery().likeLeft(StringUtils.isNotBlank(contractNumber), CrmContractExecute::getContractNumber, contractNumber).list();
        return list.stream().map(CrmContractExecute::getContractNumber).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<PaymentVerificationVO> verificationContractImportChecker(MultipartFile file, String collectionId) {
        if (collectionId == null) {
            throw new CrmException("预付款不存在");
        }
        // 预付款
        PaymentCollectionVO collection = paymentCollectionExtendService.getPaymentCollectionById(collectionId);
        try (InputStream inputStream = file.getInputStream()) {
            ExcelUtil<PaymentVerificationVO> excel = new ExcelUtil<>(PaymentVerificationVO.class);
            List<PaymentVerificationVO> list = excel.importExcel("核销合同", inputStream);
            if (list.isEmpty()) {
                throw new CrmException("导入数据为空");
            }
            // list核销总金额不超过剩余收款金额
            if (list.stream().map(PaymentVerificationVO::getVerificationAmount).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(collection.getCollectionAmount().subtract(collection.getVerifiedAmount())) > 0){
                throw new CrmException("核销总金额超过剩余收款金额");
            }
            // 业务单号不能有重复的
            Set<String> collect = list.stream().map(PaymentVerificationVO::getBusinessNumber).collect(Collectors.toSet());
            if (collect.size() != list.size()) {
                throw new CrmException("合同号重复");
            }
            // 合同信息Map
            List<CrmContractExecuteVO> numberBatch = contractExecuteService.getByContractNumberBatch(collect);
            Map<String, CrmContractExecuteVO> contractExecuteVOMap = numberBatch.stream().collect(Collectors.toMap(CrmContractExecuteVO::getContractNumber, item -> item));
            list.forEach(paymentVerificationVO -> {
                // 每个合同是否存在
                if (!contractExecuteVOMap.containsKey(paymentVerificationVO.getBusinessNumber())) {
                    throw new CrmException(paymentVerificationVO.getBusinessNumber() + "合同不存在");
                }
                // 核销金额不能大于欠款金额
                if (paymentVerificationVO.getVerificationAmount().compareTo(contractExecuteVOMap.get(paymentVerificationVO.getBusinessNumber()).getDebtAmount()) > 0){
                    throw new CrmException("核销金额不能大于欠款金额");
                }
                paymentVerificationVO.setPayer(contractExecuteVOMap.get(paymentVerificationVO.getBusinessNumber()).getContractCompanyName());
                paymentVerificationVO.setBusinessAmount(contractExecuteVOMap.get(paymentVerificationVO.getBusinessNumber()).getContractAmount());
                paymentVerificationVO.setUnVerifiedAmount(contractExecuteVOMap.get(paymentVerificationVO.getBusinessNumber()).getDebtAmount());
                paymentVerificationVO.setBusinessType(PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode());
                paymentVerificationVO.setVerificationType(PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode());
                paymentVerificationVO.setCollectionId(collectionId);
            });
            // 签约单位必须一致
            if (list.stream().map(PaymentVerificationVO::getPayer).distinct().count() > 1) {
                throw new CrmException("签约单位必须一致");
            }
            return list;
        } catch (CrmException crmException) {
            throw crmException;
        } catch (Exception e) {
            throw new RuntimeException("导入失败");
        }
    }

    @Override
    public List<String> getCollectionIdsByVerificationQuery(PaymentCollectionQuery verificationQuery) {
        BigDecimal verificationAmount = Optional.ofNullable(verificationQuery.getVerificationAmount())
                .map(amount -> amount.setScale(3, RoundingMode.HALF_UP)) // 设置为三位小数
                .orElse(null);
        return baseMapper.selectList(
                        new LambdaQueryWrapper<CrmPaymentVerification>()
                                .between(CrmPaymentVerification::getCreateTime, verificationQuery.getVerificationDateBegin(), verificationQuery.getVerificationDateEnd())
                                .eq(CrmPaymentVerification::getDelFlag, false)
                                // 核销金额为金额类型，判断是否为 null 或零值
                                .eq(verificationAmount != null &&
                                                !verificationAmount.equals(BigDecimal.ZERO),
                                        CrmPaymentVerification::getVerificationAmount,
                                        verificationAmount)
                                .like(StringUtils.isNotBlank(verificationQuery.getBusinessNumber()),
                                        CrmPaymentVerification::getBusinessNumber,
                                        verificationQuery.getBusinessNumber())
                                .orderByDesc(CrmPaymentVerification::getCreateTime)
                                .select(CrmPaymentVerification::getCollectionId)
                ).stream()
                .map(CrmPaymentVerification::getCollectionId)
                .distinct()
                .toList();
    }

    @Override
    public List<PaymentVerificationDTO> getVerificationListByBusinessNumber(String businessNumber) {
        // 查询核销明细
        List<CrmPaymentVerification> verifications = baseMapper.selectList(
                new LambdaQueryWrapper<CrmPaymentVerification>()
                        .eq(CrmPaymentVerification::getBusinessNumber, businessNumber)
                        .eq(CrmPaymentVerification::getDelFlag, false)
        );
        // 批量查询预付款信息
        List<CrmPaymentCollection> collections = paymentCollectionExtendService
                .getCollectionListByIds(verifications.stream().map(CrmPaymentVerification::getCollectionId).distinct().toList());
        Map<String, CrmPaymentCollection> collectionMap = collections.stream().collect(Collectors.toMap(CrmPaymentCollection::getId, item -> item));

        // 附件信息
        List<CrmFsmDoc> docList = fsmDocService.batchSelectByDocId(collections.stream().map(CrmPaymentCollection::getDocId).distinct().toList()).getObjEntity();
        Map<String, CrmFsmDoc> docMap = docList.stream().collect(Collectors.toMap(CrmFsmDoc::getDocId, item -> item));

        List<PaymentVerificationDTO> paymentVerificationDTOS = HyperBeanUtils.copyListProperties(verifications, PaymentVerificationDTO::new);

        paymentVerificationDTOS.forEach(verification -> {
            verification.setAccountingDate(collectionMap.get(verification.getCollectionId()).getAccountingDate());
            PaymentCollectionVO.PaymentDoc paymentDoc = new PaymentCollectionVO.PaymentDoc();
            if (docMap.containsKey(collectionMap.get(verification.getCollectionId()).getDocId())) {
                paymentDoc.setDocId(docMap.get(collectionMap.get(verification.getCollectionId()).getDocId()).getDocId());
                paymentDoc.setDocName(docMap.get(collectionMap.get(verification.getCollectionId()).getDocId()).getDocName());
                paymentDoc.setDocType(docMap.get(collectionMap.get(verification.getCollectionId()).getDocId()).getDocType());
                paymentDoc.setDocRemark(collectionMap.get(verification.getCollectionId()).getDocRemark());
                paymentDoc.setCreatedByName(docMap.get(collectionMap.get(verification.getCollectionId()).getDocId()).getCreatedByName());
                paymentDoc.setCreatedTime(docMap.get(collectionMap.get(verification.getCollectionId()).getDocId()).getCreatedTime());
            }
            verification.setFsmDoc(paymentDoc);
        });
        return paymentVerificationDTOS;
    }


    @Override
    @Transactional
    public Boolean handleRefundProcessApprovalData(VerificationRefundDTO refundDTO) {
        BigDecimal refundAmount = refundDTO.getRefundAmount();
        // 1.原核销金额扣除
        CrmPaymentVerification verification = getById(refundDTO.getVerificationId());
        verification.setVerificationAmount(verification.getVerificationAmount().subtract(refundAmount));
        updateById(verification);
        // 2.增加分类为退款的核销
        CrmPaymentVerification refundVerification = createRefundVerification(verification.getCollectionId(), refundAmount,Optional.of(verification.getBusinessNumber()).orElse(null));
        save(refundVerification);
        // 3.如果是货款，更新合同执行回款
        if (verification.getBusinessType() != null && PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode().equals(verification.getBusinessType())) {
            Map<String, BigDecimal> mapByBusinessNumbers = getVerifiedAmountsMapByBusinessNumbers(Collections.singletonList(verification.getBusinessNumber()));
            updateContractExecuteAmount(verification.getBusinessNumber(), mapByBusinessNumbers.get(verification.getBusinessNumber()));
        }
        updatePaymentCollection(verification.getCollectionId());
        return true;
    }

    @Override
    public Boolean handleConvertGoodsProcessApprovalData(VerificationConvertGoodsDTO convertGoodsDTO) {
        BigDecimal convertAmount = convertGoodsDTO.getConvertAmount();
        // 1. 原始核销金额扣除
        CrmPaymentVerification verification = getById(convertGoodsDTO.getVerificationId());
        verification.setVerificationAmount(verification.getVerificationAmount().subtract(convertAmount));
        updateById(verification);
        // 2. 根据转货款合同号，增加分类为货款的核销
        CrmPaymentVerification forGoodsVerification = createForGoodsVerification(verification.getCollectionId(), convertGoodsDTO.getConvertContractNumber(), convertAmount);
        save(forGoodsVerification);
        // 3. 更新合同执行回款
        Map<String, BigDecimal> mapByBusinessNumbers = getVerifiedAmountsMapByBusinessNumbers(Collections.singletonList(convertGoodsDTO.getConvertContractNumber()));
        updateContractExecuteAmount(convertGoodsDTO.getConvertContractNumber(), mapByBusinessNumbers.get(convertGoodsDTO.getConvertContractNumber()));
        updatePaymentCollection(verification.getCollectionId());
        return true;
    }

    @Override
    public Map<String, List<String>> getCollectionReceiptMapByContractNumber(List<String> contractNumbers) {
        // 检查 contractNumbers 是否为空
        if (contractNumbers == null || contractNumbers.isEmpty()) {
            return Collections.emptyMap();
        }

        // 根据合同号查询所有核销记录
        List<CrmPaymentVerification> verifications = baseMapper.selectList(new LambdaQueryWrapper<CrmPaymentVerification>()
                .eq(CrmPaymentVerification::getDelFlag, false)
                .in(CrmPaymentVerification::getBusinessNumber, contractNumbers)
        );

        // 获取所有核销记录对应的 collectionId
        List<String> collectionIds = verifications.stream()
                .map(CrmPaymentVerification::getCollectionId)
                .collect(Collectors.toList());
        // 根据 collectionId 查询所有预付款集合
        List<CrmPaymentCollection> collections = paymentCollectionExtendService.getCollectionListByIds(collectionIds);

        // 将预付款集合按 collectionId 分组
        Map<String, CrmPaymentCollection> collectionMap = collections.stream()
                .collect(Collectors.toMap(CrmPaymentCollection::getId, collection -> collection));

        // 按合同号分组，并收集附件ID（去重）
        return verifications.stream()
                .collect(Collectors.groupingBy(
                        CrmPaymentVerification::getBusinessNumber, // 按合同号分组
                        Collectors.mapping(
                                verification -> collectionMap.get(verification.getCollectionId()).getDocId(), // 获取附件ID
                                Collectors.collectingAndThen(Collectors.toSet(), ArrayList::new) // 去重并转换为List
                        )
                ));
    }

    /**
     * 创建操作检查VO
     */
    private DataOperationCheckVO createOperationCheckVO(String id, CrmPaymentVerification verification) {
        DataOperationCheckVO checkVO = new DataOperationCheckVO();
        checkVO.setDataId(id);
        return checkVO;
    }

    /**
     * 获取预付款已核销金额列表
     */
    private List<BigDecimal> getVerifiedAmounts(String collectionId, String excludeId) {
        return new ArrayList<>(baseMapper.selectList(new LambdaQueryWrapper<CrmPaymentVerification>()
                        .eq(CrmPaymentVerification::getCollectionId, collectionId)
                        .eq(CrmPaymentVerification::getDelFlag, false)
                        .ne(CrmPaymentVerification::getId, excludeId)
                ).stream()
                .map(CrmPaymentVerification::getVerificationAmount)
                .toList());
    }

    /**
     * 创建货款核销明细记录
     */
    private CrmPaymentVerification createForGoodsVerification(String collectionId,String contractNumber, BigDecimal convertAmount) {
        CrmContractExecuteVO byContractNumber = contractExecuteService.getByContractNumber(contractNumber);
        CrmPaymentVerification goodsVerification = new CrmPaymentVerification();
        goodsVerification.setVerificationAmount(convertAmount);
        goodsVerification.setVerificationType(PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode());
        goodsVerification.setCollectionId(collectionId);
        goodsVerification.setBusinessNumber(contractNumber);
        goodsVerification.setBusinessType(PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode());
        goodsVerification.setBusinessAmount(byContractNumber.getContractAmount());
        return goodsVerification;
    }

    /**
     * 创建退款核销记录
     */
    private CrmPaymentVerification createRefundVerification(String collectionId, BigDecimal refundAmount,String businessNumber) {
        CrmPaymentVerification refundVerification = new CrmPaymentVerification();
        refundVerification.setVerificationAmount(refundAmount);
        refundVerification.setVerificationType(PaymentVerificationVO.VerificationTypeEnum.REFUND.getCode());
        refundVerification.setBusinessNumber(businessNumber);
        refundVerification.setCollectionId(collectionId);
        return refundVerification;
    }

    /**
     * 核销明细可操作状态
     * @param vos 核销明细ID
     * @return 可操作状态
     */
    private List<DataOperationCheckVO> getOperationCheckList(List<PaymentVerificationVO> vos) {

        if (vos.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> verificationIds = vos.stream().map(PaymentVerificationVO::getId).toList();

        // 是否有正在走的流程
        Map<String, Boolean> goingRefundMap = flowPaymentVerificationService.isHasOngoingRefundProcessMap(verificationIds).getObjEntity();
        Map<String, Boolean> goingConvertMap = flowPaymentVerificationService.isHasOngoingConvertGoodsProcessMap(verificationIds).getObjEntity();
        // 合并 refundMap 和 convertMap
        Map<String, Boolean> hasGoingMap = Stream.of(goingRefundMap, goingConvertMap)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (v1, v2) -> v1 || v2 // 如果有相同的 key，取 true
                ));
        vos.stream()
                .map(PaymentVerificationVO::getId)
                .forEach(id -> hasGoingMap.putIfAbsent(id, false));

        // 是否发起过流程
        Map<String, Boolean> hasConvertMap = flowPaymentVerificationService.isHasConvertGoodsProcessMap(verificationIds).getObjEntity();
        Map<String, Boolean> hasRefundMap = flowPaymentVerificationService.isHasRefundProcessMap(verificationIds).getObjEntity();
        Map<String, Boolean> hasProcessMap = Stream.of(hasConvertMap, hasRefundMap)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (v1, v2) -> v1 || v2 // 如果有相同的 key，取 true
                ));

        List<DataOperationCheckVO> operationCheckVOS = new ArrayList<>();
        for (PaymentVerificationVO verificationVO : vos) {
            DataOperationCheckVO operationCheckVO = new DataOperationCheckVO();
            operationCheckVO.setDataId(verificationVO.getId());
            operationCheckVO.setExtraInfo(Map.of("isHasOngoingProcess", hasGoingMap.get(verificationVO.getId())));
            operationCheckVO.setExtraInfo(Map.of("isHasProcess", hasProcessMap.get(verificationVO.getId())));

            // 删除
            DataOperationCheckVO delete = isCanDelete(operationCheckVO,verificationVO);
            operationCheckVO.setIsCanDelete(delete.getIsCanDelete());
            operationCheckVO.setDeleteRejectedReason(delete.getDeleteRejectedReason());

            // 修改
            DataOperationCheckVO modify = isCanModify(operationCheckVO,verificationVO);
            operationCheckVO.setIsCanModify(modify.getIsCanModify());
            operationCheckVO.setModifyRejectedReason(modify.getModifyRejectedReason());

            // 退款
            DataOperationCheckVO refund = isCanRefund(operationCheckVO,verificationVO);
            operationCheckVO.setIsCanRefund(refund.getIsCanRefund());
            operationCheckVO.setRefundRejectedReason(refund.getRefundRejectedReason());

            // 回冲
            DataOperationCheckVO rollback = isCanRollback(operationCheckVO,verificationVO);
            operationCheckVO.setIsCanRollback(rollback.getIsCanRollback());
            operationCheckVO.setRollbackRejectedReason(rollback.getRollbackRejectedReason());

            // 转货款
            DataOperationCheckVO canConvert = IsCanConvert(operationCheckVO,verificationVO);
            operationCheckVO.setIsCanConvert(canConvert.getIsCanConvert());
            operationCheckVO.setConvertRejectedReason(canConvert.getConvertRejectedReason());

            operationCheckVOS.add(operationCheckVO);
        }

        return operationCheckVOS;
    }
    /**
     * 是否可删除
     */
    private DataOperationCheckVO isCanDelete(DataOperationCheckVO checkVO,PaymentVerificationVO verification){

        // 初始化可删除状态为 true
        checkVO.setIsCanDelete(true);

        // 条件 a: 核销金额 >= 0
        if (verification.getVerificationAmount().compareTo(BigDecimal.ZERO) < 0) {
            checkVO.setIsCanDelete(false);
            checkVO.setDeleteRejectedReason("核销金额必须大于或等于 0");
            return checkVO;
        }

        // 条件 b: 核销分类为货款，预付款，退款时，不可删除
        if (PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode().equals(verification.getVerificationType()) ||
                PaymentVerificationVO.VerificationTypeEnum.REFUND.getCode().equals(verification.getVerificationType()) ||
                PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode().equals(verification.getVerificationType())) {
            checkVO.setIsCanDelete(false);
            checkVO.setDeleteRejectedReason("核销分类为货款，预付款，退款时，不可删除");
            return checkVO;
        }

        // 条件 c: 不能存在“押金转货款”和“退款”流程
        if (checkProcess(checkVO)){
            checkVO.setIsCanDelete(false);
            checkVO.setDeleteRejectedReason("存在“押金转货款”和“退款”流程，不可删除");
            return checkVO;
        }

        // 条件 d: 需校验操作时间为核销月份当前月（自然月）
        LocalDateTime createTime = verification.getCreateTime();
        LocalDate now = LocalDate.now();
        // 判断创建时间的年份和月份是否与当前时间的年份和月份相同
        if (createTime.getYear() != now.getYear() || createTime.getMonth().getValue() != now.getMonth().getValue()) {
            checkVO.setIsCanDelete(false);
            checkVO.setDeleteRejectedReason("可删除时间为核销月份当前月（自然月）");
            return checkVO;
        }

        // 如果所有条件都满足，设置为可删除
        checkVO.setDeleteRejectedReason(null);
        checkVO.setIsCanDelete(true);

        return checkVO;
    }

    /**
     * 是否可修改
     */
    private DataOperationCheckVO isCanModify(DataOperationCheckVO checkVO,PaymentVerificationVO verification){

        // 条件 a: 核销金额 >= 0
        if (verification.getVerificationAmount().compareTo(BigDecimal.ZERO) < 0) {
            checkVO.setIsCanModify(false);
            checkVO.setModifyRejectedReason("核销金额必须大于或等于 0");
            return checkVO;
        }

        // 条件 b: 核销记录时间小于审计时间不能修改
//        CrmAuditRecordVO objEntity = auditRecordService.getLatestAuditRecord().getObjEntity();
//        if (objEntity != null && objEntity.getAuditTime().isAfter(checkVO.getData().getCreateTime())) {
//            checkVO.setIsCanModify(false);
//            checkVO.setModifyRejectedReason("操作时间小于审计时间");
//            return checkVO;
//        }

        // 条件 c: 需校验操作时间为核销月份当前月（自然月）
        LocalDateTime createTime = verification.getCreateTime();
        LocalDate now = LocalDate.now();

        // 判断创建时间的年份和月份是否与当前时间的年份和月份相同
        if (createTime.getYear() != now.getYear() || createTime.getMonth().getValue() != now.getMonth().getValue()) {
            checkVO.setIsCanModify(false);
            checkVO.setModifyRejectedReason("可修改时间为核销月份当前月（自然月）");
            return checkVO;
        }

        // 条件 d: 不能存在办理中的“押金转货款”和“退款”流程
        if (checkGoingProcess(checkVO)){
            checkVO.setIsCanModify(false);
            checkVO.setModifyRejectedReason("存在办理中的“押金转货款”和“退款”流程，不可修改");
            return checkVO;
        }

        // 分类为退款，预付款，不可修改
        if (PaymentVerificationVO.VerificationTypeEnum.REFUND.getCode().equals(verification.getVerificationType()) ||
                PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode().equals(verification.getVerificationType())) {
            checkVO.setIsCanModify(false);
            checkVO.setModifyRejectedReason("核销分类为“退款或预付款”时，不可修改");
            return checkVO;
        }

        checkVO.setIsCanModify(true);
        checkVO.setModifyRejectedReason(null);
        return checkVO;
    }

    /**
     * 是否可退款
     */
    private DataOperationCheckVO isCanRefund(DataOperationCheckVO checkVO,PaymentVerificationVO verification){

        // 条件 a: 核销金额 > 0
        if (verification.getVerificationAmount().compareTo(BigDecimal.ZERO) <= 0) {
            checkVO.setIsCanRefund(false);
            checkVO.setRefundRejectedReason("核销金额必须大于 0");
            return checkVO;
        }

        // 条件 b: 核销分类不为“预收款”或“押金”时,不可退款
        if (!PaymentVerificationVO.VerificationTypeEnum.ADVANCE.getCode().equals(verification.getVerificationType())
                && !PaymentVerificationVO.VerificationTypeEnum.DEPOSIT.getCode().equals(verification.getVerificationType())) {
            checkVO.setIsCanRefund(false);
            checkVO.setRefundRejectedReason("核销分类不为“预收款”或“押金”时,不可退款");
            return checkVO;
        }

        // 条件 c: 不能存在办理中的“押金转货款”和“退款”流程
        if (checkGoingProcess(checkVO)){
            checkVO.setIsCanRefund(false);
            checkVO.setRefundRejectedReason("存在办理中的“押金转货款”和“退款”流程，不可退款");
            return checkVO;
        }

        checkVO.setIsCanRefund(true);
        checkVO.setRefundRejectedReason(null);
        return checkVO;
    }

    /**
     * 是否可回冲
     */
    private DataOperationCheckVO isCanRollback(DataOperationCheckVO checkVO,PaymentVerificationVO verification){

        // 条件 a: 核销金额 > 0
        if (verification.getVerificationAmount().compareTo(BigDecimal.ZERO) <= 0) {
            checkVO.setIsCanRollback(false);
            checkVO.setRollbackRejectedReason("核销金额必须大于 0");
            return checkVO;
        }

        // 条件 b: 核销分类为“货款”或“保证金”时,才可回冲
        if (!PaymentVerificationVO.VerificationTypeEnum.FOR_GOODS.getCode().equals(verification.getVerificationType())
                && !PaymentVerificationVO.VerificationTypeEnum.SECURITY_FUND.getCode().equals(verification.getVerificationType())) {
            checkVO.setIsCanRollback(false);
            checkVO.setRollbackRejectedReason("核销分类不为“货款”或“保证金”时,不可回冲");
            return checkVO;
        }

        // 条件 c: 当月不可回冲
        LocalDate now = LocalDate.now();
        LocalDateTime verificationTime = verification.getCreateTime();
        if (verificationTime.getYear() == now.getYear() && verificationTime.getMonth().getValue() == now.getMonth().getValue()) {
            checkVO.setIsCanRollback(false);
            checkVO.setRollbackRejectedReason("当月不可回冲");
            return checkVO;
        }

        checkVO.setIsCanRollback(true);
        checkVO.setRollbackRejectedReason(null);
        return checkVO;
    }

    /**
     * 是否可转货款
     */
    private DataOperationCheckVO IsCanConvert(DataOperationCheckVO checkVO,PaymentVerificationVO verification){

        // 条件 a: 核销金额>0
        if (verification.getVerificationAmount().compareTo(BigDecimal.ZERO) <= 0) {
            checkVO.setIsCanConvert(false);
            checkVO.setConvertRejectedReason("核销金额必须大于 0");
            return checkVO;
        }

        // 条件 b: 核销分类必须为“押金”
        if (!PaymentVerificationVO.VerificationTypeEnum.DEPOSIT.getCode().equals(verification.getVerificationType())) {
            checkVO.setIsCanConvert(false);
            checkVO.setConvertRejectedReason("核销分类必须为“押金”");
            return checkVO;
        }

        // 条件 c: 不能存在办理中的“押金转货款”和“退款”流程
        if (checkGoingProcess(checkVO)){
            checkVO.setIsCanConvert(false);
            checkVO.setConvertRejectedReason("存在办理中的“押金转货款”和“退款”流程，不可转货款");
            return checkVO;
        }

        checkVO.setIsCanConvert(true);
        checkVO.setConvertRejectedReason(null);
        return checkVO;
    }


    /**
     * 是否存在办理中的退款，押金转货款流程
     */
    private Boolean checkGoingProcess(DataOperationCheckVO checkVO) {
        return Optional.ofNullable(checkVO.getExtraInfo())
                .map(extraInfo -> (Boolean) extraInfo.get("isHasOngoingProcess"))
                .orElse(false); // 默认值为 false
    }

    /**
     * 是否发起过退款，押金转货款流程
     */
    private Boolean checkProcess(DataOperationCheckVO checkVO) {
        return Optional.ofNullable(checkVO.getExtraInfo())
                .map(extraInfo -> (Boolean) extraInfo.get("isHasProcess"))
                .orElse(false); // 默认值为 false
    }
}
