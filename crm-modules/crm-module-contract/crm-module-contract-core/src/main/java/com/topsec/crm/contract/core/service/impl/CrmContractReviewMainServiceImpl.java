package com.topsec.crm.contract.core.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.contract.api.entity.CrmContractBaseInfoVO;
import com.topsec.crm.contract.api.entity.CrmContractProductOwnVO;
import com.topsec.crm.contract.api.entity.request.CrmContractInfoQuery;
import com.topsec.crm.contract.core.entity.*;
import com.topsec.crm.contract.core.mapper.CrmContractReviewMainMapper;
import com.topsec.crm.contract.core.service.*;
import com.topsec.crm.flow.api.RemoteContractReviewFlowService;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewOtherDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewSignContractDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewTermDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDetailDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractFlowProductMappingDTO;
import com.topsec.crm.flow.api.dto.contractreview.response.ContractMainSaveResponse;
import com.topsec.crm.flow.api.dto.contractreview.sninfo.ContractProductSnVO;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnVO;
import com.topsec.crm.framework.common.enums.OriginalTypeEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NamePair;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.product.api.RemoteDeviceService;
import com.topsec.crm.product.api.entity.CrmDeviceVo;
import com.topsec.crm.project.api.RemoteProjectDynastyService;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectProductSnClient;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.CrmProjectDynastyVo;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.TosDepartmentVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CrmContractReviewMainServiceImpl extends ServiceImpl<CrmContractReviewMainMapper, CrmContractReviewMain> implements CrmContractReviewMainService {

    private final CrmContractReviewCustomerService crmContractReviewCustomerService;
    private final CrmContractReviewSignContractService crmContractReviewSignContractService;
    private final CrmContractReviewNoticeArriveService crmContractReviewNoticeArriveService;
    private final CrmContractReviewProductOwnService crmContractReviewProductOwnService;
    private final CrmContractReviewProductThirdService crmContractReviewProductThirdService;
    private final CrmContractReviewRetentionMoneyService crmContractReviewRetentionMoneyService;
    private final CrmContractReviewPaymentProvisionService contractReviewPaymentProvisionService;
    private final CrmContractReviewRevenueRecognitionService contractReviewRevenueRecognitionService;
    private final CrmContractReviewAttachmentService crmContractReviewAttachmentService;
    private final CrmContractReviewDeliveryService crmContractReviewDeliveryService;
    private final CrmContractReviewDeliveryDetailOwnService crmContractReviewDeliveryDetailOwnService;
    private final CrmContractReviewDeliveryDetailThirdService crmContractReviewDeliveryDetailThirdService;
    private final CrmContractReviewProductSnService crmContractReviewProductSnService;
    private final TosEmployeeClient tosEmployeeClient;
    private final TosDepartmentClient departmentClient;
    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final RemoteProjectDynastyService remoteProjectDynastyService;
    private final RemoteContractReviewFlowService flowService;
    private final RemoteDeviceService remoteDeviceService;
    private final RedisTemplate<String, String> redisTemplate;
    private final CrmContractReviewProductSnOutService productSnOutService;
    private final RemoteProjectProductSnClient remoteProjectProductSnClient;

    private final static String CONTRACT_SN = "contract::sn";
    private final static String CONTRACT_SN_PUSH = "contract::push";


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractMainSaveResponse saveValidData(ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO) {
        // 先判断这个流程id是否存在记录，如果存在就是修改
        CrmContractReviewMain one = getOne(new LambdaQueryWrapper<CrmContractReviewMain>().eq(CrmContractReviewMain::getProcessInstanceId, contractReviewMainFlowLaunchDTO.getProcessInstanceId())
                .eq(CrmContractReviewMain::getDelFlag, 0)
                .last("limit 1"));


        // 写入主表信息
        CrmContractReviewMain crmContractReviewMain = HyperBeanUtils.copyProperties(contractReviewMainFlowLaunchDTO.getBaseInfoDTO(), CrmContractReviewMain::new);
        crmContractReviewMain.setId(null);
        crmContractReviewMain.setProcessInstanceId(contractReviewMainFlowLaunchDTO.getProcessInstanceId());
        crmContractReviewMain.setProcessNumber(contractReviewMainFlowLaunchDTO.getProcessNumber());
        crmContractReviewMain.setValidDate(LocalDateTime.now());
        // 冗余销售部门和销售人员信息
        NamePair salesInfo = NameUtils.getNamePair(crmContractReviewMain.getSaleId());
        JsonObject<TosDepartmentVO> salesDeptInfo = departmentClient.findById(crmContractReviewMain.getSaleDeptId());
        if (salesInfo != null) {
            crmContractReviewMain.setSaleName(salesInfo.getName());
        }
        if (salesDeptInfo.isSuccess() && salesDeptInfo.getObjEntity() != null) {
            crmContractReviewMain.setSaleDeptName(salesDeptInfo.getObjEntity().getName());
        }
        if (one != null) {
            update(crmContractReviewMain, new LambdaQueryWrapper<CrmContractReviewMain>()
                    .eq(CrmContractReviewMain::getProcessInstanceId, contractReviewMainFlowLaunchDTO.getProcessInstanceId())
                    .eq(CrmContractReviewMain::getDelFlag, 0));
        } else {
            save(crmContractReviewMain);
        }
        String contractId = one != null ? one.getId() : crmContractReviewMain.getId();
        // 拿到主表的id 保存相关信息
        CrmContractReviewCustomer crmContractReviewCustomer = HyperBeanUtils.copyProperties(contractReviewMainFlowLaunchDTO.getContractReviewCustomerDTO(), CrmContractReviewCustomer::new);
        CrmContractReviewSignContract crmContractReviewSignContract = HyperBeanUtils.copyProperties(contractReviewMainFlowLaunchDTO.getContractReviewSignContractDTO(), CrmContractReviewSignContract::new);
        List<CrmContractReviewNoticeArrive> crmContractReviewNoticeArrive = HyperBeanUtils.copyListProperties(contractReviewMainFlowLaunchDTO.getContractReviewNoticeArriveDTO(), CrmContractReviewNoticeArrive::new);

        crmContractReviewCustomer.setId(null);
        crmContractReviewCustomer.setContractReviewMainId(contractId);
        crmContractReviewSignContract.setId(null);
        crmContractReviewSignContract.setContractReviewMainId(contractId);
        crmContractReviewNoticeArrive.forEach(item -> {
            item.setId(null);
            item.setContractReviewMainId(contractId);
        });
        if (one != null) {
            // 修改 这个逻辑可能不需要 每次都是新增
            crmContractReviewCustomerService.update(crmContractReviewCustomer, new LambdaQueryWrapper<CrmContractReviewCustomer>()
                    .eq(CrmContractReviewCustomer::getContractReviewMainId, contractId)
                    .eq(CrmContractReviewCustomer::getDelFlag, 0));
            crmContractReviewSignContractService.update(crmContractReviewSignContract, new LambdaQueryWrapper<CrmContractReviewSignContract>()
                    .eq(CrmContractReviewSignContract::getContractReviewMainId, contractId)
                    .eq(CrmContractReviewSignContract::getDelFlag, 0));
            // 这个地方直接逻辑删除再新增
            crmContractReviewNoticeArriveService.update(new LambdaUpdateWrapper<CrmContractReviewNoticeArrive>()
                    .set(CrmContractReviewNoticeArrive::getDelFlag, 1)
                    .eq(CrmContractReviewNoticeArrive::getContractReviewMainId, contractId)
                    .eq(CrmContractReviewNoticeArrive::getDelFlag, 0));
            crmContractReviewNoticeArriveService.saveBatch(crmContractReviewNoticeArrive);
        } else {
            crmContractReviewCustomerService.save(crmContractReviewCustomer);
            crmContractReviewSignContractService.save(crmContractReviewSignContract);
            crmContractReviewNoticeArriveService.saveBatch(crmContractReviewNoticeArrive);
        }
        ContractMainSaveResponse response = new ContractMainSaveResponse();
        response.setContractId(contractId);
        response.setIsUpdate(one != null);
        return response;
    }

    @Override
    public boolean saveTermData(ContractReviewTermDTO contractReviewTermDTO, Boolean isUpdate) {
        String contractId = contractReviewTermDTO.getContractId();
        List<CrmContractReviewPaymentProvision> crmContractReviewPaymentProvisions = HyperBeanUtils.copyListProperties(contractReviewTermDTO.getPaymentProvisionDTOS(), CrmContractReviewPaymentProvision::new);
        List<CrmContractReviewRevenueRecognition> crmContractReviewRevenueRecognitions = HyperBeanUtils.copyListProperties(contractReviewTermDTO.getRevenueRecognitionDTOS(), CrmContractReviewRevenueRecognition::new);
        CrmContractReviewRetentionMoney crmContractReviewRetentionMoney = HyperBeanUtils.copyProperties(contractReviewTermDTO.getReviewRetentionMoneyDTO(), CrmContractReviewRetentionMoney::new);
        if (crmContractReviewRetentionMoney != null) {
            crmContractReviewRetentionMoney.setId(null);
            crmContractReviewRetentionMoney.setContractReviewMainId(contractId);
        }
        ListUtils.emptyIfNull(crmContractReviewPaymentProvisions).forEach(item -> {
            item.setFlowContractPaymentId(item.getId());
            item.setId(null);
            item.setContractReviewMainId(contractId);
        });
        ListUtils.emptyIfNull(crmContractReviewRevenueRecognitions).forEach(item -> {
            item.setFlowContractRevenueRecognitionId(item.getId());
            item.setId(null);
            item.setContractReviewMainId(contractId);
        });

        if (isUpdate) {
            // 这个地方先保留 后面可能有修改的需求 目前全部逻辑删除后再新增
        } else {
            if (crmContractReviewRetentionMoney != null) {
                crmContractReviewRetentionMoneyService.save(crmContractReviewRetentionMoney);
            }
            contractReviewPaymentProvisionService.saveBatch(crmContractReviewPaymentProvisions);
            contractReviewRevenueRecognitionService.saveBatch(crmContractReviewRevenueRecognitions);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ContractFlowProductMappingDTO> saveProductData(ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO, Boolean isUpdate) {
        // 先判断这个流程id是否存在记录，如果存在就是修改
        String contractId = contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getId();
        // 产品信息
        List<CrmContractReviewProductThird> crmContractReviewProductThirds = HyperBeanUtils.copyListProperties(contractReviewMainFlowLaunchDTO.getContractProductThirdDTOS(), CrmContractReviewProductThird::new);
        crmContractReviewProductThirds.forEach(item -> {
            item.setFlowContractThirdId(item.getId());
            // 明细有效时间
            CrmContractReviewProductThird one = crmContractReviewProductThirdService.getOne(new LambdaQueryWrapper<CrmContractReviewProductThird>()
                    .eq(CrmContractReviewProductThird::getFlowContractThirdId, item.getId())
                    .orderByAsc(CrmContractReviewProductThird::getCreateTime)
                    .last("limit 1"));
            if (one != null) {
                item.setValidDate(one.getValidDate());
            } else {
                item.setValidDate(LocalDateTime.now());
            }
            item.setId(null);
            item.setContractReviewId(contractId);
        });
        List<ContractFlowProductMappingDTO> productMappingDTOS = new ArrayList<>();
        if (isUpdate) {
            // 这个逻辑可能不需要 每次都是新增
            crmContractReviewProductThirdService.updateProductInfo(contractId, contractReviewMainFlowLaunchDTO.getContractProductThirdDTOS());
            crmContractReviewProductOwnService.updateProductInfo(contractId, contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS());
        } else {
            // 自有产品这个地方有配件 需要自己映射
            crmContractReviewProductOwnService.saveProductInfo(contractId, contractReviewMainFlowLaunchDTO.getContractProductOwnDTOS(), false);
            crmContractReviewProductThirdService.saveBatch(crmContractReviewProductThirds);

            // 查出这些自有产品、第三方产品 组装成map 方便关联表赋值
            List<CrmContractReviewProductOwn> owns = crmContractReviewProductOwnService.getByContractId(contractId);
            List<CrmContractReviewProductThird> thirds = crmContractReviewProductThirdService.getByContractId(contractId);
            Map<String, CrmContractReviewProductOwn> productOwnMap = ListUtils.emptyIfNull(owns).stream()
                    .collect(Collectors.toMap(CrmContractReviewProductOwn::getFlowContractOwnId, v -> v, (oldV, newV) -> newV));
            Map<String, CrmContractReviewProductThird> productThirdMap = ListUtils.emptyIfNull(thirds).stream()
                    .collect(Collectors.toMap(CrmContractReviewProductThird::getFlowContractThirdId, v -> v, (oldV, newV) -> newV));

            productOwnMap.forEach((k, v) -> {
                ContractFlowProductMappingDTO mappingDTO = new ContractFlowProductMappingDTO();
                mappingDTO.setFlowContractProductId(k);
                mappingDTO.setContractProductId(v.getId());
                productMappingDTOS.add(mappingDTO);
            });
            productThirdMap.forEach((k, v) -> {
                ContractFlowProductMappingDTO mappingDTO = new ContractFlowProductMappingDTO();
                mappingDTO.setFlowContractProductId(k);
                mappingDTO.setContractProductId(v.getId());
                productMappingDTOS.add(mappingDTO);
            });


            // 保存发货数据
            List<ContractDeliveryDTO> contractDeliveryDTOS = contractReviewMainFlowLaunchDTO.getContractDeliveryDTOS();
            ListUtils.emptyIfNull(contractDeliveryDTOS).forEach(contractDeliveryDTO -> {
                CrmContractReviewDelivery crmContractReviewDelivery = HyperBeanUtils.copyProperties(contractDeliveryDTO, CrmContractReviewDelivery::new);
                crmContractReviewDelivery.setId(null);
                crmContractReviewDelivery.setContractId(contractId);
                crmContractReviewDeliveryService.save(crmContractReviewDelivery);
                List<ContractDeliveryDetailDTO> detailDTOS = contractDeliveryDTO.getDetailDTOS();
                // 根据类型判断要插入哪个表
                ListUtils.emptyIfNull(detailDTOS).forEach(item -> {
                    if (ContractDeliveryDetailDTO.ProductType.OWN.getCode().equals(item.getProductType())) {
                        // 查出产品的id
                        CrmContractReviewProductOwn own = productOwnMap.get(item.getContractProductId());
                        CrmContractReviewDeliveryDetailOwn deliveryOwn = HyperBeanUtils.copyProperties(item, CrmContractReviewDeliveryDetailOwn::new);
                        deliveryOwn.setDeliveryId(crmContractReviewDelivery.getId());
                        if (own != null) {
                            deliveryOwn.setContractProductOwnId(own.getId());
                        } else {
                            deliveryOwn.setContractProductOwnId(null);
                        }
                        crmContractReviewDeliveryDetailOwnService.save(deliveryOwn);
                    }

                    if (ContractDeliveryDetailDTO.ProductType.THIRD.getCode().equals(item.getProductType())) {
                        CrmContractReviewProductThird third = productThirdMap.get(item.getContractProductId());
                        CrmContractReviewDeliveryDetailThird deliveryThird = HyperBeanUtils.copyProperties(item, CrmContractReviewDeliveryDetailThird::new);
                        deliveryThird.setDeliveryId(crmContractReviewDelivery.getId());
                        if (third != null) {
                            deliveryThird.setContractProductThirdId(third.getId());
                        } else {
                            deliveryThird.setContractProductThirdId(null);
                        }
                        crmContractReviewDeliveryDetailThirdService.save(deliveryThird);
                    }
                });
            });

            // 保存序列号数据
            List<CrmContractReviewProductSn> crmContractReviewProductSns = HyperBeanUtils.copyListProperties(contractReviewMainFlowLaunchDTO.getContractProductSnVOS(), CrmContractReviewProductSn::new);
            ListUtils.emptyIfNull(crmContractReviewProductSns).forEach(item -> {
                item.setId(null);
                item.setContractId(contractId);
                CrmContractReviewProductOwn own = productOwnMap.get(item.getContractRecordId());
                if (own != null) {
                    item.setContractRecordId(own.getId());
                } else {
                    item.setContractRecordId(null);
                }
            });

            crmContractReviewProductSnService.saveBatch(crmContractReviewProductSns);
        }
        return productMappingDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOtherData(ContractReviewOtherDTO contractReviewOtherDTO, Boolean isUpdate) {
        String contractId = contractReviewOtherDTO.getContractId();
        List<CrmContractReviewAttachment> crmContractReviewAttachments = HyperBeanUtils.copyListProperties(contractReviewOtherDTO.getContractReviewAttachmentDTOS(), CrmContractReviewAttachment::new);
        ListUtils.emptyIfNull(crmContractReviewAttachments).forEach(item -> {
            item.setId(null);
            item.setContractReviewMainId(contractId);
        });

        if (isUpdate) {
            // 这个地方先保留 后面可能有修改的需求 目前全部逻辑删除后再新增
        } else {
            crmContractReviewAttachmentService.saveBatch(crmContractReviewAttachments);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completed(String processInstanceId) {
        // todo 费用备案 借转销数据写入
        CrmContractReviewMain contract = getOne(new LambdaQueryWrapper<CrmContractReviewMain>().eq(CrmContractReviewMain::getProcessInstanceId, processInstanceId)
                .eq(CrmContractReviewMain::getDelFlag, 0));
        if (contract == null) {
            throw new CrmException("流程实例不存在");
        }
        // 考核状态 考核时间更新
        update(new LambdaUpdateWrapper<CrmContractReviewMain>()
                .set(CrmContractReviewMain::getAssessmentStatus, 1)
                .eq(CrmContractReviewMain::getProcessInstanceId, processInstanceId)
                .eq(CrmContractReviewMain::getDelFlag, 0));
        crmContractReviewProductOwnService.update(new LambdaUpdateWrapper<CrmContractReviewProductOwn>()
                .set(CrmContractReviewProductOwn::getAssessmentTime, LocalDateTime.now())
                .eq(CrmContractReviewProductOwn::getContractReviewId, contract.getId())
                .eq(CrmContractReviewProductOwn::getDelFlag, 0));
        crmContractReviewProductThirdService.update(new LambdaUpdateWrapper<CrmContractReviewProductThird>()
                .set(CrmContractReviewProductThird::getAssessmentTime, LocalDateTime.now())
                .eq(CrmContractReviewProductThird::getContractReviewId, contract.getId())
                .eq(CrmContractReviewProductThird::getDelFlag, 0));
        // 判断一下发货是否全部发完
        try {
            // 推送生产 这个地方默认全发货了
            CrmContractReviewService reviewService = SpringUtil.getBean(CrmContractReviewService.class);
            List<CrmContractProductOwnVO> products = reviewService.queryOwnProductByProcessInstanceIdBatch(Set.of(processInstanceId));
            if (CollectionUtils.isNotEmpty(products)) {
                Object o = redisTemplate.opsForHash().get(CONTRACT_SN_PUSH, contract.getId());
                if (o == null) {
                    pushProd(products, contract);
                }
            }
        } catch (Exception e) {
            log.error("生产序列号失败", e);
        }
        return true;
    }

    private void pushProd(List<CrmContractProductOwnVO> products, CrmContractReviewMain contractReviewMain) throws ExecutionException, InterruptedException {
        // todo 推送生产 后面xxx.push 放mq就不用管了 然后去监听生产返回的
        List<CrmDeviceVo> deviceVos = new ArrayList<>();
        List<CrmContractReviewProductSnOut> outs = new ArrayList<>();
        List<CrmContractReviewProductSnIn> ins = new ArrayList<>();
        List<CrmProjectProductSnVO> projectSns = new ArrayList<>();
        Boolean isImportantContractIn = contractReviewMain.getIsImportantContractIn();
        ContractReviewSignContractDTO signContractDTO;
        if (isImportantContractIn != null && isImportantContractIn) {
            // 取签约单位
            signContractDTO = crmContractReviewSignContractService.getByContractId(contractReviewMain.getId());
        } else {
            signContractDTO = null;
        }
        // 取这些产品关联的借试用 借转销等序列号
        Set<String> contractProjectIds = products.stream().map(CrmContractProductOwnVO::getId).collect(Collectors.toSet());
        CompletableFuture<List<ContractProductSnVO>> snFuture = CompletableFuture.supplyAsync(() -> crmContractReviewProductSnService.getByContractProductIdBatch(contractProjectIds));

        List<ContractProductSnVO> snVOS = snFuture.get();
        // key contractRecordId value 数量
        Map<String, Long> snMap = snVOS.stream().collect(Collectors.groupingBy(ContractProductSnVO::getContractRecordId, Collectors.counting()));
        // 试运行自己生产序列号
        ListUtils.emptyIfNull(products).forEach(product -> {
            Long productNum = product.getProductNum();
            // 这个产品关联的序列号数量 要减去
            productNum = productNum - snMap.getOrDefault(product.getId(), 0L);
            for (int i = 0; i < productNum; i++) {
                // 原子递增
                Long increment = redisTemplate.opsForValue().increment(CONTRACT_SN, 1);
                String sn = "QSZ" + StringUtils.leftPad(increment.toString(), 8, "0");
                CrmDeviceVo deviceVo = new CrmDeviceVo();
                deviceVo.setSn(sn);
                deviceVo.setStorehouseId("试运行合同库位");
                deviceVo.setProductId(product.getProductId());
                if (product.getProductPeriodStart() != null) {
                    deviceVo.setGuaranteeStart(Date.from(product.getProductPeriodStart().atStartOfDay(ZoneId.systemDefault()).toInstant()));
                }
                if (product.getProductPeriodEnd() != null) {
                    deviceVo.setGuaranteeEnd(Date.from(product.getProductPeriodEnd().atStartOfDay(ZoneId.systemDefault()).toInstant()));
                }
                if (isImportantContractIn != null && isImportantContractIn) {
                    deviceVo.setSignCompanyId(signContractDTO.getContractCompanyId());
                }
                deviceVo.setDelFlag(0);
                deviceVos.add(deviceVo);

                CrmProjectProductSnVO snVO = new CrmProjectProductSnVO();
                snVO.setPsn(sn);
                snVO.setType("生产灌装");
                snVO.setRecordId(product.getProjectProductOwnId());
                snVO.setStuffCode(product.getStuffCode());
                projectSns.add(snVO);

                if (isImportantContractIn != null && isImportantContractIn) {
                    // 重点行业备货
                    CrmContractReviewProductSnIn in = new CrmContractReviewProductSnIn();
                    in.setContractRecordId(product.getId());
                    in.setContractCompanyName(signContractDTO.getContractCompanyName());
                    in.setContractCompanyId(signContractDTO.getContractCompanyId());
                    in.setSn(sn);
                    in.setContractId(product.getContractReviewId());
                    in.setProjectRecordId(product.getProjectProductOwnId());
                    ins.add(in);
                } else {
                    // 普通产品出货
                    // todo 默认这个out表存在生产推过来的数据以及项目中关联的序列号 老版的视图都会存在，新版的方案暂未确定。
                    CrmContractReviewProductSnOut out = new CrmContractReviewProductSnOut();
                    out.setContractRecordId(product.getId());
                    out.setProductId(product.getProductId());
                    out.setSn(sn);
                    out.setContractId(product.getContractReviewId());
                    out.setProjectRecordId(product.getProjectProductOwnId());
                    outs.add(out);
                }
            }

        });

        // todo sn写out表 因为旧版是生产把借试用那些的序列号也返回了 所以out表是代表出货的序列号表 具体设计未定
        CrmContractReviewProductSnOutService outService = SpringUtil.getBean(CrmContractReviewProductSnOutService.class);
        CrmContractReviewProductSnInService inService = SpringUtil.getBean(CrmContractReviewProductSnInService.class);

        remoteDeviceService.saveOrUpdateBatch(deviceVos);
        // 直接插入项目中
        remoteProjectProductSnClient.addProductSnFromPerformanceReport(projectSns);
        if (CollectionUtils.isNotEmpty(outs)) {
            outService.saveBatch(outs);
        }
        if (isImportantContractIn != null && isImportantContractIn) {
            // 重点行业备货
            if (CollectionUtils.isNotEmpty(snVOS)) {
                List<CrmContractReviewProductSnIn> list = snVOS.stream().map(item -> {
                    CrmContractReviewProductSnIn in = new CrmContractReviewProductSnIn();
                    in.setContractRecordId(item.getContractRecordId());
                    in.setContractCompanyName(signContractDTO.getContractCompanyName());
                    in.setContractCompanyId(signContractDTO.getContractCompanyId());
                    in.setSn(item.getSn());
                    in.setContractId(item.getContractId());
                    in.setProjectRecordId(item.getProjectRecordId());
                    return in;
                }).toList();
                inService.saveBatch(list);
            }
        } else {
            List<CrmContractReviewProductSnOut> crmContractReviewProductSnOut = HyperBeanUtils.copyListProperties(snVOS, CrmContractReviewProductSnOut::new);
            if (CollectionUtils.isNotEmpty(crmContractReviewProductSnOut)) {
                crmContractReviewProductSnOut.forEach(item -> item.setId(null));
                outService.saveBatch(crmContractReviewProductSnOut);
            }
        }
        if (CollectionUtils.isNotEmpty(ins)) {
            inService.saveBatch(ins);
        }
        // 存redis 标识这个合同已经推过了
        redisTemplate.opsForHash().put(CONTRACT_SN_PUSH, contractReviewMain.getId(), "1");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean backToThree(String processInstanceId) {
        // 退回时 逻辑删除所有保存的内容
        CrmContractReviewMain contract = getOne(new LambdaQueryWrapper<CrmContractReviewMain>().eq(CrmContractReviewMain::getProcessInstanceId, processInstanceId)
                .eq(CrmContractReviewMain::getDelFlag, 0).last("limit 1"));
        if (contract == null) {
            throw new CrmException("流程实例不存在");
        }
        String contractId = contract.getId();
        CrmContractExecuteService executeService = SpringUtil.getBean(CrmContractExecuteService.class);
        executeService.deleteContractExecute(contract.getContractNumber());
        return deleteContract(contractId);
    }

    public boolean deleteContract(String contractId) {
        update(new LambdaUpdateWrapper<CrmContractReviewMain>()
                .set(CrmContractReviewMain::getDelFlag, 1).eq(CrmContractReviewMain::getId, contractId));
        crmContractReviewCustomerService.update(new LambdaUpdateWrapper<CrmContractReviewCustomer>()
                .set(CrmContractReviewCustomer::getDelFlag, 1)
                .eq(CrmContractReviewCustomer::getContractReviewMainId, contractId));
        crmContractReviewSignContractService.update(new LambdaUpdateWrapper<CrmContractReviewSignContract>()
                .set(CrmContractReviewSignContract::getDelFlag, 1)
                .eq(CrmContractReviewSignContract::getContractReviewMainId, contractId));
        crmContractReviewNoticeArriveService.update(new LambdaUpdateWrapper<CrmContractReviewNoticeArrive>()
                .set(CrmContractReviewNoticeArrive::getDelFlag, 1)
                .eq(CrmContractReviewNoticeArrive::getContractReviewMainId, contractId));

        // 附件
        crmContractReviewAttachmentService.update(new LambdaUpdateWrapper<CrmContractReviewAttachment>()
                .set(CrmContractReviewAttachment::getDelFlag, 1)
                .eq(CrmContractReviewAttachment::getContractReviewMainId, contractId));
        // 条款
        crmContractReviewRetentionMoneyService.update(new LambdaUpdateWrapper<CrmContractReviewRetentionMoney>()
                .set(CrmContractReviewRetentionMoney::getDelFlag, 1)
                .eq(CrmContractReviewRetentionMoney::getContractReviewMainId, contractId));
        contractReviewPaymentProvisionService.update(new LambdaUpdateWrapper<CrmContractReviewPaymentProvision>()
                .eq(CrmContractReviewPaymentProvision::getContractReviewMainId, contractId)
                .set(CrmContractReviewPaymentProvision::getDelFlag, 1));
        contractReviewRevenueRecognitionService.update(new LambdaUpdateWrapper<CrmContractReviewRevenueRecognition>()
                .set(CrmContractReviewRevenueRecognition::getDelFlag, 1)
                .eq(CrmContractReviewRevenueRecognition::getContractReviewMainId, contractId));

        // 产品
        crmContractReviewProductOwnService.update(new LambdaUpdateWrapper<CrmContractReviewProductOwn>()
                .set(CrmContractReviewProductOwn::getDelFlag, 1)
                .eq(CrmContractReviewProductOwn::getContractReviewId, contractId));
        crmContractReviewProductThirdService.update(new LambdaUpdateWrapper<CrmContractReviewProductThird>()
                .set(CrmContractReviewProductThird::getDelFlag, 1)
                .eq(CrmContractReviewProductThird::getContractReviewId, contractId));

        // 发货
        CrmContractReviewDelivery delivery = crmContractReviewDeliveryService.getOne(new LambdaQueryWrapper<CrmContractReviewDelivery>()
                .eq(CrmContractReviewDelivery::getContractId, contractId).eq(CrmContractReviewDelivery::getDelFlag, 0).last("limit 1"));
        if (delivery != null) {
            crmContractReviewDeliveryService.update(new LambdaUpdateWrapper<CrmContractReviewDelivery>()
                    .set(CrmContractReviewDelivery::getDelFlag, 1)
                    .eq(CrmContractReviewDelivery::getId, delivery.getId()));
            crmContractReviewDeliveryDetailOwnService.update(new LambdaUpdateWrapper<CrmContractReviewDeliveryDetailOwn>()
                    .set(CrmContractReviewDeliveryDetailOwn::getDelFlag, 1)
                    .eq(CrmContractReviewDeliveryDetailOwn::getDeliveryId, delivery.getId()));
            crmContractReviewDeliveryDetailThirdService.update(new LambdaUpdateWrapper<CrmContractReviewDeliveryDetailThird>()
                    .set(CrmContractReviewDeliveryDetailThird::getDelFlag, 1)
                    .eq(CrmContractReviewDeliveryDetailThird::getDeliveryId, delivery.getId()));
        }
        // 序列号
        crmContractReviewProductSnService.update(new LambdaUpdateWrapper<CrmContractReviewProductSn>()
                .set(CrmContractReviewProductSn::getDelFlag, 1)
                .eq(CrmContractReviewProductSn::getContractId, contractId));
        return true;
    }

    ;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean backCompleted(String processInstanceId) {
        CrmContractReviewMain contract = getOne(new LambdaQueryWrapper<CrmContractReviewMain>().eq(CrmContractReviewMain::getProcessInstanceId, processInstanceId)
                .eq(CrmContractReviewMain::getDelFlag, 0));
        if (contract == null) {
            throw new CrmException("流程实例不存在");
        }
        // 考核状态、 考核时间清空
        update(new LambdaUpdateWrapper<CrmContractReviewMain>()
                .set(CrmContractReviewMain::getAssessmentStatus, 0)
                .eq(CrmContractReviewMain::getId, contract.getId())
                .eq(CrmContractReviewMain::getDelFlag, 0));
        crmContractReviewProductOwnService.update(new LambdaUpdateWrapper<CrmContractReviewProductOwn>()
                .set(CrmContractReviewProductOwn::getAssessmentTime, null)
                .eq(CrmContractReviewProductOwn::getContractReviewId, contract.getId())
                .eq(CrmContractReviewProductOwn::getDelFlag, 0));
        crmContractReviewProductThirdService.update(new LambdaUpdateWrapper<CrmContractReviewProductThird>()
                .set(CrmContractReviewProductThird::getAssessmentTime, null)
                .eq(CrmContractReviewProductThird::getContractReviewId, contract.getId())
                .eq(CrmContractReviewProductThird::getDelFlag, 0));
        return true;
    }

    @Override
    public boolean checkContractNumberExist(String contractNumber) {
        long count = count(new LambdaQueryWrapper<CrmContractReviewMain>().eq(CrmContractReviewMain::getContractNumber, contractNumber)
                .eq(CrmContractReviewMain::getDelFlag, 0));
        return count > 0;
    }

    @Override
    public CrmContractBaseInfoVO getByContractId(String contractId) {
        CrmContractReviewMain crmContractReviewMain = getOne(new LambdaQueryWrapper<CrmContractReviewMain>().eq(CrmContractReviewMain::getId, contractId)
                .eq(CrmContractReviewMain::getDelFlag, false)
                .last("limit 1"));
        return HyperBeanUtils.copyProperties(crmContractReviewMain, CrmContractBaseInfoVO::new);
    }

    @Override
    public CrmContractBaseInfoVO getByProcessInstanceId(String processInstanceId) {
        CrmContractReviewMain crmContractReviewMain = getOne(new LambdaQueryWrapper<CrmContractReviewMain>().eq(CrmContractReviewMain::getProcessInstanceId, processInstanceId)
                .eq(CrmContractReviewMain::getDelFlag, false)
                .last("limit 1"));
        return HyperBeanUtils.copyProperties(crmContractReviewMain, CrmContractBaseInfoVO::new);
    }

    @Override
    public List<CrmContractBaseInfoVO> getByContractNumber(String contractNumber) {
        List<CrmContractReviewMain> crmContractReviewMain = list(new LambdaQueryWrapper<CrmContractReviewMain>().eq(CrmContractReviewMain::getContractNumber, contractNumber)
                .eq(CrmContractReviewMain::getDelFlag, false));
        return HyperBeanUtils.copyListProperties(crmContractReviewMain, CrmContractBaseInfoVO::new);
    }

    @Override
    public CrmContractBaseInfoVO getFirstByContractNumber(String contractNumber) {
        CrmContractReviewMain crmContractReviewMain = getOne(new LambdaQueryWrapper<CrmContractReviewMain>().eq(CrmContractReviewMain::getContractNumber, contractNumber)
                .eq(CrmContractReviewMain::getDelFlag, false)
                .orderByAsc(CrmContractReviewMain::getValidDate)
                .last("limit 1"));
        return HyperBeanUtils.copyProperties(crmContractReviewMain, CrmContractBaseInfoVO::new);
    }

    @Override
    public List<CrmContractBaseInfoVO> getByContractIdBatch(List<String> contractIds) {
        if (CollectionUtils.isEmpty(contractIds)) {
            return Collections.emptyList();
        }
        List<CrmContractReviewMain> contracts = list(new LambdaQueryWrapper<CrmContractReviewMain>().in(CrmContractReviewMain::getId, contractIds)
                .eq(CrmContractReviewMain::getDelFlag, false));
        return HyperBeanUtils.copyListPropertiesByJackson(contracts, CrmContractBaseInfoVO.class);
    }

    @Override
    public List<CrmContractBaseInfoVO> getContractInfoByParams(CrmContractInfoQuery query) {
        List<CrmContractReviewMain> list = list(new LambdaQueryWrapper<CrmContractReviewMain>().eq(CrmContractReviewMain::getDelFlag, false)
                .like(StringUtils.isNotEmpty(query.getSaleName()), CrmContractReviewMain::getSaleName, query.getSaleName())
                .like(StringUtils.isNotEmpty(query.getContractNumber()), CrmContractReviewMain::getContractNumber, query.getContractNumber())
                .like(StringUtils.isNotEmpty(query.getSaleDeptName()), CrmContractReviewMain::getSaleDeptName, query.getSaleDeptName())
        );
        List<CrmContractBaseInfoVO> crmContractBaseInfoVOS = HyperBeanUtils.copyListProperties(list, CrmContractBaseInfoVO::new);
        List<String> projectIds = crmContractBaseInfoVOS.stream().filter(v -> v.getProjectSource() != null && v.getProjectSource() == 1).map(CrmContractBaseInfoVO::getProjectId).toList();
        List<String> dynastyProjectIds = crmContractBaseInfoVOS.stream().filter(v -> v.getProjectSource() != null && v.getProjectSource() == 3).map(CrmContractBaseInfoVO::getProjectId).toList();
        if (CollectionUtils.isEmpty(projectIds) && CollectionUtils.isEmpty(dynastyProjectIds)) {
            return Collections.emptyList();
        }
        Map<String, CrmProjectDirectlyVo> projectMap;
        Map<String, CrmProjectDynastyVo> projectDynastyVoMap;
        try {
            List<CrmProjectDirectlyVo> projectDirectlyVos = remoteProjectDirectlyClient.listByIds(projectIds).getObjEntity();
            projectMap = projectDirectlyVos.stream().collect(Collectors.toMap(CrmProjectDirectlyVo::getId, v -> v, (v1, v2) -> v1));
        } catch (Exception e) {
            // 垃圾数据查项目查不出来 不处理
            log.warn("查询项目失败", e);
            projectMap = new HashMap<>();
        }
        try {
            List<CrmProjectDynastyVo> projectDynastyVos = remoteProjectDynastyService.batchGetDynastyInfo(dynastyProjectIds).getObjEntity();
            projectDynastyVoMap = projectDynastyVos.stream().collect(Collectors.toMap(CrmProjectDynastyVo::getId, v -> v, (v1, v2) -> v1));
        } catch (Exception e) {
            // 垃圾数据查项目查不出来 不处理
            log.warn("查询项目失败", e);
            projectDynastyVoMap = new HashMap<>();
        }
        for (CrmContractBaseInfoVO item : crmContractBaseInfoVOS) {
            String projectNo;
            String projectName;
            if (item.getProjectSource() != null && item.getProjectSource() == 1) {
                projectNo = Optional.ofNullable(projectMap.get(item.getProjectId())).map(CrmProjectDirectlyVo::getProjectNo).orElse("");
                projectName = Optional.ofNullable(projectMap.get(item.getProjectId())).map(CrmProjectDirectlyVo::getProjectName).orElse("");
            } else if (item.getProjectSource() != null && item.getProjectSource() == 3) {
                projectNo = Optional.ofNullable(projectDynastyVoMap.get(item.getProjectId())).map(CrmProjectDynastyVo::getProjectNo).orElse("");
                projectName = Optional.ofNullable(projectDynastyVoMap.get(item.getProjectId())).map(CrmProjectDynastyVo::getProjectName).orElse("");
            } else {
                projectNo = "";
                projectName = "";
            }
            item.setProjectNo(projectNo);
            item.setProjectName(projectName);
        }
        return crmContractBaseInfoVOS;
    }

    @Override
    public boolean updateBaseInfo(CrmContractBaseInfoVO baseInfoVO) {
        return update(new LambdaUpdateWrapper<CrmContractReviewMain>()
                .eq(CrmContractReviewMain::getId, baseInfoVO.getId())
                .set(baseInfoVO.getIsClassified() != null, CrmContractReviewMain::getIsClassified, baseInfoVO.getIsClassified())
                .set(baseInfoVO.getEstimatedTime() != null, CrmContractReviewMain::getEstimatedTime, baseInfoVO.getEstimatedTime())
                .set(baseInfoVO.getKeyWord() != null, CrmContractReviewMain::getKeyWord, JSON.toJSONString(baseInfoVO.getKeyWord())));
    }

    @Override
    public List<CrmContractBaseInfoVO> getByContractNumberBatch(List<String> contractNumbers) {
        if (CollectionUtils.isEmpty(contractNumbers)) {
            return Collections.emptyList();
        }
        List<CrmContractReviewMain> crmContractReviewMain = list(new LambdaQueryWrapper<CrmContractReviewMain>().in(CrmContractReviewMain::getContractNumber, contractNumbers)
                .eq(CrmContractReviewMain::getDelFlag, false));
        return HyperBeanUtils.copyListProperties(crmContractReviewMain, CrmContractBaseInfoVO::new).stream().map(crmContractBaseInfoVO -> {
            crmContractBaseInfoVO.setOriginalType(OriginalTypeEnum.CONTRACT_VIEW.getSource());
            return crmContractBaseInfoVO;
        }).toList();
    }

    @Override
    public Boolean pushProdByProcessInstanceId(String processInstanceId) {
        try {
            CrmContractReviewMain contract = getOne(new LambdaQueryWrapper<CrmContractReviewMain>().eq(CrmContractReviewMain::getProcessInstanceId, processInstanceId)
                    .eq(CrmContractReviewMain::getDelFlag, 0));
            CrmContractReviewService reviewService = SpringUtil.getBean(CrmContractReviewService.class);
            List<CrmContractProductOwnVO> products = reviewService.queryOwnProductByProcessInstanceIdBatch(Set.of(processInstanceId));
            if (CollectionUtils.isNotEmpty(products)) {
                Object o = redisTemplate.opsForHash().get(CONTRACT_SN_PUSH, contract.getId());
                if (o == null) {
                    pushProd(products, contract);
                }
            }
        } catch (Exception e) {
            log.error("推送产品失败", e);
            throw new CrmException("推送产品失败");
        }
        return true;
    }

    @Override
    public Boolean hasOriginalDocumentRecord(String processInstanceId) {
        CrmContractBaseInfoVO baseInfo = this.getByProcessInstanceId(processInstanceId);
        CrmContractOriginalDocumentService originalDocumentService = SpringUtil.getBean(CrmContractOriginalDocumentService.class);
        List<CrmContractOriginalDocument> list = originalDocumentService.list(new LambdaQueryWrapper<CrmContractOriginalDocument>()
                .eq(CrmContractOriginalDocument::getContractReviewMainId, baseInfo.getId())
                .eq(CrmContractOriginalDocument::getDelFlag, 0));
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        return list.stream().anyMatch(item -> StringUtils.isNotEmpty(item.getProcessInstanceId()));
    }

    @Override
    public Boolean rollbackByReturnExchange(String returnExchangeProcessInstanceId) {
        // 合同服务的直接全部删掉
        crmContractReviewProductOwnService.update(new LambdaUpdateWrapper<CrmContractReviewProductOwn>()
                .set(CrmContractReviewProductOwn::getDelFlag, true)
                .eq(CrmContractReviewProductOwn::getReturnExchangeProcessInstanceId, returnExchangeProcessInstanceId));
        crmContractReviewProductThirdService.update(new LambdaUpdateWrapper<CrmContractReviewProductThird>()
                .set(CrmContractReviewProductThird::getDelFlag, true)
                .eq(CrmContractReviewProductThird::getReturnExchangeProcessInstanceId, returnExchangeProcessInstanceId));
        contractReviewPaymentProvisionService.update(new LambdaUpdateWrapper<CrmContractReviewPaymentProvision>()
                .set(CrmContractReviewPaymentProvision::getDelFlag, true)
                .eq(CrmContractReviewPaymentProvision::getReturnExchangeProcessInstanceId, returnExchangeProcessInstanceId));
        contractReviewRevenueRecognitionService.update(new LambdaUpdateWrapper<CrmContractReviewRevenueRecognition>()
                .set(CrmContractReviewRevenueRecognition::getDelFlag, true)
                .eq(CrmContractReviewRevenueRecognition::getReturnExchangeProcessInstanceId, returnExchangeProcessInstanceId));
        crmContractReviewProductSnService.update(new LambdaUpdateWrapper<CrmContractReviewProductSn>()
                .set(CrmContractReviewProductSn::getDelFlag, true)
                .eq(CrmContractReviewProductSn::getReturnExchangeProcessInstanceId, returnExchangeProcessInstanceId));
        return true;
    }
}
