
create view [dbo].[st_military_product_inspection]
as
select jpjy1_id as id,null as process_instance_id,2 as process_state,jpjy1_dh as process_number,sp1.ywsp2_in_time as process_pass_time,
       jpjy1.xmgj1_id as project_id,xmgj1.xmgj1_xmid as project_no,case when jpjy1_sfjj = '是' or jpjy1_sfjj = '厂/军检' then 1 when jpjy1_sfjj = '仅/厂检' then 2 else 0 end as inspection_type,
       case when jpjy1_sfdz = '是' then 1 when jpjy1_sfdz = '否' then 0 else null end as customized,jpjy1_dzdh as customized_order_number,case when jpjy1_sfsm = '是' then 1 when jpjy1_sfsm = '否' then 0 else null end as sm,
       jpjy1_jgjds as military_supervision_office,jpjy1_yjcjsj as expected_factory_inspection_time,jpjy1_yjjjsj as expected_military_inspection_time, null as attachment_ids,
       jpjy1_in_time as create_time,jpjy1_up_time as update_time,g1.gscy1_uuid as create_user,g2.gscy1_uuid as update_user,0 del_flag,null as defined_military_inspection_time
from jpjy1
         left join (
    select lcid1,max(spbz1) spbz1,max(ywsp2_in_time) ywsp2_in_time from hs_splc2('军品检验')  where hs_splc2.spbz1='12结束' group by lcid1
) as sp1 on jpjy1_id = sp1.lcid1
         left join xmgj1 on jpjy1.xmgj1_id = xmgj1.xmgj1_id
         left join gscy1 g1 on g1.gscy1_id = jpjy1_in_id
         left join gscy1 g2 on g2.gscy1_name = jpjy1_in_name
where jpjy1_del = 'add' and jpjy1_dh is not null
go

update military_product_inspection
    join (
    select process_instance_id,crm_business_id,process_number,process_definition_key from process_extension_info where del_flag = 0
    ) as x1 on military_product_inspection.id = x1.crm_business_id and x1.process_definition_key = 'militaryInspection'
    set military_product_inspection.process_instance_id = x1.process_instance_id,military_product_inspection.process_number = x1.process_number;

update process_extension_info
    join (
    select process_instance_id,project_id from team_building where del_flag = 0
    ) as x1 on process_extension_info.process_instance_id = x1.process_instance_id
    set process_extension_info.project_id = x1.project_id
