package com.topsec.crm.operation.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.operation.api.dto.TransactionPriceDiscountQuery;
import com.topsec.crm.operation.api.entity.CompetitiveProductTransactionPriceVO;
import com.topsec.crm.operation.core.entity.CrmCompetitiveProductTransactionPrice;
import com.topsec.crm.operation.core.mapper.CrmCompetitiveProductMapper;
import com.topsec.crm.operation.core.mapper.CrmCompetitiveProductTransactionPriceMapper;
import com.topsec.crm.operation.core.service.CrmCompetitiveProductTransactionPriceService;
import com.topsec.tos.common.HyperBeanUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2023-10-18  16:03
 * @Description:
 */
@Service
public class CrmCompetitiveProductTransactionPriceServiceImpl extends ServiceImpl<CrmCompetitiveProductTransactionPriceMapper, CrmCompetitiveProductTransactionPrice> implements CrmCompetitiveProductTransactionPriceService {
    @Resource
    private CrmCompetitiveProductMapper crmCompetitiveProductMapper;
    @Override
    public TableDataInfo selectPageDataInfo(String competitiveProductId) {
        List<CrmCompetitiveProductTransactionPrice> crmCompetitiveProductTransactionPrices = baseMapper.selectList(new QueryWrapper<CrmCompetitiveProductTransactionPrice>()
                .eq(StringUtils.isNotEmpty(competitiveProductId), "competitive_product_id", competitiveProductId)
                .eq("del_flag", false)
                .orderByDesc("create_time")
        );
        List<CompetitiveProductTransactionPriceVO> competitiveProductTransactionPriceVOS = HyperBeanUtils.copyListProperties(crmCompetitiveProductTransactionPrices, CompetitiveProductTransactionPriceVO::new);
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setTotalCount(new PageInfo(crmCompetitiveProductTransactionPrices).getTotal());
        tableDataInfo.setList(competitiveProductTransactionPriceVOS);
        return tableDataInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdate(CompetitiveProductTransactionPriceVO competitiveProductTransactionPriceVO) {
        CrmCompetitiveProductTransactionPrice productTransactionPrice = HyperBeanUtils.copyProperties(competitiveProductTransactionPriceVO, CrmCompetitiveProductTransactionPrice::new);
        return saveOrUpdate(productTransactionPrice);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String id) {
        CrmCompetitiveProductTransactionPrice crmCompetitiveProductTransactionPrice = getById(id);
        if (crmCompetitiveProductTransactionPrice==null){
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
        crmCompetitiveProductTransactionPrice.setDelFlag(true);
        return updateById(crmCompetitiveProductTransactionPrice);
    }

    @Override
    public TransactionPriceDiscountQuery discount(TransactionPriceDiscountQuery transactionPriceDiscountQuery) {

        if (transactionPriceDiscountQuery.getCompetitionProductOffer()!=null){
            BigDecimal quotient =transactionPriceDiscountQuery.getTransactionPrice().divide(transactionPriceDiscountQuery.getCompetitionProductOffer(), 4, RoundingMode.HALF_UP);
            transactionPriceDiscountQuery.setDiscount(quotient);
        }

        return transactionPriceDiscountQuery;
    }
}