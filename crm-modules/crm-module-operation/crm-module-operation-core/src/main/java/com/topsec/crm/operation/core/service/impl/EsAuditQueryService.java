package com.topsec.crm.operation.core.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.GetRequest;
import co.elastic.clients.elasticsearch.core.GetResponse;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import co.elastic.clients.elasticsearch.core.search.TotalHits;
import co.elastic.clients.json.JsonData;
import com.topsec.crm.framework.common.util.date.DateUtil;
import com.topsec.soft4j.audit.*;
import com.topsec.tbsapi.client.TbsRoleClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.RoleVO;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class EsAuditQueryService implements QueryService {
    @Resource
    private ElasticsearchClient elasticsearchClient;
    @Resource
    private AuditProperties auditProperties;
    @Resource
    private TbsRoleClient tbsRoleClient;


    @Override
    public String getIndex() {
        return getIndexPrefix()+"*";
    }

    private String getIndexPrefix() {
        return String.format("log-audit-%s", auditProperties.getProjectName());
    }

    @Override
    public List<AuditAttribute> listAll() {
        return Collections.emptyList();
    }

    @SneakyThrows
    @Override
    public PageUtils<AuditAttribute> page(AuditParam auditParam) {
        BoolQuery boolQuery = buildBasicQuery(auditParam);
        Integer currPage = auditParam.getCurrPage();
        Integer pageSize = auditParam.getPageSize();


        // 构建初始搜索请求
        SearchRequest initialSearchRequest = SearchRequest.of(s -> s
                .index(getIndex())
                .query(boolQuery._toQuery())
                .sort(SortOptions.of(builder ->
                        builder.field(f -> f.field("opTime.keyword").order(SortOrder.Desc))
                ))
                .source(sourceBuilder -> sourceBuilder
                        .filter(filter -> filter
                                .includes("id", "userAccount", "userRoles",
                                        "modelName", "operation","opDesc","opTime","srcIp") //
                                // .excludes("body")
                        )
                )
                .from((currPage-1)*pageSize)
                .size(pageSize)
        );

        // 执行初始搜索并处理响应
        SearchResponse<AuditAttribute> initialResponse = elasticsearchClient.search(initialSearchRequest, AuditAttribute.class);

        HitsMetadata<AuditAttribute> hits = initialResponse.hits();

        long totalCount = Optional.ofNullable(hits.total()).map(TotalHits::value).orElse(0L);
        List<AuditAttribute> collect = Optional.of(initialResponse.hits()).map(HitsMetadata::hits)
                .orElse(Collections.emptyList())
                .stream()
                .map(Hit::source)
                .collect(Collectors.toList());
        List<String> roleIds = collect.stream().map(AuditAttribute::getUserRoles)
                .filter(roleId -> {
                    return !CollectionUtils.isEmpty(roleId);
                })
                .flatMap(Set::stream)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        Map<String, RoleVO> roleMap = Optional.ofNullable(tbsRoleClient.selectByIds(roleIds))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(RoleVO::getUuid, roleVO -> roleVO));
        for (AuditAttribute auditAttribute : collect) {
            Set<String> userRoles = auditAttribute.getUserRoles();
            Set<String> roleNames = Optional.ofNullable(userRoles).orElse(Collections.emptySet())
                    .stream()
                    .map(roleId -> Optional.ofNullable(roleMap.get(roleId)).map(RoleVO::getRoleName).orElse(null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            auditAttribute.setUserRoles(roleNames);
        }
        return new PageUtils<>(collect, totalCount, pageSize, currPage);
    }

    @Override
    public List<AuditAttribute> listByIds(List<String> ids) {
        return Collections.emptyList();
    }


    /**
     * 构建基本查询 - 搜索角色、功能、开始~结束时间、账号/密码-keyword
     */
    private BoolQuery buildBasicQuery(AuditParam auditParam) {
        BoolQuery.Builder boolQueryBuilder=new BoolQuery.Builder();

        /*
         角色（模糊查询-一人多角色，不分词）
         功能（精准查询），无需分词
         开始~结束时间 范围查询
         功能名称/账号都是模糊查询，不分词
         */
        // 角色（模糊查询-一人多角色，不分词）
        if (StringUtils.hasText(auditParam.getRole())){
            boolQueryBuilder.filter(
                    MatchQuery.of(r -> r
                    .field("userRoles")
                    .query(auditParam.getRole()))._toQuery()
            );
        }
        // 功能（精准查询），无需分词
        if(StringUtils.hasText(auditParam.getFunctionModel())){
            boolQueryBuilder.filter(
                    MatchQuery.of(r -> r
                            .field("modelName.keyword")
                            .query(auditParam.getFunctionModel()))._toQuery()
            );

        }
        // 开始~结束时间 范围查询
        if(StringUtils.hasText(auditParam.getOpStartTime())&&StringUtils.hasText(auditParam.getOpEndTime())){
            Query opTimeQ = RangeQuery.of(r -> r
                    .field("opTime.keyword")
                    .gt(JsonData.of(DateUtil.DATE_TIME_FORMATTER.format(LocalDateTime.parse(auditParam.getOpStartTime()))))
                    .lt(JsonData.of(DateUtil.DATE_TIME_FORMATTER.format(LocalDateTime.parse(auditParam.getOpEndTime()))))

            )._toQuery();

            boolQueryBuilder.filter(opTimeQ);
        }

        //功能名称和账号都是模糊查询
        if (StringUtils.hasText(auditParam.getSearchKey())&&StringUtils.hasText(auditParam.getSearchKeyType())) {
            // 在关键词前后添加通配符 * 实现类似 LIKE 的效果
            String searchKey = "*" + auditParam.getSearchKey() + "*";
            if (auditParam.getSearchKeyType().equals("userAccount")) {
                boolQueryBuilder.filter( WildcardQuery.of(r -> r
                        .field("userAccount.keyword") // 建议使用 keyword 类型字段
                        .value(searchKey))._toQuery());
            }
            if (auditParam.getSearchKeyType().equals("eventDesc")) {
                boolQueryBuilder.filter( WildcardQuery.of(r -> r
                        .field("operation.keyword") // 建议使用 keyword 类型字段
                        .value(searchKey))._toQuery());
            }
        }
        return boolQueryBuilder.build();
    }

    @SneakyThrows
    @Override
    public AuditAttribute detail(String id) {
        return null;
    }

    @SneakyThrows
    @Override
    public AuditAttribute detail(String id, LocalDate localDate) {
        String esIndex=String.format("%s-%s", getIndexPrefix(), localDate);
        GetRequest request = GetRequest.of(b -> b
                        .index(esIndex)
                        // 索引名称
                        .id(id)
        );
        GetResponse<AuditAttribute> response = elasticsearchClient.get(request, AuditAttribute.class);
        return response.source();   // 返回匹配的文档对象
    }
}
