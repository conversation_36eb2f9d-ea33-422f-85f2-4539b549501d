package com.topsec.crm.product.core.entity;

import com.topsec.crm.framework.common.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 *  crm_product_wholesale_price
 *
 */
@Data
public class CrmProductWholesalePrice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private String id;

    /**
     *物料代码
     */
    private String stuffCode;

    /**
     *批发价
     */
    private BigDecimal wholesalePrice;
    /**
     *sellin价格
     */
    private BigDecimal sellinPrice;

    /** 标记删除 0-未删除 1-已删除 */
    private Integer delFlag;

    /** 创建人 */
    private String createUser;

    /** 更新人 */
    private String updateUser;
}
