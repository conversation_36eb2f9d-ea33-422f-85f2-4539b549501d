package com.topsec.crm.product.core.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.framework.common.enums.MaterialEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.common.util.date.DateUtil;
import com.topsec.crm.framework.common.util.date.DateUtils;
import com.topsec.crm.product.api.RemoteProductService;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.product.core.entity.CrmMaterialDisable;
import com.topsec.crm.product.core.entity.CrmMaterialScope;
import com.topsec.crm.product.core.entity.CrmProduct;
import com.topsec.crm.product.core.service.ICrmMaterialScopeService;
import com.topsec.crm.product.core.service.ICrmProductService;
import com.topsec.tbscommon.JsonObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.topsec.crm.product.core.mapper.CrmMaterialControlMapper;
import com.topsec.crm.product.core.entity.CrmMaterialControl;
import com.topsec.crm.product.core.service.ICrmMaterialControlService;

/**
 * 产品控制Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-28
 */
@Service
public class CrmMaterialControlServiceImpl extends ServiceImpl<CrmMaterialControlMapper, CrmMaterialControl> implements ICrmMaterialControlService
{
    @Autowired
    private CrmMaterialControlMapper crmMaterialControlMapper;
    @Autowired
    private ICrmMaterialScopeService crmMaterialScopeService;
    @Autowired
    private ICrmProductService crmProductService;
    @Qualifier("com.topsec.crm.product.api.RemoteProductService")
    @Autowired
    private RemoteProductService remoteProductService;

    /**
     * 查询产品控制
     *
     * @param id 产品控制ID
     * @return 产品控制
     */
    @Override
    public CrmMaterialControl selectCrmMaterialControlById(String id)
    {
        CrmMaterialControl crmMaterialControl = crmMaterialControlMapper.selectCrmMaterialControlById(id);

        //补充产品信息
        CrmProduct crmProduct = crmProductService.selectCrmProductById(crmMaterialControl.getProductId());
        crmMaterialControl.setProductName(crmProduct != null ? crmProduct.getName() : "");

        //查询范围
        CrmMaterialScope param = new CrmMaterialScope();
        param.setLinkId(id);
        param.setDelFlag(0);
        List<CrmMaterialScope> crmMaterialScopes = crmMaterialScopeService.selectCrmMaterialScopeList(param);
        if(CollectionUtil.isNotEmpty(crmMaterialScopes)){
            crmMaterialControl.setDepts(crmMaterialScopes.stream().filter(s -> s.getType() == MaterialEnum.ScopeTypeEnum.DEPT.getCode()).toList());
            crmMaterialControl.setIdentities(crmMaterialScopes.stream().filter(s -> s.getType() == MaterialEnum.ScopeTypeEnum.IDENTITY.getCode()).toList());
            crmMaterialControl.setIndustries(crmMaterialScopes.stream().filter(s -> s.getType() == MaterialEnum.ScopeTypeEnum.INDUSTRY.getCode()).toList());
            crmMaterialControl.setPersons(crmMaterialScopes.stream().filter(s -> s.getType() == MaterialEnum.ScopeTypeEnum.PERSON.getCode()).toList());
        }

        //设置状态
        if (crmMaterialControl.getStatus() != MaterialEnum.STATUS.DEACTIVATE.getCode()) {
            //设置生效状态
            if (crmMaterialControl.getEffectiveType() == 1) {
                crmMaterialControl.setStatus(MaterialEnum.STATUS.EFFECT.getCode());
            } else if (crmMaterialControl.getEffectiveType() == 2) {
                LocalDate now = LocalDate.now();
                if (now.compareTo(crmMaterialControl.getEffectiveStart()) < 0) {
                    crmMaterialControl.setStatus(MaterialEnum.STATUS.WAIT.getCode());
                } else if(now.compareTo(crmMaterialControl.getEffectiveEnd()) > 0){
                    crmMaterialControl.setStatus(MaterialEnum.STATUS.EXPIRE.getCode());
                } else {
                    crmMaterialControl.setStatus(MaterialEnum.STATUS.EFFECT.getCode());
                }
            }
        }

        return crmMaterialControl;
    }

    /**
     * 查询产品控制列表
     *
     * @param crmMaterialControl 产品控制
     * @return 产品控制
     */
    @Override
    public List<CrmMaterialControl> selectCrmMaterialControlList(CrmMaterialControl crmMaterialControl)
    {
        List<CrmMaterialControl> crmMaterialControls = crmMaterialControlMapper.selectCrmMaterialControlList(crmMaterialControl);

        if(CollectionUtil.isNotEmpty(crmMaterialControls)) {
            List<String> productIds = crmMaterialControls.stream().map(CrmMaterialControl::getProductId).toList();
            List<CrmProduct> crmProducts = crmProductService.selectCrmProductByIds(productIds);
            for (CrmMaterialControl materialControl : crmMaterialControls) {
                CrmProduct crmProduct = crmProducts.stream().filter(c -> c.getId().equals(materialControl.getProductId())).findFirst().orElse(null);
                materialControl.setProductName(crmProduct != null ? crmProduct.getName() : "");

                //查询范围
                CrmMaterialScope param = new CrmMaterialScope();
                param.setLinkId(materialControl.getId());
                param.setDelFlag(0);
                List<CrmMaterialScope> crmMaterialScopes = crmMaterialScopeService.selectCrmMaterialScopeList(param);
                if (CollectionUtil.isNotEmpty(crmMaterialScopes)) {
                    materialControl.setDepts(crmMaterialScopes.stream().filter(s -> s.getType() == MaterialEnum.ScopeTypeEnum.DEPT.getCode()).toList());
                    materialControl.setIdentities(crmMaterialScopes.stream().filter(s -> s.getType() == MaterialEnum.ScopeTypeEnum.IDENTITY.getCode()).toList());
                    materialControl.setIndustries(crmMaterialScopes.stream().filter(s -> s.getType() == MaterialEnum.ScopeTypeEnum.INDUSTRY.getCode()).toList());
                    materialControl.setPersons(crmMaterialScopes.stream().filter(s -> s.getType() == MaterialEnum.ScopeTypeEnum.PERSON.getCode()).toList());
                }

                //设置状态
                if (materialControl.getStatus() != MaterialEnum.STATUS.DEACTIVATE.getCode()) {
                    //设置生效状态
                    if (materialControl.getEffectiveType() == 1) {
                        materialControl.setStatus(MaterialEnum.STATUS.EFFECT.getCode());
                    } else if (materialControl.getEffectiveType() == 2) {
                        LocalDate now = LocalDate.now();
                        if (now.compareTo(materialControl.getEffectiveStart()) < 0) {
                            materialControl.setStatus(MaterialEnum.STATUS.WAIT.getCode());
                        } else if(now.compareTo(materialControl.getEffectiveEnd()) > 0){
                            materialControl.setStatus(MaterialEnum.STATUS.EXPIRE.getCode());
                        } else {
                            materialControl.setStatus(MaterialEnum.STATUS.EFFECT.getCode());
                        }
                    }
                }
            }
        }

        return crmMaterialControls;
    }

    /**
     * 新增产品控制
     *
     * @param crmMaterialControl 产品控制
     * @return 结果
     */
    @Override
    public int insertCrmMaterialControl(CrmMaterialControl crmMaterialControl)
    {
        String uuid = UUID.randomUUID().toString();
        //插入禁用范围
        if(CollectionUtil.isNotEmpty(crmMaterialControl.getDepts())){
            //下单部门
            for (CrmMaterialScope dept : crmMaterialControl.getDepts()) {
                dept.setId(UUID.randomUUID().toString());
                dept.setLinkId(uuid);
                dept.setType(MaterialEnum.ScopeTypeEnum.DEPT.getCode());
                crmMaterialScopeService.insertCrmMaterialScope(dept);
            }
        }
        if(CollectionUtil.isNotEmpty(crmMaterialControl.getIdentities())){
            //下单身份
            for (CrmMaterialScope indentity : crmMaterialControl.getIdentities()) {
                indentity.setId(UUID.randomUUID().toString());
                indentity.setLinkId(uuid);
                indentity.setType(MaterialEnum.ScopeTypeEnum.IDENTITY.getCode());
                crmMaterialScopeService.insertCrmMaterialScope(indentity);
            }
        }
        if(CollectionUtil.isNotEmpty(crmMaterialControl.getIndustries())){
            //下单行业
            for (CrmMaterialScope industry : crmMaterialControl.getIndustries()) {
                industry.setId(UUID.randomUUID().toString());
                industry.setLinkId(uuid);
                industry.setType(MaterialEnum.ScopeTypeEnum.INDUSTRY.getCode());
                crmMaterialScopeService.insertCrmMaterialScope(industry);
            }
        }
        if(CollectionUtil.isNotEmpty(crmMaterialControl.getPersons())){
            //下单行业
            for (CrmMaterialScope industry : crmMaterialControl.getPersons()) {
                industry.setId(UUID.randomUUID().toString());
                industry.setLinkId(uuid);
                industry.setType(MaterialEnum.ScopeTypeEnum.PERSON.getCode());
                crmMaterialScopeService.insertCrmMaterialScope(industry);
            }
        }

        //查询产品信息
        CrmProduct crmProduct = crmProductService.selectCrmProductById(crmMaterialControl.getProductId());
        if(crmProduct != null){
            crmMaterialControl.setMaterialCode(crmProduct.getMaterialCode());
            crmMaterialControl.setPn(crmProduct.getPn());
        }

        crmMaterialControl.setId(uuid);
        crmMaterialControl.setCreateTime(LocalDateTime.now());
        crmMaterialControl.setUpdateTime(LocalDateTime.now());
        crmMaterialControl.setCreateUser(UserInfoHolder.getCurrentPersonId());
        return crmMaterialControlMapper.insertCrmMaterialControl(crmMaterialControl);
    }

    /**
     * 修改产品控制
     *
     * @param crmMaterialControl 产品控制
     * @return 结果
     */
    @Override
    public int updateCrmMaterialControl(CrmMaterialControl crmMaterialControl)
    {
        //逻辑删除禁用范围
        crmMaterialScopeService.softDeleteByLinkId(crmMaterialControl.getId());
        //插入禁用范围
        if(CollectionUtil.isNotEmpty(crmMaterialControl.getDepts())){
            //下单部门
            for (CrmMaterialScope dept : crmMaterialControl.getDepts()) {
                dept.setId(UUID.randomUUID().toString());
                dept.setLinkId(crmMaterialControl.getId());
                dept.setType(MaterialEnum.ScopeTypeEnum.DEPT.getCode());
                crmMaterialScopeService.insertCrmMaterialScope(dept);
            }
        }
        if(CollectionUtil.isNotEmpty(crmMaterialControl.getIdentities())){
            //下单身份
            for (CrmMaterialScope indentity : crmMaterialControl.getIdentities()) {
                indentity.setId(UUID.randomUUID().toString());
                indentity.setLinkId(crmMaterialControl.getId());
                indentity.setType(MaterialEnum.ScopeTypeEnum.IDENTITY.getCode());
                crmMaterialScopeService.insertCrmMaterialScope(indentity);
            }
        }
        if(CollectionUtil.isNotEmpty(crmMaterialControl.getIndustries())){
            //下单行业
            for (CrmMaterialScope industry : crmMaterialControl.getIndustries()) {
                industry.setId(UUID.randomUUID().toString());
                industry.setLinkId(crmMaterialControl.getId());
                industry.setType(MaterialEnum.ScopeTypeEnum.INDUSTRY.getCode());
                crmMaterialScopeService.insertCrmMaterialScope(industry);
            }
        }
        if(CollectionUtil.isNotEmpty(crmMaterialControl.getPersons())){
            //下单行业
            for (CrmMaterialScope industry : crmMaterialControl.getPersons()) {
                industry.setId(UUID.randomUUID().toString());
                industry.setLinkId(crmMaterialControl.getId());
                industry.setType(MaterialEnum.ScopeTypeEnum.PERSON.getCode());
                crmMaterialScopeService.insertCrmMaterialScope(industry);
            }
        }

        //查询产品信息
        CrmProduct crmProduct = crmProductService.selectCrmProductById(crmMaterialControl.getProductId());
        if(crmProduct != null){
            crmMaterialControl.setMaterialCode(crmProduct.getMaterialCode());
            crmMaterialControl.setPn(crmProduct.getPn());
        }

        crmMaterialControl.setUpdateTime(LocalDateTime.now());
        crmMaterialControl.setUpdateUser(UserInfoHolder.getCurrentPersonId());
        return crmMaterialControlMapper.updateCrmMaterialControl(crmMaterialControl);
    }

    /**
     * 删除产品控制信息
     *
     * @param id 产品控制ID
     * @return 结果
     */
    @Override
    public int deleteCrmMaterialControlById(String id)
    {
        //逻辑删除禁用范围
        crmMaterialScopeService.softDeleteByLinkId(id);

        //逻辑删除禁用信息
        return crmMaterialControlMapper.updateDelFlag(id);
    }

    /**
     * 【产品控制】规则启用/停用
     */
    @Override
    public void setAvailable(CrmMaterialControl crmMaterialControl) {
        crmMaterialControlMapper.setAvailable(crmMaterialControl);
    }

    /**
     * 代码权限申请列表选择
     */
    @Override
    public List<CrmMaterialControl> listForMaterialApply(CrmMaterialControl crmMaterialControl) {
        List<CrmMaterialControl> result = new ArrayList<CrmMaterialControl>();

        List<CrmMaterialControl> cmcs = crmMaterialControlMapper.selectEffects(crmMaterialControl);
        if(CollectionUtil.isNotEmpty(cmcs)){
            List<String> ids = cmcs.stream().map(CrmMaterialControl::getProductId).toList();
            JsonObject<List<CrmProductVo>> listJsonObject = remoteProductService.batchGetInfo(ids);
            List<CrmProductVo> objEntity = listJsonObject.getObjEntity();
            if(listJsonObject.isSuccess() && objEntity != null) {
                //对同样的productId去重，只保留最新的一条记录
                for (CrmMaterialControl cmc : cmcs) {
                    //补充产品名称
                    CrmProductVo crmProductVo = objEntity.stream().filter(p -> p.getId().equals(cmc.getProductId())).findFirst().orElse(null);
                    cmc.setProductName(crmProductVo == null ? "" : crmProductVo.getName());

                    CrmMaterialControl exist = result.stream().filter(c -> c.getProductId().equals(cmc.getProductId())).findFirst().orElse(null);
                    if (exist == null) {
                        result.add(cmc);
                    }

                    if (exist != null && cmc.getUpdateTime().compareTo(exist.getUpdateTime()) > 0) {
                        result.remove(exist);
                        result.add(cmc);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 查询生效的产品控制信息
     */
    @Override
    public List<CrmMaterialControl> selectEffects(CrmMaterialControl crmMaterialControl) {
        List<CrmMaterialControl> cmcs = crmMaterialControlMapper.selectEffects(crmMaterialControl);
        return cmcs;
    }

    /**
     * 查询是否存在有效期内生效的产品控制代码
     */
    @Override
    public List<CrmMaterialControl> findExistControlProducts(CrmMaterialControl crmMaterialControl) {
        return crmMaterialControlMapper.findExistControlProducts(crmMaterialControl);
    }
}
