package com.topsec.crm.product.core.controllerhidden;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.api.RemoteMaterialApplyMainService;
import com.topsec.crm.flow.api.dto.materialApply.ExistDisableProductVo;
import com.topsec.crm.flow.api.dto.materialApply.MaterialApplyMainVo;
import com.topsec.crm.flow.api.vo.CrmMaterialDisableVo;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.product.api.dto.CrmProductLineQuery;
import com.topsec.crm.product.api.entity.*;
import com.topsec.crm.product.core.entity.CrmProduct;
import com.topsec.crm.product.core.entity.CrmProductCategory;
import com.topsec.crm.product.core.entity.CrmProductLine;
import com.topsec.crm.product.core.service.*;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品Controller
 *
 * @date 2024-04-26
 */
@RestController
@RequestMapping("/hidden/product")
@Tag(name = "【产品】", description = "hiddenCrmProductController")
public class HiddenCrmProductController extends BaseController {
    @Autowired
    private ICrmProductService crmProductService;

    @Resource
    private CrmProductRowTypeService crmProductRowTypeService;

    @Autowired
    private ICrmQuotationPhaseService crmQuotationPhaseService;

    @Resource
    private CrmProductSoftHardwareIdentificationService crmProductSoftHardwareIdentificationService;

    @Autowired
    private CrmProductLineService crmProductLineService;

    @Autowired
    private RemoteMaterialApplyMainService remoteMaterialApplyMainService;

    @Autowired
    private ICrmProductCategoryService crmProductCategoryService;

    /**
     * 批量获取【产品】详细信息
     */
    @PostMapping(value = "/batchGetInfo")
    @Operation(summary = "批量获取【产品】详细信息")
    public JsonObject<List<CrmProductVo>> batchGetInfo(@RequestBody List<String> ids) {
        List<CrmProduct> crmProducts = crmProductService.selectCrmProductByIds(ids);
        return new JsonObject(HyperBeanUtils.copyListPropertiesByJackson(crmProducts, CrmProductVo.class));
    }

    /**
     * 根据物料代码批量获取【产品】详细信息
     */
    @PostMapping("/batchGetInfoByMaterialCode")
    @Operation(summary = "根据物料批量获取【产品】详细信息")
    JsonObject<List<CrmProductVo>> batchGetInfoByMaterialCode(@RequestBody List<String> materialCodes){
        List<CrmProduct> crmProducts = crmProductService.batchGetInfoByMaterialCode(materialCodes);
        return new JsonObject(HyperBeanUtils.copyListPropertiesByJackson(crmProducts, CrmProductVo.class));
    }

    @GetMapping("/checkProductLackOfCost")
    @Operation(summary = "产品是否缺成本")
    public JsonObject<Boolean> checkProductLackOfCost(@RequestParam String productId)
    {
        return new JsonObject(crmProductService.checkProductLackOfCost(productId));
    }

    @PostMapping(value = "/selectProductsRowType")
    @Operation(summary = "查询产品行类型")
    public JsonObject<List<CrmProductRowTypeVO>> selectProductsRowType(@RequestBody List<CrmProductSoftHardwareIdentificationVO> CrmProductSoftHardwareIdentificationVOList)
    {
        return new JsonObject(crmProductRowTypeService.selectProductsRowType(CrmProductSoftHardwareIdentificationVOList));
    }


    @PostMapping(value = "/checkProductsRowTypeLack")
    @Operation(summary = "检查产品是否缺少行类型")
    public JsonObject<List<CrmProductRowTypeVO>> checkProductsRowTypeLack(@RequestBody List<CrmProductSoftHardwareIdentificationVO> CrmProductSoftHardwareIdentificationVOList)
    {
        return new JsonObject(crmProductRowTypeService.checkProductsRowTypeLack(CrmProductSoftHardwareIdentificationVOList));
    }


    @PostMapping("/checkProductsCurrentQuotation")
    @Operation(summary = "检查产品是否有当期报价")
    public JsonObject<List<Map<String, Boolean>>> checkProductsCurrentQuotation(@RequestBody List<String> productIds)
    {
        return new JsonObject(crmQuotationPhaseService.checkProductsCurrentQuotation(productIds));
    }

    @PostMapping(value = "/getProductSoftHardwareIdentifications")
    @Operation(summary = "获取产品软硬件标识名称")
    public JsonObject<List<CrmProductSoftHardwareIdentificationVO>> getProductSoftHardwareIdentifications(@RequestBody List<CrmProductSoftHardwareIdentificationVO> crmProductSoftHardwareIdentificationVOList)
    {
        boolean result = crmProductSoftHardwareIdentificationVOList.stream().anyMatch(item -> {
            return Objects.isNull(item.getProductId()) || Objects.isNull(item.getContractSignCompanyId());
        });
        CrmAssert.isTrue(!result,"签订公司id和产品id不能为空");
        return new JsonObject(crmProductSoftHardwareIdentificationService.getProductSoftHardwareIdentifications(crmProductSoftHardwareIdentificationVOList));
    }


    @GetMapping("/getProductLineById/{id}")
    @Operation(summary = "根据id获取产品线分类", description = "产品分类")
    public JsonObject<CrmProductLineVo> selectById(@PathVariable String id) {
        CrmAssert.notNull("id不能为空",id);
        CrmProductLine crmProductLine = crmProductLineService.selectById(id);
        CrmProductLineVo crmProductLineVo = HyperBeanUtils.copyPropertiesByJackson(crmProductLine, CrmProductLineVo.class);
        return new JsonObject<>(crmProductLineVo);
    }
    @PostMapping("/getProductLineByIds")
    @Operation(summary = "根据批量id获取产品线分类", description = "产品分类")
    public JsonObject<List<CrmProductLineVo>> selectByIds(@RequestBody List<String> ids) {
        List<CrmProductLine> crmProductLines = crmProductLineService.selectByIds(ids);
        List<CrmProductLineVo> crmProductLineVos = HyperBeanUtils.copyListPropertiesByJackson(crmProductLines, CrmProductLineVo.class);
        return new JsonObject<>(crmProductLineVos);
    }

    @PostMapping("/selectCrmProductListByProductLineQuery")
    @Operation(summary = "根据产品线分类获取产品信息详情")
    public JsonObject<List<CrmProductVo>> selectCrmProductListByProductLineQuery(@RequestBody CrmProductLineQuery crmProductLineQuery)
    {
        List<CrmProduct> crmProducts = crmProductService.selectCrmProductListByProductLineQuery(crmProductLineQuery);
        return new JsonObject(HyperBeanUtils.copyListPropertiesByJackson(crmProducts, CrmProductVo.class));
    }

    /**
     * 根据产品ID查询是否有子服务或者子配件
     */
    @GetMapping("/findHasChild")
    @Operation(summary = "根据产品ID查询是否有子服务或者子配件")
    public JsonObject<Boolean> findHasChild(@RequestParam String productId)
    {
        return new JsonObject<>(crmProductService.findHasChild(productId));
    }

    /**
     * 根据产品IDs 批量查询是否有子服务或者子配件
     */
    @PostMapping("/batchFindHasChild")
    @Operation(summary = "根据产品IDs 批量查询是否有子服务或者子配件")
    public JsonObject<Map<String,Boolean>> batchFindHasChild(@RequestBody List<String> productIds)
    {
        return new JsonObject<>(crmProductService.batchFindHasChild(productIds));
    }

    /**
     * 根据产品IDs 批量查询是否含有工控产品（产品分类为工业互联网安全）
     */
    @PostMapping("/batchFindHasGKCP")
    @Operation(summary = "根据产品IDs 批量查询是否含有工控产品（产品分类为工业互联网安全）")
    public JsonObject<Boolean> batchFindHasGKCP(@RequestBody List<String> productIds)
    {
        return new JsonObject<>(crmProductService.batchFindHasGKCP(productIds));
    }

    /**
     * 根据设备序列号列表，返回不在系统中的设备序列号列表
     */
    @PostMapping("/getNotExistSns")
    @Operation(summary = "根据设备序列号列表，返回不在系统中的设备序列号列表")
    public JsonObject<List<String>> getNotExistSns(@RequestBody List<String> sns)
    {
        return new JsonObject<>(crmProductService.getNotExistSns(sns));
    }

    /**
     * 自有产品选择页面接口
     */
    @PostMapping("/crmProductForSelect")
    @Operation(summary = "自有产品选择页面接口")
    public JsonObject<PageUtils<CrmProductVo>> crmProductForSelect(@RequestParam String currentPersonId,@RequestBody CrmProductVo crmProductVo)
    {
        CrmProduct crmProduct = HyperBeanUtils.copyProperties(crmProductVo, CrmProduct::new);

        if(crmProductVo.getShowControlFlag() == null || crmProductVo.getShowControlFlag() == true) {
            //查询出产品生效的控制物料代码，这些代码不能够显示(剔除掉控制范围之外，以及审批流程生效的)
            MaterialApplyMainVo materialApplyMainVo = new MaterialApplyMainVo();
            materialApplyMainVo.setSourceType(crmProductVo.getSourceType());
            materialApplyMainVo.setProOrAgreementId(crmProductVo.getProjectId());
            materialApplyMainVo.setPersonId(StringUtils.isNotBlank(currentPersonId) ? currentPersonId : UserInfoHolder.getCurrentPersonId());
            materialApplyMainVo.setIndustryOne(crmProductVo.getIndustryOne());
            materialApplyMainVo.setIndustryTwo(crmProductVo.getIndustryTwo());
            JsonObject<ExistDisableProductVo> existObject = remoteMaterialApplyMainService.selectProductNoControl(materialApplyMainVo);
            if (existObject.isSuccess() && existObject.getObjEntity() != null) {
                ExistDisableProductVo objEntity = existObject.getObjEntity();
                if (CollectionUtil.isNotEmpty(objEntity.getControl())) {
                    crmProduct.setEffectsIds(objEntity.getControl().stream().filter(e -> e != null).map(CrmMaterialDisableVo::getProductId).toList());
                }
            }
        }
        startPage();
        List<CrmProduct> list = crmProductService.crmProductForSelect(crmProduct);
        List<CrmProductVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, CrmProductVo.class);
        //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
        PageUtils dataTable = getDataTable(list,listVo);

        setGuaranteePeriod(dataTable.getList());
        return new JsonObject<>(dataTable);
    }

    /**
     * 如果产品未设置保修期，则取产品分类保修期
     * @param voList
     */
    private void setGuaranteePeriod(List<CrmProductVo> voList) {
        List<String> categoryIds = voList.stream().filter(vo -> vo.getGuaranteePeriod() == null || vo.getGuaranteePeriod() == 0).map(vo -> vo.getCategoryId4()).collect(Collectors.toList());
        List<CrmProductCategory> categoryList = crmProductCategoryService.list(new QueryWrapper<CrmProductCategory>().in(CollectionUtils.isNotEmpty(categoryIds), "id", categoryIds));
        Map<String, CrmProductCategory> categoryMap = categoryList.stream().collect(Collectors.toMap(CrmProductCategory::getId, crmProductCategory -> crmProductCategory));
        voList.stream().forEach(vo -> {
            if (vo.getGuaranteePeriod() == null || vo.getGuaranteePeriod() == 0) {
                CrmProductCategory category = categoryMap.get(vo.getCategoryId4());
                if (category != null) {
                    vo.setGuaranteePeriod(category.getGuaranteePeriod());
                }
            }
        });
    }

    /**
     * 产品配件选择接口
     * 通过forms控制，3-配件、5-授权
     */
    @PostMapping("/moduleForSelect")
    @Operation(summary = "产品配件选择接口")
    public JsonObject<PageUtils<CrmProductVo>> moduleForSelect(@RequestBody CrmProductVo crmProductVo)
    {
        CrmProduct crmProduct = HyperBeanUtils.copyProperties(crmProductVo, CrmProduct::new);

        startPage();
        List<CrmProduct> list = crmProductService.moduleForSelect(crmProduct);
        List<CrmProductVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, CrmProductVo.class);
        //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
        PageUtils dataTable = getDataTable(list,listVo);
        setGuaranteePeriod(dataTable.getList());
        return new JsonObject<>(dataTable);
    }

    /**
     * 获取【产品】详细信息
     */
    @GetMapping(value = "/getInfoByMaterialCode")
    @Operation(summary = "获取【产品】详细信息")
    public JsonObject<CrmProductVo> getInfoByMaterialCode(@RequestParam String materialCode)
    {
        CrmProduct crmProduct = crmProductService.getInfoByMaterialCode(materialCode);
        return new JsonObject(HyperBeanUtils.copyPropertiesByJackson(crmProduct, CrmProductVo.class));
    }

    @PostMapping("/groupProductIds")
    @Operation(summary = "对【产品】分组")
    public JsonObject<CrmProductGroupVO> groupProductIds(@RequestBody List<String> productIds) {
        CrmAssert.notEmpty(productIds, "productIds不能为空");
        List<CrmProductCategoryVo> crmProductCategoryVos = crmProductCategoryService.listAll();
        Map<String, String> categoryNameMap = crmProductCategoryVos.stream().collect(Collectors.toMap(CrmProductCategoryVo::getId, CrmProductCategoryVo::getName));

        CrmProductGroupVO crmProductGroupVO = new CrmProductGroupVO();
        List<CrmProduct> products = crmProductService.selectCrmProductByIds(productIds);
        // FIXME 待完善魔法值
        products.stream().forEach(crmProduct -> {
            String categoryName2 = StringUtils.isNotEmpty(crmProduct.getCategoryId2()) ? categoryNameMap.get(crmProduct.getCategoryId2()) : "";
            if (4 == crmProduct.getForm() || crmProduct.getMaterialCode().startsWith("6")) {
                // 自有产品-服务产品
                crmProductGroupVO.getExceptDistributeServiceProducts().add(crmProduct.getId());
            } else if ("云计算".equals(categoryName2)) {
                // 自有产品-云计算
                crmProductGroupVO.getExceptDistributeCloudProducts().add(crmProduct.getId());
            } else if ("智慧无线".equals(categoryName2)) {
                // 自有产品-智慧无线
                crmProductGroupVO.getDistributeWirelessProducts().add(crmProduct.getId());
            } else {
                // 非分销
                if (1 == crmProduct.getAttr()) {
                    // 自有产品-非分销-安全产品
                    crmProductGroupVO.getExceptDistributeSafetyProducts().add(crmProduct.getId());
                } else {
                    // 自有产品-分销-安全产品
                    crmProductGroupVO.getDistributeSafetyProducts().add(crmProduct.getId());
                }
            }
        });
        return new JsonObject<>(crmProductGroupVO);
    }

    @PostMapping("/refreshProductComponentRel")
    @Operation(summary="刷新产品与配件的关系")
    JsonObject<Boolean> refreshProductComponentRel(){
        return new JsonObject<>(crmProductService.refreshProductComponentRel());
    }
}
