<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.product.core.mapper.CrmModelMachineProductSnapshotMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.product.core.entity.CrmModelMachineProductSnapshot">
            <result property="id" column="id" jdbcType="VARCHAR"/>
            <result property="contractNumber" column="contract_number" jdbcType="VARCHAR"/>
            <result property="contractTime" column="contract_time" jdbcType="TIMESTAMP"/>
            <result property="inContractCompanyName" column="in_contract_company_name" jdbcType="VARCHAR"/>
            <result property="inContractCompanyId" column="in_contract_company_id" jdbcType="VARCHAR"/>
            <result property="stuffCode" column="stuff_code" jdbcType="VARCHAR"/>
            <result property="pnCode" column="pn_code" jdbcType="VARCHAR"/>
            <result property="productCategory" column="product_category" jdbcType="VARCHAR"/>
            <result property="inSerialNumber" column="in_serial_number" jdbcType="VARCHAR"/>
            <result property="inQuotedPrice" column="in_quoted_price" jdbcType="DECIMAL"/>
            <result property="acceptanceTime" column="acceptance_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,contract_number,contract_time,
        in_contract_company_name,in_contract_company_id,stuff_code,
        pn_code,product_category,in_serial_number,
        in_quoted_price,acceptance_time
    </sql>
    <!-- * 状态说明： -->
    <!-- * 【借试用中】该样机不满足销售条件 且 在借试用中，不可借 -->
    <!-- * 【在库】该样机不满足销售条件，可借 -->
    <!-- * 【已销售】该样机作为正常设备销售出去了，仅留存统计数据，不可借 -->
    <!-- * 【可销售】有效归还次数大于等于10，且备货大于等于一年，不可借 -->
    <!-- * 1-借试用中 ，2- 在库（可借） ，3-已销售，4-可销售 ,优先级3142-->
    <insert id="batchInsert">
        insert into crm_model_machine_product_snapshot(id, contract_number, contract_time, in_contract_company_name,
                                                       in_contract_company_id, stuff_code, pn_code, product_category,
                                                       in_quoted_price, in_serial_number, out_serial_number,
                                                       acceptance_time,state,borrow_count,return_count)
        select id,
               contract_number,
               contract_time,
               in_contract_company_name,
               in_contract_company_id,
               stuff_code,
               pn_code,
               product_category,
               in_quoted_price,
               in_serial_number,
               out_serial_number,
               acceptance_time,
               case
                          when out_serial_number is not null then 3
                          when borrow_count > return_count then 1
                          when return_count >= 10 and
                               TIMESTAMPDIFF(YEAR, date(acceptance_time), CURDATE()) >= 1 then 4
                          else 2 end as state,
               ifnull(borrow_count,0),
               ifnull(return_count,0)
        from crm_product_agent_inventory

        left join (
            select sn,
                   COUNT(CASE WHEN type = 1 THEN 1 END) AS borrow_count,
                   COUNT(CASE WHEN type = 2 THEN 1 END) AS return_count
            from crm_product_sample_br group by sn
        ) as br on crm_product_agent_inventory.in_serial_number = br.sn

        where model_machine = 1
          and in_serial_number is not null
        and crm_product_agent_inventory.del_flag = 0
    </insert>

    <delete id="truncModelMachine">
        truncate TABLE crm_model_machine_product_snapshot
    </delete>
    <select id="getModelMachines" resultType="com.topsec.crm.product.api.entity.CrmModelMachineProductSnapshotVO">
        select
        id,
        contract_number,
        contract_time,
        in_contract_company_name,
        in_contract_company_id,
        stuff_code,
        pn_code,
        product_category,
        in_quoted_price,
        in_serial_number,
        out_serial_number,
        acceptance_time,
        borrow_count as effectiveBorrowCount,
        name as productName
        from (select p.*,pro.name
        from crm_model_machine_product_snapshot p
        LEFT JOIN crm_product as pro on pro.material_code = p.stuff_code) model
        where state = 2
        <if test="query.agentIds!=null and query.agentIds.size>0 ">
            and in_contract_company_id in
            <foreach collection="query.agentIds" item="agentIds" open="(" separator="," close=")">
                #{agentIds}
            </foreach>
        </if>
        <if test="query.contractNumber!=null and query.contractNumber!=''">
            and contract_number like concat('%',#{query.contractNumber},'%')
        </if>
        <if test="query.stuffCode!=null and query.stuffCode!=''">
            and stuff_code like concat('%',#{query.stuffCode},'%')
        </if>
        <if test="query.inContractCompanyName!=null and query.inContractCompanyName!=''">
            and in_contract_company_name like concat('%',#{query.inContractCompanyName},'%')
        </if>
        <if test="query.currentAgentId!=null and query.currentAgentId!=''">
            and in_contract_company_id =#{query.currentAgentId}
        </if>
    </select>

    <select id="filterCanBorrowSn" resultType="java.lang.String">
        select distinct sn from crm_model_machine_product_snapshot where state=2
        and in_serial_number in
        <foreach collection="snList" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>
    </select>
    <select id="querySnStatus" resultType="com.topsec.crm.product.core.entity.CrmModelMachineProductSnapshot">
        select in_serial_number ,
               state,
               borrow_count,
               return_count
        from crm_model_machine_product_snapshot
        where
         in_serial_number in
        <foreach collection="snList" item="sn" open="(" separator="," close=")">
            #{sn}
        </foreach>

    </select>

    <select id="pageSampleBorrowReturnDetail"
            resultType="com.topsec.crm.product.api.entity.CrmSampleBorrowReturnVO">

        select
        in_serial_number as sn,
        in_contract_company_name as agentName,
        crm_product.material_code as stuffCode,
        crm_product.name as productName,
        borrow_count as effectiveBorrowCount,
        return_count as effectiveReturnsCount,
        state as status

        from crm_model_machine_product_snapshot

        left join crm_product.crm_product on crm_model_machine_product_snapshot.stuff_code = crm_product.material_code

        <where>
            <if test="query.productName!=null and query.productName!=''">
                and crm_product.name like concat('%',#{query.productName},'%')
            </if>
            <if test="query.sn!=null and query.sn!='' ">
                and in_serial_number like concat('%',#{query.sn},'%')
            </if>
            <if test="query.stuffCode!=null and query.stuffCode!=''">
                and stuff_code like concat('%',#{query.stuffCode},'%')
            </if>
            <if test="query.dataScopeParam.agentId!=null and query.dataScopeParam.agentId!=''">
                and in_contract_company_id =#{query.dataScopeParam.agentId}
            </if>
            <if test="query.agentIds!=null and query.agentIds.size>0">
                and in_contract_company_id in
                <foreach collection="query.agentIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.status!=null">
                and state=#{query.status}
            </if>
        </where>
    </select>
</mapper>
