<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.product.core.mapper.CrmCodeProportionMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.product.core.entity.CrmCodeProportion">
            <id property="id" column="id" />
            <result property="materialCode" column="material_code" />
            <result property="pn" column="pn" />
            <result property="specification" column="specification" />
            <result property="scopeApplication" column="scope_application" />
            <result property="proportion" column="proportion" />
            <result property="remark" column="remark" />
            <result property="delFlag" column="del_flag" />
            <result property="createUser" column="create_user" />
            <result property="updateUser" column="update_user" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,material_code,pn,specification,scope_application,proportion,remark,del_flag,create_user,
        update_user,create_time,update_time
    </sql>
</mapper>
