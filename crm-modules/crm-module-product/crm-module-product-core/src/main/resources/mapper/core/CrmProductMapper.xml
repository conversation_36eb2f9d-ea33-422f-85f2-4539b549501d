<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.product.core.mapper.CrmProductMapper">

    <resultMap type="com.topsec.crm.product.core.entity.CrmProduct" id="CrmProductResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="pn"    column="pn"    />
        <result property="materialCode"    column="material_code"    />
        <result property="quality"    column="quality"    />
        <result property="attr"    column="attr"    />
        <result property="form"    column="form"    />
        <result property="productLine1"    column="product_line1"    />
        <result property="productLine2"    column="product_line2"    />
        <result property="productLine3"    column="product_line3"    />
        <result property="categoryId1"    column="category_id1"    />
        <result property="categoryId2"    column="category_id2"    />
        <result property="categoryId3"    column="category_id3"    />
        <result property="categoryId4"    column="category_id4"    />
        <result property="tqmCategory1"    column="tqm_category1"    />
        <result property="tqmCategory2"    column="tqm_category2"    />
        <result property="businessCategoryId"    column="business_category_id"    />
        <result property="outSource"    column="out_source"    />
        <result property="distributionId3"    column="distribution_id3"    />
        <result property="specificationId"    column="specification_id"    />
        <result property="seriesId"    column="series_id"    />
        <result property="guaranteePeriod"    column="guarantee_period"    />
        <result property="productionType"    column="production_type"    />
        <result property="unit"    column="unit"    />
        <result property="quotationPhaseId"    column="quotation_phase_id"    />
        <result property="price"    column="price"    />
        <result property="sellinPrice"    column="sellin_price"    />
        <result property="status"    column="status"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="profile"    column="profile"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="licFlag"    column="lic_flag"    />
        <result property="rebate"    column="rebate"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCrmProductVo">
        select id, name, pn, material_code, quality, attr, form, product_line1, product_line2, product_line3, category_id1, category_id2, category_id3,
               category_id4, tqm_category1, tqm_category2, business_category_id, out_source, distribution_id3, specification_id, series_id, guarantee_period, production_type, unit, quotation_phase_id,
               price, sellin_price, status, image_url, profile, remark, del_flag, lic_flag, rebate, create_user, update_user, create_time, update_time
        from crm_product
    </sql>

    <select id="selectCrmProductList" parameterType="com.topsec.crm.product.core.entity.CrmProduct" resultMap="CrmProductResult">
        <include refid="selectCrmProductVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="pn != null  and pn != ''"> and pn = #{pn}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="quality != null "> and quality = #{quality}</if>
            <if test="attr != null "> and attr = #{attr}</if>
            <if test="form != null "> and form = #{form}</if>
            <if test="productLine1 != null  and productLine1 != ''"> and product_line1 = #{productLine1}</if>
            <if test="productLine2 != null  and productLine2 != ''"> and product_line2 = #{productLine2}</if>
            <if test="productLine3 != null  and productLine3 != ''"> and product_line3 = #{productLine3}</if>
            <if test="categoryId1 != null  and categoryId1 != ''"> and category_id1 = #{categoryId1}</if>
            <if test="categoryId2 != null  and categoryId2 != ''"> and category_id2 = #{categoryId2}</if>
            <if test="categoryId3 != null  and categoryId3 != ''"> and category_id3 = #{categoryId3}</if>
            <if test="categoryId4 != null  and categoryId4 != ''"> and category_id4 = #{categoryId4}</if>
            <if test="tqmCategory1 != null  and tqmCategory1 != ''"> and tqm_category1 = #{tqmCategory1}</if>
            <if test="tqmCategory2 != null  and tqmCategory2 != ''"> and tqm_category2 = #{tqmCategory2}</if>
            <if test="businessCategoryId != null  and businessCategoryId != ''"> and business_category_id = #{businessCategoryId}</if>
            <if test="outSource != null  and outSource != ''"> and out_source = #{outSource}</if>
            <if test="specificationId != null  and specificationId != ''"> and specification_id = #{specificationId}</if>
            <if test="seriesId != null  and seriesId != ''"> and series_id = #{seriesId}</if>
            <if test="guaranteePeriod != null "> and guarantee_period = #{guaranteePeriod}</if>
            <if test="productionType != null  and productionType != ''"> and production_type = #{productionType}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="quotationPhaseId != null  and quotationPhaseId != ''"> and quotation_phase_id = #{quotationPhaseId}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="sellinPrice != null "> and sellin_price = #{sellinPrice}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="profile != null  and profile != ''"> and profile = #{profile}</if>
            <if test="createUser != null  and createUser != ''"> and create_user = #{createUser}</if>
            <if test="updateUser != null  and updateUser != ''"> and update_user = #{updateUser}</if>
        </where>
    </select>

    <select id="selectCrmProductById" parameterType="String" resultMap="CrmProductResult">
        <include refid="selectCrmProductVo"/>
        where id = #{id}
    </select>
    <select id="selectCrmProductByIds" parameterType="String" resultMap="CrmProductResult">
        <include refid="selectCrmProductVo"/>
        where id in
        <foreach item="productId" collection="list" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>

    <select id="selectCrmProductByMaterialCodes" resultMap="CrmProductResult">
        <include refid="selectCrmProductVo"/>
        where material_code in
        <foreach item="materialCode" collection="list" open="(" separator="," close=")">
            #{materialCode}
        </foreach>
    </select>

    <select id="crmProductForSelect" parameterType="com.topsec.crm.product.core.entity.CrmProduct" resultMap="CrmProductResult">
        select id, name, pn, material_code, quality, attr, form, product_line1, product_line2, product_line3, category_id1, category_id2, category_id3,
               category_id4, tqm_category1, tqm_category2, business_category_id, out_source, specification_id, series_id, guarantee_period, production_type, unit,
               quotation_phase_id, status, image_url, profile, remark, del_flag, lic_flag, rebate, create_user, update_user,
               create_time, update_time,price
        from crm_product
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="pn != null  and pn != ''"> and pn = #{pn}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="quality != null "> and quality = #{quality}</if>
            <if test="attr != null "> and attr = #{attr}</if>
            <if test="form != null "> and form = #{form}</if>
            <if test="productLine1 != null  and productLine1 != ''"> and product_line1 = #{productLine1}</if>
            <if test="productLine2 != null  and productLine2 != ''"> and product_line2 = #{productLine2}</if>
            <if test="productLine3 != null  and productLine3 != ''"> and product_line3 = #{productLine3}</if>
            <if test="tqmCategory1 != null  and tqmCategory1 != ''"> and tqm_category1 = #{tqmCategory1}</if>
            <if test="tqmCategory2 != null  and tqmCategory2 != ''"> and tqm_category2 = #{tqmCategory2}</if>
            <if test="businessCategoryId != null  and businessCategoryId != ''"> and business_category_id = #{businessCategoryId}</if>
            <if test="outSource != null  and outSource != ''"> and out_source = #{outSource}</if>
            <if test="specificationId != null  and specificationId != ''"> and specification_id = #{specificationId}</if>
            <if test="seriesId != null  and seriesId != ''"> and series_id = #{seriesId}</if>
            <if test="guaranteePeriod != null "> and guarantee_period = #{guaranteePeriod}</if>
            <if test="productionType != null  and productionType != ''"> and production_type = #{productionType}</if>
            <if test="quotationPhaseId != null  and quotationPhaseId != ''"> and quotation_phase_id = #{quotationPhaseId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="profile != null  and profile != ''"> and profile = #{profile}</if>
            <if test="keywords != null  and keywords != ''"> and (name like concat('%', #{keywords}, '%') or pn like concat('%', #{keywords}, '%'))</if>
            <if test="categoryId3s != null  and categoryId3s != ''"> and category_id3 in
                <foreach item="categoryId3" collection="categoryId3s" open="(" separator="," close=")">
                    #{categoryId3}
                </foreach>
            </if>
            <if test="forms != null  and forms != ''"> and form in
                <foreach item="form" collection="forms" open="(" separator="," close=")">
                    #{form}
                </foreach>
            </if>
            <if test="idList != null "> and id in
                <foreach item="id" collection="idList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="effectsIds != null  and effectsIds != ''"> and id not in
                <foreach item="effectsId" collection="effectsIds" open="(" separator="," close=")">
                    #{effectsId}
                </foreach>
            </if>
            <if test="onlyFF != null and onlyFF == true">
                and ( material_code like "4%" or material_code like "5%")
            </if>
            <if test="onlySelectWholeFlag != null and onlySelectWholeFlag == true">
                and material_code not in (
                    select DISTINCT(separation_stuff_code) from crm_product_separation_rel
                    where is_separation=1 and del_flag=0
                )
            </if>
            <if test="xsxySelectFlag != null and xsxySelectFlag == true">
                and material_code not like "5998%"
            </if>
        </where>
        <if test="agent == true and orMaterialCodeList != null and orMaterialCodeList.size() > 0">
            UNION
            select id, name, pn, material_code, quality, attr, form, product_line1, product_line2, product_line3, category_id1, category_id2, category_id3,
            category_id4, tqm_category1, tqm_category2, business_category_id, out_source, specification_id, series_id, guarantee_period, production_type, unit,
            quotation_phase_id, status, image_url, profile, remark, del_flag, lic_flag, rebate, create_user, update_user,
            create_time, update_time,price from crm_product
            where material_code in
            <foreach item="materialCode" collection="orMaterialCodeList" open="(" separator="," close=")">
                #{materialCode}
            </foreach>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="pn != null  and pn != ''"> and pn = #{pn}</if>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="quality != null "> and quality = #{quality}</if>
            <!--<if test="attr != null "> and attr = #{attr}</if>-->
            <if test="form != null "> and form = #{form}</if>
            <if test="productLine1 != null  and productLine1 != ''"> and product_line1 = #{productLine1}</if>
            <if test="productLine2 != null  and productLine2 != ''"> and product_line2 = #{productLine2}</if>
            <if test="productLine3 != null  and productLine3 != ''"> and product_line3 = #{productLine3}</if>
            <if test="tqmCategory1 != null  and tqmCategory1 != ''"> and tqm_category1 = #{tqmCategory1}</if>
            <if test="tqmCategory2 != null  and tqmCategory2 != ''"> and tqm_category2 = #{tqmCategory2}</if>
            <if test="businessCategoryId != null  and businessCategoryId != ''"> and business_category_id = #{businessCategoryId}</if>
            <if test="outSource != null  and outSource != ''"> and out_source = #{outSource}</if>
            <if test="specificationId != null  and specificationId != ''"> and specification_id = #{specificationId}</if>
            <if test="seriesId != null  and seriesId != ''"> and series_id = #{seriesId}</if>
            <if test="guaranteePeriod != null "> and guarantee_period = #{guaranteePeriod}</if>
            <if test="productionType != null  and productionType != ''"> and production_type = #{productionType}</if>
            <if test="quotationPhaseId != null  and quotationPhaseId != ''"> and quotation_phase_id = #{quotationPhaseId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="profile != null  and profile != ''"> and profile = #{profile}</if>
            <if test="keywords != null  and keywords != ''"> and (name like concat('%', #{keywords}, '%') or pn like concat('%', #{keywords}, '%'))</if>
            <if test="categoryId3s != null  and categoryId3s != ''"> and category_id3 in
                <foreach item="categoryId3" collection="categoryId3s" open="(" separator="," close=")">
                    #{categoryId3}
                </foreach>
            </if>
            <if test="forms != null  and forms != ''"> and form in
                <foreach item="form" collection="forms" open="(" separator="," close=")">
                    #{form}
                </foreach>
            </if>
            <if test="idList != null "> and id in
                <foreach item="id" collection="idList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="effectsIds != null  and effectsIds != ''"> and id not in
                <foreach item="effectsId" collection="effectsIds" open="(" separator="," close=")">
                    #{effectsId}
                </foreach>
            </if>
            <if test="onlyFF != null and onlyFF == true">
                and ( material_code like "4%" or material_code like "5%")
            </if>
            <if test="onlySelectWholeFlag != null and onlySelectWholeFlag == true">
                and material_code not in (
                    select DISTINCT(separation_stuff_code) from crm_product_separation_rel
                )
            </if>
            <if test="xsxySelectFlag != null and xsxySelectFlag == true">
                and material_code not like "5998%"
            </if>
        </if>
    </select>
    <select id="moduleForSelect" parameterType="com.topsec.crm.product.core.entity.CrmProduct" resultMap="CrmProductResult">
        select id, name, pn, material_code, quality, attr, form, product_line1, product_line2, product_line3, category_id1, category_id2, category_id3,
                category_id4, tqm_category1, tqm_category2, business_category_id, out_source, specification_id, series_id, guarantee_period, production_type, unit,
                quotation_phase_id, status, image_url, profile, remark, del_flag, lic_flag, rebate, create_user, update_user,
                create_time, update_time,price
        <if test="rels != null and rels.size() > 0">
            , CASE WHEN id in
            <foreach item="rel" collection="rels" open="(" separator="," close=")">
                #{rel}
            </foreach>
            THEN 1 ELSE 0 END as relTag
        </if>
        from crm_product
        <!-- 取消对应关系 -->
        <!-- where id in (
            select module_product_id from crm_product_module_rel where product_id = #{id}
        ) -->
        <where>
            <if test="materialCode != null  and materialCode != ''"> and material_code = #{materialCode}</if>
            <if test="keywords != null  and keywords != ''"> and (name like concat('%', #{keywords}, '%') or pn like concat('%', #{keywords}, '%'))</if>
            <if test="forms != null  and forms != ''"> and form in

            </if>
        </where>
        <if test="rels != null and rels.size() > 0">
            order by relTag desc
        </if>
    </select>
    <select id="moduleForSelectCount" resultType="java.lang.Integer">
        select count(1)
        from crm_product
        where id in (
            select module_product_id from crm_product_module_rel where product_id = #{id}
        )
    </select>
    <select id="getExistSns" resultType="String">
        select sn from crm_device
        where sn in
        <foreach item="sn" collection="list" open="(" separator="," close=")">
            #{sn}
        </foreach>
    </select>
    <select id="getNotStartForSns" resultType="String">
        select sn from crm_device
        where product_id in
        <foreach item="productId" collection="list" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>
    <select id="batchFindHadGKCP" resultType="com.topsec.crm.product.core.entity.CrmProduct">
        select * from crm_product
        where ( category_id1 in (select id from crm_product_category where name = "工业互联网安全")
             or category_id2 in (select id from crm_product_category where name = "工业互联网安全")
             or category_id3 in (select id from crm_product_category where name = "工业互联网安全")
             or category_id4 in (select id from crm_product_category where name = "工业互联网安全")
          ) and id in
          <foreach item="productId" collection="list" open="(" separator="," close=")">
            #{productId}
          </foreach>
    </select>

    <insert id="insertCrmProduct" parameterType="com.topsec.crm.product.core.entity.CrmProduct">
        insert into crm_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="pn != null and pn != ''">pn,</if>
            <if test="materialCode != null and materialCode != ''">material_code,</if>
            <if test="quality != null">quality,</if>
            <if test="attr != null">attr,</if>
            <if test="form != null">form,</if>
            <if test="productLine1 != null">product_line1,</if>
            <if test="productLine2 != null">product_line2,</if>
            <if test="productLine3 != null">product_line3,</if>
            <if test="categoryId1 != null">category_id1,</if>
            <if test="categoryId2 != null">category_id2,</if>
            <if test="categoryId3 != null">category_id3,</if>
            <if test="categoryId4 != null">category_id4,</if>
            <if test="tqmCategory1 != null">tqm_category1,</if>
            <if test="tqmCategory2 != null">tqm_category2,</if>
            <if test="businessCategoryId != null">business_category_id,</if>
            <if test="outSource != null">out_source,</if>
            <if test="specificationId != null">specification_id,</if>
            <if test="seriesId != null">series_id,</if>
            <if test="guaranteePeriod != null">guarantee_period,</if>
            <if test="productionType != null">production_type,</if>
            <if test="unit != null">unit,</if>
            <if test="quotationPhaseId != null">quotation_phase_id,</if>
            <if test="price != null">price,</if>
            <if test="sellinPrice != null">sellin_price,</if>
            <if test="status != null">status,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="profile != null">profile,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="licFlag != null">lic_flag,</if>
            <if test="rebate != null">rebate,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="pn != null and pn != ''">#{pn},</if>
            <if test="materialCode != null and materialCode != ''">#{materialCode},</if>
            <if test="quality != null">#{quality},</if>
            <if test="attr != null">#{attr},</if>
            <if test="form != null">#{form},</if>
            <if test="productLine1 != null">#{productLine1},</if>
            <if test="productLine2 != null">#{productLine2},</if>
            <if test="productLine3 != null">#{productLine3},</if>
            <if test="categoryId1 != null">#{categoryId1},</if>
            <if test="categoryId2 != null">#{categoryId2},</if>
            <if test="categoryId3 != null">#{categoryId3},</if>
            <if test="categoryId4 != null">#{categoryId4},</if>
            <if test="tqmCategory1 != null">#{tqmCategory1},</if>
            <if test="tqmCategory2 != null">#{tqmCategory2},</if>
            <if test="businessCategoryId != null">#{businessCategoryId},</if>
            <if test="outSource != null">#{outSource},</if>
            <if test="specificationId != null">#{specificationId},</if>
            <if test="seriesId != null">#{seriesId},</if>
            <if test="guaranteePeriod != null">#{guaranteePeriod},</if>
            <if test="productionType != null">#{productionType},</if>
            <if test="unit != null">#{unit},</if>
            <if test="quotationPhaseId != null">#{quotationPhaseId},</if>
            <if test="price != null">#{price},</if>
            <if test="sellinPrice != null">#{sellinPrice},</if>
            <if test="status != null">#{status},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="profile != null">#{profile},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="licFlag != null">#{licFlag},</if>
            <if test="rebate != null">#{rebate},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCrmProduct" parameterType="com.topsec.crm.product.core.entity.CrmProduct">
        update crm_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="pn != null and pn != ''">pn = #{pn},</if>
            <if test="materialCode != null and materialCode != ''">material_code = #{materialCode},</if>
            <if test="quality != null">quality = #{quality},</if>
            <if test="attr != null">attr = #{attr},</if>
            <if test="form != null">form = #{form},</if>
            <if test="productLine1 != null">product_line1 = #{productLine1},</if>
            <if test="productLine2 != null">product_line2 = #{productLine2},</if>
            <if test="productLine3 != null">product_line3 = #{productLine3},</if>
            <if test="categoryId1 != null">category_id1 = #{categoryId1},</if>
            <if test="categoryId2 != null">category_id2 = #{categoryId2},</if>
            <if test="categoryId3 != null">category_id3 = #{categoryId3},</if>
            <if test="categoryId4 != null">category_id4 = #{categoryId4},</if>
            <if test="tqmCategory1 != null">tqm_category1 = #{tqmCategory1},</if>
            <if test="tqmCategory2 != null">tqm_category2 = #{tqmCategory2},</if>
            <if test="businessCategoryId != null">business_category_id = #{businessCategoryId},</if>
            <if test="outSource != null">out_source = #{outSource},</if>
            <if test="specificationId != null">specification_id = #{specificationId},</if>
            <if test="seriesId != null">series_id = #{seriesId},</if>
            <if test="guaranteePeriod != null">guarantee_period = #{guaranteePeriod},</if>
            <if test="productionType != null">production_type = #{productionType},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="quotationPhaseId != null">quotation_phase_id = #{quotationPhaseId},</if>
            <if test="price != null">price = #{price},</if>
            <if test="sellinPrice != null">sellin_price = #{sellinPrice},</if>
            <if test="status != null">status = #{status},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="profile != null">profile = #{profile},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="licFlag != null">lic_flag = #{licFlag},</if>
            <if test="rebate != null">rebate = #{rebate},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmProductById" parameterType="String">
        delete from crm_product where id = #{id}
    </delete>

    <delete id="deleteCrmProductByIds" parameterType="String">
        delete from crm_product where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="getProductLine1List" resultType="String">
        select distinct name from crm_product_line where level = 1 and del_flag = 0
    </select>

</mapper>
