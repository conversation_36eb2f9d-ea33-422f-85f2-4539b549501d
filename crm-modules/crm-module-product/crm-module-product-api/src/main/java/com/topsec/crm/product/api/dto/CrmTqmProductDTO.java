package com.topsec.crm.product.api.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 天智分类
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-05-21
 */

@Data
public class CrmTqmProductDTO {
	/**
	* id
	*/
	@TableId(value = "uuid", type = IdType.ASSIGN_UUID)
	private String uuid;

	/**
	* 物料代码
	*/
	private String stuffCode;

	/**
	* 产品类别1
	*/
	private String productClassOne;

	/**
	* 产品类别2
	*/
	private String productClassTwo;

	/**
	* 产品类别3
	*/
	private String productClassThree;

	/**
	* 产品属性
	*/
	private String productAttr;

	/**
	* 产品形态 软件、硬件、服务
	*/
	private String productForm;

	/**
	* 产品名称
	*/
	private String productName;

	/**
	* 产品型号
	*/
	private String productCategory;

	/**
	 * PN码
	 */
	private String pnCode;

	/**
	 * 产品型号
	 */
	private String productLineFirstLevel;

	/**
	 * 产品型号
	 */
	private String productLineSecondLevel;

	/**
	 * 产品型号
	 */
	private String productLineThirdLevel;

	/**
	 * 产品型号
	 */
	private String productTag;

	/**
	 * 产品型号
	 */
	private String productProperty;

}