package com.topsec.crm.project.api.entity;

import com.topsec.crm.framework.common.web.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 公司项目生产发货产品信息VO
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@Schema(description = "公司项目生产发货产品信息VO")
public class CrmProjectDirectlyDeliveryVO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 合同号
     */
    @Schema(description = "合同号")
    private String contractNo;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * 物料代码
     */
    @Schema(description = "物料代码")
    private String materialCode;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称")
    private String productName;

    /**
     * 产品PN
     */
    @Schema(description = "产品PN")
    private String productPn;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private BigDecimal quantity;

    /**
     * 预计发货数量
     */
    @Schema(description = "预计发货数量")
    private BigDecimal expectedDeliveryQuantity;

    /**
     * 预计齐料时间
     */
    @Schema(description = "预计齐料时间")
    private LocalDateTime expectedCompleteTime;

    /**
     * 发运方式
     */
    @Schema(description = "发运方式")
    private String shippingMethod;

    /**
     * 物流公司
     */
    @Schema(description = "物流公司")
    private String logisticsCompany;

    /**
     * 物流单号
     */
    @Schema(description = "物流单号")
    private String trackingNumber;

    /**
     * 自提人
     */
    @Schema(description = "自提人")
    private String picker;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 删除标识 0未删除,1已删除
     */
    @Schema(description = "删除标识 0未删除,1已删除")
    private Integer delFlag;
}
