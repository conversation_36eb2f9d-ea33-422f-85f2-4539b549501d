<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.project.core.mapper.CrmProjectDirectlyDeliveryMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.project.core.entity.CrmProjectDirectlyDelivery">
            <id property="id" column="id" />
            <result property="contractNo" column="contract_no" />
            <result property="orderNo" column="order_no" />
            <result property="materialCode" column="material_code" />
            <result property="productName" column="product_name" />
            <result property="productPn" column="product_pn" />
            <result property="quantity" column="quantity" />
            <result property="expectedDeliveryQuantity" column="expected_delivery_quantity" />
            <result property="expectedCompleteTime" column="expected_complete_time" />
            <result property="shippingMethod" column="shipping_method" />
            <result property="logisticsCompany" column="logistics_company" />
            <result property="trackingNumber" column="tracking_number" />
            <result property="picker" column="picker" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="createUser" column="create_user" />
            <result property="updateUser" column="update_user" />
            <result property="delFlag" column="del_flag" />
    </resultMap>

    <sql id="Base_Column_List">
        id,contract_no,order_no,material_code,product_name,product_pn,
        quantity,expected_delivery_quantity,expected_complete_time,shipping_method,logistics_company,
        tracking_number,picker,status,create_time,update_time,
        create_user,update_user,del_flag
    </sql>
</mapper>
