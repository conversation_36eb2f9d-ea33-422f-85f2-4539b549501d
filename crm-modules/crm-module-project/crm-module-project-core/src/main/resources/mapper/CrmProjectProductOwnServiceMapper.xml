<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.project.core.mapper.CrmProjectProductOwnServiceMapper">
    
    <resultMap type="com.topsec.crm.project.core.entity.CrmProjectProductOwnService" id="CrmProjectProductOwnServiceResult">
        <result property="id"    column="id"    />
        <result property="recordId"    column="record_id"    />
        <result property="serviceId"    column="service_id"    />
        <result property="stuffCode"    column="stuff_code"    />
        <result property="serviceName"    column="service_name"    />
        <result property="serviceDesc"    column="service_desc"    />
        <result property="serviceNum"    column="service_num"    />
        <result property="tempServiceNum"    column="temp_service_num"    />
        <result property="serviceSpecification"    column="service_specification"    />
        <result property="quotedPrice"    column="quoted_price"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="enableEdit"    column="enable_edit"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCrmProjectProductOwnServiceVo">
        select id, record_id, service_id, stuff_code, service_name,
               service_desc, service_num, temp_service_num, service_specification, quoted_price,
               remark, del_flag, enable_edit, create_user, update_user, create_time, update_time
        from crm_project_product_own_service
    </sql>

    <select id="selectCrmProjectProductOwnServiceList" parameterType="com.topsec.crm.project.core.entity.CrmProjectProductOwnService" resultMap="CrmProjectProductOwnServiceResult">
        <include refid="selectCrmProjectProductOwnServiceVo"/>
        <where>  
            <if test="recordId != null "> and record_id = #{recordId}</if>
            <if test="serviceId != null  and serviceId != ''"> and service_id = #{serviceId}</if>
            <if test="stuffCode != null  and stuffCode != ''"> and stuff_code = #{stuffCode}</if>
            <if test="serviceName != null  and serviceName != ''"> and service_name like concat('%', #{serviceName}, '%')</if>
            <if test="serviceDesc != null  and serviceDesc != ''"> and service_desc = #{serviceDesc}</if>
            <if test="serviceNum != null "> and service_num = #{serviceNum}</if>
            <if test="tempServiceNum != null "> and temp_service_num = #{tempServiceNum}</if>
            <if test="serviceSpecification != null  and serviceSpecification != ''"> and service_specification = #{serviceSpecification}</if>
            <if test="quotedPrice != null "> and quoted_price = #{quotedPrice}</if>
            <if test="createUser != null  and createUser != ''"> and create_user = #{createUser}</if>
            <if test="updateUser != null  and updateUser != ''"> and update_user = #{updateUser}</if>
        </where>
    </select>
    
    <select id="selectCrmProjectProductOwnServiceById" parameterType="String" resultMap="CrmProjectProductOwnServiceResult">
        <include refid="selectCrmProjectProductOwnServiceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCrmProjectProductOwnService" parameterType="com.topsec.crm.project.core.entity.CrmProjectProductOwnService">
        insert into crm_project_product_own_service
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="recordId != null">record_id,</if>
            <if test="serviceId != null">service_id,</if>
            <if test="stuffCode != null">stuff_code,</if>
            <if test="serviceName != null">service_name,</if>
            <if test="serviceDesc != null">service_desc,</if>
            <if test="serviceNum != null">service_num,</if>
            <if test="tempServiceNum != null">temp_service_num,</if>
            <if test="serviceSpecification != null">service_specification,</if>
            <if test="quotedPrice != null">quoted_price,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="enableEdit != null">enable_edit,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="recordId != null">#{recordId},</if>
            <if test="serviceId != null">#{serviceId},</if>
            <if test="stuffCode != null">#{stuffCode},</if>
            <if test="serviceName != null">#{serviceName},</if>
            <if test="serviceDesc != null">#{serviceDesc},</if>
            <if test="serviceNum != null">#{serviceNum},</if>
            <if test="tempServiceNum != null">#{tempServiceNum},</if>
            <if test="serviceSpecification != null">#{serviceSpecification},</if>
            <if test="quotedPrice != null">#{quotedPrice},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="enableEdit != null">#{enableEdit},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCrmProjectProductOwnService" parameterType="com.topsec.crm.project.core.entity.CrmProjectProductOwnService">
        update crm_project_product_own_service
        <trim prefix="SET" suffixOverrides=",">
            <if test="recordId != null">product_record_id = #{recordId},</if>
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="stuffCode != null">stuff_code = #{stuffCode},</if>
            <if test="serviceName != null">service_name = #{serviceName},</if>
            <if test="serviceDesc != null">service_desc = #{serviceDesc},</if>
            <if test="serviceNum != null">service_num = #{serviceNum},</if>
            <if test="tempServiceNum != null">temp_service_num = #{tempServiceNum},</if>
            <if test="serviceSpecification != null">service_specification = #{serviceSpecification},</if>
            <if test="quotedPrice != null">quoted_price = #{quotedPrice},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="enableEdit != null">enable_edit = #{enableEdit},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCrmProjectProductOwnServiceById" parameterType="String">
        delete from crm_project_product_own_service where id = #{id}
    </delete>

    <delete id="deleteCrmProjectProductOwnServiceByIds" parameterType="String">
        delete from crm_project_product_own_service where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>