package com.topsec.crm.project.core.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.entity.CrmProjectSaleConfigVo;
import com.topsec.crm.project.core.entity.CrmProjectSaleConfig;
import com.topsec.crm.project.core.service.ICrmProjectSaleConfigService;
import com.topsec.tbsapi.client.TbsAccountClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.constant.TbsConstants;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.constants.TosConstants;
import com.topsec.tos.common.query.DepartmentQuery;
import com.topsec.tos.common.vo.DeptLevelPrincipalVO;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.tos.common.vo.TosDepartmentTreeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 销管配置
 * @date 2024-04-25
 */
@RestController
@RequestMapping("/saleConfig")
@Tag(name = "【销管配置】", description = "crmProjectSaleConfig")
public class CrmProjectSaleConfigController extends BaseController {

    @Resource
    private TosDepartmentClient tosDepartmentClient;

    @Autowired
    private ICrmProjectSaleConfigService crmProjectSaleConfigService;

    @Resource
    private TbsAccountClient tbsAccountClient;

    @Resource
    private TosEmployeeClient tosEmployeeClient;

    /**
     * 分页查询销管配置
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询销管配置")
    @PreAuthorize(hasPermission = "crm_sales_manager_config")
    public JsonObject<PageUtils<CrmProjectSaleConfigVo>> page(@RequestBody Map<String,Object> params) {
        String deptId = (String)params.get("deptId");
        String personId = (String)params.get("personId");
        startPage();
        List<CrmProjectSaleConfig> list = crmProjectSaleConfigService.list(new QueryWrapper<CrmProjectSaleConfig>().eq(StringUtils.isNotBlank(deptId), "dept_id", deptId).like(StringUtils.isNotBlank(personId), "person_id_list", personId).orderByDesc("update_time"));
        List<CrmProjectSaleConfigVo> listVo = HyperBeanUtils.copyListProperties(list,CrmProjectSaleConfigVo::new);
        PageUtils dataTable = getDataTable(list,listVo);

        return new JsonObject<>(dataTable);
    }

    /**
     * 更新【销管配置】
     */
    @PostMapping(value = "/updateSaleConfig")
    @Operation(summary = "更新【销管配置】")
    @PreAuthorize(hasPermission = "crm_sales_manager_config")
    public JsonObject<Void> updateSaleConfig(@RequestBody CrmProjectSaleConfigVo saleConfigVo) {
        String personId = getCurrentPersonId();
        if(saleConfigVo == null){
            return new JsonObject<>(ResultEnum.FAIL.getResult(),"参数异常");
        }
        String deptId = saleConfigVo.getDeptId();
        if(StringUtils.isBlank(deptId)){
            return new JsonObject<>(ResultEnum.FAIL.getResult(),"参数异常");
        }
        if(StringUtils.isBlank(saleConfigVo.getId())){
            List<CrmProjectSaleConfig> list = crmProjectSaleConfigService.query().eq("dept_id",deptId).list();
            if(!CollectionUtils.isEmpty(list)){
                return new JsonObject<>(ResultEnum.FAIL.getResult(),"该部门已存在销管配置");
            }
            saleConfigVo.setCreateUser(personId);
        }

        saleConfigVo.setUpdateUser(personId);
        return crmProjectSaleConfigService.updateSaleConfig(saleConfigVo);
    }

    /**
     * 删除【销管配置】
     */
    @GetMapping("/deleteSaleConfig")
    @Operation(summary = "删除【销管配置】")
    @PreAuthorize(hasPermission = "crm_sales_manager_config")
    public JsonObject<Void> deleteSaleConfig(@RequestParam String deptId) {
        if(StringUtils.isBlank(deptId)){
            return new JsonObject<>(ResultEnum.FAIL.getResult(),"参数异常");
        }
        return crmProjectSaleConfigService.deleteSaleConfig(deptId);
    }
    @PostMapping("/importSaleConfig")
    @Operation(summary = "导入【销管配置】")
    @PreAuthorize(hasPermission = "crm_sales_manager_config")
    public JsonObject<List<String>> importSaleConfig(MultipartFile file) throws IOException {
        String personId = getCurrentPersonId();
        String personName = getCurrentPersonName();
        if(file == null || file.isEmpty()){
            return new JsonObject<>(ResultEnum.FAIL.getResult(),"参数异常");
        }
        DepartmentQuery departmentQuery = new DepartmentQuery();
        departmentQuery.setFetchChild(true);
        departmentQuery.setPageNo(1);
        departmentQuery.setPageSize(Integer.MAX_VALUE);
        JsonObject<Page<TosDepartmentTreeVO>> pageJsonObject = tosDepartmentClient.pageChildrenDept(departmentQuery);
        Page<TosDepartmentTreeVO> tosDepartmentTreeVOPage = pageJsonObject.getObjEntity();
        List<TosDepartmentTreeVO> records = tosDepartmentTreeVOPage.getRecords();
        return crmProjectSaleConfigService.importSaleConfig(file,records,personId,personName);
    }

    @PreAuthorize
    @GetMapping("/querySaleConfig")
    @Operation(summary = "查询【销售配置】")
    public JsonObject<CrmProjectSaleConfigVo> querySaleConfig(@RequestParam String personId) throws IOException {
        List<String> depts = selectDeptPathOfEmployee(personId);
        List<CrmProjectSaleConfig> list = crmProjectSaleConfigService.query().in("dept_id",depts).list();
        if(!CollectionUtils.isEmpty(list)){
            Map<String, CrmProjectSaleConfig> configMap = list.stream().collect(Collectors.toMap(CrmProjectSaleConfig::getDeptId, config -> config));
            for (String dept : depts) {
                if(configMap.containsKey(dept)){
                    return new JsonObject<>(JSON.parseObject(JSON.toJSONString(configMap.get(dept)),CrmProjectSaleConfigVo.class));
                }
            }
        }
        return new JsonObject<>(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage());
    }

    @PreAuthorize
    @GetMapping("/querySaleConfigByDeptId")
    @Operation(summary = "根据部门ID查询【销售配置】")
    public JsonObject<CrmProjectSaleConfigVo> querySaleConfigByDeptId(@RequestParam(required = false) String deptId){
        if(StringUtils.isBlank(deptId)){
            deptId = getDepartmentId();
        }

        /*JsonObject<TosDepartmentVO> dept = tosDepartmentClient.findById(deptId);
        if(dept.getObjEntity() == null){
            return new JsonObject<>(ResultEnum.FAIL.getResult(),"部门不存在");
        }
        TosDepartmentVO tosDepartmentVO = dept.getObjEntity();
        List<String> deptList = tosDepartmentVO.getUuidPath();
        CrmProjectSaleConfigVo crmProjectSaleConfigVo = null;
        for(int i=deptList.size()-1;i>=0;i--){
            List<CrmProjectSaleConfig> list = crmProjectSaleConfigService.query().eq("dept_id",deptList.get(i)).list();
            if(!CollectionUtils.isEmpty(list)){
                crmProjectSaleConfigVo = HyperBeanUtils.copyProperties(list.get(0),CrmProjectSaleConfigVo::new);
                List<String> personIdList = crmProjectSaleConfigVo.getPersonIdList();
                if(!CollectionUtils.isEmpty(personIdList)){
                    List<String> accountIdList = new ArrayList<>();
                    JsonObject<Map<String, String>> accountMap = tbsAccountClient.selectIdMapByPersonIdList(personIdList.stream().toArray(String[]::new), TbsConstants.PlatformId.CRM);
                    for(String personId : personIdList) {
                        accountIdList.add(accountMap.getObjEntity().get(personId));
                    }
                    crmProjectSaleConfigVo.setAccountIdList(accountIdList);
                }
                break;
            }
        }*/

        CrmProjectSaleConfigVo crmProjectSaleConfigVo = null;
        JsonObject<List<DeptLevelPrincipalVO>> deptChainPrincipalObj = tosEmployeeClient.queryDeptChainPrincipal(deptId,null, TosConstants.RelationType.SALE_MANAGER_PRICE_EXAMINER);
        if(deptChainPrincipalObj.getObjEntity() != null){
            List<DeptLevelPrincipalVO> list = deptChainPrincipalObj.getObjEntity();
            if(!CollectionUtils.isEmpty(list)){
                list = list.stream().sorted(Comparator.comparingInt(DeptLevelPrincipalVO::getLevel)).collect(Collectors.toList());
                DeptLevelPrincipalVO deptLevelPrincipalVO = list.get(list.size()-1);
                List<EmployeeVO> principals = deptLevelPrincipalVO.getPrincipals();
                if(!CollectionUtils.isEmpty(principals)){
                    crmProjectSaleConfigVo = new CrmProjectSaleConfigVo();
                    crmProjectSaleConfigVo.setDeptId(deptId);
                    List<String> personIdList = principals.stream().map(EmployeeVO::getUuid).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(personIdList)){
                        List<String> accountIdList = new ArrayList<>();
                        JsonObject<Map<String, String>> accountMap = tbsAccountClient.selectIdMapByPersonIdList(personIdList.stream().toArray(String[]::new), TbsConstants.PlatformId.CRM);
                        for(String personId : personIdList) {
                            accountIdList.add(accountMap.getObjEntity().get(personId));
                        }
                        crmProjectSaleConfigVo.setAccountIdList(accountIdList);
                    }
                    crmProjectSaleConfigVo.setPersonIdList(personIdList);
                    crmProjectSaleConfigVo.setPersonNameList(principals.stream().map(person -> person.getName()+person.getJobNo()).collect(Collectors.toList()));

                }

            }
        }
        if(crmProjectSaleConfigVo == null){
            return new JsonObject<>(ResultEnum.SUCCESS.getResult(),ResultEnum.NULL_OBJ_ENTITY.getMessage());
        }
        return new JsonObject<>(crmProjectSaleConfigVo);

    }
}
