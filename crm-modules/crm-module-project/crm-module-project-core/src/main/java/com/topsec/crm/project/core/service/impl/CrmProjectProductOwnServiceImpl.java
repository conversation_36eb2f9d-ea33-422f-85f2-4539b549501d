package com.topsec.crm.project.core.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.agent.api.RemoteAgentRebateService;
import com.topsec.crm.agent.api.entity.crmagentrebate.input.CrmAgentRebateProjectInput;
import com.topsec.crm.agent.api.enums.AgentRebateEnum;
import com.topsec.crm.flow.api.RemoteFlowPriceReviewService;
import com.topsec.crm.flow.api.RemoteFlowService;
import com.topsec.crm.flow.api.RemoteMaterialApplyMainService;
import com.topsec.crm.flow.api.RemoteTargetedInventoryPreparationService;
import com.topsec.crm.flow.api.dto.ProductLockStateVO;
import com.topsec.crm.flow.api.dto.materialApply.ExistDisableProductVo;
import com.topsec.crm.flow.api.dto.materialApply.MaterialApplyMainVo;
import com.topsec.crm.flow.api.dto.pricereview.ProductPriceState;
import com.topsec.crm.flow.api.vo.CrmMaterialDisableVo;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnVO;
import com.topsec.crm.framework.common.enums.ProductTypeEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmCompletableFuture;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.Query;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.date.DateUtil;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import com.topsec.crm.product.api.RemoteCustomizedProductService;
import com.topsec.crm.product.api.RemoteProductService;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.project.api.client.TqmServiceClient;
import com.topsec.crm.project.api.dto.ProjectMemberQuery;
import com.topsec.crm.project.api.dto.ProjectProductOwnPageQuery;
import com.topsec.crm.project.api.dto.QuoteBillPageQuery;
import com.topsec.crm.project.api.entity.*;
import com.topsec.crm.project.api.entity.tqm.QuoteBill;
import com.topsec.crm.project.api.entity.tqm.json.ProductComponent;
import com.topsec.crm.project.api.entity.tqm.json.ProjectJson;
import com.topsec.crm.project.api.entity.tqm.json.Special;
import com.topsec.crm.project.api.entity.tqm.json.SubServer;
import com.topsec.crm.project.core.entity.*;
import com.topsec.crm.project.core.grossmargin.ProjectGrossMargin;
import com.topsec.crm.project.core.mapper.CrmProjectProductOwnMapper;
import com.topsec.crm.project.core.service.*;
import com.topsec.crm.project.core.util.GrossMarginCalculateUtil;
import com.topsec.tac.filter.UserInfoThreadExecutor;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 项目-自有产品Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-25
 */
@Service
public class CrmProjectProductOwnServiceImpl extends ServiceImpl<CrmProjectProductOwnMapper, CrmProjectProductOwn> implements ICrmProjectProductOwnService {
    @Autowired
    private CrmProjectProductOwnMapper crmProjectProductOwnMapper;

    @Autowired
    private ICrmProjectDirectlyService crmProjectDirectlyService;

    @Autowired
    private ICrmProjectAgentService crmProjectAgentService;

    @Autowired
    private ICrmProjectProductOwnService crmProjectProductOwnService;

    @Resource
    private ICrmProjectOutsourcingServiceService crmProjectOutsourcingServiceService;

    @Autowired
    private ICrmProjectProductThirdService crmProjectProductThirdService;

    @Autowired
    private ICrmProjectProductOwnServiceService crmProjectProductOwnServiceService;

    @Autowired
    private ICrmProjectProductOwnServiceRangeService crmProjectProductOwnServiceRangeService;

    @Autowired
    @Lazy
    private ProjectGrossMargin projectGrossMargin;

    @Autowired
    private RemoteFlowService remoteFlowService;

    @Resource
    private RemoteProductService remoteProductService;

    @Autowired
    private RemoteCustomizedProductService remoteCustomizedProductService;

    @Autowired
    private RemoteFlowPriceReviewService remoteFlowPriceReviewService;

    @Autowired
    private ICrmApprovalDiscountCategoryService crmApprovalDiscountCategoryService;

    @Autowired
    private ICrmProjectProductSnService crmProjectProductSnService;

    @Autowired
    private ICrmProjectProductAccessRulesService crmProjectProductAccessRulesService;

    @Autowired
    private ICrmProjectProductMaintainService crmProjectProductMaintainService;

    @Autowired
    private ICrmProjectProductMaintainInvestmentService crmProjectProductMaintainInvestmentService;

    @Autowired
    private ICrmProjectMemberService crmProjectMemberService;

    @Autowired
    private RemoteMaterialApplyMainService remoteMaterialApplyMainService;

    @Autowired
    private RemoteAgentRebateService remoteAgentRebateService;

    @Autowired
    private TqmServiceClient tqmServiceClient;

    @Autowired
    private RemoteTargetedInventoryPreparationService remoteTargetedInventoryPreparationService;

    // 构建默认的公司自有产品查询条件
    @Override
    public LambdaQueryWrapper<CrmProjectProductOwn> buildDefaultQueryWrapper() {
        return new LambdaQueryWrapper<CrmProjectProductOwn>()
                .apply(" product_num > return_num ");  // 产品数量大于退换数量
    }

    /**
     * 产品查询后置处理
     *
     * @param list
     */
    @Override
    public void productOwnPostProcessing(List<CrmProjectProductOwn> list) {
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(m -> {
                long actualCount = m.getProductNum() - m.getReturnNum();
                m.setProductNum(actualCount);
                m.setQuotedTotalPrice(m.getQuotedPrice().multiply(new BigDecimal(actualCount)));
                m.setDealTotalPrice(m.getDealPrice().multiply(new BigDecimal(actualCount)));
                m.setFinalTotalPrice(m.getFinalPrice().multiply(new BigDecimal(actualCount)));
            });
        }
    }
/**
     * 查询项目-自有产品
     *
     * @param id 项目-自有产品ID
     * @return 项目-自有产品
     */
    /*@Override
    public CrmProjectProductOwn selectCrmProjectProductOwnById(String id) {
        return CrmProjectProductOwnMapper.selectById(id);
    }*/

    /**
     * 修改项目-自有产品
     *
     * @param crmProjectProductOwn 项目-自有产品
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateCrmProjectProductOwn(CrmProjectProductOwn crmProjectProductOwn) {
        crmProjectProductOwn.setUpdateTime(LocalDateTime.now());
        //crmProjectProductOwnService.fillProductPrice(Collections.singletonList(crmProjectProductOwn));
        Boolean aBoolean = crmProjectProductOwnService.updateById(crmProjectProductOwn);
        List<CrmProjectProductOwn> crmProjectProductOwnComponent = crmProjectProductOwn.getChildren();
        if (CollectionUtils.isNotEmpty(crmProjectProductOwnComponent)) {
            crmProjectProductOwnService.updateBatchById(crmProjectProductOwnComponent);
            for (CrmProjectProductOwn m:crmProjectProductOwnComponent) {
                List<CrmProjectProductOwnService> subServiceOfComponents =  m.getCrmProjectProductOwnService();
                if (CollectionUtils.isNotEmpty(subServiceOfComponents)) {
                    crmProjectProductOwnServiceService.updateBatchById(subServiceOfComponents);
                }
                List<CrmProjectProductOwnServiceRange> serverRangeOfComponents = m.getCrmProjectProductOwnServiceRange();
                if (CollectionUtils.isNotEmpty(serverRangeOfComponents)) {
                    crmProjectProductOwnServiceRangeService.updateBatchById(serverRangeOfComponents);
                }
                // 自动填充分销保修服务价格，固定的物料代码（6001002003002）
                if(m.getStuffCode().equals("6001002003002")){
                    crmProjectProductOwnServiceRangeService.recalculateDistributionPrice(Collections.singletonList(m),m.getCrmProjectProductOwnServiceRange());
                }
            }
        }
        List<CrmProjectProductOwnService> productOwnService = crmProjectProductOwn.getCrmProjectProductOwnService();
        if (CollectionUtils.isNotEmpty(productOwnService)) {
            crmProjectProductOwnServiceService.updateBatchById(productOwnService);
        }
        // 自动填充分销保修服务价格，固定的物料代码（6001002003002）
        if(crmProjectProductOwn.getStuffCode().equals("6001002003002")){
            crmProjectProductOwnServiceRangeService.recalculateDistributionPrice(Collections.singletonList(crmProjectProductOwn),crmProjectProductOwn.getCrmProjectProductOwnServiceRange());
        }
        List<CrmProjectProductOwnServiceRange> crmProjectProductOwnServiceRange = crmProjectProductOwn.getCrmProjectProductOwnServiceRange();
        if (CollectionUtils.isNotEmpty(crmProjectProductOwnServiceRange)) {
            crmProjectProductOwnServiceRangeService.updateBatchById(crmProjectProductOwnServiceRange);
        }
        crmProjectProductOwnService.writeRebateCountProject(crmProjectProductOwn.getProjectId(), false);
        return aBoolean;
    }

    /**
     * 批量删除项目-自有产品
     *
     * @param ids 需要删除的项目-自有产品ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteCrmProjectProductOwnByIds(String[] ids) {
        List<String> recordIds = new ArrayList<>(Arrays.asList(ids));
        List<CrmProjectProductOwn> list = crmProjectProductOwnService.list(buildDefaultQueryWrapper().in(CrmProjectProductOwn::getParentId, recordIds));
        crmProjectProductOwnService.productOwnPostProcessing(list);
        if(CollectionUtils.isNotEmpty(list)){
            recordIds.addAll(list.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList()));
        }
        // 删除产品和配件
        crmProjectProductOwnService.removeBatchByIds(recordIds, true);
        // 删除服务范围
        List<CrmProjectProductOwnServiceRange> rangeList =  crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getRecordId, recordIds));
        if(CollectionUtils.isNotEmpty(rangeList)){
            crmProjectProductOwnServiceRangeService.removeBatchByIds(rangeList.stream().map(CrmProjectProductOwnServiceRange::getId).collect(Collectors.toSet()), true);
        }
        // 删除绑定产品
        List<CrmProjectProductOwnServiceRange> rangesOfBind = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getBindProductRecordId, recordIds));
        if (CollectionUtils.isNotEmpty(rangesOfBind)) {
            crmProjectProductOwnServiceRangeService.removeBatchByIds(rangesOfBind.stream().map(CrmProjectProductOwnServiceRange::getId).collect(Collectors.toSet()), true);
        }
        // 删除子服务
        List<CrmProjectProductOwnService> subServices = crmProjectProductOwnServiceService.list(new LambdaQueryWrapper<CrmProjectProductOwnService>().in(CrmProjectProductOwnService::getRecordId, recordIds));
        if (CollectionUtils.isNotEmpty(subServices)) {
            crmProjectProductOwnServiceService.removeBatchByIds(subServices.stream().map(CrmProjectProductOwnService::getId).collect(Collectors.toSet()), true);
        }
        // 删除产品序列号信息
        List<CrmProjectProductSn> productSns = crmProjectProductSnService.list(new LambdaQueryWrapper<CrmProjectProductSn>().in(CrmProjectProductSn::getRecordId, recordIds));
        if (CollectionUtils.isNotEmpty(productSns)) {
            crmProjectProductSnService.removeBatchByIds(productSns.stream().map(CrmProjectProductSn::getId).collect(Collectors.toSet()), true);
        }

        List<CrmProjectProductOwn> crmProjectProductOwns = crmProjectProductOwnService.listByIds(recordIds);
        if(com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(crmProjectProductOwns)){
            Set<String> projectIds = crmProjectProductOwns.stream().map(CrmProjectProductOwn::getProjectId).collect(Collectors.toSet());
            for (String projectId : projectIds){
                crmProjectProductOwnService.writeRebateCountProject(projectId, false);
            }
        }
        return ids.length;
    }

    /**
     * 删除项目-自有产品信息
     *
     * @param id 项目-自有产品ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteCrmProjectProductOwnById(String id) {
        List<String> recordIds = Arrays.asList(id);
        List<CrmProjectProductOwn> list = crmProjectProductOwnService.list(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getParentId, id));
        crmProjectProductOwnService.productOwnPostProcessing(list);
        if(CollectionUtils.isNotEmpty(list)){
            recordIds.addAll(list.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList()));
        }
        // 删除产品和配件
        crmProjectProductOwnService.removeBatchByIds(recordIds, true);
        // 删除服务范围
        List<CrmProjectProductOwnServiceRange> rangeList =  crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getRecordId, recordIds));
        if(CollectionUtils.isNotEmpty(rangeList)){
            crmProjectProductOwnServiceRangeService.removeBatchByIds(rangeList.stream().map(CrmProjectProductOwnServiceRange::getId).collect(Collectors.toSet()), true);
        }
        // 删除绑定产品
        List<CrmProjectProductOwnServiceRange> rangesOfBind = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getBindProductRecordId, recordIds));
        if (CollectionUtils.isNotEmpty(rangesOfBind)) {
            crmProjectProductOwnServiceRangeService.removeBatchByIds(rangesOfBind.stream().map(CrmProjectProductOwnServiceRange::getId).collect(Collectors.toSet()), true);
        }
        // 删除子服务
        List<CrmProjectProductOwnService> subServices = crmProjectProductOwnServiceService.list(new LambdaQueryWrapper<CrmProjectProductOwnService>().in(CrmProjectProductOwnService::getRecordId, recordIds));
        if (CollectionUtils.isNotEmpty(subServices)) {
            crmProjectProductOwnServiceService.removeBatchByIds(subServices.stream().map(CrmProjectProductOwnService::getId).collect(Collectors.toSet()), true);
        }
        // 删除产品序列号信息
        List<CrmProjectProductSn> productSns = crmProjectProductSnService.list(new LambdaQueryWrapper<CrmProjectProductSn>().in(CrmProjectProductSn::getRecordId, recordIds));
        if (CollectionUtils.isNotEmpty(productSns)) {
            crmProjectProductSnService.removeBatchByIds(productSns.stream().map(CrmProjectProductSn::getId).collect(Collectors.toSet()), true);
        }
        return 1;
    }

    @Override
    public PageUtils<CrmProjectProductOwn> selectCrmProjectProductOwnPageByParams(ProjectProductOwnPageQuery params) {
        String projectId = params.getProjectId();
        String commonSearch = params.getCommonSearch();
        String currentPersonId = params.getCurrentPersonId();
        List<Integer> saleAccess = params.getSaleAccess();
        List<Integer> reportStatus = params.getReportStatus();
        if (StringUtils.isNotBlank(projectId)) {
            BriefInfo briefInfo = crmProjectAgentService.queryInfoByProjectId(projectId);
            LambdaQueryWrapper<CrmProjectProductOwn> crmProjectProductOwnQueryWrapper = buildDefaultQueryWrapper()
                    .eq(CrmProjectProductOwn::getProjectId, projectId)
                    .in(CollectionUtils.isNotEmpty(reportStatus), CrmProjectProductOwn::getReportStatus, reportStatus)
                    .eq(CrmProjectProductOwn::getParentId, "0")
                    .orderByDesc(CollectionUtils.isEmpty(params.getOrders()), CrmProjectProductOwn::getCreateTime, CrmProjectProductOwn::getId);

            if(StringUtils.isNotBlank(commonSearch)){
                List<CrmProjectProductOwn> list = crmProjectProductOwnService.list(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId).and(StringUtils.isNotBlank(commonSearch), wrapper -> wrapper.like(CrmProjectProductOwn::getProductName, commonSearch)
                        .or().like(CrmProjectProductOwn::getStuffCode, commonSearch)));
                crmProjectProductOwnService.productOwnPostProcessing(list);
                if(CollectionUtils.isNotEmpty(list)){
                    crmProjectProductOwnQueryWrapper.in(CrmProjectProductOwn::getId, list.stream().map(m -> {
                        if("0".equals(m.getParentId())){
                            return m.getId();
                        }else{
                            return m.getParentId();
                        }
                    }).collect(Collectors.toSet()));
                }else{
                    crmProjectProductOwnQueryWrapper.isNull(CrmProjectProductOwn::getId);
                }
            }
            if(CollectionUtils.isNotEmpty(saleAccess)){
                // 根据项目ID查询指定通路的产品行ID
                Map<String, List<Integer>> accessRulesByProjectQuery = crmProjectProductAccessRulesService.getAccessRulesByProjectQuery(briefInfo.getNumber(), currentPersonId, saleAccess, null);
                Set<String> recordIdSet = accessRulesByProjectQuery.keySet();
                if(CollectionUtils.isNotEmpty(recordIdSet)){
                    crmProjectProductOwnQueryWrapper.in(CrmProjectProductOwn::getId, recordIdSet);
                }else{
                    crmProjectProductOwnQueryWrapper.isNull(CrmProjectProductOwn::getId);
                }
            }
            Page<CrmProjectProductOwn> page = crmProjectProductOwnService.page(new Query<CrmProjectProductOwn>().getPage(params), crmProjectProductOwnQueryWrapper);
            List<CrmProjectProductOwn> crmProjectProductOwns = page.getRecords();
            crmProjectProductOwnService.productOwnPostProcessing(crmProjectProductOwns);
            if (CollectionUtils.isNotEmpty(crmProjectProductOwns)) {
                fillProductOwnExt(crmProjectProductOwns, projectId, currentPersonId, briefInfo.getNumber());
            }
            return new PageUtils<>(page);
        }
        return new PageUtils<>();
    }

    /**
     *
     *
     * @param crmProjectProductOwns
     * @param projectId
     * @param currentPersonId
     * @param projectNo
     */
    private void fillProductOwnExt(List<CrmProjectProductOwn> crmProjectProductOwns, String projectId, String currentPersonId, String projectNo) {
        Set<String> recordIdsOfLevelOne = crmProjectProductOwns.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toSet());
        List<CrmProjectProductOwn> componentList = crmProjectProductOwnService.list(buildDefaultQueryWrapper().in(CrmProjectProductOwn::getParentId, recordIdsOfLevelOne));
        crmProjectProductOwnService.productOwnPostProcessing(componentList);
        Set<String> allRecordId = crmProjectProductOwns.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toSet());
        Set<String> allProductIds =  crmProjectProductOwns.stream().map(CrmProjectProductOwn::getProductId).collect(Collectors.toSet());
        if(CollectionUtils.isNotEmpty(componentList)){
            allRecordId.addAll(componentList.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toSet()));
            allProductIds.addAll(componentList.stream().map(CrmProjectProductOwn::getProductId).collect(Collectors.toSet()));
        }

        // 组装公司自有产品树
        buildProductOwnTree(crmProjectProductOwns, componentList);

        // 填充运维信息并且计算毛利
        CompletableFuture<Void> future0 = CrmCompletableFuture.timedFuture("填充运维信息并且计算毛利",() ->{
            crmProjectProductMaintainService.fillMaintainData(crmProjectProductOwns);
            projectGrossMargin.calculateProjectProductOwnGrossMargin(crmProjectProductOwns, projectId);
        }, UserInfoThreadExecutor.getExecutor());
        // 公司自有产品填充产品锁定状态
        CompletableFuture<Void> future1 = CrmCompletableFuture.timedFuture("公司自有产品填充产品锁定状态",() ->
                fillProductOwnLockStatus(projectId, allRecordId, crmProjectProductOwns), UserInfoThreadExecutor.getExecutor()
        );
        // 公司自有产品填充产品价格状态
        CompletableFuture<Void> future2 = CrmCompletableFuture.timedFuture("公司自有产品填充产品价格状态",() ->
                fillProjectOwnPriceStatus(projectId, allRecordId, crmProjectProductOwns), UserInfoThreadExecutor.getExecutor()
        );
        // 公司自有产品填充禁用状态
        CompletableFuture<Void> future3 = CrmCompletableFuture.timedFuture("公司自有产品填充禁用状态",() ->{
            if(StringUtils.isNotBlank(projectNo)){
                // 公司自有产品填充无通路禁用状态
                fillProductOwnPathDisable(projectNo, currentPersonId, crmProjectProductOwns);
            }
            // 公司自有产品填充代码禁用状态
            LocalDateTime dd1 = LocalDateTime.now();
            fillProductOwnCodeDisable(projectId, crmProjectProductOwns);
            LocalDateTime dd2 = LocalDateTime.now();
            System.out.println("dd2-dd1=============耗时"+ DateUtil.getMillisDifferenceByDuration(dd1,dd2));
        }, UserInfoThreadExecutor.getExecutor());

        // 填充产品的序列号
        CompletableFuture<Void> future4 = CrmCompletableFuture.timedFuture("填充产品的序列号",() ->{
            // 查询产品的序列号
            List<CrmProjectProductSn> crmProjectProductSns = crmProjectProductSnService.list(new LambdaQueryWrapper<CrmProjectProductSn>().in(CrmProjectProductSn::getRecordId, allRecordId));
            crmProjectProductOwns.forEach(m -> {
                m.setCrmProjectProductSn(Optional.of(crmProjectProductSns.stream().filter(n -> n.getRecordId().equals(m.getId())).collect(Collectors.toList())).orElse(new ArrayList<>()));
            });
            componentList.forEach(m -> {
                m.setCrmProjectProductSn(Optional.of(crmProjectProductSns.stream().filter(n -> n.getRecordId().equals(m.getId())).collect(Collectors.toList())).orElse(new ArrayList<>()));
            });
        }, UserInfoThreadExecutor.getExecutor());

        // 公司自有产品填充子服务或者子配件
        CompletableFuture<Void> future5 = CrmCompletableFuture.timedFuture("公司自有产品填充子服务或者子配件",() ->{
            // 查询产品是否有子服务或者子配件
            JsonObject<Map<String, Boolean>> hasChildren = remoteProductService.batchFindHasChild(new ArrayList<>(allProductIds));
            crmProjectProductOwns.forEach(m -> {
                m.setHasChild(hasChildren.isSuccess() ? hasChildren.getObjEntity().getOrDefault(m.getProductId(), false) : false);
            });
            componentList.forEach(m -> {
                m.setHasChild(hasChildren.isSuccess() ? hasChildren.getObjEntity().getOrDefault(m.getProductId(), false) : false);
            });
        }, UserInfoThreadExecutor.getExecutor());

        // 公司自有产品填充定制开发类标识
        CompletableFuture<Void> future6 = CrmCompletableFuture.timedFuture("公司自有产品填充定制开发类标识",() ->{
            // 查询产品的定制开发类标识
            JsonObject<Map<String, List<Integer>>> customizedType = remoteCustomizedProductService.batchGetCustomizedType(new ArrayList<>(allProductIds));
            crmProjectProductOwns.forEach(m -> {
                m.setCustomizedType(customizedType.isSuccess() ? customizedType.getObjEntity().getOrDefault(m.getProductId(), null) : null);
            });
            componentList.forEach(m -> {
                m.setCustomizedType(customizedType.isSuccess() ? customizedType.getObjEntity().getOrDefault(m.getProductId(), null) : null);
            });
        }, UserInfoThreadExecutor.getExecutor());
        // 等待所有任务完成
        CompletableFuture.allOf(future0, future1, future2, future3, future4, future5, future6).join();
    }

    private void buildProductOwnTree(List<CrmProjectProductOwn> crmProjectProductOwns, List<CrmProjectProductOwn> componentList) {
        // 组装产品
        CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
            List<CrmProjectProductOwn> productOwns = crmProjectProductOwns.stream().filter(m -> ProductTypeEnum.ProductTypeProductEnum.isProduct(m.getProductType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(productOwns)) {
                productOwns.forEach(m -> {
                    // 查询自有产品配件
                    List<CrmProjectProductOwn> foundComponents = componentList.stream().filter(n -> n.getParentId().equals(m.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(foundComponents)) {
                        List<String> recordIds = foundComponents.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList());
                        List<CrmProjectProductOwnService> subServices = crmProjectProductOwnServiceService.list(new LambdaQueryWrapper<CrmProjectProductOwnService>().in(CrmProjectProductOwnService::getRecordId, recordIds));
                        List<CrmProjectProductOwnServiceRange> serverRanges = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getRecordId, recordIds));
                        foundComponents.forEach(n -> {
                            // 查询自有产品服务
                            List<CrmProjectProductOwnService> foundServices = subServices.stream().filter(k -> k.getRecordId().equals(n.getId())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(foundServices)) {
                                n.setCrmProjectProductOwnService(foundServices);
                            }
                            // 查询自有产品服务范围
                            List<CrmProjectProductOwnServiceRange> foundServiceRanges = serverRanges.stream().filter(k -> k.getRecordId().equals(n.getId())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(foundServiceRanges)) {
                                n.setCrmProjectProductOwnServiceRange(foundServiceRanges);
                            }
                        });
                        // 查询配件中是否存在前端未编辑的数据,如果存在，则将状态传递到主产品中，方便前端展示
                        boolean frontEdit = foundComponents.stream().anyMatch(n -> !n.getFrontEdit());
                        m.setFrontEdit(!frontEdit);
                        m.setChildren(foundComponents);
                    }
                });
            }
        }, UserInfoThreadExecutor.getExecutor());
        // 组装服务
        CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
            List<CrmProjectProductOwn> serviceOwns = crmProjectProductOwns.stream().filter(m -> ProductTypeEnum.ProductTypeProductEnum.isService(m.getProductType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(serviceOwns)) {
                List<String> productRecordIds = serviceOwns.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList());
                List<CrmProjectProductOwnService> productOwnServices = crmProjectProductOwnServiceService.list(new LambdaQueryWrapper<CrmProjectProductOwnService>().in(CrmProjectProductOwnService::getRecordId, productRecordIds));
                List<CrmProjectProductOwnServiceRange> productOwnServiceRanges = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getRecordId, productRecordIds));
                serviceOwns.forEach(m -> {
                    // 查询自有产品安全服务
                    List<CrmProjectProductOwnService> foundServices = productOwnServices.stream().filter(n -> n.getRecordId().equals(m.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(foundServices)) {
                        m.setCrmProjectProductOwnService(foundServices);
                    }
                    // 查询自有产品安全服务范围
                    List<CrmProjectProductOwnServiceRange> foundServiceRanges = productOwnServiceRanges.stream().filter(n -> n.getRecordId().equals(m.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(foundServiceRanges)) {
                        m.setCrmProjectProductOwnServiceRange(foundServiceRanges);
                    }
                });
            }
        }, UserInfoThreadExecutor.getExecutor());
        // 等待所有任务完成
        CompletableFuture.allOf(future1, future2).join();
    }

    private void fillProjectOwnPriceStatus(String projectId, Set<String> recordId, List<CrmProjectProductOwn> crmProjectProductOwns) {
        JsonObject<Map<String, ProductPriceState>> priceState = remoteFlowPriceReviewService.queryPriceState(projectId, recordId);
        if (priceState.isSuccess()) {
            crmProjectProductOwns.forEach(m -> {
                ProductPriceState productPriceState = priceState.getObjEntity().get(m.getId());
                if (productPriceState != null) {
                    m.setState(productPriceState.getState());
                    m.setProcessInstanceId(productPriceState.getProcessInstanceId());
                    m.setExpiryDate(null != productPriceState.getPriceExpiryTime() ? productPriceState.getPriceExpiryTime().toLocalDate():null);
                    m.setPriceState(productPriceState);
                }
                if(CollectionUtils.isNotEmpty(m.getChildren())){
                    m.getChildren().forEach(n -> {
                        ProductPriceState productPriceStateChildren = priceState.getObjEntity().get(n.getId());
                        if (productPriceStateChildren != null) {
                            n.setState(productPriceStateChildren.getState());
                            n.setProcessInstanceId(productPriceStateChildren.getProcessInstanceId());
                            n.setExpiryDate(null != productPriceStateChildren.getPriceExpiryTime() ? productPriceStateChildren.getPriceExpiryTime().toLocalDate():null);
                            n.setPriceState(productPriceStateChildren);
                        }
                    });
                }
            });
        }
    }

    /**
     * (渠道项目)根据自定义参数查询项目-自有产品列表
     *
     * @param params 自定义参数
     * @return 项目-自有产品集合
     */
    @Override
    public PageUtils<CrmProjectProductOwn> selectCrmProjectProductOwnPageByParamsOfAgent(ProjectProductOwnPageQuery params) {
        String projectId = params.getProjectId();
        String commonSearch = params.getCommonSearch();
        List<Integer> reportStatus = params.getReportStatus();
        if (StringUtils.isNotBlank(projectId)) {
            //CrmProjectAgent byId = crmProjectAgentService.selectCrmProjectAgentById(projectId);
            LambdaQueryWrapper<CrmProjectProductOwn> crmProjectProductOwnQueryWrapper = buildDefaultQueryWrapper()
                    .eq(CrmProjectProductOwn::getProjectId, projectId)
                    .in(CollectionUtils.isNotEmpty(reportStatus), CrmProjectProductOwn::getReportStatus, reportStatus)
                    .eq(CrmProjectProductOwn::getParentId, "0")
                    .orderByDesc(CollectionUtils.isEmpty(params.getOrders()), CrmProjectProductOwn::getCreateTime, CrmProjectProductOwn::getId);
            if(StringUtils.isNotBlank(commonSearch)){
                List<CrmProjectProductOwn> list = crmProjectProductOwnService.list(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId).and(StringUtils.isNotBlank(commonSearch), wrapper -> wrapper.like(CrmProjectProductOwn::getProductName, commonSearch)
                        .or().like(CrmProjectProductOwn::getStuffCode, commonSearch)));
                crmProjectProductOwnService.productOwnPostProcessing(list);
                if(CollectionUtils.isNotEmpty(list)){
                    crmProjectProductOwnQueryWrapper.in(CrmProjectProductOwn::getId, list.stream().map(m -> {
                        if("0".equals(m.getParentId())){
                            return m.getId();
                        }else{
                            return m.getParentId();
                        }
                    }).collect(Collectors.toSet()));
                }else{
                    crmProjectProductOwnQueryWrapper.isNull(CrmProjectProductOwn::getId);
                }
            }
            Page<CrmProjectProductOwn> page = crmProjectProductOwnService.page(new Query<CrmProjectProductOwn>().getPage(params), crmProjectProductOwnQueryWrapper);
            List<CrmProjectProductOwn> crmProjectProductOwns = page.getRecords();
            crmProjectProductOwnService.productOwnPostProcessing(crmProjectProductOwns);
            if (CollectionUtils.isNotEmpty(crmProjectProductOwns)) {
                fillProductOwnExt(crmProjectProductOwns, projectId, null, null);
                /*Set<String> recordId = crmProjectProductOwns.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toSet());
                Set<String> productIds =  crmProjectProductOwns.stream().map(CrmProjectProductOwn::getProductId).collect(Collectors.toSet());
                // 公司自有产品填充产品锁定状态
                fillProductOwnLockStatus(projectId, recordId, crmProjectProductOwns);
                // 查询产品的序列号
                List<CrmProjectProductSn> crmProjectProductSns = crmProjectProductSnService.list(new LambdaQueryWrapper<CrmProjectProductSn>().in(CrmProjectProductSn::getRecordId, recordId));
                // 查询产品是否有子服务或者子配件
                JsonObject<Map<String, Boolean>> hasChildren = remoteProductService.batchFindHasChild(new ArrayList<>(productIds));
                // 查询产品的定制开发类标识
                JsonObject<Map<String, List<Integer>>> customizedType = remoteCustomizedProductService.batchGetCustomizedType(new ArrayList<>(productIds));
                crmProjectProductOwns.forEach(m -> {
                    m.setCrmProjectProductSn(Optional.of(crmProjectProductSns.stream().filter(n -> n.getRecordId().equals(m.getId())).collect(Collectors.toList())).orElse(new ArrayList<>()));
                    m.setHasChild(hasChildren.isSuccess() ? hasChildren.getObjEntity().getOrDefault(m.getProductId(), false) : false);
                    m.setCustomizedType(customizedType.isSuccess() ? customizedType.getObjEntity().getOrDefault(m.getProductId(), null) : null);
                });
                List<CrmProjectProductOwn> productOwns = crmProjectProductOwns.stream().filter(m -> ProductTypeEnum.ProductTypeProductEnum.isProduct(m.getProductType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(productOwns)) {
                    List<String> productRecordIds = productOwns.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList());
                    Map<String, List<Integer>> customizedTypeOfComponents = new HashMap<>();
                    Map<String, Boolean> hasChildrenOfComponents = new HashMap<>();
                    List<CrmProjectProductOwn> productOwnComponents = crmProjectProductOwnService.list(buildDefaultQueryWrapper().in(CrmProjectProductOwn::getParentId, productRecordIds));
                    crmProjectProductOwnService.productOwnPostProcessing(productOwnComponents);
                    if(CollectionUtils.isNotEmpty(productOwnComponents)){
                        Set<String> productIdsOfComponents =  productOwnComponents.stream().map(CrmProjectProductOwn::getProductId).collect(Collectors.toSet());
                        // 查询产品是否有子服务或者子配件
                        JsonObject<Map<String, Boolean>> hasChildrenJsonObject = remoteProductService.batchFindHasChild(new ArrayList<>(productIdsOfComponents));
                        if(hasChildrenJsonObject.isSuccess()){
                            hasChildrenOfComponents = hasChildrenJsonObject.getObjEntity();
                        }
                        // 查询产品的定制开发类标识
                        JsonObject<Map<String, List<Integer>>> customizedTypeJsonObject = remoteCustomizedProductService.batchGetCustomizedType(new ArrayList<>(productIdsOfComponents));
                        if(customizedTypeJsonObject.isSuccess()){
                            customizedTypeOfComponents = customizedTypeJsonObject.getObjEntity();
                        }
                    }
                    Map<String, List<Integer>> finalCustomizedTypeOfComponents = customizedTypeOfComponents;
                    Map<String, Boolean> finalHasChildrenOfComponents = hasChildrenOfComponents;
                    productOwns.forEach(m -> {
                        // 查询自有产品配件
                        List<CrmProjectProductOwn> foundComponents = productOwnComponents.stream().filter(n -> n.getParentId().equals(m.getId())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(foundComponents)) {
                            List<String> recordIds = foundComponents.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList());
                            List<CrmProjectProductOwnService> subServices = crmProjectProductOwnServiceService.list(new LambdaQueryWrapper<CrmProjectProductOwnService>().in(CrmProjectProductOwnService::getRecordId, recordIds));
                            List<CrmProjectProductOwnServiceRange> serverRanges = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getRecordId, recordIds));
                            foundComponents.forEach(n -> {
                                n.setCustomizedType(finalCustomizedTypeOfComponents.getOrDefault(n.getProductId(), null));
                                n.setHasChild(finalHasChildrenOfComponents.getOrDefault(n.getProductId(), false));
                                // 查询自有产品服务
                                List<CrmProjectProductOwnService> foundServices = subServices.stream().filter(k -> k.getRecordId().equals(n.getId())).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(foundServices)) {
                                    n.setCrmProjectProductOwnService(foundServices);
                                }
                                // 查询自有产品服务范围
                                List<CrmProjectProductOwnServiceRange> foundServiceRanges = serverRanges.stream().filter(k -> k.getRecordId().equals(n.getId())).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(foundServiceRanges)) {
                                    n.setCrmProjectProductOwnServiceRange(foundServiceRanges);
                                }
                                n.setCrmProjectProductSn(Optional.of(crmProjectProductSns.stream().filter(k -> k.getRecordId().equals(n.getId())).collect(Collectors.toList())).orElse(new ArrayList<>()));
                            });
                            m.setChildren(foundComponents);
                        }
                    });
                }
                List<CrmProjectProductOwn> serviceOwns = crmProjectProductOwns.stream().filter(m -> ProductTypeEnum.ProductTypeProductEnum.isService(m.getProductType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(serviceOwns)) {
                    List<String> productRecordIds = serviceOwns.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList());
                    List<CrmProjectProductOwnService> productOwnServices = crmProjectProductOwnServiceService.list(new LambdaQueryWrapper<CrmProjectProductOwnService>().in(CrmProjectProductOwnService::getRecordId, productRecordIds));
                    List<CrmProjectProductOwnServiceRange> productOwnServiceRanges = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getRecordId, productRecordIds));
                    serviceOwns.forEach(m -> {
                        // 查询自有产品安全服务
                        List<CrmProjectProductOwnService> foundServices = productOwnServices.stream().filter(n -> n.getRecordId().equals(m.getId())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(foundServices)) {
                            m.setCrmProjectProductOwnService(foundServices);
                        }
                        // 查询自有产品安全服务范围
                        List<CrmProjectProductOwnServiceRange> foundServiceRanges = productOwnServiceRanges.stream().filter(n -> n.getRecordId().equals(m.getId())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(foundServiceRanges)) {
                            m.setCrmProjectProductOwnServiceRange(foundServiceRanges);
                        }
                        m.setCrmProjectProductSn(Optional.of(crmProjectProductSns.stream().filter(k -> k.getRecordId().equals(m.getId())).collect(Collectors.toList())).orElse(new ArrayList<>()));
                    });
                }
                // 公司自有产品填充代码禁用状态
                fillProductOwnCodeDisable(projectId, crmProjectProductOwns);*/
            }
            return new PageUtils<>(page);
        }
        return new PageUtils<>();
    }

    /**
     * 公司自有产品填充产品锁定状态
     * @param projectId
     * @param recordId
     * @param crmProjectProductOwns
     */
    private void fillProductOwnLockStatus(String projectId, Set<String> recordId, List<CrmProjectProductOwn> crmProjectProductOwns) {
        // 查询产品锁定状态
        JsonObject<Map<String, ProductLockStateVO>> mapJsonObject = remoteFlowService.queryLockState(projectId, recordId);
        if (mapJsonObject.isSuccess()) {
            crmProjectProductOwns.forEach(m -> {
                ProductLockStateVO productLockState = mapJsonObject.getObjEntity().get(m.getId());
                if (productLockState != null) {
                    m.setProductStatus(productLockState.getState());
                    m.setProcessInfoList(productLockState.getProcessInfoList());
                }
                if(CollectionUtils.isNotEmpty(m.getChildren())){
                    m.getChildren().forEach(n -> {
                        ProductLockStateVO productLockStateChildren = mapJsonObject.getObjEntity().get(n.getId());
                        if (productLockStateChildren != null) {
                            n.setProductStatus(productLockStateChildren.getState());
                            n.setProcessInfoList(productLockStateChildren.getProcessInfoList());
                        }
                    });
                }
            });
        }
    }

    /**
     * 公司自有产品填充代码禁用状态
     * @param projectId
     * @param crmProjectProductOwns
     */
    private void fillProductOwnCodeDisable(String projectId, List<CrmProjectProductOwn> crmProjectProductOwns) {
        // 查询禁用代码
        ProjectMemberQuery query = new ProjectMemberQuery();
        query.setProjectIds(new HashSet<>(Collections.singleton(projectId)));
        List<CrmProjectMemberVO> projectLeaders = crmProjectMemberService.getProjectLeaderByQueryParam(query);
        if(CollectionUtils.isNotEmpty(projectLeaders)){
            CrmProjectMemberVO crmProjectMemberVO = projectLeaders.get(0);
            MaterialApplyMainVo materialApplyMainVo = new MaterialApplyMainVo();
            materialApplyMainVo.setSourceType(1);
            materialApplyMainVo.setPersonId(crmProjectMemberVO.getPersonId());
            materialApplyMainVo.setProOrAgreementId(projectId);
            materialApplyMainVo.setProductIds(crmProjectProductOwns.stream().map(CrmProjectProductOwn::getProductId).toList());
            LocalDateTime dd11 = LocalDateTime.now();
            JsonObject<ExistDisableProductVo> listJsonObject = remoteMaterialApplyMainService.existDisableProduct(materialApplyMainVo);
            LocalDateTime dd12 = LocalDateTime.now();
            System.out.println("dd12-dd11=============耗时"+ DateUtil.getMillisDifferenceByDuration(dd11,dd12));
            if(listJsonObject.isSuccess()){
                if(null != listJsonObject.getObjEntity()){
                    // 将二级结构调整成一级结构，一次性填充所有产品的禁用状态
                    List<CrmProjectProductOwn> temp = new ArrayList<>(crmProjectProductOwns);
                    crmProjectProductOwns.forEach(m -> {
                        if(CollectionUtils.isNotEmpty(m.getChildren())){
                            temp.addAll(m.getChildren());
                        }
                    });
                    executeFillDisableState(temp, listJsonObject);
                }
            }else{
                throw new CrmException(listJsonObject.getMessage());
            }
        }else{
            throw new CrmException("查询项目责任人出错");
        }
    }

    private static void executeFillDisableState(List<CrmProjectProductOwn> crmProjectProductOwns, JsonObject<ExistDisableProductVo> listJsonObject) {
        if(CollectionUtils.isNotEmpty(crmProjectProductOwns)){
            crmProjectProductOwns.forEach(m -> {
                long sum = crmProjectProductOwns.stream().filter(n -> n.getStuffCode().equals(m.getStuffCode())).mapToLong(CrmProjectProductOwn::getProductNum).sum();
                List<CrmMaterialDisableVo> disable = listJsonObject.getObjEntity().getDisable();
                List<CrmMaterialDisableVo> control = listJsonObject.getObjEntity().getControl();
                if(CollectionUtils.isNotEmpty(control)){
                    Optional<CrmMaterialDisableVo> first = control.stream().filter(n -> n.getProductId().equals(m.getProductId())).findFirst();
                    if(first.isPresent()){
                        m.setProductStatus(3);
                    }
                }
                if(CollectionUtils.isNotEmpty(disable)){
                    /*AtomicLong count = new AtomicLong(0L);
                    List<MaterialApplyProductDTO> lastProductFlow = listJsonObject.getObjEntity().getLastProductFlow();
                    if(CollectionUtils.isNotEmpty(lastProductFlow)){
                        lastProductFlow.stream().filter(n -> n.getMaterialCode().equals(m.getStuffCode())).findFirst().ifPresent(n -> count.set(n.getNumber()));
                    }*/
                    Optional<CrmMaterialDisableVo> first = disable.stream().filter(n -> n.getProductId().equals(m.getProductId())).findFirst();
                    if(first.isPresent() /*&& count.get() < sum*/){
                        m.setProductStatus(2);
                    }
                }
            });
        }
    }


    /**
     * 公司自有产品填充无通路禁用状态
     * @param projectNo
     * @param currentPersonId
     * @param crmProjectProductOwns
     */
    private void fillProductOwnPathDisable(String projectNo, String currentPersonId, List<CrmProjectProductOwn> crmProjectProductOwns) {
        if(CollectionUtils.isNotEmpty(crmProjectProductOwns)){
            Set<String> recordIds = new HashSet<>();
            crmProjectProductOwns.forEach(m -> {
                recordIds.add(m.getId());
                if(CollectionUtils.isNotEmpty(m.getChildren())){
                    m.getChildren().forEach(n -> recordIds.add(n.getId()));
                }
            });
            // 查询产品通路状态
            Map<String, List<Integer>> accessRulesByProjectQuery = crmProjectProductAccessRulesService.getAccessRulesByProjectQuery(projectNo, currentPersonId, Collections.singletonList(-1), new ArrayList<>(recordIds));
            if (!accessRulesByProjectQuery.isEmpty()) {
                crmProjectProductOwns.forEach(m -> {
                    List<Integer> access = accessRulesByProjectQuery.get(m.getId());
                    if (access != null) {
                        m.setSaleAccess(access);
                        if (access.contains(4)) {
                            m.setProductStatus(2);
                        }
                    }else{
                        m.setSaleAccess(Collections.singletonList(4));
                        m.setProductStatus(2);
                    }
                    if(CollectionUtils.isNotEmpty(m.getChildren())){
                        m.getChildren().forEach(n -> {
                            List<Integer> accessChilren = accessRulesByProjectQuery.get(n.getId());
                            if (accessChilren != null) {
                                n.setSaleAccess(accessChilren);
                                if (accessChilren.contains(4)) {
                                    n.setProductStatus(2);
                                }
                            }else{
                                m.setSaleAccess(Collections.singletonList(4));
                                m.setProductStatus(2);
                            }
                        });
                    }
                });
            }
        }
    }

    /**
     * 批量新增自有产品
     *
     * @param projectProductOwns 自有产品
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<CrmProjectProductOwn> insertCrmProjectProductOwnBatch(List<CrmProjectProductOwn> projectProductOwns) {
        if (CollectionUtils.isNotEmpty(projectProductOwns)) {
            //crmProjectProductOwnService.fillProductPrice(projectProductOwns);
            // 保存自有产品
            crmProjectProductOwnService.saveBatch(projectProductOwns);
            projectProductOwns.forEach(m -> {
                List<CrmProjectProductOwn> crmProjectProductOwnComponent = m.getChildren();
                if (CollectionUtils.isNotEmpty(crmProjectProductOwnComponent)) {
                    for (CrmProjectProductOwn component:crmProjectProductOwnComponent) {
                        component.setId(UUID.randomUUID().toString());
                        component.setParentId(m.getId());
                        List<CrmProjectProductOwnService> subService = component.getCrmProjectProductOwnService();
                        if (CollectionUtils.isNotEmpty(subService)) {
                            subService.forEach(service -> {
                                service.setId(UUID.randomUUID().toString());
                                service.setRecordId(component.getId());
                            });
                            // 保存自有产品安全服务
                            crmProjectProductOwnServiceService.saveBatch(subService);
                        }
                        List<CrmProjectProductOwnServiceRange> serverRange = component.getCrmProjectProductOwnServiceRange();
                        if (CollectionUtils.isNotEmpty(serverRange)) {
                            serverRange.forEach(range -> {
                                range.setId(UUID.randomUUID().toString());
                                range.setRecordId(component.getId());
                            });
                            Optional<CrmProjectProductOwnServiceRange> any = serverRange.stream().filter(n -> m.getId().equals(n.getBindProductRecordId())).findAny();
                            if(!any.isPresent()){
                                // 默认保存配件中服务的服务范围默认添加主产品的信息
                                CrmProjectProductOwnServiceRange build = CrmProjectProductOwnServiceRange.builder().id(UUID.randomUUID().toString()).recordId(component.getId()).stuffCode(m.getStuffCode()).bindProductRecordId(m.getId())
                                        .productName(m.getProductName()).pnCode(m.getPnCode()).productNum(m.getProductNum()).build();
                                serverRange.add(build);
                            }
                        }else{
                            serverRange = new ArrayList<>();
                            // 默认保存配件中服务的服务范围默认添加主产品的信息
                            CrmProjectProductOwnServiceRange build = CrmProjectProductOwnServiceRange.builder().id(UUID.randomUUID().toString()).recordId(component.getId()).stuffCode(m.getStuffCode()).bindProductRecordId(m.getId())
                                    .productName(m.getProductName()).pnCode(m.getPnCode()).productNum(m.getProductNum()).build();
                            serverRange.add(build);
                        }
                        crmProjectProductOwnServiceRangeService.saveBatch(serverRange);
                    }
                    // 保存自有产品配件
                    crmProjectProductOwnService.saveBatch(crmProjectProductOwnComponent);
                    for (CrmProjectProductOwn component :crmProjectProductOwnComponent){
                        // 自动填充分销保修服务价格，固定的物料代码（6001002003002）
                        if(component.getStuffCode().equals("6001002003002")){
                            crmProjectProductOwnServiceRangeService.recalculateDistributionPrice(Collections.singletonList(component),component.getCrmProjectProductOwnServiceRange());
                        }
                    }
                }
                List<CrmProjectProductOwnService> crmProjectProductOwnService = m.getCrmProjectProductOwnService();
                if (CollectionUtils.isNotEmpty(crmProjectProductOwnService)) {
                    crmProjectProductOwnService.forEach(service -> {
                        service.setId(UUID.randomUUID().toString());
                        service.setRecordId(m.getId());
                    });
                    // 保存自有产品安全服务
                    crmProjectProductOwnServiceService.saveBatch(crmProjectProductOwnService);
                }
                List<CrmProjectProductOwnServiceRange> crmProjectProductOwnServiceRange = m.getCrmProjectProductOwnServiceRange();
                if (CollectionUtils.isNotEmpty(crmProjectProductOwnServiceRange)) {
                    crmProjectProductOwnServiceRange.forEach(range -> {
                        range.setId(UUID.randomUUID().toString());
                        range.setRecordId(m.getId());
                    });
                    crmProjectProductOwnServiceRangeService.insertCrmProjectProductOwnServiceRangeBatch(crmProjectProductOwnServiceRange);
                    // 自动填充分销保修服务价格，固定的物料代码（6001002003002）
                    if(m.getStuffCode().equals("6001002003002")){
                        crmProjectProductOwnServiceRangeService.recalculateDistributionPrice(Collections.singletonList(m),crmProjectProductOwnServiceRange);
                    }
                }
            });
            Set<String> projectIds =  projectProductOwns.stream().map(CrmProjectProductOwn::getProjectId).collect(Collectors.toSet());
            projectIds.forEach(projectId -> crmProjectProductOwnService.writeRebateCountProject(projectId, false));
        }
        return projectProductOwns;
    }

    /**
     * 批量新增自有产品配件
     *
     * @param projectProductOwnOwnComponents 自有产品配件
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<CrmProjectProductOwn> insertCrmProjectProductOwnComponentsBatch(List<CrmProjectProductOwn> projectProductOwnOwnComponents) {
        if (CollectionUtils.isNotEmpty(projectProductOwnOwnComponents)) {
            //crmProjectProductOwnService.fillProductPrice(projectProductOwnOwnComponents);
            long count = projectProductOwnOwnComponents.stream().filter(m -> StringUtils.isBlank(m.getParentId())).count();
            if(count > 0){
                throw new CrmException("批量新增自有产品配件，主产品不能为空");
            }
            Set<String> parentIds = projectProductOwnOwnComponents.stream().map(CrmProjectProductOwn::getParentId).collect(Collectors.toSet());
            List<CrmProjectProductOwn> crmProjectProductOwns = crmProjectProductOwnService.listByIds(parentIds);
            if(CollectionUtils.isNotEmpty(crmProjectProductOwns)){
                for (CrmProjectProductOwn component:projectProductOwnOwnComponents) {
                    component.setId(UUID.randomUUID().toString());
                    Optional<CrmProjectProductOwn> anyone = crmProjectProductOwns.stream().filter(n -> n.getId().equals(component.getParentId())).findAny();
                    if(anyone.isPresent()){
                        CrmProjectProductOwn m = anyone.get();
                        List<CrmProjectProductOwnService> subService = component.getCrmProjectProductOwnService();
                        if (CollectionUtils.isNotEmpty(subService)) {
                            subService.forEach(service -> {
                                service.setId(UUID.randomUUID().toString());
                                service.setRecordId(component.getId());
                            });
                            // 保存自有产品安全服务
                            crmProjectProductOwnServiceService.saveBatch(subService);
                        }
                        List<CrmProjectProductOwnServiceRange> serverRange = component.getCrmProjectProductOwnServiceRange();
                        if (CollectionUtils.isNotEmpty(serverRange)) {
                            serverRange.forEach(range -> {
                                range.setId(UUID.randomUUID().toString());
                                range.setRecordId(component.getId());
                            });
                            Optional<CrmProjectProductOwnServiceRange> any = serverRange.stream().filter(n -> m.getId().equals(n.getBindProductRecordId())).findAny();
                            if(any.isEmpty()){
                                // 默认保存配件中服务的服务范围默认添加主产品的信息
                                CrmProjectProductOwnServiceRange build = CrmProjectProductOwnServiceRange.builder().id(UUID.randomUUID().toString()).recordId(component.getId()).stuffCode(m.getStuffCode()).bindProductRecordId(m.getId())
                                        .productName(m.getProductName()).pnCode(m.getPnCode()).productNum(m.getProductNum()).build();
                                serverRange.add(build);
                            }
                        }else{
                            serverRange = new ArrayList<>();
                            // 默认保存配件中服务的服务范围默认添加主产品的信息
                            CrmProjectProductOwnServiceRange build = CrmProjectProductOwnServiceRange.builder().id(UUID.randomUUID().toString()).recordId(component.getId()).stuffCode(m.getStuffCode()).bindProductRecordId(m.getId())
                                    .productName(m.getProductName()).pnCode(m.getPnCode()).productNum(m.getProductNum()).build();
                            serverRange.add(build);
                        }
                        crmProjectProductOwnServiceRangeService.saveBatch(serverRange);
                        component.setCrmProjectProductOwnServiceRange(serverRange);
                    }else{
                        throw new CrmException("批量新增自有产品配件，主产品不存在");
                    }
                }
                // 保存自有产品配件
                crmProjectProductOwnService.saveBatch(projectProductOwnOwnComponents);
                for (CrmProjectProductOwn component :projectProductOwnOwnComponents){
                    // 自动填充分销保修服务价格，固定的物料代码（6001002003002）
                    if(component.getStuffCode().equals("6001002003002")){
                        crmProjectProductOwnServiceRangeService.recalculateDistributionPrice(Collections.singletonList(component),component.getCrmProjectProductOwnServiceRange());
                    }
                }
            }else{
                throw new CrmException("批量新增自有产品配件，主产品不能为空");
            }
            Set<String> projectIds =  projectProductOwnOwnComponents.stream().map(CrmProjectProductOwn::getProjectId).collect(Collectors.toSet());
            projectIds.forEach(projectId -> crmProjectProductOwnService.writeRebateCountProject(projectId, false));
        }
        return projectProductOwnOwnComponents;
    }

    /**
     * 根据项目id获取项目自有产品总价
     *
     * @param id 项目id
     * @return 项目自有产品总价
     */
    @Override
    public BigDecimal getTotalPriceOfOwnProduct(String id) {
        BigDecimal totalPrice = BigDecimal.ZERO;
        LambdaQueryWrapper<CrmProjectProductOwn> crmProjectProductOwnQueryWrapper = buildDefaultQueryWrapper()
                .eq(CrmProjectProductOwn::getProjectId, id);
        List<CrmProjectProductOwn> crmProjectProductOwns = crmProjectProductOwnMapper.selectList(crmProjectProductOwnQueryWrapper);
        crmProjectProductOwnService.productOwnPostProcessing(crmProjectProductOwns);
        if (CollectionUtils.isNotEmpty(crmProjectProductOwns)) {
            // 运算
            for (CrmProjectProductOwn crmProjectProductOwn : crmProjectProductOwns) {
                totalPrice = totalPrice.add(crmProjectProductOwn.getDealTotalPrice());
            }
        }
        return totalPrice;
    }

    /**
     * @param productRecordId
     * @return
     * @Description: 产品复制
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public Boolean copyCrmProjectProductOwn(String productRecordId) {
        CrmProjectProductOwn crmProjectProductOwn = crmProjectProductOwnMapper.selectById(productRecordId);
        if (null != crmProjectProductOwn) {
            crmProjectProductOwn.setId(UUID.randomUUID().toString());
            clearBaseEntity(crmProjectProductOwn);
            crmProjectProductOwn.setHostSerialNumber(null);
            if (ProductTypeEnum.ProductTypeProductEnum.isProduct(crmProjectProductOwn.getProductType())) {
                // 查询自有产品配件
                List<CrmProjectProductOwn> productOwnComponents = crmProjectProductOwnService.list(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getParentId, productRecordId));
                crmProjectProductOwnService.productOwnPostProcessing(productOwnComponents);
                if (CollectionUtils.isNotEmpty(productOwnComponents)) {
                    productOwnComponents.forEach(component -> {
                        // 查询自有产品安全服务
                        List<CrmProjectProductOwnService> subServices = crmProjectProductOwnServiceService.list(new LambdaQueryWrapper<CrmProjectProductOwnService>().eq(CrmProjectProductOwnService::getRecordId, component.getId()));
                        if (CollectionUtils.isNotEmpty(subServices)) {
                            subServices.forEach(service -> {
                                service.setId(null);
                                service.setRecordId(null);
                                clearBaseEntity(service);
                            });
                            component.setCrmProjectProductOwnService(subServices);
                        }
                        // 查询分销保修服务安全服务范围
                        // 自动填充分销保修服务价格，固定的物料代码（6001002003002）
                        if(component.getStuffCode().equals("6001002003002")){
                            List<CrmProjectProductOwnServiceRange> serverRanges = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().eq(CrmProjectProductOwnServiceRange::getRecordId, component.getId()));
                            if (CollectionUtils.isNotEmpty(serverRanges)) {
                                serverRanges.forEach(range -> {
                                    range.setId(null);
                                    range.setRecordId(null);
                                    clearBaseEntity(range);
                                    // 替换绑定的ProductRecordId
                                    if(productRecordId.equals(range.getBindProductRecordId())){
                                        range.setBindProductRecordId(crmProjectProductOwn.getId());
                                    }
                                });
                                component.setCrmProjectProductOwnServiceRange(serverRanges);
                            }
                        }
                        component.setId(null);
                        component.setParentId(null);
                        clearBaseEntity(component);
                        component.setHostSerialNumber(null);
                    });
                    crmProjectProductOwn.setChildren(productOwnComponents);
                }
            }

            if (ProductTypeEnum.ProductTypeProductEnum.isService(crmProjectProductOwn.getProductType())) {
                // 查询自有产品安全服务
                List<CrmProjectProductOwnService> productOwnServices = crmProjectProductOwnServiceService.list(new LambdaQueryWrapper<CrmProjectProductOwnService>().eq(CrmProjectProductOwnService::getRecordId, productRecordId));
                if (CollectionUtils.isNotEmpty(productOwnServices)) {
                    productOwnServices.forEach(service -> {
                        service.setId(null);
                        service.setRecordId(null);
                        clearBaseEntity(service);
                    });
                    crmProjectProductOwn.setCrmProjectProductOwnService(productOwnServices);
                }
                // 查询分销保修服务安全服务范围
                // 自动填充分销保修服务价格，固定的物料代码（6001002003002）
                if(crmProjectProductOwn.getStuffCode().equals("6001002003002")){
                    List<CrmProjectProductOwnServiceRange> productOwnServiceRanges = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().eq(CrmProjectProductOwnServiceRange::getRecordId, productRecordId));
                    if (CollectionUtils.isNotEmpty(productOwnServiceRanges)) {
                        productOwnServiceRanges.forEach(range -> {
                            range.setId(null);
                            range.setRecordId(null);
                            clearBaseEntity(range);
                        });
                        crmProjectProductOwn.setCrmProjectProductOwnServiceRange(productOwnServiceRanges);
                    }
                }
            }
            crmProjectProductOwnService.insertCrmProjectProductOwnBatch(Collections.singletonList(crmProjectProductOwn));
            // 复制运维数据信息
            CrmProjectProductMaintain one = crmProjectProductMaintainService.getOne(new LambdaQueryWrapper<CrmProjectProductMaintain>().eq(CrmProjectProductMaintain::getRecordId, productRecordId));
            if (null != one) {
                List<CrmProjectProductMaintainInvestment> list = crmProjectProductMaintainInvestmentService.list(new LambdaQueryWrapper<CrmProjectProductMaintainInvestment>().eq(CrmProjectProductMaintainInvestment::getMaintainId, one.getId()));

                one.setId(UUID.randomUUID().toString());
                one.setRecordId(crmProjectProductOwn.getId());
                clearBaseEntity(one);
                if(CollectionUtils.isNotEmpty(list)){
                    list.forEach(item->{
                        item.setId(UUID.randomUUID().toString());
                        item.setMaintainId(one.getId());
                        clearBaseEntity(item);
                    });
                    one.setCrmProjectProductMaintainInvestment(list);
                }
                crmProjectProductMaintainService.saveOrUpdateProductMaintain(one);
            }
        }
        return true;
    }

    private void clearBaseEntity(BaseEntity baseEntity) {
        baseEntity.setCreateTime(null);
        baseEntity.setCreateUser(null);
        baseEntity.setUpdateTime(null);
        baseEntity.setUpdateUser(null);
    }

    /**
     * @param productRecordId
     * @param serialNumber
     * @return
     * @Description: 绑定宿主设备序列号
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public Boolean bindingDeviceSerialNumber(String productRecordId, String serialNumber) {
        // 需要校验设备序列号是否为真实存在的序列号
        JsonObject<List<String>> notExistSns = remoteProductService.getNotExistSns(Collections.singletonList(serialNumber));
        if (notExistSns.isSuccess() && CollectionUtils.isEmpty(notExistSns.getObjEntity())) {
            return crmProjectProductOwnService.lambdaUpdate().set(CrmProjectProductOwn::getHostSerialNumber, serialNumber).eq(CrmProjectProductOwn::getId, productRecordId).update();
        } else {
            throw new CrmException("序列号不存在");
        }
    }

    /**
     * @return
     * @Description: 获取项目产品信息（只查询产品）
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public List<CrmProjectProductOwn> queryProjectProductOwnSimple(String projectId, String productRecordId) {
        List<CrmProjectProductOwnServiceRange> hasBinding = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().eq(CrmProjectProductOwnServiceRange::getRecordId, productRecordId));
        List<String> bindProductRecordIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hasBinding)) {
            bindProductRecordIds = hasBinding.stream().map(CrmProjectProductOwnServiceRange::getBindProductRecordId).collect(Collectors.toList());
        }
        List<CrmProjectProductOwn> list = crmProjectProductOwnService.list(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId).notIn(CollectionUtils.isNotEmpty(bindProductRecordIds), CrmProjectProductOwn::getId, bindProductRecordIds));
        crmProjectProductOwnService.productOwnPostProcessing(list);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().filter(m -> ProductTypeEnum.ProductTypeProductEnum.isProduct(m.getProductType())).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * @param projectId
     * @return
     * @Description: 获取项目产品保存数据库的数据
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public List<CrmProjectProductOwn> queryProjectProductOwnStore(String projectId) {
        if (StringUtils.isNotBlank(projectId)) {
            LambdaQueryWrapper<CrmProjectProductOwn> crmProjectProductOwnQueryWrapper = buildDefaultQueryWrapper()
                    .eq(CrmProjectProductOwn::getProjectId, projectId)
                    .orderByDesc(CrmProjectProductOwn::getCreateTime, CrmProjectProductOwn::getId);
            List<CrmProjectProductOwn> allCrmProjectProductOwns = crmProjectProductOwnMapper.selectList(crmProjectProductOwnQueryWrapper);
            crmProjectProductOwnService.productOwnPostProcessing(allCrmProjectProductOwns);
            if(CollectionUtils.isNotEmpty(allCrmProjectProductOwns)){
                List<String> recordIds = allCrmProjectProductOwns.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList());
                // 查询自有产品所有的子服务
                List<CrmProjectProductOwnService> allSubServices = crmProjectProductOwnServiceService.list(new LambdaQueryWrapper<CrmProjectProductOwnService>().in(CrmProjectProductOwnService::getRecordId, recordIds));
                // 查询自有产品服务所有的服务范围
                List<CrmProjectProductOwnServiceRange> allServerRanges = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getRecordId, recordIds));
                List<CrmProjectProductOwn> crmProjectProductOwns = allCrmProjectProductOwns.stream().filter(m -> "0".equals(m.getParentId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(crmProjectProductOwns)) {
                    List<CrmProjectProductOwn> productOwns = crmProjectProductOwns.stream().filter(m -> ProductTypeEnum.ProductTypeProductEnum.isProduct(m.getProductType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(productOwns)) {
                        List<String> productRecordIds =  productOwns.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList());
                        productOwns.forEach(m -> {
                            // 查询自有产品配件
                            List<CrmProjectProductOwn> productOwnComponents = allCrmProjectProductOwns.stream().filter(n -> productRecordIds.contains(n.getParentId())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(productOwnComponents)) {
                                productOwnComponents.forEach(component -> {
                                    List<CrmProjectProductOwnService> subServices = allSubServices.stream().filter(k -> k.getRecordId().equals(component.getId())).collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(subServices)) {
                                        component.setCrmProjectProductOwnService(subServices);
                                    }
                                    // 查询自有产品服务范围
                                    List<CrmProjectProductOwnServiceRange> foundServiceRanges = allServerRanges.stream().filter(k -> k.getRecordId().equals(component.getId())).collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(foundServiceRanges)) {
                                        component.setCrmProjectProductOwnServiceRange(foundServiceRanges);
                                    }
                                });
                                m.setChildren(productOwnComponents);
                            }
                        });
                    }
                    List<CrmProjectProductOwn> serviceOwns = crmProjectProductOwns.stream().filter(m -> ProductTypeEnum.ProductTypeProductEnum.isService(m.getProductType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(serviceOwns)) {
                        serviceOwns.forEach(m -> {
                            // 查询自有产品安全服务
                            List<CrmProjectProductOwnService> productOwnServices = allSubServices.stream().filter(k -> k.getRecordId().equals(m.getId())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(productOwnServices)) {
                                m.setCrmProjectProductOwnService(productOwnServices);
                            }
                            // 查询自有产品安全服务范围
                            List<CrmProjectProductOwnServiceRange> foundServiceRanges = allServerRanges.stream().filter(n -> n.getRecordId().equals(m.getId())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(foundServiceRanges)) {
                                m.setCrmProjectProductOwnServiceRange(foundServiceRanges);
                            }
                        });
                    }
                }
                return crmProjectProductOwns;
            }
        }
        return new ArrayList<>();
    }

    /**
     * minggang
     * 查询项目是否存在
     * 分销产品行的成交价 < si价格(标准折扣价中的最小值)
     */
    @Override
    public Boolean findExistPriceLTSi(String projectId) {
        //查询项目的自由产品
        List<CrmProjectProductOwn> projectOwns = queryProjectProductOwnStore(projectId);

        //判断产品中是否有 分销产品行的成交价 < si价格(标准折扣价中的最小值)
        for (CrmProjectProductOwn projectOwn : projectOwns) {
            //校验每一条主产品的价格
            JsonObject<CrmProductVo> productEntity = remoteProductService.getProduct(projectOwn.getProductId());
            if (productEntity.isSuccess()) {
                CrmProductVo productVo = productEntity.getObjEntity();
                //判断是否是分销产品
                if (productVo.getAttr() == 2) {
                    //判断产品中是否有 分销产品行的成交价 < si价格(标准折扣价中的最小值)
                    if (projectOwn.getDealPrice().compareTo(productVo.getSellinPrice()) < 0) {
                        return true;
                    }

                    //判断配件中是否有 分销产品行的成交价 < si价格(标准折扣价中的最小值)
                    if (projectOwn.getChildren() != null && projectOwn.getChildren().size() > 0) {
                        for (CrmProjectProductOwn component : projectOwn.getChildren()) {
                            JsonObject<CrmProductVo> componentEntity = remoteProductService.getProduct(component.getProductId());
                            if (componentEntity.isSuccess()) {
                                CrmProductVo componentVo = componentEntity.getObjEntity();
                                //判断是否是分销产品
                                if (componentVo.getAttr() == 2) {
                                    if (component.getDealPrice().compareTo(componentVo.getSellinPrice()) < 0) {
                                        return true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return false;
    }

    /**
     * minggang
     * 查询项目是否存在 非分销产品行的成交价 < 标准折扣价
     * 公式：非分销产品成交价 / 报价单价  < 标准折扣
     */
    @Override
    public Boolean findExistPriceLTProportion(String projectId) {
        //查询项目的自由产品
        List<CrmProjectProductOwn> projectOwns = queryProjectProductOwnStore(projectId);

        //判断产品中是否有 非分销产品行的成交价 < 标准折扣价
        for (CrmProjectProductOwn projectOwn : projectOwns) {
            //校验每一条非分销产品行的成交价 < 标准折扣价
            JsonObject<CrmProductVo> productEntity = remoteProductService.getProduct(projectOwn.getProductId());
            if (productEntity.isSuccess()) {
                CrmProductVo productVo = productEntity.getObjEntity();
                //根据产品ID查询产品所属分类的标准折扣
                /*String categoryId = "";
                if (StringUtils.isNotBlank(productVo.getCategoryId4())) {
                    categoryId = productVo.getCategoryId4();
                }else{
                    if (StringUtils.isNotBlank(productVo.getCategoryId3())){
                        categoryId = productVo.getCategoryId3();
                    }else{
                        if (StringUtils.isNotBlank(productVo.getCategoryId4())){
                            categoryId = productVo.getCategoryId4();
                        }
                    }
                }*/
                BigDecimal discount = crmApprovalDiscountCategoryService.selectByCategoryId(productVo.getBusinessCategoryId());
                //判断是否是非分销产品
                if (productVo.getAttr() != 2) {
                    //判断产品中是否有 非分销产品行的成交价 < 标准折扣价
                    if(projectOwn.getQuotedPrice() != null && projectOwn.getQuotedPrice().compareTo(new BigDecimal(0)) != 0) {
                        BigDecimal divide = projectOwn.getDealPrice().divide(projectOwn.getQuotedPrice(), 2, BigDecimal.ROUND_HALF_UP);
                        if (discount == null || divide.compareTo(discount) < 0) {
                            return true;
                        }
                    }

                    //判断配件中是否有 非分销产品行的成交价 < 标准折扣价
                    if (projectOwn.getChildren() != null && projectOwn.getChildren().size() > 0) {
                        for (CrmProjectProductOwn component : projectOwn.getChildren()) {
                            JsonObject<CrmProductVo> componentEntity = remoteProductService.getProduct(component.getProductId());
                            //根据产品ID查询产品所属分类的标准折扣
                            /*String cId = "";
                            if (StringUtils.isNotBlank(productVo.getCategoryId4())) {
                                cId = productVo.getCategoryId4();
                            }else{
                                if (StringUtils.isNotBlank(productVo.getCategoryId3())){
                                    cId = productVo.getCategoryId3();
                                }else{
                                    if (StringUtils.isNotBlank(productVo.getCategoryId4())){
                                        cId = productVo.getCategoryId4();
                                    }
                                }
                            }*/
                            BigDecimal componentDiscount = crmApprovalDiscountCategoryService.selectByCategoryId(productVo.getBusinessCategoryId());
                            if (componentEntity.isSuccess()) {
                                CrmProductVo componentVo = componentEntity.getObjEntity();
                                //判断是否是分销产品
                                if (componentVo.getAttr() != 2) {
                                    //判断产品中是否有 非分销产品行的成交价 < 标准折扣价
                                    if(component.getQuotedPrice() != null && component.getQuotedPrice().compareTo(new BigDecimal(0)) != 0) {
                                        BigDecimal componentDivide = component.getDealPrice().divide(component.getQuotedPrice(), 2, BigDecimal.ROUND_HALF_UP);
                                        if (discount == null || componentDivide.compareTo(componentDiscount) < 0) {
                                            return true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return false;
    }

    /**
     * 还原产品成交价
     *
     * @param projectId
     */
    @Override
    public Boolean rollBackProductPrice(String projectId) {
        List<CrmProjectProductOwn> crmProjectProductOwns = crmProjectProductOwnService.queryProjectProductOwnStore(projectId);
        if (CollectionUtils.isNotEmpty(crmProjectProductOwns)) {
            crmProjectProductOwns.forEach(crmProjectProductOwn -> {
                crmProjectProductOwn.setDealPrice(crmProjectProductOwn.getQuotedPrice());
                crmProjectProductOwn.setDealTotalPrice(crmProjectProductOwn.getQuotedTotalPrice());
                List<CrmProjectProductOwn> crmProjectProductOwnComponent = crmProjectProductOwn.getChildren();
                if (CollectionUtils.isNotEmpty(crmProjectProductOwnComponent)) {
                    crmProjectProductOwnComponent.forEach(component -> {
                        component.setDealPrice(component.getQuotedPrice());
                        component.setDealTotalPrice(component.getQuotedTotalPrice());
                    });
                }
            });
        }
        return true;
    }

    /**
     * @param projectId
     * @return
     * @Description: 是否有公司自有产品
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public Boolean hasOwnProduct(String projectId) {
        long count = crmProjectProductOwnService.count(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId));
        return count > 0;
    }

    /**
     * @param projectId
     * @param recordIdList
     * @return
     * @Description: 根据产品记录id查询项目自有产品
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public List<CrmProjectProductOwn> productByRecordIds(String projectId, String currentPersonId, List<String> recordIdList) {
        if (StringUtils.isNotBlank(projectId)) {
            BriefInfo briefInfo = crmProjectAgentService.queryInfoByProjectId(projectId);
            LambdaQueryWrapper<CrmProjectProductOwn> crmProjectProductOwnQueryWrapper = new LambdaQueryWrapper<CrmProjectProductOwn>()
                    .eq(CrmProjectProductOwn::getProjectId, projectId)
                    .eq(CrmProjectProductOwn::getParentId, "0")
                    .in(CrmProjectProductOwn::getId, recordIdList);
            List<CrmProjectProductOwn> crmProjectProductOwns = crmProjectProductOwnService.list(crmProjectProductOwnQueryWrapper);
            crmProjectProductOwnService.productOwnPostProcessing(crmProjectProductOwns);
            if (CollectionUtils.isNotEmpty(crmProjectProductOwns)) {
                fillProductOwnExt(crmProjectProductOwns, projectId, currentPersonId, briefInfo.getNumber());
                /*Set<String> recordId = crmProjectProductOwns.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toSet());
                Set<String> productIds =  crmProjectProductOwns.stream().map(CrmProjectProductOwn::getProductId).collect(Collectors.toSet());
                // 公司自有产品填充产品锁定状态
                fillProductOwnLockStatus(projectId, recordId, crmProjectProductOwns);
                // 查询产品的序列号
                List<CrmProjectProductSn> crmProjectProductSns = crmProjectProductSnService.list(new LambdaQueryWrapper<CrmProjectProductSn>().in(CrmProjectProductSn::getRecordId, recordId));
                // 查询产品是否有子服务或者子配件
                JsonObject<Map<String, Boolean>> hasChildren = remoteProductService.batchFindHasChild(new ArrayList<>(productIds));
                // 查询产品的定制开发类标识
                JsonObject<Map<String, List<Integer>>> customizedType = remoteCustomizedProductService.batchGetCustomizedType(new ArrayList<>(productIds));
                crmProjectProductOwns.forEach(m -> {
                    m.setCrmProjectProductSn(Optional.of(crmProjectProductSns.stream().filter(n -> n.getRecordId().equals(m.getId())).collect(Collectors.toList())).orElse(new ArrayList<>()));
                    m.setHasChild(hasChildren.isSuccess() ? hasChildren.getObjEntity().getOrDefault(m.getProductId(), false) : false);
                    m.setCustomizedType(customizedType.isSuccess() ? customizedType.getObjEntity().getOrDefault(m.getProductId(), null) : null);
                });
                List<CrmProjectProductOwn> productOwns = crmProjectProductOwns.stream().filter(m -> ProductTypeEnum.ProductTypeProductEnum.isProduct(m.getProductType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(productOwns)) {
                    List<String> productRecordIds = productOwns.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList());
                    List<CrmProjectProductOwn> productOwnComponents = crmProjectProductOwnService.list(buildDefaultQueryWrapper().in(CrmProjectProductOwn::getParentId, productRecordIds));
                     crmProjectProductOwnService.productOwnPostProcessing(productOwnComponents);
                    productOwns.forEach(m -> {
                        // 查询自有产品配件
                        List<CrmProjectProductOwn> foundComponents = productOwnComponents.stream().filter(n -> n.getParentId().equals(m.getId())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(foundComponents)) {
                            List<String> recordIds = foundComponents.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList());
                            List<CrmProjectProductOwnService> subServices = crmProjectProductOwnServiceService.list(new LambdaQueryWrapper<CrmProjectProductOwnService>().in(CrmProjectProductOwnService::getRecordId, recordIds));
                            List<CrmProjectProductOwnServiceRange> serverRanges = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getRecordId, recordIds));
                            foundComponents.forEach(n -> {
                                // 查询自有产品服务
                                List<CrmProjectProductOwnService> foundServices = subServices.stream().filter(k -> k.getRecordId().equals(n.getId())).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(foundServices)) {
                                    n.setCrmProjectProductOwnService(foundServices);
                                }
                                // 查询自有产品服务范围
                                List<CrmProjectProductOwnServiceRange> foundServiceRanges = serverRanges.stream().filter(k -> k.getRecordId().equals(n.getId())).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(foundServiceRanges)) {
                                    n.setCrmProjectProductOwnServiceRange(foundServiceRanges);
                                }
                                n.setCrmProjectProductSn(Optional.of(crmProjectProductSns.stream().filter(k -> k.getRecordId().equals(n.getId())).collect(Collectors.toList())).orElse(new ArrayList<>()));
                            });
                            m.setChildren(foundComponents);
                        }
                    });
                }
                List<CrmProjectProductOwn> serviceOwns = crmProjectProductOwns.stream().filter(m -> ProductTypeEnum.ProductTypeProductEnum.isService(m.getProductType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(serviceOwns)) {
                    List<String> productRecordIds = serviceOwns.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList());
                    List<CrmProjectProductOwnService> productOwnServices = crmProjectProductOwnServiceService.list(new LambdaQueryWrapper<CrmProjectProductOwnService>().in(CrmProjectProductOwnService::getRecordId, productRecordIds));
                    List<CrmProjectProductOwnServiceRange> productOwnServiceRanges = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getRecordId, productRecordIds));
                    serviceOwns.forEach(m -> {
                        // 查询自有产品安全服务
                        List<CrmProjectProductOwnService> foundServices = productOwnServices.stream().filter(n -> n.getRecordId().equals(m.getId())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(foundServices)) {
                            m.setCrmProjectProductOwnService(foundServices);
                        }
                        // 查询自有产品安全服务范围
                        List<CrmProjectProductOwnServiceRange> foundServiceRanges = productOwnServiceRanges.stream().filter(n -> n.getRecordId().equals(m.getId())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(foundServiceRanges)) {
                            m.setCrmProjectProductOwnServiceRange(foundServiceRanges);
                        }
                        m.setCrmProjectProductSn(Optional.of(crmProjectProductSns.stream().filter(k -> k.getRecordId().equals(m.getId())).collect(Collectors.toList())).orElse(new ArrayList<>()));
                    });
                }
                crmProjectProductMaintainService.fillMaintainData(crmProjectProductOwns);
                if(StringUtils.isNotBlank(currentPersonId)){
                    // 公司自有产品填充无通路禁用状态
                    fillProductOwnPathDisable(briefInfo.getNumber(), currentPersonId, crmProjectProductOwns);
                    // 公司自有产品填充代码禁用状态
                    fillProductOwnCodeDisable(projectId, crmProjectProductOwns);
                }*/
            }
            //projectGrossMargin.calculateProjectProductOwnGrossMargin(crmProjectProductOwns, projectId);
            return crmProjectProductOwns;
        }
        return new ArrayList<>();
    }

    /**
     * @param projectId
     * @param productRecordIds
     * @return
     * @Description: 刷新产品状态, 返回刷新后状态为已禁用的产品的物料代码
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public List<String> refreshProductStatus(String projectId, List<String> productRecordIds) {
        /*if (CollectionUtils.isNotEmpty(productRecordIds)) {
            CrmProjectDirectly byId = crmProjectDirectlyService.getById(projectId);
            if(null != byId){
                List<CrmMaterialDisableVo> disableVoList = new ArrayList<>();
                ProjectMemberQuery query = new ProjectMemberQuery();
                query.setProjectIds(Collections.singleton(projectId));
                List<CrmProjectMemberVO> projectLeaderByQueryParam = crmProjectMemberService.getProjectLeaderByQueryParam(query);
                if(CollectionUtils.isNotEmpty(projectLeaderByQueryParam)){
                    Optional<CrmProjectMemberVO> first = projectLeaderByQueryParam.stream().filter(n -> projectId.equals(n.getProjectId())).findFirst();
                    if(first.isPresent()){
                        String personId = first.get().getPersonId();
                        MaterialApplyMainVo materialApplyMainVo = new MaterialApplyMainVo();
                        materialApplyMainVo.setSourceType(1);
                        materialApplyMainVo.setPersonId(personId);
                        materialApplyMainVo.setProOrAgreementId(projectId);
                        JsonObject<List<CrmMaterialDisableVo>> listJsonObject = remoteMaterialApplyMainService.existDisableProduct(materialApplyMainVo);
                        if(listJsonObject.isSuccess()){
                            disableVoList = listJsonObject.getObjEntity();
                        }else{
                            throw new CrmException(listJsonObject.getMessage());
                        }
                    }else{
                        throw new CrmException("项目负责人不存在");
                    }
                }
                List<CrmProjectProductOwn> crmProjectProductOwns = crmProjectProductOwnService.list(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId).in(CrmProjectProductOwn::getId, productRecordIds));
                crmProjectProductOwnService.productOwnPostProcessing(crmProjectProductOwns);
                if (CollectionUtils.isNotEmpty(crmProjectProductOwns) && CollectionUtils.isNotEmpty(disableVoList)) {
                    // 查询代码禁用导致的产品禁用
                    List<String> disableStuffCodes = new ArrayList<>();
                    List<CrmMaterialDisableVo> finalDisableVoList = disableVoList;
                    crmProjectProductOwns.stream().forEach(m -> {
                        Optional<CrmMaterialDisableVo> first = finalDisableVoList.stream().filter(n -> n.getProductId().equals(m.getProductId())).findFirst();
                        if(first.isPresent()){
                            disableStuffCodes.add(first.get().getMaterialCode());
                            // 根据返回的产品状态更新项目产品信息，同时返回状态为已禁用的产品物料代码
                            crmProjectProductOwnService.lambdaUpdate().set(CrmProjectProductOwn::getProductStatus,2).eq(CrmProjectProductOwn::getId,m.getId()).update();
                        }
                    });
                    return disableStuffCodes;
                }
            }
        }*/
        return new ArrayList<>();
    }

    /**
     * @param
     * @param projectId
     * @param productRecordIds
     * @return
     * @Description: 计算自有产品的毛利信息
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public CrmProjectProductOwn productGrossMarginOfOwn(String projectId, String productRecordIds) {
        List<CrmProjectProductOwn> one = crmProjectProductOwnService.list(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId).eq(CrmProjectProductOwn::getId, productRecordIds));
        crmProjectProductOwnService.productOwnPostProcessing(one);
        if(CollectionUtils.isNotEmpty(one)){
            crmProjectProductMaintainService.fillMaintainData(one);
            projectGrossMargin.calculateProjectProductOwnGrossMargin(one,projectId);
            return one.get(0);
        }
        return null;
    }

    /**
     * @param projectId
     * @param leaseDeviceStuffCode
     * @return
     * @Description: 删除自有产品租赁设备
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public Boolean deleteLeaseDevice(String projectId, String leaseDeviceStuffCode) {
        return crmProjectProductOwnService.remove(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId).eq(CrmProjectProductOwn::getStuffCode, leaseDeviceStuffCode));
    }

    /**
     * @param params
     * @return
     * @Description: 分页查询项目自有产品平铺数据
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public PageUtils<CrmProjectProductOwn> tiledPage(ProjectProductOwnPageQuery params) {
        String projectId = params.getProjectId();
        String commonSearch = params.getCommonSearch();
        List<String> productType = params.getProductType();
        if (StringUtils.isNotBlank(projectId) && CollectionUtils.isNotEmpty(productType)) {
            LambdaQueryWrapper<CrmProjectProductOwn> crmProjectProductOwnQueryWrapper = buildDefaultQueryWrapper()
                    .eq(CrmProjectProductOwn::getProjectId, projectId)
                    .in(CrmProjectProductOwn::getProductType, productType)
                    .like(StringUtils.isNotBlank(commonSearch), CrmProjectProductOwn::getProductName, commonSearch)
                    .like(StringUtils.isNotBlank(commonSearch), CrmProjectProductOwn::getStuffCode, commonSearch)
                    .ne(CrmProjectProductOwn::getRemark, "租赁设备") // 不显示租赁产品
                    .orderByDesc(CollectionUtils.isEmpty(params.getOrders()), CrmProjectProductOwn::getCreateTime, CrmProjectProductOwn::getId);
            Page<CrmProjectProductOwn> page = crmProjectProductOwnService.page(new Query<CrmProjectProductOwn>().getPage(params), crmProjectProductOwnQueryWrapper);
            crmProjectProductOwnService.productOwnPostProcessing(page.getRecords());
            return new PageUtils<>(page);
        }
        return new PageUtils<>();
    }

    /**
     * @param projectId
     * @param recordIds
     * @return
     * @Description: 释放组件
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public Boolean releaseComponent(String projectId, List<String> recordIds) {
        List<ProductTreeNode> productTreeNodes = new ArrayList<>();
        List<CrmProjectProductOwn> list = crmProjectProductOwnService.list(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId));
        crmProjectProductOwnService.productOwnPostProcessing(list);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(m -> {
                if (m.getParentId().equals("0")) {
                    List<ProductTreeNode> children = new ArrayList<>();
                    List<CrmProjectProductOwn> componentOwns =  list.stream().filter(n -> m.getId().equals(n.getParentId())).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(componentOwns)){
                        componentOwns.forEach(n -> {
                            children.add(ProductTreeNode.builder().id(n.getId()).selected(recordIds.contains(n.getId())).build());
                        });
                    }
                    productTreeNodes.add(ProductTreeNode.builder().id(m.getId()).selected(recordIds.contains(m.getId())).children(children).build());
                }
            });
        }
        if(CollectionUtils.isNotEmpty(productTreeNodes)){
            List<String> releaseComponentIds = new ArrayList<>();
            productTreeNodes.stream().filter(m -> CollectionUtils.isNotEmpty(m.getChildren())).forEach(m -> {
                List<ProductTreeNode> children = m.getChildren();
                if(m.getSelected()){
                    // 父节点选中，释放子节点中未选中的配件
                    releaseComponentIds.addAll(children.stream().filter(n -> !n.getSelected()).map(ProductTreeNode::getId).collect(Collectors.toList()));
                }else{
                    // 父节点未选中，释放子节点中选中的配件
                    releaseComponentIds.addAll(children.stream().filter(ProductTreeNode::getSelected).map(ProductTreeNode::getId).collect(Collectors.toList()));
                }
            });
            if(CollectionUtils.isNotEmpty(releaseComponentIds)){
                return crmProjectProductOwnService.lambdaUpdate().set(CrmProjectProductOwn::getParentId, "0").eq(CrmProjectProductOwn::getProjectId, projectId).in(CrmProjectProductOwn::getId, releaseComponentIds).update();
            }
        }
        return true;
    }

    /**
     * @param updateData
     * @return
     * @Description: 写入回写数据
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean writeBack(List<CrmProjectProductOwnUpdateVO> updateData) {
        if(CollectionUtils.isNotEmpty(updateData)){
            for (CrmProjectProductOwnUpdateVO m: updateData) {
                String recordId = m.getId();
                if(StringUtils.isNotBlank(recordId)){
                    BigDecimal taxRate = m.getTaxRate();
                    Integer productPeriod = m.getProductPeriod();
                    String supplier = m.getSupplier();
                    Integer reportStatus = m.getReportStatus();
                    String stuffCode = m.getStuffCode();
                    Integer inspection = m.getInspection();
                    Boolean specialPreparation = m.getSpecialPreparation();
                    crmProjectProductOwnService.lambdaUpdate().set(null != taxRate,CrmProjectProductOwn::getTaxRate, taxRate)
                            .set(null != productPeriod,CrmProjectProductOwn::getProductPeriod, productPeriod)
                            .set(StringUtils.isNotBlank(supplier),CrmProjectProductOwn::getSupplier, supplier)
                            .set(null != reportStatus,CrmProjectProductOwn::getReportStatus, reportStatus)
                            .set(StringUtils.isNotBlank(stuffCode),CrmProjectProductOwn::getStuffCode, stuffCode)
                            .set(null != inspection,CrmProjectProductOwn::getInspection, inspection)
                            .set(null != specialPreparation,CrmProjectProductOwn::getSpecialPreparation, specialPreparation)
                            .eq(CrmProjectProductOwn::getId, recordId).update();
                }
            }
        }
        return true;
    }

    /**
     * @param ownProjects
     * @return
     * @Description: 获取合同中关联的项目自有产品
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public List<CrmProjectProductOwn> getProjectProductOwns(List<CrmProjectProductOwnVO> ownProjects) {
        if(CollectionUtils.isNotEmpty(ownProjects)){
            List<String> collect = ownProjects.stream().map(CrmProjectProductOwnVO::getId).collect(Collectors.toList());
            List<CrmProjectProductOwn> list = crmProjectProductOwnService.list(buildDefaultQueryWrapper().in(CrmProjectProductOwn::getId, collect));
            crmProjectProductOwnService.productOwnPostProcessing(list);
            return list;
        }
        return new ArrayList<>();
    }

    /**
     * @param projectId
     * @return
     * @Description: 项目产品中是否有0金额续保产品【项目-自有产品】
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public Boolean hasZeroInsurance(String projectId) {
        return crmProjectProductOwnService.count(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId).eq(CrmProjectProductOwn::getStuffCode, "6001002003002")) > 0;
    }

    /**
     * @param startDate
     * @param endDate
     * @return
     * @Description: 查询时间范围内所有变动的数据
     * @author: leo
     * @date: 2024-05-28 13:56
     */
    @Override
    public List<CrmProjectProductOwn> queryAllUpdateData(LocalDateTime startDate, LocalDateTime endDate) {
        return baseMapper.queryAllUpdateData(startDate, endDate);
    }

    /**
     * 清空产品维修数据
     *
     * @param productRecordIds
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean clearProductMaintain(List<String> productRecordIds) {
        List<CrmProjectProductMaintain> list = crmProjectProductMaintainService.list(new LambdaQueryWrapper<CrmProjectProductMaintain>().in(CrmProjectProductMaintain::getRecordId, productRecordIds));
        if(CollectionUtils.isNotEmpty(list)){
            List<String> maintainIds = list.stream().map(CrmProjectProductMaintain::getId).toList();
            crmProjectProductMaintainInvestmentService.remove(new LambdaQueryWrapper<CrmProjectProductMaintainInvestment>().in(CrmProjectProductMaintainInvestment::getMaintainId, maintainIds));
        }
        return crmProjectProductMaintainService.remove(new LambdaQueryWrapper<CrmProjectProductMaintain>().in(CrmProjectProductMaintain::getRecordId, productRecordIds));
    }

    /**
     * 是否有分销产品
     *
     * @param projectId
     * @return
     */
    @Override
    public Boolean hasFxProduct(String projectId) {
        return crmProjectProductOwnService.count(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId).eq(CrmProjectProductOwn::getAttr, 2)) > 0;
    }

    /**
     * 是否有未编辑的前端产品
     *
     * @param projectId
     * @return
     */
    @Override
    public List<String> hasNoFrontEdit(String projectId) {
        List<CrmProjectProductOwn> own = crmProjectProductOwnService.list(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId));
        crmProjectProductOwnService.productOwnPostProcessing(own);
        if(CollectionUtils.isNotEmpty(own)){
            List<String> list = own.stream().filter(m -> !m.getFrontEdit()).map(CrmProjectProductOwn::getStuffCode).toList();
            if(CollectionUtils.isNotEmpty(list)){
                return list;
            }
        }
        List<CrmProjectProductThird> third = crmProjectProductThirdService.list(crmProjectProductThirdService.buildDefaultQueryWrapper().eq(CrmProjectProductThird::getProjectId, projectId));
        crmProjectProductThirdService.productThirdPostProcessing(third);
        if(CollectionUtils.isNotEmpty(third)){
            List<String> list = third.stream().filter(m-> !m.getFrontEdit()).map(CrmProjectProductThird::getStuffCode).toList();
            if(CollectionUtils.isNotEmpty(list)){
                return list;
            }
        }
        return List.of();
    }

    /**
     * 写入项目使用返点额度
     * 注意，该接口在项目删除和项目失败进行前置调用，其他新增和修改进行后置调用
     * @param projectId
     * @return
     */
    @Override
    public boolean writeRebateCountProject(String projectId, boolean isDelete) {
        BriefInfo briefInfo = crmProjectAgentService.queryInfoByProjectId(projectId);
        if(null != briefInfo){
            // 排除公司项目
            if(1 == briefInfo.getType()){
                return false;
            }
            // 排除渠道项目直签以及下单中样机场景
            if(3 == briefInfo.getType()){
                CrmProjectAgent byId = crmProjectAgentService.getById(briefInfo.getId());
                if(byId.getSigningType() == 0 || (byId.getSigningType() == 1 && byId.getPrototype() == 1)){
                    return false;
                }
            }
            CrmAgentRebateProjectInput crmAgentRebateProject = new CrmAgentRebateProjectInput();
            crmAgentRebateProject.setOutAgentId(briefInfo.getAgentId());
            crmAgentRebateProject.setProjectNo(briefInfo.getNumber());
            crmAgentRebateProject.setProjectType(2 == briefInfo.getType() ? AgentRebateEnum.ProjectTypeEnum.ZD.getCode() : AgentRebateEnum.ProjectTypeEnum.QD.getCode());
            if(isDelete){
                crmAgentRebateProject.setProjectRebate(BigDecimal.ZERO);
                crmAgentRebateProject.setOperationType(AgentRebateEnum.OperationTypeEnum.DELETE.getCode());
            }else{
                BigDecimal projectRebate = BigDecimal.ZERO;
                List<CrmProjectProductOwn> list = crmProjectProductOwnService.list(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId));
                crmProjectProductOwnService.productOwnPostProcessing(list);
                if(CollectionUtils.isNotEmpty(list)){
                    double sum = list.stream().filter(m -> null != m.getRebatePrice()).mapToDouble(m -> m.getRebatePrice().multiply(BigDecimal.valueOf(m.getProductNum())).doubleValue()).sum();
                    projectRebate = BigDecimal.valueOf(sum).setScale(2, RoundingMode.HALF_UP);
                }
                crmAgentRebateProject.setProjectRebate(projectRebate);
                crmAgentRebateProject.setOperationType(AgentRebateEnum.OperationTypeEnum.UPDATE.getCode());

            }
            JsonObject<Boolean> booleanJsonObject = remoteAgentRebateService.rebateCountProject(crmAgentRebateProject);
            if(booleanJsonObject.isSuccess()){
                return booleanJsonObject.getObjEntity();
            }else{
                throw new CrmException("写入项目使用返点额度失败");
            }
        }
        return false;
    }

    /**
     * 获取项目产品平铺信息（支持公司项目，国代项目和渠道项目）
     *
     * @param projectId
     * @param productRecordIds
     * @return
     */
    @Override
    public List<CrmProjectProductOwn> queryProjectProductOwnTiled(String projectId, List<String> productRecordIds) {
        List<CrmProjectProductOwn> list = crmProjectProductOwnService.list(buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId).in(CollectionUtils.isNotEmpty(productRecordIds), CrmProjectProductOwn::getId, productRecordIds));
        crmProjectProductOwnService.productOwnPostProcessing(list);
        crmProjectProductMaintainService.fillMaintainData(list);
        return list;
    }

    /**
     * 退换货
     *
     * @param returnBackData
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean returnBack(CrmProjectProductOwnReturnVO returnBackData) {
        List<ProductReturnData> returnData = returnBackData.getReturnData();
        if(CollectionUtils.isNotEmpty(returnData)){
            List<String> psns = new ArrayList<>();
            for (ProductReturnData productReturnData : returnData){
                if(CollectionUtils.isNotEmpty(productReturnData.getSns())){
                    psns.addAll(productReturnData.getSns());
                }
                crmProjectProductOwnService.lambdaUpdate()
                        .eq(CrmProjectProductOwn::getId, productReturnData.getProductRecordId())
                        .set(CrmProjectProductOwn::getReturnNum, productReturnData.getReturnNum())
                        .update();
            }
            // 产品退换货以后解除项目中的序列号绑定关系
            if(CollectionUtils.isNotEmpty(psns)){
                crmProjectProductSnService.remove(new LambdaQueryWrapper<CrmProjectProductSn>().in(CrmProjectProductSn::getPsn, psns));
            }
        }
        List<CrmProjectProductOwnVO> newProduct = returnBackData.getNewProduct();
        if(CollectionUtils.isNotEmpty(newProduct)){
            List<CrmProjectProductOwn> crmProjectProductOwns = HyperBeanUtils.copyListPropertiesByJackson(newProduct, CrmProjectProductOwn.class);
            crmProjectProductOwnService.saveBatch(crmProjectProductOwns);
            crmProjectProductOwns.forEach(m -> {
                List<CrmProjectProductOwnService> productOwnService = m.getCrmProjectProductOwnService();
                if(CollectionUtils.isNotEmpty(productOwnService)){
                    productOwnService.forEach(service -> {
                        service.setId(UUID.randomUUID().toString());
                        service.setRecordId(m.getId());
                    });
                    crmProjectProductOwnServiceService.saveBatch(productOwnService);
                }
                List<CrmProjectProductOwnServiceRange> crmProjectProductOwnServiceRange = m.getCrmProjectProductOwnServiceRange();
                if(CollectionUtils.isNotEmpty(crmProjectProductOwnServiceRange)){
                    crmProjectProductOwnServiceRange.forEach(range -> {
                        range.setId(UUID.randomUUID().toString());
                        range.setRecordId(m.getId());
                    });
                    crmProjectProductOwnServiceRangeService.insertCrmProjectProductOwnServiceRangeBatch(crmProjectProductOwnServiceRange);
                    // 自动填充分销保修服务价格，固定的物料代码（6001002003002）
                    if(m.getStuffCode().equals("6001002003002")){
                        crmProjectProductOwnServiceRangeService.recalculateDistributionPrice(Collections.singletonList(m),crmProjectProductOwnServiceRange);
                    }
                }
            });

        }
        List<CrmProjectProductSnVO> newProductPsn = returnBackData.getNewProductPsn();
        if(CollectionUtils.isNotEmpty(newProductPsn)){
            List<CrmProjectProductSn> crmProjectProductSns = HyperBeanUtils.copyListPropertiesByJackson(newProductPsn, CrmProjectProductSn.class);
            crmProjectProductSnService.saveBatch(crmProjectProductSns);
        }
        return true;
    }

    /**
     * 取消退换货
     *
     * @param cancelReturnBackData
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelReturnBack(CrmProjectProductOwnCancelReturnVO cancelReturnBackData) {
        List<ProductCancelReturnData> cancelReturnData = cancelReturnBackData.getCancelReturnData();
        if(CollectionUtils.isNotEmpty(cancelReturnData)){
            List<CrmProjectProductSnVO> psns = new ArrayList<>();
            for (ProductCancelReturnData productCancelReturnData : cancelReturnData){
                if(CollectionUtils.isNotEmpty(productCancelReturnData.getSns())){
                    psns.addAll(productCancelReturnData.getSns());
                }
                crmProjectProductOwnService.lambdaUpdate()
                        .set(CrmProjectProductOwn::getReturnNum, productCancelReturnData.getReturnNum())
                        .eq(CrmProjectProductOwn::getId, productCancelReturnData.getProductRecordId())
                        .update();
            }
            if(CollectionUtils.isNotEmpty(psns)){
                List<CrmProjectProductSn> crmProjectProductSns = HyperBeanUtils.copyListPropertiesByJackson(psns, CrmProjectProductSn.class);
                crmProjectProductSnService.saveBatch(crmProjectProductSns);
            }
        }
        List<String> deleteProductRecordIds = cancelReturnBackData.getDeleteProductRecordIds();
        if(CollectionUtils.isNotEmpty(deleteProductRecordIds)){
            CrmProjectProductOwnMapper ownMapper = SpringUtil.getBean(CrmProjectProductOwnMapper.class);
            ownMapper.hardDeleteByProcessInstanceId(deleteProductRecordIds);
//            crmProjectProductOwnService.removeByIds(deleteProductRecordIds);
            crmProjectProductSnService.remove(new LambdaQueryWrapper<CrmProjectProductSn>().in(CrmProjectProductSn::getRecordId, deleteProductRecordIds));
        }
        return true;
    }

    /**
     * 查询产品行对应的专项备货数量
     *
     * @param productRecordId
     * @param projectId
     * @return
     */
    @Override
    public Long querySpecialPreparationCount(String productRecordId, String projectId) {
        // 根据行ID查询专项备货数量
        JsonObject<Map<String, Long>> stockUpQuantity = remoteTargetedInventoryPreparationService.getStockUpQuantity(Collections.singletonList(productRecordId));
        if (stockUpQuantity.isSuccess()){
            return stockUpQuantity.getObjEntity().getOrDefault(productRecordId,  0L);
        }else{
            throw new CrmException(stockUpQuantity.getMessage());
        }
    }

    /**
     * 获取当前用户的天智报价系统报价单数据
     *
     * @param quoteBillPageQuery
     * @return
     */
    @Override
    public PageUtils<QuoteBill> allQuotesPageOfCurrentPerson(QuoteBillPageQuery quoteBillPageQuery) {
        String personId = quoteBillPageQuery.getPersonId();
        if(StringUtils.isNotBlank(personId)){
            Map<String, Object> params = new HashMap<>();
            params.put("personId", personId);
            params.put("pageNum", quoteBillPageQuery.getPageNum());
            params.put("pageSize", quoteBillPageQuery.getPageSize());
            JsonObject<PageUtils<QuoteBill>> pageUtilsJsonObject = tqmServiceClient.allQuotes(params);
            if(pageUtilsJsonObject.isSuccess()){
                return pageUtilsJsonObject.getObjEntity();
            }
        }
        return new PageUtils<>();
    }

    /**
     * 导入天智报价系统报价单产品数据
     *
     * @param quoteNo
     * @return
     */
    @Override
    public Boolean inputQuotesBillByQuoteNo(String quoteNo, String projectId) {
        JsonObject<List<ProjectJson>> quotesByQuoteNo = tqmServiceClient.getQuotesByQuoteNo(quoteNo);
        if(quotesByQuoteNo.isSuccess()){
            List<ProjectJson> projectJson = quotesByQuoteNo.getObjEntity();
            if(CollectionUtils.isNotEmpty(projectJson)){
                // 构建公司项目产品提交结构
                Set<String> productStuffCodes = new HashSet<>();
                projectJson.forEach(m->{
                    productStuffCodes.add(m.getCode());
                    if(CollectionUtils.isNotEmpty(m.getComponents())){
                        productStuffCodes.addAll(m.getComponents().stream().map(ProductComponent::getScode).collect(Collectors.toSet()));
                    }
                    if(CollectionUtils.isNotEmpty(m.getService())){
                        productStuffCodes.addAll(m.getService().stream().map(Special::getProductcode).collect(Collectors.toSet()));
                    }
                    if(CollectionUtils.isNotEmpty(m.getSubServers())){
                        productStuffCodes.addAll(m.getSubServers().stream().map(SubServer::getCode).collect(Collectors.toSet()));
                    }
                });
                // 根据物料代码查询产品信息
                JsonObject<List<CrmProductVo>> listJsonObject = remoteProductService.batchGetInfoByMaterialCode(new ArrayList<>(productStuffCodes));
                if(listJsonObject.isSuccess() && CollectionUtils.isNotEmpty(listJsonObject.getObjEntity())){
                    List<CrmProjectProductOwn> projectProductOwns = buildProjectProductOwns(projectJson, listJsonObject.getObjEntity(), projectId);
                    crmProjectProductOwnService.insertCrmProjectProductOwnBatch(projectProductOwns);
                    return true;
                }else{
                    throw new CrmException("导入报价单产品失败");
                }
            }
        }
        return false;
    }

    private List<CrmProjectProductOwn> buildProjectProductOwns(List<ProjectJson> projectJson, List<CrmProductVo> productDataList, String projectId) {
        List<CrmProjectProductOwn> projectProductOwns = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(projectJson) && CollectionUtils.isNotEmpty(productDataList)){
            projectJson.forEach(m -> {
                CrmProjectProductOwn projectProductOwn = new CrmProjectProductOwn();
                projectProductOwn.setId(UUID.randomUUID().toString());
                projectProductOwn.setParentId("0");
                projectProductOwn.setProjectId(projectId);
                // 构建产品信息
                Optional<CrmProductVo> first = productDataList.stream().filter(n -> m.getCode().equals(n.getMaterialCode())).findFirst();
                if(first.isPresent()){
                    projectProductOwn.setProductId(first.get().getId());
                    projectProductOwn.setProductLic(0L);
                    projectProductOwn.setProductNum(Long.valueOf(m.getCartNumber()));
                    projectProductOwn.setTempProductNum(Long.valueOf(m.getCartNumber()));
                    projectProductOwn.setQuotedPrice(first.get().getPrice());
                    projectProductOwn.setQuotedTotalPrice(first.get().getPrice().multiply(BigDecimal.valueOf(m.getCartNumber())));
                    projectProductOwn.setAttr(first.get().getAttr());
                    projectProductOwn.setProductType(first.get().getForm().toString());
                    projectProductOwn.setProductSpecification(first.get().getUnit());
                    projectProductOwn.setStuffCode(first.get().getMaterialCode());
                    projectProductOwn.setProductName(first.get().getName());
                    projectProductOwn.setPnCode(first.get().getPn());
                    projectProductOwn.setProductCategory(first.get().getSpecificationId());
                    projectProductOwn.setDealPrice(BigDecimal.valueOf(m.getPrice()));
                    projectProductOwn.setDealTotalPrice(BigDecimal.valueOf(m.getPrice()).multiply(BigDecimal.valueOf(m.getCartNumber())));
                    projectProductOwn.setRebatePrice(BigDecimal.ZERO);
                    projectProductOwn.setFinalPrice(BigDecimal.valueOf(m.getPrice()));
                    projectProductOwn.setFinalTotalPrice(BigDecimal.valueOf(m.getPrice()).multiply(BigDecimal.valueOf(m.getCartNumber())));
                    projectProductOwn.setTaxRate(new BigDecimal("0.09"));
                    projectProductOwn.setProductPeriod(new BigDecimal(24));
                    projectProductOwn.setState(0);
                    projectProductOwn.setRemark("天智报价单导入");
                    projectProductOwn.setProductLine1(first.get().getProductLine1());
                    List<CrmProjectProductOwn> productOwnComponents = new ArrayList<>();
                    // 构建配件信息
                    List<ProductComponent> components = m.getComponents();
                    if(CollectionUtils.isNotEmpty(components)){
                        for (ProductComponent component : components) {
                            // 构建产品信息
                            Optional<CrmProductVo> firstComponent = productDataList.stream().filter(n -> component.getScode().equals(n.getMaterialCode())).findFirst();
                            if(firstComponent.isPresent()){
                                CrmProjectProductOwn crmProjectProductOwn = new CrmProjectProductOwn();
                                crmProjectProductOwn.setId(UUID.randomUUID().toString());
                                crmProjectProductOwn.setParentId(projectProductOwn.getId());
                                crmProjectProductOwn.setProjectId(projectId);
                                crmProjectProductOwn.setProductId(firstComponent.get().getId());
                                crmProjectProductOwn.setProductLic(0L);
                                crmProjectProductOwn.setProductNum(Long.valueOf(component.getNumber()));
                                crmProjectProductOwn.setTempProductNum(Long.valueOf(component.getNumber()));
                                crmProjectProductOwn.setQuotedPrice(firstComponent.get().getPrice());
                                crmProjectProductOwn.setQuotedTotalPrice(firstComponent.get().getPrice().multiply(BigDecimal.valueOf(component.getNumber())));
                                crmProjectProductOwn.setAttr(firstComponent.get().getAttr());
                                crmProjectProductOwn.setProductType(firstComponent.get().getForm().toString());
                                crmProjectProductOwn.setProductSpecification(firstComponent.get().getUnit());
                                crmProjectProductOwn.setStuffCode(firstComponent.get().getMaterialCode());
                                crmProjectProductOwn.setProductName(firstComponent.get().getName());
                                crmProjectProductOwn.setPnCode(firstComponent.get().getPn());
                                crmProjectProductOwn.setProductCategory(firstComponent.get().getSpecificationId());
                                crmProjectProductOwn.setDealPrice(BigDecimal.valueOf(component.getPrice()));
                                crmProjectProductOwn.setDealTotalPrice(BigDecimal.valueOf(component.getPrice()).multiply(BigDecimal.valueOf(component.getNumber())));
                                crmProjectProductOwn.setRebatePrice(BigDecimal.ZERO);
                                crmProjectProductOwn.setFinalPrice(BigDecimal.valueOf(component.getPrice()));
                                crmProjectProductOwn.setFinalTotalPrice(BigDecimal.valueOf(component.getPrice()).multiply(BigDecimal.valueOf(component.getNumber())));
                                crmProjectProductOwn.setTaxRate(new BigDecimal("0.09"));
                                crmProjectProductOwn.setProductPeriod(new BigDecimal(24));
                                crmProjectProductOwn.setState(0);
                                crmProjectProductOwn.setRemark("天智报价单导入");
                                crmProjectProductOwn.setProductLine1(firstComponent.get().getProductLine1());
                                productOwnComponents.add(crmProjectProductOwn);
                            }
                        }
                    }
                    // 构建服务信息
                    List<Special> service = m.getService();
                    if(CollectionUtils.isNotEmpty(service)){
                        for (Special serviceComponent : service) {
                            // 构建产品信息
                            Optional<CrmProductVo> firstComponent = productDataList.stream().filter(n -> serviceComponent.getProductcode().equals(n.getMaterialCode())).findFirst();
                            if(firstComponent.isPresent()){
                                CrmProjectProductOwn crmProjectProductOwn = new CrmProjectProductOwn();
                                crmProjectProductOwn.setId(UUID.randomUUID().toString());
                                crmProjectProductOwn.setParentId(projectProductOwn.getId());
                                crmProjectProductOwn.setProjectId(projectId);
                                crmProjectProductOwn.setProductId(firstComponent.get().getId());
                                crmProjectProductOwn.setProductLic(0L);
                                crmProjectProductOwn.setProductNum(Long.valueOf(serviceComponent.getNumber()));
                                crmProjectProductOwn.setTempProductNum(Long.valueOf(serviceComponent.getNumber()));
                                crmProjectProductOwn.setQuotedPrice(firstComponent.get().getPrice());
                                crmProjectProductOwn.setQuotedTotalPrice(firstComponent.get().getPrice().multiply(BigDecimal.valueOf(serviceComponent.getNumber())));
                                crmProjectProductOwn.setAttr(firstComponent.get().getAttr());
                                crmProjectProductOwn.setProductType(firstComponent.get().getForm().toString());
                                crmProjectProductOwn.setProductSpecification(firstComponent.get().getUnit());
                                crmProjectProductOwn.setStuffCode(firstComponent.get().getMaterialCode());
                                crmProjectProductOwn.setProductName(firstComponent.get().getName());
                                crmProjectProductOwn.setPnCode(firstComponent.get().getPn());
                                crmProjectProductOwn.setProductCategory(firstComponent.get().getSpecificationId());
                                crmProjectProductOwn.setDealPrice(BigDecimal.valueOf(serviceComponent.getPrice()));
                                crmProjectProductOwn.setDealTotalPrice(BigDecimal.valueOf(serviceComponent.getPrice()).multiply(BigDecimal.valueOf(serviceComponent.getNumber())));
                                crmProjectProductOwn.setRebatePrice(BigDecimal.ZERO);
                                crmProjectProductOwn.setFinalPrice(BigDecimal.valueOf(serviceComponent.getPrice()));
                                crmProjectProductOwn.setFinalTotalPrice(BigDecimal.valueOf(serviceComponent.getPrice()).multiply(BigDecimal.valueOf(serviceComponent.getNumber())));
                                crmProjectProductOwn.setTaxRate(new BigDecimal("0.09"));
                                crmProjectProductOwn.setProductPeriod(new BigDecimal(24));
                                crmProjectProductOwn.setState(0);
                                crmProjectProductOwn.setRemark("天智报价单导入");
                                crmProjectProductOwn.setProductLine1(firstComponent.get().getProductLine1());
                                productOwnComponents.add(crmProjectProductOwn);
                            }
                        }
                    }
                    projectProductOwn.setChildren(productOwnComponents);
                    projectProductOwns.add(projectProductOwn);
                }
            });
        }
        return projectProductOwns;
    }

    /**
     * 根据产品行ID批量查询分摊的外包服务费
     *
     * @param projectId
     * @param productRecordIds
     * @return
     */
    @Override
    public List<CrmProjectProductOwn> fillSplitOutsourcePriceByRecordIds(String projectId, Set<String> productRecordIds) {
        if(CollectionUtils.isNotEmpty(productRecordIds) && StringUtils.isNotBlank(projectId)){
            BigDecimal totalPriceOfOwnProduct = BigDecimal.ZERO;
            BigDecimal totalOutsourcingServicePrice = BigDecimal.ZERO;
            // 异步获取项目自有产品总价
            CompletableFuture<List<CrmProjectProductOwn>> futureQueryOwnProduct = CompletableFuture.supplyAsync(() -> {
                List<CrmProjectProductOwn> list = crmProjectProductOwnService.list(new LambdaQueryWrapper<>(CrmProjectProductOwn.class).eq(CrmProjectProductOwn::getProjectId, projectId).in(CrmProjectProductOwn::getId, productRecordIds));
                crmProjectProductOwnService.productOwnPostProcessing(list);
                return list;
            }, UserInfoThreadExecutor.getExecutor());

            // 异步获取项目自有产品总价
            CompletableFuture<BigDecimal> futureOwnProduct = CompletableFuture.supplyAsync(() -> crmProjectProductOwnService.getTotalPriceOfOwnProduct(projectId), UserInfoThreadExecutor.getExecutor());

            // 异步获取项目外包服务总价
            CompletableFuture<BigDecimal> futureOutsourcingService = CompletableFuture.supplyAsync(() -> crmProjectOutsourcingServiceService.getTotalOutsourcingServicePrice(projectId), UserInfoThreadExecutor.getExecutor());

            // 当两个异步任务都完成时，组合结果
            CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(futureQueryOwnProduct, futureOwnProduct, futureOutsourcingService);
            // 阻塞直到所有任务完成
            combinedFuture.join();
            totalPriceOfOwnProduct = futureOwnProduct.join();
            totalOutsourcingServicePrice = futureOutsourcingService.join();
            List<CrmProjectProductOwn> list = futureQueryOwnProduct.join();
            // 外包服务分摊
            for (CrmProjectProductOwn crmProjectProductOwn:list) {
                BigDecimal dealTotalPrice = crmProjectProductOwn.getDealTotalPrice();
                BigDecimal splitOutsourcePrice = GrossMarginCalculateUtil.calculateSplitOutsourcePrice(dealTotalPrice, totalPriceOfOwnProduct, totalOutsourcingServicePrice);
                crmProjectProductOwn.setSplitOutsourcePrice(splitOutsourcePrice);
                if (CollectionUtils.isNotEmpty(crmProjectProductOwn.getChildren())) {
                    for (CrmProjectProductOwn c:crmProjectProductOwn.getChildren()){
                        BigDecimal quotedTotalPriceOfComponent = c.getQuotedTotalPrice();
                        BigDecimal splitOutsourcePriceOfComponent = GrossMarginCalculateUtil.calculateSplitOutsourcePrice(quotedTotalPriceOfComponent, totalPriceOfOwnProduct, totalOutsourcingServicePrice);
                        c.setSplitOutsourcePrice(splitOutsourcePriceOfComponent);
                    }
                }
            }
            return list;
        }
        return List.of();
    }
}
