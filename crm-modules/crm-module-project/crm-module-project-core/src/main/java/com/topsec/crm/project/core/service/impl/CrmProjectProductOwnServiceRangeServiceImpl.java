package com.topsec.crm.project.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.contract.api.RemoteContractProductOwnService;
import com.topsec.crm.contract.api.entity.response.CrmContractInfoBySn;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.Query;
import java.time.temporal.ChronoUnit;
import com.topsec.crm.product.api.RemoteDeviceService;
import com.topsec.crm.product.api.entity.CrmDeviceVo;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.project.api.dto.ProjectProductOwnServiceRangePageQuery;
import com.topsec.crm.project.core.entity.CrmProjectProductOwn;
import com.topsec.crm.project.core.entity.CrmProjectProductOwnServiceRange;
import com.topsec.crm.project.core.mapper.CrmProjectProductOwnServiceRangeMapper;
import com.topsec.crm.project.core.service.ICrmProjectProductOwnService;
import com.topsec.crm.project.core.service.ICrmProjectProductOwnServiceRangeService;
import com.topsec.tbscommon.JsonObject;
import org.apache.commons.collections4.CollectionUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目-自有产品的配件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-25
 */
@Service
public class CrmProjectProductOwnServiceRangeServiceImpl extends ServiceImpl<CrmProjectProductOwnServiceRangeMapper, CrmProjectProductOwnServiceRange> implements ICrmProjectProductOwnServiceRangeService {
    @Autowired
    private CrmProjectProductOwnServiceRangeMapper crmProjectProductOwnServiceRangeMapper;

    @Autowired
    private ICrmProjectProductOwnService crmProjectProductOwnService;

    @Autowired
    private ICrmProjectProductOwnServiceRangeService crmProjectProductOwnServiceRangeService;

    @Autowired
    private RemoteDeviceService remoteDeviceService;

    @Autowired
    private RemoteContractProductOwnService remoteContractProductOwnService;

    /**
     * 查询项目-自有产品的配件
     *
     * @param id 项目-自有产品的配件ID
     * @return 项目-自有产品的配件
     */
    @Override
    public CrmProjectProductOwnServiceRange selectCrmProjectProductOwnServiceRangeById(String id) {
        return crmProjectProductOwnServiceRangeMapper.selectCrmProjectProductOwnServiceRangeById(id);
    }

    /**
     * 查询项目-自有产品的配件列表
     *
     * @param crmProjectProductOwnServiceRange 项目-自有产品的配件
     * @return 项目-自有产品的配件
     */
    @Override
    public List<CrmProjectProductOwnServiceRange> selectCrmProjectProductOwnServiceRangeList(CrmProjectProductOwnServiceRange crmProjectProductOwnServiceRange) {
        return crmProjectProductOwnServiceRangeMapper.selectCrmProjectProductOwnServiceRangeList(crmProjectProductOwnServiceRange);
    }

    /**
     * 新增项目-自有产品的配件
     *
     * @param crmProjectProductOwnServiceRange 项目-自有产品的配件
     * @return 结果
     */
    @Override
    public int insertCrmProjectProductOwnServiceRange(CrmProjectProductOwnServiceRange crmProjectProductOwnServiceRange) {
        crmProjectProductOwnServiceRange.setCreateTime(LocalDateTime.now());
        return crmProjectProductOwnServiceRangeMapper.insertCrmProjectProductOwnServiceRange(crmProjectProductOwnServiceRange);
    }

    /**
     * 修改项目-自有产品的配件
     *
     * @param crmProjectProductOwnServiceRange 项目-自有产品的配件
     * @return 结果
     */
    @Override
    public int updateCrmProjectProductOwnServiceRange(CrmProjectProductOwnServiceRange crmProjectProductOwnServiceRange) {
        crmProjectProductOwnServiceRange.setUpdateTime(LocalDateTime.now());
        return crmProjectProductOwnServiceRangeMapper.updateCrmProjectProductOwnServiceRange(crmProjectProductOwnServiceRange);
    }

    /**
     * 批量删除项目-自有产品的配件
     *
     * @param ids 需要删除的项目-自有产品的配件ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteCrmProjectProductOwnServiceRangeByIds(String[] ids) {
        // 自动填充分销保修服务价格，固定的物料代码（6001002003002）
        List<CrmProjectProductOwnServiceRange> list = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getId, Arrays.asList(ids)));
        if(CollectionUtils.isNotEmpty(list)){
            Set<String> collect = list.stream().map(CrmProjectProductOwnServiceRange::getRecordId).collect(Collectors.toSet());
            List<CrmProjectProductOwn> distribution = crmProjectProductOwnService.list(crmProjectProductOwnService.buildDefaultQueryWrapper().in(CrmProjectProductOwn::getId, collect).eq(CrmProjectProductOwn::getStuffCode, "6001002003002"));
            crmProjectProductOwnService.productOwnPostProcessing(distribution);
            if(CollectionUtils.isNotEmpty(distribution)){
                Set<String> recordIds = distribution.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toSet());
                List<CrmProjectProductOwnServiceRange> rangeNow = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getRecordId, recordIds).notIn(CrmProjectProductOwnServiceRange::getId, Arrays.asList(ids)));
                crmProjectProductOwnServiceRangeService.recalculateDistributionPrice(distribution,rangeNow);
            }
        }
        return crmProjectProductOwnServiceRangeService.removeBatchByIds(Arrays.asList(ids), true);
    }

    /**
     * 批量重算分销保修服务价格
     *
     * @param distribution
     * @param rangeNow
     */
    @Override
    public void recalculateDistributionPrice(List<CrmProjectProductOwn> distribution, List<CrmProjectProductOwnServiceRange> rangeNow) {
        if(CollectionUtils.isNotEmpty(distribution)){
            for (CrmProjectProductOwn crmProjectProductOwn : distribution){
                List<CrmProjectProductOwnServiceRange> collect = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(rangeNow)){
                    collect = rangeNow.stream().filter(crmProjectProductOwnServiceRange -> crmProjectProductOwnServiceRange.getRecordId().equals(crmProjectProductOwn.getId())).toList();
                }
                BigDecimal price = BigDecimal.ZERO;
                if(CollectionUtils.isNotEmpty(collect)){
                    List<CrmProjectProductOwnServiceRange> firstBuy = collect.stream().filter(crmProjectProductOwnServiceRange -> StringUtils.isNotBlank(crmProjectProductOwnServiceRange.getBindProductRecordId())).toList();
                    if(CollectionUtils.isNotEmpty(firstBuy)){
                        List<String> bindProductRecordId = firstBuy.stream().map(CrmProjectProductOwnServiceRange::getBindProductRecordId).toList();
                        List<CrmProjectProductOwn> bindProductOwns = crmProjectProductOwnService.listByIds(bindProductRecordId);
                        for (CrmProjectProductOwn bindProductOwn : bindProductOwns){
                            price = price.add(distributePrice(crmProjectProductOwn.getProductPeriod(), bindProductOwn.getProductPeriod(), bindProductOwn.getQuotedTotalPrice()));
                        }
                    }
                    List<CrmProjectProductOwnServiceRange> oldBuy = collect.stream().filter(crmProjectProductOwnServiceRange -> StringUtils.isNotBlank(crmProjectProductOwnServiceRange.getSerialNumber())).toList();
                    if(CollectionUtils.isNotEmpty(oldBuy)){
                        List<String> list = oldBuy.stream().map(CrmProjectProductOwnServiceRange::getSerialNumber).toList();
                        JsonObject<List<CrmContractInfoBySn>> bySn = remoteContractProductOwnService.getBySn(list);
                        if(bySn.isSuccess() && CollectionUtils.isNotEmpty(bySn.getObjEntity())){
                            List<CrmContractInfoBySn> objList = bySn.getObjEntity();
                            for (CrmContractInfoBySn crmContractInfoBySn : objList){
                                LocalDate productPeriodStart = crmContractInfoBySn.getProductPeriodStart();
                                LocalDate productPeriodEnd = crmContractInfoBySn.getProductPeriodEnd();
                                if(productPeriodEnd.isBefore(LocalDate.now())){
                                    productPeriodEnd = LocalDate.now();
                                }
                                // 计算两个日期之间的月份差
                                long monthsBetween = ChronoUnit.MONTHS.between(productPeriodStart, productPeriodEnd);
                                price = price.add(distributePrice(crmProjectProductOwn.getProductPeriod(), new BigDecimal(monthsBetween), crmContractInfoBySn.getQuotedPrice()));
                            }
                        }else{
                            // 根据序列号查询设备失败
                            throw new CrmException("未找到服务范围对应的产品");
                        }
                    }
                    price = price.setScale(2, RoundingMode.HALF_UP);
                    crmProjectProductOwnService.lambdaUpdate().set(CrmProjectProductOwn::getProductNum, 1)
                            .set(CrmProjectProductOwn::getQuotedPrice, price).set(CrmProjectProductOwn::getQuotedTotalPrice, price)
                            .set(CrmProjectProductOwn::getFinalPrice, price).set(CrmProjectProductOwn::getFinalTotalPrice, price)
                            .set(CrmProjectProductOwn::getDealPrice, price).set(CrmProjectProductOwn::getDealTotalPrice, price)
                            .eq(CrmProjectProductOwn::getId, crmProjectProductOwn.getId()).update();
                }else{
                    // 分销保修服务的服务范围为空 默认写0
                    crmProjectProductOwnService.lambdaUpdate().set(CrmProjectProductOwn::getProductNum, 1)
                            .set(CrmProjectProductOwn::getQuotedPrice, price).set(CrmProjectProductOwn::getQuotedTotalPrice, price)
                            .set(CrmProjectProductOwn::getFinalPrice, price).set(CrmProjectProductOwn::getFinalTotalPrice, price)
                            .set(CrmProjectProductOwn::getDealPrice, price).set(CrmProjectProductOwn::getDealTotalPrice, price)
                            .eq(CrmProjectProductOwn::getId, crmProjectProductOwn.getId()).update();
                }
            }
        }
    }

    /**
     * 分销保修服务产品价格计算
     * @param distributionProductPeriod  购买的分销保修服务的月份数
     * @param bindProductPeriod 绑定产品的保修月份数
     * @param quotedTotalPrice 报价总价
     * @return
     */
    private static BigDecimal distributePrice(BigDecimal distributionProductPeriod, BigDecimal bindProductPeriod, BigDecimal quotedTotalPrice) {
        BigDecimal price = BigDecimal.ZERO;
        // 每个月的价格
        BigDecimal eachMonthPrice = quotedTotalPrice.divide(BigDecimal.valueOf(12), 2, RoundingMode.HALF_UP);
        BigDecimal totalProductPeriod = bindProductPeriod.add(distributionProductPeriod);
        if(totalProductPeriod.compareTo(BigDecimal.valueOf(60)) > 0){
            // 总保修时间大于5年（60个月）
            BigDecimal exceed = totalProductPeriod.subtract(BigDecimal.valueOf(60));
            if(exceed.compareTo(distributionProductPeriod) >= 0){
                price = price.add(eachMonthPrice.multiply(distributionProductPeriod).multiply(new BigDecimal("0.06")));
            }else{
                BigDecimal subtract = distributionProductPeriod.subtract(exceed);
                price = price.add(eachMonthPrice.multiply(subtract).multiply(new BigDecimal("0.01")));
                price = price.add(eachMonthPrice.multiply(exceed).multiply(new BigDecimal("0.06")));
            }
        }else{
            // 总保修时间小于或等于5年（60个月）
            price = price.add(eachMonthPrice.multiply(distributionProductPeriod).multiply(new BigDecimal("0.01")));
        }
        return price;
    }

    /**
     * 删除项目-自有产品的配件信息
     *
     * @param id 项目-自有产品的配件ID
     * @return 结果
     */
    @Override
    public int deleteCrmProjectProductOwnServiceRangeById(String id) {
        return crmProjectProductOwnServiceRangeMapper.deleteCrmProjectProductOwnServiceRangeById(id);
    }

    /**
     * 批量新增安全服务产品子服务的服务范围
     *
     * @param collect
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertCrmProjectProductOwnServiceRangeBatch(List<CrmProjectProductOwnServiceRange> collect) {
        if (CollectionUtils.isNotEmpty(collect)) {
            List<String> serialNumbers = collect.stream().map(CrmProjectProductOwnServiceRange::getSerialNumber).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(serialNumbers)) {
                // 一次提交的服务范围一般是同一个产品行的数据服务范围，故此处统一获取第一个元素的产品行ID
                String recordId = collect.get(0).getRecordId();
                // 校验是否存在已经添加过的序列号
                List<CrmProjectProductOwnServiceRange> crmProjectProductOwnServiceRanges = crmProjectProductOwnServiceRangeMapper.selectList(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().in(CrmProjectProductOwnServiceRange::getSerialNumber, serialNumbers).eq(CrmProjectProductOwnServiceRange::getRecordId, recordId));
                if(CollectionUtils.isNotEmpty(crmProjectProductOwnServiceRanges)){
                    throw new RuntimeException("序列号重复，请勿重复添加");
                }
                // 自动填充分销保修服务价格，固定的物料代码（6001002003002）
                CrmProjectProductOwn byId = crmProjectProductOwnService.getById(recordId);
                if(null != byId && byId.getStuffCode().equals("6001002003002")){
                    List<CrmProjectProductOwnServiceRange> list = crmProjectProductOwnServiceRangeService.list(new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>().eq(CrmProjectProductOwnServiceRange::getRecordId, recordId));
                    if(CollectionUtils.isNotEmpty(list)){
                        list.addAll(collect);
                        crmProjectProductOwnServiceRangeService.recalculateDistributionPrice(Collections.singletonList(byId),list);
                    }else{
                        crmProjectProductOwnServiceRangeService.recalculateDistributionPrice(Collections.singletonList(byId),collect);
                    }
                }
                // 根据产品序列号查询对应的产品，填充服务范围中的数据
                JsonObject<List<CrmDeviceVo>> listJsonObject = remoteDeviceService.selectProductBySns(serialNumbers);
                if (listJsonObject.isSuccess() && CollectionUtils.isNotEmpty(listJsonObject.getObjEntity())) {
                    List<CrmDeviceVo> objEntity = listJsonObject.getObjEntity();
                    collect.stream().filter(m -> StringUtils.isNotBlank(m.getSerialNumber())).forEach(m -> {
                        CrmDeviceVo crmDeviceVo = objEntity.stream().filter(n -> n.getSn().equals(m.getSerialNumber())).findFirst().orElse(null);
                        if (null != crmDeviceVo) {
                            CrmProductVo crmProductVo = crmDeviceVo.getCrmProductVo();
                            if (null != crmProductVo) {
                                m.setProductName(crmProductVo.getName());
                                m.setStuffCode(crmProductVo.getMaterialCode());
                                m.setPnCode(crmProductVo.getPn());
                                m.setProductNum(1L);
                            }
                        }
                    });
                }
            }
        }
        boolean saveBatch = super.saveBatch(collect);
        return saveBatch;
    }

    /**
     * 根据参数查询安全服务产品子服务的服务范围
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils<CrmProjectProductOwnServiceRange> selectCrmProjectProductOwnServiceRangePageByParams(ProjectProductOwnServiceRangePageQuery params) {
        String projectId = params.getProjectId();
        String currentPersonId = params.getCurrentPersonId();
        String recordId = params.getRecordId();
        Boolean projectProduct = params.getProjectProduct();
        if (StringUtils.isNotBlank(projectId) && StringUtils.isNotBlank(currentPersonId)) {
            LambdaQueryWrapper<CrmProjectProductOwnServiceRange> serviceRangeQueryWrapper = new LambdaQueryWrapper<CrmProjectProductOwnServiceRange>()
                    .eq(CrmProjectProductOwnServiceRange::getRecordId, recordId)
                    .isNotNull(projectProduct, CrmProjectProductOwnServiceRange::getBindProductRecordId)
                    .isNull(!projectProduct, CrmProjectProductOwnServiceRange::getBindProductRecordId)
                    .orderByDesc(CollectionUtils.isEmpty(params.getOrders()), CrmProjectProductOwnServiceRange::getCreateTime);
            Page<CrmProjectProductOwnServiceRange> page = super.page(new Query<CrmProjectProductOwnServiceRange>().getPage(params), serviceRangeQueryWrapper);
            List<CrmProjectProductOwnServiceRange> list = page.getRecords();
            if (CollectionUtils.isNotEmpty(list)) {
                List<String> bindProductRecordId = list.stream().map(CrmProjectProductOwnServiceRange::getBindProductRecordId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(bindProductRecordId)){
                    List<CrmProjectProductOwn> crmProjectProductOwns = crmProjectProductOwnService.listByIds(bindProductRecordId);
                    list.forEach(item -> {
                        CrmProjectProductOwn crmProjectProductOwn = crmProjectProductOwns.stream().filter(own -> own.getId().equals(item.getBindProductRecordId())).findFirst().orElse(null);
                        if (null != crmProjectProductOwn) {
                            item.setProductNum(crmProjectProductOwn.getProductNum());
                            // todo liujia 根据组ID获取设备生产后的序列号信息
                            item.setSerialNumber("序列号占位符");
                        }
                    });
                }
            }
            return new PageUtils<>(page);
        }
        return new PageUtils<>();
    }
}
