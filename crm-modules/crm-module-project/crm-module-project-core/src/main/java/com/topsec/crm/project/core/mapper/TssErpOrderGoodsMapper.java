package com.topsec.crm.project.core.mapper;

import com.topsec.crm.project.api.entity.CrmProjectDirectlyDeliveryVO;
import com.topsec.crm.project.api.entity.tss.TssErpOrderGoods;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TssErpOrderGoods 到 CrmProjectDirectlyDeliveryVO 的映射器
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Component
public class TssErpOrderGoodsMapper {

    /**
     * 将TssErpOrderGoods转换为CrmProjectDirectlyDeliveryVO
     * 
     * @param source TssErpOrderGoods对象
     * @return CrmProjectDirectlyDeliveryVO对象
     */
    public CrmProjectDirectlyDeliveryVO toDeliveryVO(TssErpOrderGoods source) {
        if (source == null) {
            return null;
        }
        
        CrmProjectDirectlyDeliveryVO target = new CrmProjectDirectlyDeliveryVO();
        
        // 字段映射
        target.setContractNo(source.getContractNumber()); // 合同号
        target.setOrderNo(source.getOrderNumber()); // 订单号
        target.setMaterialCode(source.getMaterialCode()); // 物料代码
        
        // 数量：将Integer转换为BigDecimal
        if (source.getGoodsNumber() != null) {
            target.setQuantity(new BigDecimal(source.getGoodsNumber()));
        }
        
        // 预计发货数量：将Integer转换为BigDecimal
        if (source.getPreDeliverNumber() != null) {
            target.setExpectedDeliveryQuantity(new BigDecimal(source.getPreDeliverNumber()));
        }
        
        // 预计齐料时间：将LocalDateTime转换为Date
        if (source.getNoticeCreateTime() != null) {
            target.setExpectedCompleteTime(source.getNoticeCreateTime());
        }
        
        // 物流信息
        target.setLogisticsCompany(source.getExpressName()); // 物流公司
        target.setTrackingNumber(source.getExpressNumber()); // 物流单号
        target.setShippingMethod(source.getSendMethod()); // 发运方式
        
        // 状态转换：将Integer状态转换为String
        if (source.getStatus() != null) {
            target.setStatus(convertStatusToString(source.getStatus()));
        }
        
        // 设置默认值
        target.setDelFlag(0); // 未删除
        

        target.setPicker(source.getExtendInfo()); // 自提人 - 暂无对应字段
        
        return target;
    }

    /**
     * 批量转换
     * 
     * @param sourceList TssErpOrderGoods列表
     * @return CrmProjectDirectlyDeliveryVO列表
     */
    public List<CrmProjectDirectlyDeliveryVO> toDeliveryVOList(List<TssErpOrderGoods> sourceList) {
        if (sourceList == null) {
            return null;
        }
        
        return sourceList.stream()
                .map(this::toDeliveryVO)
                .collect(Collectors.toList());
    }



    /**
     * 将状态码转换为状态描述
     * 
     * @param status 状态码
     * @return 状态描述
     */
    private String convertStatusToString(Integer status) {
        if (status == null) {
            return null;
        }
        
        return switch (status) {
            case 1 -> "待排产";
            case 2 -> "待发货";
            case 3 -> "待收货";
            case 4 -> "已自提";
            case 5 -> "已完成";
            default -> "未知状态(" + status + ")";
        };
    }
}
