package com.topsec.crm.project.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 价审折扣明细对象 crm_approval_discount_details
 *
 * @date 2024-05-10
 */
@Data
@TableName(value = "crm_approval_discount_details")
public class CrmApprovalDiscountDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private String id;
    /** 主表ID */
    private String masterId;
    /** 折扣分类ID */
    private String discountCategoryId;

    /** 折扣分类名称 */
    @TableField(exist = false)
    private String discountCategoryName;

    /** 产品分类名称 */
    @TableField(exist = false)
    private String productCategoryName;
    /** 折扣值 */
    private BigDecimal discountValue;
    /** 创建人ID */
    private String createUser;
    /** 更新人ID */
    private String updateUser;
    /** 删除标记 0-未删除 1-已删除 */
    private Integer delFlag;
}
