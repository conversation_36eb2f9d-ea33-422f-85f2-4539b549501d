package com.topsec.crm.project.core.controller;

import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.project.api.client.TssApiServiceClient;
import com.topsec.crm.project.api.dto.CrmProjectDirectlyDeliveryQuery;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyDeliveryVO;
import com.topsec.crm.project.api.entity.tss.TssErpOrderGoodsMaterial;
import com.topsec.crm.project.core.service.CrmProjectDirectlyDeliveryService;
import com.topsec.logistics.api.client.RemoteLogisticsClient;
import com.topsec.logistics.api.domain.BatchSubParam;
import com.topsec.logistics.api.domain.LogisticsInfo;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 公司项目生产发货产品信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@RestController
@RequestMapping("/projectDirectlyDelivery")
@Tag(name = "【公司项目生产发货产品信息】", description = "projectDirectlyDelivery")
@RequiredArgsConstructor
public class CrmProjectDirectlyDeliveryController extends BaseController {

    private final CrmProjectDirectlyDeliveryService crmProjectDirectlyDeliveryService;
    private final RemoteLogisticsClient remoteLogisticsClient;

    private final TssApiServiceClient tssApiServiceClient;

    /**
     * 分页查询发货信息
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询发货信息")
//    @PreAuthorize(hasPermission = "crm_project_directly")
    public JsonObject<PageUtils<CrmProjectDirectlyDeliveryVO>> page(@RequestBody CrmProjectDirectlyDeliveryQuery query) {
        PageUtils<CrmProjectDirectlyDeliveryVO> result = crmProjectDirectlyDeliveryService.pageDeliveryInfo(query);
        return new JsonObject<>(result);
    }

    



    @PostMapping("/getLackCodes")
    @Operation(summary = "获取缺料信息")
//    @PreAuthorize(hasPermission = "crm_project_directly")
    public JsonObject<PageUtils<TssErpOrderGoodsMaterial>> getLackCodes(@RequestBody CrmProjectDirectlyDeliveryQuery query) {
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        stringObjectHashMap.put("materialCode",query.getMaterialCode());
        JsonObject<PageUtils<TssErpOrderGoodsMaterial>> result = tssApiServiceClient.getLackCodes(stringObjectHashMap);
        return result;
    }


    /**
     * 根据物料代码、合同号、状态查询发货信息
     */
    @GetMapping("/queryByConditions")
    @Operation(summary = "根据物料代码、合同号、状态查询发货信息")
//    @PreAuthorize(hasPermission = "crm_project_directly")
    public JsonObject<List<CrmProjectDirectlyDeliveryVO>> queryByConditions(@RequestParam(required = false) String materialCode, @RequestParam(required = false) String contractNo, @RequestParam(required = false) String status) {
        
        List<CrmProjectDirectlyDeliveryVO> result = crmProjectDirectlyDeliveryService.queryByMaterialCodeAndContractNoAndStatus(materialCode, contractNo, status);
        return new JsonObject<>(result);
    }

    /**
     * 根据快递单号查询快递信息
     */
    @GetMapping("/getLogisticsInfo")
    @Operation(summary = "根据快递单号查询快递信息")
//    @PreAuthorize(hasPermission = "crm_project_directly")
    public JsonObject<LogisticsInfo> getLogisticsInfo(@RequestParam String trackingNumber, @RequestParam(required = false) String logisticsCompany) throws Exception {
        
        if (StringUtils.isEmpty(trackingNumber)) {
            return JsonObject.errorT("快递单号不能为空");
        }

        try {
            // 构建查询参数
            BatchSubParam param = new BatchSubParam();
            param.setNumber(trackingNumber);
            if (StringUtils.isNotEmpty(logisticsCompany)) {
                param.setCompany(logisticsCompany);
            }

            // 调用物流服务查询快递信息
            JsonObject<LogisticsInfo> result = remoteLogisticsClient.getLogisticsInfo(param);
            
            if (result.isSuccess()) {
                log.info("查询快递信息成功，单号：{}", trackingNumber);
                return result;
            } else {
                log.warn("查询快递信息失败，单号：{}，错误信息：{}", trackingNumber, result.getMessage());
                return JsonObject.errorT("查询快递信息失败：" + result.getMessage());
            }
        } catch (Exception e) {
            log.error("查询快递信息异常，单号：{}", trackingNumber, e);
            return JsonObject.errorT("查询快递信息异常：" + e.getMessage());
        }
    }

}
