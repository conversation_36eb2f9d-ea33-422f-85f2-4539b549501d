package com.topsec.crm.project.core.controller;

import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.product.api.RemoteProductService;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.project.api.client.TssApiServiceClient;
import com.topsec.crm.project.api.dto.CrmProjectDirectlyDeliveryQuery;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyDeliveryVO;

import com.topsec.crm.project.api.entity.tss.TssErpOrderGoodsMaterial;
import com.topsec.crm.project.core.mapper.TssErpOrderGoodsMaterialMapper;
import com.topsec.crm.project.core.service.CrmProjectDirectlyDeliveryService;
import com.topsec.logistics.api.client.RemoteLogisticsClient;
import com.topsec.logistics.api.domain.BatchSubParam;
import com.topsec.logistics.api.domain.LogisticsInfo;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 公司项目生产发货产品信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@RestController
@RequestMapping("/projectDirectlyDelivery")
@Tag(name = "【公司项目生产发货产品信息】", description = "projectDirectlyDelivery")
@RequiredArgsConstructor
public class CrmProjectDirectlyDeliveryController extends BaseController {

    private final CrmProjectDirectlyDeliveryService crmProjectDirectlyDeliveryService;
    private final RemoteLogisticsClient remoteLogisticsClient;
    private final TssApiServiceClient tssApiServiceClient;
    private final RemoteProductService remoteProductService;
    private final TssErpOrderGoodsMaterialMapper tssErpOrderGoodsMaterialMapper;

    /**
     * 分页查询发货信息
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询发货信息")
//    @PreAuthorize(hasPermission = "crm_project_directly")
    public JsonObject<PageUtils<CrmProjectDirectlyDeliveryVO>> page(@RequestBody CrmProjectDirectlyDeliveryQuery query) {
        PageUtils<CrmProjectDirectlyDeliveryVO> result = crmProjectDirectlyDeliveryService.pageDeliveryInfo(query);
        return new JsonObject<>(result);
    }

    /**
     * 从TSS系统分页查询发货信息
     * 将TssErpOrderGoodsMaterial转换为CrmProjectDirectlyDeliveryVO
     */
    @PostMapping("/tssPage")
    @Operation(summary = "从TSS系统分页查询发货信息")
//    @PreAuthorize(hasPermission = "crm_project_directly")
    public JsonObject<PageUtils<CrmProjectDirectlyDeliveryVO>> tssPage(@RequestBody CrmProjectDirectlyDeliveryQuery query) {
        try {
            // 1. 构建TSS API查询参数
            Map<String, Object> tssParams = new HashMap<>();
            if (StringUtils.isNotEmpty(query.getMaterialCode())) {
                tssParams.put("materialCode", query.getMaterialCode());
            }
            // 可以根据需要添加更多查询参数

            // 2. 调用TSS API获取缺料信息
            JsonObject<PageUtils<TssErpOrderGoodsMaterial>> tssResult = tssApiServiceClient.getLackCodes(tssParams);

            if (!tssResult.isSuccess() || tssResult.getObjEntity() == null) {
                log.warn("调用TSS API失败或返回数据为空: {}", tssResult.getMessage());
                return JsonObject.errorT("获取TSS数据失败: " + tssResult.getMessage());
            }

            PageUtils<TssErpOrderGoodsMaterial> tssPageData = tssResult.getObjEntity();
            List<TssErpOrderGoodsMaterial> tssList = tssPageData.getList();

            if (tssList == null || tssList.isEmpty()) {
                // 返回空的分页结果，保持分页信息
                PageUtils<CrmProjectDirectlyDeliveryVO> emptyResult = new PageUtils<>();
                return new JsonObject<>(emptyResult);
            }

            // 3. 使用Mapper进行对象转换
            List<CrmProjectDirectlyDeliveryVO> deliveryVOList = tssErpOrderGoodsMaterialMapper.toDeliveryVOList(tssList);

            // 4. 补充产品信息（产品名称和PN）
            enrichProductInfo(deliveryVOList);

            // 5. 构建分页结果，保持原有的分页信息
            PageUtils<CrmProjectDirectlyDeliveryVO> result = new PageUtils<>();
            result.setList(deliveryVOList);
            result.setTotalCount(tssPageData.getTotalCount());
            result.setPageSize(tssPageData.getPageSize());
            result.setPageNum(tssPageData.getPageNum());

            return new JsonObject<>(result);

        } catch (Exception e) {
            log.error("TSS分页查询发货信息异常", e);
            return JsonObject.errorT("查询失败: " + e.getMessage());
        }
    }


 



    @PostMapping("/getLackCodes")
    @Operation(summary = "获取缺料信息")
//    @PreAuthorize(hasPermission = "crm_project_directly")
    public JsonObject<PageUtils<TssErpOrderGoodsMaterial>> getLackCodes(@RequestBody CrmProjectDirectlyDeliveryQuery query) {
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        stringObjectHashMap.put("materialCode",query.getMaterialCode());
        JsonObject<PageUtils<TssErpOrderGoodsMaterial>> result = tssApiServiceClient.getLackCodes(stringObjectHashMap);
        return result;
    }


    /**
     * 根据物料代码、合同号、状态查询发货信息
     */
    @GetMapping("/queryByConditions")
    @Operation(summary = "根据物料代码、合同号、状态查询发货信息")
//    @PreAuthorize(hasPermission = "crm_project_directly")
    public JsonObject<List<CrmProjectDirectlyDeliveryVO>> queryByConditions(@RequestParam(required = false) String materialCode, @RequestParam(required = false) String contractNo, @RequestParam(required = false) String status) {
        
        List<CrmProjectDirectlyDeliveryVO> result = crmProjectDirectlyDeliveryService.queryByMaterialCodeAndContractNoAndStatus(materialCode, contractNo, status);
        return new JsonObject<>(result);
    }

    /**
     * 根据快递单号查询快递信息
     */
    @GetMapping("/getLogisticsInfo")
    @Operation(summary = "根据快递单号查询快递信息")
//    @PreAuthorize(hasPermission = "crm_project_directly")
    public JsonObject<LogisticsInfo> getLogisticsInfo(@RequestParam String trackingNumber, @RequestParam(required = false) String logisticsCompany) throws Exception {
        
        if (StringUtils.isEmpty(trackingNumber)) {
            return JsonObject.errorT("快递单号不能为空");
        }

        try {
            // 构建查询参数
            BatchSubParam param = new BatchSubParam();
            param.setNumber(trackingNumber);
            if (StringUtils.isNotEmpty(logisticsCompany)) {
                param.setCompany(logisticsCompany);
            }

            // 调用物流服务查询快递信息
            JsonObject<LogisticsInfo> result = remoteLogisticsClient.getLogisticsInfo(param);
            
            if (result.isSuccess()) {
                log.info("查询快递信息成功，单号：{}", trackingNumber);
                return result;
            } else {
                log.warn("查询快递信息失败，单号：{}，错误信息：{}", trackingNumber, result.getMessage());
                return JsonObject.errorT("查询快递信息失败：" + result.getMessage());
            }
        } catch (Exception e) {
            log.error("查询快递信息异常，单号：{}", trackingNumber, e);
            return JsonObject.errorT("查询快递信息异常：" + e.getMessage());
        }
    }

    /**
     * 补充产品信息（产品名称和PN）
     * 根据物料代码调用产品服务获取产品信息
     *
     * @param deliveryVOList 发货信息列表
     */
    private void enrichProductInfo(List<CrmProjectDirectlyDeliveryVO> deliveryVOList) {
        if (deliveryVOList == null || deliveryVOList.isEmpty()) {
            return;
        }

        // 收集所有需要查询的物料代码
        List<String> materialCodes = deliveryVOList.stream()
                .map(CrmProjectDirectlyDeliveryVO::getMaterialCode)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        if (materialCodes.isEmpty()) {
            log.warn("没有有效的物料代码需要查询产品信息");
            return;
        }

        try {
            // 批量查询产品信息
            JsonObject<List<CrmProductVo>> productResult = remoteProductService.batchGetInfoByMaterialCode(materialCodes);

            if (!productResult.isSuccess() || productResult.getObjEntity() == null) {
                log.warn("批量查询产品信息失败: {}", productResult.getMessage());
                return;
            }

            List<CrmProductVo> productList = productResult.getObjEntity();

            // 构建物料代码到产品信息的映射
            Map<String, CrmProductVo> materialCodeToProductMap = productList.stream()
                    .collect(Collectors.toMap(
                            CrmProductVo::getMaterialCode,
                            product -> product,
                            (existing, replacement) -> existing // 如果有重复的物料代码，保留第一个
                    ));

            // 为每个发货信息补充产品信息
            for (CrmProjectDirectlyDeliveryVO deliveryVO : deliveryVOList) {
                String materialCode = deliveryVO.getMaterialCode();
                if (StringUtils.isNotEmpty(materialCode)) {
                    CrmProductVo product = materialCodeToProductMap.get(materialCode);
                    if (product != null) {
                        // 使用Mapper更新产品信息
                        tssErpOrderGoodsMaterialMapper.updateProductInfo(deliveryVO, product.getName(), product.getPn());
                    } else {
                        // 未找到产品信息时添加注释
                        log.debug("未找到物料代码 {} 对应的产品信息", materialCode);
                        // 可以设置默认值或者保持为null
                        deliveryVO.setProductName("未找到产品信息"); // 添加注释说明
                        deliveryVO.setProductPn("未找到PN信息"); // 添加注释说明
                    }
                }
            }

        } catch (Exception e) {
            log.error("补充产品信息时发生异常", e);
        }
    }

}
