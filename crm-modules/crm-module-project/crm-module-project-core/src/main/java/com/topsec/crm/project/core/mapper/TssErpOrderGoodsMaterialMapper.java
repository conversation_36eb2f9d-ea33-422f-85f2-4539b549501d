package com.topsec.crm.project.core.mapper;

import com.topsec.crm.project.api.entity.CrmProjectDirectlyDeliveryVO;
import com.topsec.crm.project.api.entity.tss.TssErpOrderGoodsMaterial;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TssErpOrderGoodsMaterial 到 CrmProjectDirectlyDeliveryVO 的映射器
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Component
public class TssErpOrderGoodsMaterialMapper {

    /**
     * 将TssErpOrderGoodsMaterial转换为CrmProjectDirectlyDeliveryVO
     *
     * @param source TssErpOrderGoodsMaterial对象
     * @return CrmProjectDirectlyDeliveryVO对象
     */
    public CrmProjectDirectlyDeliveryVO toDeliveryVO(TssErpOrderGoodsMaterial source) {
        if (source == null) {
            return null;
        }

        CrmProjectDirectlyDeliveryVO target = new CrmProjectDirectlyDeliveryVO();

        // 字段映射
        target.setOrderNo(source.getOrderNumber()); // 订单号
        target.setMaterialCode(source.getMaterialCode()); // 物料代码

        // 预计发货数量：将Integer转换为BigDecimal
        if (source.getPreDeliverNumber() != null) {
            target.setExpectedDeliveryQuantity(new BigDecimal(source.getPreDeliverNumber()));
        }

        // 预计齐料时间：将LocalDate转换为Date
        if (source.getPlanCompleteDate() != null) {
            target.setExpectedCompleteTime(java.sql.Date.valueOf(source.getPlanCompleteDate()));
        }

        // 设置默认值
        target.setDelFlag(0); // 未删除

        // 以下字段需要从其他地方获取或暂时为空，添加注释说明：
        // target.setContractNo(null); // 合同号 - 需要从其他地方获取
        // target.setProductName(null); // 产品名称 - 需要通过产品服务获取
        // target.setProductPn(null); // 产品PN - 需要通过产品服务获取
        // target.setQuantity(null); // 数量 - 暂无对应字段
        // target.setShippingMethod(null); // 发运方式 - 暂无对应字段
        // target.setLogisticsCompany(null); // 物流公司 - 暂无对应字段
        // target.setTrackingNumber(null); // 物流单号 - 暂无对应字段
        // target.setPicker(null); // 自提人 - 暂无对应字段
        // target.setStatus(null); // 状态 - 暂无对应字段

        return target;
    }

    /**
     * 批量转换
     *
     * @param sourceList TssErpOrderGoodsMaterial列表
     * @return CrmProjectDirectlyDeliveryVO列表
     */
    public List<CrmProjectDirectlyDeliveryVO> toDeliveryVOList(List<TssErpOrderGoodsMaterial> sourceList) {
        if (sourceList == null) {
            return null;
        }

        return sourceList.stream()
                .map(this::toDeliveryVO)
                .collect(Collectors.toList());
    }

    /**
     * 更新产品信息到目标对象
     *
     * @param target 目标对象
     * @param productName 产品名称
     * @param productPn 产品PN
     */
    public void updateProductInfo(CrmProjectDirectlyDeliveryVO target, String productName, String productPn) {
        if (target != null) {
            target.setProductName(productName);
            target.setProductPn(productPn);
        }
    }
}
