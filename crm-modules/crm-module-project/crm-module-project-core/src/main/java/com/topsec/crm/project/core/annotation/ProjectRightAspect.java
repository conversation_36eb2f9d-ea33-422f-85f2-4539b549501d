package com.topsec.crm.project.core.annotation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.ProjectMemberEnum;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import com.topsec.crm.framework.common.web.page.CrmPageQuery;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.crm.framework.security.utils.AuthorizeUtil;
import com.topsec.crm.project.api.dto.ProjectMemberQuery;
import com.topsec.crm.project.api.entity.CrmProjectMemberVO;
import com.topsec.crm.project.core.service.ICrmProjectDirectlyService;
import com.topsec.crm.project.core.service.ICrmProjectMemberService;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tac.common.JwtLoginInfo;
import com.topsec.tos.common.HyperBeanUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 项目权限校验切面
 * 注意：该切面必须在PreAuthorizeAspect切面之后执行,不能够单独使用，否则会报错
 */
@Aspect
@Component
public class ProjectRightAspect {

    @Autowired
    private ICrmProjectMemberService crmProjectMemberService;

    @Autowired
    private ICrmProjectDirectlyService crmProjectDirectlyService;

    @Resource
    private AuthorizeUtil authorizeUtil;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取当前接口数据范围参数
     * @return
     */
    public static DataScopeParam getDataScopeParam() {
        return AuthorizeContextHolder.getDataScopeParam();
    }

    public static boolean checkDirectlyProjectRight(String projectId){
        DataScopeParam dataScopeParam = ProjectRightAspect.getDataScopeParam();
        if(null != dataScopeParam && (CollectionUtils.isEmpty(dataScopeParam.getPersonIdList()) || (CollectionUtils.isNotEmpty(dataScopeParam.getProjectIdList()) && dataScopeParam.getProjectIdList().contains(projectId)))){
            return true;
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    public static boolean checkDirectlyProjectRight(Set<String> projectIds){
        DataScopeParam dataScopeParam = ProjectRightAspect.getDataScopeParam();
        if(null != dataScopeParam && (CollectionUtils.isEmpty(dataScopeParam.getPersonIdList()) || (CollectionUtils.isNotEmpty(dataScopeParam.getProjectIdList()) && dataScopeParam.getProjectIdList().containsAll(projectIds)))){
            return true;
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @Before("@annotation(com.topsec.crm.project.core.annotation.ProjectRight)")
    public void checkProjectRight(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method targetMethod = methodSignature.getMethod();
        if (targetMethod.isAnnotationPresent(ProjectRight.class)) {
            // 获取方法上注解中表明的权限
            ProjectRight projectRight =  targetMethod.getAnnotation(ProjectRight.class);
            if (StringUtils.isNotBlank(projectRight.operate())) {
                JwtLoginInfo jwtAccount = checkLogin();
                DataScopeParam dataScopeParam = ProjectRightAspect.getDataScopeParam();
                if(null == dataScopeParam){
                    dataScopeParam = new DataScopeParam();
                    dataScopeParam.setPersonIdList(new HashSet<>(Arrays.asList(jwtAccount.getPersonId())));
                }
                Set<String> personIds = new HashSet<>();
                if(CollectionUtils.isNotEmpty(dataScopeParam.getPersonIdList())){
                    personIds.addAll(dataScopeParam.getPersonIdList());
                }else{
                    personIds.add(jwtAccount.getPersonId());
                }
                String projectIdsCache = redisTemplate.opsForValue().get("crm:projectRight:" + projectRight.operate() + ":" + jwtAccount.getPersonId());
                if(StringUtils.isNotBlank(projectIdsCache)){
                    HashSet<String> allProjectIds = JSONArray.parseArray(projectIdsCache).stream()
                            .map(Object::toString)
                            .collect(Collectors.toCollection(HashSet::new));
                    dataScopeParam.setProjectIdList(new HashSet<>(allProjectIds));
                }else{
                    List<String> allProjectIds = crmProjectMemberService.getProjectIdsByPersonIds(new ArrayList<>(personIds));
                    // 是否有战略项目权限
                    boolean hasStrategicProjectRight = authorizeUtil.hasPermission(jwtAccount.getAccountId(), "crm_project_strategic");
                    if(hasStrategicProjectRight){
                        allProjectIds.addAll(crmProjectDirectlyService.queryStrategicProjectIds());
                    }
                    if("query".equals(projectRight.operate())){
                        // 查询当前用户可以查看的项目ID集合
                        dataScopeParam.setProjectIdList(new HashSet<>(allProjectIds));
                        redisTemplate.opsForValue().set("crm:projectRight:" + projectRight.operate() + ":" + jwtAccount.getPersonId(), JSONArray.toJSONString(allProjectIds), 15, TimeUnit.SECONDS);
                    }else if ("manager".equals(projectRight.operate())){
                        // 查询当前用户是项目管理员的项目ID集合
                        if(CollectionUtils.isNotEmpty(allProjectIds)){
                            ProjectMemberQuery query = new ProjectMemberQuery();
                            query.setProjectIds(new HashSet<>(allProjectIds));
                            query.setRole(ProjectMemberEnum.ProjectMemberRoleEnum.PROJECT_LEADER.getCode());
                            List<CrmProjectMemberVO> projectLeaderByProjectIds = crmProjectMemberService.getProjectLeaderByQueryParam(query);
                            Set<String> collect = projectLeaderByProjectIds.stream().filter(item -> jwtAccount.getPersonId().equals(item.getPersonId())).map(CrmProjectMemberVO::getProjectId).collect(Collectors.toSet());
                            dataScopeParam.setProjectIdList(collect);
                            redisTemplate.opsForValue().set("crm:projectRight:" + projectRight.operate() + ":" + jwtAccount.getPersonId(), JSONObject.toJSONString(collect), 15, TimeUnit.SECONDS);
                        }
                    }else{
                        throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                    }
                }
                AuthorizeContextHolder.putDataScopeParam(dataScopeParam);
                // 如果请求参数为BaseEntity或者CrmPageQuery，则也将dataScopeParam设置进去
                Object params = joinPoint.getArgs().length == 0 ? null : joinPoint.getArgs()[0];
                if (ObjectUtils.isNotEmpty(params) && params instanceof BaseEntity baseEntity) {
                    baseEntity.setDataScopeParam(dataScopeParam);
                } else if (ObjectUtils.isNotEmpty(params) && params instanceof CrmPageQuery crmPageQuery) {
                    crmPageQuery.setDataScopeParam(dataScopeParam);
                }
            }
        }
    }

    @After("@annotation(com.topsec.crm.project.core.annotation.ProjectRight)")
    public void removeDataScopeParam() {
        AuthorizeContextHolder.remove();
    }

    /**
     * 验证用户是否登录
     *
     * @return
     */
    private JwtLoginInfo checkLogin() {
        JwtLoginInfo jwtAccount = UserInfoHolder.getLoginInfo();
        if (ObjectUtils.isEmpty(jwtAccount) || ObjectUtils.isEmpty(jwtAccount.getAccountId())) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return jwtAccount;
    }
}
