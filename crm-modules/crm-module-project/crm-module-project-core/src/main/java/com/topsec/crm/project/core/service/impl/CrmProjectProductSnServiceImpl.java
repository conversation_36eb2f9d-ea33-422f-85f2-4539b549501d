package com.topsec.crm.project.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.flow.api.RemoteFlowService;
import com.topsec.crm.framework.common.bean.CrmProjectProductDeviceSnVO;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.Query;
import com.topsec.crm.product.api.RemoteProductSeparationRelService;
import com.topsec.crm.project.api.dto.ProjectProductSnPageQuery;
import com.topsec.crm.project.core.entity.*;
import com.topsec.crm.project.core.mapper.CrmProjectProductSnMapper;
import com.topsec.crm.project.core.service.ICrmProjectDirectlyService;
import com.topsec.crm.project.core.service.ICrmProjectProductOwnService;
import com.topsec.crm.project.core.service.ICrmProjectProductSnService;
import com.topsec.msg.api.client.NotifyClient;
import com.topsec.msg.common.constants.MessageConstant;
import com.topsec.msg.common.vo.NotifyMessageVo;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.vo.EmployeeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.collections4.CollectionUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 项目中产品序列号表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Service
public class CrmProjectProductSnServiceImpl extends ServiceImpl<CrmProjectProductSnMapper, CrmProjectProductSn> implements ICrmProjectProductSnService {

    @Autowired
    private ICrmProjectProductSnService crmProjectProductSnService;

    @Autowired
    private ICrmProjectProductOwnService crmProjectProductOwnService;

    @Autowired
    private RemoteFlowService remoteFlowService;

    @Autowired
    private ICrmProjectDirectlyService crmProjectDirectlyService;

    @Resource
    private TosEmployeeClient tosEmployeeClient;

    @Resource
    private NotifyClient notifyClient;

    @Resource
    private RemoteProductSeparationRelService remoteProductSeparationRelService;

    /**
     * 分页查询产品序列号
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils<CrmProjectProductDeviceSnVO> pageProductSn(ProjectProductSnPageQuery params) {
        String projectId = params.getProjectId();
        String type = params.getType();
        String psn = params.getPsn();
        String stuffCode = params.getStuffCode();
        Set<String> productRecordIds = params.getProductRecordIds();
        if (CollectionUtils.isNotEmpty(productRecordIds) && StringUtils.isNotBlank(projectId)) {
            LambdaQueryWrapper<CrmProjectProductSn> queryWrapper = new LambdaQueryWrapper<CrmProjectProductSn>()
                    .in(CollectionUtils.isNotEmpty(productRecordIds), CrmProjectProductSn::getRecordId, productRecordIds)
                    .eq(StringUtils.isNotBlank(type), CrmProjectProductSn::getType, type)
                    .eq(StringUtils.isNotBlank(psn), CrmProjectProductSn::getPsn, psn)
                    .orderByDesc(CollectionUtils.isEmpty(params.getOrders()), CrmProjectProductSn::getCreateTime, CrmProjectProductSn::getId);
            if(StringUtils.isNotBlank(stuffCode)){
                List<CrmProjectProductOwn> productOwns = crmProjectProductOwnService.list(crmProjectProductOwnService.buildDefaultQueryWrapper()
                        .eq(CrmProjectProductOwn::getProjectId, projectId)
                        .eq(CrmProjectProductOwn::getStuffCode, stuffCode)
                        .in(CrmProjectProductOwn::getId, productRecordIds));
                crmProjectProductOwnService.productOwnPostProcessing(productOwns);
                if(CollectionUtils.isNotEmpty(productOwns)){
                    queryWrapper.in(CrmProjectProductSn::getRecordId, productOwns.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toList()));
                }else{
                    queryWrapper.isNull(CrmProjectProductSn::getId);
                }
            }
            Page<CrmProjectProductSn> page = crmProjectProductSnService.page(new Query<CrmProjectProductSn>().getPage(params), queryWrapper);
            List<CrmProjectProductDeviceSnVO> list = new ArrayList<>();
            List<CrmProjectProductSn> records = page.getRecords();
            if (CollectionUtils.isNotEmpty(records)) {
                List<CrmProjectProductOwn> productOwns = crmProjectProductOwnService.list(crmProjectProductOwnService.buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId).in(CrmProjectProductOwn::getId, productRecordIds));
                crmProjectProductOwnService.productOwnPostProcessing(productOwns);
                records.forEach(crmProjectProductSn -> {
                    Optional<CrmProjectProductOwn> first = productOwns.stream().filter(m -> m.getId().equals(crmProjectProductSn.getRecordId())).findFirst();
                    first.ifPresent(crmProjectProductOwn -> list.add(CrmProjectProductDeviceSnVO.builder()
                            .id(crmProjectProductSn.getId())
                            .recordId(crmProjectProductSn.getRecordId())
                            .stuffCode(crmProjectProductOwn.getStuffCode())
                            .psn(crmProjectProductSn.getPsn())
                            .type(crmProjectProductSn.getType())
                            .productName(crmProjectProductOwn.getProductName())
                            .pnCode(crmProjectProductOwn.getPnCode())
                            .dealPrice(crmProjectProductOwn.getDealPrice()).build()));
                });
            }
            return new PageUtils<>(list, page.getTotal(), params.getPageSize(), params.getPageNum());
        }
        return new PageUtils<>();
    }

    /**
     * 根据产品记录ID查询产品序列号
     *
     * @param productRecordId
     * @return
     */
    @Override
    public List<CrmProjectProductSn> getProductSn(String productRecordId) {
        return super.list(new LambdaQueryWrapper<CrmProjectProductSn>().eq(CrmProjectProductSn::getRecordId, productRecordId));
    }

    /**
     * 根据产品记录ID查询产品序列号
     *
     * @param productRecordIds
     * @return
     */
    @Override
    public List<CrmProjectProductSn> getProductSnBatch(List<String> productRecordIds) {
        return super.list(new LambdaQueryWrapper<CrmProjectProductSn>().in(CrmProjectProductSn::getRecordId, productRecordIds));
    }

    /**
     * 根据产品记录ID查询产品序列号(借试用、借转销)
     *
     * @param productRecordId
     * @return
     */
    @Override
    public List<CrmProjectProductSn> getContractProductSn(String productRecordId) {
        return super.list(new LambdaQueryWrapper<CrmProjectProductSn>()
                .eq(CrmProjectProductSn::getRecordId, productRecordId)
                .and(entity->{
                    entity.eq(CrmProjectProductSn::getType,"借转销");
                    entity.or().eq(CrmProjectProductSn::getType,"借试用");
                }));
    }

    /**
     * 根据设备ID查询产品序列号(借试用、借转销)
     *
     * @param deviceIds
     * @return
     */
    @Override
    public List<CrmProjectProductSn> getProductSnByDeviceIds(Set<String> deviceIds) {
        return super.list(new LambdaQueryWrapper<CrmProjectProductSn>()
                .in(CrmProjectProductSn::getDeviceId, deviceIds));
    }

    /**
     * 保存产品序列号
     *
     * @param crmProjectProductSns
     * @param projectId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveProductSn(List<CrmProjectProductSn> crmProjectProductSns, String projectId) {
        // 清理旧数据
        Set<String> recordHasContract = new HashSet<>();
        List<CrmProjectProductOwn> allProduct = crmProjectProductOwnService.list(crmProjectProductOwnService.buildDefaultQueryWrapper().eq(CrmProjectProductOwn::getProjectId, projectId));
        crmProjectProductOwnService.productOwnPostProcessing(allProduct);
        if(CollectionUtils.isNotEmpty(allProduct)){
            Set<String> collect = allProduct.stream().map(CrmProjectProductOwn::getId).collect(Collectors.toSet());
            JsonObject<Set<String>> setJsonObject = remoteFlowService.queryRecordHasContract(projectId, collect);
            if(setJsonObject.isSuccess()){
                recordHasContract = setJsonObject.getObjEntity();
                if(CollectionUtils.isNotEmpty(crmProjectProductSns)){
                    Set<String> finalRecordHasContract = recordHasContract;
                    long count = crmProjectProductSns.stream().filter(m -> finalRecordHasContract.contains(m.getRecordId())).count();
                    if(count > 0){
                        throw new CrmException("项目存在合同，无法释放");
                    }
                }
                crmProjectProductSnService.remove(new LambdaQueryWrapper<CrmProjectProductSn>().in(CrmProjectProductSn::getRecordId, collect).notIn(CollectionUtils.isNotEmpty(recordHasContract), CrmProjectProductSn::getRecordId, recordHasContract));
                // 取消序列号绑定关系时，需要还原产品行中一体机标识
                crmProjectProductOwnService.lambdaUpdate().set(CrmProjectProductOwn::getFission,0).in(CrmProjectProductOwn::getId,collect).notIn(CollectionUtils.isNotEmpty(recordHasContract), CrmProjectProductOwn::getId, recordHasContract).update();
            }else{
                throw new CrmException("查询出错，请稍后重试");
            }
        }
        if(CollectionUtils.isNotEmpty(crmProjectProductSns)){
            List<CrmProjectProductSn> newPsns = crmProjectProductSns.stream().filter(crmProjectProductSn -> StringUtils.isNotBlank(crmProjectProductSn.getPsn())).toList();
            if(CollectionUtils.isNotEmpty(newPsns)){
                boolean b = crmProjectProductSnService.saveBatch(newPsns);
                if(b){
                    List<String> aioMachineStuffCodes = new ArrayList<>();
                    List<String> list = newPsns.stream().map(CrmProjectProductSn::getStuffCode).toList();
                    // 查询一体机物料代码查询关联的基础型物料代码
                    JsonObject<Map<String, List<String>>> crmProductSeparationRelListByStuffCode = remoteProductSeparationRelService.getCrmProductSeparationRelListByStuffCode(list);
                    if (crmProductSeparationRelListByStuffCode.isSuccess()) {
                        if(null != crmProductSeparationRelListByStuffCode.getObjEntity()){
                            Set<String> strings = crmProductSeparationRelListByStuffCode.getObjEntity().keySet();
                            aioMachineStuffCodes.addAll(strings);
                        }
                    }
                    for (CrmProjectProductSn crmProjectProductSn : newPsns){
                        // 是否是一体机
                        if(aioMachineStuffCodes.contains(crmProjectProductSn.getStuffCode())){
                            // 是否是关联的基础型物料代码
                            if(crmProductSeparationRelListByStuffCode.getObjEntity().getOrDefault(crmProjectProductSn.getStuffCode(),new ArrayList<>()).contains(crmProjectProductSn.getDeviceStuffCode())){
                                crmProjectProductOwnService.lambdaUpdate().set(CrmProjectProductOwn::getFission,2).eq(CrmProjectProductOwn::getId,crmProjectProductSn.getRecordId()).update();
                            }else{
                                crmProjectProductOwnService.lambdaUpdate().set(CrmProjectProductOwn::getFission,1).eq(CrmProjectProductOwn::getId,crmProjectProductSn.getRecordId()).update();
                            }
                        }
                    }
                }
                return b;
            }
        }
        return false;
    }

    /**
     * 释放产品序列号并通知
     *
     * @param sn
     * @param noticePersonId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean releaseProductSnAndSendNotice(String sn, String noticePersonId) {
        List<CrmProjectProductSn> list = crmProjectProductSnService.list(new LambdaQueryWrapper<CrmProjectProductSn>().eq(CrmProjectProductSn::getPsn, sn).eq(CrmProjectProductSn::getType,"重点行业"));
        if(CollectionUtils.isNotEmpty(list)){
            Set<String> recordIds = list.stream().map(CrmProjectProductSn::getRecordId).collect(Collectors.toSet());
            List<CrmProjectProductOwn> crmProjectProductOwns = crmProjectProductOwnService.listByIds(recordIds);
            if(CollectionUtils.isNotEmpty(crmProjectProductOwns)){
                for (CrmProjectProductOwn crmProjectProductOwn : crmProjectProductOwns) {
                    JsonObject<Boolean> booleanJsonObject = remoteFlowService.hasContractOfProjectByRecordIds(crmProjectProductOwn.getProjectId(), Collections.singleton(crmProjectProductOwn.getId()));
                    if(booleanJsonObject.isSuccess()){
                        if(booleanJsonObject.getObjEntity()){
                            // 项目存在合同，无法释放
                            throw new CrmException("产品序列号："+sn+"，项目存在合同，无法释放");
                        }else{
                            Set<String> collect = list.stream().filter(crmProjectProductSn -> crmProjectProductSn.getRecordId().equals(crmProjectProductOwn.getId())).map(CrmProjectProductSn::getId).collect(Collectors.toSet());
                            boolean b = crmProjectProductSnService.removeBatchByIds(collect, true);
                            if(b){
                                // 发送邮件通知
                                // 您XX项目【编号：MX2024012000325】的重点行业产品【物料代码：5101700330001、序列号：Q3797925855】出货权限已变更，可能导致无法发起合同评审，请及时登录CRM系统进行处理，如有任何疑问可联系合同管理部
                                CrmProjectDirectly byId = crmProjectDirectlyService.getById(crmProjectProductOwn.getProjectId());
                                if(null != byId){
                                    JsonObject<EmployeeVO> employee =  tosEmployeeClient.findById(noticePersonId);
                                    if(employee.isSuccess() && null != employee.getObjEntity()){
                                        //1.定义消息实体，设置邮箱标题和内容
                                        NotifyMessageVo entity = new NotifyMessageVo();
                                        entity.setSubject("重点行业出货序列号变更提醒");
                                        entity.setContent("您"+byId.getProjectName()+"项目【编号："+byId.getProjectNo()+"】的重点行业产品【物料代码："+crmProjectProductOwn.getStuffCode()+"、序列号："+sn+"】出货权限已变更，可能导致无法发起合同评审，请及时登录CRM系统进行处理，如有任何疑问可联系合同管理部");
                                        //发送邮件
                                        if(StringUtils.isNotBlank(employee.getObjEntity().getEmail())){
                                            //2.设置消息通知类型-email
                                            entity.setNotifyType(MessageConstant.EMAIL);
                                            //3.设置消息接收人列表
                                            entity.setReceiver(Collections.singletonList(employee.getObjEntity().getEmail()));
                                            notifyClient.send(JSONObject.toJSONString(entity));
                                        }

                                        //发送企微通知
                                        if(StringUtils.isNotBlank(employee.getObjEntity().getWorkWechatId())){
                                            //2.设置消息通知类型-email
                                            entity.setNotifyType(MessageConstant.WECHAT);
                                            //3.设置消息接收人列表
                                            entity.setReceiver(Collections.singletonList(employee.getObjEntity().getWorkWechatId()));
                                            entity.setNotifyCard(MessageConstant.NOTIFY_CARD_TH);
                                            entity.setWeChatType(MessageConstant.WE_CHAT_TEXT);
                                            notifyClient.send(JSONObject.toJSONString(entity));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 释放产品序列号并通知
     *
     * @param sns
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean releaseProductSnAndSendNoticeBatch(Map<String, List<String>> sns) {
        List<String> allSns = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : sns.entrySet()) {
            allSns.addAll(entry.getValue());
        }
        if(CollectionUtils.isNotEmpty(allSns)){
            List<CrmProjectProductSn> list = crmProjectProductSnService.list(new LambdaQueryWrapper<CrmProjectProductSn>().in(CrmProjectProductSn::getPsn, allSns).eq(CrmProjectProductSn::getType,"重点行业"));
            if(CollectionUtils.isNotEmpty(list)){
                Set<String> recordIds = list.stream().map(CrmProjectProductSn::getRecordId).collect(Collectors.toSet());
                List<CrmProjectProductOwn> crmProjectProductOwns = crmProjectProductOwnService.listByIds(recordIds);
                if(CollectionUtils.isNotEmpty(crmProjectProductOwns)){
                    // 校验是否已经发起合同
                    Map<String, List<CrmProjectProductOwn>> groupByProjectId = crmProjectProductOwns.stream().collect(Collectors.groupingBy(CrmProjectProductOwn::getProjectId));
                    for (Map.Entry<String, List<CrmProjectProductOwn>> entry : groupByProjectId.entrySet()) {
                        Set<String> productRecordIds = entry.getValue().stream().map(CrmProjectProductOwn::getId).collect(Collectors.toSet());
                        JsonObject<Boolean> booleanJsonObject = remoteFlowService.hasContractOfProjectByRecordIds(entry.getKey(), productRecordIds);
                        if(booleanJsonObject.isSuccess()) {
                            if (booleanJsonObject.getObjEntity()) {
                                // 项目存在合同，无法释放
                                throw new CrmException("存在产品序列号已经发起合同，无法释放");
                            }
                        }
                    }
                    // 释放设备并且发送邮件
                    for (Map.Entry<String, List<String>> entry : sns.entrySet()) {
                        List<CrmProjectProductSn> currentUserSns = list.stream().filter(crmProjectProductSn -> entry.getValue().contains(crmProjectProductSn.getPsn())).toList();
                        if(CollectionUtils.isEmpty(currentUserSns)){
                            continue;
                        }
                        Set<String> collect = currentUserSns.stream().map(CrmProjectProductSn::getId).collect(Collectors.toSet());
                        boolean b = crmProjectProductSnService.removeBatchByIds(collect, true);
                        if(b){
                            // 发送邮件通知
                            // 您XX项目【编号：MX2024012000325】的重点行业产品【物料代码：5101700330001、序列号：Q3797925855】出货权限已变更，可能导致无法发起合同评审，请及时登录CRM系统进行处理，如有任何疑问可联系合同管理部
                            Map<String, List<CrmProjectProductOwn>> noticeBase = crmProjectProductOwns.stream().filter(crmProjectProductOwn -> currentUserSns.stream().map(CrmProjectProductSn::getRecordId).toList().contains(crmProjectProductOwn.getId())).collect(Collectors.groupingBy(CrmProjectProductOwn::getProjectId));
                            noticeBase.forEach((projectId, crmProjectProductOwnsGroup) -> {
                                Set<String> recordIdsOfProject = crmProjectProductOwnsGroup.stream().map(crmProjectProductOwn -> crmProjectProductOwn.getId()).collect(Collectors.toSet());
                                CrmProjectDirectly byId = crmProjectDirectlyService.getById(projectId);
                                String device = currentUserSns.stream().filter(m -> recordIdsOfProject.contains(m.getRecordId())).map(m -> {
                                    return "物料代码：" + m.getStuffCode() + "、序列号：" + m.getPsn();
                                }).collect(Collectors.joining(","));
                                String content = "您"+byId.getProjectName()+"项目【编号："+byId.getProjectNo()+"】的重点行业产品【"+device+"】出货权限已变更，可能导致无法发起合同评审，请及时登录CRM系统进行处理，如有任何疑问可联系合同管理部";
                                JsonObject<EmployeeVO> employee =  tosEmployeeClient.findById(entry.getKey());
                                if(employee.isSuccess() && null != employee.getObjEntity()){
                                    //1.定义消息实体，设置邮箱标题和内容
                                    NotifyMessageVo entity = new NotifyMessageVo();
                                    entity.setSubject("重点行业出货序列号变更提醒");
                                    entity.setContent(content);
                                    //发送邮件
                                    if(StringUtils.isNotBlank(employee.getObjEntity().getEmail())){
                                        //2.设置消息通知类型-email
                                        entity.setNotifyType(MessageConstant.EMAIL);
                                        //3.设置消息接收人列表
                                        entity.setReceiver(Collections.singletonList(employee.getObjEntity().getEmail()));
                                        notifyClient.send(JSONObject.toJSONString(entity));
                                    }

                                    //发送企微通知
                                    if(StringUtils.isNotBlank(employee.getObjEntity().getWorkWechatId())){
                                        //2.设置消息通知类型-email
                                        entity.setNotifyType(MessageConstant.WECHAT);
                                        //3.设置消息接收人列表
                                        entity.setReceiver(Collections.singletonList(employee.getObjEntity().getWorkWechatId()));
                                        entity.setNotifyCard(MessageConstant.NOTIFY_CARD_TH);
                                        entity.setWeChatType(MessageConstant.WE_CHAT_TEXT);
                                        notifyClient.send(JSONObject.toJSONString(entity));
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 根据记录ID和序列号查询产品序列号
     *
     * @param recordId
     * @param sn
     * @return
     */
    @Override
    public CrmProjectProductSn queryProductSnByRecordIdAndSn(String recordId, String sn) {
        return crmProjectProductSnService.getOne(new LambdaQueryWrapper<CrmProjectProductSn>().eq(CrmProjectProductSn::getRecordId, recordId).eq(CrmProjectProductSn::getPsn, sn));
    }

    /**
     * 批量根据序列号查询产品序列号
     *
     * @param sns
     * @return
     */
    @Override
    public List<CrmProjectProductSn> getProductSnBatchBySns(List<String> sns) {
        if(CollectionUtils.isNotEmpty(sns)){
            return crmProjectProductSnService.list(new LambdaQueryWrapper<CrmProjectProductSn>().in(CrmProjectProductSn::getPsn, sns));
        }else{
            return List.of();
        }
    }
/**
     * 保存或更新产品序列号
     *
     * @param crmProjectProductSns
     * @param projectId
     * @return
     */
    /*@Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateProductSn(List<CrmProjectProductSn> crmProjectProductSns, String projectId) {
        if(CollectionUtils.isNotEmpty(crmProjectProductSns)){
            boolean b = crmProjectProductSnService.saveBatch(crmProjectProductSns);
            if(b){
                List<String> aioMachineStuffCodes = new ArrayList<>();
                List<String> list = crmProjectProductSns.stream().map(CrmProjectProductSn::getStuffCode).toList();
                // 查询一体机物料代码查询关联的基础型物料代码
                JsonObject<Map<String, List<String>>> crmProductSeparationRelListByStuffCode = remoteProductSeparationRelService.getCrmProductSeparationRelListByStuffCode(list);
                if (crmProductSeparationRelListByStuffCode.isSuccess()) {
                    if(null != crmProductSeparationRelListByStuffCode.getObjEntity()){
                        Set<String> strings = crmProductSeparationRelListByStuffCode.getObjEntity().keySet();
                        aioMachineStuffCodes.addAll(strings);
                    }
                }
                for (CrmProjectProductSn crmProjectProductSn : crmProjectProductSns){
                    // 是否是一体机
                    if(aioMachineStuffCodes.contains(crmProjectProductSn.getStuffCode())){
                        if(!crmProjectProductSn.getStuffCode().equals(crmProjectProductSn.getDeviceStuffCode())){
                            crmProjectProductOwnService.lambdaUpdate().set(CrmProjectProductOwn::getFission,2).eq(CrmProjectProductOwn::getId,crmProjectProductSn.getRecordId()).update();
                        }else{
                            crmProjectProductOwnService.lambdaUpdate().set(CrmProjectProductOwn::getFission,1).eq(CrmProjectProductOwn::getId,crmProjectProductSn.getRecordId()).update();
                        }
                    }
                }
            }
            return b;
        }
        return true;
    }*/
}
