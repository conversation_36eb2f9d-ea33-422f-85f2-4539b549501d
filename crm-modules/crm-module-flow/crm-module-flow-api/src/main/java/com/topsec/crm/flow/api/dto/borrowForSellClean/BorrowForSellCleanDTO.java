package com.topsec.crm.flow.api.dto.borrowForSellClean;

import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

@Data
public class BorrowForSellCleanDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private String id;

    @Schema(description = "流程实例ID")
    private String processInstanceId;

    @Schema(description = "流程状态")
    private Integer processState;

    @Schema(description = "流程编号")
    private String processNumber;

    @Schema(description = "销售人员")
    private String salesman;

    @Schema(description = "销售人员id")
    private String salesmanPersonId;

    @Schema(description = "销售部门")
    private String salesmanDept;


    @Schema(description = "当前审批节点")
    private Set<ApproveNode> approvalNode;



    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建用户")
    private String createUser;

    @Schema(description = "更新用户")
    private String updateUser;

}
