package com.topsec.crm.flow.api;

import com.topsec.crm.flow.api.dto.BorrowForProbationDeviceOccupyStateVO;
import com.topsec.crm.flow.api.dto.ProductLockStateVO;
import com.topsec.crm.flow.api.dto.biddingMonitoring.VO.BiddingMonitoringInfoListVO;
import com.topsec.crm.flow.api.dto.biddingMonitoring.VO.BiddingMonitoringInfoVO;
import com.topsec.crm.flow.api.dto.militaryinspection.MilitaryProductInspectionVO;
import com.topsec.crm.framework.common.ProcessExtensionInfoQuery;
import com.topsec.crm.framework.common.bean.CrmProjectProgressVO;
import com.topsec.crm.framework.common.bean.ProcessExtensionInfoVO;
import com.topsec.crm.framework.common.constant.ServiceNameConstants;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.enums.ProcessDefinitionKeyEnum;
import com.topsec.query.CommonProcessQuery;
import com.topsec.tbscommon.JsonObject;
import com.topsec.vo.TfsFormContentVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 流程提供给其他模块调用的rpc服务
 */
@FeignClient(contextId = "remoteFlowService", value = ServiceNameConstants.FLOW_SERVICE,url = ServiceNameConstants.FLOW_SERVICE_URL)
public interface RemoteFlowService {

    /**
     * 返回锁定状态信息
     * @return 返回锁定状态信息
     */
    @GetMapping("/commonFlow/queryLockState")
    JsonObject<Map<String, ProductLockStateVO>> queryLockState(@RequestParam String projectId, @RequestParam Set<String> productRecordIdSet);

    /**
     * 返回借用试用产品占用状态信息
     * @return 返回借用试用产品占用状态信息
     */
    @PostMapping("/commonFlow/queryBorrowForProbationDeviceOccupyState")
    JsonObject<Map<String, BorrowForProbationDeviceOccupyStateVO>> queryBorrowForProbationDeviceOccupyState(@RequestBody Set<String> deviceIds);

    @GetMapping("/commonFlow/isProjectProductLocked")
    JsonObject<Boolean> isProjectProductLocked(@RequestParam String projectId);

    @PostMapping("/commonFlow/hasLockedProductOfProject")
    JsonObject<Boolean>  hasLockedProductOfProject(@RequestParam String projectId, @RequestBody Set<String> productRecordIdSet);

    // 查询项目流程分页列表
    @PostMapping({"/process/queryProcessPage"})
    JsonObject<PageUtils<TfsFormContentVo>> queryProcessPage(@RequestBody CommonProcessQuery commonProcessQuery);

    @GetMapping("/commonFlow/queryProjectFlowStatus")
    JsonObject<List<CrmProjectProgressVO>> queryProjectFlowStatus(@RequestParam String projectId);

    @GetMapping("/commonFlow/hasContractOfProject")
    JsonObject<Boolean> hasContractOfProject(@RequestParam String projectId);

    @PostMapping("/commonFlow/hasContractOfProjectByRecordIds")
    JsonObject<Boolean> hasContractOfProjectByRecordIds(@RequestParam String projectId, @RequestBody Set<String> productRecordIdSet);

    @GetMapping("/commonFlow/hasPriceReviewUnderwayOfProject")
    JsonObject<Boolean> hasPriceReviewUnderwayOfProject(@RequestParam String projectId);

    @PostMapping("/commonFlow/queryRecordHasContract")
    JsonObject<Set<String>> queryRecordHasContract(@RequestParam String projectId, @RequestBody Set<String> productRecordIdSet);

    @GetMapping("/hidden/militaryInspection/militaryInspectionDetailInfo")
    JsonObject<MilitaryProductInspectionVO> militaryInspectionDetailInfo(@RequestParam String processInstanceId);


    @PostMapping("/hidden/militaryInspection/militaryInspectionDetailInfoByProcessIds")
    @Operation(summary = "根据流程id批量获取军品检验信息")
    JsonObject<List<MilitaryProductInspectionVO>> militaryInspectionDetailInfoByProcessIds(@RequestBody List<String> processInstanceIds);
    @GetMapping("/hidden/militaryInspection/existsMilitaryProductInspectionProcess")
    JsonObject<Boolean> existsMilitaryProductInspectionProcess(@RequestParam String projectId);

    @PostMapping("/commonFlow/listProcessExtensionInfo")
    JsonObject<List<ProcessExtensionInfoVO>> listProcessExtensionInfo(@RequestBody  ProcessExtensionInfoQuery query);

    @PostMapping("/commonFlow/checkProjectHasProcess")
    JsonObject<Boolean> checkProjectHasProcess(@RequestParam String projectId,@RequestParam(required = false) List<ProcessDefinitionKeyEnum> excludeProcessDefinitionKeyEnumList);


    @PostMapping("/hidden/biddingMonitoring/insert")
    @Operation(summary = "招投标信息监测新增")
    JsonObject<Boolean> insert(@Valid @RequestBody BiddingMonitoringInfoVO biddingMonitoringInfoVo);

    @PutMapping("/hidden/biddingMonitoring/updateByInfoId")
    @Operation(summary = "招投标信息监测根据infoId修改")
    JsonObject<Boolean> updateByInfoId(@Valid @RequestBody BiddingMonitoringInfoVO biddingMonitoringInfoVo);

    @GetMapping("/hidden/biddingMonitoring/getByInfoId")
    @Operation(summary = "招投标信息监测根据infoId查询")
    JsonObject<BiddingMonitoringInfoVO> getByInfoId(@Valid @RequestBody BiddingMonitoringInfoVO biddingMonitoringInfoVo);

    @GetMapping("/hidden/biddingMonitoring/getCurrentCursorMark")
    @Operation(summary = "招投标信息监测根据infoId查询")
    JsonObject<String> getCurrentCursorMark();

    @GetMapping("/hidden/biddingMonitoring/getAllBiddingInfos")
    @Operation(summary = "获取所有已发起流程的招投标信息")
    JsonObject<List<BiddingMonitoringInfoListVO>> getAllBiddingInfos();

}
