<!DOCTYPE html>
<html lang="zh"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于${projectName}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ccc;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            font-size: 25px;
        }
        .Team-Info,.Team-appointment {
            width: 100%;
            border-collapse: collapse;
            margin-top: 14px;
        }
        .Team-Info th {
            padding: 10px 2px 10px 2px;
            text-align: center;
            border-bottom: 1px solid #ddd;
        }
        .Team-appointment th,td {
            padding: 10px;
            text-align: center;
            border: 1px solid #f1f1f1;
        }
        p{
            font-size: 15px;
            color: #666666;
        }
        p span{
            font-weight: bold;
            font-size: 15px;
            color: #666666;
        }
        h5{
            font-size: 17px;
            color: #333333;
            display: flex;
        }
        th{
            font-size: 15px;
            color: #333333;
            background: #f5f5f5;
            text-align: center;
        }
        td{
            font-size: 15px;
            color: #666666;
            text-align: center;
        }
        .info {
            display: flex;
            flex-direction: column;
            height: 19px;
            width: 5px;
            background: #FF0000;
            margin: 2px 10px 0px 0px;
        }
        .company {
            text-align: right;
            font-size: 13px;
            color: #666;
        }
        .td-left{
            text-align: left !important;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>关于${projectName}团队组建任命通知</h1>

    <h5><div class="info"></div>项目背景</h5>
    <p><span>项目名称:</span> ${projectName}</p>
    <p><span>项目ID:</span> ${projectNumber}</p>
    <p><span>项目总结:</span> ${projectSummary}</p>
    <h5><div class="info"></div>团队资料</h5>
    <table class="Team-Info">
        <tbody><tr>
            <th>团队组建单号</th>
            <td>${teamBuildingNum}</td>
            <th>预计验收时间</th>
            <td>${acceptanceTime}</td>
            <th>项目类型</th>
            <td>${projectType}</td>
        </tr>
        <tr>
            <th>售前人员</th>
            <td>${preSales}</td>
            <th>涉及的产品和服务</th>
            <td>${productsAndServicesInvolved}</td>
            <th>预计涉及部门</th>
            <td>${departmentsInvolvedList}</td>
        </tr>
        <tr>
            <th>预计涉及分支机构</th>
            <td>${branchInvolvedList}</td>
            <th>提供项目经理部门</th>
            <td>${projectManagerDeptName}</td>
            <th></th>
            <td></td>
        </tr>
        <tr>
            <th>项目期望及团队需求</th>
            <td colspan="3">${projectExpectationsAndTeamRequirements}</td>
            <th></th>
            <td></td>
        </tr>
        </tbody></table>
    <h5><div class="info"></div>团队任命</h5>
    <table class="Team-appointment">
        <tbody><tr>
            <th style="width:15%;">成员姓名</th>
            <th style="width:20%;">所属部门</th>
            <th style="width:15%;">项目角色</th>
            <th style="width:50%;"> 职责描述</th>
        </tr>
        <#list teamAppointmentMails as t>
            <tr>
                <td style="width:15%;">${t.name}</td>
                <td style="width:20%;">${t.dept}</td>
                <td style="width:15%;">${t.projectRole}</td>
                <td style="width:50%;">${t.jobDescription}</td>
            </tr>
        </#list>
        </tbody></table>
    <p class="company">${companyName}<br>${nowDate}</p>
</div>

</body></html>
