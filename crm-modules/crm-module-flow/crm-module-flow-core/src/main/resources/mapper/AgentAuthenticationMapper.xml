<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentAuthenticationMapper">

    <resultMap type="com.topsec.crm.flow.core.entity.AgentAuthentication" id="AgentAuthenticationResult">
        <result property="id" column="id" />
        <result property="processInstanceId" column="process_instance_id" />
        <result property="processState" column="process_state" />
        <result property="processNumber" column="process_number" />
        <result property="agentId" column="agent_id" />
        <result property="agentName" column="agent_name" />
        <result property="agentClassification" column="agent_classification" />
        <result property="creditLevel" column="credit_level" />
        <result property="keyExpandDirection" column="key_expand_direction" />
        <result property="agentProductRange" column="agent_product_range" />
        <result property="applyCentralProcurement" column="apply_central_procurement" />
        <result property="advancePayment" column="advance_payment" />
        <result property="taskAmount" column="task_amount" />
        <result property="arrearsAmount" column="arrears_amount" />
        <result property="overdueReceivable" column="overdue_receivable" />
        <result property="tspa" column="tspa" />
        <result property="tspp" column="tspp" />
        <result property="tscp" column="tscp" />
        <result property="tsca" column="tsca" />
        <result property="generalAgentId" column="general_agent_id" />
        <result property="generalAgentName" column="general_agent_name" />
        <result property="saleId" column="sale_id" />
        <result property="saleDept" column="sale_dept" />
        <result property="saleName" column="sale_name" />
        <result property="salePhone" column="sale_phone" />
        <result property="saleEmail" column="sale_email" />
        <result property="agentDirectorId" column="agent_director_id" />
        <result property="agentDirectorName" column="agent_director_name" />
        <result property="agentDirectorDept" column="agent_director_dept" />
        <result property="agentDirectorPhone" column="agent_director_phone" />
        <result property="agentDirectorEmail" column="agent_director_email" />
        <result property="agentStartTime" column="agent_start_time" />
        <result property="agentEndTime" column="agent_end_time" />
        <result property="effectiveTime" column="effective_time" />
        <result property="agentContactId" column="agent_contact_id" />
        <result property="delFlag" column="del_flag" />
        <result property="createUser" column="create_user" />
        <result property="updateUser" column="update_user" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectAgentAuthenticationVo">
        select id,process_instance_id,process_state,process_number,agent_id,agent_name,agent_classification,credit_level,
        key_expand_direction,agent_product_range,apply_central_procurement,advance_payment,task_amount,arrears_amount,
        overdue_receivable,tspa,tspp,tscp,tsca,general_agent_id,general_agent_name,sale_id,sale_dept,sale_name,sale_phone,
        sale_email,agent_director_id,agent_director_name,agent_director_dept,agent_director_phone,agent_director_email,
        agent_start_time,agent_end_time,del_flag,create_user,update_user,create_time,update_time,effective_time,agent_contact_id
        from agent_authentication
    </sql>

    <select id="selectAgentAuthenticationInfo" parameterType="String" resultMap="AgentAuthenticationResult">
        select agent_id,agent_classification,task_amount,effective_time
        From (select agent_id,agent_classification,task_amount,effective_time,ROW_NUMBER() over(partition by agent_id
        order by effective_time desc ) rn
        from agent_authentication where del_flag = 0 and agent_id=#{agent_id} and YEAR (NOW())-YEAR (effective_time) =1 LIMIT 1) a1
        where a1.rn=1
    </select>

    <select id="selectAgentAuthenticationManage" parameterType="String" resultType="com.topsec.crm.flow.api.dto.agentauthentication.AgentAuthenticationManageVO">
        select agent_id,process_instance_id,create_time,create_user,
               case when agent_authentication.process_state = 1 then 2
                    when agent_authentication.process_state = 2 then 3
                    when DATEDIFF(agent_end_time,NOW()) > 0 then 4 end as authentication_state
        from agent_authentication
        where del_flag=0 and process_instance_id=#{processInstanceId}
    </select>

    <select id="selectAgentAuthentication" parameterType="String" resultMap="AgentAuthenticationResult">
        select id,process_instance_id
        from agent_authentication
        where del_flag = 0 and agent_id=#{generalAgentId} and effective_time is not null
        order by effective_time desc
        limit 1
    </select>

    <select id="selectHistoryAgentAuthentication" parameterType="String" resultType="com.topsec.crm.flow.api.dto.agentauthentication.AgentAuthenticationManageVO">
        select agent_id,process_instance_id,process_number,create_time,create_user,agent_classification,agent_start_time,agent_end_time
        from agent_authentication
        where del_flag=0 and agent_id=#{agentId} and process_instance_id != #{processInstanceId}
        order by effective_time desc
    </select>

    <select id="selectAgentAuthenticationProvince" parameterType="String" resultMap="AgentAuthenticationResult">
        select agent_authentication.id,agent_authentication.process_instance_id
        from agent_authentication
        left join agent_authentication_area on agent_authentication.process_instance_id = agent_authentication_area.process_instance_id
        where agent_authentication.del_flag=0 and agent_authentication_area.del_flag=0 and agent_classification = 1 and YEAR (effective_time) = YEAR(NOW())
        and area_code = #{areaCode}  and area_code != '110000'
        limit 1
    </select>

    <select id="selectAgentAuthenticationThisYear" parameterType="String" resultMap="AgentAuthenticationResult">
        select  id, agent_id, agent_name,agent_classification,process_instance_id,sale_id
        from agent_authentication
        where del_flag = 0 and agent_id=#{agentId} and effective_time is not null and YEAR (effective_time) = YEAR(NOW())
        order by effective_time desc
        limit 1
    </select>

    <select id="selectAgentAuthThisYearByAgentIds" parameterType="java.util.List" resultMap="AgentAuthenticationResult">
        select  id, agent_id, agent_name,agent_classification,process_instance_id,sale_id
        from agent_authentication
        where del_flag = 0 and agent_id in
        <foreach item="agentIds" collection="agentIds" open="(" separator="," close=")">
            #{agentIds}
        </foreach>
        and effective_time is not null and YEAR (effective_time) = YEAR(NOW())
        order by effective_time desc
    </select>

    <select id="selectPerformanceReportSupplierNameByProvinceCode" parameterType="String" resultMap="AgentAuthenticationResult">
        select id, agent_id, agent_name,agent_classification,process_instance_id
        from agent_authentication
        where del_flag = 0 and (agent_classification = 1 or agent_classification = 2) and effective_time is not null and YEAR (effective_time) = YEAR(NOW())
        order by effective_time desc
    </select>

    <select id="selectAgentPaymentDisburseList" parameterType="com.topsec.crm.flow.api.dto.agentauthentication.AgentPaymentDisburseQuery" resultType="com.topsec.crm.flow.api.dto.agentauthentication.AgentPaymentDisburseVO">
        select q1.agent_id,q1.agent_name,q1.agent_classification,general_agent_id,q1.general_agent_name,q5.agent_classification as level,advance_payment,
               ifnull(payout_amount,0)-ifnull(q4.used_prepaid_amount,0) as advance_payment_balance,
               case when ifnull(payout_amount,0)-ifnull(q4.used_prepaid_amount,0) >= advance_payment then 0
                   else advance_payment-ifnull(payout_amount,0)+ifnull(q4.used_prepaid_amount,0) end as pay_advance_payment,
               ifnull(q3.used_prepaid_amount,0) as occupation_payment,
               ifnull(payout_amount,0)-ifnull(q4.used_prepaid_amount,0)-ifnull(q3.used_prepaid_amount,0) as available_payment
        from (
            select agent_id,agent_name,agent_classification,general_agent_id,advance_payment,sale_id,general_agent_name
            From (select agent_id,agent_name,agent_classification,general_agent_id,advance_payment,sale_id,general_agent_name,ROW_NUMBER() over(partition by agent_id ) rn
                  from agent_authentication where del_flag = 0 and effective_time is not null and advance_payment is not null) a1
            where a1.rn=1) as q1
        left join (
            select agent_id,agent_name,agent_classification
            from agent_authentication
            where del_flag = 0 and effective_time is not null and agent_classification in (1,2) and YEAR (effective_time) = YEAR(NOW())
        ) as q5 on q1.general_agent_id=q5.agent_id
        left join (
            select payout_company_id,recipient_company_id,IFNULL(sum(payout_amount),0) as payout_amount
            from agent_auth_payment_disburse
            where process_state = 2 and payment_nature=1
            group by payout_company_id,recipient_company_id
        ) as q2 on q1.agent_id=q2.payout_company_id and q1.general_agent_id=q2.recipient_company_id
        left join (
            select channel_company_id,supplier_id,IFNULL(sum(used_prepaid_amount),0) as used_prepaid_amount
            from performance_report
            where performance_report.process_state=1 and del_flag=0 and used_prepaid_amount is not null
            group by channel_company_id,supplier_id
        ) as q3 on q1.agent_id=q3.channel_company_id and q1.general_agent_id=q3.supplier_id
        left join (
            select channel_company_id,supplier_id,IFNULL(sum(used_prepaid_amount),0) as used_prepaid_amount
            from performance_report
            where performance_report.process_state=2 and del_flag=0 and used_prepaid_amount is not null
            group by channel_company_id,supplier_id
        ) as q4 on q1.agent_id=q4.channel_company_id and q1.general_agent_id=q4.supplier_id
        where 1=1
        <if test="generalAgentName != null and generalAgentName != ''">
            and q1.general_agent_name like '%' #{generalAgentName} '%'
        </if>
        <if test="agentName != null and agentName != ''">
            and q1.agent_name like '%' #{agentName} '%'
        </if>
        <if test="agentClassification != null and agentClassification != ''">
            and q5.agent_classification = #{agentClassification}
        </if>
        <if test="authenticationClassification != null and authenticationClassification != ''">
            and q1.agent_classification = #{authenticationClassification}
        </if>
        <if test="agentId != null and agentId != ''">
            and (q1.general_agent_id = #{agentId} or q1.agent_id = #{agentId})
        </if>
        <if test="salesId != null and salesId != ''">
            and q1.sale_id in
            <foreach item="salesId" collection="salesId" open="(" separator="," close=")">
                #{salesId}
            </foreach>
        </if>
    </select>
</mapper>
