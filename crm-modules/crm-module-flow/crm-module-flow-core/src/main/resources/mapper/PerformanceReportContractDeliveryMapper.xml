<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PerformanceReportContractDeliveryMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PerformanceReportContractDelivery">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="consigneeId" column="consignee_id" jdbcType="VARCHAR"/>
            <result property="deliveryMethod" column="delivery_method" jdbcType="TINYINT"/>
            <result property="deliveryDate" column="delivery_date" jdbcType="DATE"/>
            <result property="deliveryLeadTime" column="delivery_lead_time" jdbcType="INTEGER"/>
            <result property="isContractWaived" column="is_contract_waived" jdbcType="TINYINT"/>
            <result property="originalContractNo" column="original_contract_no" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,consignee_id,delivery_method,
        delivery_date,delivery_lead_time,is_contract_waived,
        original_contract_no,remarks,process_instance_id
    </sql>
</mapper>
