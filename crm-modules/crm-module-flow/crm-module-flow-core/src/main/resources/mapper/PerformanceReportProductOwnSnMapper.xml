<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PerformanceReportProductOwnSnMapper">
    
    <resultMap type="com.topsec.crm.flow.core.entity.PerformanceReportProductOwnSn" id="PerformanceReportProductSnResult">
        <result property="id"    column="id"    />
        <result property="performanceReportProductOwnId"    column="performance_report_product_own_id"    />
        <result property="psn"    column="psn"    />
        <result property="productDeliveryId"    column="product_delivery_id"    />
        <result property="storageTime"    column="storage_time"    />
        <result property="deliveryId"    column="delivery_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPerformanceReportProductSnVo">
        select id, performance_report_product_own_id, psn, product_delivery_id,storage_time,delivery_id, del_flag, create_user, update_user, create_time, update_time from performance_report_product_own_sn
    </sql>

    <select id="selectProductSnStatistics" parameterType="String" resultType="int">
        select count(psn) as productSnTotalNum
        from performance_report_product_own_sn
        left join performance_report_product_own on performance_report_product_own_sn.performance_report_product_own_id=performance_report_product_own.id
        where performance_report_product_own_sn.del_flag=0 and performance_report_product_own.del_flag=0 and process_instance_id = #{id}
    </select>

    <update id="deletePerformanceReportProductSn" parameterType="String">
        update performance_report_product_own_sn set del_flag=1
        where del_flag=0 and performance_report_product_own_id=#{performanceReportProductId}
    </update>

    <select id="selectPerformanceReportProductSn" parameterType="String" resultMap="PerformanceReportProductSnResult">
        select psn,storage_time
        from performance_report_product_own_sn
        left join performance_report_product_own on performance_report_product_own_sn.performance_report_product_own_id=performance_report_product_own.id
        where performance_report_product_own_sn.del_flag=0 and performance_report_product_own.del_flag=0 and process_instance_id = #{processInstanceId}
    </select>

    <select id="selectProductDeliverySnInfo" resultType="com.topsec.crm.flow.api.dto.performancereport.ContractDeliveryProductSnVO">
        select performance_report_product_own_sn.performance_report_product_own_id as performance_report_product_id, psn
        from performance_report_product_own_sn
        left join performance_report_product_own on performance_report_product_own_sn.performance_report_product_own_id=performance_report_product_own.id
        where performance_report_product_own_sn.del_flag=0 and performance_report_product_own.del_flag=0 and performance_report_product_own.id in
        <foreach item="performanceReportProductId" collection="performanceReportProductIds" open="(" separator="," close=")">
            #{performanceReportProductId}
        </foreach>
        <if test="psnList != null and psnList != ''">
            and psn not in
            <foreach item="psn" collection="psnList" open="(" separator="," close=")">
                #{psn}
            </foreach>
        </if>
    </select>
</mapper>