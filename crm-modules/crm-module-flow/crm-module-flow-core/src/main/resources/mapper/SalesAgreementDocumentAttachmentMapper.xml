<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.SalesAgreementDocumentAttachmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.SalesAgreementDocumentAttachment">
        <id column="id" property="id" />
        <result column="agreement_document_id" property="agreementDocumentId" />
        <result column="file_type" property="fileType" />
        <result column="duplicate" property="duplicate" />
        <result column="copies" property="copies" />
        <result column="doc_id" property="docId" />
        <result column="file_name" property="fileName" />
        <result column="doc_size" property="docSize" />
        <result column="remark" property="remark" />
        <result column="del_flag" property="delFlag" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        sada.id,  sada.agreement_document_id,  sada.file_type,  sada.duplicate,  sada.copies,  sada.doc_id,  sada.file_name,  sada.doc_size,  sada.remark,  sada.del_flag,  sada.create_user,  sada.update_user, sada.create_time,  sada.update_time
    </sql>

    <select id="selectAttachmentList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from sales_agreement_document_attachment sada , sales_agreement_document sad where
        sada.agreement_document_id = sad.id and sad.process_instance_id = #{processInstanceId} and  sada.del_flag =0
    </select>
</mapper>
