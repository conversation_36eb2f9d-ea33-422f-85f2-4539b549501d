<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ImpunityContractMapper">
  <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ImpunityContract">
    <!--@mbg.generated-->
    <!--@Table impunity_contract-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="contract_suspension_id" jdbcType="VARCHAR" property="contractSuspensionId" />
    <result column="reason" jdbcType="LONGVARCHAR" property="reason" />
    <result column="remark" jdbcType="LONGVARCHAR" property="remark" />
    <result column="free_date" jdbcType="DATE" property="freeDate" />
    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, contract_suspension_id, reason, remark, free_date, process_instance_id
  </sql>

  <select id="page" resultType="com.topsec.crm.flow.api.dto.impunity.ImpunityContractVO">
      select impunity_contract.contract_number          as contractNumber,

             impunity_contract.reason                                      as reason,
             impunity_contract.remark                                      as remark,
             impunity_contract.free_date                                   as freeDate,
             impunity.process_instance_id                                  as processInstanceId,
             impunity.process_number                                       as processNumber,
             impunity.process_state                                        as processState,
             impunity.dept_id   as contractOwnerDeptId,
             impunity.dept_name as contractOwnerDeptName,
             impunity.`periods`                                            as periods
      from impunity_contract
          inner join impunity on impunity.process_instance_id = impunity_contract.process_instance_id
      <where>
          <if test="query.deptIds != null and query.deptIds.size() > 0">
              and impunity.dept_id in
              <foreach item="deptIdItem" collection="query.deptIds" open="(" separator="," close=")">
                  #{deptIdItem}
              </foreach>
          </if>
          <if test="query.contractNumber != null and query.contractNumber != ''">
              and impunity_contract.contract_number like concat('%', #{query.contractNumber}, '%')
          </if>
          <if test="query.contractCompanyName != null and query.contractCompanyName != ''">
              and EXISTS (
                  SELECT 1
                  FROM contract_review_main inner join contract_review_sign_contract on
                      contract_review_main.id=contract_review_sign_contract.contract_review_main_id
                  WHERE contract_review_main.contract_number = impunity_contract.contract_number
                  and contract_review_sign_contract.contract_company_name like concat('%', #{query.contractCompanyName}, '%')
              )
          </if>
      </where>
      order by impunity.create_time desc
  </select>

  <select id="queryByProcessInstanceId" resultType="com.topsec.crm.flow.api.dto.impunity.ImpunityContractVO">
      select impunity_contract.contract_number          as contractNumber,
             impunity_contract.reason                                      as reason,
             impunity_contract.remark                                      as remark,
             impunity_contract.free_date                                   as freeDate,
             impunity_contract.id                                          as id
      from impunity_contract
      where process_instance_id=#{processInstanceId}
  </select>
</mapper>
