<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.topsec.crm.flow.core.mapper.TargetedInventoryPreparationProductQuashMapper">

<!--  <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.TargetedInventoryPreparationProductQuash">-->
<!--    <id column="id" jdbcType="VARCHAR" property="id"/>-->
<!--    <result column="targeted_inventory_preparation_quash_id" jdbcType="VARCHAR" property="targetedInventoryPreparationQuashId"/>-->
<!--    <result column="targeted_inventory_preparation_product_id" jdbcType="VARCHAR" property="targetedInventoryPreparationProductId"/>-->
<!--    <result column="crm_project_product_own_id" jdbcType="VARCHAR" property="crmProjectProductOwnId"/>-->
<!--    <result column="whether_military_quality_control" jdbcType="TINYINT" property="whetherMilitaryQualityControl"/>-->
<!--    <result column="crm_product_id" jdbcType="VARCHAR" property="crmProductId"/>-->
<!--    <result column="product_lic" jdbcType="INTEGER" property="productLic"/>-->
<!--    <result column="product_num" jdbcType="INTEGER" property="productNum"/>-->
<!--    <result column="targeted_inventory_preparation_erp_state" jdbcType="TINYINT" property="targetedInventoryPreparationErpState"/>-->
<!--    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>-->
<!--    <result column="create_user" jdbcType="VARCHAR" property="createUser"/>-->
<!--    <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>-->
<!--    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>-->
<!--    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>-->
<!--  </resultMap>-->
<!--  <sql id="Base_Column_List">-->
<!--    id, targeted_inventory_preparation_quash_id, targeted_inventory_preparation_product_id, crm_project_product_own_id, whether_military_quality_control, crm_product_id, product_lic, -->
<!--	product_num, targeted_inventory_preparation_erp_state, del_flag, create_user, update_user, create_time, update_time-->
<!--  </sql>-->
</mapper>
