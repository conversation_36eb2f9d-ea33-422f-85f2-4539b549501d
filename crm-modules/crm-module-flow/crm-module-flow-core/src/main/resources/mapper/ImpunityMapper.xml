<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ImpunityMapper">
  <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.Impunity">
    <!--@mbg.generated-->
    <!--@Table impunity-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId" />
    <result column="process_number" jdbcType="VARCHAR" property="processNumber" />
    <result column="process_state" jdbcType="BOOLEAN" property="processState" />
    <result column="periods" jdbcType="DATE" property="periods" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, process_instance_id, process_number, process_state, periods, dept_name, dept_id,
    contract_number, contract_time, contract_company_name, contract_company_id, contract_amount,
    returned_amount, overdue_amount, free_date, create_time, create_user, update_time,
    update_user
  </sql>
</mapper>
