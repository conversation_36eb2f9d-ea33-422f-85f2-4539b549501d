<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.IndustryPaymentReceiptMapper">
  <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.IndustryPaymentReceipt">
    <!--@mbg.generated-->
    <!--@Table industry_payment_receipt-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="payer_company" jdbcType="VARCHAR" property="payerCompany" />
    <result column="payer_company_id" jdbcType="VARCHAR" property="payerCompanyId" />
    <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount" />
    <result column="recipient_company" jdbcType="VARCHAR" property="recipientCompany" />
    <result column="recipient_company_id" jdbcType="VARCHAR" property="recipientCompanyId" />
    <result column="arrival_time" jdbcType="TIMESTAMP" property="arrivalTime" />
    <result column="remarks" jdbcType="LONGVARCHAR" property="remarks" />
    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId" />
    <result column="process_state" jdbcType="BOOLEAN" property="processState" />
    <result column="process_number" jdbcType="INTEGER" property="processNumber" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, payer_company, payer_company_id, payment_amount, recipient_company, recipient_company_id, 
    arrival_time, remarks, process_instance_id, process_state, process_number, create_time, 
    create_user, update_time, update_user
  </sql>
</mapper>