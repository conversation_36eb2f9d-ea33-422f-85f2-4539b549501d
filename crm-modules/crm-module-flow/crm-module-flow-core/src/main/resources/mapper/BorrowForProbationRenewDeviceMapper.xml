<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForProbationRenewDeviceMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForProbationRenewDevice">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="borrowForProbationRenewId" column="borrow_for_probation_renew_id" jdbcType="VARCHAR"/>
            <result property="projectProbationDeviceId" column="project_probation_device_id" jdbcType="VARCHAR"/>
            <result property="estimatedReturnTime" column="estimated_return_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,borrow_for_probation_renew_id,project_probation_device_id,estimated_return_time,
        create_time,update_time,create_user,
        update_user,del_flag
    </sql>
</mapper>
