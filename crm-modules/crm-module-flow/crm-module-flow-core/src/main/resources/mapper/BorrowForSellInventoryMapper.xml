<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForSellInventoryMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForSellInventory">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="psn" column="psn" jdbcType="VARCHAR"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="productModel" column="product_model" jdbcType="VARCHAR"/>
            <result property="productPn" column="product_pn" jdbcType="VARCHAR"/>
            <result property="corePart" column="core_part" jdbcType="VARCHAR"/>
            <result property="unit" column="unit" jdbcType="VARCHAR"/>
            <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
            <result property="salesUnit" column="sales_unit" jdbcType="VARCHAR"/>
            <result property="salesPerson" column="sales_person" jdbcType="VARCHAR"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="finalCustomer" column="final_customer" jdbcType="VARCHAR"/>
            <result property="borrower" column="borrower" jdbcType="VARCHAR"/>
            <result property="loanContract" column="loan_contract" jdbcType="VARCHAR"/>
            <result property="isDirectSign" column="is_direct_sign" jdbcType="TINYINT"/>
            <result property="loanType" column="loan_type" jdbcType="VARCHAR"/>
            <result property="expectedReturnDate" column="expected_return_date" jdbcType="VARCHAR"/>
            <result property="locationCode" column="location_code" jdbcType="VARCHAR"/>
            <result property="locationDesc" column="location_desc" jdbcType="VARCHAR"/>
            <result property="subinventoryType" column="subinventory_type" jdbcType="VARCHAR"/>
            <result property="handleTime" column="handle_time" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,psn,material_code,product_name,
        product_model,product_pn,core_part,
        unit,unit_price,sales_unit,
        sales_person,project_id,final_customer,
        borrower,loan_contract,is_direct_sign,
        loan_type,expected_return_date,location_code,
        location_desc,subinventory_type,handle_time,
        create_time,update_time,create_user,
        update_user,del_flag
    </sql>
</mapper>
