<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.IndustryPaymentReceiptWriteOffMapper">
  <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.IndustryPaymentReceiptWriteOff">
    <!--@mbg.generated-->
    <!--@Table industry_payment_receipt_write_off-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId" />
    <result column="contract_number" jdbcType="VARCHAR" property="contractNumber" />
    <result column="write_off_amount" jdbcType="DECIMAL" property="writeOffAmount" />
    <result column="remarks" jdbcType="LONGVARCHAR" property="remarks" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, process_instance_id, contract_number, write_off_amount, remarks, create_user, 
    update_user, create_time, update_time
  </sql>
</mapper>