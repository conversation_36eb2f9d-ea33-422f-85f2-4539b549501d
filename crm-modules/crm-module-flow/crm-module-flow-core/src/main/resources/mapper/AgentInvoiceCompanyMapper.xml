<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentInvoiceCompanyMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AgentInvoiceCompany">
            <result property="id" column="id" jdbcType="VARCHAR"/>
            <result property="company" column="company" jdbcType="VARCHAR"/>
            <result property="registerAddr" column="register_addr" jdbcType="VARCHAR"/>
            <result property="taxnumber" column="taxNumber" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="bankDeposit" column="bank_deposit" jdbcType="VARCHAR"/>
            <result property="bankAccount" column="bank_account" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,company,register_addr,
        taxNumber,phone,bank_deposit,
        bank_account,create_user,update_user,
        create_time,update_time
    </sql>
</mapper>
