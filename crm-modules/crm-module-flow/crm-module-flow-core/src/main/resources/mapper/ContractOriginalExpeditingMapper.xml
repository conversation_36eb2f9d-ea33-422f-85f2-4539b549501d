<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ContractOriginalExpeditingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ContractOriginalExpediting">
        <id column="id" property="id"/>
        <result column="contract_review_main_id" property="contractReviewMainId"/>
        <result column="original_type" property="originalType"/>
        <result column="expediting_id" property="expeditingId"/>
        <result column="contract_owner_dept_id" property="contractOwnerDeptId"/>
        <result column="contract_owner_dept_name" property="contractOwnerDeptName"/>
        <result column="submission_time" property="submissionTime"/>
        <result column="remark" property="remark"/>
        <result column="contract_owner_id" property="contractOwnerId"/>
        <result column="contract_owner_name" property="contractOwnerName"/>
        <result column="sale_id" property="saleId"/>
        <result column="sale_name" property="saleName"/>
        <result column="contract_time" property="contractTime"/>
        <result column="contract_type" property="contractType"/>
        <result column="final_customer_id" property="finalCustomerId"/>
        <result column="final_customer_name" property="finalCustomerName"/>
        <result column="contract_company_id" property="contractCompanyId"/>
        <result column="contract_company_name" property="contractCompanyName"/>
        <result column="contract_amount" property="contractAmount"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="contract_number" property="contractNumber"/>
        <result column="signing_company" property="signingCompany"/>
        <result column="sale_dept_id" property="saleDeptId"/>
        <result column="sale_dept_name" property="saleDeptName"/>
        <result column="document_id" property="documentId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        main.id
        , main.contract_review_main_id, main.original_type,main.contract_owner_dept_id, main.contract_owner_dept_name, main.submission_time, main.remark, main.contract_owner_id, main.contract_owner_name, main.sale_id, main.sale_name, main.contract_time, main.contract_type, main.final_customer_id, main.final_customer_name, main.contract_company_id, main.contract_company_name, main.contract_amount, main.del_flag, main.create_user, main.update_user, main.create_time, main.update_time,main.contract_number
         ,main.signing_company,main.sale_dept_id,main.sale_dept_name,main.document_id,main.expediting_id
    </sql>


    <select id="pageOriginalExpediting"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from contract_original_expediting main ,contract_original_expediting_main emain
        <where>
            main.expediting_id = emain.id and
            main.del_flag = 0 and emain.process_state = 2
            <if test="query.signCompanyId != null and query.signCompanyId != ''">
                and main.signing_company = #{query.signCompanyId}
            </if>
            <if test="query.originalType != null and query.originalType != ''">
                and main.original_type = #{query.originalType}
            </if>
            <if test="query.contractOwnerDeptId != null and query.contractOwnerDeptId != ''">
                and main.contract_owner_dept_id = #{query.contractOwnerDeptId}
            </if>
            <if test="query.finalCustomerName != null and query.finalCustomerName != ''">
                and main.final_customer_name like  CONCAT('%',#{query.finalCustomerName},'%')
            </if>

            <if test="query.contractOwnerId != null and query.contractOwnerId != ''">
                and main.contract_owner_id = #{query.contractOwnerId}
            </if>
            <if test="query.contractOwnerIds != null and query.contractOwnerIds.size() >0">
                and  main.contract_owner_id in
                <foreach collection="query.contractOwnerIds" item="implementer" open="(" separator="," close=")">
                    #{implementer}
                </foreach>

            </if>
            <if test="query.contractCompanyName != null and query.contractCompanyName != ''">
                and main.contract_company_name  like  CONCAT('%',#{query.contractCompanyName},'%')
            </if>
            <if test="query.contractCompanyId != null and query.contractCompanyId != ''">
                and main.contract_company_id = #{query.contractCompanyId}
            </if>
            <if test="query.contractTimeStart != null ">
                and <![CDATA[main.contract_time >= #{query.contractTimeStart}]]>
            </if>
            <if test="query.contractTimeEnd != null">
                and <![CDATA[main.contract_time <= #{query.contractTimeEnd}]]>
            </if>

        </where>

       order by main.contract_time desc
    </select>



    <select id="selectReviewByProcessInstanceId"
            resultMap="BaseResultMap">
        select
        main.document_id,main.expediting_id,main.id,main.contract_review_main_id
        from contract_original_expediting main ,contract_original_expediting_main emain
        <where>
            main.expediting_id = emain.id and
            main.del_flag = 0 and emain.process_state = 1 and emain.process_instance_id = #{processInstanceId} and main.submission_time is null
        </where>

        order by main.update_time desc
    </select>

    <select id="reviewByDocumentIds"
            resultMap="BaseResultMap">
        select
        main.document_id,main.expediting_id,main.id,main.contract_review_main_id
        from contract_original_expediting main ,contract_original_expediting_main emain
        <where>
            main.expediting_id = emain.id and
            main.del_flag = 0 and emain.process_state = #{processState} and main.contract_review_main_id in
            <foreach collection="reviewIds" item="implementer" open="(" separator="or" close=")">
                (#{implementer})
            </foreach>
        </where>

        order by main.update_time desc
    </select>

    <select id="esixtByDocumentIds"
            resultMap="BaseResultMap">
        select
        main.document_id,main.expediting_id,main.id,main.contract_review_main_id
        from contract_original_expediting main
        <where>
            main.del_flag = 0  and main.contract_review_main_id in
            <foreach collection="reviewIds" item="implementer" open="(" separator="," close=")">
                #{implementer}
            </foreach>
            <if test="contractTimeEnd != null">
                and <![CDATA[main.create_time <= #{contractTimeEnd}]]>
            </if>
            <if test="contractTimeStart != null">
                and <![CDATA[main.create_time >= #{contractTimeStart}]]>
            </if>
        </where>

        order by main.update_time desc
    </select>
</mapper>
