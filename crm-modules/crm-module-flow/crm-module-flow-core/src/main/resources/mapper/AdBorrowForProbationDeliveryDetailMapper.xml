<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AdBorrowForProbationDeliveryDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AdBorrowForProbationDeliveryDetail">
        <id column="id" property="id" />
        <result column="ad_borrow_id" property="adBorrowId" />
        <result column="ad_delivery_id" property="adDeliveryId" />
        <result column="delivery_no" property="deliveryNo" />
        <result column="company_code" property="companyCode" />
        <result column="company_name" property="companyName" />
        <result column="sender_name" property="senderName" />
        <result column="sender_phone" property="senderPhone" />
        <result column="sender_address" property="senderAddress" />
        <result column="sender_city_code" property="senderCityCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ad_borrow_id, ad_delivery_id, delivery_no, company_code, company_name, sender_name, sender_phone, sender_address, sender_city_code, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
