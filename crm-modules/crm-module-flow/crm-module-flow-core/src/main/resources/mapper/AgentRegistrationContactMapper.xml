<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentRegistrationContactMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AgentRegistrationContact">
        <result property="id"    column="id"    />
        <result property="agentRegistrationId"    column="agent_registration_id"    />
        <result property="contact"    column="contact"    />
        <result property="phone"    column="phone"    />
        <result property="job"    column="job"    />
        <result property="email"    column="email"    />
        <result property="docId"    column="doc_id"  typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"   />
        <result property="delFlag"    column="del_flag"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <select id="selectContactById" resultType="java.lang.String">
        select id from agent_registration_contact where del_flag=0 and agent_registration_id = #{agentId}
    </select>

    <delete id="deleteContactByIds" parameterType="java.util.List">
        delete from agent_registration_contact where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>