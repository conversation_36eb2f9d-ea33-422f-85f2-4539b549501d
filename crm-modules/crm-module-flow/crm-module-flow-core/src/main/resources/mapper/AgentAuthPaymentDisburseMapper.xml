<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentAuthPaymentDisburseMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AgentAuthPaymentDisburse">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="payoutCompanyId" column="payout_company_id" jdbcType="VARCHAR"/>
            <result property="payoutCompanyName" column="payout_company_name" jdbcType="VARCHAR"/>
            <result property="payoutDate" column="payout_date" jdbcType="DATE"/>
            <result property="payoutAmount" column="payout_amount" jdbcType="DECIMAL"/>
            <result property="recipientCompanyId" column="recipient_company_id" jdbcType="VARCHAR"/>
            <result property="recipientCompanyName" column="recipient_company_name" jdbcType="VARCHAR"/>
            <result property="recipientCompanyLevel" column="recipient_company_level" jdbcType="INTEGER"/>
            <result property="parentProcessInstanceId" column="parent_process_instance_id" jdbcType="INTEGER"/>
            <result property="paymentNature" column="payment_nature" jdbcType="TINYINT"/>
            <result property="attachmentIds" column="attachment_ids" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="receiptNumber" column="receipt_number" jdbcType="VARCHAR"/>
            <result property="receiptDate" column="receipt_date" jdbcType="DATE"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,payout_company_id,payout_company_name,
        recipient_company_id,recipient_company_name,recipient_company_level,
        payment_nature,attachment_ids,receipt_number,
        receipt_date,remark,create_user,
        create_time,update_user,update_time
    </sql>

    <select id="selectPerformanceReportTotalAdvancePayment" resultType="java.math.BigDecimal">
        select IFNULL(sum(payout_amount),0) as payout_amount
        from agent_auth_payment_disburse
        where process_state = 2 and payout_company_id=#{channelCompanyId} and recipient_company_id=#{supplierId}
    </select>
</mapper>
