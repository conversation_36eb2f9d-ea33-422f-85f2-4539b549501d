<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentPrepaymentUsageDetailMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AgentPrepaymentUsageDetail">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="prepaymentUsageId" column="prepayment_usage_id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="performanceProcessInstanceId" column="performance_process_instance_id" jdbcType="VARCHAR"/>
            <result property="performanceNumber" column="performance_number" jdbcType="VARCHAR"/>
            <result property="writeOffPrice" column="write_off_price" jdbcType="DECIMAL"/>
            <result property="supplierId" column="supplier_id" jdbcType="VARCHAR"/>
            <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
            <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,prepayment_usage_id,process_instance_id,performance_process_instance_id,
        performance_number,write_off_price,supplier_id,
        supplier_name,customer_id,customer_name,
        remark,create_user,update_user,
        create_time,update_time,del_flag
    </sql>
</mapper>
