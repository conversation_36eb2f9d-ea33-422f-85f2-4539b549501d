<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.TeamAppointmentMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.TeamAppointment">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="teamBuildingId" column="team_building_id" jdbcType="VARCHAR"/>
            <result property="projectManager" column="project_manager" jdbcType="TINYINT"/>
            <result property="technicalManager" column="technical_manager" jdbcType="TINYINT"/>
            <result property="departmentsInvolved" column="departments_involved" jdbcType="VARCHAR"/>
            <result property="typesOfDepartmentsInvolved" column="types_of_departments_involved" jdbcType="TINYINT"/>
            <result property="whetherToAssignPersonnel" column="whether_to_assign_personnel" jdbcType="TINYINT"/>
            <result property="personId" column="person_id" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="projectRole" column="project_role" jdbcType="VARCHAR"/>
            <result property="jobDescription" column="job_description" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,team_building_id,project_manager,
        technical_manager,departments_involved,types_of_departments_involved,
        whether_to_assign_personnel,person_id,project_role,
        job_description,create_time,update_time,
        create_user,update_user,del_flag
    </sql>
</mapper>
