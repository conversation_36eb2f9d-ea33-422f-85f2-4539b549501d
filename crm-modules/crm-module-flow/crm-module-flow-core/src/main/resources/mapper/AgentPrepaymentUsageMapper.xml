<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentPrepaymentUsageMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AgentPrepaymentUsage">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="INTEGER"/>
            <result property="payoutCompanyId" column="payout_company_id" jdbcType="VARCHAR"/>
            <result property="payoutCompanyName" column="payout_company_name" jdbcType="VARCHAR"/>
            <result property="recipientCompanyId" column="recipient_company_id" jdbcType="VARCHAR"/>
            <result property="recipientCompanyName" column="recipient_company_name" jdbcType="VARCHAR"/>
            <result property="recipientCompanyLevel" column="recipient_company_level" jdbcType="INTEGER"/>
            <result property="remainingPrepayment" column="remaining_prepayment" jdbcType="DECIMAL"/>
            <result property="availablePrepaymentAmount" column="available_prepayment_amount" jdbcType="DECIMAL"/>
            <result property="effectiveTime" column="effective_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_number,
        process_state,payout_company_id,payout_company_name,
        recipient_company_id,recipient_company_name,recipient_company_level,
        remaining_prepayment,available_prepayment_amount,effective_time,
        remark,create_user,create_user_name,
        update_user,create_time,update_time,
        del_flag
    </sql>
</mapper>
