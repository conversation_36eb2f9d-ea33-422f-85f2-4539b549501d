<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PerformanceReportPaymentTermsMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PerformanceReportPaymentTerms">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="dueDate" column="due_date" jdbcType="DATE"/>
            <result property="amountDue" column="amount_due" jdbcType="DECIMAL"/>
            <result property="termDescription" column="term_description" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,due_date,
        amount_due,term_description,create_user,
        update_user,create_time,update_time
    </sql>
</mapper>
