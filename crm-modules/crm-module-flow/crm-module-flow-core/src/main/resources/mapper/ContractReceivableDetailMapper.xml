<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ContractReceivableDetailMapper">

    <select id="getByCondition" resultType="com.topsec.crm.flow.core.entity.ContractReceivableDetail">
        select * from contract_receivable_detail detail left join contract_receivable_main main on detail.contract_receivable_main_id=main.id
        where detail.del_flag = 0 and main.del_flag = 0
        <if test="query.startTime != null">
            and main.receivable_date &gt;= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and main.receivable_date &lt;=#{query.endTime}
        </if>
        <if test="query.contractOwnerId != null and query.contractOwnerId != ''">
            and detail.contract_owner_id=#{query.contractOwnerId}
        </if>
    </select>


</mapper>