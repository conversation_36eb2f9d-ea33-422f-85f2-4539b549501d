<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.CostContractMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.CostContract">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="TINYINT"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="costFilingId" column="cost_filing_id" jdbcType="VARCHAR"/>
            <result property="costFilingNumber" column="cost_filing_number" jdbcType="VARCHAR"/>
            <result property="contractType" column="contract_type" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
            <result property="contractTaxRate" column="contract_tax_rate" jdbcType="DECIMAL"/>
            <result property="confirmTerms" column="confirm_terms" jdbcType="TINYINT"/>
            <result property="contractMode" column="contract_mode" jdbcType="TINYINT"/>
            <result property="supplierId" column="supplier_id" jdbcType="VARCHAR"/>
            <result property="registerAddr" column="register_addr" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="taxNumber" column="tax_number" jdbcType="VARCHAR"/>
            <result property="bank" column="bank" jdbcType="VARCHAR"/>
            <result property="bankAccount" column="bank_account" jdbcType="VARCHAR"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="fsmDocId" column="fsm_doc_id"  jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="processPassTime" column="process_pass_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,project_id,cost_filing_id,
        cost_filing_number,contract_type,company_id,
        dept_id,contract_tax_rate,confirm_terms,
        contract_mode,supplier_id,register_addr,
        phone,tax_number,bank,
        bank_account,start_time,end_time,
        fsm_doc_id,remarks,process_pass_time,
        create_user,update_user,create_time,
        update_time,del_flag
    </sql>
</mapper>
