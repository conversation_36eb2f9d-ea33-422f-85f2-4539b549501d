<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PriceReviewTempCostMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PriceReviewTempCost">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="added" column="added" jdbcType="TINYINT"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_id,added,
        process_instance_id,create_time,update_time,
        create_user,update_user
    </sql>
</mapper>
