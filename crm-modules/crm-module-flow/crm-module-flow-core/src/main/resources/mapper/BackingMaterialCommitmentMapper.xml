<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BackingMaterialCommitmentMapper">
    
    <resultMap type="com.topsec.crm.flow.core.entity.BackingMaterialCommitment" id="BackingMaterialCommitmentResult">
        <result property="id"    column="id"    />
        <result property="commitmentType"    column="commitment_type"    />
        <result property="commitmentFlag"    column="commitment_flag"    />
        <result property="parentAgentCommitment"    column="parent_agent_commitment"    />
        <result property="commitmentNumber"    column="commitment_number"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>


</mapper>