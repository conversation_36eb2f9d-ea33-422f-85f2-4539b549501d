<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentInvioceProductMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AgentInvioceProduct">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="performanceReportProcessInstanceId" column="performance_report_process_instance_id" jdbcType="VARCHAR"/>
            <result property="dealPrice" column="deal_price" jdbcType="DECIMAL"/>
            <result property="invoiceNum" column="invoice_num" jdbcType="INTEGER"/>
            <result property="invoiceAmount" column="invoice_amount" jdbcType="DECIMAL"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,performance_report_process_instance_id,deal_price,
        invoice_num,invoice_amount,del_flag,create_user,update_user,create_time,update_time
    </sql>
    <select id="selectAgentInvioceProductList" resultType="com.topsec.crm.flow.api.dto.agentInvoice.AgentInvioceProductDTO">
        SELECT
            performance_report_product_own.id as performance_report_product_own_record_id,
            performance_report_product_own.process_instance_id as performance_report_process_instance_id,
            process_number,
            stuff_code,
            specification_id as product_category,
            pn_code,
            product_num,
            product_lic,
            deal_price,
            deal_total_price,
            CASE
                WHEN IFNULL(invoice_num, 0) = 0 THEN product_num
                ELSE product_num - invoice_num
                END AS unbilled_num,
            CASE
                WHEN IFNULL(invoice_num, 0) = 0 THEN product_num * deal_price
                ELSE (product_num - invoice_num) * deal_price
                END AS unbilled_amount
        FROM
            performance_report_product_own
                LEFT JOIN
            performance_report ON performance_report.process_instance_id = performance_report_product_own.process_instance_id
                LEFT JOIN (
                SELECT
                    performance_report_process_instance_id,
                    performance_report_product_own_record_id,
                    SUM(invoice_num) AS invoice_num
                FROM
                    agent_invioce_product
                WHERE
                    del_flag = 0
                GROUP BY
                    performance_report_process_instance_id, performance_report_product_own_record_id
            ) AS invoice ON invoice.performance_report_process_instance_id = performance_report_product_own.process_instance_id
                AND invoice.performance_report_product_own_record_id = performance_report_product_own.id
        WHERE
            performance_report_product_own.del_flag = 0
        <if test="processNumber != null  and processNumber != ''"> and process_number = #{processNumber}</if>
        <if test="stuffCode != null  and stuffCode != ''"> and stuff_code = #{stuffCode}</if>
        <if test="performanceReportProcessInstanceId != null  and performanceReportProcessInstanceId != ''"> and performance_report_process_instance_id = #{performanceReportProcessInstanceId}</if>
        <if test="performanceReportProductOwnRecordId != null  and performanceReportProductOwnRecordId != ''"> and performance_report_product_own_record_id = #{performanceReportProductOwnRecordId}</if>
    </select>
    <select id="getByPerformanceReportProcessInstanceId" resultType="com.topsec.crm.flow.api.dto.agentInvoice.AgentInvioceProductDTO">
            SELECT pro.*,stuff_code FROM `agent_invioce_product` pro
            left join performance_report_product_own own on pro.performance_report_product_own_record_id = own.id
            where pro.del_flag=0
        <if test="performanceReportProcessInstanceId != null  and performanceReportProcessInstanceId != ''"> and pro.performance_report_process_instance_id = #{performanceReportProcessInstanceId}</if>
    </select>
</mapper>
