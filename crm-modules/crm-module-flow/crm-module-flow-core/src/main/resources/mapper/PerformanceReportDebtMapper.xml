<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PerformanceReportDebtMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PerformanceReportDebt">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="performanceProcessInstanceId" column="performance_process_instance_id" jdbcType="VARCHAR"/>
            <result property="contractNumber" column="contract_number" jdbcType="VARCHAR"/>
            <result property="saleId" column="sale_id" jdbcType="VARCHAR"/>
            <result property="saleName" column="sale_name" jdbcType="VARCHAR"/>
            <result property="saleDeptId" column="sale_dept_id" jdbcType="VARCHAR"/>
            <result property="saleDeptName" column="sale_dept_name" jdbcType="VARCHAR"/>
            <result property="contractAmount" column="contract_amount" jdbcType="DECIMAL"/>
            <result property="returnedAmount" column="returned_amount" jdbcType="DECIMAL"/>
            <result property="debtAmount" column="debt_amount" jdbcType="DECIMAL"/>
            <result property="overdueAmount" column="overdue_amount" jdbcType="DECIMAL"/>
            <result property="overdueDays" column="overdue_days" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,performance_process_instance_id,contract_number,
        sale_id,sale_name,sale_dept_id,
        sale_dept_name,contract_amount,returned_amount,
        debt_amount,overdue_amount,overdue_days,
        del_flag,create_user,update_user,
        create_time,update_time
    </sql>
</mapper>
