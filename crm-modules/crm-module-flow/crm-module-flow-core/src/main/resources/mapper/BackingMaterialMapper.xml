<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BackingMaterialMapper">

    <resultMap type="com.topsec.crm.flow.core.entity.BackingMaterial" id="BackingMaterialResult">
        <result property="id"    column="id"    />
        <result property="priceReviewProcessInstanceId"    column="price_review_process_instance_id"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="processNumber" column="process_number" />
        <result property="processState" column="process_state" />
        <result property="companyId"    column="company_id"    />
        <result property="companyName"    column="company_name"    />
        <result property="subordinateCompanyId"    column="subordinate_company_id"    />
        <result property="commitmentTypes"    column="commitment_types"   />
        <result property="commitments"    column="commitments"   />
        <result property="provideFlag"    column="provide_flag"    />
        <result property="notProvideReason"    column="not_provide_reason"    />
        <result property="firstDeferReason"    column="first_defer_reason"    />
        <result property="firstDeferType"    column="first_defer_type"    />
        <result property="firstDeferStart"    column="first_defer_start"    />
        <result property="firstDeferEnd"    column="first_defer_end"    />
        <result property="secondDeferReason"    column="second_defer_reason"    />
        <result property="secondDeferType"    column="second_defer_type"    />
        <result property="secondDeferStart"    column="second_defer_start"    />
        <result property="secondDeferEnd"    column="second_defer_end"    />
        <result property="secondDeferFlag"    column="second_defer_flag"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectBackingMaterialVo">
        select id,price_review_process_instance_id,process_id,company_id,company_name,subordinate_company_id,commitment_types,commitments,provide_flag,not_provide_reason,first_defer_reason,first_defer_type,first_defer_start,first_defer_end,second_defer_reason,second_defer_type,second_defer_start,second_defer_end,second_defer_flag,create_user,update_user,create_time,update_time,del_flag
        from backing_material
    </sql>

    <select id="selectBackingMaterialList" parameterType="com.topsec.crm.flow.core.entity.BackingMaterial" resultMap="BackingMaterialResult">
        <include refid="selectBackingMaterialVo"/>
        <where>
            <if test="processId != null  and processId != ''"> and process_id = #{processId}</if>
            <if test="priceReviewProcessInstanceId != null  and priceReviewProcessInstanceId != ''"> and price_review_process_instance_id = #{priceReviewProcessInstanceId}</if>
            <if test="companyId != null  and companyId != ''"> and company_id = #{companyId}</if>
            <if test="companyName != null and companyName != ''"> and company_name = #{companyName}</if>
            <if test="subordinateCompanyId != null and subordinateCompanyId != ''"> and subordinate_company_id = #{subordinateCompanyId}</if>
            <if test="commitmentTypes != null and commitmentTypes != ''"> and commitment_types = #{commitmentTypes}</if>
            <if test="commitments != null and commitments != ''"> and commitments = #{commitments}</if>
            <if test="provideFlag != null and provideFlag != ''"> and provide_flag = #{provideFlag}</if>
            <if test="notProvideReason != null and notProvideReason != ''"> and not_provide_reason = #{notProvideReason}</if>
            <if test="firstDeferReason != null and firstDeferReason != ''"> and first_defer_reason = #{firstDeferReason}</if>
            <if test="firstDeferType != null and firstDeferType != ''"> and first_defer_type = #{firstDeferType}</if>
            <if test="firstDeferStart != null and firstDeferStart != ''"> and first_defer_start = #{firstDeferStart}</if>
            <if test="firstDeferEnd != null and firstDeferEnd != ''"> and first_defer_end = #{firstDeferEnd}</if>
            <if test="secondDeferReason != null and secondDeferReason != ''"> and second_defer_reason = #{secondDeferReaso}</if>
            <if test="secondDeferType != null and secondDeferType != ''"> and second_defer_type = #{secondDeferType}</if>
            <if test="secondDeferStart != null and secondDeferStart != ''"> and second_defer_start = #{secondDeferStart}</if>
            <if test="secondDeferEnd != null and secondDeferEnd != ''"> and second_defer_end = #{secondDeferEnd}</if>
            <if test="secondDeferFlag != null and secondDeferFlag != ''"> and second_defer_flag = #{secondDeferFlag}</if>
            <if test="createUser != null  and createUser != ''"> and create_user = #{createUser}</if>
            <if test="updateUser != null  and updateUser != ''"> and update_user = #{updateUser}</if>
        </where>
    </select>

    <select id="selectBackingMaterialByPriceReviewProcessId" parameterType="String" resultMap="BackingMaterialResult">
        <include refid="selectBackingMaterialVo"/>
        where del_flag = 0 and price_review_process_instance_id = #{priceReviewProcessId}
    </select>

    <insert id="insertBackingMaterial" parameterType="com.topsec.crm.flow.core.entity.BackingMaterial">
        insert into backing_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="processId != null">process_id,</if>
            <if test="priceReviewProcessInstanceId != null">price_review_process_instance_id,</if>
            <if test="companyId != null">company_id,</if>
            <if test="companyName != null">company_name,</if>
            <if test="subordinateCompanyId != null">subordinate_company_id,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="commitmentTypes != null       ">commitment_types,</if>
            <if test="commitments != null       ">commitments,</if>
            <if test="provideFlag != null      ">provide_flag,</if>
            <if test="notProvideReason != null ">not_provide_reason,</if>
            <if test="firstDeferReason != null ">first_defer_reason,</if>
            <if test="firstDeferType != null   ">first_defer_type,</if>
            <if test="firstDeferStart != null  ">first_defer_start,</if>
            <if test="firstDeferEnd != null    ">first_defer_end,</if>
            <if test="secondDeferReason != null">second_defer_reason,</if>
            <if test="secondDeferType != null  ">second_defer_type,</if>
            <if test="secondDeferStart != null ">second_defer_start,</if>
            <if test="secondDeferEnd != null   ">second_defer_end,</if>
            <if test="secondDeferFlag != null   ">second_defer_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="processId != null">#{processId},</if>
            <if test="priceReviewProcessInstanceId != null">#{priceReviewProcessInstanceId},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="subordinateCompanyId != null">#{subordinateCompanyId},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="commitmentTypes != null       ">#{commitment_types},</if>
            <if test="commitments != null       ">#{commitments},</if>
            <if test="provideFlag != null      ">#{provideFlag},</if>
            <if test="notProvideReason != null ">#{notProvideReason},</if>
            <if test="firstDeferReason != null ">#{firstDeferReason},</if>
            <if test="firstDeferType != null   ">#{firstDeferType},</if>
            <if test="firstDeferStart != null  ">#{firstDeferStart},</if>
            <if test="firstDeferEnd != null    ">#{firstDeferEnd},</if>
            <if test="secondDeferReason != null">#{secondDeferReaso},</if>
            <if test="secondDeferType != null  ">#{secondDeferType},</if>
            <if test="secondDeferStart != null ">#{secondDeferStart},</if>
            <if test="secondDeferEnd != null   ">#{secondDeferEnd},</if>
            <if test="secondDeferFlag != null   ">#{secondDeferFlag},</if>
        </trim>
    </insert>

    <update id="updateBackingMaterial" parameterType="com.topsec.crm.flow.core.entity.BackingMaterial">
        update backing_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="processId != null">process_id = #{processId},</if>
            <if test="priceReviewProcessInstanceId != null">price_review_process_instance_id = #{priceReviewProcessInstanceId},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="subordinateCompanyId != null">subordinate_company_id = #{subordinateCompanyId},</if>
            <if test="commitmentTypes != null">commitment_types = #{commitmentTypes},</if>
            <if test="commitments != null">commitments = #{commitments},</if>
            <if test="provideFlag != null">provide_flag = #{provideFlag},</if>
            <if test="notProvideReason != null">not_provide_reason = #{notProvideReason},</if>
            <if test="firstDeferReason != null">first_defer_reason = #{firstDeferReason},</if>
            <if test="firstDeferType != null">first_defer_type = #{firstDeferType},</if>
            <if test="firstDeferStart != null">first_defer_start = #{firstDeferStart},</if>
            <if test="firstDeferEnd != null">first_defer_end = #{firstDeferEnd},</if>
            <if test="secondDeferReason != null">second_defer_reason = #{secondDeferReaso},</if>
            <if test="secondDeferType != null">second_defer_type = #{secondDeferType},</if>
            <if test="secondDeferStart != null">second_defer_start = #{secondDeferStart},</if>
            <if test="secondDeferEnd != null">second_defer_end = #{secondDeferEnd},</if>
            <if test="secondDeferFlag != null">second_defer_flag = #{secondDeferFlag},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBackingMaterial" parameterType="String">
        update backing_material set del_flag=1 where id = #{id}
    </delete>
</mapper>
