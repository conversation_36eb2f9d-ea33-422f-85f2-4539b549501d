<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.topsec.crm.flow.core.mapper.SalesAgreementMembersRelMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.topsec.crm.flow.core.entity.SalesAgreementMembersRel" id="agreementMembersRelMap">
        <result property="id" column="id"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="personId" column="person_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="type" column="type"/>
        <result property="currentStatus" column="current_status"/>
        <result property="changeStatus" column="change_status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
    </resultMap>


</mapper>