<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForProbationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForProbation">
        <id column="id" property="id" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="process_state" property="processState" />
        <result column="process_number" property="processNumber" />
        <result column="project_id" property="projectId" />
        <result column="link_man" property="linkMan" />
        <result column="borrower" property="borrower" />
        <result column="person_id" property="personId" />
        <result column="sign_company" property="signCompany" />
        <result column="borrow_type" property="borrowType" />
        <result column="product_type" property="productType" />
        <result column="middleman" property="middleman" />
        <result column="middleman_name" property="middlemanName" />
        <result column="middleman_link_man" property="middlemanLinkMan" />
        <result column="middleman_link_phone" property="middlemanLinkPhone" />
        <result column="estimated_return_time" property="estimatedReturnTime" />
        <result column="reason" property="reason" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, process_instance_id, process_state, process_number, project_id, link_man, borrower, person_id, sign_company, borrow_type, product_type, middleman, middleman_name, middleman_link_man, middleman_link_phone, estimated_return_time, reason, remark, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
