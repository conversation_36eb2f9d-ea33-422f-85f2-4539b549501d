<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentExamineSdDetailMapper">

    <select id="selectAgentExamineSdDetailList" parameterType="com.topsec.crm.flow.api.dto.agentExamineSd.AgentExamineSdDetailQuery" resultType="com.topsec.crm.flow.api.dto.agentExamineSd.AgentExamineSdDetailDTO">
        select detail.*
        from agent_examine_sd_detail detail
        left join agent_examine_sd sd on detail.process_instance_id=sd.process_instance_id
        where sd.del_flag=0 and detail.del_flag=0
        <if test="platformIds != null and platformIds != ''">
            and platform_id in
            <foreach item="platformIds" collection="platformIds" open="(" separator="," close=")">
                #{platformIds}
            </foreach>
        </if>
        <if test="areaCode != null and areaCode != ''">
            and area_code = #{areaCode}
        </if>
        <if test="examineYear != null and examineYear != ''">
            and examine_year = #{examineYear}
        </if>
        <if test="quarter != null and quarter != ''">
            and quarter = #{quarter}
        </if>
        <if test="agentName != null and agentName != ''">
            and agent_name like '%' #{agentName} '%'
        </if>
        <if test="areaName != null and areaName != ''">
            and (area_name like '%' #{areaName} '%' or city_name like '%' #{areaName} '%')
        </if>
    </select>
</mapper>
