<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForProbationProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForProbationProduct">
        <id column="id" property="id" />
        <result column="borrow_id" property="borrowId" />
        <result column="stuff_code" property="stuffCode" />
        <result column="actual_stuff_code" property="actualStuffCode" />
        <result column="product_name" property="productName" />
        <result column="pn_code" property="pnCode" />
        <result column="product_category" property="productCategory" />
        <result column="product_specification" property="productSpecification" />
        <result column="quoted_price" property="quotedPrice" />
        <result column="product_num" property="productNum" />
        <result column="product_type" property="productType" />
        <result column="deposit" property="deposit" />
        <result column="deposit_total" property="depositTotal" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, borrow_id, stuff_code, actual_stuff_code, product_name, pn_code, product_category, product_specification, quoted_price, product_num, product_type, deposit, deposit_total, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
