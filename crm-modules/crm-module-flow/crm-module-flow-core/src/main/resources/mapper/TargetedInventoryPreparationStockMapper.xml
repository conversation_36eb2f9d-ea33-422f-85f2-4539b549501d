<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.topsec.crm.flow.core.mapper.TargetedInventoryPreparationStockMapper">

  <!--<resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.TargetedInventoryPreparationStock">
    <id column="id" jdbcType="VARCHAR" property="id"/>
    <result column="process_detail_id" jdbcType="VARCHAR" property="processDetailId"/>
    <result column="crm_row_id" jdbcType="VARCHAR" property="crmRowId"/>
    <result column="stock_in_date" jdbcType="TIMESTAMP" property="stockInDate"/>
    <result column="stuff_code" jdbcType="VARCHAR" property="stuffCode"/>
    <result column="model" jdbcType="VARCHAR" property="model"/>
    <result column="pn_code" jdbcType="VARCHAR" property="pnCode"/>
    <result column="key_part" jdbcType="VARCHAR" property="keyPart"/>
    <result column="sn" jdbcType="VARCHAR" property="sn"/>
    <result column="stock_in_quantity" jdbcType="INTEGER" property="stockInQuantity"/>
    <result column="stock_in_number" jdbcType="VARCHAR" property="stockInNumber"/>
    <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
    <result column="stock_in_dept_name" jdbcType="VARCHAR" property="stockInDeptName"/>
    <result column="stock_in_person_name" jdbcType="VARCHAR" property="stockInPersonName"/>
    <result column="process_number" jdbcType="VARCHAR" property="processNumber"/>
    <result column="stock_location_code" jdbcType="VARCHAR" property="stockLocationCode"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, process_detail_id, crm_row_id, stock_in_date, stuff_code, model, pn_code, 
	key_part, sn, stock_in_quantity, stock_in_number, customer_name, stock_in_dept_name, stock_in_person_name, 
	process_number, stock_location_code, remark, create_user, create_time, update_user, update_time, 
	del_flag
  </sql>-->
</mapper>
