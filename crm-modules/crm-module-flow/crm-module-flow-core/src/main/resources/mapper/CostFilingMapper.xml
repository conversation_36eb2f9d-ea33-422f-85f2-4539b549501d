<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.topsec.crm.flow.core.mapper.CostFilingMapper">

    <resultMap type="com.topsec.crm.flow.core.entity.CostFiling" id="costFilingMap">
        <result property="id" column="id"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="processState" column="process_state"/>
        <result property="processNumber" column="process_number"/>
        <result property="projectId" column="project_id"/>
        <result property="contractId" column="contract_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="fsmDocId"    column="fsm_doc_id"  jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"  />
        <result property="remarks" column="remarks"/>
        <result property="processPassTime" column="process_pass_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

</mapper>