<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForSellDeliverySnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForSellDeliverySn">
        <id column="id" property="id" />
        <result column="delivery_id" property="deliveryId" />
        <result column="express_type" property="expressType" />
        <result column="express_id" property="expressId" />
        <result column="express_company" property="expressCompany" />
        <result column="send_city" property="sendCity" />
        <result column="receive_city" property="receiveCity" />
        <result column="product_sns" property="productSns" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, delivery_id, express_type, express_id, express_company, send_city, receive_city, product_sns, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
