<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForForwardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForForward">
        <id column="id" property="id" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="process_state" property="processState" />
        <result column="process_number" property="processNumber" />
        <result column="from_probation_num" property="fromProbationNum" />
        <result column="project_id" property="projectId" />
        <result column="link_man" property="linkMan" />
        <result column="person_id" property="personId" />
        <result column="receiver" property="receiver" />
        <result column="sign_company" property="signCompany" />
        <result column="borrow_type" property="borrowType" />
        <result column="borrow_product_type" property="borrowProductType" />
        <result column="middleman" property="middleman" />
        <result column="middleman_name" property="middlemanName" />
        <result column="middleman_link_man" property="middlemanLinkMan" />
        <result column="middleman_link_man_phone" property="middlemanLinkManPhone" />
        <result column="estimated_return_time" property="estimatedReturnTime" />
        <result column="forward_reason" property="forwardReason" />
        <result column="transport_num" property="transportNum" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, process_instance_id, process_state, process_number, from_probation_num, project_id, link_man, person_id, receiver, sign_company,
          borrow_type, borrow_product_type, middleman, middleman_name, middleman_link_man, middleman_link_man_phone, estimated_return_time,
          forward_reason, transport_num, remark, create_time, update_time, create_user, update_user, del_flag
    </sql>

    <sql id="selectBorrowForForwardVo">
        select id, process_instance_id, process_state, process_number, from_probation_num, project_id, link_man, person_id, receiver, sign_company,
               borrow_type, borrow_product_type, middleman, middleman_name, middleman_link_man, middleman_link_man_phone, estimated_return_time,
               forward_reason, transport_num, remark, create_time, update_time, create_user, update_user, del_flag
        from borrow_for_forward
    </sql>
    <select id="queryLatestBorrowForForwardByDeviceId" resultMap="BaseResultMap">
        <include refid="selectBorrowForForwardVo"/>
        WHERE del_flag=0 AND id =
            (select forward_id from borrow_for_forward_product
                where project_probation_device_id = #{deviceId}
                 ORDER BY create_time desc limit 1
            )
    </select>
    <select id="queryAllBorrowForForwardByDeviceId" resultMap="BaseResultMap">
        <include refid="selectBorrowForForwardVo"/>
        WHERE del_flag=0 AND id in (
            select forward_id from borrow_for_forward_product
            where project_probation_device_id = #{deviceId}
        )
    </select>
    <select id="findByProcessNumber" resultMap="BaseResultMap">
        <include refid="selectBorrowForForwardVo"/>
        where process_number = #{processNumber}
    </select>

</mapper>
