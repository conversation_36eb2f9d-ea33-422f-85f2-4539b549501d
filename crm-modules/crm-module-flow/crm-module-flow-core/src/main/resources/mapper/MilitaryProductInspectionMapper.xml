<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.MilitaryProductInspectionMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.MilitaryProductInspection">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="TINYINT"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="processPassTime" column="process_pass_time" jdbcType="TIMESTAMP"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="projectNo" column="project_no" jdbcType="VARCHAR"/>
            <result property="inspectionType" column="inspection_type" jdbcType="TINYINT"/>
            <result property="customized" column="customized" jdbcType="TINYINT"/>
            <result property="customizedOrderNumber" column="customized_order_number" jdbcType="VARCHAR"/>
            <result property="sm" column="sm" jdbcType="TINYINT"/>
            <result property="militarySupervisionOffice" column="military_supervision_office" jdbcType="VARCHAR"/>
            <result property="expectedFactoryInspectionTime" column="expected_factory_inspection_time" jdbcType="TIMESTAMP"/>
            <result property="expectedMilitaryInspectionTime" column="expected_military_inspection_time" jdbcType="TIMESTAMP"/>
            <result property="definedMilitaryInspectionTime" column="defined_military_inspection_time" jdbcType="TIMESTAMP"/>
            <result property="attachmentIds" column="attachment_ids" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,process_pass_time,project_id,project_no,
        inspection_type,customized,customized_order_number,
        sm,military_supervision_office,expected_factory_inspection_time,defined_military_inspection_time,
        expected_military_inspection_time,attachment_ids,create_time,update_time,
        create_user,update_user,del_flag
    </sql>
</mapper>
