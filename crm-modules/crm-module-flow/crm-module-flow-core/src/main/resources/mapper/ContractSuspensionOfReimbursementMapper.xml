<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ContractSuspensionOfReimbursementMapper">
  <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ContractSuspensionOfReimbursement">
    <!--@mbg.generated-->
    <!--@Table contract_suspension_of_reimbursement-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="contract_id" jdbcType="VARCHAR" property="contractId" />
    <result column="contract_time" jdbcType="DATE" property="contractTime" />
    <result column="contract_company_name" jdbcType="VARCHAR" property="contractCompanyName" />
    <result column="contract_company_id" jdbcType="VARCHAR" property="contractCompanyId" />
    <result column="contract_number" jdbcType="VARCHAR" property="contractNumber" />
    <result column="contract_owner_id" jdbcType="VARCHAR" property="contractOwnerId" />
    <result column="contract_owner_name" jdbcType="VARCHAR" property="contractOwnerName" />
    <result column="contract_owner_dept_id" jdbcType="VARCHAR" property="contractOwnerDeptId" />
    <result column="contract_owner_dept_name" jdbcType="VARCHAR" property="contractOwnerDeptName" />
    <result column="contract_amount" jdbcType="DECIMAL" property="contractAmount" />
    <result column="overdue_amount" jdbcType="DECIMAL" property="overdueAmount" />
    <result column="overdue_date" jdbcType="INTEGER" property="overdueDate" />
    <result column="returned_amount" jdbcType="DECIMAL" property="returnedAmount" />
    <result column="remind_classification" jdbcType="VARCHAR" property="remindClassification" />
    <result column="proportion_of_reimbursement" jdbcType="DECIMAL" property="proportionOfReimbursement" />
    <result column="free_date" jdbcType="DATE" property="freeDate" />
    <result column="free" jdbcType="BOOLEAN" property="free" />
    <result column="periods" jdbcType="DATE" property="periods" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>


  <select id="page" resultMap="BaseResultMap">
    WITH LatestPeriod AS (
      SELECT MAX(periods) AS max_period
      FROM contract_suspension_of_reimbursement
    )
    SELECT *
    FROM contract_suspension_of_reimbursement
    where free = false
    <choose>
      <when test="query.periods!=null">
          AND periods=#{query.periods}
      </when>
      <otherwise>
          AND periods = (SELECT max_period FROM LatestPeriod)
      </otherwise>
    </choose>
    <if test="query.contractOwnerId!=null and query.contractOwnerId!=''">
       and contract_owner_id = #{query.contractOwnerId}
    </if>

    <if test="query.contractOwnerDeptIds!=null and query.contractOwnerDeptIds.size()>0">
       and contract_owner_dept_id in
       <foreach collection="query.contractOwnerDeptIds" item="item" open="(" separator="," close=")">
          #{item}
       </foreach>
    </if>
    <if test="query.contractNumber!=null and query.contractNumber!=''">
       and contract_number like concat('%',#{query.contractNumber},'%')
    </if>


  </select>

  <select id="queryLastPeriods" resultType="java.time.LocalDate">
    SELECT MAX(periods) AS max_period
    FROM contract_suspension_of_reimbursement
  </select>
</mapper>
