<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BiddingMonitoringInfoMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BiddingMonitoringInfo">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="cursorMark" column="cursor_mark" jdbcType="VARCHAR"/>
            <result property="infoId" column="info_id" jdbcType="VARCHAR"/>
            <result property="biddingMonitoringInfoNumber" column="bidding_monitoring_info_number" jdbcType="VARCHAR"/>
            <result property="businessUltimate" column="business_ultimate" jdbcType="VARCHAR"/>
            <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
            <result property="province" column="province" jdbcType="VARCHAR"/>
            <result property="city" column="city" jdbcType="VARCHAR"/>
            <result property="region" column="region" jdbcType="VARCHAR"/>
            <result property="firstLevelIndustryAffiliation" column="first_level_industry_affiliation" jdbcType="VARCHAR"/>
            <result property="secondaryLevelIndustryAffiliation" column="secondary_level_industry_affiliation" jdbcType="VARCHAR"/>
            <result property="biddingProjectName" column="bidding_project_name" jdbcType="VARCHAR"/>
            <result property="releaseTime" column="release_time" jdbcType="VARCHAR"/>
            <result property="involvingProducts" column="involving_products" jdbcType="VARCHAR"/>
            <result property="projectBudget" column="project_budget" jdbcType="VARCHAR"/>
            <result property="biddingDeadline" column="bidding_deadline" jdbcType="VARCHAR"/>
            <result property="biddingNumber" column="bidding_number" jdbcType="VARCHAR"/>
            <result property="biddingWebsite" column="bidding_website" jdbcType="VARCHAR"/>
            <result property="biddingMethod" column="bidding_method" jdbcType="VARCHAR"/>
            <result property="infoType" column="info_type" jdbcType="VARCHAR"/>
            <result property="followUpDept" column="follow_up_dept" jdbcType="VARCHAR"/>
            <result property="followUpDeptLeader" column="follow_up_dept_leader" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>




    <resultMap id="BiddingMonitoringInfoListVOMap" type="com.topsec.crm.flow.api.dto.biddingMonitoring.VO.BiddingMonitoringInfoListVO" autoMapping="true">
        <!-- 主键字段 -->
        <id property="id" column="id" />

        <!-- 显式映射所有查询字段 -->
        <result property="biddingMonitoringInfoNumber" column="bidding_monitoring_info_number" />
        <result property="businessUltimate" column="business_ultimate" />
        <result property="biddingProjectName" column="bidding_project_name" />
        <result property="releaseTime" column="release_time" />
        <result property="involvingProducts" column="involving_products" />
        <result property="projectBudget" column="project_budget" />
        <result property="biddingDeadline" column="bidding_deadline" />
        <result property="biddingNumber" column="bidding_number" />
        <result property="biddingWebsite" column="bidding_website" />
        <result property="processInstanceId" column="process_instance_id" />
        <result property="processNumber" column="process_number" />
        <result property="biddingMethod" column="bidding_method" />
        <result property="infoType" column="info_type" />
        <result property="bidderPublicationTime" column="bidder_publication_time" />
        <result property="bidderUnit" column="bidder_unit" />
        <result property="bidderResult" column="bidder_result" />
        <!-- JSON 字段需要指定 TypeHandler -->
        <result property="bidderProvider" column="bidder_provider"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result property="followUpDept" column="follow_up_dept" />
        <result property="followUpDeptLeader" column="follow_up_dept_leader" />
        <result property="followUp" column="follow_up" />
        <result property="followUpType" column="follow_up_type" />
        <result property="projectNo" column="project_no" />
        <result property="notFollowReason" column="not_follow_reason" />
        <result property="projectId" column="project_id" />
        <result property="customerId" column="customer_id" />
        <result property="province" column="province" />
        <result property="city" column="city" />
        <result property="region" column="region" />
        <result property="firstLevelIndustryAffiliation" column="first_level_industry_affiliation" />
        <result property="secondaryLevelIndustryAffiliation" column="secondary_level_industry_affiliation" />
    </resultMap>

    <sql id="Base_Column_List">
        id,cursor_mark,info_id,bidding_monitoring_info_number,business_ultimate,customerId,province,city,region,first_level_industry_affiliation,secondary_level_industry_affiliation,
        bidding_project_name,release_time,involving_products,
        project_budget,bidding_deadline,bidding_number,
        bidding_website,bidding_method,info_type,follow_up_dept,follow_up_dept_leader,
        process_instance_id,process_number,del_flag,
        create_user,update_user,create_time,
        update_time
    </sql>


    <select id="biddingMonitoringPage" resultMap="BiddingMonitoringInfoListVOMap">

        select x1.id,x1.bidding_monitoring_info_number,x1.business_ultimate,x1.bidding_project_name,x1.release_time,x1.involving_products,x1.project_budget,x1.bidding_deadline,x1.bidding_number,x1.bidding_website,x1.process_instance_id,x1.process_number,
               x1.bidding_method,x1.info_type,x2.bidder_publication_time,x2.bidder_unit,x2.bidder_result,x2.bidding_type,x2.bidder_provider,x1.follow_up_dept,x1.follow_up_dept_leader,x2.bidder_result,x2.follow_up,
               x2.follow_up_type,x2.project_no,x2.not_follow_reason,x2.project_id,x2.bidder_result,x1.customer_id,x1.province,x1.city,x1.region,x1.first_level_industry_affiliation,x1.secondary_level_industry_affiliation
        from bidding_monitoring_info as x1
                 left join bidding_monitoring_flow as x2 on x1.bidding_monitoring_info_number = x2.bidding_monitoring_info_number and x2.del_flag=0
        <where>
            x1.del_flag=0
            <if test="biddingMonitoringPageQuery.follow != null">
            and x2.follow_up = #{biddingMonitoringPageQuery.follow}
            </if>
            <if test="biddingMonitoringPageQuery.followUpType != null and biddingMonitoringPageQuery.followUpType != ''">
                and x2.follow_up_type = #{biddingMonitoringPageQuery.followUpType}
            </if>
            <if test="biddingMonitoringPageQuery.biddingMethod != null ">
                and x1.bidding_method = #{biddingMonitoringPageQuery.biddingMethod}
            </if>
            <if test="biddingMonitoringPageQuery.infoType != null ">
                and x1.info_type = #{biddingMonitoringPageQuery.infoType}
            </if>
            <if test="biddingMonitoringPageQuery.biddingMonitoringInfoNumber != null and biddingMonitoringPageQuery.biddingMonitoringInfoNumber != ''">
                and x1.bidding_monitoring_info_number = #{biddingMonitoringPageQuery.biddingMonitoringInfoNumber}
            </if>
            <if test="biddingMonitoringPageQuery.processNumber != null and biddingMonitoringPageQuery.processNumber != ''">
                and x2.process_number = #{biddingMonitoringPageQuery.processNumber}
            </if>
            <if test="biddingMonitoringPageQuery.followDeptId != null and biddingMonitoringPageQuery.followDeptId != ''">
                and x1.follow_up_dept = #{biddingMonitoringPageQuery.followDeptId}
            </if>
            <if test="biddingMonitoringPageQuery.followUserId != null and biddingMonitoringPageQuery.followUserId != ''">
                and x2.follow_up_person = #{biddingMonitoringPageQuery.followUserId}
            </if>

            <if test="biddingMonitoringPageQuery.followUserId != null and biddingMonitoringPageQuery.followUserId != ''">
                and x2.follow_up_person = #{biddingMonitoringPageQuery.followUserId}
            </if>

            <if test="biddingMonitoringPageQuery.startTime != null">
                and STR_TO_DATE(x1.release_time,'%Y - %m - %d %H:%i:%s') &gt;= #{biddingMonitoringPageQuery.startTime}
            </if>

            <if test="biddingMonitoringPageQuery.endTime != null">
                and STR_TO_DATE(x1.release_time,'%Y - %m - %d %H:%i:%s') &lt; #{biddingMonitoringPageQuery.endTime}
            </if>

            <if test="biddingMonitoringPageQuery.existProcess != null  and biddingMonitoringPageQuery.existProcess == true">
                and x1.process_instance_id is not null
            </if>
            <if test="biddingMonitoringPageQuery.existProcess != null  and biddingMonitoringPageQuery.existProcess == false">
                and x1.process_instance_id is null
            </if>
            <if test="biddingMonitoringPageQuery.existFollowUpDept != null  and biddingMonitoringPageQuery.existFollowUpDept == true">
                and x1.follow_up_dept is not null
            </if>
            <if test="biddingMonitoringPageQuery.existFollowUpDept != null  and biddingMonitoringPageQuery.existFollowUpDept == false">
                and x1.follow_up_dept is null
            </if>
            <if test="biddingMonitoringPageQuery.dataScopeParam.personIdList != null">
                and x2.follow_up_person in
                <foreach collection="biddingMonitoringPageQuery.dataScopeParam.personIdList" item="personId" open="(" close=")" separator=",">
                    #{personId}
                </foreach>
            </if>

        </where>
            order by x1.create_time desc
    </select>
</mapper>
