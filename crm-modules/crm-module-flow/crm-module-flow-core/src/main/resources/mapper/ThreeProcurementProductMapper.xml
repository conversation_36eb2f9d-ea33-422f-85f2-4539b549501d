<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ThreeProcurementProductMapper">
    
    <resultMap type="com.topsec.crm.flow.core.entity.ThreeProcurementProduct" id="ThreeProcurementProductResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="productId"    column="product_id"    />
        <result property="contractId"    column="contract_id"    />
        <result property="recordId"    column="record_id"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="procurementPrice"    column="procurement_price"    />
        <result property="procurementTaxRate"    column="procurement_tax_rate"    />
        <result property="procurementPeriod"    column="procurement_period"    />
        <result property="procurementPeriodStartTime"    column="procurement_period_start_time"    />
        <result property="procurementPeriodEndTime"    column="procurement_period_end_time"    />
        <result property="procurementStatus"    column="procurement_status"    />
        <result property="productCategory"    column="product_category"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
    </resultMap>

    <sql id="selectThreeProcurementProductVo">
        select id, project_id, product_id, contract_id, record_id, process_instance_id, procurement_price, procurement_tax_rate, procurement_period, procurement_period_start_time, procurement_period_end_time, procurement_status, product_category, del_flag, create_time, update_time, create_user, update_user from three_procurement_product
    </sql>


    




    <delete id="deleteThreeProcurementProductById" parameterType="String">
        delete from three_procurement_product where id = #{id}
    </delete>

    <delete id="deleteThreeProcurementProductByIds" parameterType="String">
        delete from three_procurement_product where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>