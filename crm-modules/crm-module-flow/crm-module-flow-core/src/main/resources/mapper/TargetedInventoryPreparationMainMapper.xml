<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.topsec.crm.flow.core.mapper.TargetedInventoryPreparationMainMapper">

  <!--<resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.TargetedInventoryPreparationMain">
    <id column="id" jdbcType="VARCHAR" property="id"/>
    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId"/>
    <result column="process_state" jdbcType="TINYINT" property="processState"/>
    <result column="process_number" jdbcType="VARCHAR" property="processNumber"/>
    <result column="project_id" jdbcType="VARCHAR" property="projectId"/>
    <result column="targeted_inventory_preparation_company_id" jdbcType="VARCHAR" property="targetedInventoryPreparationCompanyId"/>
    <result column="demand_time" jdbcType="TIMESTAMP" property="demandTime"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="attachment_ids" jdbcType="CHAR" property="attachmentIds"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
    <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
  </resultMap>-->
<!--  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.topsec.crm.flow.core.entity.TargetedInventoryPreparationMain">
    <result column="reason_for_application" jdbcType="LONGVARCHAR" property="reasonForApplication"/>
    <result column="product_requirement" jdbcType="LONGVARCHAR" property="productRequirement"/>
    <result column="remark" jdbcType="LONGVARCHAR" property="remark"/>
  </resultMap>-->
<!--  <sql id="Base_Column_List">
    id, process_instance_id, process_state, process_number, project_id, targeted_inventory_preparation_company_id, demand_time, 
	del_flag, attachment_ids, create_time, update_time, create_user, update_user
  </sql>
  <sql id="Blob_Column_List">
    reason_for_application, product_requirement, remark
  </sql>-->
</mapper>
