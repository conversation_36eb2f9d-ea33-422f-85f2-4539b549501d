<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ThreeProcurementSmReviewMainMapper">
    
    <resultMap type="com.topsec.crm.flow.core.entity.ThreeProcurementSmReviewMain" id="ThreeProcurementSmReviewMainResult">
        <result property="id"    column="id"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="processState"    column="process_state"    />
        <result property="processNumber"    column="process_number"    />
        <result property="projectId"    column="project_id"    />
        <result property="contractId"    column="contract_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
    </resultMap>

    <sql id="selectThreeProcurementSmReviewMainVo">
        select id, process_instance_id, process_state, process_number, project_id, contract_id, del_flag, create_time, update_time, create_user, update_user from three_procurement_sm_review_main
    </sql>









    <delete id="deleteThreeProcurementSmReviewMainByIds" parameterType="String">
        delete from three_procurement_sm_review_main where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>