<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PerformanceReportMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PerformanceReport">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="INTEGER"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="supplierId" column="supplier_id" jdbcType="VARCHAR"/>
            <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
            <result property="channelType" column="channel_type" jdbcType="TINYINT"/>
            <result property="reportNumber" column="report_number" jdbcType="VARCHAR"/>
            <result property="saleId" column="sale_id" jdbcType="VARCHAR"/>
            <result property="channelCompanyId" column="channel_company_id" jdbcType="VARCHAR"/>
            <result property="channelCompanyName" column="channel_company_name" jdbcType="VARCHAR"/>
            <result property="creditLevel" column="credit_level" jdbcType="INTEGER"/>
            <result property="paymentRatio" column="payment_ratio" jdbcType="DECIMAL"/>
            <result property="timeLimit" column="time_limit" jdbcType="FLOAT"/>
            <result property="channelBankName" column="channel_bank_name" jdbcType="VARCHAR"/>
            <result property="channelBankAccount" column="channel_bank_account" jdbcType="VARCHAR"/>
            <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="firstIndustry" column="first_industry" jdbcType="VARCHAR"/>
            <result property="secondIndustry" column="second_industry" jdbcType="VARCHAR"/>
            <result property="provinceId" column="province_id" jdbcType="VARCHAR"/>
            <result property="cityId" column="city_id" jdbcType="VARCHAR"/>
            <result property="customerAddress" column="customer_address" jdbcType="VARCHAR"/>
            <result property="customerContactId" column="customer_contact_id" jdbcType="VARCHAR"/>
            <result property="countyId" column="county_id" jdbcType="VARCHAR"/>
            <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/>
            <result property="prepaidAmount" column="prepaid_amount" jdbcType="DECIMAL"/>
            <result property="usedPrepaidAmount" column="used_prepaid_amount" jdbcType="DECIMAL"/>
            <result property="acceptOrder" column="accept_order" jdbcType="TINYINT"/>
            <result property="rebateReplaceOs" column="rebate_replace_os" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_number,
        process_state,project_id,supplier_id,
        supplier_name,channel_type,report_number,
        sale_id,channel_company_id,channel_company_name,
        credit_level,payment_ratio,time_limit,
        channel_bank_name,channel_bank_account,customer_id,
        customer_name,first_industry,second_industry,
        province_id,city_id,customer_address,
        customer_contact_id,county_id,total_amount,
        prepaid_amount,used_prepaid_amount,accept_order,rebate_replace_os,create_user,
        update_user,create_time,update_time,
        del_flag
    </sql>
    <select id="listYearSamplePackageProductByAgentId"
            resultType="com.topsec.crm.agent.api.entity.PackageProductInfoVO">
        select product_id as productId,sum(product_num) as productNum from crm_flow.performance_report inner join crm_flow.performance_report_product_own
        on performance_report.process_instance_id=performance_report_product_own.process_instance_id
        where performance_report.process_state in (1,2)
          and  TIMESTAMPDIFF(YEAR, date(performance_report.update_time), CURDATE()) &lt;= 1
          and  TIMESTAMPDIFF(YEAR, date(performance_report.update_time), date_sub(CURDATE(), interval 1 year)) &gt; -1
          and performance_report.model_machine=1
          and performance_report.del_flag=0
          and performance_report.supplier_id=#{agentId}
        <if test="productIds!=null and productIds.size()>0">
            and  performance_report_product_own.product_id in
            <foreach item="productId" collection="productIds" open="(" separator="," close=")">
                #{productId}
            </foreach>
        </if>
        group by performance_report_product_own.product_id
    </select>

    <select id="selectPerformanceReportUsedAdvancePayment" resultType="java.math.BigDecimal">
        select IFNULL(sum(used_prepaid_amount),0) as used_prepaid_amount
        from performance_report
        where performance_report.process_state in (1,2) and del_flag=0 and supplier_id=#{supplierId} and channel_company_id=#{channelCompanyId}
    </select>

    <select id="selectUsedAdvancePaymentInfo" resultType="com.topsec.crm.flow.core.entity.PerformanceReport">
        select process_number,process_state,project_id,project_type,channel_company_name,supplier_name,used_prepaid_amount
        from performance_report
        where performance_report.process_state in (1,2) and del_flag=0 and used_prepaid_amount is not null and channel_company_name=#{agentName} and supplier_name=#{generalAgentName}
    </select>

    <select id="selectPriceDifferenceTotal" resultType="java.math.BigDecimal">
        select ifnull(sum(separation.price_difference),0) as price_difference
        from performance_report_product_separation separation
        left join performance_report_product_own own on separation.performance_report_product_id=own.id
        left join performance_report on own.process_instance_id=performance_report.process_instance_id
        where separation.del_flag=0 and own.del_flag=0 and performance_report.del_flag=0 and performance_report.process_instance_id=#{processInstanceId}
    </select>

    <select id="selectOSConsumePriceTotal" resultType="java.math.BigDecimal">
        select ifnull(sum(consume_price*product_num),0) as price_difference
        from (
            select
                CASE WHEN own.deal_price - (own.deal_price + own.rebate_price)*0.02 - hardware_separation.sell_in_price > 0
                    THEN own.deal_price - (own.deal_price + own.rebate_price)*0.02 - hardware_separation.sell_in_price
                    ELSE 0 END AS consume_price ,separation.product_num
            from performance_report_product_separation separation
            left join performance_report_product_separation hardware_separation on separation.hardware_separation_id=hardware_separation.id
            left join performance_report_product_own own on separation.performance_report_product_id=own.id
            left join performance_report on own.process_instance_id=performance_report.process_instance_id
            where separation.del_flag=0 and own.del_flag=0 and performance_report.del_flag=0 and performance_report.process_instance_id=#{processInstanceId}
            and separation.product_classification=2 and separation.os_reality_num=0 and performance_report.rebate_replace_os=1
        ) as a1
    </select>

    <select id="selectTotalOsDifference" resultType="java.math.BigDecimal">
        select ifnull(sum(os_difference_price),0) as total_os_difference_price
        from performance_report_product_separation separation
        left join performance_report_product_own own on separation.performance_report_product_id=own.id
        left join performance_report on own.process_instance_id=performance_report.process_instance_id
        where separation.del_flag=0 and own.del_flag=0 and performance_report.del_flag=0 and performance_report.supplier_id=#{supplierId}
        and separation.product_classification=2 and performance_report.process_state=2
    </select>

    <select id="selectDealTotalPriceByProjectId" resultType="java.math.BigDecimal">
        select ifnull(sum(deal_total_price),0) as deal_total_price
        from performance_report_product_own own
        left join performance_report on own.process_instance_id=performance_report.process_instance_id
        where own.del_flag=0 and performance_report.del_flag=0 and performance_report.project_id=#{projectId} and performance_report.process_state=2
    </select>

    <select id="existsByRecordId" resultType="java.lang.Boolean">
        select count(*)>0 from performance_report_product_own
        where exists(
            select 1 from performance_report where performance_report.del_flag=0
                                             and performance_report.process_instance_id=performance_report_product_own.process_instance_id
        )
        and performance_report_product_own.del_flag=0
        and performance_report_product_own.project_record_id in
        <foreach item="recordId" collection="recordIds" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </select>
</mapper>
