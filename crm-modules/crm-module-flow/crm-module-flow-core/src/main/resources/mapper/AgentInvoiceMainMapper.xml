<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentInvoiceMainMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AgentInvoiceMain">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
        <result property="processState" column="process_state" jdbcType="INTEGER"/>
        <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
        <result property="performanceReportProcessInstanceIds" column="performance_report_process_instance_ids" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="channelCompanyId" column="channel_company_id" jdbcType="VARCHAR"/>
        <result property="channelCompanyName" column="channel_company_name" jdbcType="VARCHAR"/>
        <result property="invoiceType" column="invoice_type" jdbcType="TINYINT"/>
        <result property="invoiceCompanyId" column="invoice_company_id" jdbcType="VARCHAR"/>
        <result property="receiverId" column="receiver_id" jdbcType="VARCHAR"/>
        <result property="processPassTime" column="process_pass_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,performance_report_process_instance_ids,channel_company_id,
        channel_company,invoice_type,invoice_company_id,receiver_id,process_pass_time,del_flag,
        create_user,update_user,create_time,update_time
    </sql>

    <select id="selectPerformanceReportByProcessInstanceIds"
            resultType="com.topsec.crm.flow.api.dto.agentInvoice.AgentInvioceInfoDTO">
        select process_number,supplier_type,supplier_id,channel_company_id,channel_company_name,project.project_no,project.project_name from performance_report
        LEFT JOIN (
        SELECT 1 AS project_type, id as project_id,project_no,project_name FROM crm_project.crm_project_directly WHERE
        del_flag = 0
        union
        select 2 as project_type,id as project_id,project_no,project_name FROM crm_project.crm_project_agent WHERE
        del_flag = 0
        ) project ON project.project_type = performance_report.project_type and project.project_id =
        performance_report.project_id
        where del_flag=0 and process_instance_id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectPerformanceReportList" resultType="com.topsec.crm.flow.api.dto.agentInvoice.AgentInvioceInfoDTO">
        SELECT
        report.process_instance_id,
        report.process_number,
        report.supplier_type,
        report.supplier_id,
        report.channel_company_id,
        report.channel_company_name,
        project.project_no,
        project.project_name,
        CASE WHEN report.channel_company_id = report.customer_id THEN report.sale_id else report.channel_company_id END AS invoice_initiator,
        report.sale_id,
        report.create_user,
        COALESCE(unbilled_amount.total_unbilled_amount, 0) AS total_unbilled_amount
        FROM
        performance_report report
        LEFT JOIN (
        SELECT 1 AS project_type, id as project_id, project_no, project_name
        FROM crm_project.crm_project_directly
        WHERE del_flag = 0
        UNION
        SELECT 2 AS project_type, id AS project_id, project_no, project_name
        FROM crm_project.crm_project_agent
        WHERE del_flag = 0
        ) project ON project.project_type = report.project_type AND project.project_id = report.project_id
        LEFT JOIN (
        SELECT
        process_instance_id,
        SUM(CASE
        WHEN IFNULL(i.invoice_num, 0) = 0 THEN p.product_num * p.deal_price
        ELSE (p.product_num - i.invoice_num) * p.deal_price
        END) AS total_unbilled_amount
        FROM
        performance_report_product_own p
        LEFT JOIN (
        SELECT
        performance_report_process_instance_id,
        agent_invioce_product.performance_report_product_own_record_id,
        SUM(invoice_num) AS invoice_num
        FROM
        agent_invioce_product
        WHERE
        del_flag = 0
        GROUP BY
        performance_report_process_instance_id,
        agent_invioce_product.performance_report_product_own_record_id
        ) AS i ON i.performance_report_process_instance_id = p.process_instance_id and i.performance_report_product_own_record_id=p.id
        where p.del_flag=0
        GROUP BY
        process_instance_id
        ) unbilled_amount ON unbilled_amount.process_instance_id = report.process_instance_id
        WHERE
        report.del_flag = 0
        AND effective_time IS NOT NULL
        AND COALESCE(unbilled_amount.total_unbilled_amount, 0) > 0
        <if test="processNumber != null and processNumber != ''">
            AND report.process_number LIKE CONCAT('%', #{processNumber}, '%')
        </if>
        <if test="processNumberList != null and !processNumberList.isEmpty()">
            AND report.process_number IN
            <foreach item="number" index="index" collection="processNumberList" open="(" separator="," close=")">
                #{number}
            </foreach>
        </if>
        <if test="supplierId != null and supplierId != ''">
            AND report.supplier_id =#{supplierId}
        </if>
        <if test="channelCompanyName != null and channelCompanyName != ''">
            AND report.channel_company_name LIKE CONCAT('%', #{channelCompanyName}, '%')
        </if>
        <if test="projectNo != null and projectNo != ''">
            AND project.project_no LIKE CONCAT('%', #{projectNo}, '%')
        </if>
        <if test="projectName != null and projectName != ''">
            AND project.project_name LIKE CONCAT('%', #{projectName}, '%')
        </if>

        ORDER BY effective_time
    </select>

</mapper>
