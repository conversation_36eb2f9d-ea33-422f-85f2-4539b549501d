<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AdBorrowForProbationBackSnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AdBorrowForProbationBackSn">
        <id column="id" property="id" />
        <result column="ad_back_id" property="adBackId" />
        <result column="ad_sn_id" property="adSnId" />
        <result column="device_id" property="deviceId" />
        <result column="sn" property="sn" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <resultMap id="VOResultMap" type="com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationBackSnVO">
        <id column="id" property="id" />
        <result column="sn" property="sn" />
        <association property="productVO" javaType="com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationProductVO">
            <result column="stuff_code" property="stuffCode" />
            <result column="pn_code" property="pnCode" />
            <result column="product_category" property="productCategory" />
            <result column="general_agent_name" property="generalAgentName"  />
            <result column="sell_in_price" property="sellInPrice"  />
        </association>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ad_back_id, ad_sn_id, sn, create_time, update_time, create_user, update_user, del_flag
    </sql>

    <select id="selectBackSnVOListByProcessId" resultMap="VOResultMap">
        SELECT a.id,a.sn,d.stuff_code,d.pn_code,d.product_category,d.general_agent_name,d.sell_in_price
        from ad_borrow_for_probation_back_sn as a
        left JOIN ad_borrow_for_probation_back as b on a.ad_back_id=b.id and b.del_flag=0
        left JOIN ad_borrow_for_probation_product_sn as c on a.ad_sn_id=c.id and c.del_flag=0
        left join ad_borrow_for_probation_product as d on c.ad_product_id=d.id and d.del_flag=0
        where b.process_instance_id=#{processInstanceId} and a.del_flag=0
    </select>
</mapper>
