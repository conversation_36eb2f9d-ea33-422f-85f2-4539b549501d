<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.topsec.crm.flow.core.mapper.ContractProductSnMapper">

    <resultMap type="com.topsec.crm.flow.core.entity.ContractProductSn" id="contractProductSnMap">
        <result property="contractRecordId" column="contract_record_id"/>
        <result property="projectRecordId" column="project_record_id"/>
        <result property="projectProbationDeviceId" column="project_probation_device_id"/>
        <result property="sn" column="sn"/>
        <result property="type" column="type"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getContractReviewMainByDeviceId" resultType="com.topsec.crm.flow.core.entity.ContractReviewMain">
        select * from contract_review_main where id = (
        select contract_review_id from contract_review_product_own where del_flag=0 and id in (
        select contract_record_id from contract_product_sn where del_flag=0 and project_probation_device_id=#{deviceId}
        )  ORDER BY create_time desc limit 1)
    </select>


    <select id="queryChangeSalesSn" resultType="com.topsec.crm.flow.api.dto.contractreview.sninfo.ChangeSaleSnVO">
        select sn COLLATE utf8mb4_unicode_ci as psn ,crm.process_number,crm.contract_number,null as quoted_price, crm.update_time, ccpo.project_product_own_id as record_id,crm.process_instance_id from
            contract_product_sn cps
                left join contract_review_product_own ccpo on cps.contract_record_id = ccpo.id
                left join contract_review_main crm on cps.contract_id = crm.id
        where cps.del_flag = 0 and ccpo.del_flag = 0 and crm.del_flag = 0 and crm.is_important_contract_in=0
            <if test="query.type == 1">
                and cps.type='借转销'
            </if>
            <if test="query.type == 2">
                and cps.type='专项备货'
            </if>
            <if test="query.psn != null and query.psn != ''">
                and cps.sn like concat('%', #{query.psn}, '%')
            </if>
            <if test="query.processNumber != null and query.processNumber != ''">
                and crm.process_number like concat('%', #{query.processNumber}, '%')
            </if>
            <if test="query.contractNumber != null and query.contractNumber != ''">
                and crm.contract_number like concat('%', #{query.contractNumber}, '%')
            </if>
            <if test="query.projectId != null and query.projectId != ''">
                and crm.project_id = #{query.projectId}
            </if>

        union
        SELECT psn,pr.process_number ,pr.signing_contract_number as contract_number ,prps.quoted_price ,pr.update_time, null as record_id,pr.process_instance_id
        from performance_report_product_separation_sn prpss
                 left join performance_report_product_separation prps on prpss.performance_report_product_separation_id =prps.id
                 left join performance_report_product_own prpo on prps.performance_report_product_id =prpo.id
                 left join performance_report pr on prpo.process_instance_id =pr.process_instance_id
        where prpss.del_flag =0 and prps.del_flag =0 and prpo.del_flag =0 and pr.del_flag =0 and pr.supplier_type =1
        <if test="query.type == 1">
          and prps.borrow_for_forward =1
        </if>
        <if test="query.type ==2">
          and prps.special_item =1
        </if>
        <if test="query.psn != null and query.psn != ''">
            and prpss.psn like concat('%', #{query.psn}, '%')
        </if>
        <if test="query.processNumber != null and query.processNumber != ''">
            and pr.process_number like concat('%', #{query.processNumber}, '%')
        </if>
        <if test="query.contractNumber != null and query.contractNumber != ''">
            and pr.signing_contract_number like concat('%', #{query.contractNumber}, '%')
        </if>
        <if test="query.projectId != null and query.projectId != ''">
            and pr.project_id = #{query.projectId}
        </if>

        order by update_time desc
    </select>
</mapper>