<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentInvoiceContactMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AgentInvoiceContact">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="agentInvoicingCompanyId" column="agent_invoicing_company_id" jdbcType="VARCHAR"/>
            <result property="invoicingCompany" column="invoicing_company" jdbcType="VARCHAR"/>
            <result property="receiver" column="receiver" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="postalCode" column="postalCode" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,agent_invoicing_company_id,invoicing_company,
        receiver,address,email,
        phone,postalCode,del_flag,
        create_user,update_user,create_time,
        update_time
    </sql>
</mapper>
