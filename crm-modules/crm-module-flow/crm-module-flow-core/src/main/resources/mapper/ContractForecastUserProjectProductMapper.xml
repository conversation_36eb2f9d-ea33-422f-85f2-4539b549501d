<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ContractForecastUserProjectProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ContractForecastUserProjectProduct">
        <id column="id" property="id" />
        <result column="forecast_user_project_id" property="forecastUserProjectId" />
        <result column="product_record_id" property="productRecordId" />
        <result column="stuff_code" property="stuffCode" />
        <result column="product_category" property="productCategory" />
        <result column="pn_code" property="pnCode" />
        <result column="product_num" property="productNum" />
        <result column="deal_total_price" property="dealTotalPrice" />
        <result column="forecast_sign_date" property="forecastSignDate" />
        <result column="forecast_sign_rate" property="forecastSignRate" />
        <result column="forecast_delivery_date" property="forecastDeliveryDate" />
        <result column="forecast_comfirm_date" property="forecastComfirmDate" />
        <result column="biding" property="biding" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, forecast_user_project_id, product_record_id, stuff_code, product_category, pn_code, product_num, deal_total_price, forecast_sign_date, forecast_sign_rate, forecast_delivery_date, forecast_comfirm_date, biding, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
