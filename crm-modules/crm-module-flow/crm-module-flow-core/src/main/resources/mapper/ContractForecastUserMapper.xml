<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ContractForecastUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ContractForecastUser">
        <id column="id" property="id" />
        <result column="forecast_id" property="forecastId" />
        <result column="project_leader_id" property="projectLeaderId" />
        <result column="project_leader_name" property="projectLeaderName" />
        <result column="current_month_contract_amount" property="currentMonthContractAmount" />
        <result column="current_month_bid_amount" property="currentMonthBidAmount" />
        <result column="sprint_target" property="sprintTarget" />
        <result column="least_target" property="leastTarget" />
        <result column="bid_amount" property="bidAmount" />
        <result column="delivery_amount" property="deliveryAmount" />
        <result column="comfirm_amount" property="comfirmAmount" />
        <result column="sprint_target_year" property="sprintTargetYear" />
        <result column="least_target_year" property="leastTargetYear" />
        <result column="sprint_target_year_rate" property="sprintTargetYearRate" />
        <result column="least_target_year_rate" property="leastTargetYearRate" />
        <result column="january" property="january" />
        <result column="february" property="february" />
        <result column="march" property="march" />
        <result column="april" property="april" />
        <result column="may" property="may" />
        <result column="june" property="june" />
        <result column="july" property="july" />
        <result column="august" property="august" />
        <result column="september" property="september" />
        <result column="october" property="october" />
        <result column="november" property="november" />
        <result column="december" property="december" />
        <result column="sign_fifty" property="signFifty" />
        <result column="sign_sixty" property="signSixty" />
        <result column="sign_seventy" property="signSeventy" />
        <result column="sign_eighty" property="signEighty" />
        <result column="sign_ninety" property="signNinety" />
        <result column="sign_hundred" property="signHundred" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, forecast_id, project_leader_id, project_leader_name, current_month_contract_amount, current_month_bid_amount, sprint_target, least_target, bid_amount, delivery_amount, comfirm_amount, sprint_target_year, least_target_year, sprint_target_year_rate, least_target_year_rate, january, february, march, april, may, june, july, august, september, october, november, december, sign_fifty, sign_sixty, sign_seventy, sign_eighty, sign_ninety, sign_hundred, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
