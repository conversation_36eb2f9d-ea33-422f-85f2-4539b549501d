<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PerformanceReportContractDeliveryDetailMapper">

    <update id="deleteContractDeliveryDetail" parameterType="String">
        update performance_report_contract_delivery_detail
        left join performance_report_contract_delivery on performance_report_contract_delivery_detail.contract_delivery_id = performance_report_contract_delivery.id
        set performance_report_contract_delivery_detail.del_flag=1
        where performance_report_contract_delivery_detail.del_flag=0 and process_instance_id=#{processInstanceId}
    </update>
</mapper>
