<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ThreeProcurementReviewMainMapper">
    
    <resultMap type="com.topsec.crm.flow.core.entity.ThreeProcurementReviewMain" id="ThreeProcurementReviewMainResult">
        <result property="id"    column="id"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="parentProcessInstanceId"    column="parent_process_instance_id"    />
        <result property="processState"    column="process_state"    />
        <result property="processNumber"    column="process_number"    />
        <result property="projectId"    column="project_id"    />
        <result property="contractId"    column="contract_id"    />
        <result property="requirementTime"    column="requirement_time"    />
        <result property="supplier"    column="supplier"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="area"    column="area"    />
        <result property="supplierAddr"    column="address"    />
        <result property="contacts"    column="contacts"    />
        <result property="contactInformation"    column="contact_information"    />
        <result property="procurementMethod"    column="procurement_method"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="sm"    column="sm"    />
        <result property="signCompany"    column="sign_company"    />
        <result property="reason"    column="reason"    />
        <result property="consignee"    column="consignee"    />
        <result property="consigneeMethod"    column="consignee_method"    />
        <result property="consigneeProvince"    column="consignee_province"    />
        <result property="consigneeCity"    column="consignee_city"    />
        <result property="consigneeArea"    column="consignee_area"    />
        <result property="consigneeAddress"    column="consignee_address"    />
        <result property="attachmentIds" column="attachment_ids" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
    </resultMap>

    <sql id="selectThreeProcurementReviewMainVo">
        select id, process_instance_id, process_state, process_number, project_id, contract_id, requirement_time, supplier, province, city, area, address, contacts, contact_information, procurement_method, payment_method, sm, sign_company, reason, consignee, consignee_method, consignee_province, consignee_city, consignee_area, consignee_address, attachment_ids, del_flag, create_time, update_time, create_user, update_user from three_procurement_review_main
    </sql>





    <delete id="deleteThreeProcurementReviewMainByIds" parameterType="String">
        delete from three_procurement_review_main where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>