<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentAuthPaymentDisburseDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AgentAuthPaymentDisburseDetail">
        <id column="id" property="id" />
        <result column="disburse_id" property="disburseId" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="performance_report_process_instance_id" property="performanceReportProcessInstanceId" />
        <result column="performance_number" property="performanceNumber" />
        <result column="write_off_price" property="writeOffPrice" />
        <result column="supplier_id" property="supplierId" />
        <result column="supplier_name" property="supplierName" />
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="remark" property="remark" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, disburse_id, process_instance_id, performance_report_process_instance_id, performance_number, write_off_price, supplier_id, supplier_name, customer_id, customer_name, remark, create_user, create_time, update_user, update_time, del_flag
    </sql>

</mapper>
