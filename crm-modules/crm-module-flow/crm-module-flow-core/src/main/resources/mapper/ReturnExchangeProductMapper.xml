<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ReturnExchangeProductMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ReturnExchangeProduct">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="recordId" column="record_id" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="stuffCode" column="stuff_code" jdbcType="VARCHAR"/>
            <result property="pn" column="pn" jdbcType="VARCHAR"/>
            <result property="quantity" column="quantity" jdbcType="INTEGER"/>
            <result property="productLic" column="product_lic" jdbcType="INTEGER"/>
            <result property="quotedTotalPrice" column="quoted_total_price" jdbcType="DECIMAL"/>
            <result property="dealTotalPrice" column="deal_total_price" jdbcType="DECIMAL"/>
            <result property="invoicedAmount" column="invoiced_amount" jdbcType="DECIMAL"/>
            <result property="taxRate" column="tax_rate" jdbcType="DECIMAL"/>
            <result property="discount" column="discount" jdbcType="DECIMAL"/>
            <result property="psn" column="psn" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>
