<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForCompensateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForCompensate">
        <id column="id" property="id" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="process_state" property="processState" />
        <result column="process_number" property="processNumber" />
        <result column="product_company_account" property="productCompanyAccount" />
        <result column="classify" property="classify" />
        <result column="responsible_man_id" property="responsibleManId" />
        <result column="responsible_man_name" property="responsibleManName" />
        <result column="approved_price" property="approvedPrice" />
        <result column="payable_price" property="payablePrice" />
        <result column="receive_price" property="receivePrice" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, process_instance_id, process_state, process_number, product_company_account, classify, responsible_man_id,
          responsible_man_name, approved_price, payable_price, receive_price,
          remark, create_time, update_time, create_user, update_user, del_flag
    </sql>

    <sql id="selectBorrowForCompensateVo">
        select id, process_instance_id, process_state, process_number, product_company_account, classify, responsible_man_id,
               responsible_man_name, approved_price, payable_price, receive_price,
               remark, create_time, update_time, create_user, update_user, del_flag
        from borrow_for_compensate
    </sql>
    <select id="queryLatestBorrowForCompensateByDeviceId" resultMap="BaseResultMap">
        <include refid="selectBorrowForCompensateVo"/>
        WHERE del_flag=0 AND id =
            (select compensate_id from borrow_for_compensate_product
                where project_probation_device_id = #{deviceId}
                 ORDER BY create_time desc limit 1
            )
    </select>
    <select id="queryAllBorrowForForwardByDeviceId" resultMap="BaseResultMap">
        <include refid="selectBorrowForCompensateVo"/>
        WHERE del_flag=0 AND id in
        (
            select compensate_id from borrow_for_compensate_product
            where project_probation_device_id = #{deviceId}
        )
    </select>

</mapper>
