<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.SalesAgreementDocumentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.SalesAgreementDocument">
        <id column="id" property="id" />
        <result column="sales_agreement_main_id" property="salesAgreementMainId" />
        <result column="effective_state" property="effectiveState" />
        <result column="pages" property="pages" />
        <result column="copies" property="copies" />
        <result column="doc_id" property="docId" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="process_number" property="processNumber" />
        <result column="dossier_no" property="dossierNo" />
        <result column="procurement_method" property="procurementMethod" />
        <result column="joint_management_confirm" property="jointManagementConfirm" />
        <result column="legal_affairs_confirm" property="legalAffairsConfirm" />
        <result column="legal_confirm_time" property="legalConfirmTime" />
        <result column="management_confirm_time" property="managementConfirmTime" />
        <result column="del_flag" property="delFlag" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="process_state" property="processState" />
        <result column="agreement_process_instance_Id" property="agreementProcessInstanceId" />
        <result column="contract_owner_dept_id" property="contractOwnerDeptId"/>
        <result column="contract_owner_dept_name" property="contractOwnerDeptName"/>
        <result column="apply_user_id" property="applyUserId"/>
        <result column="apply_user_name" property="applyUserName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,contract_owner_dept_id,apply_user_name,apply_user_id,contract_owner_dept_name, sales_agreement_main_id, effective_state, pages, copies, doc_id, process_instance_id, process_number, dossier_no, procurement_method, joint_management_confirm, legal_affairs_confirm, legal_confirm_time, management_confirm_time, del_flag, create_user, update_user, create_time, update_time, process_state, agreement_process_instance_Id
    </sql>


    <select id="listAll" resultType="com.topsec.crm.flow.api.vo.salesAgreementDocment.SalesAgreemenDocVO">
        select  sad.id, sad.sales_agreement_main_id, sad.effective_state, sad.pages, sad.copies, sad.doc_id, sad.process_instance_id, sad.process_number,
        sad.dossier_no, sad.procurement_method, sad.joint_management_confirm, sad.legal_affairs_confirm, sad.legal_confirm_time, sad.management_confirm_time, sad.del_flag,
        sad.create_user, sad.update_user, sad.create_time, sad.update_time, sad.process_state,sarm.agreement_name,sarm.process_number as agreementProcessNumber
        ,sarm.agreement_classification,sarm.process_instance_id as agreementProcessInstanceId,sarm.agreement_manager_name,sarm.sign_company_name
        from sales_agreement_document sad,sales_agreement_review_main sarm
            <where>
                sad.sales_agreement_main_id = sarm.id and sad.del_flag = 0
                <if test="query.agreementName!=null and query.agreementName!=''">
                    and sarm.agreement_name like  CONCAT('%',#{query.agreementName},'%')
                </if>
                <if test="query.agreementProcessNumber!=null and query.agreementProcessNumber!=''">
                    and sarm.process_number like  CONCAT('%',#{query.agreementProcessNumber},'%')
                </if>
                <if test="query.processNumber!=null and query.processNumber!=''">
                    and sad.process_number like  CONCAT('%',#{query.processNumber},'%')
                </if>
                <if test="query.processState!=null and query.processState != 9">
                    and sad.process_state = #{query.processState}
                </if>
                <if test="query.processState == 9 ">
                    and sad.process_state is null
                </if>
                <if test="query.agreementClassification!=null and query.agreementClassification!=''">
                    and sarm.agreement_classification  = #{query.agreementClassification}
                </if>
                <if test="personIdList != null and personIdList.size() > 0">
                    and (sarm.agreement_manager_person_id in
                    <foreach collection="personIdList" item="personId" open="(" separator="," close=")">
                        #{personId}
                    </foreach>
                    <if test="processInstanceIds != null and processInstanceIds.size() > 0">
                        or (sarm.process_instance_id in
                        <foreach collection="processInstanceIds" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                        )
                    </if>
                    )
                </if>
            </where>
        order by sad.update_time desc
    </select>
</mapper>
