<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.CostContractOriginalSignatureMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.CostContractOriginalSignature">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="originalId" column="original_id" jdbcType="VARCHAR"/>
            <result property="signature" column="signature" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,original_id,signature,
        remarks,create_user,update_user,
        create_time,update_time,del_flag
    </sql>
</mapper>
