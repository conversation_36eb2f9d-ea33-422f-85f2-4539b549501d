<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.topsec.crm.flow.core.mapper.UnGenerateLoanCollectionProcessMapper">

  <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.UnGenerateLoanCollectionProcess">
    <id column="id" jdbcType="VARCHAR" property="id"/>
    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
    <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.topsec.crm.flow.core.entity.UnGenerateLoanCollectionProcess">
    <result column="remark" jdbcType="LONGVARCHAR" property="remark"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, process_instance_id, create_time, update_time, create_user, update_user, del_flag
  </sql>
  <sql id="Blob_Column_List">
    remark
  </sql>
</mapper>
