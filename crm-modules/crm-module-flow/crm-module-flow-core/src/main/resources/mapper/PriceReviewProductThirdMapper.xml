<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PriceReviewProductThirdMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PriceReviewProductThird">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="recordId" column="record_id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="stuffCode" column="stuff_code" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="supplierId" column="supplier_id" jdbcType="VARCHAR"/>
            <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
            <result property="productModel" column="product_model" jdbcType="VARCHAR"/>
            <result property="finalTotalPrice" column="final_total_price" jdbcType="DECIMAL"/>
            <result property="productNum" column="product_num" jdbcType="INTEGER"/>
            <result property="purchasePrice" column="purchase_price" jdbcType="DECIMAL"/>
            <result property="dealPrice" column="deal_price" jdbcType="DECIMAL"/>
            <result property="taxRate" column="tax_rate" jdbcType="DECIMAL"/>
            <result property="productPeriodStart" column="product_period_start" jdbcType="DATE"/>
            <result property="productPeriodEnd" column="product_period_end" jdbcType="DATE"/>
            <result property="productPeriod" column="product_period" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>

    </resultMap>


</mapper>
