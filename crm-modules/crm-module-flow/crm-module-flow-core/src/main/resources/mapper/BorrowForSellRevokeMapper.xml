<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForSellRevokeMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForSellRevoke">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="INTEGER"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="projectNo" column="project_no" jdbcType="VARCHAR"/>
            <result property="borrowForSellProcessInstanceId" column="borrow_for_sell_process_instance_id" jdbcType="VARCHAR"/>
            <result property="borrowForSellProcessNumber" column="borrow_for_sell_process_number" jdbcType="VARCHAR"/>
            <result property="applyType" column="apply_type" jdbcType="VARCHAR"/>
            <result property="borrowerName" column="borrower_name" jdbcType="VARCHAR"/>
            <result property="salesman" column="salesman" jdbcType="VARCHAR"/>
            <result property="salesmanPersonId" column="salesman_person_id" jdbcType="VARCHAR"/>
            <result property="salesmanDept" column="salesman_dept" jdbcType="VARCHAR"/>
            <result property="situationDescription" column="situation_description" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,project_id,project_no,
        borrow_for_sell_process_instance_id,borrow_for_sell_process_number,apply_type,
        borrower_name,salesman,salesman_person_id,
        salesman_dept,situation_description,create_time,
        update_time,create_user,update_user,
        del_flag
    </sql>


    <select id="borrowForSellRevokePage" resultType="com.topsec.crm.flow.api.dto.borrowForSellRevoke.RevokePageVO">
        SELECT id,process_instance_id, process_number, salesman, salesman_dept, salesman_person_id, create_time
        FROM borrow_for_sell_revoke
        <where>
            del_flag = 0
            <if test="query.processNumber != null and query.processNumber != ''">
                AND process_number = #{query.processNumber}
            </if>
            <if test="query.salesmanPersonId != null and query.salesmanPersonId != ''">
                AND salesman_person_id = #{query.salesmanPersonId}
            </if>
            <if test="query.startTime != null">
                AND create_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND create_time &lt; #{query.endTime}
            </if>
            <if test="query.dataScopeParam.personIdList != null and !query.dataScopeParam.personIdList.isEmpty()">
                AND create_user IN
                <foreach item="personId" index="index" collection="query.dataScopeParam.personIdList" open="(" separator="," close=")">
                    #{personId}
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>
