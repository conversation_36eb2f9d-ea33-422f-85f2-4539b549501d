<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.topsec.crm.flow.core.mapper.TargetedInventoryPreparationProductOwnMapper">

  <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.TargetedInventoryPreparationProductOwn">
    <id column="id" jdbcType="VARCHAR" property="id"/>
    <result column="targeted_inventory_preparation_main_id" jdbcType="VARCHAR" property="targetedInventoryPreparationMainId"/>
    <result column="project_product_own_id" jdbcType="VARCHAR" property="projectProductOwnId"/>
    <result column="product_lic" jdbcType="INTEGER" property="productLic"/>
    <result column="product_num" jdbcType="INTEGER" property="productNum"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
    <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
    <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, targeted_inventory_preparation_main_id, crm_project_product_own_id, product_lic, product_num, del_flag, create_user, 
	update_user, create_time, update_time
  </sql>

  <select id="getSaleSwitchingByProjectId" resultType="Map">
    select own.project_product_own_id,main.sale_switching from targeted_inventory_preparation_main as main
    left join targeted_inventory_preparation_product_own as own on main.id=own.targeted_inventory_preparation_main_id
    where main.del_flag=0 and own.del_flag=0 and project_id=#{projectId}
  </select>

  <select id="getByRowIds" resultType="com.topsec.crm.flow.core.entity.TargetedInventoryPreparationProductOwn">
    select project_product_own_id,product_id,(select targeted_inventory_preparation_main_id from targeted_inventory_preparation_product_own as main_own where main_own.project_product_own_id=own.project_product_own_id and del_flag=0 ORDER BY create_time asc limit 1 ) as targeted_inventory_preparation_main_id
    from targeted_inventory_preparation_product_own as own
    where own.del_flag=0 and own.project_product_own_id in
    <foreach item="id" collection="rowIds" open="(" separator="," close=")">
      #{id}
    </foreach>
    GROUP BY project_product_own_id,product_id
  </select>

</mapper>
