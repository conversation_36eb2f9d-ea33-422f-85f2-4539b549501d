<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AdBorrowForProbationProductSnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AdBorrowForProbationProductSn">
        <id column="id" property="id" />
        <result column="ad_borrow_id" property="adBorrowId" />
        <result column="ad_product_id" property="adProductId" />
        <result column="device_id" property="deviceId" />
        <result column="sn" property="sn" />
        <result column="ad_delivery_id" property="adDeliveryId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <resultMap id="VOResultMap" type="com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationProductSnVO">
        <id column="id" property="id" />
        <result column="sn" property="sn" />
        <result column="valid_count" property="validCount" />
        <association property="productVO" javaType="com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationProductVO">
            <result column="stuff_code" property="stuffCode" />
            <result column="pn_code" property="pnCode" />
            <result column="product_category" property="productCategory" />
            <result column="general_agent_name" property="generalAgentName"  />
            <result column="sell_in_price" property="sellInPrice"  />
        </association>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ad_borrow_id, ad_product_id, sn, ad_delivery_id, create_time, update_time, create_user, update_user, del_flag
    </sql>
    <select id="selectSnVOListByProcessId" resultMap="VOResultMap">
        SELECT m1.*,m2.valid_count
        from(
                SELECT a.id,a.sn,c.stuff_code,c.pn_code,c.product_category,c.general_agent_name,c.sell_in_price
                from ad_borrow_for_probation_product_sn as a
                left JOIN ad_borrow_for_probation as b on a.ad_borrow_id=b.id and b.del_flag=0
                left JOIN ad_borrow_for_probation_product as c on a.ad_product_id=c.id and c.del_flag=0
                where b.process_instance_id=#{processInstanceId} and a.del_flag=0
            ) as m1
                LEFT JOIN(
                    select a.sn,count(b.id) as valid_count
                    from ad_borrow_for_probation_product_sn as a
                             LEFT JOIN ad_borrow_for_probation as b on a.ad_borrow_id=b.id and b.process_state=2 and b.del_flag=0
                    where a.del_flag=0
                    GROUP BY a.sn
        ) as m2 on m1.sn=m2.sn
    </select>

    <select id="selectSnListByProcessId" resultMap="VOResultMap">
        SELECT a.id,a.sn,c.stuff_code,c.pn_code,c.product_category,c.general_agent_name
        from ad_borrow_for_probation_product_sn as a
        left JOIN ad_borrow_for_probation as b on a.ad_borrow_id=b.id and b.del_flag=0
        left JOIN ad_borrow_for_probation_product as c on a.ad_product_id=c.id and c.del_flag=0
        where b.process_instance_id=#{processInstanceId} and a.del_flag=0 and a.id not in(
            select d.ad_sn_id from ad_borrow_for_probation_back_sn as d
            left join ad_borrow_for_probation_back as e on d.ad_back_id=e.id
            where e.ad_borrow_process_id=#{processInstanceId} and d.del_flag=0 and e.del_flag=0
        )
    </select>

    <select id="selectSnVOListByDeliveryId" resultMap="VOResultMap">
        SELECT a.id,a.sn,c.stuff_code,c.pn_code,c.product_category,c.general_agent_name
        from ad_borrow_for_probation_product_sn as a
        left JOIN ad_borrow_for_probation as b on a.ad_borrow_id=b.id and b.del_flag=0
        left JOIN ad_borrow_for_probation_product as c on a.ad_product_id=c.id and c.del_flag=0
        where a.ad_delivery_id=#{deliveryId} and a.del_flag=0
    </select>

    <select id="getSnValidCountBySns" resultMap="VOResultMap">
        select a.sn,count(b.id) as valid_count
        from ad_borrow_for_probation_product_sn as a
        LEFT JOIN ad_borrow_for_probation as b on a.ad_borrow_id=b.id and b.process_state=2 and b.del_flag=0
        LEFT JOIN ad_borrow_for_probation_product as c on a.ad_product_id=c.id and c.general_agent_name=#{ZDname} and c.del_flag=0
        where a.sn in
              <foreach item="item" collection="productSnList" open="(" separator="," close=")">
                  #{item}
              </foreach> and a.del_flag=0
        GROUP BY a.sn
    </select>

</mapper>
