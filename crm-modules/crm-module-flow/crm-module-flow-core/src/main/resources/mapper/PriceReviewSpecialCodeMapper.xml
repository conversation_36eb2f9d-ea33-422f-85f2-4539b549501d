<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PriceReviewSpecialCodeMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PriceReviewSpecialCode">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="recordId" column="record_id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="relatedDepartmentId" column="related_department_id" jdbcType="VARCHAR"/>
            <result property="evaluationResult" column="evaluation_result" jdbcType="TINYINT"/>
            <result property="operationMaintenanceWorkloadRatio" column="operation_maintenance_workload_ratio" jdbcType="DECIMAL"/>
            <result property="onSiteSupportMode" column="on_site_support_mode" jdbcType="TINYINT"/>
            <result property="maintainInvestment" column="maintain_investment" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="added" column="added" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_id,record_id,
        process_instance_id,related_department_id,evaluation_result,
        operation_maintenance_workload_ratio,on_site_support_mode,maintain_investment,
        special_code_approval_info,added,create_time,
        update_time,create_user,update_user
    </sql>
</mapper>
