<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ThreeProcurementReviewInvoicingMapper">
    
    <resultMap type="com.topsec.crm.flow.core.entity.ThreeProcurementReviewInvoicing" id="ThreeProcurementReviewInvoicingResult">
        <result property="id"    column="id"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="invoicingPrice"    column="Invoicing_price"    />
        <result property="invoicingTime"    column="Invoicing_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
    </resultMap>

    <sql id="selectThreeProcurementReviewInvoicingVo">
        select id, process_instance_id, Invoicing_price, Invoicing_time, del_flag, create_time, update_time, create_user, update_user from three_procurement_review_invoicing
    </sql>


    



    <delete id="deleteThreeProcurementReviewInvoicingByIds" parameterType="String">
        delete from three_procurement_review_invoicing where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>