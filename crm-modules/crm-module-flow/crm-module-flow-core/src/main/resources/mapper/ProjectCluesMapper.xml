<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ProjectCluesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ProjectClues">
        <id column="id" property="id" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="process_state" property="processState" />
        <result column="process_number" property="processNumber" />
        <result column="consult_type" property="consultType" />
        <result column="consult_date" property="consultDate" />
        <result column="consult_class_one" property="consultClassOne" />
        <result column="consult_class_two" property="consultClassTwo" />
        <result column="consult_purpose" property="consultPurpose" />
        <result column="consult_desc" property="consultDesc" />
        <result column="remark" property="remark" />
        <result column="customer_type" property="customerType" />
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="industry_one" property="industryOne" />
        <result column="industry_two" property="industryTwo" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="district" property="district" />
        <result column="follow" property="follow" />
        <result column="follow_dept_id" property="followDeptId" />
        <result column="follow_dept_name" property="followDeptName" />
        <result column="not_follow_reason" property="notFollowReason" />
        <result column="follow_user_id" property="followUserId" />
        <result column="follow_user_name" property="followUserName" />
        <result column="clue_effective" property="clueEffective" />
        <result column="project_id" property="projectId" />
        <result column="project_no" property="projectNo" />
        <result column="clue_status" property="clueStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, process_instance_id, process_state, process_number, consult_type, consult_date, consult_class_one, consult_class_two, consult_purpose, consult_desc, remark, customer_type, customer_id, customer_name, industry_one, industry_two, province, city, district, follow, follow_dept_id, follow_dept_name, not_follow_reason, follow_user_id, follow_user_name, clue_effective, project_id, project_no, clue_status, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
