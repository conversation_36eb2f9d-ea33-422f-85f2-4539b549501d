<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForSellCleanDeviceMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForSellCleanDevice">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="borrowForSellCleanId" column="borrow_for_sell_clean_id" jdbcType="VARCHAR"/>
        <result property="cleanMonthly" column="clean_monthly" jdbcType="VARCHAR"/>
        <result property="salesmanDept" column="salesman_dept" jdbcType="VARCHAR"/>
        <result property="salesman" column="salesman" jdbcType="VARCHAR"/>
        <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
        <result property="projectNo" column="project_no" jdbcType="VARCHAR"/>
        <result property="finalCustomer" column="final_customer" jdbcType="VARCHAR"/>
        <result property="borrowForSellProcessNumber" column="borrow_for_sell_process_number" jdbcType="VARCHAR"/>
        <result property="signingType" column="signing_type" jdbcType="TINYINT"/>
        <result property="expectedReturnDate" column="expected_return_date" jdbcType="TIMESTAMP"/>
        <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
        <result property="pn" column="pn" jdbcType="VARCHAR"/>
        <result property="productCategory" column="product_category" jdbcType="VARCHAR"/>
        <result property="psn" column="psn" jdbcType="VARCHAR"/>
        <result property="feedback" column="feedback" jdbcType="VARCHAR"/>
        <result property="expectedProcessingDate" column="expected_processing_date" jdbcType="VARCHAR"/>
        <result property="feedbackExplanation" column="feedback_explanation" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,borrow_for_sell_clean_id,clean_monthly,salesman,salesman_dept,
        project_id,project_no,final_customer,
        borrow_for_sell_process_number,signing_type,expected_return_date,
        material_code,pn,product_category,
        psn,feedback,expected_processing_date,
        feedback_explanation,create_time,update_time,
        create_user,update_user,del_flag
    </sql>
</mapper>
