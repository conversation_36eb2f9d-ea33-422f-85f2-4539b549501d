<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PriceReviewOutsourcingServiceMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PriceReviewProductServiceOutsourcing">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="recordId" column="record_id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="supplierId" column="supplier_id" jdbcType="VARCHAR"/>
            <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
            <result property="productModel" column="product_model" jdbcType="VARCHAR"/>
            <result property="productNum" column="product_num" jdbcType="INTEGER"/>
            <result property="purchasePrice" column="purchase_price" jdbcType="DECIMAL"/>
            <result property="purchaseTotalPrice" column="purchase_total_price" jdbcType="DECIMAL"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,project_id,product_id,
        supplier_id,supplier_name,product_model,
        product_num,purchase_price,purchase_total_price,
        state,remark,del_flag,
        process_instance_id,create_user,update_user,
        create_time,update_time
    </sql>
</mapper>
