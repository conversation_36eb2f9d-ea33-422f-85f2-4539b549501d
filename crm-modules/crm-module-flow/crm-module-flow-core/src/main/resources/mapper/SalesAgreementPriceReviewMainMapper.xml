<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.topsec.crm.flow.core.mapper.SalesAgreementPriceReviewMainMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.topsec.crm.flow.core.entity.SalesAgreementPriceReviewMain" id="agreementPriceReviewMainMap">
        <result property="id" column="id"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="processState" column="process_state"/>
        <result property="processNumber" column="process_number"/>
        <result property="agreementName" column="agreement_name"/>
        <result property="agreementClassification" column="agreement_classification"/>
        <result property="firstLevelIndustryId" column="first_level_industry_id"/>
        <result property="firstLevelIndustryName" column="first_level_industry_name"/>
        <result property="secondaryIndustryId" column="secondary_industry_id"/>
        <result property="secondaryIndustryName" column="secondary_industry_name"/>
        <result property="signCompanyId" column="sign_company_id"/>
        <result property="signCompanyName" column="sign_company_name"/>
        <result property="agreementManagerPersonId" column="agreement_manager_person_id"/>
        <result property="agreementManagerName" column="agreement_manager_name"/>
        <result property="agreementStartTime" column="agreement_start_time"/>
        <result property="agreementEndTime" column="agreement_end_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
    </resultMap>


</mapper>