<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AdBorrowForProbationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.AdBorrowForProbation">
        <id column="id" property="id" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="process_state" property="processState" />
        <result column="process_number" property="processNumber" />
        <result column="process_pass_time" property="processPassTime" />
        <result column="name" property="name" />
        <result column="province_code" property="provinceCode" />
        <result column="city_code" property="cityCode" />
        <result column="county_code" property="countyCode" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="contact_name" property="contactName" />
        <result column="contact_number" property="contactNumber" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, process_instance_id, process_state, process_number, process_pass_time, name, province_id, city_id, county_id, start_time, end_time, contact_name, contact_number, remark, create_time, update_time, create_user, update_user, del_flag
    </sql>

    <select id="selectSnOccupyList" resultType="String">
        select sn
        from ad_borrow_for_probation as a
        LEFT JOIN ad_borrow_for_probation_product as b on a.id=b.ad_borrow_id and b.del_flag=0
        LEFT JOIN ad_borrow_for_probation_product_sn as c on b.id=c.ad_product_id and c.del_flag=0
        where process_state=1
        <if test="agentId != null  and agentId != ''"> and general_agent_id = #{agentId}</if>
        and sn is not null
        and a.del_flag=0
        union all
		select sn
        from ad_borrow_for_probation_back as a
        LEFT JOIN ad_borrow_for_probation_back_sn as c on a.id=c.ad_back_id and c.del_flag=0
        where process_state=1
		<if test="agentId != null  and agentId != ''"> and agent_id_zd = #{agentId}</if>
        and sn is not null
        and a.del_flag=0
    </select>

    <select id="selectALLBackByCreateUser" resultType="String">
        select process_number
        from(
        select a1.process_number,a1.process_instance_id,COUNT(sn) as sn_count
        from ad_borrow_for_probation as a1
        left join ad_borrow_for_probation_product_sn as a2 on a2.ad_borrow_id=a1.id and a2.del_flag=0
        where a1.process_state=2
              <if test="createUser != null  and createUser != ''"> and a1.create_user = #{createUser}</if>
              and a1.del_flag=0
        group by a1.process_number,a1.process_instance_id
        ) as b1
        left join(
        select a2.ad_borrow_process_id,COUNT(sn) as sn_back_count
        from ad_borrow_for_probation_back_sn as a1
        left join ad_borrow_for_probation_back as a2 on a1.ad_back_id=a2.id
        where a2.process_state in(1,2) and a1.del_flag=0 and a2.del_flag=0
        group by a2.ad_borrow_process_id
        ) as b2 on b1.process_instance_id=b2.ad_borrow_process_id
        where IFNULL(sn_count, 0)=IFNULL(sn_back_count, 0)
    </select>

</mapper>
