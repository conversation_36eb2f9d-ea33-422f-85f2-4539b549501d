<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.CostContractReMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.CostContractRe">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="costContractId" column="cost_contract_id" jdbcType="VARCHAR"/>
            <result property="costFilingDetailId" column="cost_filing_detail_id" jdbcType="VARCHAR"/>
            <result property="costType" column="cost_type" jdbcType="VARCHAR"/>
            <result property="cost" column="cost" jdbcType="VARCHAR"/>
            <result property="personnelId" column="personnel_id" jdbcType="VARCHAR"/>
            <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
            <result property="contractId" column="contract_id" jdbcType="VARCHAR"/>
            <result property="contractNo" column="contract_no" jdbcType="VARCHAR"/>
            <result property="contractCollectionRatio" column="contract_collection_ratio" jdbcType="DECIMAL"/>
            <result property="taxRate" column="tax_rate" jdbcType="DECIMAL"/>
            <result property="costMoney" column="cost_money" jdbcType="DECIMAL"/>
            <result property="costMoneyNoTax" column="cost_money_no_tax" jdbcType="DECIMAL"/>
            <result property="contractTaxRate" column="contract_tax_rate" jdbcType="DECIMAL"/>
            <result property="canApplyMoney" column="can_apply_money" jdbcType="DECIMAL"/>
            <result property="applyMoney" column="apply_money" jdbcType="DECIMAL"/>
            <result property="applyMoneyNoTax" column="apply_money_no_tax" jdbcType="DECIMAL"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="projectNo" column="project_no" jdbcType="VARCHAR"/>
            <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,cost_contract_id,cost_filing_detail,
        cost,personnel_id,dept_id,
        contract_id,contract_no,contract_collection_ratio,
        tax_rate,cost_money,cost_money_no_tax,
        contract_tax_rate,can_apply_money,apply_money,
        apply_money_no_tax,project_id,remarks,
        create_user,update_user,create_time,
        update_time,del_flag
    </sql>

</mapper>
