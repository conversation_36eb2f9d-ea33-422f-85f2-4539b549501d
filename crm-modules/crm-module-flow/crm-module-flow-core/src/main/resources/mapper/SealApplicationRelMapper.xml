<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.SealApplicationRelMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.SealApplicationRel">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="contractParty" column="contract_party" jdbcType="VARCHAR"/>
            <result property="matterType" column="matter_type" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="projectNo" column="project_no" jdbcType="VARCHAR"/>
            <result property="authorizeObject" column="authorize_object" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="isBidDocument" column="is_bid_document" jdbcType="TINYINT"/>
            <result property="tenderingProcessInstanceId" column="tendering_process_instance_id" jdbcType="VARCHAR"/>
            <result property="tenderingProcessNumber" column="tendering_process_number" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,seal_application_id,
        contract_party,matter_type,project_id,
        project_no,authorize_object,is_bid_document,
        tendering_process_instance_id,tendering_process_number,authorize_dept_id,
        authorize_dept,remarks,create_user,
        update_user,create_user_name,update_user_name,
        create_time,update_time,del_flag
    </sql>
</mapper>
