<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PriceReviewStatisticsMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PriceReviewStatistics">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="label" column="label" jdbcType="VARCHAR"/>
            <result property="quotedPrice" column="quoted_price" jdbcType="DECIMAL"/>
            <result property="dealPrice" column="deal_price" jdbcType="DECIMAL"/>
            <result property="finalPrice" column="final_price" jdbcType="DECIMAL"/>
            <result property="discount" column="discount" jdbcType="DECIMAL"/>
            <result property="priceSubtraction" column="price_subtraction" jdbcType="DECIMAL"/>
            <result property="priceSubtractionRatio" column="price_subtraction_ratio" jdbcType="DECIMAL"/>
            <result property="grossMargin" column="gross_margin" jdbcType="DECIMAL"/>
            <result property="grossMarginRatio" column="gross_margin_ratio" jdbcType="DECIMAL"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="statisticsCategory" column="statistics_category" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,label,quoted_price,
        deal_price,final_price,discount,
        price_subtraction,price_subtraction_ratio,gross_margin,
        gross_margin_ratio,process_instance_id,create_user,
        create_time,update_user,update_time
    </sql>
</mapper>
