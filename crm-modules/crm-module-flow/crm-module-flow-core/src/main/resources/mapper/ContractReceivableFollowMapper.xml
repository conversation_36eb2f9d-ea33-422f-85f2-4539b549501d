<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ContractReceivableFollowMapper">

    <select id="getContractNumberByCondition" resultType="java.lang.String">
        WITH RankedFollowUps AS (
            SELECT
                crd.contract_number,
                crf.handle_suggest,
                crm.receivable_date,
                ROW_NUMBER() OVER (PARTITION BY crd.contract_number ORDER BY crm.receivable_date DESC) AS rn
            FROM
                contract_receivable_follow crf
                    JOIN
                contract_receivable_detail crd ON crf.receivable_detail_id = crd.id
                    JOIN
                contract_receivable_main crm ON crd.contract_receivable_main_id = crm.id
            WHERE
                crf.del_flag = 0 AND crd.del_flag = 0 AND crm.del_flag = 0 and receivable_source = 1
        )
        SELECT
            contract_number AS contractNumber
        FROM
            RankedFollowUps
        WHERE
            rn = 1
          AND handle_suggest = #{handleSuggest};
    </select>

    <select id="getFollowByCondition" resultType="com.topsec.crm.flow.api.dto.contractreceivable.ContractReceivableDetailDTO">
        select * from (contract_receivable_follow follow left join contract_receivable_detail detail on follow.receivable_detail_id = detail.id)
                          left join contract_receivable_main main on detail.contract_receivable_main_id = main.id
        <where>
            receivable_source=1 and follow.del_flag=0 and detail.del_flag=0 and main.del_flag=0
            <if test="query.contractNumber != null and query.contractNumber != ''">
                 and detail.contract_number like concat('%',#{query.contractNumber} ,'%')
            </if>
            <if test="query.contractOwnerId != null and query.contractOwnerId != ''">
                 and detail.contract_owner_id=#{query.contractOwnerId}
            </if>
            <if test="query.contractOwnerDeptId != null and query.contractOwnerDeptId != ''">
                 and detail.contract_owner_dept_id=#{query.contractOwnerDeptId}
            </if>
            <if test="query.contractCompanyName != null and query.contractCompanyName != ''">
                 and detail.contract_company_name like concat('%',#{query.contractCompanyName} ,'%')
            </if>
            <if test="query.receivableDateStart != null and query.receivableDateStart != ''">
                 and main.receivable_date &gt;= #{query.receivableDateStart}
            </if>
            <if test="query.receivableDateEnd != null and query.receivableDateEnd != ''">
                and main.receivable_date &lt;=#{query.receivableDateEnd}
            </if>
            <if test="query.saleId != null and query.saleId != ''">
                and detail.sale_id=#{query.saleId}
            </if>
        </where>
    </select>

</mapper>