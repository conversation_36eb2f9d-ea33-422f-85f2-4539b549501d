<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.topsec.crm.flow.core.mapper.TargetedInventoryPreparationStockReturnMapper">

  <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.TargetedInventoryPreparationStockReturn">
    <id column="id" jdbcType="VARCHAR" property="id"/>
    <result column="transporte_number" jdbcType="VARCHAR" property="transporteNumber"/>
    <result column="transact_type" jdbcType="VARCHAR" property="transactType"/>
    <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
    <result column="targeted_inventory_preparation_product_own_id" jdbcType="VARCHAR" property="targetedInventoryPreparationProductOwnId"/>
    <result column="crm_row_id" jdbcType="VARCHAR" property="crmRowId"/>
    <result column="erp_create_time" jdbcType="TIMESTAMP" property="erpCreateTime"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, transporte_number, transact_type, business_type, targeted_inventory_preparation_product_own_id, crm_row_id, erp_create_time, 
	remark, create_user, create_time, update_user, update_time, del_flag
  </sql>
</mapper>
