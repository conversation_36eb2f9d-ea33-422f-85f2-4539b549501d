<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PerformanceReportProductSeparationMapper">

    <resultMap type="com.topsec.crm.flow.core.entity.PerformanceReportProductSeparation" id="PerformanceReportProductSeparationResult">
        <result property="id"    column="id"    />
        <result property="performanceReportProductId"    column="performance_report_product_id"    />
        <result property="productId"    column="product_id"    />
        <result property="productClassification"    column="product_classification"    />
        <result property="isDefault"    column="is_default"    />
        <result property="productSpecification"    column="product_specification"    />
        <result property="stuffCode"    column="stuff_code"    />
        <result property="pnCode"    column="pn_code"    />
        <result property="productNum"    column="product_num"    />
        <result property="sellInPrice"    column="sell_in_price"    />
        <result property="quotedPrice"    column="quoted_price"    />
        <result property="osOutNum"    column="os_out_num"    />
        <result property="priceDifference"    column="price_difference"    />
        <result property="osDifferencePrice"    column="os_difference_price"    />
        <result property="totalOsDifferencePrice"    column="total_os_difference_price"    />
        <result property="totalOsDifferenceNum"    column="total_os_difference_num"    />
        <result property="consumePrice"    column="consume_price"    />
        <result property="hardwareSeparationId"    column="hardware_separation_id"    />
        <result property="osSystemNum"    column="os_system_num"    />
        <result property="osRealityNum"    column="os_reality_num"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPerformanceReportProductSeparationVo">
        select id,performance_report_product_id,product_id,product_classification,is_default,stuff_code,product_specification,pn_code,product_num,
        quoted_price,sell_in_price,price_difference,os_out_num,os_difference_price,total_os_difference_price,total_os_difference_num,del_flag,create_user,
        update_user,create_time,update_time,consume_price,hardware_separation_id,os_system_num,os_reality_num
        from performance_report_product_separation
    </sql>

    <resultMap type="com.topsec.crm.flow.api.dto.performancereport.PerformanceReportProductStatisticsDTO" id="PerformanceReportProductStatisticsDTOResult">
        <result property="dealTotalPrice"    column="deal_total_price"    />
        <result property="rebateTotalPrice"    column="rebate_price"    />
        <result property="priceDifferenceTotal"    column="price_difference_total"    />
        <result property="osDifferenceTotalPrice"    column="os_difference_total_price"    />
        <result property="productTotalNum"    column="product_num"    />
    </resultMap>

    <select id="selectProductStatistics" parameterType="String" resultMap="PerformanceReportProductStatisticsDTOResult">
        select ifnull(sum(separation.price_difference),0) as price_difference_total,ifnull(sum(os_difference_price),0) as os_difference_total_price
        from performance_report_product_separation separation
        left join performance_report_product_own on separation.performance_report_product_id=performance_report_product_own.id
        where separation.del_flag=0 and performance_report_product_own.del_flag=0 and process_instance_id = #{id}
    </select>

    <select id="selectSeparationHardwareTotalNum" parameterType="String" resultType="int">
        select IFNULL( sum(product_num),0)
        from performance_report_product_separation
        where del_flag=0 and product_classification=1 and performance_report_product_id=#{id}
    </select>

    <insert id="insertPerformanceReportProductSeparation" useGeneratedKeys="true" keyProperty="id" parameterType="com.topsec.crm.flow.core.entity.PerformanceReportProductSeparation">
        insert into performance_report_product_separation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="performanceReportProductId != null">performance_report_product_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="productClassification != null">product_classification,</if>
            <if test="stuffCode != null">stuff_code,</if>
            <if test="productSpecification != null">product_specification,</if>
            <if test="pnCode != null">pn_code,</if>
            <if test="productNum != null">product_num,</if>
            <if test="sellInPrice != null">sell_in_price,</if>
            <if test="quotedPrice != null">quoted_price,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="osOutNum != null">os_out_num,</if>
            <if test="priceDifference != null">price_difference,</if>
            <if test="osDifferencePrice != null">os_difference_price,</if>
            <if test="totalOsDifferencePrice != null">total_os_difference_price,</if>
            <if test="totalOsDifferenceNum != null">total_os_difference_num,</if>
            <if test="consumePrice != null">consume_price,</if>
            <if test="hardwareSeparationId != null">hardware_separation_id,</if>
            <if test="osSystemNum != null">os_system_num,</if>
            <if test="osRealityNum != null">os_reality_num,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="performanceReportProductId != null">#{performanceReportProductId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productClassification != null">#{productClassification},</if>
            <if test="stuffCode != null">#{stuffCode},</if>
            <if test="productSpecification != null">#{productSpecification},</if>
            <if test="pnCode != null">#{pnCode},</if>
            <if test="productNum != null">#{productNum},</if>
            <if test="sellInPrice != null">#{sellInPrice},</if>
            <if test="quotedPrice != null">#{quotedPrice},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="osOutNum != null">#{osOutNum},</if>
            <if test="priceDifference != null">#{priceDifference},</if>
            <if test="osDifferencePrice != null">#{osDifferencePrice},</if>
            <if test="totalOsDifferencePrice != null">#{totalOsDifferencePrice},</if>
            <if test="totalOsDifferenceNum != null">#{totalOsDifferenceNum},</if>
            <if test="consumePrice != null">#{consumePrice},</if>
            <if test="hardwareSeparationId != null">#{hardwareSeparationId},</if>
            <if test="osSystemNum != null">#{osSystemNum},</if>
            <if test="osRealityNum != null">#{osRealityNum},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="deletePerformanceReportProductSeparation" parameterType="String">
        update performance_report_product_separation as a1
            left join performance_report_product_separation as a2 on a1.hardware_separation_id=a2.id
            set a1.del_flag =1
        where a1.del_flag=0 and a1.performance_report_product_id=#{performanceReportProductId}
          and (a1.borrow_for_forward is null or a1.borrow_for_forward = 0) and (a1.special_item is null or a1.special_item = 0)
          and (a2.borrow_for_forward is null or a2.borrow_for_forward = 0) and (a2.special_item is null or a2.special_item = 0)
    </update>

    <update id="updateOsConsume" parameterType="String">
        update performance_report_product_separation set consume_price=0,os_system_num=0,os_reality_num=0,os_difference_price=0
        where del_flag=0 and performance_report_product_id=#{id} and product_classification=2
    </update>

    <select id="selectBorrowForForwardOrSpecialItemNum" resultType="int">
        select IFNULL( sum(product_num),0)
        from performance_report_product_separation
        where del_flag=0 and performance_report_product_id=#{id} and (borrow_for_forward = 1 or special_item =1)
    </select>

    <select id="selectTotalOsDifferenceNum" resultType="java.math.BigDecimal">
        select IFNULL(#{totalOsDifference}/sell_in_price,0) as total_os_difference_num
        from performance_report_product_separation
        where del_flag=0 and id=#{id}
    </select>

    <select id="selectQuotedPrice" resultType="java.math.BigDecimal">
        select deal_price - (deal_price + IFNULL(rebate_price,0))*0.02 as deal_price
            from performance_report_product_own
            where del_flag=0 and id=#{performanceReportProductId}
    </select>

    <select id="selectOsPrice" resultType="java.math.BigDecimal">
        select ifnull(sum(separation.consume_price*separation.product_num),0) as consume_price
        from performance_report_product_separation separation
                 left join performance_report_product_own on separation.performance_report_product_id=performance_report_product_own.id
        where separation.del_flag=0 and performance_report_product_own.del_flag=0 and process_instance_id = #{processInstanceId} and separation.product_classification=2
            and return_exchange_process_instance_id is null
    </select>
</mapper>
