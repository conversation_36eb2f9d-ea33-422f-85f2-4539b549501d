<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PriceReviewProductOwnMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PriceReviewProductOwn">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="recordId" column="record_id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
            <result property="productLic" column="product_lic" jdbcType="INTEGER"/>
            <result property="productNum" column="product_num" jdbcType="INTEGER"/>
            <result property="quotedPrice" column="quoted_price" jdbcType="DECIMAL"/>
            <result property="quotedTotalPrice" column="quoted_total_price" jdbcType="DECIMAL"/>
            <result property="productType" column="product_type" jdbcType="VARCHAR"/>
            <result property="productSpecification" column="product_specification" jdbcType="VARCHAR"/>
            <result property="stuffCode" column="stuff_code" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="pnCode" column="pn_code" jdbcType="VARCHAR"/>
            <result property="hostSerialNumber" column="host_serial_number" jdbcType="VARCHAR"/>
            <result property="dealPrice" column="deal_price" jdbcType="DECIMAL"/>
            <result property="dealTotalPrice" column="deal_total_price" jdbcType="DECIMAL"/>
            <result property="rebatePrice" column="rebate_price" jdbcType="DECIMAL"/>
            <result property="finalPrice" column="final_price" jdbcType="DECIMAL"/>
            <result property="finalTotalPrice" column="final_total_price" jdbcType="DECIMAL"/>
            <result property="discount" column="discount" jdbcType="DECIMAL"/>
            <result property="taxRate" column="tax_rate" jdbcType="DECIMAL"/>
            <result property="productPeriod" column="product_period" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="splitOutsourcePrice" column="split_outsource_price" jdbcType="DECIMAL"/>
            <result property="productSn" column="product_sn" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="attr" column="attr" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>


    </resultMap>


</mapper>
