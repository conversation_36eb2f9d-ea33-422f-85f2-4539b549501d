<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForSellProductSelectableMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForSellProductSelectable">
        <id column="id" property="id" />
        <result column="borrow_product_id" property="borrowProductId" />
        <result column="stuff_code" property="stuffCode" />
        <result column="pn_code" property="pnCode" />
        <result column="product_category" property="productCategory" />
        <result column="stock" property="stock" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, borrow_product_id, stuff_code, pn_code, product_category, stock, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
