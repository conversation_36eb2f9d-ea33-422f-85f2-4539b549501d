<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.MilitaryInspectionProductMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.MilitaryInspectionProduct">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="militaryProductInspectionId" column="military_product_inspection_id" jdbcType="VARCHAR"/>
            <result property="productOwnId" column="product_own_id" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="quantity" column="quantity" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,military_product_inspection_id,product_id,product_own_id,
        material_code,quantity,create_time,
        update_time,create_user,update_user,
        del_flag
    </sql>
</mapper>
