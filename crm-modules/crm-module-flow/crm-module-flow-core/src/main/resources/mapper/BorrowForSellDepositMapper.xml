<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForSellDepositMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForSellDeposit">
        <id column="id" property="id" />
        <result column="borrow_id" property="borrowId" />
        <result column="deposit_type" property="depositType" />
        <result column="payment_amount" property="paymentAmount" />
        <result column="payment_date" property="paymentDate" />
        <result column="offset_amount" property="offsetAmount" />
        <result column="receive_date" property="receiveDate" />
        <result column="receive_id" property="receiveId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, borrow_id, deposit_type, payment_amount, payment_date, offset_amount, receive_date, receive_id, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
