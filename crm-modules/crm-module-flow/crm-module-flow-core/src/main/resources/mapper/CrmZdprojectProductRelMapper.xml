<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.CrmZdprojectProductRelMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.CrmZdprojectProductRel">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
        <result property="productRecordId" column="product_record_id" jdbcType="VARCHAR"/>
        <result property="performanceProjectRecordId" column="performance_project_record_id" jdbcType="VARCHAR"/>
        <result property="performanceReportRecordId" column="performance_report_record_id" jdbcType="VARCHAR"/>
        <result property="productNum" column="product_num" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,project_id,product_record_id,
        performance_project_record_id,performance_report_record_id,product_num,
        remark,del_flag,create_user,
        update_user,create_time,update_time
    </sql>
    <delete id="deleteByProjectId">
        update crm_zdproject_product_rel set del_flag=1 where project_id = #{projectId}
    </delete>
</mapper>
