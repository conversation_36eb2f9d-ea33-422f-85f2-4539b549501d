<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.FactoryInspectionDetailsMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.FactoryInspectionDetails">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <id property="productId" column="product_id" jdbcType="VARCHAR"/>
            <id property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <id property="quantity" column="quantity" jdbcType="INTEGER"/>
            <result property="militaryProductInspectionId" column="military_product_inspection_id" jdbcType="VARCHAR"/>
            <result property="softwareVersion" column="software_version" jdbcType="VARCHAR"/>
            <result property="actualInspectedCount" column="actual_inspected_count" jdbcType="INTEGER"/>
            <result property="unqualifiedCount" column="unqualified_count" jdbcType="INTEGER"/>
            <result property="unqualifiedType" column="unqualified_type" jdbcType="VARCHAR"/>
            <result property="inspectorPersonId" column="inspector_person_id" jdbcType="VARCHAR"/>
            <result property="inspectionDate" column="inspection_date" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_id,material_code,quantity,military_product_inspection_id,software_version,
        actual_inspected_count,unqualified_count,unqualified_type,
        inspector_person_id,inspection_date,remark,
        create_time,update_time,create_user,
        update_user,del_flag
    </sql>
</mapper>
