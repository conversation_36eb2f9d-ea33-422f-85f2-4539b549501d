<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PerformanceReportCrossAgentSubmitMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PerformanceReportCrossAgentSubmit">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="TINYINT"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="projectType" column="project_type" jdbcType="TINYINT"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="targetAgentId" column="target_agent_id" jdbcType="VARCHAR"/>
            <result property="targetAgentName" column="target_agent_name" jdbcType="VARCHAR"/>
            <result property="targetAgentType" column="target_agent_type" jdbcType="TINYINT"/>
            <result property="targetAgentAreaId" column="target_agent_area_id" jdbcType="VARCHAR"/>
            <result property="channelCompanyId" column="channel_company_id" jdbcType="VARCHAR"/>
            <result property="channelCompanyName" column="channel_company_name" jdbcType="VARCHAR"/>
            <result property="reason" column="reason" jdbcType="VARCHAR"/>
            <result property="effectiveTime" column="effective_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,project_type,project_id,
        target_agent_id,target_agent_name,target_agent_type,
        target_agent_area_id,channel_company_id,channel_company_name,
        reason,effective_time,del_flag,
        create_user,update_user,create_time,
        update_time
    </sql>
</mapper>
