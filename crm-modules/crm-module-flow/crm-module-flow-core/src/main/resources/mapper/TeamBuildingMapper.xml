<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.TeamBuildingMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.TeamBuilding">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="TINYINT"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="projectType" column="project_type" jdbcType="TINYINT"/>
            <result property="projectExpectationsAndTeamRequirements" column="project_expectations_and_team_requirements" jdbcType="VARCHAR"/>
            <result property="projectSummary" column="project_summary" jdbcType="VARCHAR"/>
            <result property="attachmentIds" column="attachment_ids" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="productsAndServicesInvolved" column="products_and_services_involved" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="processPassTime" column="process_pass_time" jdbcType="TIMESTAMP"/>
            <result property="acceptanceTime" column="Acceptance_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,project_id,project_type,
        project_expectations_and_team_requirements,project_summary,attachment_ids,products_and_services_involved
        process_pass_time,Acceptance_time,create_time,
        update_time,create_user,update_user,del_flag
    </sql>
</mapper>
