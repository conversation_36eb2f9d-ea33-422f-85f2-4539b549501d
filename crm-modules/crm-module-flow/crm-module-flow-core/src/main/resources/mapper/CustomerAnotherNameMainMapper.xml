<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.CustomerAnotherNameMainMapper">

    <select id="selectFlowName" resultType="com.topsec.crm.flow.core.entity.CustomerAnotherNameMain">
        select * from customer_another_name_main where process_state = 1
        and JSON_CONTAINS(new_names, CONCAT('"',#{newName},'"'),'$')
    </select>

    <select id="selectFlowNotNowFlow" resultType="com.topsec.crm.flow.core.entity.CustomerAnotherNameMain">
        select * from customer_another_name_main where process_state = 1
        and JSON_CONTAINS(new_names, CONCAT('"',#{newName},'"'),'$')
        and process_instance_id != #{processInstanceId}
    </select>

</mapper>
