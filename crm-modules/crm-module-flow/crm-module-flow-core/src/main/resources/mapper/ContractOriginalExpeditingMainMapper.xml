<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ContractOriginalExpeditingMainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ContractOriginalExpeditingMain">
        <id column="id" property="id" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="process_state" property="processState" />
        <result column="process_number" property="processNumber" />
        <result column="contract_owner_dept_id" property="contractOwnerDeptId" />
        <result column="contract_owner_dept_name" property="contractOwnerDeptName" />
        <result column="process_progress" property="processProgress" />
        <result column="approvers" property="approvers" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="del_flag" property="delFlag" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, process_instance_id, process_state,  apply_user_id, apply_user_name, process_number, contract_owner_dept_id, contract_owner_dept_name, process_progress, approvers, del_flag, create_user, update_user, create_time, update_time
    </sql>

    <select id="approvalPages"   resultMap="BaseResultMap">
        select
        DISTINCT process_instance_id, process_number,process_progress,approvers,create_time,update_time,contract_owner_dept_name,contract_owner_dept_id
        from contract_original_expediting_main
        <where>
            del_flag = 0 and process_state = 1
            <if test="query.contractOwnerDeptId != null and query.contractOwnerDeptId != ''">
                and contract_owner_dept_id = #{query.contractOwnerDeptId}
            </if>

            <if test="query.timeEnd != null">
                and <![CDATA[create_time <= #{query.timeEnd}]]>
            </if>
            <if test="query.timeStart != null">
                and <![CDATA[create_time >= #{query.timeStart}]]>
            </if>
            <if test="query.contractOwnerIds != null and query.contractOwnerIds.size() >0">
                and  contract_owner_dept_id in (select DISTINCT contract_owner_dept_id from contract_original_expediting main where  main.contract_owner_id in
                <foreach collection="query.contractOwnerIds" item="implementer" open="(" separator="," close=")">
                    #{implementer}
                </foreach>
                )
            </if>
            <if test="query.approvers != null and query.approvers.size() >0">
                and
                <foreach collection="query.approvers" item="implementer" open="(" separator="or" close=")">
                    JSON_CONTAINS( approvers  -> '$[*].value', JSON_ARRAY(#{implementer}))
                </foreach>

            </if>
        </where>

        order by update_time desc
    </select>

    <select id="pageExpediting"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from contract_original_expediting_main main
        <where>
            main.del_flag = 0
            <if test="timeStart != null ">
                and <![CDATA[main.create_time >= #{timeStart}]]>
            </if>
            <if test="timeEnd != null">
                and <![CDATA[main.create_time <= #{timeEnd}]]>
            </if>
        </where>

        order by main.create_time desc
    </select>


</mapper>
