<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.topsec.crm.flow.core.mapper.SubLoanCollectionMapper">

  <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.SubLoanCollection">
    <id column="id" jdbcType="VARCHAR" property="id"/>
    <result column="parent_process_instance_id" jdbcType="VARCHAR" property="parentProcessInstanceId"/>
    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId"/>
    <result column="process_state" jdbcType="TINYINT" property="processState"/>
    <result column="process_number" jdbcType="VARCHAR" property="processNumber"/>
    <result column="loan_user_id" jdbcType="VARCHAR" property="loanUserId"/>
    <result column="loan_user_name" jdbcType="VARCHAR" property="loanUserName"/>
    <result column="dep_name" jdbcType="VARCHAR" property="depName"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
    <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.topsec.crm.flow.core.entity.SubLoanCollection">
    <result column="remark" jdbcType="LONGVARCHAR" property="remark"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, parent_process_instance_id, process_instance_id, process_state, process_number, loan_user_id, loan_user_name, 
	dep_name, create_time, update_time, create_user, update_user, del_flag
  </sql>
  <sql id="Blob_Column_List">
    remark
  </sql>
</mapper>
