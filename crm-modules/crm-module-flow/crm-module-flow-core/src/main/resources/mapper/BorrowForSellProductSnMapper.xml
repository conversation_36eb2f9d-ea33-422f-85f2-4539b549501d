<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForSellProductSnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForSellProductSn">
        <id column="id" property="id" />
        <result column="borrow_product_id" property="borrowProductId" />
        <result column="borrow_product_selectable_id" property="borrowProductSelectableId" />
        <result column="shipment_company" property="shipmentCompany" />
        <result column="psn" property="psn" />
        <result column="storage_time" property="storageTime" />
        <result column="shipment_type" property="shipmentType" />
        <result column="effective_date" property="effectiveDate" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
        <result column="delivery_sn_id" property="deliverySnId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, borrow_product_id, borrow_product_selectable_id, shipment_company, psn, storage_time, shipment_type, effective_date, create_time, update_time, create_user, update_user, del_flag, delivery_sn_id
    </sql>

</mapper>
