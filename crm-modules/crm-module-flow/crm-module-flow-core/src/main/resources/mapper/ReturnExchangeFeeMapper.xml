<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ReturnExchangeFeeMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ReturnExchangeFee">
            <result property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="recordId" column="record_id" jdbcType="VARCHAR"/>
            <result property="feeType" column="fee_type" jdbcType="TINYINT"/>
            <result property="fee" column="fee" jdbcType="DECIMAL"/>
            <result property="payWay" column="pay_way" jdbcType="TINYINT"/>
            <result property="feeWaiver" column="fee_waiver" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,record_id,
        fee_type,fee,pay_way,
        fee_waiver,create_time,update_time,
        create_user,update_user
    </sql>
</mapper>
