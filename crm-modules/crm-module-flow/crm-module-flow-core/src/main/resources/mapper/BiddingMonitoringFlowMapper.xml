<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BiddingMonitoringFlowMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BiddingMonitoringFlow">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="TINYINT"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="biddingMonitoringInfoNumber" column="bidding_monitoring_info_number" jdbcType="VARCHAR"/>
            <result property="businessType" column="business_type" jdbcType="VARCHAR"/>
            <result property="followUp" column="follow_up" jdbcType="TINYINT"/>
            <result property="followUpPerson" column="follow_up_person" jdbcType="VARCHAR"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="projectNo" column="project_no" jdbcType="VARCHAR"/>
            <result property="bidderResult" column="bidder_result" jdbcType="VARCHAR"/>
            <result property="biddingType" column="bidding_type" jdbcType="VARCHAR"/>
            <result property="bidderUnit" column="bidder_unit" jdbcType="VARCHAR"/>
            <result property="bidderPublicationTime" column="bidder_publication_time" jdbcType="VARCHAR"/>
            <result property="bidderProvider" column="bidder_provider" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="bidderWebsite" column="bidder_website" jdbcType="VARCHAR"/>
            <result property="followUpTime" column="follow_up_time" jdbcType="TIMESTAMP"/>
            <result property="notFollowReason" column="not_follow_reason" jdbcType="VARCHAR"/>
            <result property="followUpType" column="follow_up_type" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,business_type,bidding_monitoring_info_number,
        follow_up,follow_up_person,
        project_id,project_no,bidder_result,
        bidding_type,bidder_unit,bidder_publication_time,
        bidder_provider,bidder_website,follow_up_time,not_follow_reason,
        follow_up_type,del_flag,create_user,
        update_user,create_time,update_time
    </sql>
</mapper>
