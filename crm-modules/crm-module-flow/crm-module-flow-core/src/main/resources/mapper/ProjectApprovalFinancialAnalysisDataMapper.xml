<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ProjectApprovalFinancialAnalysisDataMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ProjectApprovalFinancialAnalysisData">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="projectApprovalId" column="project_approval_id" jdbcType="VARCHAR"/>
            <result property="contractAmountTotal" column="contract_amount_total" jdbcType="DECIMAL"/>
            <result property="projectCostTotal" column="project_cost_total" jdbcType="DECIMAL"/>
            <result property="projectExpenseTotal" column="project_expense_total" jdbcType="DECIMAL"/>
            <result property="projectGrossMarginRatio" column="project_gross_margin_ratio" jdbcType="DECIMAL"/>
            <result property="projectGrossMarginRatioTotal" column="project_gross_margin_ratio_total" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,project_approval_id,contract_amount_total,
        project_cost_total,project_expense_total,project_gross_margin_ratio,
        project_gross_margin_ratio_total,create_time,update_time,
        create_user,update_user,del_flag
    </sql>
</mapper>
