<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForProbationBackMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForProbationBack">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="INTEGER"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="processPassTime" column="process_pass_time" jdbcType="TIMESTAMP"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="borrowProcessId" column="borrow_process_id" jdbcType="VARCHAR"/>
            <result property="probationNumber" column="probation_number" jdbcType="VARCHAR"/>
            <result property="probationType" column="probation_type" jdbcType="INTEGER"/>
            <result property="probationPersonId" column="probation_person_id" jdbcType="VARCHAR"/>
            <result property="probationDept" column="probation_dept" jdbcType="VARCHAR"/>
            <result property="handlingNumber" column="handling_number" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="reason" column="reason" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,process_pass_time,project_id,borrow_process_id,
        probation_number,probation_type,probation_person_id,
        probation_dept,handling_number,remarks,reason,
        create_time,update_time,create_user,
        update_user,del_flag
    </sql>
</mapper>
