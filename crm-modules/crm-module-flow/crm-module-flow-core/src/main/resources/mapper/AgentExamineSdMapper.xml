<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentExamineSdMapper">

    <select id="selectAgentExamineSdList" parameterType="com.topsec.crm.flow.api.dto.agentExamineSd.AgentExamineSdQuery" resultType="com.topsec.crm.flow.api.dto.agentExamineSd.AgentExamineSdVO">
        select process_number,process_instance_id,platform,examine_year, quarter,start_time, end_time,sd.create_user,sd.create_time,area_code,city_code,platform_id
        from agent_examine_sd sd
        where sd.del_flag=0
        <if test="platformIds != null and platformIds != ''">
            and platform_id in
            <foreach item="platformIds" collection="platformIds" open="(" separator="," close=")">
                #{platformIds}
            </foreach>
        </if>
        <if test="areaCode != null and areaCode != ''">
            and area_code = #{areaCode}
        </if>
        <if test="processNumber != null and processNumber != ''">
            and process_number like '%' #{processNumber} '%'
        </if>
        <if test="areaName != null and areaName != ''">
            and (area_name like '%' #{areaName} '%' or city_name like '%' #{areaName} '%')
        </if>
        <if test="platform != null and platform != ''">
            and platform like '%' #{platform} '%'
        </if>
        <if test="startTime != null and startTime != ''">
            and DATEDIFF(start_time,#{startTime}) >=0
        </if>
        <if test="endTime != null and endTime != ''">
            and DATEDIFF(#{endTime},end_time) >=0
        </if>
    </select>
</mapper>
