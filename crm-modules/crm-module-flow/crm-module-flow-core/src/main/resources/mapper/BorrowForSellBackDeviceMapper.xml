<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForSellBackDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForSellBackDevice">
        <id column="id" property="id" />
        <result column="borrow_back_id" property="borrowBackId" />
        <result column="borrow_product_sn_id" property="borrowProductSnId" />
        <result column="psn" property="psn" />
        <result column="stuff_code" property="stuffCode" />
        <result column="pn_code" property="pnCode" />
        <result column="product_category" property="productCategory" />
        <result column="deal_price" property="dealPrice" />
        <result column="cost" property="cost" />
        <result column="shipment_type" property="shipmentType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, borrow_back_id, borrow_product_sn_id, psn, stuff_code, pn_code, product_category, deal_price, cost, shipment_type, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
