<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.topsec.crm.flow.core.mapper.ThreeProcurementReleaseMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.topsec.crm.flow.core.entity.ThreeProcurementRelease" id="threeProcurementReleaseMap">
        <result property="id" column="id"/>
        <result property="processNumber" column="process_number"/>
        <result property="contractNumber" column="contract_number"/>
        <result property="contractId" column="contract_id"/>
        <result property="salePersonId" column="sale_person_id"/>
        <result property="saleName" column="sale_name"/>
        <result property="releasePersonId" column="release_person_id" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="personId" column="person_id"/>
        <result property="personName" column="person_name"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
    </resultMap>


</mapper>