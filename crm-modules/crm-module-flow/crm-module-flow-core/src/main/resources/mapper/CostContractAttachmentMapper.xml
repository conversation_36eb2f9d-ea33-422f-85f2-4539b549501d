<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.CostContractAttachmentMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.CostContractAttachment">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="costContractId" column="cost_contract_id" jdbcType="VARCHAR"/>
            <result property="fileType" column="file_type" jdbcType="VARCHAR"/>
            <result property="duplicate" column="duplicate" jdbcType="INTEGER"/>
            <result property="copies" column="copies" jdbcType="INTEGER"/>
            <result property="docId" column="doc_id" jdbcType="VARCHAR"/>
            <result property="isFinal" column="is_final" jdbcType="BOOLEAN"/>
            <result property="partyAContactPerson" column="party_a_contact_person" jdbcType="VARCHAR"/>
            <result property="partyAPhone" column="party_a_phone" jdbcType="VARCHAR"/>
            <result property="partyAEmail" column="party_a_email" jdbcType="VARCHAR"/>
            <result property="partyAAddress" column="party_a_address" jdbcType="VARCHAR"/>
            <result property="partyBContactPerson" column="party_b_contact_person" jdbcType="VARCHAR"/>
            <result property="partyBPhone" column="party_b_phone" jdbcType="VARCHAR"/>
            <result property="partyBEmail" column="party_b_email" jdbcType="VARCHAR"/>
            <result property="partyBAddress" column="party_b_address" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,cost_contract_id,file_type,
        contract_type,duplicate,copies,
        doc_id,is_final,party_a_contact_person,
        party_a_phone,party_a_email,party_a_address,
        party_b_contact_person,party_b_phone,party_b_email,
        party_b_address,remarks,process_pass_time,
        create_user,update_user,create_time,
        update_time,del_flag
    </sql>
</mapper>
