<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PriceReviewSaleAgreementRelMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PriceReviewSaleAgreementRel">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="priceReviewProcessInstanceId" column="price_review_process_instance_id" jdbcType="VARCHAR"/>
            <result property="saleAgreementId" column="sale_agreement_id" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="querySaleAgreementRelatedProjectId" resultType="java.lang.String">
        select distinct project_id from price_review_main
        where exists(
            select 1 from price_review_sale_agreement_rel
            where price_review_process_instance_id = price_review_main.process_instance_id
            and sale_agreement_id = #{saleAgreementId}
        )
        and price_review_main.del_flag=0
    </select>


</mapper>
