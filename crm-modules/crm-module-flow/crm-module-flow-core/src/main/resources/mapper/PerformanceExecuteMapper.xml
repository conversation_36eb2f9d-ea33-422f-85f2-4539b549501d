<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PerformanceExecuteMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PerformanceExecute">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="performanceProcessInstanceId" column="performance_process_instance_id" jdbcType="VARCHAR"/>
            <result property="contractNumber" column="contract_number" jdbcType="VARCHAR"/>
            <result property="saleId" column="sale_id" jdbcType="VARCHAR"/>
            <result property="saleName" column="sale_name" jdbcType="VARCHAR"/>
            <result property="saleDeptId" column="sale_dept_id" jdbcType="VARCHAR"/>
            <result property="saleDeptName" column="sale_dept_name" jdbcType="VARCHAR"/>
            <result property="contractTime" column="contract_time" jdbcType="TIMESTAMP"/>
            <result property="signingTime" column="signing_time" jdbcType="TIMESTAMP"/>
            <result property="supplierId" column="supplier_id" jdbcType="VARCHAR"/>
            <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
            <result property="channelCompanyId" column="channel_company_id" jdbcType="VARCHAR"/>
            <result property="channelCompanyName" column="channel_company_name" jdbcType="VARCHAR"/>
            <result property="finalCustomerId" column="final_customer_id" jdbcType="VARCHAR"/>
            <result property="finalCustomerName" column="final_customer_name" jdbcType="VARCHAR"/>
            <result property="contractAmount" column="contract_amount" jdbcType="DECIMAL"/>
            <result property="returnedAmount" column="returned_amount" jdbcType="DECIMAL"/>
            <result property="debtAmount" column="debt_amount" jdbcType="DECIMAL"/>
            <result property="overdueAmount" column="overdue_amount" jdbcType="DECIMAL"/>
            <result property="reportStatus" column="report_status" jdbcType="TINYINT"/>
            <result property="supplierType" column="supplier_type" jdbcType="TINYINT"/>
            <result property="invoiceStatus" column="invoice_status" jdbcType="TINYINT"/>
            <result property="projectReportNumber" column="project_report_number" jdbcType="VARCHAR"/>
            <result property="stayTime" column="stay_time" jdbcType="TINYINT"/>
            <result property="isOriginal" column="is_original" jdbcType="TINYINT"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <select id="queryPerformanceInfo" resultType="com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteVO">
            SELECT
                    sale_id AS sale_id,
                    performance_report.process_instance_id AS performance_process_instance_id,
                    process_number AS contract_number,
                    signing_contract_number AS signing_contract_number,
                    effective_time AS contract_time,
                    payment.contract_signing_date AS signing_time,
                    supplier_id AS supplier_id,
                    supplier_name AS supplier_name,
                    channel_company_id AS channel_company_id,
                    channel_company_name AS channel_company_name,
                    customer_id AS final_customer_id,
                    customer_name AS final_customer_name,
                    own.contract_amount AS contract_amount,
                    report_status AS report_status,
                    supplier_type AS supplier_type,
                    performance_report.report_number AS project_report_number,
                    CASE WHEN doc.doc_id IS NOT NULL THEN 1 ELSE 0 END AS is_original,
                    project.project_name as project_name
            FROM performance_report
            LEFT JOIN (
                    SELECT process_instance_id, contract_signing_date
                    FROM performance_report_payment_info
                    WHERE del_flag = 0
                    ORDER BY create_time DESC LIMIT 1
            ) payment ON payment.process_instance_id = performance_report.process_instance_id
            LEFT JOIN (
                    SELECT SUM(deal_price * product_num) AS contract_amount, process_instance_id
                    FROM performance_report_product_own
                    WHERE del_flag = 0
                    GROUP BY process_instance_id
            ) own ON own.process_instance_id = performance_report.process_instance_id
            LEFT JOIN (
                    SELECT 1 AS project_type, id as project_id,project_name FROM crm_project.crm_project_directly WHERE del_flag = 0
                    union
                    select 2 as project_type,id as project_id,project_name FROM crm_project.crm_project_agent WHERE del_flag = 0
            ) project ON project.project_type = performance_report.project_type and project.project_id = performance_report.project_id
            LEFT JOIN (
                    SELECT doc_id, process_instance_id
                    FROM performance_report_doc
                    WHERE type = '合同' ORDER BY create_time DESC LIMIT 1
            ) doc ON doc.process_instance_id = performance_report.process_instance_id
            WHERE performance_report.del_flag = 0
    </select>
    <select id="getAgentContractTotalAmount" resultType="com.topsec.crm.stats.api.entity.StatsAgentSaleVO">
            SELECT
                    pfe.channel_company_id AS agentId,
                    pfe.channel_company_name AS agentName,
                    SUM(pfe.contract_amount) AS amount
            FROM
                    performance_execute pfe
            WHERE
                    YEAR(pfe.contract_time) = YEAR(CURDATE())
                    <if test="month != null and month != ''">
                        AND MONTH(pfe.contract_time) = #{month}
                    </if>
            GROUP BY
                    pfe.channel_company_id,
                    pfe.channel_company_name,
                    MONTH(pfe.contract_time)
    </select>
</mapper>
