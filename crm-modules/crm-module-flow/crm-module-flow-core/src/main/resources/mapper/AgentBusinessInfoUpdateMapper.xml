<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.AgentBusinessInfoUpdateMapper">

    <resultMap id="AgentBusinessInfoUpdateResult" type="com.topsec.crm.flow.core.entity.AgentBusinessInfoUpdate">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="agentId" column="agent_id" jdbcType="VARCHAR"/>
            <result property="operateUser" column="operate_user" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="selectAgentBusinessInfoUpdateVo">
        select id, agent_id, operate_user, update_date from agent_business_info_update
    </sql>

    <select id="selectAgentBusinessInfoUpdateList" resultType="com.topsec.crm.flow.core.entity.AgentBusinessInfoUpdate" resultMap="AgentBusinessInfoUpdateResult">
        <include refid="selectAgentBusinessInfoUpdateVo"/>
        <where>
            <if test="agentId != null  and agentId != ''"> and agent_id = #{agentId}</if>
            <if test="operateUser != null  and operateUser != ''"> and operate_user = #{operateUser}</if>
            <if test="updateDate != null  and updateDate != ''"> and update_date = #{updateDate}</if>
        </where>
    </select>
</mapper>
