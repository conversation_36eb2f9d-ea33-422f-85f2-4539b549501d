<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForSellMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForSell">
        <id column="id" property="id" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="process_state" property="processState" />
        <result column="process_number" property="processNumber" />
        <result column="project_id" property="projectId" />
        <result column="project_no" property="projectNo" />
        <result column="salesman" property="salesman" />
        <result column="salesman_person_id" property="salesmanPersonId" />
        <result column="salesman_dept" property="salesmanDept" />
        <result column="salesman_dept_name" property="salesmanDeptName" />
        <result column="agent_shipment" property="agentShipment" />
        <result column="advice_shipment_company_id" property="adviceShipmentCompanyId" />
        <result column="advice_shipment_company" property="adviceShipmentCompany" />
        <result column="link_man" property="linkMan" />
        <result column="link_phone" property="linkPhone" />
        <result column="sign_company_id" property="signCompanyId" />
        <result column="sign_company" property="signCompany" />
        <result column="borrower_name" property="borrowerName" />
        <result column="borrower_link_man" property="borrowerLinkMan" />
        <result column="borrower_link_phone" property="borrowerLinkPhone" />
        <result column="need_deposit" property="needDeposit" />
        <result column="borrow_period" property="borrowPeriod" />
        <result column="apply_reason" property="applyReason" />
        <result column="delay_reason" property="delayReason" />
        <result column="can_borrow" property="canBorrow" />
        <result column="shipment_company_id" property="shipmentCompanyId" />
        <result column="shipment_company" property="shipmentCompany" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, process_instance_id, process_state, process_number, project_id, project_no, salesman, salesman_person_id, salesman_dept, salesman_dept_name, agent_shipment, advice_shipment_company_id, advice_shipment_company, link_man, link_phone, sign_company_id, sign_company, borrower_name, borrower_link_man, borrower_link_phone, need_deposit, borrow_period, apply_reason, delay_reason, can_borrow, shipment_company_id, shipment_company, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
