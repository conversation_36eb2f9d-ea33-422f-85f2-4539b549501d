<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ContractOriginalDocumentAttachmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ContractOriginalDocumentAttachment">
        <id column="id" property="id" />
        <result column="contract_original_id" property="contractOriginalId" />
        <result column="file_type" property="fileType" />
        <result column="duplicate" property="duplicate" />
        <result column="copies" property="copies" />
        <result column="doc_id" property="docId" />
        <result column="file_name" property="fileName" />
        <result column="doc_size" property="docSize" />
        <result column="remark" property="remark" />
        <result column="del_flag" property="delFlag" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="created_name" property="createdName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, contract_original_id, file_type, duplicate, copies, doc_id, file_name, doc_size, remark, del_flag, create_user, update_user, create_time, update_time
    </sql>

</mapper>
