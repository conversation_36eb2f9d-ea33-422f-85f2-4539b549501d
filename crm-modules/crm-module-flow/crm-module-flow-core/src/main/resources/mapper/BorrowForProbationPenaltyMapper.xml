<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForProbationPenaltyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForProbationPenalty">
        <id column="id" property="id" />
        <result column="borrow_id" property="borrowId" />
        <result column="apply_time" property="applyTime" />
        <result column="penalty_type" property="penaltyType" />
        <result column="effective_time" property="effectiveTime" />
        <result column="payable" property="payable" />
        <result column="paid_in" property="paidIn" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, borrow_id, apply_time, penalty_type, effective_time, payable, paid_in, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
