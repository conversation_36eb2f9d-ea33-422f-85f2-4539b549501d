<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BackingMaterialDocMapper">
    
    <resultMap type="com.topsec.crm.flow.core.entity.BackingMaterialDoc" id="BackingMaterialDocResult">
        <result property="id"    column="id"    />
        <result property="materialId"    column="material_id"    />
        <result property="commitmentType"    column="commitment_type"    />
        <result property="docId"    column="doc_id"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBackingMaterialDocVo">
        select id, material_id, commitment_type, doc_id, del_flag, create_user, update_user, create_time, update_time
        from backing_material_doc
    </sql>

    <select id="selectBackingMaterialDocList" parameterType="com.topsec.crm.flow.core.entity.BackingMaterialDoc" resultMap="BackingMaterialDocResult">
        <include refid="selectBackingMaterialDocVo"/>
        <where>  
            <if test="materialId != null  and materialId != ''"> and material_id = #{materialId}</if>
            <if test="commitmentType != null  and commitmentType != ''"> and commitment_type = #{commitmentType}</if>
            <if test="docId != null  and docId != ''"> and doc_id = #{docId}</if>
            <if test="createUser != null  and createUser != ''"> and create_user = #{createUser}</if>
            <if test="updateUser != null  and updateUser != ''"> and update_user = #{updateUser}</if>
        </where>
    </select>
    
    <select id="selectBackingMaterialDocById" parameterType="String" resultMap="BackingMaterialDocResult">
        <include refid="selectBackingMaterialDocVo"/>
        where material_id = #{materialId}
    </select>
        
    <insert id="insertBackingMaterialDoc" parameterType="com.topsec.crm.flow.core.entity.BackingMaterialDoc">
        insert into backing_material_doc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="materialId != null">material_id,</if>
            <if test="commitmentType != null">commitment_type,</if>
            <if test="docId != null">doc_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="materialId != null">#{materialId},</if>
            <if test="commitmentType != null">#{commitment_type},</if>
            <if test="docId != null">#{docId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <delete id="deleteBackingMaterialDoc" parameterType="String">
        update backing_material_doc set del_flag=1 where id = #{id}
    </delete>

    <delete id="deleteBackingMaterialDocByMaterialId" parameterType="String">
        update backing_material_doc set del_flag=1 where material_id = #{id}
    </delete>
</mapper>