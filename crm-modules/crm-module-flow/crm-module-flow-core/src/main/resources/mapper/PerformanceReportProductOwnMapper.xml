<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PerformanceReportProductOwnMapper">

    <resultMap type="com.topsec.crm.flow.core.entity.PerformanceReportProductOwn" id="PerformanceReportProductOwnResult">
        <result property="id"    column="id"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="projectRecordId"    column="project_record_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="productId"    column="product_id"    />
        <result property="productLic"    column="product_lic"    />
        <result property="productNum"    column="product_num"    />
        <result property="quotedPrice"    column="quoted_price"    />
        <result property="quotedTotalPrice"    column="quoted_total_price"    />
        <result property="attr"    column="attr"    />
        <result property="productSpecification"    column="product_specification"    />
        <result property="stuffCode"    column="stuff_code"    />
        <result property="productName"    column="product_name"    />
        <result property="pnCode"    column="pn_code"    />
        <result property="hostSerialNumber"    column="host_serial_number"    />
        <result property="dealPrice"    column="deal_price"    />
        <result property="dealTotalPrice"    column="deal_total_price"    />
        <result property="rebatePrice"    column="rebate_price"    />
        <result property="finalPrice"    column="final_price"    />
        <result property="finalTotalPrice"    column="final_total_price"    />
        <result property="taxRate"    column="tax_rate"    />
        <result property="productPeriod"    column="product_period"    />
        <result property="productPeriodStart"    column="product_period_start"    />
        <result property="productPeriodEnd"    column="product_period_end"    />
        <result property="reportStatus"    column="report_status"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPerformanceReportProductOwnVo">
        select id, process_instance_id, project_id, product_id, product_lic, product_num, quoted_price,
        quoted_total_price, attr, product_specification, stuff_code,
        product_name, pn_code, host_serial_number, deal_price, deal_total_price,
        rebate_price, final_price, final_total_price, tax_rate, product_period,product_period_start,product_period_end,report_status,product_return_parent_id
        remark, del_flag, create_user, update_user, create_time, update_time from performance_report_product_own
    </sql>

    <resultMap type="com.topsec.crm.flow.api.dto.performancereport.PerformanceReportProductStatisticsDTO" id="PerformanceReportProductStatisticsDTOResult">
        <result property="quotedTotalPrice"    column="quoted_total_price"    />
        <result property="rebateTotalPrice"    column="rebate_price"    />
        <result property="priceDifferenceTotal"    column="price_difference_total"    />
        <result property="osDifferenceTotalPrice"    column="os_difference_total_price"    />
        <result property="finalTotalPrice"    column="final_total_price"    />
        <result property="productTotalNum"    column="product_num"    />
        <result property="realityDealTotalPrice"    column="reality_deal_total_price"    />
    </resultMap>

    <select id="selectProductStatistics" parameterType="String" resultMap="PerformanceReportProductStatisticsDTOResult">
        select sum(quoted_total_price) as quoted_total_price,sum(ifnull(rebate_total_price,0)) as rebate_price,sum(product_num) as product_num,
        sum(final_total_price) as final_total_price,sum(deal_total_price) as reality_deal_total_price
        from performance_report_product_own
        where del_flag=0 and process_instance_id = #{id}
    </select>

    <select id="selectPerformanceReportProductSeparationConsumePrice" parameterType="String" resultType="java.math.BigDecimal">
            SELECT consume_price
            FROM (
                SELECT sm1.deal_price,sm1.product_lic,
                    case when deal_price - sell_in_price >0 then deal_price - sell_in_price
                    else 0 end AS consume_price
                FROM (
                    SELECT cp1.deal_price - (cp1.deal_price + ifnull(cp1.rebate_price,0))*0.02 as deal_price,cp1.product_lic,cp1.id,yjppb.sell_in_price
                    FROM (
                        performance_report_product_own AS cp1
                        LEFT JOIN (
                            SELECT performance_report_product_id ,stuff_code,sell_in_price
                            FROM performance_report_product_separation
                            WHERE product_classification=1 AND del_flag=0 AND performance_report_product_id=#{performanceReportProductId}
                              AND id=#{performanceReportProductSeparationId}
                            GROUP BY performance_report_product_id ,stuff_code,sell_in_price
                        ) AS yjppb ON cp1.id=yjppb.performance_report_product_id
                    )
                    LEFT JOIN performance_report ON cp1.process_instance_id =performance_report.process_instance_id AND performance_report.del_flag=0
                    WHERE cp1.id=#{performanceReportProductId} AND cp1.del_flag=0
                ) AS sm1
            ) AS bg1
    </select>

    <select id="selectPerformanceReportProductSeparationSplit" parameterType="String" resultType="int">
        select case when left(stuff_code,1) != '6' then 1 else 0 end as is_split from performance_report_product_own where del_flag=0 and id=#{performanceReportProductId} and product_num >= 0

    </select>


    <select id="selectPerformanceReportProductOwnList" parameterType="com.topsec.crm.flow.core.entity.PerformanceReportProductOwn" resultMap="PerformanceReportProductOwnResult">
        <include refid="selectPerformanceReportProductOwnVo"/>
        <where>
            <if test="performanceReportId != null  and performanceReportId != ''"> and process_instance_id = #{performanceReportId}</if>
            <if test="projectId != null  and projectId != ''"> and project_id = #{projectId}</if>
            <if test="productId != null  and productId != ''"> and product_id = #{productId}</if>
            <if test="productLic != null "> and product_lic = #{productLic}</if>
            <if test="productNum != null "> and product_num = #{productNum}</if>
            <if test="quotedPrice != null "> and quoted_price = #{quotedPrice}</if>
            <if test="quotedTotalPrice != null "> and quoted_total_price = #{quotedTotalPrice}</if>
            <if test="attr != null  and attr != ''"> and attr = #{attr}</if>
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="productSpecification != null  and productSpecification != ''"> and product_specification = #{productSpecification}</if>
            <if test="stuffCode != null  and stuffCode != ''"> and stuff_code = #{stuffCode}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="pnCode != null  and pnCode != ''"> and pn_code = #{pnCode}</if>
            <if test="hostSerialNumber != null  and hostSerialNumber != ''"> and host_serial_number = #{hostSerialNumber}</if>
            <if test="dealPrice != null "> and deal_price = #{dealPrice}</if>
            <if test="dealTotalPrice != null "> and deal_total_price = #{dealTotalPrice}</if>
            <if test="rebatePrice != null "> and rebate_price = #{rebatePrice}</if>
            <if test="finalPrice != null "> and final_price = #{finalPrice}</if>
            <if test="finalTotalPrice != null "> and final_total_price = #{finalTotalPrice}</if>
            <if test="taxRate != null "> and tax_rate = #{taxRate}</if>
            <if test="productPeriod != null "> and product_period = #{productPeriod}</if>
            <if test="productPeriodStart != null "> and product_period_start = #{productPeriodStart}</if>
            <if test="productPeriodEnd != null "> and product_period_end = #{productPeriodEnd}</if>
            <if test="createUser != null  and createUser != ''"> and create_user = #{createUser}</if>
            <if test="updateUser != null  and updateUser != ''"> and update_user = #{updateUser}</if>
        </where>
    </select>

    <select id="selectPerformanceReportProductOwnById" parameterType="String" resultMap="PerformanceReportProductOwnResult">
        <include refid="selectPerformanceReportProductOwnVo"/>
        where del_flag=0 and process_instance_id = #{id}
    </select>

    <insert id="insertPerformanceReportProductOwn" parameterType="com.topsec.crm.flow.core.entity.PerformanceReportProductOwn">
        insert into performance_report_product_own
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="performanceReportId != null">process_instance_id,</if>
            <if test="projectId != null">project_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="productLic != null">product_lic,</if>
            <if test="productNum != null">product_num,</if>
            <if test="quotedPrice != null">quoted_price,</if>
            <if test="quotedTotalPrice != null">quoted_total_price,</if>
            <if test="attr != null">attr,</if>
            <if test="productType != null">product_type,</if>
            <if test="productSpecification != null">product_specification,</if>
            <if test="stuffCode != null">stuff_code,</if>
            <if test="productName != null">product_name,</if>
            <if test="pnCode != null">pn_code,</if>
            <if test="hostSerialNumber != null">host_serial_number,</if>
            <if test="dealPrice != null">deal_price,</if>
            <if test="dealTotalPrice != null">deal_total_price,</if>
            <if test="rebatePrice != null">rebate_price,</if>
            <if test="finalPrice != null">final_price,</if>
            <if test="finalTotalPrice != null">final_total_price,</if>
            <if test="taxRate != null">tax_rate,</if>
            <if test="productPeriod != null">product_period,</if>
            <if test="productPeriodStart != null">product_period_start,</if>
            <if test="productPeriodEnd != null">product_period_end,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="performanceReportId != null">#{performanceReportId},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productLic != null">#{productLic},</if>
            <if test="productNum != null">#{productNum},</if>
            <if test="quotedPrice != null">#{quotedPrice},</if>
            <if test="quotedTotalPrice != null">#{quotedTotalPrice},</if>
            <if test="attr != null">#{attr},</if>
            <if test="productType != null">#{productType},</if>
            <if test="productSpecification != null">#{productSpecification},</if>
            <if test="stuffCode != null">#{stuffCode},</if>
            <if test="productName != null">#{productName},</if>
            <if test="pnCode != null">#{pnCode},</if>
            <if test="hostSerialNumber != null">#{hostSerialNumber},</if>
            <if test="dealPrice != null">#{dealPrice},</if>
            <if test="dealTotalPrice != null">#{dealTotalPrice},</if>
            <if test="rebatePrice != null">#{rebatePrice},</if>
            <if test="finalPrice != null">#{finalPrice},</if>
            <if test="finalTotalPrice != null">#{finalTotalPrice},</if>
            <if test="taxRate != null">#{taxRate},</if>
            <if test="productPeriod != null">#{productPeriod},</if>
            <if test="productPeriodStart != null">#{productPeriodStart},</if>
            <if test="productPeriodEnd != null">#{productPeriodEnd},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePerformanceReportProductOwn" parameterType="com.topsec.crm.flow.core.entity.PerformanceReportProductOwn">
        update performance_report_product_own
        <trim prefix="SET" suffixOverrides=",">
            <if test="performanceReportId != null">process_instance_id = #{performanceReportId},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productLic != null">product_lic = #{productLic},</if>
            <if test="productNum != null">product_num = #{productNum},</if>
            <if test="quotedPrice != null">quoted_price = #{quotedPrice},</if>
            <if test="quotedTotalPrice != null">quoted_total_price = #{quotedTotalPrice},</if>
            <if test="attr != null">attr = #{attr},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="productSpecification != null">product_specification = #{productSpecification},</if>
            <if test="stuffCode != null">stuff_code = #{stuffCode},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="pnCode != null">pn_code = #{pnCode},</if>
            <if test="hostSerialNumber != null">host_serial_number = #{hostSerialNumber},</if>
            <if test="dealPrice != null">deal_price = #{dealPrice},</if>
            <if test="dealTotalPrice != null">deal_total_price = #{dealTotalPrice},</if>
            <if test="rebatePrice != null">rebate_price = #{rebatePrice},</if>
            <if test="finalPrice != null">final_price = #{finalPrice},</if>
            <if test="finalTotalPrice != null">final_total_price = #{finalTotalPrice},</if>
            <if test="taxRate != null">tax_rate = #{taxRate},</if>
            <if test="productPeriod != null">product_period = #{productPeriod},</if>
            <if test="productPeriodStart != null">product_period_start = #{productPeriodStart},</if>
            <if test="productPeriodEnd != null">product_period_end = #{productPeriodEnd},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePerformanceReportProductOwnById" parameterType="String">
        update performance_report_product_own set del_flag=1 where id = #{id}
    </delete>

    <delete id="deletePerformanceReportProductOwnByIds" parameterType="String">
        update performance_report_product_own set del_flag=1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectProductOwnByProcessNumber" parameterType="String" resultType="com.topsec.crm.flow.api.dto.performancereport.PerformanceReportProductOwnDTO">
        select own.id,stuff_code,product_name,pn_code,specification_id,product_num,rebate_price,deal_price,deal_total_price
        from performance_report report
        left join performance_report_product_own own on report.process_instance_id=own.process_instance_id
        where report.del_flag=0 and own.del_flag=0 and report.process_number=#{processNumber}
    </select>

    <select id="selectPriceDifference" resultType="java.math.BigDecimal">
        select ifnull(sum(separation.price_difference),0) as price_difference
        from performance_report_product_separation separation
                 left join performance_report_product_own own on separation.performance_report_product_id=own.id
        where separation.del_flag=0 and own.del_flag=0 and own.id=#{performanceReportProductId}
    </select>

    <select id="selectOSConsumePrice" resultType="java.math.BigDecimal">
        select ifnull(sum(consume_price*product_num),0) as price_difference
        from (
                 select
                     CASE WHEN own.deal_price - (own.deal_price + own.rebate_price)*0.02 - hardware_separation.sell_in_price > 0
                         THEN own.deal_price - (own.deal_price + own.rebate_price)*0.02 - hardware_separation.sell_in_price
                         ELSE 0 END AS consume_price ,separation.product_num
                 from performance_report_product_separation separation
                 left join performance_report_product_separation hardware_separation on separation.hardware_separation_id=hardware_separation.id
                 left join performance_report_product_own own on separation.performance_report_product_id=own.id
                 left join performance_report on own.process_instance_id=performance_report.process_instance_id
                 where separation.del_flag=0 and own.del_flag=0 and own.id=#{performanceReportProductId}
                   and separation.product_classification=2 and separation.os_reality_num=0 and performance_report.rebate_replace_os=1
             ) as a1
    </select>
</mapper>
