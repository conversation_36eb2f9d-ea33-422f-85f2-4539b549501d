<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ProofOfDebtCollectionMapper">
  <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ProofOfDebtCollection">
    <!--@mbg.generated-->
    <!--@Table proof_of_debt_collection-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="remarks" jdbcType="INTEGER" property="remarks" />
    <result column="contract_number" jdbcType="VARCHAR" property="contractNumber" />
    <result column="archive_number" jdbcType="VARCHAR" property="archiveNumber" />
    <result column="pages_number" jdbcType="INTEGER" property="pagesNumber" />
    <result column="copies_number" jdbcType="INTEGER" property="copiesNumber" />
    <result column="procurement_method" jdbcType="TINYINT" property="procurementMethod" />

    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId" />
    <result column="process_number" jdbcType="VARCHAR" property="processNumber" />
    <result column="process_state" jdbcType="BOOLEAN" property="processState" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>
