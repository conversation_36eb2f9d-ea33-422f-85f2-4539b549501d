<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.TargetedInventoryPreparationDocMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.TargetedInventoryPreparationDoc">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="targetedInventoryPreparationId" column="targeted_inventory_preparation_id" jdbcType="VARCHAR"/>
            <result property="docId" column="doc_id" jdbcType="VARCHAR"/>
            <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
            <result property="fileType" column="file_type" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,targeted_inventory_preparation_id,doc_id,
        file_name,file_type,remarks,
        create_user,create_user_name,update_user,
        create_time,update_time,del_flag
    </sql>
</mapper>
