<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PriceReviewProductOwnSubServMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PriceReviewProductOwnSubServ">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="recordId" column="record_id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="serviceId" column="service_id" jdbcType="VARCHAR"/>
            <result property="stuffCode" column="stuff_code" jdbcType="VARCHAR"/>
            <result property="serviceName" column="service_name" jdbcType="VARCHAR"/>
            <result property="serviceDesc" column="service_desc" jdbcType="VARCHAR"/>
            <result property="serviceNum" column="service_num" jdbcType="INTEGER"/>
            <result property="tempServiceNum" column="temp_service_num" jdbcType="INTEGER"/>
            <result property="serviceSpecification" column="service_specification" jdbcType="VARCHAR"/>
            <result property="quotedPrice" column="quoted_price" jdbcType="DECIMAL"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="splitOutsourcePrice" column="split_outsource_price" jdbcType="DECIMAL"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="enableEdit" column="enable_edit" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,record_id,process_instance_id,
        service_id,stuff_code,service_name,
        service_desc,service_num,temp_service_num,
        service_specification,quoted_price,remark,
        split_outsource_price,del_flag,enable_edit,
        create_user,update_user,create_time,
        update_time
    </sql>
</mapper>
