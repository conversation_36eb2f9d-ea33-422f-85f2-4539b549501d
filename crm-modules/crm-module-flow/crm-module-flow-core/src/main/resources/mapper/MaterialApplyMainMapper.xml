<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.MaterialApplyMainMapper">

    <select id="selectUnDisableProducts" resultType="java.lang.String">
        select DISTINCT(map.product_id) from material_apply_main mam
        left join material_apply_product map on mam.id = map.material_apply_main_id
        where mam.pro_or_agreement_id = #{proOrAgreementId}
        and source_type = #{sourceType}
        and mam.process_state = 2
        and CURDATE() BETWEEN effective_start_time and effective_end_time
    </select>
    <select id="findProductInFlow" resultType="com.topsec.crm.flow.core.entity.MaterialApplyProduct">
        select * from material_apply_product
        where product_id = #{productId} and material_apply_main_id =
        (
            select id from material_apply_main
                where process_state = 2
                and pro_or_agreement_id = #{projectId}
                and id in (
                    select material_apply_main_id from material_apply_product where product_id = #{productId}
                )
            ORDER BY update_time desc limit 1
        )
    </select>

</mapper>
