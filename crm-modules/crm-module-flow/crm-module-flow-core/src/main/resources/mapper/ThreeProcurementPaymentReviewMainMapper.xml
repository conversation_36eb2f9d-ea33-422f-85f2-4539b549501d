<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.topsec.crm.flow.core.mapper.ThreeProcurementPaymentReviewMainMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.topsec.crm.flow.core.entity.ThreeProcurementPaymentReviewMain" id="threeProcuremenPaymentReviewMainMap">
        <result property="id" column="id"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="processState" column="process_state"/>
        <result property="processNumber" column="process_number"/>
        <result property="reducePersonalLoans" column="reduce_personal_loans"/>
        <result property="proposedPaymentTime" column="proposed_payment_time"/>
        <result property="proposedPaymentAmount" column="proposed_payment_amount"/>
        <result property="reason" column="reason"/>
        <result property="purchaseNumber" column="purchase_number"/>
        <result property="projectId" column="project_id"/>
        <result property="contractId" column="contract_id"/>
        <result property="attachmentIds" column="attachment_ids" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
    </resultMap>


</mapper>