<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PriceReviewMainMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PriceReviewMain">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="VARCHAR"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="salesAgreements" column="sales_agreements" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="preConfirmation" column="pre_confirmation" jdbcType="BOOLEAN"/>
            <result property="priceExpiryDays" column="price_expiry_days" jdbcType="INTEGER"/>
            <result property="priceExpiryDaysOfApproval" column="price_expiry_days_of_approval" jdbcType="INTEGER"/>
            <result property="priceExpiryDaysOfApprovalRemark" column="price_expiry_days_of_approval_remark" jdbcType="VARCHAR"/>
            <result property="priceExpiryReason" column="price_expiry_reason" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="attachmentIds" column="attachment_ids" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="signingType" column="signing_type" jdbcType="INTEGER"/>

            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>


</mapper>
