<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.CostContractOriginalMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.CostContractOriginal">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="TINYINT"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="costContractId" column="cost_contract_id" jdbcType="VARCHAR"/>
            <result property="costContractNumber" column="cost_contract_number" jdbcType="VARCHAR"/>
            <result property="signingTime" column="signing_time" jdbcType="TIMESTAMP"/>
            <result property="archiveNumber" column="archive_number" jdbcType="VARCHAR"/>
            <result property="pagesNumber" column="pages_number" jdbcType="TINYINT"/>
            <result property="copiesNumber" column="copies_number" jdbcType="TINYINT"/>
            <result property="procurementMethod" column="procurement_method" jdbcType="TINYINT"/>
            <result property="fsmDocId" column="fsm_doc_id" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="signature" column="signature" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="isOriginal" column="is_original" jdbcType="VARCHAR"/>
            <result property="isContract" column="is_contract" jdbcType="VARCHAR"/>
            <result property="isLegalAffairs" column="is_legal_affairs" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,cost_contract_id,cost_contract_number,
        signing_time,archive_number,confirm_terms,
        contract_mode,procurement_method,fsm_doc_id,
        signature,is_original,is_contract,
        is_legal_affairs,remarks,create_user,
        update_user,create_time,update_time,
        del_flag
    </sql>
</mapper>
