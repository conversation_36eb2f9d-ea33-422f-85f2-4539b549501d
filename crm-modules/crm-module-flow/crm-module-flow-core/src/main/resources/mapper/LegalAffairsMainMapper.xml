<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.LegalAffairsMainMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.LegalAffairsMain">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="TINYINT"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="contractId" column="contract_id" jdbcType="VARCHAR"/>
            <result property="contractNumber" column="contract_number" jdbcType="VARCHAR"/>
            <result property="contractCompanyId" column="contract_company_id" jdbcType="VARCHAR"/>
            <result property="contractCompanyName" column="contract_company_name" jdbcType="VARCHAR"/>
            <result property="extensionDemandTime" column="extension_demand_time" jdbcType="TIMESTAMP"/>
            <result property="collectionSuggestion" column="collection_suggestion" jdbcType="TINYINT"/>
            <result property="legalAffairsPerson" column="legal_affairs_person" jdbcType="VARCHAR"/>
            <result property="liaisonPerson" column="liaison_person" jdbcType="VARCHAR"/>
            <result property="liaisonPhone" column="liaison_phone" jdbcType="VARCHAR"/>
            <result property="postalCode" column="postal_code" jdbcType="VARCHAR"/>
            <result property="registerAddr" column="register_addr" jdbcType="VARCHAR"/>
            <result property="liaisonAddr" column="liaison_addr" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,contract_id,contract_number,
        contract_company_id,contract_company_name,extension_demand_time,
        collection_suggestion,legal_affairs_person,liaison_person,
        liaison_phone,postal_code,register_addr,
        liaison_addr,remark,del_flag,
        create_time,update_time,create_user,
        create_user_name,update_user,update_user_name
    </sql>
</mapper>
