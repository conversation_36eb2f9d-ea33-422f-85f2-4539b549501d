<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ProjectApprovalBudgetDetailsMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ProjectApprovalBudgetDetails">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="projectApprovalId" column="project_approval_id" jdbcType="VARCHAR"/>
            <result property="budgetClassification" column="budget_classification" jdbcType="VARCHAR"/>
            <result property="amount" column="amount" jdbcType="DECIMAL"/>
            <result property="proportion" column="proportion" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,project_approval_id,budget_classification,
        amount,proportion,create_time,
        update_time,create_user,update_user,
        del_flag
    </sql>
</mapper>
