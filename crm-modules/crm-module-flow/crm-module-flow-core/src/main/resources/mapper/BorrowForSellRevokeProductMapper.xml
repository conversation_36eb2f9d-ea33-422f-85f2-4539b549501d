<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForSellRevokeProductMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForSellRevokeProduct">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="revokeId" column="revoke_id" jdbcType="VARCHAR"/>
            <result property="productRecordId" column="product_record_id" jdbcType="VARCHAR"/>
            <result property="stuffCode" column="stuff_code" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="pnCode" column="pn_code" jdbcType="VARCHAR"/>
            <result property="productCategory" column="product_category" jdbcType="VARCHAR"/>
            <result property="dealPrice" column="deal_price" jdbcType="DECIMAL"/>
            <result property="applyNum" column="apply_num" jdbcType="INTEGER"/>
            <result property="deposit" column="deposit" jdbcType="DECIMAL"/>
            <result property="revokeNum" column="revoke_num" jdbcType="INTEGER"/>
            <result property="revokeDeposit" column="revoke_deposit" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,revoke_id,product_record_id,stuff_code,
        product_name,pn_code,product_category,
        deal_price,apply_num,deposit,
        revoke_num,revoke_deposit,create_time,
        update_time,create_user,update_user,
        del_flag
    </sql>


</mapper>
