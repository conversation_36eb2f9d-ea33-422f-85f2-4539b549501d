<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.topsec.crm.flow.core.mapper.LoanCollectionDetailsMapper">

  <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.LoanCollectionDetails">
    <id column="id" jdbcType="VARCHAR" property="id"/>
    <result column="parent_process_instance_id" jdbcType="VARCHAR" property="parentProcessInstanceId"/>
    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId"/>
    <result column="loan_apply_process_instance_id" jdbcType="VARCHAR" property="loanApplyProcessInstanceId"/>
    <result column="loan_apply_process_number" jdbcType="VARCHAR" property="loanApplyProcessNumber"/>
    <result column="loan_amount" jdbcType="DECIMAL" property="loanAmount"/>
    <result column="loan_apply_time" jdbcType="DATE" property="loanApplyTime"/>
    <result column="sign_company" jdbcType="VARCHAR" property="signCompany"/>
    <result column="project_no" jdbcType="VARCHAR" property="projectNo"/>
    <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
    <result column="payment_type" jdbcType="INTEGER" property="paymentType"/>
    <result column="payer_type" jdbcType="INTEGER" property="payerType"/>
    <result column="payer_name" jdbcType="VARCHAR" property="payerName"/>
    <result column="repayment_date" jdbcType="DATE" property="repaymentDate"/>
    <result column="loan_to_cost_number" jdbcType="VARCHAR" property="loanToCostNumber"/>
    <result column="loan_type" jdbcType="INTEGER" property="loanType"/>
    <result column="cost_money" jdbcType="DECIMAL" property="costMoney"/>
    <result column="recognized_money" jdbcType="DECIMAL" property="recognizedMoney"/>
    <result column="unrecognized_money" jdbcType="DECIMAL" property="unrecognizedMoney"/>
    <result column="repayment_due_date" jdbcType="TIMESTAMP" property="repaymentDueDate"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
    <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    <result column="del_flag" jdbcType="TINYINT" property="delFlag"/>
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.topsec.crm.flow.core.entity.LoanCollectionDetails">
    <result column="collection_feedback" jdbcType="LONGVARCHAR" property="collectionFeedback"/>
    <result column="remark" jdbcType="LONGVARCHAR" property="remark"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, parent_process_instance_id, process_instance_id, loan_apply_process_instance_id, loan_apply_process_number, loan_amount, sign_company, 
	project_no, project_name, payer_type, payer_name, repayment_date, loan_to_cost_number, loan_type, 
	cost_money, recognized_money, unrecognized_money, repayment_due_date, create_time, update_time, create_user, 
	update_user, del_flag
  </sql>
  <sql id="Blob_Column_List">
    collection_feedback, remark
  </sql>
</mapper>
