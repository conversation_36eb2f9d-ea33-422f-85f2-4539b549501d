<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.SealApplicationMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.SealApplication">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="TINYINT"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="processType" column="process_type" jdbcType="VARCHAR"/>
            <result property="sealType" column="seal_type" jdbcType="VARCHAR"/>
            <result property="applyReason" column="apply_reason" jdbcType="VARCHAR"/>
            <result property="fsmDocId" column="fsm_doc_id" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,contract_party,
        apply_reason,matter_type,
        project_id,project_no,authority_object,
        authority_unit,is_bid_document,bidding_process_id,
        bidding_process_number,fsm_doc_id,source_table,
        source_table_id,remarks,process_pass_time,
        create_user,update_user,create_user_name,
        update_user_name,create_time,update_time,
        del_flag
    </sql>
</mapper>
