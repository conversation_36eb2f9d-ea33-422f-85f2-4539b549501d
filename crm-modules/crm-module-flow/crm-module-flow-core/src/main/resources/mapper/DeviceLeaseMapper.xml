<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.DeviceLeaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.DeviceLease">
        <id column="id" property="id" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="process_state" property="processState" />
        <result column="process_number" property="processNumber" />
        <result column="project_id" property="projectId" />
        <result column="apply_user" property="applyUser" />
        <result column="person_id" property="personId" />
        <result column="company" property="company" />
        <result column="total_price" property="totalPrice" />
        <result column="deal_total_price" property="dealTotalPrice" />
        <result column="final_price" property="finalPrice" />
        <result column="lease_type" property="leaseType" />
        <result column="lease_stuff_code" property="leaseStuffCode" />
        <result column="apply_time" property="applyTime" />
        <result column="expiration_time" property="expirationTime" />
        <result column="lease_limit" property="leaseLimit" />
        <result column="lease_warranty" property="leaseWarranty" />
        <result column="reference_discount" property="referenceDiscount" />
        <result column="actual_discount" property="actualDiscount" />
        <result column="reason" property="reason" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, process_instance_id, process_state, process_number, project_id, apply_user, person_id, company, total_price, deal_total_price, final_price, lease_type, lease_stuff_code, apply_time, expiration_time, lease_limit, lease_warranty, reference_discount, actual_discount, reason, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
