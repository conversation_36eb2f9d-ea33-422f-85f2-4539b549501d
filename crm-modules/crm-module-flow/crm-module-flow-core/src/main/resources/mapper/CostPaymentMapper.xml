<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.CostPaymentMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.CostPayment">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="TINYINT"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="costContractId" column="cost_contract_id" jdbcType="VARCHAR"/>
            <result property="costContractNumber" column="cost_contract_number" jdbcType="VARCHAR"/>
            <result property="canApplyMoney" column="can_apply_money" jdbcType="DECIMAL"/>
            <result property="paymentMoney" column="payment_money" jdbcType="DECIMAL"/>
            <result property="capitalNature" column="capital_nature" jdbcType="TINYINT"/>
            <result property="bank" column="bank" jdbcType="VARCHAR"/>
            <result property="bankAccount" column="bank_account" jdbcType="VARCHAR"/>
            <result property="personnelId" column="personnel_id" jdbcType="VARCHAR"/>
            <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
            <result property="purpose" column="purpose" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="processPassTime" column="process_pass_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,cost_filing_id,cost_filing_number,
        can_apply_money,payment_money,capital_nature,
        bank,bank_account,personnel_id,
        dept_id,purpose,remarks,
        process_pass_time,create_user,update_user,
        create_time,update_time,del_flag
    </sql>
</mapper>
