<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.CostContractPaymentClauseMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.CostContractPaymentClause">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="costContractId" column="cost_contract_id" jdbcType="VARCHAR"/>
            <result property="shouldPayTime" column="should_pay_time" jdbcType="TIMESTAMP"/>
            <result property="shouldPayMoney" column="should_pay_money" jdbcType="DECIMAL"/>
            <result property="paymentType" column="payment_type" jdbcType="VARCHAR"/>
            <result property="paymentCondition" column="payment_condition" jdbcType="VARCHAR"/>
            <result property="paymentClause" column="payment_clause" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,cost_contract_id,should_pay_time,
        should_pay_money,payment_type,payment_condition,
        payment_clause,remarks,create_user,
        update_user,create_time,update_time,
        del_flag
    </sql>
</mapper>
