<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.CostTransferMinusMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.CostTransferMinus">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="processState" column="process_state" jdbcType="TINYINT"/>
            <result property="processNumber" column="process_number" jdbcType="VARCHAR"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
            <result property="projectNo" column="project_no" jdbcType="VARCHAR"/>
            <result property="contractId" column="contract_id" jdbcType="VARCHAR"/>
            <result property="contractNumber" column="contract_number" jdbcType="VARCHAR"/>
            <result property="projectTotalServePrice" column="project_total_serve_price" jdbcType="DECIMAL"/>
            <result property="projectAlreadyPaymentPrice" column="project_already_payment_price" jdbcType="DECIMAL"/>
            <result property="costMinusMoney" column="cost_minus_money" jdbcType="DECIMAL"/>
            <result property="costMinusMoneyUp" column="cost_minus_money_up" jdbcType="DECIMAL"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="processPassTime" column="process_pass_time" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_instance_id,process_state,
        process_number,project_id,project_name,
        project_no,contract_id,contract_number,
        project_total_serve_price,project_total_serve_price,
        cost_minus_money,cost_minus_money_up,remarks,process_pass_time,
        create_user,update_user,create_user_name,
        update_user_name,create_time,update_time,
        del_flag
    </sql>
</mapper>
