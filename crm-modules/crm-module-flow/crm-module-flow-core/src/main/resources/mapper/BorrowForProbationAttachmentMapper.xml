<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForProbationAttachmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForProbationAttachment">
        <id column="id" property="id" />
        <result column="borrow_id" property="borrowId" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="file_type" property="fileType" />
        <result column="doc_id" property="docId" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, borrow_id, process_instance_id, file_type, doc_id, remark, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
