<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ContractForecastUserProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.ContractForecastUserProject">
        <id column="id" property="id" />
        <result column="forecast_user_id" property="forecastUserId" />
        <result column="project_id" property="projectId" />
        <result column="project_no" property="projectNo" />
        <result column="project_name" property="projectName" />
        <result column="final_customer_id" property="finalCustomerId" />
        <result column="final_customer_name" property="finalCustomerName" />
        <result column="specify" property="specify" />
        <result column="forecast_new_contract_amount" property="forecastNewContractAmount" />
        <result column="january" property="january" />
        <result column="february" property="february" />
        <result column="march" property="march" />
        <result column="april" property="april" />
        <result column="may" property="may" />
        <result column="june" property="june" />
        <result column="july" property="july" />
        <result column="august" property="august" />
        <result column="september" property="september" />
        <result column="october" property="october" />
        <result column="november" property="november" />
        <result column="december" property="december" />
        <result column="sign_fifty" property="signFifty" />
        <result column="sign_sixty" property="signSixty" />
        <result column="sign_seventy" property="signSeventy" />
        <result column="sign_eighty" property="signEighty" />
        <result column="sign_ninety" property="signNinety" />
        <result column="sign_hundred" property="signHundred" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, forecast_user_id, project_id, project_no, project_name, final_customer_id, final_customer_name, specify, forecast_new_contract_amount, january, february, march, april, may, june, july, august, september, october, november, december, sign_fifty, sign_sixty, sign_seventy, sign_eighty, sign_ninety, sign_hundred, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
