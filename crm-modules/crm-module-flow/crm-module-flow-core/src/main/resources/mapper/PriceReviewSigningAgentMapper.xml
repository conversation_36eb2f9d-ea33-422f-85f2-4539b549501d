<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PriceReviewSigningAgentMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.PriceReviewSigningAgent">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="projectId" column="project_id" jdbcType="VARCHAR"/>
            <result property="agentId" column="agent_id" jdbcType="VARCHAR"/>
            <result property="parentAgentId" column="parent_agent_id" jdbcType="VARCHAR"/>
            <result property="contractAmount" column="contract_amount" jdbcType="DECIMAL"/>
            <result property="isContractPartyA" column="is_contract_party_a" jdbcType="TINYINT"/>
            <result property="isWinningBidder" column="is_winning_bidder" jdbcType="TINYINT"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="processInstanceId" column="process_instance_id" jdbcType="VARCHAR"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,project_id,agent_id,
        parent_agent_id,contract_amount,is_contract_party_a,
        is_winning_bidder,del_flag,process_instance_id,
        create_user,update_user,create_time,
        update_time
    </sql>
</mapper>
