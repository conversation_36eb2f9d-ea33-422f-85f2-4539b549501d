<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.BorrowForSellDeliveryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.BorrowForSellDelivery">
        <id column="id" property="id" />
        <result column="borrow_id" property="borrowId" />
        <result column="receive" property="receive" />
        <result column="contact" property="contact" />
        <result column="email" property="email" />
        <result column="address" property="address" />
        <result column="delivery" property="delivery" />
        <result column="delivery_aging" property="deliveryAging" />
        <result column="erp_contract" property="erpContract" />
        <result column="receive_company" property="receiveCompany" />
        <result column="receive_city" property="receiveCity" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, borrow_id, receive, contact, email, address, delivery, delivery_aging, erp_contract, receive_company, receive_city, remark, create_time, update_time, create_user, update_user, del_flag
    </sql>

</mapper>
