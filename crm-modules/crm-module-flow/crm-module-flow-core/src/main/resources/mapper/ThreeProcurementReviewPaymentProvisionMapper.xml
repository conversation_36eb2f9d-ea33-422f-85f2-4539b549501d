<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.ThreeProcurementReviewPaymentProvisionMapper">
    
    <resultMap type="com.topsec.crm.flow.core.entity.ThreeProcurementReviewPaymentProvision" id="ThreeProcurementReviewPaymentProvisionResult">
        <result property="id"    column="id"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="dueDate"    column="due_date"    />
        <result property="payableAmount"    column="payable_amount"    />
        <result property="provisionType"    column="provision_type"    />
        <result property="paymentTerms"    column="payment_terms"    />
        <result property="paymentProvision"    column="payment_provision"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectThreeProcurementReviewPaymentProvisionVo">
        select id, process_instance_id, due_date, payable_amount, provision_type, payment_terms, payment_provision, remark, del_flag, create_user, update_user, create_time, update_time from three_procurement_review_payment_provision
    </sql>

    






    <delete id="deleteThreeProcurementReviewPaymentProvisionByIds" parameterType="String">
        delete from three_procurement_review_payment_provision where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>