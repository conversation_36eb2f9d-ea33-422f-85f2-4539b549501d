<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.PerformanceReportProductSeparationSnMapper">
    
    <resultMap type="com.topsec.crm.flow.core.entity.PerformanceReportProductSeparationSn" id="PerformanceReportProductSeparationSnResult">
        <result property="id"    column="id"    />
        <result property="performanceReportProductId"    column="performance_report_product_id"    />
        <result property="performanceReportProductSeparationId"    column="performance_report_product_separation_id"    />
        <result property="psn"    column="psn"    />
        <result property="productDeliveryId"    column="product_delivery_id"    />
        <result property="storageTime"    column="storage_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createUser"    column="create_user"    />
        <result property="updateUser"    column="update_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPerformanceReportProductSeparationSnVo">
        select id, performance_report_product_separation_id, psn,performance_report_product_id , product_delivery_id,storage_time, del_flag, create_user, update_user, create_time, update_time from performance_report_product_separation_sn
    </sql>

    <select id="selectProductSeparationSnStatistics" parameterType="String" resultType="int">
        select count(psn) as productSnTotalNum
        from performance_report_product_own
        left join performance_report_product_separation on performance_report_product_own.id=performance_report_product_separation.performance_report_product_id
        left join performance_report_product_separation_sn on performance_report_product_separation.id=performance_report_product_separation_sn.performance_report_product_separation_id
        where performance_report_product_own.del_flag=0 and performance_report_product_separation.del_flag=0 and performance_report_product_separation_sn.del_flag=0 and process_instance_id = #{id}
    </select>

    <update id="deletePerformanceReportProductSeparationSn" parameterType="String">
        update performance_report_product_separation_sn
        left join performance_report_product_separation on performance_report_product_separation_sn.performance_report_product_separation_id=performance_report_product_separation.id
        set performance_report_product_separation_sn.del_flag=1
        where performance_report_product_separation_sn.del_flag=0 and performance_report_product_separation.del_flag=0 and performance_report_product_separation.performance_report_product_id=#{performanceReportProductId} and (borrow_for_forward is null or borrow_for_forward = 0) and (special_item is null or special_item = 0)
    </update>

    <update id="updateByPerformanceReportProductSeparationId" parameterType="String">
        update performance_report_product_separation_sn set del_flag=1
        where del_flag=0 and performance_report_product_separation_id=#{performanceReportProductSeparationId}
    </update>

    <select id="selectPerformanceReportProductSeparationSn" parameterType="String" resultMap="PerformanceReportProductSeparationSnResult">
        select psn,storage_time
        from performance_report_product_separation_sn
        left join performance_report_product_separation on performance_report_product_separation.id=performance_report_product_separation_sn.performance_report_product_separation_id
        left join performance_report_product_own on performance_report_product_own.id=performance_report_product_separation.performance_report_product_id
        where performance_report_product_own.del_flag=0 and performance_report_product_separation.del_flag=0 and performance_report_product_separation_sn.del_flag=0 and process_instance_id = #{id}
    </select>

    <select id="selectProductDeliverySnInfo" resultType="com.topsec.crm.flow.api.dto.performancereport.ContractDeliveryProductSnVO">
        select performance_report_product_separation_sn.performance_report_product_id, psn
        from performance_report_product_separation_sn
        left join performance_report_product_separation on performance_report_product_separation_sn.performance_report_product_separation_id=performance_report_product_separation.id
        where performance_report_product_separation_sn.del_flag=0 and performance_report_product_separation.del_flag=0 and performance_report_product_separation_sn.performance_report_product_id in
        <foreach item="performanceReportProductId" collection="performanceReportProductIds" open="(" separator="," close=")">
            #{performanceReportProductId}
        </foreach>
        <if test="psnList != null and psnList != ''">
            and psn not in
            <foreach item="psn" collection="psnList" open="(" separator="," close=")">
                #{psn}
            </foreach>
        </if>
    </select>
</mapper>