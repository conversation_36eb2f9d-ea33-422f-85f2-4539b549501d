<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.topsec.crm.flow.core.mapper.CostFilingDetailMapper">

    <resultMap type="com.topsec.crm.flow.core.entity.CostFilingDetail" id="costFilingDetailMap">
        <result property="id" column="id"/>
        <result property="costFilingId" column="cost_filing_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="costType" column="cost_type"/>
        <result property="costMoney" column="cost_money"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="projectId" column="project_id"/>
        <result property="projectNo" column="project_no"/>
        <result property="projectName" column="project_name"/>
        <result property="buckleFrontProfit" column="buckle_front_profit"/>
        <result property="buckleFrontProfitRate" column="buckle_front_profit_rate"/>
        <result property="buckleAfterProfit" column="buckle_after_profit"/>
        <result property="buckleAfterProfitRate" column="buckle_after_profit_rate"/>
        <result property="cost" column="cost"/>
        <result property="whetherPayment" column="whether_payment"/>
        <result property="whetherDeduction" column="whether_deduction"/>
        <result property="deductionMode" column="deduction_mode"/>
        <result property="contractId" column="contract_id"/>
        <result property="contractNo" column="contract_nd"/>
        <result property="personnelId" column="personnel_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="remarks" column="remarks"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <select id="selectByProjectId" resultType="BigDecimal">
        select sum(cost_money) from cost_filing_detail
        where del_flag=0 and project_id=#{projectId} and parent_id is null
    </select>

    <update id="updateCostFilingDetailTaxRate">
        update cost_filing_detail set tax_rate=#{taxRate} where id=#{id} and parent_id is null
    </update>

    <update id="updateCostFilingDetailProject">
        update cost_filing_detail set project_id=#{projectId},project_no=#{projectNo},project_name=#{projectName}
        where id=#{id} and parent_id is null
    </update>

    <select id="selectByParentId" resultType="BigDecimal">
        select sum(cost_money) from cost_filing_detail
        where del_flag=0 and parent_id=#{parentId}
    </select>

    <select id="selectCostMoneyById" resultType="BigDecimal">
        select cost_money from cost_filing_detail
        where del_flag=0 and id=#{id}
    </select>

    <update id="deleteCostFilingDetailByCostFilingId">
        update cost_filing_detail set del_flag=1 where cost_filing_id=#{costFilingId} and del_flag=0
    </update>

    <select id="getCostFilingDetailByCostFilingId" resultType="com.topsec.crm.flow.core.entity.CostFilingDetail">
        select case when d.id is not null then d.id else p.id end id,
               p.cost_type,d.cost,case when d.personnel_id is not null and d.personnel_id!='' then d.personnel_id else p.create_user end personnel_id,d.dept_id,
               d.contract_no,d.contract_id,p.tax_rate,case when d.cost_money is not null then d.cost_money else p.cost_money end cost_money
                ,p.project_no,p.project_name
        from cost_filing_detail as p left join cost_filing_detail as d on p.id=d.parent_id and d.del_flag=0
        where p.del_flag=0 and p.parent_id is null and p.whether_payment=0 and p.cost_filing_id=#{costFilingId}

    </select>

    <select id="getCostFilingDetailByProjectId" resultType="com.topsec.crm.flow.core.entity.CostFilingDetail">
        select case when  SUM(det.cost_money) is not null then SUM(det.cost_money) else SUM(d.cost_money) end as cost_money,
               case when SUM(det.cost_money) is not null then SUM(det.cost_money)/(1+d.tax_rate) else SUM(d.cost_money)/(1+d.tax_rate) end as cost_money_no_tax,d.tax_rate,d.project_id
        from cost_filing_detail as d
                 left join cost_filing as main on main.id=d.cost_filing_id
                 left join cost_filing_detail as det on det.parent_id=d.id and det.del_flag=0
        where d.del_flag=0 and main.del_flag=0 and d.project_id=#{projectId}
        GROUP BY d.tax_rate,d.project_id

    </select>
</mapper>