<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.topsec.crm.flow.core.mapper.SalesAgreementProductMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.topsec.crm.flow.core.entity.SalesAgreementProduct" id="salesAgreementProductMap">
        <result property="id" column="id"/>
        <result property="productId" column="product_id"/>
        <result property="parentProcessInstanceId" column="parent_process_instance_id"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="stuffCode" column="stuff_code"/>
        <result property="productNum" column="product_num"/>
        <result property="dealPrice" column="deal_price"/>
        <result property="productPeriod" column="product_period"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
    </resultMap>


</mapper>