<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.flow.core.mapper.TargetedInventoryPreparationOwnChangeReMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.flow.core.entity.TargetedInventoryPreparationOwnChangeRe">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="targetedInventoryPreparationProductOwnId" column="targeted_inventory_preparation_product_own_id" jdbcType="VARCHAR"/>
            <result property="primaryNum" column="primary_num" jdbcType="INTEGER"/>
            <result property="presentNum" column="present_num" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,targeted_inventory_preparation_product_own_id,primary_num,
        present_num,del_flag,create_user,
        update_user,create_time,update_time
    </sql>
</mapper>
