spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ${CRM-NACOS.SERVER-NACOS-TEST}
  # Hikari连接池
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:mysql://${CRM-MYSQL.SERVER-MYSQL-URL-TEST}/crm_flow?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&useSSL=false&allowMultiQueries=true&allowPublicKeyRetrieval=true
    username: ${CRM-MYSQL.SERVER-MYSQL-USERNAME-TEST}
    password: ${CRM-MYSQL.SERVER-MYSQL-PASSWORD-TEST}
rocketmq:
  name-server: ${CRM-ROCKETMQ.SERVER-TEST}
qr_code:
  base_url: https://10.7.175.140/agentRegistration/validateQrCode
