spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ${CRM-NACOS.SERVER-NACOS-PROD}
  # Hikari连接池
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:mysql://${CRM-MYSQL.SERVER-MYSQL-URL-PROD}/crm_flow?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&useSSL=false&allowMultiQueries=true&allowPublicKeyRetrieval=true
    username: ${CRM-MYSQL.SERVER-MYSQL-USERNAME-PROD}
    password: ${CRM-MYSQL.SERVER-MYSQL-PASSWORD-PROD}
rocketmq:
  name-server: ${CRM-ROCKETMQ.SERVER-PROD}
qr_code:
  base_url:
