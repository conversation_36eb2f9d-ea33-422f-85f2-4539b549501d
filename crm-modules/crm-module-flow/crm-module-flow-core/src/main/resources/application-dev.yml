spring:
  config:
    import:
      - optional:classpath:import-local.yml
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ${CRM-NACOS.SERVER-NACOS-DEV}
  # Hikari连接池
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:mysql://${CRM-MYSQL.SERVER-MYSQL-URL-DEV}/crm_flow?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&useSSL=false&allowMultiQueries=true&allowPublicKeyRetrieval=true
    username: ${CRM-MYSQL.SERVER-MYSQL-USERNAME-DEV}
    password: ${CRM-MYSQL.SERVER-MYSQL-PASSWORD-DEV}
rocketmq:
  name-server: ${CRM-ROCKETMQ.SERVER-DEV}
qr_code:
  base_url: https://10.7.175.140/agentRegistration/validateQrCode
