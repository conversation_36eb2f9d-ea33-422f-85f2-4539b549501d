package com.topsec.crm.flow.core.validator.pricereview.condition;

import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.core.validator.CheckCondition;
import com.topsec.crm.flow.core.validator.pricereview.PriceReviewCheckContext;
import com.topsec.crm.flow.core.validator.pricereview.ProductCompareUtil;
import org.apache.commons.collections4.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 自有产品中lic数量变化 ，lic数增大失效，减小不失效 ，价审应该失效
 * <AUTHOR>
 */
public class OwnLicNumChangeCondition implements CheckCondition<PriceReviewCheckContext> {


    @Override
    public boolean check(PriceReviewCheckContext context)  {
        List<PriceReviewProductOwnDTO> currentOwnList = ListUtils.emptyIfNull(context.getProjectDetail().getProductOwnList());
        List<PriceReviewProductOwnDTO> snapshotOwnList = ListUtils.emptyIfNull(context.getProjectSnapshot().getProductOwnList());
        return ProductCompareUtil.validate(currentOwnList, snapshotOwnList,"productLic",(currentCompareMap, snapshotCompareMap) -> {
            Integer currentCount = currentCompareMap.values().stream().mapToInt(Integer::intValue).sum();
            Integer snapshotCount = snapshotCompareMap.values().stream().mapToInt(Integer::intValue).sum();

            if(!currentCount.equals(snapshotCount)) return false;

            List<Integer> currentExpandedList = expandAndSortMap(currentCompareMap);
            List<Integer> snapshotExpandedList = expandAndSortMap(snapshotCompareMap);

            return compareLists(currentExpandedList, snapshotExpandedList);
        });
    }
    private static List<Integer> expandAndSortMap(Map<Object, Integer> map) {
        // 对列表进行排序
        return map.entrySet().stream()
                .flatMapToInt(entry -> IntStream.generate(() -> {
                    Object key = entry.getKey();
                    return (Integer) key;
                }).limit(entry.getValue()))
                .boxed().sorted().collect(Collectors.toList());
    }

    private static boolean compareLists(List<Integer> current, List<Integer> snapshot) {
        for (int i = 0; i < current.size(); i++) {
            Integer c = current.get(i);
            Integer s = snapshot.get(i);
            if (c>s) return false;
        }
        return true;
    }


    @Override
    public String defaultFailureReason() {
        return "自有产品中lic数量增加";
    }

    public static void main(String[] args) {

        List<PriceReviewProductOwnDTO> current=new ArrayList<>();

        PriceReviewProductOwnDTO priceReviewProductThirdDTO=new PriceReviewProductOwnDTO();
        priceReviewProductThirdDTO.setProductId("1");
        priceReviewProductThirdDTO.setProductNum(1);
        priceReviewProductThirdDTO.setProductLic(300);
        // priceReviewProductThirdDTO.setGrossMargin(new BigDecimal("4455"));
        current.add(priceReviewProductThirdDTO);
        priceReviewProductThirdDTO=new PriceReviewProductOwnDTO();
        priceReviewProductThirdDTO.setProductId("1");
        priceReviewProductThirdDTO.setProductNum(2);
        current.add(priceReviewProductThirdDTO);

        List<PriceReviewProductOwnDTO> snapshot=new ArrayList<>();
        priceReviewProductThirdDTO=new PriceReviewProductOwnDTO();
        priceReviewProductThirdDTO.setProductId("1");
        priceReviewProductThirdDTO.setProductNum(2);
        priceReviewProductThirdDTO.setProductLic(150);
        // priceReviewProductThirdDTO.setGrossMargin(new BigDecimal("4455.000000"));
        snapshot.add(priceReviewProductThirdDTO);
        priceReviewProductThirdDTO=new PriceReviewProductOwnDTO();
        priceReviewProductThirdDTO.setProductId("1");
        priceReviewProductThirdDTO.setProductNum(2);
        snapshot.add(priceReviewProductThirdDTO);


        boolean r1 = ProductCompareUtil.validate(current, snapshot ,"productLic");
        // boolean r2 = ProductCompareUtil.validate(current, snapshot, "productLic");
        boolean r2= ProductCompareUtil.validate(current, snapshot,"productLic",(currentCompareMap, snapshotCompareMap) -> {
            Integer currentCount = currentCompareMap.values().stream().mapToInt(Integer::intValue).sum();
            Integer snapshotCount = snapshotCompareMap.values().stream().mapToInt(Integer::intValue).sum();

            if(!currentCount.equals(snapshotCount)) return false;

            List<Integer> currentExpandedList = expandAndSortMap(currentCompareMap);
            List<Integer> snapshotExpandedList = expandAndSortMap(snapshotCompareMap);

            return compareLists(currentExpandedList, snapshotExpandedList);
        });


        Map<String, List<PriceReviewProductOwnDTO>> currentMap = ProductCompareUtil.convert(current);
        Map<String, List<PriceReviewProductOwnDTO>> snapshotMap = ProductCompareUtil.convert(snapshot);
        boolean grossMargin = ProductCompareUtil.validate(currentMap, snapshotMap, "grossMargin");
        boolean grossMarginRatio =  ProductCompareUtil.validate(currentMap, snapshotMap, "grossMarginRatio");
        System.out.println(grossMargin);
        System.out.println(grossMarginRatio);
    }


}
