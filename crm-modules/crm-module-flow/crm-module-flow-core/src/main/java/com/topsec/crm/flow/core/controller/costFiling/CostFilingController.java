package com.topsec.crm.flow.core.controller.costFiling;


import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.entity.CrmContractBaseInfoVO;
import com.topsec.crm.flow.api.dto.costContarct.CostContractAttachmentInfoDTO;
import com.topsec.crm.flow.api.dto.costEarlyPayment.CostEarlyPaymentReInfoDTO;
import com.topsec.crm.flow.api.dto.costFiling.CostFilingDetailInfoDTO;
import com.topsec.crm.flow.api.dto.costFiling.CostFilingFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.costFiling.CostFilingInfo;
import com.topsec.crm.flow.api.dto.costFiling.VO.CostFilingQueryVO;
import com.topsec.crm.flow.api.dto.costFiling.VO.SupplierInfoVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CostFiling;
import com.topsec.crm.flow.core.entity.CostFilingDetail;
import com.topsec.crm.flow.core.process.impl.CostFilingProcessService;
import com.topsec.crm.flow.core.service.CostContractAttachmentService;
import com.topsec.crm.flow.core.service.ICostFilingDetailService;
import com.topsec.crm.flow.core.service.ICostFilingService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;


/**
* 费用备案主表
*
* <AUTHOR>
* @since 1.0.0 2024-07-18
*/
@RestController
@RequestMapping("/costFiling")
@Tag(name = "【费用备案-流程接口】", description = "costFiling")
@RequiredArgsConstructor
@Validated
public class CostFilingController extends BaseController {

    @Resource
    private ICostFilingService iCostFilingService;
    @Resource
    private CostFilingProcessService costFilingProcessService;
    @Resource
    private ICostFilingDetailService iCostFilingDetailService;

    private final CostContractAttachmentService costContractAttachmentService;

    private final RemoteContractReviewService remoteContractReviewService;

    @PreAuthorize(hasAnyPermission={"crm_flow_cost_filing","crm_cost_filing"})
    @PostMapping("/launch")
    @Operation(summary = "发起费用备案")
    public JsonObject<Boolean> launch(@Valid @RequestBody CostFilingFlowLaunchDTO costFilingFlowLaunchDTO){
        return new JsonObject<>(costFilingProcessService.launch(costFilingFlowLaunchDTO));
    }

    @PreFlowPermission
    @GetMapping("/selectCostFilingByProcessInstanceId")
    @Operation(summary = "根据流程ID查询费用备案")
    public JsonObject<CostFilingInfo> selectCostFilingByProcessInstanceId(String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iCostFilingService.selectCostFilingByProcessInstanceId(processInstanceId));
    }


    @PreFlowPermission(hasAnyNodes = {"expenseFiling_02"})
    @PostMapping("/saveOrUpdateCostFilingDetail")
    @Operation(summary = "新增/修改 拆分费用备案明细")
    public JsonObject<Boolean> saveOrUpdateCostFilingDetail(@RequestBody CostFilingDetailInfoDTO costFilingDetailInfoDTO){
        CrmAssert.hasText(costFilingDetailInfoDTO.getParentId(),"父级ID不能为空！");
        Optional<CostFilingDetail> optionalById = Optional.ofNullable(iCostFilingDetailService.getById(costFilingDetailInfoDTO.getParentId()));
        if (optionalById.isPresent()){
            CostFilingDetail costFilingDetail = optionalById.get();
            CostFiling costFiling  = iCostFilingService.getById(costFilingDetail.getCostFilingId());
            PreFlowPermissionAspect.checkProcessInstanceId(costFiling.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(iCostFilingDetailService.saveOrUpdateCostFilingDetail(costFilingDetailInfoDTO));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }

    }

    @PreFlowPermission
    @GetMapping("/selectDetailByCostFilingId")
    @Operation(summary = "根据主表ID分页查询费用备案明细")
    public JsonObject<TableDataInfo> selectDetailByCostFilingId(@RequestParam String costFilingId){
        CrmAssert.hasText(costFilingId,"主表ID不能为空！");
        Optional<CostFiling> optionalById = Optional.ofNullable(iCostFilingService.getById(costFilingId));
        if (optionalById.isPresent()){
            CostFiling costFiling = optionalById.get();
            PreFlowPermissionAspect.checkProcessInstanceId(costFiling.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(iCostFilingDetailService.selectDetailByCostFilingId(costFilingId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission(hasAnyNodes = {"expenseFiling_02"})
    @GetMapping("/updateCostFilingDetail")
    @Operation(summary = "02合同管理部审批后写死毛利、毛利率")
    public JsonObject<Boolean> updateCostFilingDetail(String costFilingId){

        CrmAssert.hasText(costFilingId,"主表ID不能为空！");
        Optional<CostFiling> optionalById = Optional.ofNullable(iCostFilingService.getById(costFilingId));
        if (optionalById.isPresent()){
            CostFiling costFiling = optionalById.get();
            PreFlowPermissionAspect.checkProcessInstanceId(costFiling.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(iCostFilingDetailService.updateCostFilingDetail(costFilingId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreAuthorize
    @GetMapping("/getEarlyPaymentReByCostFilingId")
    @Operation(summary = "根据费用备案ID查询费用提前支付明细")
    public JsonObject<List<CostEarlyPaymentReInfoDTO>> getEarlyPaymentReByCostFilingId(@RequestParam String costFilingId){
        return new JsonObject<>(iCostFilingDetailService.getEarlyPaymentReByCostFilingId(costFilingId));
    }

    @PreFlowPermission
    @GetMapping("/getCostFilingMoenyByProcessInstanceId")
    @Operation(summary = "流程中校验是否产过项目中外包服务金额")
    public JsonObject<Boolean> getCostFilingMoenyByProcessInstanceId(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iCostFilingService.getCostFilingMoenyByProcessInstanceId(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/getByCostContractId")
    @Operation(summary = "查询费用备案附件信息")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getByCostContractId(@RequestParam String costId, @RequestParam(required = false) String isFinalQuery){
        CrmAssert.hasText(costId,"主表ID不能为空！");
        Optional<CostFiling> optionalById = Optional.ofNullable(iCostFilingService.getById(costId));
        if (optionalById.isPresent()){
            CostFiling costFiling = optionalById.get();
            PreFlowPermissionAspect.checkProcessInstanceId(costFiling.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractAttachmentService.getByCostContractId(costId,isFinalQuery));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreAuthorize(hasAnyPermission={"crm_flow_cost_filing","crm_cost_filing"})
    @GetMapping("/getSupplierInfoVoByCorporateId")
    @Operation(summary = "根据人员查询供应商信息")
    public JsonObject<List<SupplierInfoVo>> getSupplierInfoVoByCorporateId(String corporateId){
        return new JsonObject<>(iCostFilingService.getSupplierInfoVoByCorporateId(corporateId));
    }

    @PreFlowPermission(hasAnyNodes = {"expenseFiling_02"})
    @GetMapping("/getContractInfoByParams")
    @Operation(summary = "合同信息查询")
    public JsonObject<List<CrmContractBaseInfoVO>> getContractInfoByParams(CostFilingQueryVO queryVO){
        return new JsonObject<>(iCostFilingService.getCostFilingContractInfo(queryVO));
    }

    @PreFlowPermission
    @GetMapping("/getIsTransferMinus")
    @Operation(summary = "流程中校验是否在走费用调减流程")
    public JsonObject<Boolean> getIsTransferMinus(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iCostFilingService.getIsTransferMinus(processInstanceId));
    }

}
