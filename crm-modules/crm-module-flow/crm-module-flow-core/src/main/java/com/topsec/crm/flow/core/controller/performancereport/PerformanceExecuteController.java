package com.topsec.crm.flow.core.controller.performancereport;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.topsec.crm.flow.api.dto.agentInvoice.AgentInvoiceDTO;
import com.topsec.crm.flow.api.dto.performancereport.*;
import com.topsec.crm.flow.core.entity.PerformanceExecute;
import com.topsec.crm.flow.core.entity.PerformanceReportDoc;
import com.topsec.crm.flow.core.entity.PerformanceReportPaymentInfo;
import com.topsec.crm.flow.core.mapstruct.PerformanceReportConvertor;
import com.topsec.crm.flow.core.mapstruct.performanceExcuteConvertor;
import com.topsec.crm.flow.core.service.AgentInvoiceMainService;
import com.topsec.crm.flow.core.service.PerformanceExecuteService;
import com.topsec.crm.flow.core.service.PerformanceReportProductOwnService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.logistics.api.domain.BatchSubParam;
import com.topsec.logistics.api.domain.LogisticsInfo;
import com.topsec.logistics.api.domain.LogisticsInfoDetail;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/performanceExecute")
@Tag(name = "业绩执行", description = "/performanceExecute")
@RequiredArgsConstructor
public class PerformanceExecuteController extends BaseController {

    private final PerformanceExecuteService performanceReportService;

    private final PerformanceReportProductOwnService performanceReportProductOwnService;

    private final AgentInvoiceMainService agentInvoiceMainService;
    @Data
    public class LogisticsInfoVO{
        @Schema(description = "物流信息")
        private LogisticsInfo logisticsInfo;

        @Schema(description = "发货序列号详情")
        private DeliveryProductSnDTO deliveryProductSnDTO;
    }

    @PostMapping("/pagePerformanceReport")
    @Operation(summary = "分页查询业绩执行信息(内部员工)")
    @PreAuthorize(hasPermission = "crm_performance_execute_execute", dataScope = "crm_performance_execute_execute")
    public JsonObject<PageUtils<PerformanceExecuteVO>> pageNternal(@RequestBody PerformanceExecuteQuery query){
        PageUtils<PerformanceExecuteVO> performanceExecuteVOPageUtils = performanceReportService.pageInternal(query);
        return new JsonObject<>(performanceExecuteVOPageUtils);
    }
    @PostMapping("/exportNternal")
    @Operation(summary = "导出业绩执行执行信息(内部员工)", method = "POST")
    @PreAuthorize(hasPermission = "crm_performance_execute_export", dataScope = "crm_performance_execute_export")
    public void exportNternal(@RequestBody PerformanceExecuteQuery query) throws Exception {
        PageUtils<PerformanceExecuteVO> pageUtils = performanceReportService.pageInternal(query);
        List<PerformanceExecuteVO> list = pageUtils.getList();
        ExcelUtil<PerformanceExecuteVO> excelUtil = new ExcelUtil<>(PerformanceExecuteVO.class);
        excelUtil.exportExcel(response, list,"业绩执行");
    }
    @PostMapping("/pageAgent")
    @Operation(summary = "分页查询业绩执行信息(渠道)")
    @PreAuthorize(hasPermission = "crm_performance_execute_execute_agent", dataScope = "crm_performance_execute_execute_agent")
    public JsonObject<PageUtils<PerformanceExecuteVO>> pageAgent(@RequestBody PerformanceExecuteQuery query){
        PageUtils<PerformanceExecuteVO> performanceExecuteVOPageUtils = performanceReportService.pageAgent(query);
        return new JsonObject<>(performanceExecuteVOPageUtils);
    }

    @PostMapping("/getByProcessInstanceId")
    @Operation(summary = "业绩执行-根据流程实例id获取业绩上报明细")
    @PreAuthorize(hasAnyPermission = {"crm_performance_execute_execute_agent","crm_performance_execute_execute"}, dataScope = "crm_performance_execute_execute")
    public JsonObject<PerformanceExecuteVO> getByProcessInstanceId(@RequestParam String processInstanceId) {
        if (!checkDataScopeAndPermission(processInstanceId)) {
            return new JsonObject<>(null);
        }
        PerformanceExecute performanceExecute = performanceReportService.getByProcessInstanceId(processInstanceId);
        PerformanceExecuteVO convert = performanceExcuteConvertor.INSTANCE.toExecuteVO(performanceExecute);
        return new JsonObject<>(convert);
    }
    @PostMapping("/getPerformanceReportProductByProcessInstanceId")
    @Operation(summary = "业绩执行-根据流程实例id获取业绩上报产品明细")
    @PreAuthorize(hasAnyPermission = {"crm_performance_execute_execute_agent","crm_performance_execute_execute"}, dataScope = "crm_performance_execute_execute")
    public JsonObject<PageUtils<PerformanceReportProductOwnDTO>> getPerformanceReportProductByProcessInstanceId(@RequestParam String processInstanceId) {
        if (!checkDataScopeAndPermission(processInstanceId)) {
            return new JsonObject<>(null);
        }
        List<PerformanceReportProductOwnDTO> performanceReportProductOwnDTOList = performanceReportService.getPerformanceReportProductByProcessInstanceId(processInstanceId);
        PerformanceExecuteQuery query = new PerformanceExecuteQuery();
        IPage<PerformanceReportProductOwnDTO> page = new Page<>(query.getPageNum(), query.getPageSize());
        page.setRecords(performanceReportProductOwnDTOList);
        PageUtils<PerformanceReportProductOwnDTO> pageUtils = new PageUtils<>(page);
        return new JsonObject<>(pageUtils);
    }

    @GetMapping("/paymentInfoList")
    @Operation(summary = "业绩执行-付款信息")
    @PreAuthorize(hasAnyPermission = {"crm_performance_execute_execute_agent","crm_performance_execute_execute"}, dataScope = "crm_performance_execute_execute")
    public JsonObject<PageUtils<PerformanceReportPaymentInfoDTO>>paymentInfoList (@RequestParam String processInstanceId){
        if (!checkDataScopeAndPermission(processInstanceId)) {
            return new JsonObject<>(null);
        }
        List<PerformanceReportPaymentInfo> performanceReportPaymentInfos = performanceReportService.selectPerformanceReportPaymentInfoByProcessInstanceId(processInstanceId);
        List<PerformanceReportPaymentInfoDTO> paymentInfoVOList = PerformanceReportConvertor.INSTANCE.toPaymentInfoVOList(performanceReportPaymentInfos);
        PageUtils dataTable = getDataTable(performanceReportPaymentInfos,paymentInfoVOList);
        return new JsonObject<>(dataTable);
    }

    @GetMapping("/contractDeliveryList")
    @Operation(summary = "业绩执行-合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_performance_execute_execute_agent","crm_performance_execute_execute"}, dataScope = "crm_performance_execute_execute")
    public JsonObject<List<PerformanceReportContractDeliveryDTO>>contractDeliveryList (@RequestParam String processInstanceId){
        if (!checkDataScopeAndPermission(processInstanceId)) {
            return new JsonObject<>(null);
        }
        List<PerformanceReportContractDeliveryDTO> contractDeliveryVOList = performanceReportService.getContractDeliveryVOList(processInstanceId);
        return new JsonObject<>(contractDeliveryVOList);
    }

    @GetMapping("/selectPerformanceReportDoc")
    @Operation(summary = "业绩执行-业绩上报合同附件")
    @PreAuthorize(hasAnyPermission = {"crm_performance_execute_execute_agent","crm_performance_execute_execute"}, dataScope = "crm_performance_execute_execute")
    public JsonObject<List<PerformanceReportDocDTO>> selectPerformanceReportDoc (@RequestParam String processInstanceId){
        if (!checkDataScopeAndPermission(processInstanceId)) {
            return new JsonObject<>(null);
        }
        List<PerformanceReportDoc> performanceReportDocList = performanceReportService.selectPerformanceReportDoc(processInstanceId);
        List<PerformanceReportDocDTO> docVOList = PerformanceReportConvertor.INSTANCE.toDocVOList(performanceReportDocList);
        return new JsonObject<>(docVOList);
    }

    @GetMapping("/priceStatistics")
    @Operation(summary = "业绩执行-业绩上报价格统计")
    @PreAuthorize(hasAnyPermission = {"crm_performance_execute_execute_agent","crm_performance_execute_execute"}, dataScope = "crm_performance_execute_execute")
    public JsonObject<performancereportPriceStatisticsVO> priceStatistics(@RequestParam String processInstanceId){
        //todo 业绩上报价格统计
        return new JsonObject<>();
    }

    @GetMapping("/getDeliverySnInfo")
    @Operation(summary = "业绩执行-查询发货详情信息")
    @PreAuthorize(hasAnyPermission = {"crm_performance_execute_execute_agent","crm_performance_execute_execute"}, dataScope = "crm_performance_execute_execute")
    public JsonObject<List<PerformanceExecuteController.LogisticsInfoVO>> getDeliverySnInfo(@RequestParam String contractDeliveryId){
        List<PerformanceExecuteController.LogisticsInfoVO> logisticsInfoVOList = new ArrayList<>();
        List<DeliveryProductSnDTO> deliveryProductSnDTOS = performanceReportProductOwnService.selectDeliverySnInfo(contractDeliveryId);
        // 快递信息
        ListUtils.emptyIfNull(deliveryProductSnDTOS).forEach(deliveryProductSnDTO -> {
            BatchSubParam batchSubParam=new BatchSubParam();
            batchSubParam.setCompany(deliveryProductSnDTO.getCompanyName());
            batchSubParam.setKdybCom(deliveryProductSnDTO.getCompanyCode());
            batchSubParam.setNumber(deliveryProductSnDTO.getDeliveryNo());
            batchSubParam.setPhone("1111");
            List<LogisticsInfoDetail> logisticsInfoDetails =new ArrayList<>();
            logisticsInfoDetails.add(new LogisticsInfoDetail("3", deliveryProductSnDTO.getDeliveryNo(), "[洛阳市]您的快件已签收，如有疑问请电联快递员【徐好，电话：18336768057】，感谢您使用顺丰，期待再次为您服务。（主单总件数：1件）", "2024-08-17", "河南,洛阳市", "已签收", "洛阳市", "112.45404,34.619682", "luo yang shi", "3"));
            logisticsInfoDetails.add(new LogisticsInfoDetail("2", deliveryProductSnDTO.getDeliveryNo(), "[洛阳市]快件交给徐好，正在派送途中（联系电话：18336768057，顺丰已开启“安全呼叫”保护您的电话隐私,请放心接听！）（主单总件数：1件）", "2024-07-17", "河南,洛阳市", "派件中", "洛阳市", "112.45404,34.619682", "luo yang shi", "5"));
            logisticsInfoDetails.add(new LogisticsInfoDetail("1", deliveryProductSnDTO.getDeliveryNo(), "[洛阳市]快件已发车", "2024-06-17", "河南,洛阳市", "揽收", "洛阳市", "112.45404,34.619682", "luo yang shi", "103"));
            JsonObject<LogisticsInfo> info = new JsonObject<>();
            info.setResult(0);
            info.setObjEntity(new LogisticsInfo("1", "shutdown", "ZTO", "中通快递", "3", "1", "2023-11-11","北京市","洛阳市", logisticsInfoDetails, "1"));

            PerformanceExecuteController.LogisticsInfoVO logisticsInfoVO=new PerformanceExecuteController.LogisticsInfoVO();
            if (info.isSuccess()){
                logisticsInfoVO.setLogisticsInfo(info.getObjEntity());
            }
            logisticsInfoVO.setDeliveryProductSnDTO(deliveryProductSnDTO);
            logisticsInfoVOList.add(logisticsInfoVO);
        });
        return new JsonObject<>(logisticsInfoVOList);
    }


    @GetMapping("/getAgentInvoiceList")
    @Operation(summary = "业绩执行-总代开票")
    @PreAuthorize(hasAnyPermission = {"crm_performance_execute_execute_agent","crm_performance_execute_execute"}, dataScope = "crm_performance_execute_execute")
    public JsonObject<List<AgentInvoiceDTO>> getAgentInvoiceList(@RequestParam String performanceReportProcessInstanceId){
        checkDataScopeAndPermission(performanceReportProcessInstanceId);
        return new JsonObject<>(agentInvoiceMainService.getByPerformanceReportProcessInstanceId(performanceReportProcessInstanceId));
    }

    @GetMapping("/querySupplierList")
    @Operation(summary = "业绩执行-供货方")
    @PreAuthorize(hasPermission = "crm_performance_execute_execute", dataScope = "crm_performance_execute_execute")
    public JsonObject<Map<String,String>> querySupplierList(){
        Map<String, String> map = performanceReportService.quertSupplierList();
        return new JsonObject<>(map);
    }

    //todo “回款明细“数据来源于业绩上报中的回款情况；
    //todo 总代开票 数据来源于同步开票信息，详情字段来源参考总代开票
    private boolean checkDataScopeAndPermission(String processInstanceId) {
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        if (StringUtils.isNotEmpty(UserInfoHolder.getCurrentAgentId())) {
            dataScopeParam.setAgentId(UserInfoHolder.getCurrentAgentId());
        }
        return performanceReportService.hasRight(processInstanceId, dataScopeParam);
    }
}
