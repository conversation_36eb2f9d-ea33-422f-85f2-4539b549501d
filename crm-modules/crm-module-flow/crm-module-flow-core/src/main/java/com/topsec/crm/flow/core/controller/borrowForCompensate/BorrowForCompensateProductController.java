package com.topsec.crm.flow.core.controller.borrowForCompensate;


import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.flow.api.dto.borrowForCompensate.BorrowForCompensateDTO;
import com.topsec.crm.flow.api.dto.borrowForCompensate.BorrowForCompensateProductDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BorrowForCompensate;
import com.topsec.crm.flow.core.entity.BorrowForCompensateProduct;
import com.topsec.crm.flow.core.service.IBorrowForCompensateProductService;
import com.topsec.crm.flow.core.service.IBorrowForCompensateService;
import com.topsec.crm.framework.common.constant.BorrowForProbationConstants;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteBorrowForProbationClient;
import com.topsec.crm.project.api.entity.CrmBorrowForProbationDeviceVO;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 丢失赔偿产品控制器
 */
@RestController
@RequestMapping("/borrowForCompensateProduct")
@Tag(name = "丢失赔偿产品控制器", description = "/borrowForCompensateProduct")
public class BorrowForCompensateProductController extends BaseController {

    @Autowired
    private IBorrowForCompensateService borrowForCompensateService;
    @Autowired
    private IBorrowForCompensateProductService borrowForCompensateProductService;
    @Autowired
    private RemoteBorrowForProbationClient remoteBorrowForProbationClient;
    @Autowired
    private TfsNodeClient tfsNodeClient;

    @PostMapping("/page")
    @Operation(summary = "丢失赔偿产品分页列表")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<PageUtils<BorrowForCompensateProductDTO>> list(@RequestBody BorrowForCompensateDTO borrowForCompensateDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(borrowForCompensateDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询转借流程信息
        BorrowForCompensate bfc = borrowForCompensateService.query()
                .eq("process_instance_id", borrowForCompensateDTO.getProcessInstanceId()).one();

        startPage();
        //查询产品关联关系列表信息
        List<BorrowForCompensateProduct> compensateProducts = borrowForCompensateProductService.query().eq("compensate_id", bfc.getId()).list();
        List<BorrowForCompensateProductDTO> borrowForCompensateProductDTOS = HyperBeanUtils.copyListPropertiesByJackson(compensateProducts, BorrowForCompensateProductDTO.class);
        //查询具体产品信息
        for (BorrowForCompensateProductDTO borrowForCompensateProductDTO : borrowForCompensateProductDTOS) {
            String id = borrowForCompensateProductDTO.getId();
            String probationId = borrowForCompensateProductDTO.getProjectProbationDeviceId();
            JsonObject<CrmBorrowForProbationDeviceVO> jObj = remoteBorrowForProbationClient.getBorrowForProbationDeviceById(probationId);
            if(jObj.isSuccess()){
                CrmBorrowForProbationDeviceVO deviceVO = jObj.getObjEntity();
                HyperBeanUtils.copyProperties(deviceVO, borrowForCompensateProductDTO);
                borrowForCompensateProductDTO.setId(id);
            }
        }

        //对字段做权限过滤
        filterData(bfc,borrowForCompensateProductDTOS);

        //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
        PageUtils dataTable = getDataTable(compensateProducts,borrowForCompensateProductDTOS);
        return new JsonObject<>(dataTable);
    }

    private void filterData(BorrowForCompensate bfc,List<BorrowForCompensateProductDTO> borrowForCompensateProductDTOS) {
        /**
         * 赔偿流程核定金额、参考金额可见节点
         * 01、02、05、05A
         */
        List<String> activityIds = BorrowForProbationConstants.BORROW_FOR_COM_YJ;
        //可见ID
        JsonObject<Set<String>> setJsonObject = tfsNodeClient.queryAssigneeAccountIdList(bfc.getProcessInstanceId(), activityIds);
        if(setJsonObject.isSuccess()){
            if(!UserInfoHolder.getCurrentPersonId().equals(bfc.getCreateUser())
                    && !setJsonObject.getObjEntity().contains(UserInfoHolder.getCurrentAccountId())){
                for (BorrowForCompensateProductDTO borrowForCompensateProductDTO : borrowForCompensateProductDTOS) {
                    borrowForCompensateProductDTO.setReferenceAmount(null);
                }
            }
        }
    }

    @PostMapping("/edit")
    @Operation(summary = "01.填写参考赔偿金额")
    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"sid-********-97B5-407F-B711-DDFFC846A515"})
    public JsonObject<Boolean> editReferenceAmount(@RequestBody List<BorrowForCompensateProductDTO> borrowForBorrowForCompensateProductDTOS) {
        //权限校验
        //1.查询转借流程信息
        BorrowForCompensate bfc = borrowForCompensateService.query()
                .eq("process_instance_id", HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID)).one();

        //查询产品关联关系列表信息
        List<BorrowForCompensateProduct> compensateProducts = borrowForCompensateProductService.query().eq("compensate_id", bfc.getId()).list();
        Set<String> collect = compensateProducts.stream().map(BorrowForCompensateProduct::getId).collect(Collectors.toSet());
        for (BorrowForCompensateProductDTO bfbf : borrowForBorrowForCompensateProductDTOS) {
            if(!collect.contains(bfbf.getId())){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }

        List<BorrowForCompensateProduct> products = HyperBeanUtils.copyListPropertiesByJackson(borrowForBorrowForCompensateProductDTOS, BorrowForCompensateProduct.class);
        for (BorrowForCompensateProduct product : products) {
            borrowForCompensateProductService.update(new UpdateWrapper<BorrowForCompensateProduct>().eq("id",product.getId())
                    .set("reference_amount", product.getReferenceAmount())
            );
        }
        return new JsonObject<Boolean>(ResultEnum.SUCCESS.getResult(), ResultEnum.SUCCESS.getMessage(),true);
    }
}
