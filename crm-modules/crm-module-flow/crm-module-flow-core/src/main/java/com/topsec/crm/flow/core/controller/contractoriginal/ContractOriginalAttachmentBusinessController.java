package com.topsec.crm.flow.core.controller.contractoriginal;

import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.originaldocument.CrmContractOriginalDocumentVO;
import com.topsec.crm.flow.api.dto.contractoriginal.ContractOriginalAttachmentInfoDTO;
import com.topsec.crm.flow.core.service.ContractOriginalDocumentAttachmentService;
import com.topsec.crm.flow.core.service.ContractOriginalDocumentService;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * 费用合同附件
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/contractOriginalAttachment")
@Tag(name = "【合同原件附件-业务接口】", description = "contractOriginalAttachment")
@RequiredArgsConstructor
@Validated
public class ContractOriginalAttachmentBusinessController extends BaseController {

    private final ContractOriginalDocumentAttachmentService contractOriginalDocumentAttachmentService;

    @Resource
    private ContractOriginalDocumentService contractOriginalDocumentService;

    @Resource
    private RemoteContractExecuteService reviewService;

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @GetMapping("/getByCostContractId")
    @Operation(summary = "查询合同原件附件信息")
    public JsonObject<List<ContractOriginalAttachmentInfoDTO>> getByCostContractId(@RequestParam String originalId) {
        check(originalId);
        return new JsonObject<>(contractOriginalDocumentAttachmentService.getByCostContractId(originalId));
    }

    private void check(String originalId){
        CrmContractOriginalDocumentVO contractOriginalDocInfoVO = contractOriginalDocumentService.getInfoById(originalId);
        CrmAssert.notNull(contractOriginalDocInfoVO, "合同原件记录不存在");
        Optional.ofNullable(reviewService.getByContractNumber(contractOriginalDocInfoVO.getContractNumber()).getObjEntity()).ifPresent(crmContractExecuteVO -> {
            PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), crmContractExecuteVO.getContractOwnerId());
        });
    }

}
