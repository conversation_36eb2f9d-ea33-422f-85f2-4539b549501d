package com.topsec.crm.flow.core.controller.threeProcurement;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.baseinfo.ContractBasicInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.api.vo.*;
import com.topsec.crm.flow.core.entity.ContractReviewPaymentProvision;
import com.topsec.crm.flow.core.entity.ContractReviewReturnExchange;
import com.topsec.crm.flow.core.entity.ThreeProcurementRelease;
import com.topsec.crm.flow.core.entity.ThreeProcurementReviewMain;
import com.topsec.crm.flow.core.process.impl.ThreeProcurementReviewProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.operation.api.entity.SupplierVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbsapi.client.TbsResourceClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 第三方采购Controller
 *
 * @date 2024-09-02
 */
@RestController
@RequestMapping("/business/threeProcurement")
@Tag(name = "【第三方采购】", description = "threeProcurement")
@RequiredArgsConstructor
@Validated
public class ThreeProcurementBusinessController extends BaseController
{

    private final ThreeProcurementReviewMainService threeProcurementReviewMainService;


    private final ContractReviewMainService contractReviewMainService;
    private final ReturnExchangeProductThirdService returnExchangeProductThirdService;


    private final ContractReviewPaymentProvisionService contractReviewPaymentProvisionService;

    private final ThreeProcurementPaymentReviewMainService threeProcurementPaymentReviewMainService;

    private final ThreeProcurementReleaseService threeProcurementReleaseService;

    private final ThreeProcurementReviewProcessService threeProcurementReviewProcessService;
    private final ThreeProcurementProductService threeProcurementProductService;
    private final TbsResourceClient tbsResourceClient;

    private final TfsNodeClient tfsNodeClient;

    private final ContractReviewReturnExchangeService contractReviewReturnExchangeService;




    @PostMapping("/launch")
    @Operation(summary = "发起第三方采购流程-合同评审详情普通采购")
    @PreAuthorize
    public JsonObject<Boolean> launch(@Valid @RequestBody ThreeProcurementReviewLaunchVo threeProcurementReviewLaunchVo) {
        ThreeProcurementReviewLaunchVo.ThreeProcurementBaseInfoLaunchDTO baseInfo = threeProcurementReviewLaunchVo.getBaseInfo();
        if (Objects.isNull(baseInfo)){
            throw new CrmException("参数不能为空");
        }
        String contractId = baseInfo.getContractId();
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        if (Objects.isNull(contractInfo)){
            throw new CrmException("合同信息不存在");
        }
        if(threeProcurementReviewLaunchVo.getBaseInfo().isReturnAndExchange()){
            String returnAndExchangeProcessId = threeProcurementReviewLaunchVo.getBaseInfo().getReturnAndExchangeProcessId();
            CrmAssert.hasText(returnAndExchangeProcessId, "退换货流程实例id不能为空");
            ContractReviewReturnExchange contractReviewReturnExchange = contractReviewReturnExchangeService.returnExchangeContractInfo(returnAndExchangeProcessId);
            if (contractReviewReturnExchange == null) {
                throw new CrmException("退换货流程不存在");
            }
            if (!Objects.equals(contractReviewReturnExchange.getCreateUser(), UserInfoHolder.getCurrentPersonId())){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }

        }else {
            if (!Objects.equals(contractInfo.getSaleId(), UserInfoHolder.getCurrentPersonId())){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }

        }

        return new JsonObject<>(threeProcurementReviewProcessService.launch(threeProcurementReviewLaunchVo));
    }


    @PostMapping("/pageThreeProcurementApprove")
    @Operation(summary = "分页查询第三方采购审批列表（包含放行）")
    @PreAuthorize(hasPermission = "crm_third_purchase", dataScope = "crm_third_purchase")
    public JsonObject<PageUtils<ThreeProcurementReviewMainVo>> pageThreeProcurementApprove(@RequestBody ThreeProcurementReviewQuery threeProcurementReviewQuery)
    {
        //设置采购放行人员数据
        List<ThreeProcurementRelease> threeProcurementReleaseList = threeProcurementReleaseService.queryThreeProcurementReleaseByPersonId(UserInfoHolder.getCurrentPersonId());
        if (CollectionUtils.isNotEmpty(threeProcurementReleaseList)){
            threeProcurementReviewQuery.setProcessNumbers(threeProcurementReleaseList.stream().map(ThreeProcurementRelease::getProcessNumber).toList());
        }
        Page<ThreeProcurementReviewMain> threeProcurementReviewMainPage = threeProcurementReviewMainService.pageThreeProcurementApprove(threeProcurementReviewQuery);
        IPage<ThreeProcurementReviewMainVo> threeProcurementReviewMainVoIPage = threeProcurementReviewMainService.convertThreeProcurementApprove(threeProcurementReviewMainPage);
        if (CollectionUtils.isNotEmpty(threeProcurementReviewMainVoIPage.getRecords())){
            List<String> processInstanceIds = threeProcurementReviewMainVoIPage.getRecords().stream().map(ThreeProcurementReviewMainVo::getProcessInstanceId).toList();
            Map<String, Set<ApproveNode>> stringSetMap = Optional.ofNullable(tfsNodeClient.queryNodeByProcessInstanceIdList(ListUtils.emptyIfNull(processInstanceIds)))
                    .map(JsonObject::getObjEntity).orElseThrow(() -> new RuntimeException("审批节点查询失败"));
            threeProcurementReviewMainVoIPage.getRecords().forEach(threeProcurementReviewMainVo -> {
                threeProcurementReviewMainVo.setApprovalNode(stringSetMap.get(threeProcurementReviewMainVo.getProcessInstanceId()));
            });
        }
        return new JsonObject<>(new PageUtils<>(threeProcurementReviewMainVoIPage));

    }


    @GetMapping("/contractBasicInfo")
    @Operation(summary = "合同评审基础信息")
    @PreAuthorize(hasPermission = "crm_third_purchase", dataScope = "crm_third_purchase")
    public JsonObject<ContractBasicInfoDTO> contractBasicInfo(@RequestParam String processInstanceId) {
        ThreeProcurementReviewMain threeProcurementReviewMain = threeProcurementReviewMainService.selectThreeProcurementReviewMainByProcessInstanceId(processInstanceId);
        if (Objects.isNull(threeProcurementReviewMain)){
            throw new CrmException("合同信息不存在");
        }
        findRelease(threeProcurementReviewMain);

        String contractId = threeProcurementReviewMain.getContractId();
        return new JsonObject<>(contractReviewMainService.contractBasicInfo(contractId, true));
    }

    private void findRelease(ThreeProcurementReviewMain threeProcurementReviewMain) {
        List<ThreeProcurementRelease> threeProcurementReleaseList = threeProcurementReleaseService.queryThreeProcurementReleaseByPersonId(UserInfoHolder.getCurrentPersonId());
        if (CollectionUtils.isNotEmpty(threeProcurementReleaseList)){
                List<String> strings = threeProcurementReleaseList.stream().map(ThreeProcurementRelease::getProcessNumber).toList();
                if (!strings.contains(threeProcurementReviewMain.getProcessNumber())){
                    PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementReviewMain.getCreateUser());
            }
        }else {
            PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementReviewMain.getCreateUser());
        }
    }


    @GetMapping("/pagePaymentProvisionByContractId")
    @Operation(summary = "根据流程实例id查询合同付款条款")
    @PreAuthorize(hasPermission = "crm_third_purchase", dataScope = "crm_third_purchase")
    public JsonObject<TableDataInfo> pagePaymentProvisionByContractId(@RequestParam String processInstanceId) {
        ThreeProcurementReviewMain threeProcurementReviewMain = threeProcurementReviewMainService.selectThreeProcurementReviewMainByProcessInstanceId(processInstanceId);
        if (Objects.isNull(threeProcurementReviewMain)){
            throw new CrmException("合同信息不存在");
        }
        findRelease(threeProcurementReviewMain);
        String contractId = threeProcurementReviewMain.getContractId();
        startPage();
        if (threeProcurementReviewMain.isReturnAndExchange()){
            List<ContractReviewPaymentProvision>  reviewPaymentProvisionList = contractReviewPaymentProvisionService.getByReturnExchangeProcessInstanceId(threeProcurementReviewMain.getReturnAndExchangeProcessId());
            List<PaymentProvisionDTO> result = HyperBeanUtils.copyListProperties(reviewPaymentProvisionList, PaymentProvisionDTO::new);
            TableDataInfo tableDataInfo = new TableDataInfo();
            NameUtils.setName(result);
            tableDataInfo.setList(result);
            tableDataInfo.setTotalCount(new PageInfo<>(reviewPaymentProvisionList).getTotal());
            return new JsonObject<>(tableDataInfo);
        }
        return new JsonObject<>(contractReviewPaymentProvisionService.pageByContractId(contractId));
    }



    /**
     * 获取【第三方采购】详情
     */
    @GetMapping(value = "/getThreeProcurementBaseInfo")
    @Operation(summary = "获取【第三方采购】详情")
    @PreAuthorize(hasPermission = "crm_third_purchase", dataScope = "crm_third_purchase")
    public JsonObject<ThreeProcurementReviewBaseInfoVo> getThreeProcurementBaseInfo(@RequestParam String processInstanceId)
    {
        ThreeProcurementReviewBaseInfoVo baseInfoVo = threeProcurementReviewMainService.selectThreeProcurementReviewBaseInfoVoByProcessInstanceId(processInstanceId);
        if (Objects.isNull(baseInfoVo)){
            throw new CrmException("第三方采购信息不存在");
        }
        ThreeProcurementReviewMainVo threeProcurementReviewMainVo = baseInfoVo.getThreeProcurementReviewMainVo();
        if (Objects.isNull(threeProcurementReviewMainVo)){
            throw new CrmException("第三方采购信息不存在");
        }
        ThreeProcurementReviewMain threeProcurementReviewMain = HyperBeanUtils.copyPropertiesByJackson(threeProcurementReviewMainVo, ThreeProcurementReviewMain.class);
        findRelease(threeProcurementReviewMain);
        return new JsonObject<>(baseInfoVo);
    }


    @PostMapping("/selectAttachmentList")
    @Operation(summary = "查询第三方采购附件列表")
    @PreAuthorize(hasPermission = "crm_third_purchase", dataScope = "crm_third_purchase")
    public JsonObject<List<ProcessAttachmentVo>> selectAttachmentList(@RequestParam String processInstanceId) {
        ThreeProcurementReviewMain threeProcurementReviewMain = threeProcurementReviewMainService.selectThreeProcurementReviewMainByProcessInstanceId(processInstanceId);
        if (Objects.isNull(threeProcurementReviewMain)){
            throw new CrmException("第三方采购信息不存在");
        }
        findRelease(threeProcurementReviewMain);
        List<ProcessAttachmentVo> listThreeProcurementAttachment = threeProcurementPaymentReviewMainService.getListThreeProcurementAttachment(threeProcurementReviewMain.getAttachmentIds());
        return new JsonObject<>(listThreeProcurementAttachment);
    }




    @GetMapping("/findSupplierByContractId")
    @Operation(summary = "根据合同id查询第三方产品的供应商 ")
    @PreAuthorize
    public JsonObject<List<SupplierVO>> selectSupplierByReturnExchangeId(@RequestParam String contractId){
        CrmAssert.hasText(contractId,"合同ID不能为空");
        checkAuth(contractId);
        return new JsonObject<>(threeProcurementProductService.selectSupplierByContractId(contractId));
    }


    @GetMapping("/querySupplierByReturnExchangeId")
    @Operation(summary = "根据退换货流程实例id查询第三方产品的供应商 ")
    @PreAuthorize
    public JsonObject<List<SupplierVO>> querySupplierByReturnExchangeId(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程实例ID不能为空");
        return new JsonObject<>(returnExchangeProductThirdService.selectSupplierByReturnExchangeId(processInstanceId));
    }

    @GetMapping("/queryThirdInfoByReturnExchangeId")
    @Operation(summary = "根据退换货流程实例id和供应商id查询第三方产品信息 ")
    @PreAuthorize
    public JsonObject<ThreeProcurementProductInfoVo> queryThirdInfoByReturnExchangeId(@RequestParam String processInstanceId,@RequestParam String supplierId){
        CrmAssert.hasText(processInstanceId,"流程实例ID不能为空");
        CrmAssert.hasText(supplierId,"供应商ID不能为空");
        ThreeProcurementProductInfoVo procurementProductInfoVo = threeProcurementReviewMainService.queryThirdProductInfo(processInstanceId, supplierId,true);
        return new JsonObject<>(procurementProductInfoVo);
    }



    @GetMapping("/queryThirdInfoByContractIdAndSupplier")
    @Operation(summary = "根据合同id和供应商id查询第三方产品信息 ")
    @PreAuthorize
    public JsonObject<ThreeProcurementProductInfoVo> queryThirdInfoByContractIdAndSupplier(@RequestParam String contractId,@RequestParam String supplierId){
        CrmAssert.hasText(contractId,"合同ID不能为空");
        CrmAssert.hasText(supplierId,"供应商ID不能为空");
        ThreeProcurementProductInfoVo procurementProductInfoVo = threeProcurementReviewMainService.queryThirdProductInfo(contractId, supplierId,false);
        return new JsonObject<>(procurementProductInfoVo);
    }






    private void checkAuth(String contractId) {
        Boolean auth = Optional.ofNullable(tbsResourceClient.rolesHaveResource(UserInfoHolder.getCurrentRoles().toArray(new String[0]), "crm_third_purchase_sm_todo"))
                .map(JsonObject::getObjEntity)
                .orElseThrow(() -> new CrmException("您没有权限"));
        if (!auth){
            ContractReviewMainBaseInfoDTO byContractId = contractReviewMainService.getByContractId(contractId);
            if (Objects.isNull(byContractId)){
                throw new CrmException("合同信息不存在");
            }
            if (!Objects.equals(byContractId.getSaleId(), UserInfoHolder.getCurrentPersonId())) {
                throw new CrmException("您没有权限");
            }
        }
    }

    @GetMapping("/contractSigningCompany")
    @Operation(summary = "第三方采购-查询合同中签订公司")
    @PreAuthorize
    public JsonObject<Map<String, Object>> contractSigningCompany(@RequestParam String contractId) {
        CrmAssert.hasText(contractId,"合同ID不能为空");
        Boolean smAuth = Optional.ofNullable(tbsResourceClient.rolesHaveResource(UserInfoHolder.getCurrentRoles().toArray(new String[0]), "crm_third_purchase_sm_todo"))
                .map(JsonObject::getObjEntity)
                .orElseThrow(() -> new CrmException("您没有权限"));

        Boolean productAuth = Optional.ofNullable(tbsResourceClient.rolesHaveResource(UserInfoHolder.getCurrentRoles().toArray(new String[0]), "crm_third_purchase_product_line_todo"))
                .map(JsonObject::getObjEntity)
                .orElseThrow(() -> new CrmException("您没有权限"));
        ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO = contractReviewMainService.getByContractId(contractId);
        if (!smAuth && !productAuth){
            if (Objects.isNull(contractReviewMainBaseInfoDTO)){
                throw new CrmException("合同信息不存在");
            }
            if (!Objects.equals(contractReviewMainBaseInfoDTO.getSaleId(), UserInfoHolder.getCurrentPersonId())) {
                throw new CrmException("您没有权限");
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("signingCompany", contractReviewMainBaseInfoDTO.getSigningCompany());
        map.put("signingCompanyName", contractReviewMainBaseInfoDTO.getSigningCompanyName());
        return new JsonObject<>(map);
    }






















}
