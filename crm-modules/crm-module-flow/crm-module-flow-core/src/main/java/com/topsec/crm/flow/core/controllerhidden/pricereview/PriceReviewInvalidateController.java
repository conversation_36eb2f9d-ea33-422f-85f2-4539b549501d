package com.topsec.crm.flow.core.controllerhidden.pricereview;

import com.topsec.crm.flow.api.dto.pricereview.ProductPriceState;
import com.topsec.crm.flow.core.service.PriceReviewMainService;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/priceReviewInvalidate")
@Tag(name = "价格评审失效", description = "/priceReviewInvalidate")
@RequiredArgsConstructor
public class PriceReviewInvalidateController {


    private final PriceReviewMainService priceReviewMainService;


    @Operation(summary = "查询产品记录价审状态")
    @GetMapping("/queryPriceState")
    public JsonObject<Map<String, ProductPriceState>> queryPriceState(@RequestParam String projectId, @RequestParam Set<String> productRecordIdSet) {
        return new JsonObject<>(priceReviewMainService.queryPriceState(projectId, productRecordIdSet));
    }


}
