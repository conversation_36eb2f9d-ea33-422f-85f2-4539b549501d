package com.topsec.crm.flow.core.controller.performancenegotiation;

import com.topsec.crm.flow.api.dto.performancenegotiation.PerformanceNegotiationBaseInfoDTO;
import com.topsec.crm.flow.api.dto.performancenegotiation.PerformanceNegotiationFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.process.impl.PerformanceNegotiationProcessService;
import com.topsec.crm.flow.core.service.PerformanceNegotiationService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/performanceNegotiation")
@Tag(name = "业绩协商", description = "/performanceNegotiation")
public class PerformanceNegotiationController extends BaseController {

    @Resource
    private PerformanceNegotiationService performanceNegotiationService;

    @Resource
    private PerformanceNegotiationProcessService negotiationProcessService;

    @Resource
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    @PostMapping("/launch")
    @Operation(summary = "提交业绩协商流程")
    @PreAuthorize(hasPermission = "crm_flow_performance_negotiation")
    public JsonObject<Boolean> launch(@Valid @RequestBody PerformanceNegotiationFlowLaunchDTO performanceNegotiationFlowLaunchDTO){
        if (!remoteProjectDirectlyClient.hasRight(performanceNegotiationFlowLaunchDTO.getBaseInfoDTO().getProjectId(), UserInfoHolder.getCurrentPersonId()).getObjEntity()) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(negotiationProcessService.launch(performanceNegotiationFlowLaunchDTO));
    }

    @GetMapping("/getLaunchInfo")
    @Operation(summary = "流程信息详情")
    @PreFlowPermission
    public JsonObject<PerformanceNegotiationBaseInfoDTO> getLaunchInfo(String processId){
        processId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processId, "流程实例id不能为空");
        return new JsonObject<>(performanceNegotiationService.getLaunchInfo(processId));
    }

    @GetMapping("/getLeaderInfoByPersonId")
    @Operation(summary = "根据人员获取负责人列表")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> getLeaderInfoByPersonId(String personId){
        return new JsonObject<>(performanceNegotiationService.getLeaderInfoByPersonId(personId));
    }

    @GetMapping("/getNegotiatedInfoList")
    @Operation(summary = "获取被协商人列表")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> getNegotiatedInfoList(String processId){
        processId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processId, "流程实例id不能为空");
        return new JsonObject<>(performanceNegotiationService.getNegotiatedInfoList(processId));
    }

    @GetMapping("/isExistProcessing")
    @Operation(summary = "项目是否存在正在走的业绩协商流程")
    @PreAuthorize(hasPermission = "crm_flow_performance_negotiation")
    public JsonObject<Boolean> isExistProcessing(String projectId){
        if (!remoteProjectDirectlyClient.hasRight(projectId, UserInfoHolder.getCurrentPersonId()).getObjEntity()) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(performanceNegotiationService.isExistProcessing(projectId));
    }
}
