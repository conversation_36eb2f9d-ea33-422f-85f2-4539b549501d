package com.topsec.crm.flow.core.controller.agentInvioce;

import com.topsec.crm.flow.api.dto.agentInvoice.*;
import com.topsec.crm.flow.core.entity.AgentInvoiceMain;
import com.topsec.crm.flow.core.service.AgentInvioceProductService;
import com.topsec.crm.flow.core.service.AgentInvoiceCompanyService;
import com.topsec.crm.flow.core.service.AgentInvoiceContactService;
import com.topsec.crm.flow.core.service.AgentInvoiceMainService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/business/agentInvioce")
@Tag(name = "总代开票-业务")
@RequiredArgsConstructor
public class AgentInvoiceMainBusinessController extends BaseController {

    private final AgentInvoiceCompanyService agentInvoiceCompanyService;

    private final AgentInvoiceContactService agentInvoiceContactService;

    private final AgentInvoiceMainService agentInvoiceMainService;

    private final AgentInvioceProductService agentInvioceProductService;

    @PostMapping("/page")
    @Operation(summary = "总代开票列表")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice", dataScope = "crm_dynasty_invoice")
    public JsonObject<PageUtils<AgentInvoiceMainDTO>> page(@RequestBody AgentInvoiceQuery query){
        return new JsonObject<>(agentInvoiceMainService.agentInvoicePage(query));
    }

    @GetMapping("/getAgentInvoiceDetailByProcessInstanceId")
    @Operation(summary = "根据总代开票实例ID查询流程详细信息")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice", dataScope = "crm_dynasty_invoice")
    public JsonObject<AgentInvoiceLaunchDTO> getAgentInvoiceDetailByProcessInstanceId(String processInstanceId) {
        AgentInvoiceMain infoByProcessInstanceId = agentInvoiceMainService.getInfoByProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList()) || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(infoByProcessInstanceId.getCreateUser()) || infoByProcessInstanceId.getSupplierId().equals(getCurrentAgentId())){
            return new JsonObject<>(agentInvoiceMainService.getAgentInvoiceDetailByProcessInstanceId(processInstanceId));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }
    @PostMapping("/exportAgentInvoice")
    @Operation(summary = "导出总代开票", method = "POST")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice", dataScope = "crm_dynasty_invoice")
    public void exportAgentInvoice(@RequestBody AgentInvoiceQuery query) throws Exception {
        PageUtils<AgentInvoiceMainDTO> agentInvoiceMainDTOPageUtils = agentInvoiceMainService.agentInvoicePage(query);
        List<AgentInvoiceMainDTO> list = agentInvoiceMainDTOPageUtils.getList();
        ExcelUtil<AgentInvoiceMainDTO> excelUtil = new ExcelUtil<>(AgentInvoiceMainDTO.class);
        excelUtil.exportExcel(response, list,"总代开票");
    }

    @GetMapping("/delete")
    @Operation(summary = "总代开票删除")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice", dataScope = "crm_dynasty_invoice")
    public JsonObject<Boolean> delete(@RequestParam String id) {
        CrmAssert.hasText(id, "Id不能为空");
        AgentInvoiceMain byId = agentInvoiceMainService.getById(id);
        if (byId == null) {
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }else {
            if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList()) || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(byId.getCreateUser())){
                return new JsonObject<>(agentInvoiceMainService.delete(id));
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }
    }
    @PostMapping("/selectPerformanceReportList")
    @Operation(summary = "可发起总代开票的业绩上报")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice", dataScope = "crm_dynasty_invoice")
    public JsonObject<PageUtils<AgentInvioceInfoDTO>> selectPerformanceReportList(@RequestBody AgentInvioceInfoQuery query){
        List<AgentInvioceInfoDTO> list = agentInvoiceMainService.selectPerformanceReportList(query);
        return new JsonObject<>(PageUtils.paginate(list, query.getPageSize(), query.getPageNum()));
    }
    @PostMapping("/agentInvoiceCompanySaveOrUpdate")
    @Operation(summary = "总代开票公司新增或更新", description = "总代开票公司新增或更新")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice", dataScope = "crm_dynasty_invoice")
    public JsonObject<Boolean> agentInvoiceCompanySaveOrUpdate(@RequestBody AgentInvoiceCompanyDTO dto) {
        return new JsonObject<>(agentInvoiceCompanyService.saveOrUpdate(dto));
    }

    @PostMapping("/importAgentInvoiceMain")
    @Operation(summary = "导入总代开票")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice_import")
    public JsonObject<Map<String,String>> importAgentInvoiceMain(@RequestParam("files") List<MultipartFile> files) throws Exception {
        if(files == null || files.isEmpty()){
            throw new CrmException("参数异常", ResultEnum.FAIL.getResult());
        }
        return new JsonObject<>(agentInvoiceMainService.importAgentInvoiceMain(files));
    }

    @GetMapping("/getAgentInvoiceCompanysByCurrentPersonId")
    @Operation(summary = "根据当前登录人ID查询所有添加的总代开票公司", description = "根据当前登录人ID查询所有添加的总代开票公司")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice", dataScope = "crm_dynasty_invoice")
    public JsonObject<List<AgentInvoiceCompanyDTO>> getAgentInvoiceCompanysByCurrentPersonId(){
        return new JsonObject<>(agentInvoiceCompanyService.getAgentInvoiceCompanysByCurrentPersonId(getCurrentPersonId()));
    }

    @PostMapping("/agentInvoiceContactSaveOrUpdate")
    @Operation(summary = "发票接收人新增或更新", description = "发票接收人新增或更新")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice", dataScope = "crm_dynasty_invoice")
    public JsonObject<Boolean> agentInvoiceContactSaveOrUpdate(@RequestBody AgentInvoiceContactDTO dto) {
        return new JsonObject<>(agentInvoiceContactService.saveOrUpdate(dto));
    }

    @GetMapping("/getAgentInvoiceContactsByCurrentPersonId")
    @Operation(summary = "根据总代开票公司ID查询当前登录人所有添加的发票接收人", description = "根据总代开票公司ID查询当前登录人所有添加的发票接收人")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice", dataScope = "crm_dynasty_invoice")
    public JsonObject<List<AgentInvoiceContactDTO>> getAgentInvoiceContactsByCurrentPersonId(@RequestParam String agentInvoicingCompanyId){
        return new JsonObject<>(agentInvoiceContactService.getAgentInvoiceContactsByCurrentPersonId(agentInvoicingCompanyId, getCurrentPersonId()));
    }

    @GetMapping("/selectPerformanceReportByProcessInstanceIds")
    @Operation(summary = "根据选择的业绩上报流程ID查询信息")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice", dataScope = "crm_dynasty_invoice")
    public JsonObject<List<AgentInvioceInfoDTO>> selectPerformanceReportByProcessInstanceIds(@RequestParam List<String> performanceReportProcessInstanceIds){
        return new JsonObject<>(agentInvoiceMainService.selectPerformanceReportByProcessInstanceIds(performanceReportProcessInstanceIds));
    }

    @PostMapping("/pageAgentInvioceProductInfo")
    @Operation(summary = "选择产品", method = "POST")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice", dataScope = "crm_dynasty_invoice")
    public JsonObject<PageUtils<AgentInvioceProductDTO>> pageAgentInvioceProductInfo(@RequestBody AgentInvioceProductQuery query) {
        CrmAssert.hasText(query.getProcessNumber(), "业绩上报单号不能传空");
        List<AgentInvioceProductDTO> agentInvioceProductDTOs = agentInvioceProductService.selectAgentInvioceProductList(query);
        PageUtils<AgentInvioceProductDTO> page = PageUtils.paginate(agentInvioceProductDTOs, query.getPageSize(), query.getPageNum());
        return new JsonObject<>(page);
    }

    @GetMapping("/downloadTemplate")
    @Operation(summary = "下载总代开票导入模板")
    public ResponseEntity<Resource> downloadTemplate(){
        try {
            // 从resources目录中读取Excel文件
            Resource resource = new ClassPathResource("excel/AgentInvoiceImport.xlsx");

            // 设置文件名（包含中文）
            String fileName = "总代开票导入模板.xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFileName + "\"");

            // 返回文件下载响应
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);

        } catch (Exception e) {
            throw new CrmException("下载模板失败");
        }
    }
}
