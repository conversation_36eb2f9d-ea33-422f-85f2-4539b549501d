package com.topsec.crm.flow.core.controller.adborrowforprobation;


import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationDeliveryDetailDTO;
import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationDeliveryDetailVO;
import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationProductSnDTO;
import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationProductSnVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.AdBorrowForProbation;
import com.topsec.crm.flow.core.entity.AdBorrowForProbationDelivery;
import com.topsec.crm.flow.core.entity.AdBorrowForProbationDeliveryDetail;
import com.topsec.crm.flow.core.entity.AdBorrowForProbationProductSn;
import com.topsec.crm.flow.core.service.AdBorrowForProbationDeliveryDetailService;
import com.topsec.crm.flow.core.service.AdBorrowForProbationDeliveryService;
import com.topsec.crm.flow.core.service.AdBorrowForProbationProductSnService;
import com.topsec.crm.flow.core.service.AdBorrowForProbationService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.product.api.RemoteProductFlowRelService;
import com.topsec.logistics.api.client.RemoteLogisticsClient;
import com.topsec.logistics.api.domain.BatchSubParam;
import com.topsec.logistics.api.domain.LogisticsCompany;
import com.topsec.logistics.api.domain.LogisticsInfo;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 样机借试用产品序列号信息
 */
@RestController
@RequestMapping("/adBorrowForProbationProductSn")
@Tag(description = "/adBorrowForProbationProductSn", name = "样机借试用产品序列号")
public class AdBorrowForProbationProductSnController extends BaseController {

    @Resource
    private AdBorrowForProbationProductSnService adBorrowForProbationProductSnService;

    @Resource
    private AdBorrowForProbationDeliveryService adBorrowForProbationDeliveryService;

    @Resource
    private AdBorrowForProbationDeliveryDetailService adBorrowForProbationDeliveryDetailService;

    @Resource
    private AdBorrowForProbationService adBorrowForProbationService;

    @Resource
    private RemoteLogisticsClient remoteLogisticsClient;

    @Resource
    private RemoteProductFlowRelService remoteProductFlowRelService;

    @Data
    public static class LogisticsInfoVO{
        @Schema(description = "物流信息")
        private LogisticsInfo logisticsInfo;
        @Schema(description = "物流基本信息:收件人收货人等")
        private AdBorrowForProbationDeliveryDetailVO detailVO;
    }

    @PostMapping("/flow/selectSnVOListByProcessId/{processInstanceId}")
    @Operation(summary = "样机借试用流程里样机序列号相关信息查询")
    @PreFlowPermission
    public JsonObject<List<AdBorrowForProbationProductSnVO>> selectSnVOListByProcessId(@PathVariable String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(adBorrowForProbationProductSnService.selectSnVOListByProcessId(processInstanceId));
    }

    @PostMapping("/flow/pageByDeliveryId/{deliveryId}")
    @Operation(summary = "根据发货ID查询发货样机序列号相关信息列表")
    @PreFlowPermission
    public JsonObject<List<AdBorrowForProbationProductSnVO>> pageByDeliveryId(@PathVariable String deliveryId) {
        checkPermission(deliveryId, null, null);
        return new JsonObject<>(adBorrowForProbationProductSnService.selectSnListByDeliveryId(deliveryId));
    }

//    @PostMapping("/flow/updateOrDeleteProductSns")
//    @Operation(summary = "修改或删除样机借试用产品序列号信息list")
//    @PreFlowPermission
//    public JsonObject<Boolean> updateOrDeleteProductSns(@RequestBody List<AdBorrowForProbationProductSnDTO> snListDTO) {
//        if(CollectionUtils.isNotEmpty(snListDTO)){
//            List<String> sns = snListDTO.stream().map(sn -> sn.getSn()).filter(StringUtils::isNotBlank).collect(Collectors.toList());
//            if(sns != null && sns.size()>0){
//                JsonObject<Map<String, Boolean>> mapJsonObject = remoteProductFlowRelService.querySnCanBorrow(sns);
//                if (mapJsonObject.isSuccess()) {
//                    String zdId = adBorrowForProbationProductSnService.selectZdIdBySnId(snListDTO.get(0).getId());
//                    // 不在库
//                    Assert.isFalse(mapJsonObject.getObjEntity().containsValue(false),"该总代下有序列号已不在库，请在新增样机页面重新保存获取序列号数据。");
//                    // 在库但被占用
//                    List<String> occupyList = adBorrowForProbationService.selectSnOccupyList(zdId);
//                    occupyList.retainAll(sns);
//                    Assert.isFalse(occupyList.size() > 0,"该总代下已存在该序列号的借试用记录，请在新增样机页面重新保存获取序列号数据。");
//                }
//            }
//        }
//        return new JsonObject<>(adBorrowForProbationProductSnService.updateOrDeleteProductSns(snListDTO));
//    }
//
//    @PostMapping("/flow/insertOrDeleteDDInfos")
//    @Operation(summary = "新增或删除样机借试用发货信息list")
//    @PreFlowPermission
//    public JsonObject<Boolean> insertOrDeleteDDInfos(@RequestBody List<AdBorrowForProbationDeliveryDetailDTO> ddListDTO) {
//        return new JsonObject<>(adBorrowForProbationDeliveryDetailService.insertOrDeleteDDInfos(ddListDTO));
//    }

    @PostMapping("/flow/updateProductSn")
    @Operation(summary = "修改样机借试用产品序列号")
    @PreFlowPermission
    public JsonObject<Boolean> updateAdProductSn(@RequestBody AdBorrowForProbationProductSnDTO snDTO) {
        if(StringUtils.isEmpty(snDTO.getId()) || StringUtils.isEmpty(snDTO.getSn())){
            throw new CrmException("主键和新序列号不能为空!");
        }
        checkPermission(null, null, snDTO.getId());
        List<String> snList = new ArrayList<>();
        snList.add(snDTO.getSn());
        JsonObject<Map<String, Boolean>> mapJsonObject = remoteProductFlowRelService.querySnCanBorrow(snList);
        if (mapJsonObject.isSuccess()) {
            String zdId = adBorrowForProbationProductSnService.selectZdIdBySnId(snDTO.getId());
            // 不在库
            Assert.isFalse(mapJsonObject.getObjEntity().containsValue(false),"该总代下有序列号已不在库，请在新增样机页面重新保存获取序列号数据。");
            // 在库但被占用
            List<String> occupyList = adBorrowForProbationService.selectSnOccupyList(zdId);
            occupyList.retainAll(snList);
            Assert.isFalse(occupyList.size() > 0,"该总代下已存在该序列号的借试用记录，请在新增样机页面重新保存获取序列号数据。");
        }
        return new JsonObject<>(adBorrowForProbationProductSnService.updateAdProductSn(snDTO));
    }

    @GetMapping("/flow/deleteAdProductSn")
    @Operation(summary = "逻辑删除样机借试用产品序列号")
    @PreFlowPermission
    public JsonObject<Boolean> deleteAdProductSn(@RequestParam String id) {
        checkPermission(null, null, id);
        return new JsonObject<>(adBorrowForProbationProductSnService.deleteAdProductSn(id));
    }

    @PostMapping("/flow/saveSnDeliveryDetail")
    @Operation(summary = "新增样机借试用产品序列号的发货单信息")
    @PreFlowPermission
    public JsonObject<String> saveSnDeliveryDetail(@RequestBody AdBorrowForProbationDeliveryDetailDTO detailDTO) {
        if(CollectionUtils.isEmpty(detailDTO.getSnIds()) || StringUtils.isEmpty(detailDTO.getAdDeliveryId())){
            throw new CrmException("发货信息ID、序列号信息不能为空!");
        }
        checkPermission(detailDTO.getAdDeliveryId(), null, null);
        return new JsonObject<>(adBorrowForProbationDeliveryDetailService.saveSnDeliveryDetail(detailDTO));
    }

    @PostMapping("/flow/selectDetailsByDDId/{ddId}")
    @Operation(summary = "根据发货详情ID查询发货样机序列号相关信息")
    @PreFlowPermission
    public JsonObject<LogisticsInfoVO> selectDetailsByDDId(@PathVariable String ddId) throws Exception {
        AdBorrowForProbationDeliveryDetailVO detailVO = adBorrowForProbationDeliveryDetailService.selectDetailsByDDId(ddId);
        if (detailVO==null) return JsonObject.errorT("物流信息不存在");
        checkPermission(detailVO.getAdDeliveryId(), null, null);
        BatchSubParam batchSubParam=new BatchSubParam();
        batchSubParam.setCompany(detailVO.getCompanyName());
        batchSubParam.setKdybCom(detailVO.getCompanyCode());
        batchSubParam.setNumber(detailVO.getDeliveryNo());
        JsonObject<LogisticsInfo> info = remoteLogisticsClient.getLogisticsInfo(batchSubParam);

        LogisticsInfoVO logisticsInfoVO=new LogisticsInfoVO();
        if (info.isSuccess()){
            logisticsInfoVO.setLogisticsInfo(info.getObjEntity());
        }
        logisticsInfoVO.setDetailVO(detailVO);
        return new JsonObject<>(logisticsInfoVO);
    }

    @GetMapping({"/flow/notifyCompany"})
    @Operation(summary = "预测物流公司")
    @PreAuthorize(hasAnyPermission = {"crm_agent_rebate_pool","crm_sample_probation","crm_project_directly","crm_project_dynasty","crm_project_agent"})
    JsonObject<List<LogisticsCompany>> notifyCompany(@RequestParam String lNum){
        return remoteLogisticsClient.notifyCompany(lNum);
    }

    @GetMapping({"/flow/getLogisticsInfo"})
    @Operation(summary = "根据运单号获取运单的基本信息")
    @PreAuthorize(hasAnyPermission = {"crm_agent_rebate_pool","crm_sample_probation","crm_project_directly","crm_project_dynasty","crm_project_agent"})
    JsonObject<LogisticsInfo> getLogisticsInfo(@RequestParam String lNum, @RequestParam String companyName) throws Exception {
        BatchSubParam param = new BatchSubParam();
        param.setNumber(lNum);
        param.setCompany(companyName);
        return remoteLogisticsClient.getLogisticsInfo(param);
    }

    @GetMapping("/flow/deleteDDInfo")
    @Operation(summary = "逻辑删除发货详情及相关数据")
    @PreFlowPermission
    public JsonObject<Boolean> deleteDDInfo(@RequestParam String ddId) {
        checkPermission(null, ddId, null);
        return new JsonObject<>(adBorrowForProbationDeliveryDetailService.deleteDDInfo(ddId));
    }

    @GetMapping("/flow/selectAddSnVOList")
    @Operation(summary = "获取新增发货详情里的查询序列号list")
    @PreFlowPermission
    public JsonObject<List<AdBorrowForProbationProductSnVO>> selectAddSnVOList(@RequestParam String deliveryId) {
        checkPermission(deliveryId, null, null);
        return new JsonObject<>(adBorrowForProbationProductSnService.selectAddSnVOList(deliveryId));
    }

    private void checkPermission(String deliveryId, String ddId, String snId) {
        String adBorrowId = "";
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        if(deliveryId != null){
            AdBorrowForProbationDelivery delivery = adBorrowForProbationDeliveryService.getById(deliveryId);
            if(delivery == null) throw new CrmException("记录不存在");
            adBorrowId = delivery.getAdBorrowId();
        }else if(ddId != null){
            AdBorrowForProbationDeliveryDetail dd = adBorrowForProbationDeliveryDetailService.getById(ddId);
            if(dd == null) throw new CrmException("记录不存在");
            adBorrowId = dd.getAdBorrowId();
        }else if(snId != null){
            AdBorrowForProbationProductSn sn = adBorrowForProbationProductSnService.getById(snId);
            if(sn == null) throw new CrmException("记录不存在");
            adBorrowId = sn.getAdBorrowId();
        }else{
            throw new CrmException("参数不能全为空");
        }
        AdBorrowForProbation adEntity = adBorrowForProbationService.getAdEntityByProcessInstanceId(processInstanceId);
        if (adEntity == null || !adBorrowId.equals(adEntity.getId())) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

}
