package com.topsec.crm.flow.core.controller.contractoriginal;


import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractOriginalDocumentService;
import com.topsec.crm.contract.api.entity.CrmContractProductOwnVO;
import com.topsec.crm.contract.api.entity.CrmContractProductThirdVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractProcessInfoVO;
import com.topsec.crm.contract.api.entity.originaldocument.OriginalDocumentVO;
import com.topsec.crm.contract.api.entity.request.OriginalDocumentQuery;
import com.topsec.crm.flow.api.dto.contractoriginal.*;
import com.topsec.crm.flow.api.dto.contractoriginal.vo.ContractDocVO;
import com.topsec.crm.flow.api.dto.contractoriginal.vo.ContractOriginalDocInfoVO;
import com.topsec.crm.flow.api.dto.contractreview.fileinfo.ContractReviewAttachmentDTO;
import com.topsec.crm.flow.core.process.impl.ContractOriginalDocumentProcessService;
import com.topsec.crm.flow.core.service.ContractOriginalDocumentService;
import com.topsec.crm.framework.common.util.PageIterator;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyPriceStatisticsVO;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ForkJoinPool;
import java.util.function.BiFunction;

/**
 * <p>
 * 合同原件表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@RestController
@RequestMapping("/business/original")
@Tag(name = "合同原件-业务接口", description = "/business/original")
@RequiredArgsConstructor
@Validated
public class ContractOriginalDocumentBusinessController extends BaseController {

    @Resource
    private ContractOriginalDocumentProcessService processService;

    @Resource
    private ContractOriginalDocumentService contractOriginalDocumentService;

    @Resource
    private RemoteContractOriginalDocumentService service;

    @Resource
    private RemoteContractExecuteService reviewService;

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @PostMapping("/page")
    @Operation(summary = "分页查询合同原件列表", method = "POST")
    public JsonObject<PageUtils<OriginalDocumentVO>> pages(@RequestBody OriginalDocumentQuery query) {
        startPage();
        //水平权限校验
        if (CollectionUtils.isNotEmpty(query.getContractOwnerIds())){
            List<String> contractOwnerIds = query.getContractOwnerIds().stream().filter(s -> {
                if (CollectionUtils.isNotEmpty(PreAuthorizeAspect.getPermissionPersonIds())&&!PreAuthorizeAspect.getPermissionPersonIds().contains(s)){
                    return false;
                }
                return true;
            }).toList();
            if (CollectionUtils.isNotEmpty(contractOwnerIds)){
                query.setContractOwnerIds(contractOwnerIds);
            }else {
                return new JsonObject<>(new PageUtils<>());
            }
        }else {
            query.setContractOwnerIds(PreAuthorizeAspect.getPermissionPersonIds().stream().toList());
        }
        return service.pages(query);
    }


    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @GetMapping("/selectContractInfoById")
    @Operation(summary = "合同原件详情-合同明细", method = "GET")
    public JsonObject<CrmContractProcessInfoVO> selectContractInfoById(@RequestParam String contractReviewMainId) {
        JsonObject<CrmContractProcessInfoVO> jsonObject = service.selectContractInfoByReviewMainId(contractReviewMainId);
        if (jsonObject.getObjEntity() != null) {
            PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), jsonObject.getObjEntity().getContractOwnerId());
        }
        return jsonObject;
    }

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @GetMapping("/listDocument")
    @Operation(summary = "合同原件详情-合同原件明细", method = "GET")
    public JsonObject<List<OriginalDocumentVO>> listDocument(@RequestParam String contractReviewMainId) {
        horizontalPermissionVerification(null, contractReviewMainId);
        return service.listDocument(contractReviewMainId);
    }

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @GetMapping("/listDocumentByContractNumber")
    @Operation(summary = "合同原件详情-合同原件列表(带合同原件信息)", method = "GET")
    public JsonObject<List<ContractDocVO>> listDocumentByContractNumber(@RequestParam String contractNumber) {
        horizontalPermissionVerification(contractNumber, null);
        return new JsonObject<>(contractOriginalDocumentService.listDocumentByContractNumber(contractNumber));
    }

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @GetMapping("/queryFinalAttachment")
    @Operation(summary = "合同原件详情-合同终稿", method = "GET")
    public JsonObject<ContractReviewAttachmentDTO> queryFinalAttachment(@RequestParam String contractReviewMainId) {
        horizontalPermissionVerification(null, contractReviewMainId);
        return service.queryFinalAttachment(contractReviewMainId);
    }

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @GetMapping("/getContractPriceStatistics")
    @Operation(summary = "合同原件详情-产品明细", method = "POST")
    public JsonObject<CrmProjectDirectlyPriceStatisticsVO> getContractPriceStatistics(@RequestParam String contractNumber) {
        horizontalPermissionVerification(contractNumber, null);
        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(service.getContractPriceStatistics(contractNumber).getObjEntity(), CrmProjectDirectlyPriceStatisticsVO.class));
    }

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @PostMapping("/launch")
    @Operation(summary = "上交合同原件——发起审批")
    public JsonObject<Boolean> launch(@RequestBody ContractOriginalFlowLaunchDTO contractOriginalFlowLaunchDTO) {
        return new JsonObject<>(processService.launch(contractOriginalFlowLaunchDTO));
    }

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @PostMapping(value = "/checkOriginal")
    @Operation(summary = "批量上传合同原件--是否有二次校验提示")
    public JsonObject<BatchAttachmentInDTO> checkOriginal(@RequestBody List<BatchBaseInfoDTO> costContractAttachmentInfoDTOS) {
        return new JsonObject<>(contractOriginalDocumentService.checkOriginals(costContractAttachmentInfoDTOS));
    }

    @PreAuthorize(hasPermission = "crm_contract_original_batch_upload")
    @PostMapping(value = "/bacthSubmit")
    @Operation(summary = "批量上传合同原件-确认提交")
    public JsonObject<Boolean> bacthSubmit(@RequestBody List<BatchBaseInfoDTO> costContractAttachmentInfoDTOS) {
        return new JsonObject<>(contractOriginalDocumentService.bacthSubmit(costContractAttachmentInfoDTOS));
    }

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @PostMapping("/export")
    @Operation(summary = "导出合同原件列表", method = "POST")
    public void export(@RequestBody OriginalDocumentQuery query) throws Exception {
        //水平权限校验
        if (CollectionUtils.isNotEmpty(query.getContractOwnerIds())){
            List<String> contractOwnerIds = query.getContractOwnerIds().stream().filter(s -> {
                if (CollectionUtils.isNotEmpty(PreAuthorizeAspect.getPermissionPersonIds())&&!PreAuthorizeAspect.getPermissionPersonIds().contains(s)){
                    return false;
                }
                return true;
            }).toList();
            if (CollectionUtils.isNotEmpty(contractOwnerIds)){
                query.setContractOwnerIds(contractOwnerIds);
            }else {
                ExcelUtil<OriginalDocumentVO> excelUtil = new ExcelUtil<>(OriginalDocumentVO.class);
                excelUtil.exportExcel(response, new ArrayList<>(), "合同原件");
            }
        }else {
            query.setContractOwnerIds(PreAuthorizeAspect.getPermissionPersonIds().stream().toList());
        }
        BiFunction<Integer, Integer, PageUtils<OriginalDocumentVO>> call = (pageNo, pageSize) -> {
            OriginalDocumentQuery queryNew = HyperBeanUtils.copyPropertiesByJackson(query, OriginalDocumentQuery.class);
            queryNew.setPageNum(pageNo);
            queryNew.setPageSize(pageSize);
            JsonObject<PageUtils<OriginalDocumentVO>> pageUtilsJsonObject = service.pages(queryNew);
            return Optional.ofNullable(pageUtilsJsonObject).map(JsonObject::getObjEntity).orElse(null);
        };
        List<OriginalDocumentVO> contentList = PageIterator.iteratePageToList(1000, call, true, new ForkJoinPool(50));
        ExcelUtil<OriginalDocumentVO> excelUtil = new ExcelUtil<>(OriginalDocumentVO.class);
        excelUtil.exportExcel(response, contentList, "合同原件");
    }

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @PostMapping("/pageOwnProductByContractNumber")
    @Operation(summary = "合同原件详情-产品明细询-自有产品", method = "POST")
    public JsonObject<PageUtils<CrmContractProductOwnVO>> pageOwnProductByContractNumber(@RequestBody OriginalPageQuery query, @Schema(hidden = true) HttpServletRequest httpServletRequest) {
        httpServletRequest.setAttribute("pageNum", query.getPageNum());
        httpServletRequest.setAttribute("pageSize", query.getPageSize());
        horizontalPermissionVerification(query.getContractNumber(), null);
        return reviewService.pageOwnProductByContractNumber(query.getContractNumber());
    }

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @PostMapping("/pageThirdProductByContractNumber")
    @Operation(summary = "合同原件详情-产品明细询-第三方产品", method = "POST")
    public JsonObject<PageUtils<CrmContractProductThirdVO>> pageThirdProductByContractNumber(@RequestBody OriginalPageQuery query, @Schema(hidden = true) HttpServletRequest httpServletRequest) {
        httpServletRequest.setAttribute("pageNum", query.getPageNum());
        httpServletRequest.setAttribute("pageSize", query.getPageSize());
        horizontalPermissionVerification(query.getContractNumber(), null);
        return reviewService.pageThirdProductByContractNumber(query.getContractNumber());
    }

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @GetMapping(value = "/checkIsOriginal")
    @Operation(summary = "合同原件详情--上交原件--是否有二次校验提示")
    public JsonObject<CheckAttachmentInDTO> checkIsOriginal(@RequestParam String contractNumber) {
        horizontalPermissionVerification(contractNumber, null);
        return new JsonObject<>(contractOriginalDocumentService.checkIsOriginal(contractNumber));
    }

    @PreAuthorize(hasPermission = "crm_contract_original", dataScope = "crm_contract_original")
    @GetMapping("/getByOriginalId")
    @Operation(summary = "上交合同原件流程审批列表——查看详情")
    public JsonObject<ContractOriginalDocInfoVO> getByOriginalId(@RequestParam String originalId) {
        ContractOriginalDocInfoVO contractOriginalDocInfoVO = contractOriginalDocumentService.getByOriginalId(originalId);
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), contractOriginalDocInfoVO.getContractInfoVO().getContractOwnerId());
        return new JsonObject<>(contractOriginalDocInfoVO);
    }

    private void horizontalPermissionVerification(String contractNumber, String contractReviewMainId) {
        Set<String> personList = PreAuthorizeAspect.getPermissionPersonIds();
        if (CollectionUtils.isEmpty(personList)) {
            return;
        }
        if (StringUtils.isNotEmpty(contractNumber)) {
            Optional.ofNullable(reviewService.getByContractNumber(contractNumber).getObjEntity()).ifPresent(crmContractExecuteVO -> {
                PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), crmContractExecuteVO.getContractOwnerId());
            });
        } else if (StringUtils.isNotEmpty(contractReviewMainId)) {
            Optional.ofNullable(service.selectContractInfoByReviewMainId(contractReviewMainId).getObjEntity()).ifPresent(crmContractExecuteVO -> {
                PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), crmContractExecuteVO.getContractOwnerId());
            });
        }
    }


}

