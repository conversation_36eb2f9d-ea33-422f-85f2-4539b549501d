package com.topsec.crm.flow.core.controller.flow;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.api.dto.flow.DraftsAble;
import com.topsec.crm.flow.api.dto.flow.FlowDraftsDTO;
import com.topsec.crm.flow.core.entity.FlowDrafts;
import com.topsec.crm.flow.core.service.FlowDraftsService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 草稿箱 controller
 *
 * <AUTHOR>
 * @date 2024/8/1 14:12
 */
@RestController
@RequestMapping("/drafts")
@Tag(name = "流程草稿箱", description = "/drafts")
@RequiredArgsConstructor
@Validated
public class FlowDraftsController extends BaseController {

    private final FlowDraftsService flowDraftsService;

    @GetMapping("/queryDraftsDetail")
    @Operation(summary = "查询草稿")
    @PreAuthorize
    public JsonObject<DraftsAble> queryDraftsDetail(@RequestParam String id) {
        checkDraftId(id);
        return new JsonObject<>(flowDraftsService.getById(id));
    }

    @GetMapping("/deleteDrafts")
    @Operation(summary = "删除草稿")
    @PreAuthorize
    public JsonObject<Boolean> deleteDrafts(@RequestParam String id) {
        checkDraftId(id);
        return new JsonObject<>(flowDraftsService.deleteById(id));
    }

    @PostMapping("/queryDraftsList")
    @Operation(summary = "分页查询当前登陆人的指定流程的草稿，若不指定则查全部")
    @PreAuthorize
    public JsonObject<TableDataInfo> queryDrafts(@RequestBody FlowDraftsDTO flowDraftsDTO) {
        startPage();
        return new JsonObject<>(flowDraftsService.queryDraftsList(flowDraftsDTO));
    }

    @GetMapping("/checkDraftsValid")
    @Operation(summary = "检查草稿是否合法")
    @PreAuthorize
    public JsonObject<Boolean> checkDraftsValid(@RequestBody FlowDraftsDTO flowDraftsDTO) {
        return new JsonObject<>(flowDraftsService.checkDraftsValid(flowDraftsDTO));
    }

    private void checkDraftId(String draftId){
        // 查这个草稿是不是自己的
        FlowDrafts flowDrafts = flowDraftsService.getOne(new LambdaQueryWrapper<FlowDrafts>().eq(FlowDrafts::getId, draftId).eq(FlowDrafts::getDelFlag, 0).last("limit 1"));
        String createUser = flowDrafts.getCreateUser();
        if (!Objects.equals(createUser, UserInfoHolder.getCurrentPersonId())){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

}
