package com.topsec.crm.flow.core.controller.agentRegistration;

import com.alibaba.fastjson.JSONObject;
import com.topsec.crm.flow.api.dto.agentRegistration.AgentRegistrationFlowLaunchDTO;
import com.topsec.crm.flow.core.entity.AgentRegistration;
import com.topsec.crm.flow.core.service.AgentRegistrationService;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.MD5Util;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.tyc.api.RemoteTycSelectService;
import com.topsec.crm.tyc.api.vo.CrmCustomerBussinessInfoDTO;
import com.topsec.tac.common.enums.AuthErrorEnum;
import com.topsec.tbsapi.client.TbsAccountClient;
import com.topsec.tbsapi.client.TbsPersonClient;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping("/business/agentRegistration")
@Tag(name = "渠道注册-业务")
@RequiredArgsConstructor
public class AgentRegistrationBusinessController extends BaseController {

    private final AgentRegistrationService registrationService;

    @Value("${tyc.certifiedToken}")
    private String certifiedToken;

    private final RemoteTycSelectService remoteTycSelectService;

    private final TbsPersonClient tbsPersonClient;

    private final TbsAccountClient tbsAccountClient;

    private final ResourceLoader resourceLoader;

    @PostMapping("/page")
    @Operation(summary = "渠道注册列表查询")
    @PreAuthorize(hasPermission = "crm_agent_registration", dataScope = "crm_agent_registration")
    public JsonObject<TableDataInfo> page(@RequestBody AgentRegistrationFlowLaunchDTO registrationDTO) {
        startPage();
        TableDataInfo tableDataInfo = registrationService.page(registrationDTO, PreAuthorizeAspect.getDataScopeParam());
        return new JsonObject<>(tableDataInfo);
    }

    @GetMapping("/getLaunchInfo")
    @Operation(summary = "渠道注册基础信息")
    @PreAuthorize(hasPermission = "crm_agent_registration", dataScope = "crm_agent_registration")
    public JsonObject<AgentRegistrationFlowLaunchDTO> getLaunchInfo(@RequestParam String processId) {
        CrmAssert.hasText(processId, "流程实例id不能为空");
        AgentRegistrationFlowLaunchDTO launchInfo = registrationService.getLaunchInfo(processId);
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(),launchInfo.getCreateUser());
        NameUtils.setName(launchInfo);
        return new JsonObject<>(launchInfo);
    }

    @GetMapping("/delete")
    @Operation(summary = "注册信息删除")
    @PreAuthorize(hasPermission = "crm_agent_registration_delete", dataScope = "crm_agent_registration_delete")
    public JsonObject<Boolean> delete(@RequestParam String id) {
        CrmAssert.hasText(id, "渠道Id不能为空");
        AgentRegistration agentRegistration = registrationService.selectInfoById(id);
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(),agentRegistration.getCreateUser());
        return new JsonObject<>(registrationService.delete(id));
    }

    @PostMapping("/updateEnterpriseInfo")
    @Operation(summary = "更新企业信息")
    @PreAuthorize(hasPermission = "crm_agent_registration", dataScope = "crm_agent_registration")
    public JsonObject<CrmCustomerBussinessInfoDTO> updateEnterpriseInfo(@RequestBody AgentRegistration agentRegistration) {
        return new JsonObject<>(registrationService.updateCustomerBussinessInfo(agentRegistration));
    }

    @Operation(summary = "输入客户名称后，先查询本地数据库有没有这个客户的信息，如果没有调用天眼查的接口查询企业工商信息")
    @GetMapping("/getTYCBusinessInfoByName")
    @PreAuthorize(hasPermission = "crm_agent_registration_add", dataScope = "crm_agent_registration_add")
    public JsonObject<JSONObject> getTYCBusinessInfoByName(@RequestParam String agentName)
    {
        JsonObject<JSONObject> businessInfoByName = remoteTycSelectService.getBusinessInfoByName(agentName);
        if(businessInfoByName.isSuccess() && businessInfoByName.getObjEntity() != null){
            return businessInfoByName;
        }else{
            String sign = MD5Util.MD5(agentName + certifiedToken).toUpperCase();
            JsonObject<JSONObject> tycBusinessInfo = remoteTycSelectService.getTYCBusinessInfo(agentName, sign, null);
            if(tycBusinessInfo.isSuccess()){
                String busInfo = tycBusinessInfo.getObjEntity().toString();
                Integer errorCode = JSONObject.parseObject(busInfo).getInteger("error_code");
                //判断状态
                if (errorCode == null || errorCode == 0) {
                    JsonObject<JSONObject> queryNew = remoteTycSelectService.getBusinessInfoByName(agentName);
                    return queryNew;
                }
            }else{
                throw new CrmException("未查询到该客户工商注册信息，请手动输入企业工商信息！");
            }
        }
        return new JsonObject<>(null);
    }


    @GetMapping("/checkMobileAndEmail")
    @Operation(summary = "校验手机号和邮箱是否重复", description = "校验手机号和邮箱是否重复")
    @PreAuthorize(hasAnyPermission = {"crm_agent_registration_add","crm_agent_account"})
    public JsonObject<Boolean> checkMobileAndEmail(@RequestParam String mobile, @RequestParam String email) {
        // 判断手机号是否已经注册
        JsonObject<Boolean> jsonObject = tbsAccountClient.isExistByUsernameOrMobile(mobile);

        // 不存在则可以注册
        boolean b = jsonObject != null && !jsonObject.getObjEntity();
        if (!b) {
            throw new CrmException(AuthErrorEnum.AUTH_ERROR_500301.getMessage());
        }

        jsonObject = tbsPersonClient.existByEmail(email);
        // 不存在则可以注册
        b = jsonObject != null && !jsonObject.getObjEntity();
        if (b) {
            return new JsonObject<>(true);
        } else {
            throw new CrmException(AuthErrorEnum.AUTH_ERROR_500309.getMessage());
        }
    }

    @GetMapping("/download/rule")
    @Operation(summary = "渠道违规行为分类及处理细则表")
    @PreAuthorize(hasPermission = "crm_agent_registration_add", dataScope = "crm_agent_registration_add")
    public void downloadRule(HttpServletResponse response) throws IOException {
        downloadAgentFile(response, "渠道违规行为分类及处理细则表.xlsx");
    }

    @GetMapping("/download/order")
    @Operation(summary = "天融信渠道违规行为管理制度")
    @PreAuthorize(hasPermission = "crm_agent_registration_add", dataScope = "crm_agent_registration_add")
    public void downloadOrder(HttpServletResponse response) throws IOException {
        downloadAgentFile(response, "天融信渠道违规行为管理制度.doc");
    }

    @GetMapping("/download/promise")
    @Operation(summary = "天融信合作伙伴廉洁诚信承诺书")
    @PreAuthorize(hasPermission = "crm_agent_registration_add", dataScope = "crm_agent_registration_add")
    public void downloadPromise(HttpServletResponse response) throws IOException {
        downloadAgentFile(response, "天融信合作伙伴廉洁诚信承诺书.docx");
    }

    public void downloadAgentFile(HttpServletResponse response,@RequestParam String fileName) throws IOException {
        // 构建资源路径
        String resourcePath = "classpath:agent/" + fileName;
        // 加载资源
        Resource resource = resourceLoader.getResource(resourcePath);
        if (!resource.exists()) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "File not found");
            return;
        }

        String mimeType = getMimeType(fileName);
        // 设置响应头
        response.setContentType(mimeType);
        // 对文件名进行 URL 编码
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + encodedFileName);
        // 读取文件内容并写入响应输出流
        try (InputStream inputStream = new BufferedInputStream(resource.getInputStream());
             OutputStream outputStream = response.getOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
    }

    private String getMimeType(String fileName) {
        String type = "application/octet-stream";
        String fileExtension = "";
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex != -1) {
            fileExtension = fileName.substring(dotIndex + 1).toLowerCase();

            switch (fileExtension) {
                case "doc":
                    type = "application/msword";
                    break;
                case "docx":
                    type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                    break;
                case "xlsx":
                    type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    break;
            }
        }
        return type;
    }
}
