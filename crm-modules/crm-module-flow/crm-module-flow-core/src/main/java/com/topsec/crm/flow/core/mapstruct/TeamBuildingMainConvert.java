package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.teamBuilding.TeamBuildingMainInfo;
import com.topsec.crm.flow.core.entity.TeamBuilding;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-07-02  14:51
 * @Description:
 */
@Mapper
public interface TeamBuildingMainConvert {

    TeamBuildingMainConvert INSTANCE = Mappers.getMapper(TeamBuildingMainConvert.class);


    @Mapping(target = "acceptanceTime", expression = "java(StringToLocalDateTimeConverter.parseLocalDateTime(teamBuildingMainInfo.getAcceptanceTime()))",ignore = false)
    @Mapping(target="id",source = "teamBuildingMainInfo.id")
    TeamBuilding toInfo(TeamBuildingMainInfo teamBuildingMainInfo);

    @Mapping(target = "accountId", expression = "java(FlowPersonUtils.getAccountIdByPersonId(employeeVO.getUuid()))",ignore = false)
    @Mapping(target="personId",source = "uuid")
    @Mapping(target = "personName", source = "name")
    @Mapping(target="jobNo",source = "jobNo")
    @Mapping(target="jobStatus",ignore = true)
    @Mapping(target="avatarDocId",ignore = true)
    @Mapping(target="dept",ignore = true)
    @Mapping(target="position",ignore = true)
    @Mapping(target="rank",ignore = true)
    FlowPerson toFlowPerson(EmployeeVO employeeVO);



    @Mapping(target = "accountId", expression = "java(FlowPersonUtils.getAccountIdByPersonId(employeeVO.getUuid()))",ignore = false)
    @Mapping(target="personId",source = "uuid")
    @Mapping(target = "personName", source = "name")
    @Mapping(target="jobNo",source = "jobNo")
    @Mapping(target="jobStatus",ignore = true)
    @Mapping(target="avatarDocId",ignore = true)
    @Mapping(target="dept",ignore = true)
    @Mapping(target="position",ignore = true)
    @Mapping(target="rank",ignore = true)
    List<FlowPerson> toFlowPersonList(List<EmployeeVO> employeeVOList);


    public class StringToLocalDateTimeConverter {
        private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

        public static LocalDateTime parseLocalDateTime(String dateTimeStr) {
            return LocalDateTime.parse(dateTimeStr, formatter);
        }
    }
    public class FlowPersonUtils {
        public static String getAccountIdByPersonId(String personId) {
            return AccountAccquireUtils.getAccountIdByPersonId(personId);
        }
    }

}
