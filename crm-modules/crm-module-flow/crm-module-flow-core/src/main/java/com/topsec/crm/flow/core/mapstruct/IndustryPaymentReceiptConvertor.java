package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.industryPaymentReceipt.IndustryPaymentReceiptLaunchable;
import com.topsec.crm.flow.api.dto.industryPaymentReceipt.IndustryPaymentReceiptVO;
import com.topsec.crm.flow.api.dto.industryPaymentReceipt.IndustryPaymentReceiptWriteOffVO;
import com.topsec.crm.flow.core.entity.IndustryPaymentReceipt;
import com.topsec.crm.flow.core.entity.IndustryPaymentReceiptWriteOff;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface IndustryPaymentReceiptConvertor {
    IndustryPaymentReceiptConvertor INSTANCE = Mappers.getMapper(IndustryPaymentReceiptConvertor.class);



    @Mapping(target = "processState", ignore = true)
    @Mapping(target = "processNumber", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    IndustryPaymentReceipt launchableToEntity(IndustryPaymentReceiptLaunchable launchable);


    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    IndustryPaymentReceiptWriteOff writeOffToEntity(IndustryPaymentReceiptWriteOffVO writeOff);

    List<IndustryPaymentReceiptWriteOff> writeOffToEntity(List<IndustryPaymentReceiptWriteOffVO> writeOff);


    IndustryPaymentReceiptWriteOffVO writeEntityToVO(IndustryPaymentReceiptWriteOff writeOff);

    List<IndustryPaymentReceiptWriteOffVO> writeEntityToVO(List<IndustryPaymentReceiptWriteOff> writeOff);


    @Mapping(target = "writeOffDetails", ignore = true)
    IndustryPaymentReceiptVO entityToVO(IndustryPaymentReceipt industryPaymentReceipt);
}
