package com.topsec.crm.flow.core.controller.projectRadio;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.topsec.crm.account.api.client.RemoteAccountService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.dto.projectRadio.ProjectRadioFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.projectRadio.ProjectRadioMainVo;
import com.topsec.crm.flow.api.dto.projectReport.ProjectReportMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ProjectRadioMain;
import com.topsec.crm.flow.core.entity.ProjectReportMain;
import com.topsec.crm.flow.core.process.impl.ProjectRadioProcessService;
import com.topsec.crm.flow.core.service.IProjectRadioMainService;
import com.topsec.crm.flow.core.service.IProjectReportMainService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.date.DateUtil;
import com.topsec.crm.framework.common.util.date.DateUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.entity.CrmProjectAgentVo;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.ProjectIdQueryParam;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbsapi.client.TbsCrmClient;
import com.topsec.tbsapi.client.TbsPersonClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.process.DetailBaseVO;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/projectRadio")
@Tag(name = "项目广播", description = "/projectRadio")
@RequiredArgsConstructor
public class ProjectRadioMainController extends BaseController {

    @Autowired
    private IProjectRadioMainService projectRadioMainService;
    @Autowired
    private ProjectRadioProcessService projectRadioProcessService;
    @Autowired
    private IProjectReportMainService projectReportMainService;
    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    @Autowired
    private RemoteAccountService remoteAccountService;
    @Autowired
    private TfsNodeClient tfsNodeClient;
    @Autowired
    private TbsPersonClient tbsPersonClient;

    @PostMapping("/page")
    @Operation(summary = "项目广播分页列表")
    @PreAuthorize(hasPermission = "crm_project_radio",dataScope = "crm_project_radio")
    public JsonObject<PageUtils<ProjectRadioMainVo>> selectPage(@RequestBody ProjectRadioMainVo projectReportMainVo) {
        List<String> personIds = new ArrayList<String>();
        Set<String> projectIds = new HashSet<String>();
        if(StringUtils.isNotBlank(projectReportMainVo.getKeyword())) {
            if (projectReportMainVo.getSearchType() == 4) {
                //查询部门下所有的人员ID
                // 获取部门所有人员ID
                personIds = remoteAccountService
                        .findEmployeeByDeptId(projectReportMainVo.getKeyword()).getObjEntity()
                        .stream().map(DetailBaseVO::getUuid).toList();
                if(CollectionUtils.isEmpty(personIds)) {
                    return new JsonObject<>(new PageUtils<ProjectRadioMainVo>());
                }
            } else if (projectReportMainVo.getSearchType() == 3 || projectReportMainVo.getSearchType() == 6) {
                Set<String> directlyProjectIds = remoteProjectDirectlyClient.queryProjectIdsByParam(new ProjectIdQueryParam(projectReportMainVo.getSearchType(), projectReportMainVo.getKeyword())).getObjEntity();
                projectIds.addAll(directlyProjectIds);
                if(CollectionUtils.isEmpty(directlyProjectIds)) {
                    return new JsonObject<>(new PageUtils<ProjectRadioMainVo>());
                }
            }
        }

        List<ProjectReportMain> reports = projectReportMainService.query()
                .lt(projectReportMainVo.getIsHistory() != null && projectReportMainVo.getIsHistory() == 1, "protect_end_date", LocalDate.now())
                .and(projectReportMainVo.getIsHistory() == null || projectReportMainVo.getIsHistory() == 0,
                        wrapper -> wrapper.lt("protect_start_date", LocalDate.now())
                                .gt("protect_end_date", LocalDate.now())
                                .or().eq("process_state", 1)
                )
                .list();
        List<String> reportIds = reports.stream().map(ProjectReportMain::getId).toList();

        //分页查询
        List<ProjectRadioMain> list = new ArrayList<ProjectRadioMain>();
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        startPage();
        if(StringUtils.isNotBlank(projectReportMainVo.getKeyword())) {
            list = projectRadioMainService.query()
                    .eq(StringUtils.isNotNull(projectReportMainVo.getProcessState()), "process_state", projectReportMainVo.getProcessState())
                    .eq("del_flag", false)
                    .in(CollectionUtil.isNotEmpty(dataScopeParam.getPersonIdList()), "create_user", dataScopeParam.getPersonIdList())
                    .in("report_id", reportIds)
                    .eq(projectReportMainVo.getSearchType() == 1, "process_number", projectReportMainVo.getKeyword())
                    .like(projectReportMainVo.getSearchType() == 2, "project_name", projectReportMainVo.getKeyword())
                    .in(projectReportMainVo.getSearchType() == 3 || projectReportMainVo.getSearchType() == 6, "project_id", projectIds)
                    .in(projectReportMainVo.getSearchType() == 4, "create_user", personIds)
                    .eq(projectReportMainVo.getSearchType() == 5, "create_user", projectReportMainVo.getKeyword())
                    .orderByDesc("create_time")
                    .list();
        }else{
            list = projectRadioMainService.query()
                    .eq(StringUtils.isNotNull(projectReportMainVo.getProcessState()),"process_state", projectReportMainVo.getProcessState())
                    .eq("del_flag", false)
                    .in(CollectionUtil.isNotEmpty(dataScopeParam.getPersonIdList()), "create_user", dataScopeParam.getPersonIdList())
                    .orderByDesc("create_time")
                    .in("report_id", reportIds)
                    .list();
        }

        List<ProjectRadioMainVo> listVo = new ArrayList<ProjectRadioMainVo>();
        if(CollectionUtils.isNotEmpty(list)){
            List<String> rIds = list.stream().map(ProjectRadioMain::getReportId).toList();
            List<String> createUserIds = list.stream().map(ProjectRadioMain::getCreateUser).toList();
            List<String> directlyIds = list.stream().map(ProjectRadioMain::getProjectId).toList();

            List<PersonVO> personVOSVOS = new ArrayList<PersonVO>();
            List<CrmProjectAgentVo> projectAgents = new ArrayList<CrmProjectAgentVo>();
            List<CrmProjectDirectlyVo> projectDirectlys = new ArrayList<CrmProjectDirectlyVo>();
            //1.批量查询用户信息
            JsonObject<List<PersonVO>> byIds = tbsPersonClient.listByIds(createUserIds.toArray(new String[]{}));
            if(byIds.isSuccess()){
                personVOSVOS = byIds.getObjEntity();
            }

            //2.批量查询直签项目信息
            if(CollectionUtil.isNotEmpty(directlyIds)) {
                JsonObject<List<CrmProjectDirectlyVo>> listJsonObject = remoteProjectDirectlyClient.listByIds(directlyIds);
                if(listJsonObject.isSuccess()){
                    projectDirectlys = listJsonObject.getObjEntity();
                }
            }

            //3.批量查询项目报备信息
            List<ProjectReportMain> res = projectReportMainService.query().in("id", rIds).list();

            //4补充项目信息
            List<CrmProjectDirectlyVo> finalProjectDirectlys = projectDirectlys;
            List<PersonVO> finalPersonVOSVOS = personVOSVOS;
            list.stream().forEach(radioMain -> {
                ProjectRadioMainVo prmv = HyperBeanUtils.copyPropertiesByJackson(radioMain, ProjectRadioMainVo.class);

                //4.1补全项目信息
                CrmProjectDirectlyVo directlyInfo = finalProjectDirectlys.stream().filter(ra -> ra.getId().equals(prmv.getProjectId())).findFirst().orElse(null);
                if(directlyInfo != null) {
                    prmv.setProjectNo(directlyInfo.getProjectNo());
                    prmv.setFinalCustomName(directlyInfo.getCrmProjectSigningInfo() != null ? directlyInfo.getCrmProjectSigningInfo().getCustomerName() : "");
                }

                //4.2补全报备信息
                ProjectReportMain reportMain = res.stream().filter(re -> re.getId().equals(prmv.getReportId())).findFirst().orElse(null);
                if(reportMain != null) {
                    prmv.setProtectStartDate(reportMain.getProtectStartDate());
                    prmv.setProtectEndDate(reportMain.getProtectEndDate());
                }

                //4.3补充发起人信息
                List<PersonVO> collect = finalPersonVOSVOS.stream().filter(e -> e.getUuid().equals(prmv.getCreateUser())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(collect)){
                    PersonVO personVO = collect.get(0);
                    prmv.setCreateUserName(NameUtils.getNameByPersonVO(personVO));
                    prmv.setDeptName(personVO.getDepartmentVO() != null ? personVO.getDepartmentVO().getDeptName() : "");
                }

                //4.3查询项目具体审批节点
                JsonObject<Set<ApproveNode>> setJsonObject = tfsNodeClient.queryNodeByProcessInstanceId(reportMain.getProcessInstanceId());
                if(setJsonObject.isSuccess()){
                    prmv.setApprovalNode(setJsonObject.getObjEntity());
                }
                listVo.add(prmv);
            });
        }

        //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
        PageUtils dataTable = getDataTable(list,listVo);
        return new JsonObject<>(dataTable);
    }
    @PostMapping("/launch")
    @Operation(summary = "发起公司项目广播流程")
    @PreAuthorize(hasAnyPermission = {"crm_flow_project_radio"})
    public JsonObject<Boolean> launch(@Valid @RequestBody ProjectRadioFlowLaunchDTO launchDTO){
        // 加一个项目的权限，根据当前登录人看看有没有项目的权限，发起之前的话 要根据当前人判断
        if (!remoteProjectDirectlyClient.hasRight(launchDTO.getProjectId(), UserInfoHolder.getCurrentPersonId()).getObjEntity()) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        };

        //查询项目报备流程信息
        ProjectReportMain projectReportMain = projectReportMainService.getOne(new QueryWrapper<ProjectReportMain>()
                .eq("process_instance_id", launchDTO.getProcessInstanceId())
                .orderByAsc("create_time")
                .last("limit 1")
        );

        if(projectReportMain != null) {
            Assert.isFalse(projectReportMain.getProcessState() == ApprovalStatusEnum.SPZ.getCode(), "项目审批未通过，不可发起项目广播");
            Assert.isFalse(LocalDate.now().isBefore(projectReportMain.getProtectStartDate()) || LocalDate.now().isAfter(projectReportMain.getProtectEndDate()), "不在有效期内不可发起项目广播");
            launchDTO.getBaseInfo().setReportId(projectReportMain.getId());
        }

        //查询是否有正在审批的项目广播流程
        ProjectRadioMain one = projectRadioMainService.getOne(new QueryWrapper<ProjectRadioMain>()
                .eq("project_id", launchDTO.getBaseInfo().getProjectId())
                .orderByDesc("create_time")
                .last("limit 1")
        );
        Assert.isFalse(one != null && one.getProcessState() == ApprovalStatusEnum.SPZ.getCode(),"项目存在正在审批中的项目广播流程。");

        return new JsonObject<>(projectRadioProcessService.launch(launchDTO));
    }

    @PostMapping("/selectInfo")
    @Operation(summary = "查询广播内容")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<ProjectRadioMainVo> selectInfo(@RequestBody ProjectRadioMainVo projectRadioMainVo){
        PreFlowPermissionAspect.checkProcessInstanceId(projectRadioMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //查询项目报备流程信息
        ProjectRadioMain projectReportMain = projectRadioMainService.getOne(new QueryWrapper<ProjectRadioMain>().eq("process_instance_id", projectRadioMainVo.getProcessInstanceId()));

        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(projectReportMain,ProjectRadioMainVo.class));
    }

    @PostMapping("/update")
    @Operation(summary = "修改广播内容")
    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"sid-BEEBB7E7-4792-4F5F-908D-D9F503B47C01"})
    public JsonObject<Boolean> update(@Valid @RequestBody ProjectRadioMainVo projectRadioMainVo){
        PreFlowPermissionAspect.checkProcessInstanceId(projectRadioMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        projectRadioMainService.update(new UpdateWrapper<ProjectRadioMain>().eq("process_instance_id",projectRadioMainVo.getProcessInstanceId())
                .set("content", projectRadioMainVo.getContent())
                .set("re_radio_reason", projectRadioMainVo.getReRadioReason())
        );

        return new JsonObject<Boolean>(ResultEnum.SUCCESS.getResult(), ResultEnum.SUCCESS.getMessage(),true);
    }

    @PostMapping("/selectAlreadyRadio")
    @Operation(summary = "查询是否发起过项目广播")
    @PreAuthorize(hasPermission = "crm_flow_project_radio")
    public JsonObject<Map<String,Boolean>> selectAlreadyRadio(@Valid @RequestBody ProjectRadioMainVo projectRadioMainVo){
        // 加一个项目的权限，根据当前登录人看看有没有项目的权限，发起之前的话 要根据当前人判断
        if (!remoteProjectDirectlyClient.hasRight(projectRadioMainVo.getProjectId(), UserInfoHolder.getCurrentPersonId()).getObjEntity()) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        };

        Map<String,Boolean> result = new HashMap<String,Boolean>();

        List<ProjectRadioMain> list = projectRadioMainService.query()
                .eq("project_id", projectRadioMainVo.getProjectId())
                .eq("process_state", ApprovalStatusEnum.YSP.getCode())
                .gt("create_time", DateUtil.dateToLocalDate(DateUtils.addMonths(new Date(), -3)))
                .list();

        result.put("inThreeMonth",list.size() > 0 ? true : false);

        List<ProjectRadioMain> list2 = projectRadioMainService.query()
                .eq("project_id", projectRadioMainVo.getProjectId())
                .eq("process_state", ApprovalStatusEnum.YSP.getCode())
                .lt("create_time", DateUtil.dateToLocalDate(DateUtils.addMonths(new Date(), -3)))
                .list();
        result.put("outThreeMonth",list2.size() > 0 ? true : false);

        return new JsonObject<>(result);
    }

    @PostMapping("/selectFlowRadio")
    @Operation(summary = "查询当前项目是否存在流程中的项目广播")
    @PreAuthorize(hasPermission = "crm_flow_project_radio")
    public JsonObject<Boolean> selectFlowRadio(@Valid @RequestBody ProjectRadioMainVo projectRadioMainVo){
        // 加一个项目的权限，根据当前登录人看看有没有项目的权限，发起之前的话 要根据当前人判断
        if (!remoteProjectDirectlyClient.hasRight(projectRadioMainVo.getProjectId(), UserInfoHolder.getCurrentPersonId()).getObjEntity()) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        };

        List<ProjectRadioMain> list = projectRadioMainService.query()
                .eq("project_id", projectRadioMainVo.getProjectId())
                .eq("process_state", ApprovalStatusEnum.SPZ.getCode())
                .list();
        return new JsonObject<>(list.size() > 0 ? true : false);
    }

}
