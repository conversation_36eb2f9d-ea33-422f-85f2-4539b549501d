package com.topsec.crm.flow.core.controller.teamBuilding;

import com.topsec.crm.flow.api.dto.teamBuilding.input.TeamAppointmentAssignmentDTO;
import com.topsec.crm.flow.api.dto.teamBuilding.input.TeamAppointmentVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.TeamAppointment;
import com.topsec.crm.flow.core.service.TeamAppointmentService;
import com.topsec.crm.flow.core.service.TeamBuildingService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-07-02  17:41
 * @Description:
 */
@RestController
@RequestMapping("/teamAppointment")
@Tag(name = "团队任命", description = "/teamAppointment")
@RequiredArgsConstructor
@Validated
public class TeamAppointmentController extends BaseController {
    private final TeamAppointmentService teamAppointmentService;

    private final TeamBuildingService teamBuildingService;

    @PostMapping("/designateQA")
    @PreFlowPermission(hasAnyNodes = {"sid-FEDB2DFF-FB57-4A5A-9128-A34749FFAD3A","sid-5A1D9598-FF38-4AF8-AE29-E8C7F7210B24","sid-4B37B824-A838-4D8B-B022-E9B5A7C137AB"})
    @Operation(summary = "(04,06,07指派)")
    public JsonObject<Boolean> designateQA(@RequestBody TeamAppointmentVO teamAppointmentVO){
        CrmAssert.notNull("团队组建id不能为null",teamAppointmentVO.getTeamBuildingId());
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = teamBuildingService.getById(teamAppointmentVO.getTeamBuildingId()).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(teamAppointmentService.designateQA(teamAppointmentVO));
    }

    @PostMapping("/designateQA2")
    @PreFlowPermission
    @Operation(summary = "01B或者01C指派QA")
    public JsonObject<Boolean> designateQA2(@RequestBody TeamAppointmentVO teamAppointmentVO){
        CrmAssert.notNull("团队组建id不能为null",teamAppointmentVO.getTeamBuildingId());
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = teamBuildingService.getById(teamAppointmentVO.getTeamBuildingId()).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(teamAppointmentService.designateQA(teamAppointmentVO));
    }

    @PostMapping("/designateMembers")
    @PreFlowPermission(hasAnyNodes = {"sid-4B37B824-A838-4D8B-B022-E9B5A7C137AB","sid-E03CAB3C-B3B4-4B6A-85D9-AE1CACA335D3"})
    @Operation(summary = "07团队任命")
    public JsonObject<Boolean> designateMembers(@RequestBody List<TeamAppointmentVO> teamAppointmentVOList){
        teamAppointmentVOList.stream().forEach(item->{
            String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
            String processInstanceId = teamBuildingService.getById(item.getTeamBuildingId()).getProcessInstanceId();
            PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        });
        return new JsonObject<>(teamAppointmentService.designateMembers(teamAppointmentVOList));
    }

    @PostMapping("/designateManager")
    @PreFlowPermission(hasAnyNodes = {"sid-FEDB2DFF-FB57-4A5A-9128-A34749FFAD3A"})
    @Operation(summary = "04指派PM或者TM")
    public JsonObject<Boolean> designateManager(@RequestBody List<TeamAppointmentVO> teamAppointmentVOList){
        teamAppointmentVOList.stream().forEach(item->{
            String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
            String processInstanceId = teamBuildingService.getById(item.getTeamBuildingId()).getProcessInstanceId();
            PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        });
        return new JsonObject<>(teamAppointmentService.designateManager(teamAppointmentVOList));
    }



    @GetMapping("/teamAppointmentInfo")
    @PreFlowPermission
    @Operation(summary = "团队任命信息")
    public JsonObject<List<TeamAppointmentVO>> teamAppointmentInfo(@RequestParam String teamBuildingId){
        CrmAssert.notNull("团队组建id不能为null",teamBuildingId);
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = teamBuildingService.getById(teamBuildingId).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(teamAppointmentService.teamAppointmentInfo(teamBuildingId));
    }

    @PostMapping("/systemLeadershipAssignment")
    @PreFlowPermission(hasAnyNodes = {"sid-9818449C-DD9B-49A9-9F25-A2F5552E5345"})
    @Operation(summary = "03体系领导指派")
    public JsonObject<Boolean> systemLeadershipAssignment(@RequestBody TeamAppointmentAssignmentDTO teamAppointmentAssignmentDTO){
        CrmAssert.notNull("团队组建id不能为null",teamAppointmentAssignmentDTO.getTeamBuildingId());
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = teamBuildingService.getById(teamAppointmentAssignmentDTO.getTeamBuildingId()).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(teamAppointmentService.systemLeadershipAssignment(teamAppointmentAssignmentDTO));
    }


    @PostMapping("/delete")
    @PreFlowPermission(hasAnyNodes = {"sid-5A1D9598-FF38-4AF8-AE29-E8C7F7210B24"})
    @Operation(summary = "删除")
    public JsonObject<Boolean> delete(@RequestParam String id){
        CrmAssert.notNull("团队任命id不能为null",id);
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        TeamAppointment teamAppointment = Optional.ofNullable(teamAppointmentService.getById(id)).orElseThrow(() -> new CrmException(ResultEnum.NULL_OBJ_ENTITY));
        String processInstanceId = teamBuildingService.getById(teamAppointment.getTeamBuildingId()).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(teamAppointmentService.delete(id));
    }






}
