package com.topsec.crm.flow.core.controllerhidden.targetedinventorypreparation;

import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.TargetedInventoryPreparationBaseInfoVO;
import com.topsec.crm.flow.core.service.ITargetedInventoryPreparationMainService;
import com.topsec.crm.flow.core.service.ITargetedInventoryPreparationProductOwnService;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/hidden/targetedInventoryPreparationMain")
@Tag(name = "专项备货-内部调用", description = "/hidden/targetedInventoryPreparationMain")
@RequiredArgsConstructor
@Validated
public class HiddenTargetedInventoryPreparationMainController {

    private final ITargetedInventoryPreparationMainService iTargetedInventoryPreparationMainService;

    private final ITargetedInventoryPreparationProductOwnService iTargetedInventoryPreparationProductOwnService;

//    @PreFlowPermission
    @GetMapping("/autoUpdateApprovalNodeShort")
    @Operation(summary = "06/10 暂挂自动办结")
    public void autoUpdateApprovalNodeShort(){
        iTargetedInventoryPreparationMainService.autoUpdateApprovalNodeShort();
    }

//    @PreFlowPermission
    @GetMapping("/autoUpdateApprovalNodePayment")
    @Operation(summary = "09/12 财务自动办结")
    public void autoUpdateApprovalNodePayment(){
        iTargetedInventoryPreparationMainService.autoUpdateApprovalNodePayment();
    }

    @GetMapping("/getMainInfo")
    @Operation(summary = "根据项目ID获取专项备货信息")
    public JsonObject<List<TargetedInventoryPreparationBaseInfoVO>> getMainInfo(@RequestParam String projectId){
        return new JsonObject<>(iTargetedInventoryPreparationMainService.getMainInfo(projectId));
    }

    @GetMapping("/getStockUpQuantity")
    @Operation(summary = "根据自有产品行ID获取备货数量")
    public JsonObject<Map<String,Long>> getStockUpQuantity(@RequestParam List<String> rowIds){
        return new JsonObject<>(iTargetedInventoryPreparationProductOwnService.getStockUpQuantity(rowIds));
    }
}
