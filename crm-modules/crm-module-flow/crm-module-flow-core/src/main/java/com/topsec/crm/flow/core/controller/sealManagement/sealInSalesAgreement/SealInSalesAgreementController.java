package com.topsec.crm.flow.core.controller.sealManagement.sealInSalesAgreement;

import com.topsec.crm.flow.api.dto.sealApplication.SealApplicationFlowLaunchDTO;
import com.topsec.crm.flow.core.process.impl.SealInSalesAgreementProcessService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sealInSalesAgreement")
@Tag(name = "【销售协议印鉴申请】", description = "sealInSalesAgreement")
@RequiredArgsConstructor
@Validated
public class SealInSalesAgreementController extends BaseController {


    private final SealInSalesAgreementProcessService sealInSalesAgreementProcessService;


    @PostMapping("/launch")
    @Operation(summary = "发起销售印鉴流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody SealApplicationFlowLaunchDTO sealApplicationFlowLaunchDTO) {
        return new JsonObject<>(sealInSalesAgreementProcessService.launch(sealApplicationFlowLaunchDTO));
    }

}
