package com.topsec.crm.flow.core.controller.costFiling.costTransferMinus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.account.api.client.RemoteAccountService;
import com.topsec.crm.flow.api.dto.costTransferMinus.CostTransferMinusFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.returnexchange.ReturnExchangeDetailVO;
import com.topsec.crm.flow.api.dto.returnexchange.ReturnExchangeFeeVO;
import com.topsec.crm.flow.api.vo.costTransferMinus.CostTransferMinusInfoVO;
import com.topsec.crm.flow.api.vo.costTransferMinus.MinusMoneyQuery;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.process.impl.CostTransferMinusProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/costTransferMinus")
@Tag(name = "【费用调减-流程接口】", description = "costTransferMinus")
@RequiredArgsConstructor
@Validated
public class CostTransferMinusController {

    private final ICostFilingService costFilingService;
    private final CostTransferMinusProcessService costTransferMinusProcessService;
    private final CostTransferMinusService costTransferMinusService;
    private final ContractReviewReturnExchangeService contractReviewReturnExchangeService;
    private final ReturnExchangeProductService returnExchangeProductService;
    private final ReturnExchangeProductThirdService returnExchangeProductThirdService;
    private final RemoteAccountService remoteAccountService;
    private final ReturnExchangeFeeService returnExchangeFeeService;
    private final ProcessExtensionInfoService processExtensionInfoService;
    private final TfsNodeClient tfsNodeClient;

    @PostMapping("/getIsCostCondition")
    @PreAuthorize(hasAnyPermission={"crm_flow_cost_filing","crm_cost_filing"})
    @Operation(summary = "获取合同是否可以发起扣减流程")
    public JsonObject<Boolean> getIsCostCondition(@RequestBody MinusMoneyQuery query){
        return new JsonObject<>(costFilingService.getIsCostCondition(query));
    }

    @PreAuthorize(hasAnyPermission={"crm_flow_cost_filing","crm_cost_filing"})
    @PostMapping("/launch")
    @Operation(summary = "发起费用扣减")
    public JsonObject<Boolean> launch(@Valid @RequestBody CostTransferMinusFlowLaunchDTO costTransferMinusFlowLaunchDTO){
        return new JsonObject<>(costTransferMinusProcessService.launch(costTransferMinusFlowLaunchDTO));
    }

    @GetMapping("/getCostTransferMinusInfo")
    @Operation(summary = "查询费用调减详情")
    @PreFlowPermission
    public JsonObject<CostTransferMinusInfoVO> getCostTransferMinusInfo(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(costTransferMinusService.getCostTransferMinusInfo(processInstanceId));
    }

    @PostMapping("/updateCostTtransferMinusMoney")
    @Operation(summary = "修改费用合调减金额")
    @PreFlowPermission
    public JsonObject<Boolean> updateCostTtransferMinusMoney( @RequestBody CostTransferMinusInfoVO costTransferMinusInfoVO){
        PreFlowPermissionAspect.checkProcessInstanceId(costTransferMinusInfoVO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(costTransferMinusService.updateCostTtransferMinusMoney(costTransferMinusInfoVO));
    }

    @GetMapping("/returnExchangeDetail")
    @Operation(summary = "退换货流程基础信息")
    @PreFlowPermission
    public JsonObject<ReturnExchangeDetailVO> returnExchangeContractInfo() {
        CostTransferMinus costTransferMinus = costTransferMinusService.getOne(new LambdaQueryWrapper<CostTransferMinus>()
                .eq(CostTransferMinus::getProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID)));
        String processInstanceId = costTransferMinus.getRefundProcessInstanceId();
        ContractReviewReturnExchange returnExchangeBaseVO = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        ReturnExchangeDetailVO returnExchangeDetailVO = HyperBeanUtils.copyProperties(returnExchangeBaseVO, ReturnExchangeDetailVO::new);
        String createUser = returnExchangeBaseVO.getCreateUser();
        Map<String, PersonVO> personVOMap = remoteAccountService.queryPersonByIdsWithDept(List.of(createUser)).getObjEntity().stream().collect(Collectors.toMap(PersonVO::getUuid, item -> item));;
        returnExchangeDetailVO.setCreateUserDeptId(personVOMap.get(createUser).getDepartmentVO().getUuid());
        returnExchangeDetailVO.setNewProductAmount(returnExchangeProductService.getNewProductAmount(processInstanceId));
        returnExchangeDetailVO.setReturnedProductAmount(returnExchangeProductService.getOldProductAmount(processInstanceId));

        //产品线是否需要审批 -审批人反显用/退换货信息 字段赋值
        List<ReturnExchange.ProductLineRequired> productLineRequiredList =ListUtils.emptyIfNull(returnExchangeBaseVO.getProductLineRequiredList()) ;
        Map<String, Boolean> map = productLineRequiredList.stream().collect(Collectors.toMap(ReturnExchange.ProductLineRequired::getPersonId,
                ReturnExchange.ProductLineRequired::getRequired));

        returnExchangeDetailVO.setProductLineRequiredFinal(MapUtils.isNotEmpty(map)?map.containsValue(true):null);
        returnExchangeDetailVO.setProductLineRequiredForApply(map.getOrDefault(UserInfoHolder.getCurrentPersonId(),true));

        ProcessExtensionInfo one = processExtensionInfoService.getOne(new LambdaQueryWrapper<ProcessExtensionInfo>().eq(ProcessExtensionInfo::getProcessInstanceId, processInstanceId)
                .select(ProcessExtensionInfo::getProjectId));
        returnExchangeDetailVO.setProjectId(one.getProjectId());
        return new JsonObject<>(returnExchangeDetailVO);
    }

    @GetMapping("/returnExchangeReProduct")
    @Operation(summary = "查询退货换货旧产品详情")
    @PreFlowPermission
    public JsonObject<List<ReturnExchangeProduct>> returnExchangeDetail() {
        CostTransferMinus costTransferMinus = costTransferMinusService.getOne(new LambdaQueryWrapper<CostTransferMinus>()
                .eq(CostTransferMinus::getProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID)));
        String processInstanceId = costTransferMinus.getRefundProcessInstanceId();
        return new JsonObject<>(returnExchangeProductService.listOldProductByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/returnExchangeReProductThird")
    @Operation(summary = "查询退货换货旧第三方产品详情")
    @PreFlowPermission
    public JsonObject<List<ReturnExchangeProductThird>> returnExchangeReProductThird() {
        CostTransferMinus costTransferMinus = costTransferMinusService.getOne(new LambdaQueryWrapper<CostTransferMinus>()
                .eq(CostTransferMinus::getProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID)));
        String processInstanceId = costTransferMinus.getRefundProcessInstanceId();
        return new JsonObject<>(returnExchangeProductThirdService.listOldProductByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/returnExchangeNewProduct")
    @Operation(summary = "查询退货换货新产品详情")
    @PreFlowPermission
    public JsonObject<List<ReturnExchangeProduct>> returnExchangeNewProduct() {
        CostTransferMinus costTransferMinus = costTransferMinusService.getOne(new LambdaQueryWrapper<CostTransferMinus>()
                .eq(CostTransferMinus::getProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID)));
        String processInstanceId = costTransferMinus.getRefundProcessInstanceId();
        return new JsonObject<>(returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/returnExchangeNewProductThird")
    @Operation(summary = "查询退货换货新第三方产品详情")
    @PreFlowPermission
    public JsonObject<List<ReturnExchangeProductThird>> returnExchangeNewProductThird() {
        CostTransferMinus costTransferMinus = costTransferMinusService.getOne(new LambdaQueryWrapper<CostTransferMinus>()
                .eq(CostTransferMinus::getProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID)));
        String processInstanceId = costTransferMinus.getRefundProcessInstanceId();
        return new JsonObject<>(returnExchangeProductThirdService.listNewProductByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/queryAndMergeProcessFee")
    @Operation(summary = "获取退换货费用")
    @PreFlowPermission
    public JsonObject<List<ReturnExchangeFeeVO>> queryAndMergeProcessFee(){
        CostTransferMinus costTransferMinus = costTransferMinusService.getOne(new LambdaQueryWrapper<CostTransferMinus>()
                .eq(CostTransferMinus::getProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID)));
        String processInstanceId = costTransferMinus.getRefundProcessInstanceId();
        return new JsonObject<>(returnExchangeFeeService.queryAndMergeProcessFee(processInstanceId));
    }



}
