package com.topsec.crm.flow.core.controllerhidden.contractSignVerify;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.topsec.crm.flow.api.RemoteContractUnconfirmedDetailService;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractSignVerifyAttachmentDTO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractSignVerifyMainVo;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedVo;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/hidden/contractSignVerifyMain")
@RequiredArgsConstructor
@Validated
public class HiddenContractSignVerifyMainController extends BaseController {
    @Autowired
    private IContractSignVerifyMainService contractSignVerifyMainService;
    @Autowired
    private IContractSignVerifyAttachmentService contractSignVerifyAttachmentService;
    @Autowired
    private TfsNodeClient tfsNodeClient;
    @Autowired
    private IContractUnconfirmedDetailService contractUnconfirmedDetailService;
    @Autowired
    private IContractUnconfirmedService contractUnconfirmedService;
    @Autowired
    private IContractUnconfirmedDetailSnapshotService contractUnconfirmedDetailSnapshotService;
    @Autowired
    private RemoteContractUnconfirmedDetailService remoteContractUnconfirmedDetailService;
    @Autowired
    private IContractUnconfirmedSnapshotService contractUnconfirmedSnapshotService;

    //查询审批过01节点的所有附件
    @PostMapping("/getAttachAfter01")
    public JsonObject<Map<String,List<ContractSignVerifyAttachmentDTO>>> getAttachAfter01(@RequestBody List<String> contractNumbers) {
        Assert.isFalse(CollectionUtil.isEmpty(contractNumbers),"合同号不为空。");
        Map<String,List<ContractSignVerifyAttachmentDTO>> result = new HashMap<String, List<ContractSignVerifyAttachmentDTO>>();

        //查询合同所处的所有流程
        List<ContractSignVerifyMain> signVerifyMains = contractSignVerifyMainService.query().in("contract_number", contractNumbers).list();
        Map<String,List<String>> over01pro = new HashMap<String,List<String>>();
        for (ContractSignVerifyMain signVerifyMain : signVerifyMains) {
            if(signVerifyMain.getProcessState() == 2){
                if(over01pro.containsKey(signVerifyMain.getContractNumber())){
                    List<String> strings = over01pro.get(signVerifyMain.getContractNumber());
                    ArrayList<String> strings1 = CollectionUtil.newArrayList(strings);
                    strings1.add(signVerifyMain.getProcessInstanceId());
                    over01pro.put(signVerifyMain.getContractNumber(),strings1);
                }else{
                    over01pro.put(signVerifyMain.getContractNumber(),Collections.singletonList(signVerifyMain.getProcessInstanceId()));
                }
            }else{
                JsonObject<Set<ApproveNode>> setJsonObject = tfsNodeClient.queryNodeByProcessInstanceId(signVerifyMain.getProcessInstanceId());
                if(setJsonObject.isSuccess()){
                    ApproveNode approveNode = setJsonObject.getObjEntity().stream().findFirst().orElse(null);
                    if(approveNode != null){
                        if(!approveNode.getNodeName().equals("00呈报人") && !approveNode.getNodeName().equals("01合同负责人审批")){
                            //汇总当前超过01节点的流程ID
                            if(over01pro.containsKey(signVerifyMain.getContractNumber())){
                                List<String> strings = over01pro.get(signVerifyMain.getContractNumber());
                                ArrayList<String> strings1 = CollectionUtil.newArrayList(strings);
                                strings1.add(signVerifyMain.getProcessInstanceId());
                                over01pro.put(signVerifyMain.getContractNumber(),strings1);
                            }else{
                                over01pro.put(signVerifyMain.getContractNumber(),Collections.singletonList(signVerifyMain.getProcessInstanceId()));
                            }
                        }
                    }
                }
            }
        }

        for (String contractId : over01pro.keySet()) {
            List<String> proIds = over01pro.get(contractId);
            //1.查询附件列表
            List<ContractSignVerifyAttachment> attachments = contractSignVerifyAttachmentService.query().in("process_instance_id", proIds).list();
            result.put(contractId,HyperBeanUtils.copyListPropertiesByJackson(attachments, ContractSignVerifyAttachmentDTO.class));
        }

        return new JsonObject<>(result);
    }

    @PostMapping("/list")
    @Operation(summary = "签验收单列表")
//    @PreAuthorize(hasPermission = "crm_customer_dept_na_apply")
//    @Audit(eventName = "launch" ,eventDesc = "发起合同坏账审批流程", eventType = "NA客户申请",getMethodData = true)
    public JsonObject<List<ContractSignVerifyMainVo>> list(@RequestParam(required = true) String contractNumber) {
        List<ContractSignVerifyMain> signVerifyMains = contractSignVerifyMainService.query().eq("contract_number", contractNumber).list();
        List<ContractSignVerifyMainVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(signVerifyMains, ContractSignVerifyMainVo.class);

        List<String> personIds = listVo.stream().map(ContractSignVerifyMainVo::getCreateUser).toList();
        JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(personIds);

        if(byIds.isSuccess() && byIds.getObjEntity() != null) {
            List<EmployeeVO> employeeVOS = byIds.getObjEntity();
            for (ContractSignVerifyMainVo vo : listVo) {
                //1.查询项目具体审批节点
                JsonObject<Set<ApproveNode>> setJsonObject = tfsNodeClient.queryNodeByProcessInstanceId(vo.getProcessInstanceId());
                if (setJsonObject.isSuccess()) {
                    vo.setApprovalNode(setJsonObject.getObjEntity());
                }

                EmployeeVO employeeVO = employeeVOS.stream().filter(e -> e.getUuid().equals(vo.getCreateUser())).findFirst().orElse(null);
                vo.setCreateUserName(employeeVO != null ? employeeVO.getName() : null);

                //2.查询附件列表
                List<ContractSignVerifyAttachment> attachments = contractSignVerifyAttachmentService.query().eq("process_instance_id", vo.getProcessInstanceId()).list();
                vo.setAttachmentDTOS(HyperBeanUtils.copyListPropertiesByJackson(attachments, ContractSignVerifyAttachmentDTO.class));

            }
        }

        return new JsonObject<>(listVo);
    }

    //数据同步-清洗快照数据
    @GetMapping("/cleanContractSignVerifySnapshot")
    public JsonObject<Boolean> cleanContractSignVerifySnapshot() {
        List<ContractSignVerifyMain> list = contractSignVerifyMainService.list();

        for (ContractSignVerifyMain vmain : list) {
            //3.保存收入确认核定快照数据
            List<ContractUnconfirmedDetailSnapshot> snapshots = new ArrayList<ContractUnconfirmedDetailSnapshot>();
            if(StringUtils.isNotBlank(vmain.getUnconfirmedId())) {
                List<ContractUnconfirmedDetail> details = contractUnconfirmedDetailService.query()
                        .eq(StringUtils.isNotBlank(vmain.getUnconfirmedId()), "unconfirmed_id", vmain.getUnconfirmedId())
                        .eq("del_flag", 0)
                        .orderByDesc("sign_date")
                        .list();
                snapshots.addAll(HyperBeanUtils.copyListPropertiesByJackson(details, ContractUnconfirmedDetailSnapshot.class));
            }
            if(StringUtils.isBlank(vmain.getUnconfirmedId()) && StringUtils.isNotBlank(vmain.getContractNumber())){
                //查询未确认明细
                List<ContractUnconfirmed> uns = contractUnconfirmedService.query().eq("contract_number", vmain.getContractNumber()).list();

                for (ContractUnconfirmed un : uns) {
                    List<ContractUnconfirmedDetail> details = contractUnconfirmedDetailService.query()
                            .eq(StringUtils.isNotBlank(un.getId()), "unconfirmed_id", un.getId())
                            .eq("del_flag", 0)
                            .orderByDesc("sign_date")
                            .list();
                    snapshots.addAll(HyperBeanUtils.copyListPropertiesByJackson(details, ContractUnconfirmedDetailSnapshot.class));
                }
            }

            for (ContractUnconfirmedDetailSnapshot snapshot : snapshots) {
                snapshot.setId(UUID.randomUUID().toString());
                snapshot.setProcessInstanceId(vmain.getProcessInstanceId());
            }
            contractUnconfirmedDetailSnapshotService.saveBatch(snapshots);

            //4.保存未确认明细快照数据
            //4.1查询未确认明细列表
            ContractUnconfirmedVo params = new ContractUnconfirmedVo();
            params.setContractNumber(vmain.getContractNumber());
            params.setId(vmain.getUnconfirmedId());
            JsonObject<List<ContractUnconfirmedVo>> listJsonObject = remoteContractUnconfirmedDetailService.selectList(params);
            if(listJsonObject.isSuccess() && listJsonObject.getObjEntity() != null){
                List<ContractUnconfirmedSnapshot> snaps = HyperBeanUtils.copyListPropertiesByJackson(listJsonObject.getObjEntity(), ContractUnconfirmedSnapshot.class);
                for (ContractUnconfirmedSnapshot snap : snaps) {
                    snap.setUnconfirmedId(snap.getId());
                    snap.setId(UUID.randomUUID().toString());
                    snap.setProcessInstanceId(vmain.getProcessInstanceId());
                }
                contractUnconfirmedSnapshotService.saveBatch(snaps);
            }
        }

        return new JsonObject<>(true);
    }
}
