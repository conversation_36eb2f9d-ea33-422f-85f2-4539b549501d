package com.topsec.crm.flow.core.controller.sealManagement.borrowForSellSeal;


import com.topsec.crm.flow.api.dto.sealApplication.SealApplicationFlowLaunchDTO;
import com.topsec.crm.flow.core.process.impl.BorrowForSellSealProcessService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @version V1.0
 * @Description: 借转销印鉴申请流程控制层
 * @ClassName: com.topsec.crm.flow.core.controller.SealManagement.borrowForSellSeal.BorrowForSellSealController.java
 * @Copyright 天融信 - Powered By 企业软件研发中心
 * @author: leo
 * @date: 2025-05-20 09:14
 */
@RestController
@RequestMapping("/borrowForSellSeal")
@Tag(name = "借转销印鉴申请流程", description = "/borrowForSellSeal")
public class BorrowForSellSealController extends BaseController {

    @Autowired
    private BorrowForSellSealProcessService borrowForSellSealProcessService;

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell")
    @PostMapping("/launch")
    @Operation(summary = "发起借转销印鉴申请流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody SealApplicationFlowLaunchDTO launchDTO) {
        if(null != launchDTO.getBeanInfoLaunchDTO() && getCurrentPersonId().equals(launchDTO.getBeanInfoLaunchDTO().getCreateUser())){
            return new JsonObject<>(borrowForSellSealProcessService.launch(launchDTO));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }
}
