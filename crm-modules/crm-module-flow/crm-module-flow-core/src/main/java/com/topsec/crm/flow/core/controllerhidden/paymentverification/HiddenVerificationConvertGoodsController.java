package com.topsec.crm.flow.core.controllerhidden.paymentverification;

import com.topsec.crm.flow.core.service.VerificationConvertGoodsService;
import com.topsec.crm.framework.common.bean.ProcessExtensionInfoVO;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/hidden/verificationConvertGoods")
@RequiredArgsConstructor
@Tag(name = "押金转货款")
public class HiddenVerificationConvertGoodsController {

    private final VerificationConvertGoodsService verificationConvertGoodsService;

    @GetMapping("/getConvertGoodsLaunchInfoList")
    @Operation(hidden = true)
    public JsonObject<List<ProcessExtensionInfoVO>> getConvertGoodsLaunchInfoList(@RequestParam List<String> verificationIds) {
        return new JsonObject<>(verificationConvertGoodsService.getLaunchInfoList(verificationIds));
    }

    @GetMapping("/isHasOngoingConvertGoodsProcessMap")
    @Operation(hidden = true)
    public JsonObject<Map<String,Boolean>> isHasOngoingConvertGoodsProcessMap(@RequestParam List<String> verificationIds) {
        return new JsonObject<>(verificationConvertGoodsService.isHasOngoingConvertGoodsProcessMap(verificationIds));
    }

    @GetMapping("/isHasConvertGoodsProcessMap")
    @Operation(hidden = true)
    public JsonObject<Map<String,Boolean>> isHasConvertGoodsProcessMap(@RequestParam List<String> verificationIds) {
        return new JsonObject<>(verificationConvertGoodsService.isHasConvertGoodsProcessMap(verificationIds));
    }

}
