package com.topsec.crm.flow.core.controller.impunity;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.util.WebFilenameUtils;
import com.topsec.crm.flow.api.dto.contractSuspensionOfReimbursement.ContractSuspensionOfReimbursementQuery;
import com.topsec.crm.flow.api.dto.contractSuspensionOfReimbursement.ContractSuspensionOfReimbursementVO;
import com.topsec.crm.flow.api.dto.impunity.*;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.process.impl.ImpunityProcessService;
import com.topsec.crm.flow.core.service.ContractSuspensionOfReimbursementService;
import com.topsec.crm.flow.core.service.ImpunityService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.utils.AuthorizeUtil;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/impunity")
@Tag(name = "免罚申请", description = "免罚申请")
@RequiredArgsConstructor
@Slf4j
public class ImpunityController {


    private final ImpunityService impunityService;
    private final ImpunityProcessService impunityProcessService;
    private final ContractSuspensionOfReimbursementService contractSuspensionOfReimbursementService;

    @PostMapping("/export")
    @Operation(summary = "导出免罚申请")
    @PreAuthorize(hasPermission = "crm_contract_receivable_impunity_export")
    public void export(@RequestBody ImpunityQuery query, HttpServletResponse response){
        ExportParams exportParam=new ExportParams();
        exportParam.setSheetName("免罚申请");
        exportParam.setTitle("免罚申请");
        exportParam.setType(ExcelType.XSSF);
        query.setPageNum(1);
        query.setPageSize(Integer.MAX_VALUE);
        PageUtils<ImpunityContractVO> page = impunityService.page(query, UserInfoHolder.getCurrentAudience());
        List<ImpunityContractExcelVO> list = Optional.ofNullable(page.getList()).orElse(Collections.emptyList())
                .stream()
                .map(item -> {
                    return HyperBeanUtils.copyPropertiesByJackson(item, ImpunityContractExcelVO.class);
                })
                .collect(Collectors.toList());
        try (Workbook workbook = ExcelExportUtil.exportExcel(exportParam, ImpunityContractExcelVO.class, list)){
            response.addHeader(HttpHeaders.CONTENT_DISPOSITION, WebFilenameUtils.disposition("免罚申请.xlsx"));
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            workbook.write(response.getOutputStream());
        }catch (Exception e){
            log.error("导出失败",e);
            throw new CrmException("导出失败", ResultEnum.FAIL.getResult(), e);
        }
    }

    // 该页面为免罚待办处理页面，销售不可见，上级领导可以看所属及管辖部门的，商务查看管辖部门的，合管可以看所有的；
    @PostMapping("/page")
    @Operation(summary = "免罚申请分页")
    @PreAuthorize(hasPermission = "crm_contract_receivable_impunity",dataScope = "crm_contract_receivable_impunity")
    public JsonObject<PageUtils<ImpunityContractVO>> page(@RequestBody ImpunityQuery query){
        Set<String> deptSet = AuthorizeUtil.mergeDeptIds(Stream.ofNullable(query.getDeptId()).collect(Collectors.toSet()));
        query.setDeptIds(deptSet);
        return new JsonObject<>(impunityService.page(query, UserInfoHolder.getCurrentAudience()));
    }



    @PostMapping("/launch")
    @Operation(summary = "发起免罚申请")
    @PreAuthorize(hasPermission = "crm_contract_receivable_impunity")
    public JsonObject<Boolean> launch(@RequestBody @Valid ImpunityLaunchableDTO launchableDTO){
        return new JsonObject<>(impunityProcessService.launch(launchableDTO));
    }

    @Operation(summary = "填写免罚信息")
    @PostMapping("/fill")
    @PreFlowPermission
    public JsonObject<Boolean> fill(@RequestBody @Valid ImpunityFillVO fillVO){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        fillVO.setProcessInstanceId(processInstanceId);
        return new JsonObject<>(impunityService.fill(fillVO));
    }

    @Operation(summary = "免罚申请详情")
    @PostMapping("/detail")
    @PreFlowPermission
    public JsonObject<ImpunityVO> detail(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(impunityService.detail(processInstanceId));
    }

    @PostMapping("/pageImpunityContract")
    @Operation(summary = "免罚合同分页")
    @PreAuthorize(hasPermission = "crm_contract_receivable_impunity",dataScope = "crm_contract_receivable_impunity")
    public JsonObject<PageUtils<ContractSuspensionOfReimbursementVO>> page(@RequestBody ContractSuspensionOfReimbursementQuery query){
        Set<String> deptIds = AuthorizeUtil.mergeDeptIds(Stream.ofNullable(query.getContractOwnerDeptId()).collect(Collectors.toSet()));
        query.setContractOwnerDeptIds(deptIds);
        return new JsonObject<>(contractSuspensionOfReimbursementService.page(query));
    }



    @GetMapping("/queryDeptIdList")
    @Operation(summary = "免罚合同-查询存在暂停报销的部门id列表")
    @PreAuthorize(hasPermission = "crm_contract_receivable_impunity",dataScope = "crm_contract_receivable_impunity")
    public JsonObject<Set<String>> queryDeptIdList(@Schema(hidden = true) BaseEntity baseEntity){
        Set<String> personIds = Optional.ofNullable(baseEntity.getDataScopeParam())
                .map(DataScopeParam::getPersonIdList)
                .orElse(Collections.emptySet());
        Map<String, String> pdMap = contractSuspensionOfReimbursementService.queryPersonIdAndDeptId();

        if (CollectionUtils.isNotEmpty(personIds)){
            Iterator<Map.Entry<String, String>> iterator = pdMap.entrySet().iterator();
            while (iterator.hasNext()){
                Map.Entry<String, String> entry = iterator.next();
                String personId = entry.getKey();
                if (!personIds.contains(personId)){
                    iterator.remove();
                }
            }
        }
        Collection<String> deptIds = pdMap.values();
        return new JsonObject<>(new HashSet<>(deptIds));
    }

}
