package com.topsec.crm.flow.core.controller.costPayment;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.api.dto.costContarct.CostContractAttachmentInfoDTO;
import com.topsec.crm.flow.api.dto.costPayment.CostPaymentFlowLauchDTO;
import com.topsec.crm.flow.api.dto.costPayment.VO.CostContractInfoVo;
import com.topsec.crm.flow.api.dto.costPayment.VO.CostPaymentInfoVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CostContract;
import com.topsec.crm.flow.core.entity.CostContractAttachment;
import com.topsec.crm.flow.core.entity.CostContractOriginal;
import com.topsec.crm.flow.core.entity.CostPayment;
import com.topsec.crm.flow.core.process.impl.CostPaymentProcessService;
import com.topsec.crm.flow.core.service.CostContractAttachmentService;
import com.topsec.crm.flow.core.service.CostContractService;
import com.topsec.crm.flow.core.service.CostPaymentService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;

import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@RestController
@RequestMapping("/costPayment")
@Tag(name = "【费用支付-流程接口】", description = "costPayment")
@RequiredArgsConstructor
@Validated
public class CostPaymentController extends BaseController {

    private final CostPaymentService costPaymentService;

    private final CostPaymentProcessService costPaymentProcessService;


    private final CostContractAttachmentService costContractAttachmentService;

    @PreAuthorize(hasPermission="crm_flow_cost_payment")
    @PostMapping("/launch")
    @Operation(summary = "发起费用支付")
    public JsonObject<Boolean> launch(@Valid @RequestBody CostPaymentFlowLauchDTO flowLauchDTO){
        return new JsonObject<>(costPaymentProcessService.launch(flowLauchDTO));
    }

    @PreFlowPermission
    @GetMapping("/getPaymentInfoByProcessInstanceId")
    @Operation(summary = "费用支付详情查询(流程ID)")
    public JsonObject<CostPaymentInfoVO> getPaymentInfoByProcessInstanceId(String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(costPaymentService.getPaymentInfoByProcessInstanceId(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/getAttachmentInfoByPaymentId")
    @Operation(summary = "费用支付合同原件附件查询")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getAttachmentInfoByPaymentId(@RequestParam String paymentId){
        CrmAssert.hasText(paymentId,"ID不能为空");
        Optional<CostPayment> optional = Optional.ofNullable(costPaymentService.getById(paymentId));
        if (optional.isPresent()){
            CostPayment costPayment = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(costPayment.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractAttachmentService.getByPaymentId(paymentId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }

    }

    @PreFlowPermission
    @GetMapping("/getPaymentMoneyByProcessInstanceId")
    @Operation(summary = "校验费用支付金额是否超过项目中外包服务费(费用备案自动生成)")
    public JsonObject<Boolean> getPaymentMoneyByProcessInstanceId(@RequestParam("processInstanceId") String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(costPaymentService.getPaymentMoneyByProcessInstanceId(processInstanceId));
    }

    @PreFlowPermission
    @PostMapping("/savePaymentAttachment")
    @Operation(summary = "新增费用支付附件")
    public JsonObject<Boolean> saveAttachment(@RequestBody CostContractAttachmentInfoDTO attachmentInfoDTO){
        CrmAssert.hasText(attachmentInfoDTO.getCostId(),"主表ID不能为空");
        Optional<CostPayment> optional = Optional.ofNullable(costPaymentService.getById(attachmentInfoDTO.getCostId()));
        if (optional.isPresent()){
            CostPayment costPayment = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(costPayment.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            attachmentInfoDTO.setCostType("费用支付");
            return new JsonObject<>(costContractAttachmentService.saveAttachment(attachmentInfoDTO));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission
    @GetMapping("/deletePaymentAttachment")
    @Operation(summary = "删除费用支付附件信息")
    public JsonObject<Boolean> deleteAttachment(@RequestParam String id){
        Optional<CostContractAttachment> optional = Optional.ofNullable(costContractAttachmentService.getById(id));
        if (optional.isPresent()){
            CostPayment costPayment = costPaymentService.getById(optional.get().getCostId());
            PreFlowPermissionAspect.checkProcessInstanceId(costPayment.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractAttachmentService.deleteAttachment(id));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission
    @GetMapping("/getAttachmentByProcessInstanceId")
    @Operation(summary = "查询费用支付附件信息")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getAttachmentByProcessInstanceId(@RequestParam String processInstanceId,@RequestParam(required = false) String isFinalQuery){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        Optional<CostPayment> optional = Optional.ofNullable(costPaymentService.getOne(new QueryWrapper<CostPayment>().lambda()
                .eq(CostPayment::getDelFlag,0).eq(CostPayment::getProcessInstanceId,processInstanceId)));
        if (optional.isPresent()){
            CostPayment costPayment = optional.get();
//            CostContract costContract = costContractService.getById(costContractOriginal.getCostContractId());
            return new JsonObject<>(costContractAttachmentService.getByCostContractId(costPayment.getId(),isFinalQuery));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

}
