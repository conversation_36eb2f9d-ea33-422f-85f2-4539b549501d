package com.topsec.crm.flow.core.controller.contractreview;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewSignContractDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.dto.contractreview.request.ContractOwnQuery;
import com.topsec.crm.flow.api.dto.contractreview.request.ContractPageQuery;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ContractReviewAttachment;
import com.topsec.crm.flow.core.entity.ContractReviewProductOwn;
import com.topsec.crm.flow.core.process.impl.ContractReviewProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 合同评审
 *
 * <AUTHOR>
 * @date 2024/7/26 17:28
 */
@RestController
@RequestMapping("/contractReview")
@Tag(name = "合同评审流程相关", description = "/contractReview")
@RequiredArgsConstructor
@Validated
public class ContractReviewProcessController extends BaseController {

    private final ContractReviewProcessService contractReviewProcessService;

    private final ContractReviewMainService contractReviewMainService;

    private final ContractReviewFlowService contractReviewFlowService;

    private final ContractReviewRevenueRecognitionService contractReviewRevenueRecognitionService;

    private final ContractReviewProductOwnService contractReviewProductOwnService;

    private final ContractReviewProductThirdService contractReviewProductThirdService;

    private final ContractReviewSignContractService contractReviewSignContractService;

    private final ContractReviewAttachmentService contractReviewAttachmentService;
    private final RemoteProjectDirectlyClient projectDirectlyClient;
    private final RedissonClient redissonClient;

    private static final String REDIS_KEY_PREFIX = "contractReview:";

    @PostMapping("/launch")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    @Operation(summary = "提交合同评审流程")
    public JsonObject<Boolean> launch(@RequestBody ContractReviewMainFlowLaunchDTO contractReviewMainBaseInfoDTO) {
        assert contractReviewMainBaseInfoDTO != null;
        String contractId = contractReviewMainBaseInfoDTO.getBaseInfoDTO().getId();
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        if (contractInfo == null) {
            throw new CrmException("该合同信息不存在");
        }
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        if (!currentPersonId.equals(contractInfo.getCreateUser())) {
            // 不一样，直接返回无权限
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        contractReviewMainBaseInfoDTO.setPersonId(UserInfoHolder.getCurrentPersonId());
        contractReviewMainBaseInfoDTO.setLaunchFlag(true);
        String redisKey = REDIS_KEY_PREFIX + contractId;
        RLock rlock = redissonClient.getLock(redisKey);
        try {
            rlock.lock();
            boolean launch = contractReviewProcessService.launch(contractReviewMainBaseInfoDTO);
            return new JsonObject<>(launch);
        } finally {
            rlock.unlock();
        }
    }

    @PostMapping("/changeRemindClassification")
    @PreFlowPermission
    @Operation(summary = "修改催款分类")
    public JsonObject<Boolean> changeRemindClassification(@RequestBody ContractReviewSignContractDTO contractReviewSignContractDTO){
        checkFlowAuthByContractId(contractReviewSignContractDTO.getId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewSignContractService.changeRemindClassification(contractReviewSignContractDTO));
    }

    @PostMapping("/changeContractNumber")
    @PreFlowPermission
    @Operation(summary = "修改合同号")
    public JsonObject<Boolean> changeContractNumber(@RequestBody ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO) {
        checkFlowAuthByContractId(contractReviewMainBaseInfoDTO.getId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewMainService.changeContractNumber(contractReviewMainBaseInfoDTO));
    }

    @PostMapping("/changesBusinessType")
    @PreFlowPermission
    @Operation(summary = "修改业务类型")
    public JsonObject<Boolean> changesBusinessType(@RequestBody ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO) {
        checkFlowAuthByContractId(contractReviewMainBaseInfoDTO.getId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewMainService.changesBusinessType(contractReviewMainBaseInfoDTO));
    }

    @PostMapping("/changeIsClassified")
    @PreFlowPermission
    @Operation(summary = "修改是否涉密")
    public JsonObject<Boolean> changeIsClassified(@RequestBody ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO) {
        checkFlowAuthByContractId(contractReviewMainBaseInfoDTO.getId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewMainService.changeIsClassified(contractReviewMainBaseInfoDTO));
    }

    @PostMapping("/changeOwnPeriod")
    @PreFlowPermission
    @Operation(summary = "自有产品修改保修期")
    public JsonObject<Boolean> changePeriod(@RequestBody List<ContractProductOwnDTO> contractProductOwnDTOS) {
        if (CollectionUtils.isNotEmpty(contractProductOwnDTOS)) {
            Set<String> contractIds = contractProductOwnDTOS.stream().map(ContractProductOwnDTO::getContractReviewId).filter(Objects::nonNull).collect(Collectors.toSet());
            checkFlowAuthByContractIds(contractIds, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }
        return new JsonObject<>(contractReviewProductOwnService.changePeriod(contractProductOwnDTOS));
    }

    @PostMapping("/changeThirdPeriod")
    @PreFlowPermission
    @Operation(summary = "修改第三方产品修改保修期")
    public JsonObject<Boolean> changeThirdPeriod(@RequestBody List<ContractProductThirdDTO> contractProductThirdDTOS) {
        if (CollectionUtils.isNotEmpty(contractProductThirdDTOS)) {
            Set<String> contractIds = contractProductThirdDTOS.stream().map(ContractProductThirdDTO::getContractReviewId).filter(Objects::nonNull).collect(Collectors.toSet());
            checkFlowAuthByContractIds(contractIds, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }
        return new JsonObject<>(contractReviewProductThirdService.changePeriod(contractProductThirdDTOS));
    }


    @PostMapping("/tabTaxLoss")
    @PreFlowPermission
    @Operation(summary = "标记税损")
    public JsonObject<List<ContractProductOwnDTO>> tabTaxLoss(@RequestBody List<String> productOwnIds) {
        if (CollectionUtils.isNotEmpty(productOwnIds)) {
            List<ContractReviewProductOwn> list = contractReviewProductOwnService.list(new LambdaQueryWrapper<ContractReviewProductOwn>().in(ContractReviewProductOwn::getId, productOwnIds)
                    .eq(ContractReviewProductOwn::getDelFlag, 0));
            Set<String> contractIds = list.stream().filter(item -> item.getContractReviewId() != null).map(ContractReviewProductOwn::getContractReviewId).collect(Collectors.toSet());
            checkFlowAuthByContractIds(contractIds, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }
        return new JsonObject<>(contractReviewFlowService.tabTaxLoss(productOwnIds));
    }

    @PostMapping("/cancelTaxLoss")
    @PreFlowPermission
    @Operation(summary = "取消标记税损")
    public JsonObject<Boolean> cancelTaxLoss(@RequestBody List<String> productOwnIds) {
        if (CollectionUtils.isNotEmpty(productOwnIds)) {
            List<ContractReviewProductOwn> list = contractReviewProductOwnService.list(new LambdaQueryWrapper<ContractReviewProductOwn>().in(ContractReviewProductOwn::getId, productOwnIds)
                    .eq(ContractReviewProductOwn::getDelFlag, 0));
            Set<String> contractIds = list.stream().filter(item -> item.getContractReviewId() != null).map(ContractReviewProductOwn::getContractReviewId).collect(Collectors.toSet());
            checkFlowAuthByContractIds(contractIds, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }
        return new JsonObject<>(contractReviewFlowService.cancelTaxLoss(productOwnIds));
    }

    @GetMapping("/tabFinalContractFile")
    @PreFlowPermission
    @Operation(summary = "标记终稿")
    public JsonObject<Boolean> tabFinalContractFile(@RequestParam String id, @RequestParam Boolean isFinal) {
        ContractReviewAttachment attachment = contractReviewAttachmentService.getById(id);
        if (attachment == null || attachment.getDelFlag() == 1) {
            throw new CrmException("附件不存在");
        }
        checkFlowAuthByContractId(attachment.getContractReviewMainId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewAttachmentService.tabFinalContractFile(id, isFinal));
    }

    @GetMapping("/hasDefaultConfirm")
    @PreFlowPermission
    @Operation(summary = "产品是否缺少默认的收入确认")
    public JsonObject<Boolean> hasDefaultConfirm(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.hasDefaultConfirm(contractId));
    }

    @GetMapping("/hasRowType")
    @PreFlowPermission
    @Operation(summary = "除重点行业的合同，产品是否缺少行类型")
    public JsonObject<Boolean> hasRowType(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.hasRowType(contractId));
    }

    @GetMapping("/productValidStraightLine")
    @PreFlowPermission
    @Operation(summary = "产品明细和直线摊销时间的校验")
    public JsonObject<Boolean> productValidStraightLine(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<ContractProductOwnDTO> owns = contractReviewProductOwnService.productOwnInfoTileByContractId(contractId, true);
        List<ContractProductThirdDTO> thirds = contractReviewProductThirdService.productThirdInfoByContractId(contractId, true);
        return new JsonObject<>(contractReviewRevenueRecognitionService.productValidStraightLine(contractId, owns, thirds));
    }

    @GetMapping("/isPaper")
    @PreFlowPermission
    @Operation(summary = "合同是否为纸合同")
    public JsonObject<Boolean> isPaper(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.isPaper(contractId));
    }

    @GetMapping("/isStandard")
    @PreFlowPermission
    @Operation(summary = "合同是否为标准合同")
    public JsonObject<Boolean> isStandard(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.isStandard(contractId));
    }

    @GetMapping("/isRegional")
    @PreFlowPermission
    @Operation(summary = "合同的销售人员是否为区域销售")
    public JsonObject<Boolean> isRegional(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.isRegional(contractId));
    }

    @PostMapping("/hasSpecialCode")
    @PreFlowPermission
    @Operation(summary = "合同是否含有特殊代码")
    public JsonObject<Boolean> hasSpecialCode(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.hasSpecialCode(processInstanceId));
    }

    @PostMapping("/checkEmptySpecialCode")
    @PreFlowPermission
    @Operation(summary = "是否有特殊代码没填写盈亏或驻场服务")
    public JsonObject<Boolean> checkEmptySpecialCode(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.checkEmptySpecialCode(processInstanceId));
    }


    @GetMapping("/pageByProjectIdAndContractState")
    @Operation(summary = "根据项目id和合同状态查询合同评审条款")
    @PreAuthorize
    public JsonObject<TableDataInfo> pageByProjectIdAndContractState(ContractPageQuery query) {
        if (!projectDirectlyClient.hasRight(query.getProjectId(), UserInfoHolder.getCurrentPersonId()).getObjEntity()){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        };
        startPage();
        return new JsonObject<>(contractReviewFlowService.pageByProjectIdAndContractState(query));
    }

    @GetMapping("/checkHasZeroMoney")
    @PreFlowPermission
    public JsonObject<Boolean> checkHasZeroMoney(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.checkHasZeroMoney(contractId));
    }

    private void checkFlowAuthByContractId(String contractId, String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        if (contractInfo == null) {
            throw new CrmException("该合同信息不存在");
        }
        String processInstanceId1 = contractInfo.getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId1, processInstanceId);
    }

    private void checkFlowAuthByContractIds(Set<String> contractIds, String processInstanceId) {
        if (contractIds.size() > 1) {
            throw new CrmException("参数有误，请检查参数");
        }
        String contractId = contractIds.stream().findFirst().orElseThrow(() -> new CrmException("参数有误，请检查参数"));
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
    }

}
