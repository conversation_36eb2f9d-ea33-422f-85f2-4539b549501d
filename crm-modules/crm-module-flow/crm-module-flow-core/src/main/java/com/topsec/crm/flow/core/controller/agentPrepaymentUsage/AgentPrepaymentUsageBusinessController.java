package com.topsec.crm.flow.core.controller.agentPrepaymentUsage;

import com.topsec.crm.contract.api.RemoteCrmAgentPaymentVerificationService;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.AgentPaymentHiddenPageQuery;
import com.topsec.crm.contract.api.entity.crmagentpayment.agentpaymentverification.AgentPrePaymentVerificationVO;
import com.topsec.crm.flow.api.dto.agentPrepaymentUsage.AgentPrepaymentUsageDTO;
import com.topsec.crm.flow.api.dto.agentPrepaymentUsage.AgentPrepaymentUsageQuery;
import com.topsec.crm.flow.core.service.AgentPrepaymentUsageService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/agentPrepaymentUsage")
@Tag(name = "预付款使用-业务")
public class AgentPrepaymentUsageBusinessController {
    @Autowired
    private AgentPrepaymentUsageService agentPrepaymentUsageService;
    @Autowired
    private RemoteCrmAgentPaymentVerificationService remoteCrmAgentPaymentVerificationService;

    @PostMapping("/paymentPage")
    @Operation(summary = "预付款使用列表")
    @PreAuthorize(hasPermission = "crm_agent_repayment_use",dataScope = "crm_agent_repayment_use")
    public JsonObject<PageUtils<AgentPrepaymentUsageDTO>> paymentPage(@RequestBody AgentPrepaymentUsageQuery query) {
        return new JsonObject<>(agentPrepaymentUsageService.prepaymentUsagePage(query));
    }
    @GetMapping("/paymentDetail")
    @Operation(summary = "预付款使用-详情")
    @PreAuthorize(hasPermission = "crm_agent_repayment_use",dataScope = "crm_agent_repayment_use")
    public JsonObject<AgentPrepaymentUsageDTO> paymentDetail(String processInstanceId) {
        AgentPrepaymentUsageDTO prepaymentUsageDTO = agentPrepaymentUsageService.prepaymentUsageDetail(processInstanceId);
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(),prepaymentUsageDTO.getCreateUser());
        return new JsonObject<>(prepaymentUsageDTO);
    }

    @PostMapping("/verificationPrePage")
    @Operation(summary = "回款核销预付款分页列表")
    @PreAuthorize(hasPermission = "crm_agent_repayment_use_add",dataScope = "crm_agent_repayment_use_add")
    public JsonObject<PageUtils<AgentPrePaymentVerificationVO>> verificationPrePage(@RequestBody AgentPaymentHiddenPageQuery query){
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        query.setRoles(UserInfoHolder.getLoginInfo().getRoles());
        query.setDataScopeParam(dataScopeParam);
        JsonObject<PageUtils<AgentPrePaymentVerificationVO>> pageUtilsJsonObject = remoteCrmAgentPaymentVerificationService.verificationPrePage(query);
        if (pageUtilsJsonObject.isSuccess()) {
            PageUtils<AgentPrePaymentVerificationVO> pageUtils = pageUtilsJsonObject.getObjEntity();
            pageUtils.convert(vo -> {
                vo.setPrepaymentAmount(vo.getRemainingPrepaymentAmount().subtract(agentPrepaymentUsageService.getUnfinishedProcessVerificationAmount(vo.getVerificationId())));
                return vo;
            });
            return new JsonObject<>(pageUtils);
        }
        return new JsonObject<>();
    }
}
