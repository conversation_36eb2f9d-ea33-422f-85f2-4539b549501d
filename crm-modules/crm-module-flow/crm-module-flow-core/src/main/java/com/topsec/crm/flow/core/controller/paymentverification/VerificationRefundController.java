package com.topsec.crm.flow.core.controller.paymentverification;

import com.topsec.crm.flow.api.dto.paymentverification.VerificationRefundFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.process.impl.VerificationRefundProcessService;
import com.topsec.crm.flow.core.service.VerificationRefundService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/verificationRefund")
@Tag(name = "回款核销")
@RequiredArgsConstructor
@Validated
public class VerificationRefundController extends BaseController {

    private final VerificationRefundProcessService processService;
    private final VerificationRefundService refundService;

    @PostMapping("/launch")
    @Operation(summary = "发起退款流程")
    @PreAuthorize(hasPermission = "crm_repayment_verify_prepayment_refund")
    public JsonObject<Boolean> launch(@RequestBody VerificationRefundFlowLaunchDTO req){
        return new JsonObject<>(processService.launch(req));
    }

    @GetMapping("/getLaunchInfo")
    @Operation(summary = "获取退款流程信息")
    @PreFlowPermission
    public JsonObject<VerificationRefundFlowLaunchDTO> getLaunchInfo(@RequestParam String processInstanceId){
        processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        return new JsonObject<>(refundService.getLaunchInfo(processInstanceId));
    }

}
