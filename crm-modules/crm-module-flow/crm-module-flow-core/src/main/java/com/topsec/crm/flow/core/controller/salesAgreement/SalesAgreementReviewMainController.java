package com.topsec.crm.flow.core.controller.salesAgreement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.topsec.crm.flow.api.vo.ProcessAttachmentVo;
import com.topsec.crm.flow.api.vo.salesAgreement.*;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.SalesAgreementReviewMain;
import com.topsec.crm.flow.core.process.impl.SalesAgreementPriceProcessService;
import com.topsec.crm.flow.core.process.impl.SalesAgreementReviewProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.PageDomain;
import com.topsec.crm.framework.common.web.page.TableSupport;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.project.api.RemoteProjectDirectlyService;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.ExecutionException;


/**
 * 销售协议流程主表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-31 10:52:58
 */
@RestController
@RequestMapping("/salesAgreement")
@Tag(name = "【销售协议主流程】", description = "salesAgreement")
@RequiredArgsConstructor
@Validated
public class SalesAgreementReviewMainController extends BaseController {

    private final SalesAgreementReviewMainService salesAgreementReviewMainService;



    private final SalesAgreementPriceReviewMainService salesAgreementPriceReviewMainService;

    private final SalesAgreementMembersService salesAgreementMembersService;

    private final  SalesAgreementMembersReviewMainService salesAgreementMembersReviewMainService;

    private final SalesAgreementProductService salesAgreementProductService;

    private final ThreeProcurementPaymentReviewMainService threeProcurementPaymentReviewMainService;

    private final SalesAgreementPriceProcessService salesAgreementPriceProcessService;

    private final SalesAgreementReviewProcessService salesAgreementReviewProcessService;

    private final RemoteProjectDirectlyService remoteProjectDirectlyService;



    @PostMapping("/launch")
    @Operation(summary = "发起销售协议流程")
    @PreAuthorize(hasPermission = "crm_sales_agreements_add")
    public JsonObject<Boolean> launch(@Valid @RequestBody SalesAgreementReviewMainLaunchVo salesAgreementReviewMainLaunchVo) {
        return new JsonObject<>(salesAgreementReviewProcessService.launch(salesAgreementReviewMainLaunchVo));
    }





    @GetMapping("/querySalesAgreementBaseInfo")
    @Operation(summary = "销售协议基本信息")
    @PreFlowPermission
    public JsonObject<SalesAgreementReviewMainVo> querySalesAgreementBaseInfo(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(processInstanceId);
        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(salesAgreement, SalesAgreementReviewMainVo.class));
    }

    @GetMapping("/querySalesAgreementMembersList")
    @Operation(summary = "查询销售协议主流程协议成员列表")
    @PreFlowPermission
    public JsonObject<List<SalesAgreementMembersVo> > querySalesAgreementMembersList(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<SalesAgreementMembersVo> salesAgreementMembersVoList = salesAgreementMembersService.convertSalesAgreementMembersList(processInstanceId);
        return new JsonObject<>(salesAgreementMembersVoList);
    }

    @GetMapping("/querySalesAgreementProductList")
    @Operation(summary = "查询销售协议主流程自有产品")
    @PreFlowPermission
    public JsonObject<SalesAgreementOwnProductVo> querySalesAgreementProductList(@RequestParam String processInstanceId) throws ExecutionException {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        SalesAgreementOwnProductVo salesAgreementOwnProductVo = salesAgreementProductService.convertToSalesAgreementOwnProductVo(processInstanceId,true);
        return new JsonObject<>(salesAgreementOwnProductVo);
    }

    @PostMapping("/querySalesAgreementPriceProcessPage")
    @Operation(summary = "分页查询协议价格审批")
    @PreFlowPermission
    public JsonObject<PageUtils<SalesAgreementProcessVo>> querySalesAgreementPriceProcessPage(@RequestBody SalesAgreementProcessQuery salesAgreementProcessQuery) {
        String parentProcessInstanceId = salesAgreementProcessQuery.getParentProcessInstanceId();
        CrmAssert.hasText(parentProcessInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(parentProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        IPage<SalesAgreementProcessVo> page = salesAgreementPriceReviewMainService.findSalesAgreementPriceProcessPage(salesAgreementProcessQuery)
                .convert(crmThreeProcurement -> HyperBeanUtils.copyPropertiesByJackson(crmThreeProcurement, SalesAgreementProcessVo.class));
        NameUtils.setName(page.getRecords());
        return new JsonObject<>(new PageUtils<>(page));
    }



    @PostMapping("/querySalesAgreementMembersProcessPage")
    @Operation(summary = "查询协议成员流程审批")
    @PreFlowPermission
    public JsonObject<PageUtils<SalesAgreementProcessVo>> querySalesAgreementMembersProcessPage(@RequestBody SalesAgreementProcessQuery salesAgreementProcessQuery) {
        String parentProcessInstanceId = salesAgreementProcessQuery.getParentProcessInstanceId();
        CrmAssert.hasText(parentProcessInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(parentProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        IPage<SalesAgreementProcessVo> page = salesAgreementMembersReviewMainService.findSalesAgreementMembersPage(salesAgreementProcessQuery)
                .convert(crmThreeProcurement -> HyperBeanUtils.copyPropertiesByJackson(crmThreeProcurement, SalesAgreementProcessVo.class));
        NameUtils.setName(page.getRecords());
        return new JsonObject<>(new PageUtils<>(page));
    }


    @GetMapping("/selectSalesAgreementAttachmentList")
    @Operation(summary = "查询销售协议以及子流程附件列表  type: 0 销售协议主流程 1：协议价格审批 2：协议成员变更")
    @PreFlowPermission
    public JsonObject<List<ProcessAttachmentVo>> selectSalesAgreementAttachmentList(@RequestParam String processInstanceId, @RequestParam int type) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.getProcessInstanceId(processInstanceId, type);
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<ProcessAttachmentVo> listThreeProcurementAttachment = threeProcurementPaymentReviewMainService.getListThreeProcurementAttachment(salesAgreement.getAttachmentIds());
        return new JsonObject<>(listThreeProcurementAttachment);
    }

    @PostMapping("/updateSalesAgreement")
    @Operation(summary = "修改协议信息")
    @PreFlowPermission(hasAnyNodes = {"salesAgreement_01","salesAgreement_03"})
    public JsonObject<Boolean> updateSalesAgreement(@RequestBody SalesAgreementReviewMainVo salesAgreementReviewMainVo){
        CrmAssert.notNull(salesAgreementReviewMainVo, "参数不能为空");
        CrmAssert.hasText(salesAgreementReviewMainVo.getProcessInstanceId(), "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(salesAgreementReviewMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        SalesAgreementReviewMain salesAgreementReviewMain = HyperBeanUtils.copyPropertiesByJackson(salesAgreementReviewMainVo, SalesAgreementReviewMain.class);
        return new JsonObject<>(salesAgreementReviewMainService.updateById(salesAgreementReviewMain));
    }



    @PostMapping("/checkSalesAgreement")
    @Operation(summary = "销售协议流程校验")
    @PreAuthorize
    public JsonObject<Void>  checkSalesAgreement(@Validated @RequestBody  SalesAgreementProductVerifyVo salesAgreementProductVerifyVo){
        return salesAgreementPriceProcessService.checkBeforeFillingJsonObj(salesAgreementProductVerifyVo);
    }



    @PostMapping("/pageOwnProducts")
    @Operation(summary = "销售协议流程详情分页查询公司产品")
    @PreFlowPermission
    public JsonObject<PageUtils<CrmProductVo>> pageOwnProducts(@RequestBody CrmProductVo crmProductVo)
    {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        return remoteProjectDirectlyService.pageOwnProducts(pageNum,pageSize,crmProductVo);
    }




    @GetMapping("/querySalesAgreementProjects")
    @Operation(summary = "销售协议关联项目", method = "GET")
    @PreFlowPermission
    public JsonObject<List<SalesAgreementProjectVo>> querySalesAgreementProjects(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        SalesAgreementReviewMain agreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(processInstanceId);
        if (agreement == null) {
            throw new CrmException("销售协议不存在");
        }
        List<SalesAgreementProjectVo> salesAgreementProjectVos = salesAgreementReviewMainService.selectSalesAgreementProjects(agreement.getId());
        return new JsonObject<>(salesAgreementProjectVos);

    }



}
