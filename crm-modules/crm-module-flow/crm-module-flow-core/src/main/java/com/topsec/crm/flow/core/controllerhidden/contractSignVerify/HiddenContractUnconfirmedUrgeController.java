package com.topsec.crm.flow.core.controllerhidden.contractSignVerify;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.topsec.crm.account.api.client.RemoteAccountService;
import com.topsec.crm.account.api.dto.CrmDeptAssistantDTO;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedUrgeVo;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUrgeSignVerifyFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUrgeSignVerifySonFlowLaunchDTO;
import com.topsec.crm.flow.core.entity.ContractUnconfirmed;
import com.topsec.crm.flow.core.entity.ContractUnconfirmedUrge;
import com.topsec.crm.flow.core.process.impl.ContractUrgeSignVerifyProcessService;
import com.topsec.crm.flow.core.process.impl.ContractUrgeSignVerifySonProcessService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedUrgeService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.date.DateUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.msg.api.client.NotifyClient;
import com.topsec.msg.common.constants.MessageConstant;
import com.topsec.msg.common.dto.EmailCommon;
import com.topsec.msg.common.vo.NotifyMessageVo;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/hidden/contractUnconfirmedUrge")
@Tag(name = "催交签验收单明细列表 相关Controller", description = "/contractUnconfirmedUrge")
@RequiredArgsConstructor
@Validated
public class HiddenContractUnconfirmedUrgeController extends BaseController {
    @Autowired
    private IContractUnconfirmedService contractUnconfirmedService;
    @Autowired
    private IContractUnconfirmedUrgeService contractUnconfirmedUrgeService;
    @Autowired
    private RemoteContractExecuteService remoteContractExecuteService;
    @Autowired
    private ContractUrgeSignVerifyProcessService contractUrgeSignVerifyProcessService;
    @Autowired
    private ContractUrgeSignVerifySonProcessService contractUrgeSignVerifySonProcessService;
    @Autowired
    private RemoteAccountService remoteAccountService;
    @Autowired
    private NotifyClient notifyClient;

    /**
     * 每月1日，同步未上交签验收单明细
     */
    @PostMapping("/syncUnconfirmedForUrge")
    public JsonObject<Boolean> syncUnconfirmedForUrge() {
        //查询未上交签验收单的数据
        List<ContractUnconfirmed> list = contractUnconfirmedService.query().list();

        //插入列表
        List<ContractUnconfirmedUrge> insertList = new ArrayList<ContractUnconfirmedUrge>();
        if(CollectionUtil.isNotEmpty(list)){
            Set<String> contractNumbers = list.stream().map(ContractUnconfirmed::getContractNumber).collect(Collectors.toSet());
            //查询合同执行信息
            JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(contractNumbers);
            if(byContractNumberBatch.isSuccess()){
                List<CrmContractExecuteVO> contractInfos = byContractNumberBatch.getObjEntity();

                for (ContractUnconfirmed contractUnconfirmed : list) {
                    CrmContractExecuteVO executeVO = contractInfos.stream().filter(c -> c.getContractNumber().equals(contractUnconfirmed.getContractNumber())).findFirst().orElse(null);
                    if(executeVO != null){
                        ContractUnconfirmedUrge in = new ContractUnconfirmedUrge();
                        in.setId(UUID.randomUUID().toString());
                        in.setContractNumber(executeVO.getContractNumber());
                        in.setContractOwnerDeptId(executeVO.getContractOwnerDeptId());
                        in.setContractOwnerDeptName(executeVO.getContractOwnerDeptName());
                        in.setContractOwnerId(executeVO.getContractOwnerId());
                        in.setContractOwnerName(executeVO.getContractOwnerName());
                        in.setProductId(contractUnconfirmed.getProductId());
                        in.setMaterialCode(contractUnconfirmed.getMaterialCode());
                        in.setSpecificationId(contractUnconfirmed.getSpecificationId());
                        in.setPn(contractUnconfirmed.getPn());
                        in.setNumber(contractUnconfirmed.getNumber());
                        in.setDefaultConfirm(contractUnconfirmed.getDefaultConfirm());
                        in.setStage(contractUnconfirmed.getStage());
                        in.setConfirmType(contractUnconfirmed.getConfirmType());
                        in.setFinalCustomerId(executeVO.getFinalCustomerId());
                        in.setFinalCustomerName(executeVO.getFinalCustomerName());
                        in.setEstimatedTime(executeVO.getEstimatedTime());
                        in.setContractCompanyName(executeVO.getContractCompanyName());
                        in.setCycle(DateUtils.getDate());

                        //TODO minggang 以合同为纬度插入数据，校验是否有存在该数据-暂时按列表随机处理
                        if(CollectionUtil.isEmpty(insertList)) {
                            insertList.add(in);
                        }else{
                            if(!insertList.stream().map(ContractUnconfirmedUrge::getContractNumber).collect(Collectors.toSet()).contains(in.getContractNumber())){
                                insertList.add(in);
                            }
                        }
                    }
                }
                contractUnconfirmedUrgeService.saveBatch(insertList);
            }
        }

        return new JsonObject<>(true);
    }

    /**
     * 每月1日，自动发起子催交签验收单和催交签验收单
     */
    @PostMapping("/syncFlow")
    public JsonObject<Boolean> syncFlow() {
        //查询未上交签验收单的数据
        List<ContractUnconfirmedUrge> urges = contractUnconfirmedUrgeService.query().eq("handler_flag", 0).list();

        //1.-------------->发起催交签验收单
        //数据汇总，以部门纬度
        Map<String,String> temp = new HashMap<String,String>();
        List<ContractUrgeSignVerifyFlowLaunchDTO> fatherDtos = new ArrayList<ContractUrgeSignVerifyFlowLaunchDTO>();
        for (ContractUnconfirmedUrge urge : urges) {
            ContractUrgeSignVerifyFlowLaunchDTO exist = fatherDtos.stream().filter(e -> e.getContractOwnerDeptId().equals(urge.getContractOwnerDeptId())).findFirst().orElse(null);
            if(exist == null){
                ContractUrgeSignVerifyFlowLaunchDTO launchDTO = new ContractUrgeSignVerifyFlowLaunchDTO();
                launchDTO.setContractOwnerDeptId(urge.getContractOwnerDeptId());
                launchDTO.setContractOwnerDeptName(urge.getContractOwnerDeptName());
                fatherDtos.add(launchDTO);
            }
        }
        for (ContractUrgeSignVerifyFlowLaunchDTO launchDTO : fatherDtos) {
            //查询办理人-部门商务
            JsonObject<List<CrmDeptAssistantDTO>> mapJsonObject = remoteAccountService.queryDeptAssistants(launchDTO.getContractOwnerDeptId());
            if (mapJsonObject.isSuccess()) {
                List<CrmDeptAssistantDTO> objEntity = mapJsonObject.getObjEntity();
                if (CollectionUtil.isEmpty(objEntity)) {
                    //1.定义消息实体，设置邮箱标题和内容
                    NotifyMessageVo entity = new NotifyMessageVo();
                    entity.setSubject("催交签验收单发起失败提示");
                    entity.setContent(launchDTO.getContractOwnerDeptName()+"催交签验收单发起失败,需要最少配置一个部门商务");
                    //2.设置消息通知类型-email
                    entity.setNotifyType(MessageConstant.EMAIL);
                    entity.setNotifyCard("tsc_cloud");
                    //3.设置消息接收人列表
                    List<String> tousers = new ArrayList<String>();
                    tousers.add("<EMAIL>");
                    entity.setReceiver(tousers);
                    //4.自定义邮件服务通用数据实体
                    EmailCommon emailCommon = new EmailCommon();
                    entity.setEmailCommon(emailCommon);
                    notifyClient.send(JSONObject.toJSONString(entity));
                }else{
                    launchDTO.setHandleList(objEntity.stream().map(CrmDeptAssistantDTO::getAccountId).collect(Collectors.toList()));
                    String processInstanceId = contractUrgeSignVerifyProcessService.launchAndReturnProcessInstanceId(launchDTO);
                    temp.put(launchDTO.getContractOwnerDeptId(), processInstanceId);
                }
            }
        }

        //2.-------------->发起子催交签验收单
        //数据汇总，以合同负责人纬度和周期（以年月日为标识）纬度
        List<ContractUrgeSignVerifySonFlowLaunchDTO> launchDTOS = new ArrayList<ContractUrgeSignVerifySonFlowLaunchDTO>();
        for (ContractUnconfirmedUrge urge : urges) {
            ContractUnconfirmedUrgeVo urgeVo = HyperBeanUtils.copyPropertiesByJackson(urge, ContractUnconfirmedUrgeVo.class);
            ContractUrgeSignVerifySonFlowLaunchDTO exist = launchDTOS.stream().filter(e ->
                    e.getContractOwnerId().equals(urge.getContractOwnerId()) && e.getCycle().equals(urge.getCycle()))
                .findFirst().orElse(null);
            if(exist == null){
                ContractUrgeSignVerifySonFlowLaunchDTO launchDTO = new ContractUrgeSignVerifySonFlowLaunchDTO();
                launchDTO.setContractOwnerId(urge.getContractOwnerId());
                launchDTO.setParentProcessInstanceId(temp.get(urge.getContractOwnerDeptId()));
                launchDTO.setUnconfirmedUrgeVos(Collections.singletonList(urgeVo));
                launchDTO.setCycle(urge.getCycle());
                launchDTOS.add(launchDTO);
            }else {
                List<ContractUnconfirmedUrgeVo> unconfirmedUrgeVos = exist.getUnconfirmedUrgeVos();
                List<ContractUnconfirmedUrgeVo> newList = CollectionUtil.newCopyOnWriteArrayList(unconfirmedUrgeVos);
                newList.add(urgeVo);
                exist.setUnconfirmedUrgeVos(newList);
            }
        }

        for (ContractUrgeSignVerifySonFlowLaunchDTO launchDTO : launchDTOS) {
            contractUrgeSignVerifySonProcessService.launch(launchDTO);
        }
        return new JsonObject<>(true);
    }

}
