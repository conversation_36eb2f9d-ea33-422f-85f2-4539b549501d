package com.topsec.crm.flow.core.validator.pricereview.condition;

import com.topsec.crm.flow.core.validator.CheckCondition;
import com.topsec.crm.flow.core.validator.pricereview.PriceReviewCheckContext;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.CrmProjectSigningInfoVo;

import java.util.Objects;
import java.util.Optional;

/**
 * 项目采购方式变更
 * <AUTHOR>
 */
public class ProjectProcureTypeChangeCondition implements CheckCondition<PriceReviewCheckContext> {
    @Override
    public boolean check(PriceReviewCheckContext context)  {
        Integer currentType = Optional.ofNullable(context.getProjectDetail().getProjectInfo())
                .map(CrmProjectDirectlyVo::getCrmProjectSigningInfo)
                .map(CrmProjectSigningInfoVo::getProcureType)
                .orElse(null);
        Integer snapshotType = Optional.ofNullable(context.getProjectSnapshot().getPriceReviewMain().getProjectInfo())
                        .map(CrmProjectDirectlyVo::getCrmProjectSigningInfo)
                        .map(CrmProjectSigningInfoVo::getProcureType)
                        .orElse(null);
        return Objects.equals(currentType, snapshotType);
    }
}
