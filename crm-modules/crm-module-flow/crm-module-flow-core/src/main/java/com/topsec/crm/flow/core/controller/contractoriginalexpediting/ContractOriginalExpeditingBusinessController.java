package com.topsec.crm.flow.core.controller.contractoriginalexpediting;


import com.github.pagehelper.PageHelper;
import com.topsec.crm.flow.api.dto.contractexpediting.ContractOriginalApproralDTO;
import com.topsec.crm.flow.api.dto.contractexpediting.ContractOriginalExpeditingDTO;
import com.topsec.crm.flow.api.dto.contractexpediting.vo.OriginaApproralQuery;
import com.topsec.crm.flow.api.dto.contractexpediting.vo.OriginaExpeditingQuery;
import com.topsec.crm.flow.core.service.ContractOriginalExpeditingMainService;
import com.topsec.crm.flow.core.service.ContractOriginalExpeditingService;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 催交合同原件表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@RestController
@RequestMapping("/business/contractExpediting")
@Tag(name = "催交合同原件-业务接口", description = "/business/contractExpediting")
@RequiredArgsConstructor
@Validated
public class ContractOriginalExpeditingBusinessController extends BaseController {

    @Resource
    private ContractOriginalExpeditingService contractOriginalExpeditingService;

    @Resource
    private ContractOriginalExpeditingMainService contractOriginalExpeditingMainService;

    @PreAuthorize(hasPermission = "crm_contract_original_expediting", dataScope = "crm_contract_original_expediting")
    @PostMapping("/page")
    @Operation(summary = "分页查询催交合同原件列表-已经办结", method = "POST")
    public JsonObject<PageUtils<ContractOriginalExpeditingDTO>> pages(@RequestBody OriginaExpeditingQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        PageUtils<ContractOriginalExpeditingDTO> pageUtils = contractOriginalExpeditingService.pages(query);
        //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
        return new JsonObject<>(pageUtils);
    }

    @PreAuthorize(hasPermission = "crm_contract_original_expediting_export", dataScope = "crm_contract_original_expediting_export")
    @PostMapping("/export")
    @Operation(summary = "分页查询催交合同原件列表-已经办结-导出", method = "POST")
    public void export(@RequestBody OriginaExpeditingQuery query) throws Exception {
        List<ContractOriginalExpeditingDTO> contentList = contractOriginalExpeditingService.list(query);
        ExcelUtil<ContractOriginalExpeditingDTO> excelUtil = new ExcelUtil<>(ContractOriginalExpeditingDTO.class);
        excelUtil.exportExcel(response, contentList, "催交合同原件已审核数据");
    }

    @PreAuthorize(hasPermission = "crm_contract_original_expediting")
    @PostMapping("/approvalPages")
    @Operation(summary = "分页查询催交合同原件列表-审核中", method = "POST")
    public JsonObject<PageUtils<ContractOriginalApproralDTO>> approvalPages(@RequestBody OriginaApproralQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        PageUtils<ContractOriginalApproralDTO> dataTable = contractOriginalExpeditingMainService.approvalPages(query);
        return new JsonObject<>(dataTable);
    }

    @PreAuthorize(hasPermission = "crm_contract_original_expediting_export", dataScope = "crm_contract_original_expediting_export")
    @PostMapping("/approvalExport")
    @Operation(summary = "分页查询催交合同原件列表-审核中-导出", method = "POST")
    public void approvalExport(@RequestBody OriginaApproralQuery query) throws Exception {
        List<ContractOriginalApproralDTO> contentList = contractOriginalExpeditingMainService.approvalList(query);
        ExcelUtil<ContractOriginalApproralDTO> excelUtil = new ExcelUtil<>(ContractOriginalApproralDTO.class);
        excelUtil.exportExcel(response, contentList, "催交合同原件审核中数据");
    }


}

