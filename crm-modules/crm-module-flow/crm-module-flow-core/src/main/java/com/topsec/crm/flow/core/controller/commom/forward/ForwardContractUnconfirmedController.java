package com.topsec.crm.flow.core.controller.commom.forward;

import cn.hutool.core.collection.CollectionUtil;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedVo;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewCustomerDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.controllerhidden.contractSignVerify.HiddenContractUnconfirmedController;
import com.topsec.crm.flow.core.entity.ContractSignVerifyMain;
import com.topsec.crm.flow.core.entity.ContractUnconfirmed;
import com.topsec.crm.flow.core.entity.ContractUnconfirmedSnapshot;
import com.topsec.crm.flow.core.service.IContractSignVerifyMainService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedSnapshotService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.product.api.RemoteProductService;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.project.api.client.RemoteProjectAgentClient;
import com.topsec.crm.project.api.entity.CrmProjectAgentVo;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/forward/contractUnconfirmed")
@Tag(name = "转发渠道项目Controller", description = "/forward")
@RequiredArgsConstructor
@Validated
public class ForwardContractUnconfirmedController extends BaseController {

    private final HiddenContractUnconfirmedController hiddenContractUnconfirmedController;

    @Autowired
    private IContractUnconfirmedService contractUnconfirmedService;
    @Autowired
    private IContractUnconfirmedSnapshotService contractUnconfirmedSnapshotService;
    @Autowired
    private IContractSignVerifyMainService contractSignVerifyMainService;
    @Autowired
    private RemoteContractReviewService remoteContractReviewService;
    @Autowired
    private RemoteContractExecuteService remoteContractExecuteService;
    @Autowired
    private RemoteProductService remoteProductService;

    /**
     * 分页列表
     */
    @PostMapping("/selectPage")
    @Operation(summary = "查询未确认明细列表记录")
    @PreFlowPermission
    public JsonObject<PageUtils<ContractUnconfirmedVo>> selectPage(@RequestBody ContractUnconfirmedVo contractUnconfirmedVo) {
        //ProcessInstanceId校验
        PreFlowPermissionAspect.checkProcessInstanceId(contractUnconfirmedVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //查询流程详情
        ContractSignVerifyMain contractSignVerifyMain = contractSignVerifyMainService.query().eq("process_instance_id", contractUnconfirmedVo.getProcessInstanceId()).one();

        List<ContractUnconfirmed> list = new ArrayList<ContractUnconfirmed>();
        startPage();
        if(contractSignVerifyMain.getProcessState().equals(ApprovalStatusEnum.SPZ.getCode())) {
            list = contractUnconfirmedService.query()
                    .eq(StringUtils.isNotBlank(contractSignVerifyMain.getUnconfirmedId()), "id", contractSignVerifyMain.getUnconfirmedId())
                    .eq(StringUtils.isNotBlank(contractSignVerifyMain.getContractNumber()), "contract_number", contractSignVerifyMain.getContractNumber())
                    .orderByDesc("create_time")
                    .list();
        }else{
            List<ContractUnconfirmedSnapshot> snapshots = contractUnconfirmedSnapshotService.query()
                    .eq("process_instance_id", contractUnconfirmedVo.getProcessInstanceId())
                    .list();
            for (ContractUnconfirmedSnapshot snapshot : snapshots) {
                //重新赋值，页面通过该ID做渲染
                snapshot.setId(snapshot.getUnconfirmedId());
            }
            list = HyperBeanUtils.copyListPropertiesByJackson(snapshots,ContractUnconfirmed.class);
        }
        
        if(CollectionUtil.isNotEmpty(list)){
            //查询用户信息
            List<String> personIds = list.stream().map(ContractUnconfirmed::getCreateUser).toList();
            JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(personIds);


            //查询产品信息
            Set<String> materialCodes = list.stream().map(ContractUnconfirmed::getMaterialCode).collect(Collectors.toSet());
            JsonObject<List<CrmProductVo>> proObj = remoteProductService.batchGetInfoByMaterialCode(materialCodes.stream().toList());

            //查询合同执行信息
            Set<String> contractNumbers = list.stream().map(ContractUnconfirmed::getContractNumber).collect(Collectors.toSet());
            JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(contractNumbers);
            if(byContractNumberBatch.isSuccess() && proObj.isSuccess()){
                List<CrmContractExecuteVO> contractInfos = byContractNumberBatch.getObjEntity();
                Set<String> contractIds = contractInfos.stream().map(CrmContractExecuteVO::getContractId).collect(Collectors.toSet());
                //查询预付款金额
                JsonObject<List<ContractReviewCustomerDTO>> cib = remoteContractReviewService.getCustomerInfoByContractIdBatch(contractIds);
                if(cib.isSuccess()) {
                    List<ContractReviewCustomerDTO> reviews = cib.getObjEntity();
                    for (ContractUnconfirmed contractUnconfirmed : list) {
                        //设置合同执行信息
                        CrmContractExecuteVO crmContractExecuteVO = contractInfos.stream().filter(c -> c.getContractNumber().equals(contractUnconfirmed.getContractNumber())).findFirst().orElse(null);
                        contractUnconfirmed.setContractExecuteVO(crmContractExecuteVO);
                        if (crmContractExecuteVO != null) {
                            ContractReviewCustomerDTO reviewCustomerDTO = reviews.stream().filter(c -> c.getContractReviewMainId().equals(crmContractExecuteVO.getContractId())).findFirst().orElse(null);
                            contractUnconfirmed.setPrepayment(reviewCustomerDTO != null ? reviewCustomerDTO.getPrepayment() : null);
                        }
                    }
                }

                List<CrmProductVo> productVos = proObj.getObjEntity();

                //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
                List<ContractUnconfirmedVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, ContractUnconfirmedVo.class);
                //补充合同原销售单位字段-坏账时间字段
                for (ContractUnconfirmedVo detail : listVo) {
                    ContractExecuteVO contractExecuteVO = detail.getContractExecuteVO();

                    JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(contractExecuteVO.getSaleId());
                    if(byId.isSuccess() && byId.getObjEntity() != null){
                        contractExecuteVO.setSaleDeptName(byId.getObjEntity().getDept() != null ? byId.getObjEntity().getDept().getName() : "");
                    }

                    if(byIds.isSuccess() && byIds.getObjEntity() != null){
                        EmployeeVO employeeVO = byIds.getObjEntity().stream().filter(e -> e.getUuid().equals(detail.getCreateUser())).findFirst().orElse(null);
                        detail.setCreateUserName(employeeVO != null ? employeeVO.getName() : "");
                    }

                    //设置生产类型
                    CrmProductVo productVo = productVos.stream().filter(c -> c.getMaterialCode().equals(detail.getMaterialCode())).findFirst().orElse(null);
                    if(productVo != null && productVo.getProductionType() != null) {
                        detail.setIsDZFH(productVo.getProductionType().equals("DZFH") ? true : false);
                    }else{
                        detail.setIsDZFH(false);
                    }
                }

                PageUtils dataTable = getDataTable(list,listVo);
                return new JsonObject<>(dataTable);
            }
        }
        return new JsonObject<>(new PageUtils<ContractUnconfirmedVo>());
    }
}
