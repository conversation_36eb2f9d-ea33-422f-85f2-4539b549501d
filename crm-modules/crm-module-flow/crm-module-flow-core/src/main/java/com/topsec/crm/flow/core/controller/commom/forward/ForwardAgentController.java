package com.topsec.crm.flow.core.controller.commom.forward;

import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteProjectAgentClient;
import com.topsec.crm.project.api.entity.CrmProjectAgentVo;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.vo.TfsFormContentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/forward/agent")
@Tag(name = "转发渠道项目Controller", description = "/forward")
@RequiredArgsConstructor
@Validated
public class ForwardAgentController extends BaseController {

    private final RemoteProjectAgentClient remoteProjectAgentClient;
    private final TfsFormContentClient tfsformContentClient;

    @PreAuthorize
    @PreFlowPermission
    @GetMapping("/{projectId}")
    @Operation(summary = "查询渠道项目详情")
    JsonObject<CrmProjectAgentVo> getInfo(@PathVariable String projectId) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(projectId.equals(tfsFormContentVo.getProjectId())){
                return remoteProjectAgentClient.getInfo(projectId);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

}
