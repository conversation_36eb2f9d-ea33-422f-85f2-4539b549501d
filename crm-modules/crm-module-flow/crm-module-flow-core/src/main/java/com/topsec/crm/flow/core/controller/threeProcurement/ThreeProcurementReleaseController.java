package com.topsec.crm.flow.core.controller.threeProcurement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.topsec.crm.flow.api.vo.ThreeProcurementReleaseQuery;
import com.topsec.crm.flow.api.vo.ThreeProcurementReleaseVo;
import com.topsec.crm.flow.core.entity.ThreeProcurementRelease;
import com.topsec.crm.flow.core.service.ThreeProcurementReleaseService;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;



/**
 * 
 *
 * <AUTHOR>
 * @email 
 * @date 2024-10-31 16:39:51
 */

@RestController
@RequestMapping("/threeprocurementrelease")
@Tag(name = "【【第三方采购付款申请放行】】", description = "threeprocurementrelease")
@RequiredArgsConstructor
@Validated
public class ThreeProcurementReleaseController {

    private final ThreeProcurementReleaseService threeProcurementReleaseService;


    /**
     * 列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询采购放行数据")
    @PreAuthorize(hasPermission = "crm_third_purchase_payment_pass_config")
    public JsonObject<PageUtils<ThreeProcurementReleaseVo>> page(@RequestBody ThreeProcurementReleaseQuery threeProcurementReleaseQuery){
        Page<ThreeProcurementRelease> threeProcurementReleasePage = threeProcurementReleaseService.pageThreeProcurementRelease(threeProcurementReleaseQuery);
        IPage<ThreeProcurementReleaseVo> convert = threeProcurementReleasePage.convert(procurementRelease -> {
            return HyperBeanUtils.copyPropertiesByJackson(procurementRelease, ThreeProcurementReleaseVo.class);
        });
        return new JsonObject<>(new PageUtils<>(convert));
    }


    @GetMapping("/selectThreeProcurementByPurchaseNumber")
    @Operation(summary = "根据采购编号查询采购数据")
    @PreAuthorize(hasPermission = "crm_third_purchase_payment_pass_config")
    public JsonObject<ThreeProcurementReleaseVo> selectThreeProcurementByPurchaseNumber(@RequestParam String purchaseNumber){
        ThreeProcurementRelease threeProcurementRelease = threeProcurementReleaseService.queryThreeProcurementByPurchaseNumber(purchaseNumber);
        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(threeProcurementRelease, ThreeProcurementReleaseVo.class));
    }



    /**
     * 保存
     */
    @PostMapping("/save")
    @Operation(summary = "新增采购放行数据")
    @PreAuthorize(hasPermission = "crm_third_purchase_payment_pass_config")
    public JsonObject<Boolean> save(@RequestBody ThreeProcurementReleaseVo threeProcurementReleaseVo){
        threeProcurementReleaseVo.setDelFlag(0);
        checkRelease(threeProcurementReleaseVo);
        ThreeProcurementRelease threeProcurementRelease = HyperBeanUtils.copyPropertiesByJackson(threeProcurementReleaseVo, ThreeProcurementRelease.class);
        return new JsonObject<>(threeProcurementReleaseService.save(threeProcurementRelease));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @Operation(summary = "修改采购放行数据")
    @PreAuthorize(hasPermission = "crm_third_purchase_payment_pass_config")
    public JsonObject<Boolean> update(@RequestBody ThreeProcurementReleaseVo threeProcurementReleaseVo){
        ThreeProcurementRelease threeProcurementRelease = HyperBeanUtils.copyPropertiesByJackson(threeProcurementReleaseVo, ThreeProcurementRelease.class);
        return new JsonObject<>(threeProcurementReleaseService.updateById(threeProcurementRelease));
    }

    /**
     * 校验采购编号是否已经存在
     * @param threeProcurementReleaseVo
     */
    private void checkRelease(ThreeProcurementReleaseVo threeProcurementReleaseVo) {
        ThreeProcurementRelease procurementRelease = threeProcurementReleaseService.queryThreeProcurementReleaseByPurchaseNumber(threeProcurementReleaseVo.getProcessNumber());
        if (procurementRelease != null){
            throw new CrmException("该采购编号已存在");
        }
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @Operation(summary = "删除采购放行数据")
    @PreAuthorize(hasPermission = "crm_third_purchase_payment_pass_config")
    public JsonObject<Boolean> delete(@RequestBody String[] ids){
        return new JsonObject<>(threeProcurementReleaseService.removeByIds(Arrays.asList(ids)));
    }

}
