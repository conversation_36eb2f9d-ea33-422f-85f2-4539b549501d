package com.topsec.crm.flow.core.validator.pricereview.condition;

import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.core.validator.CheckCondition;
import com.topsec.crm.flow.core.validator.pricereview.PriceReviewCheckContext;
import com.topsec.crm.flow.core.validator.pricereview.ProductCompareUtil;

import java.util.List;
import java.util.Map;

/**
 * 自有产品最终用户价或成交单价变化，则失效
 * <AUTHOR>
 */
public class OwnPriceChangeCondition implements CheckCondition<PriceReviewCheckContext> {



    @Override
    public boolean check(PriceReviewCheckContext context)  {

        Map<String, List<PriceReviewProductOwnDTO>> currentMap = ProductCompareUtil.convert(context.getProjectDetail().getProductOwnList());
        Map<String, List<PriceReviewProductOwnDTO>> snapshotMap = ProductCompareUtil.convert(context.getProjectSnapshot().getProductOwnList());
        return ProductCompareUtil.validate(currentMap, snapshotMap, "dealPrice")
                && ProductCompareUtil.validate(currentMap, snapshotMap, "finalPrice");
    }

    @Override
    public String defaultFailureReason() {
        return "自有产品最终用户价或成交单价变化";
    }


}
