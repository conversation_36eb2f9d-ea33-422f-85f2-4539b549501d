package com.topsec.crm.flow.core.aspect;

import com.github.pagehelper.PageHelper;
import com.topsec.crm.flow.api.vo.ProcessCheckVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.process.impl.TfsProcessService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.crm.framework.security.utils.AuthorizeUtil;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.constant.TbsConstants;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.vo.node.ApproveNode;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 流程权限拦截器
 */
@Aspect
@Component
@Slf4j
public class PreFlowPermissionAspect {

    @Resource
    private HttpServletRequest request;



    @Resource
    private AuthorizeUtil authorizeUtil;

    @Resource
    private TfsProcessService tfsProcessService;

//    @Resource
//    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;
//    @Resource
//    private ProcessExtensionInfoService processExtensionInfoService;

    @Resource
    private TfsNodeClient tfsNodeClient;


//
//    @Resource
//    private TfsHistoryClient tfsHistoryClient;
//
//    @Resource
//    private TfsFormContentClient tfsFormContentClient;


    @Around("@annotation(com.topsec.crm.flow.core.annotation.PreFlowPermission)")
    public Object handleDataPermission(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法上的注解
        PreFlowPermission preFlowPermission = getAnnotation(joinPoint);
        // 根据注解值进行数据权限校验
        if (preFlowPermission != null) {
            // 获取header参数
            String processInstanceId = request.getHeader("Process-Instance-Id");
            if (StringUtils.isEmpty(processInstanceId)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
            String parentProcessInstanceId = request.getHeader("Parent-Process-Instance-Id");
            String projectId = request.getHeader("Project-Id");
            // 在此处可以记录日志或打印输出
            log.info("header processInstanceId：{}", processInstanceId);
            log.info("header projectId：{}", projectId);
            if (StringUtils.isEmpty(processInstanceId) && StringUtils.isEmpty(projectId)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
            String accountId = UserInfoHolder.getCurrentAccountId();
            String currentPersonId = UserInfoHolder.getCurrentPersonId();
            Set<String> currentRoles = UserInfoHolder.getCurrentRoles();
            List<String> deptIds = authorizeUtil.queryDeptIdList(currentPersonId, TbsConstants.Datapurview.DEPTDATA);
            ProcessCheckVo processCheckVo = ProcessCheckVo.builder().projectId(projectId)
                    .processInstanceId(processInstanceId).parentProcessInstanceId(parentProcessInstanceId).accountId(accountId)
                    .deptIds(deptIds).roles(currentRoles).build();
            Pair<Boolean, String> booleanStringPair = tfsProcessService.checkBeforeFilling(processCheckVo);
            Boolean result = booleanStringPair.getLeft();
            if (!result){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }

            if (!ArrayUtils.isEmpty(preFlowPermission.hasAnyNodes()) && !processInstanceId.startsWith("history_")){
                String[] nodeIds = preFlowPermission.hasAnyNodes();
                Optional.ofNullable( tfsNodeClient.queryNodeByProcessInstanceIdList(List.of(processInstanceId)))
                        .map(JsonObject::getObjEntity)
                        .ifPresent(map -> {
                            if (map.containsKey(processInstanceId)){
                                Set<ApproveNode> approveNodes = map.get(processInstanceId);
                                if (CollectionUtils.isEmpty(approveNodes)){
                                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                                }
                                // 判断nodeIds 是否在 approveNodes中存在
                                if (approveNodes.stream().noneMatch(item -> ArrayUtils.contains(nodeIds, item.getNodeId()))){
                                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                                }
                                // 判断当前登录人是否在approveNodes的currentApprovedList 中存在
                                if (approveNodes.stream().noneMatch(item -> item.getCurrentApprovedList().stream().anyMatch(leader -> leader.getValue().equals(currentPersonId)))){
                                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                                }

                            }else {
                                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                            }
                        });
                String nodeStr = ArrayUtils.toString(preFlowPermission.hasAnyNodes());
                log.warn(" 当前用户id:{},节点id:{}",accountId, nodeStr);

            }
        }
        // 继续执行目标方法
        return joinPoint.proceed();
    }


    @After("@annotation(com.topsec.crm.flow.core.annotation.PreFlowPermission)")
    public void removeDataScopeParam() {
        // clearPage
        PageHelper.clearPage();
    }


    private PreFlowPermission getAnnotation(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return AnnotationUtils.findAnnotation(method, PreFlowPermission.class);
    }

    /**
     * 校验header中流程实例id和参数流程实例id是否一致
     */
    public static void checkProcessInstanceId(String processInstanceId,String headerProcessInstanceId) {
        CrmAssert.hasText(processInstanceId, "参数不能为空");
        CrmAssert.hasText(headerProcessInstanceId, "参数不能为空");
        if (!processInstanceId.equals(headerProcessInstanceId)) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    /**
     * 字段打码，目前只处理L5级字段
     * @param joinPoint
     * @param jsonResult
     */
    @AfterReturning(pointcut = "@annotation(com.topsec.crm.flow.core.annotation.PreFlowPermission)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, JsonObject jsonResult) {
        List<String> roleIds = UserInfoHolder.getLoginInfo().getRoles();
        authorizeUtil.masking(roleIds, jsonResult);
    }

}
