package com.topsec.crm.flow.core.validator.pricereview.condition;

import com.topsec.crm.flow.core.entity.ProjectDetail;
import com.topsec.crm.flow.core.validator.CheckCondition;

import com.topsec.crm.flow.core.validator.pricereview.PriceReviewCheckContext;
import com.topsec.crm.framework.common.util.MapCompareUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自有产品 产品数量变化
 * 按物料代码分组 比较数量
 * <AUTHOR>
 */
public class OwnQuantityChangeCondition implements CheckCondition<PriceReviewCheckContext> {



    @Override
    public boolean check(PriceReviewCheckContext context)  {

        ProjectDetail projectDetail = context.getProjectDetail();
        // 按物料代码分组
        Map<String, Integer> currentMap= projectDetail.getProductOwnList()
                .stream().map(own -> {
                    return Pair.of(own.getProductId(), own.getProductNum());
                }).collect(Collectors.groupingBy(Pair::getLeft, Collectors.summingInt(Pair::getRight)));


        Map<String, Integer> snapshotMap = ListUtils.emptyIfNull(context.getProjectSnapshot().getProductOwnList())
                .stream().map(own -> {
                    return Pair.of(own.getProductId(), own.getProductNum());
                }).collect(Collectors.groupingBy(Pair::getLeft, Collectors.summingInt(Pair::getRight)));

        return MapCompareUtils.areMapsEqual(currentMap,snapshotMap);
    }

    @Override
    public String defaultFailureReason() {
        return "自有产品产品数量变化";
    }
}
