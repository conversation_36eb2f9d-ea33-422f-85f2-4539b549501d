package com.topsec.crm.flow.core.controller.projectRadio.business;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.topsec.crm.flow.api.dto.projectRadio.ProjectRadioFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.projectRadio.ProjectRadioMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ProjectRadioMain;
import com.topsec.crm.flow.core.entity.ProjectReportMain;
import com.topsec.crm.flow.core.process.impl.ProjectRadioProcessService;
import com.topsec.crm.flow.core.service.IProjectRadioMainService;
import com.topsec.crm.flow.core.service.IProjectReportMainService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.date.DateUtil;
import com.topsec.crm.framework.common.util.date.DateUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.*;

@RestController
@RequestMapping("/business/projectRadio")
@Tag(name = "项目广播", description = "/projectRadio")
@RequiredArgsConstructor
public class BusinessProjectRadioMainController extends BaseController {

    @Autowired
    private IProjectRadioMainService projectRadioMainService;
    @Autowired
    private ProjectRadioProcessService projectRadioProcessService;
    @Autowired
    private IProjectReportMainService projectReportMainService;
    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    @PostMapping("/selectInfo")
    @Operation(summary = "查询广播内容")
    @PreAuthorize(hasPermission = "crm_flow_project_redio", dataScope = "crm_flow_project_redio")
    public JsonObject<ProjectRadioMainVo> selectInfo(@RequestBody ProjectRadioMainVo projectRadioMainVo){
        PreFlowPermissionAspect.checkProcessInstanceId(projectRadioMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //查询项目报备流程信息
        ProjectRadioMain projectRadioMain = projectRadioMainService.getOne(new QueryWrapper<ProjectRadioMain>().eq("process_instance_id", projectRadioMainVo.getProcessInstanceId()));
        //数据范围校验
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        if(CollectionUtil.isNotEmpty(personIdList) && !personIdList.contains(projectRadioMain.getCreateUser())){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(projectRadioMain,ProjectRadioMainVo.class));
    }

}
