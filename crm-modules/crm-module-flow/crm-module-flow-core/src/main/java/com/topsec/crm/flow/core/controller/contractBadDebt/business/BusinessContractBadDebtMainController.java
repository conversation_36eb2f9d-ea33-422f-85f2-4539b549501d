package com.topsec.crm.flow.core.controller.contractBadDebt.business;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.entity.CrmContractBaseInfoVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.erp.api.RemoteAuditRecordService;
import com.topsec.crm.erp.api.entity.CrmAuditRecordVO;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractBadDebtApproveFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractBadDebtAttachmentDTO;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractBadDebtDetailVo;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractBadDebtMainVo;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.CrmContractProcessInfoVO;
import com.topsec.crm.flow.api.enums.AgentapprovalStatusEnum;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ContractBadDebtAttachment;
import com.topsec.crm.flow.core.entity.ContractBadDebtDetail;
import com.topsec.crm.flow.core.entity.ContractBadDebtExecuteSnapshot;
import com.topsec.crm.flow.core.entity.ContractBadDebtMain;
import com.topsec.crm.flow.core.process.impl.ContractBadDebtProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.flow.core.service.impl.ContractReviewMainServiceImpl;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.date.DateUtil;
import com.topsec.crm.framework.common.util.date.DateUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyPriceStatisticsVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/contractBadDebtMain")
@Tag(name = "合同坏账相关Controller", description = "/contractBadDebtMain/flow")
@RequiredArgsConstructor
@Validated
public class BusinessContractBadDebtMainController extends BaseController {
    @Autowired
    private IContractBadDebtMainService contractBadDebtMainService;
    @Autowired
    private IContractBadDebtDetailService contractBadDebtDetailService;
    @Autowired
    private IContractBadDebtAttachmentService contractBadDebtAttachmentService;
    @Autowired
    private IContractBadDebtExecuteSnapshotService contractBadDebtExecuteSnapshotService;
    @Autowired
    private RemoteContractExecuteService remoteContractExecuteService;
    @Autowired
    private RemoteAuditRecordService remoteAuditRecordService;
    @Autowired
    private RemoteContractReviewService remoteContractReviewService;
    @Autowired
    private ContractReviewMainService contractReviewMainService;

    @GetMapping("/info")
    @Operation(summary = "合同坏账详情信息")
    @PreAuthorize(hasPermission = "crm_contract_bad_debt",dataScope = "crm_contract_bad_debt")
    public JsonObject<ContractBadDebtMainVo> info(@RequestParam String processInstanceId) {
        //ProcessInstanceId校验
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //1.查询合同坏账明细基本信息
        List<ContractBadDebtDetail> list = contractBadDebtDetailService.query().eq("process_instance_id", processInstanceId).list();
        //组合页面负责人参数和权限范围内的用户ID
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        //查询合同执行信息
        Set<String> contractNumbers = list.stream().map(ContractBadDebtDetail::getContractNumber).collect(Collectors.toSet());
        JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch1 = remoteContractExecuteService.getByContractNumberBatchNotEffective(contractNumbers);
        if(byContractNumberBatch1 != null && byContractNumberBatch1.getObjEntity() != null){
            for (CrmContractExecuteVO crmContractExecuteVO : byContractNumberBatch1.getObjEntity()) {
                if(CollectionUtil.isNotEmpty(personIdList) && !personIdList.contains(crmContractExecuteVO.getContractOwnerId())){
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
            }
        }

        ContractBadDebtMain contractBadDebtMain = contractBadDebtMainService.query().eq("process_instance_id", processInstanceId).one();
        ContractBadDebtMainVo cbdmv = HyperBeanUtils.copyPropertiesByJackson(contractBadDebtMain, ContractBadDebtMainVo.class);

        //2.查询合同坏账明细
        //2.1查询合同执行信息,流程审批中的-实时查询；审批通过的-查询快照
        if (contractBadDebtMain.getProcessState().intValue() == AgentapprovalStatusEnum.SPZ.getCode()) {
            //批量查询合同执行信息
            JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(contractNumbers);
            if (byContractNumberBatch.isSuccess()) {
                List<CrmContractExecuteVO> contractInfos = byContractNumberBatch.getObjEntity();
                //查询用户信息
                JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(contractInfos.stream().map(CrmContractExecuteVO::getSaleId).toList());
                List<EmployeeVO> users = byIds.getObjEntity();
                //查询流程信息
                List<ContractReviewMainBaseInfoDTO> rmbis = contractReviewMainService.getByContractNumberBatch(contractInfos.stream().map(CrmContractExecuteVO::getContractNumber).collect(Collectors.toSet()));

                for (ContractBadDebtDetail contractBadDebtDetail : list) {
                    CrmContractExecuteVO crmContractExecuteVO = contractInfos.stream().filter(c -> c.getContractNumber().equals(contractBadDebtDetail.getContractNumber())).findFirst().orElse(null);
                    //补充原销售单位字段
                    EmployeeVO employeeVO = users.stream().filter(e -> e.getUuid().equals(crmContractExecuteVO.getSaleId())).findFirst().orElse(null);
                    crmContractExecuteVO.setSaleDeptName(employeeVO.getDept() != null ? employeeVO.getDept().getName() : "");
                    contractBadDebtDetail.setContractExecuteVO(crmContractExecuteVO);

                    //5.查询合同明细
                    List<ContractReviewMainBaseInfoDTO> list1 = rmbis.stream().filter(e -> e.getProcessInstanceId() != null).filter(e -> e.getContractNumber().equals(contractBadDebtDetail.getContractNumber())).toList();
                    List<CrmContractProcessInfoVO> crmContractProcessInfoVOS = new ArrayList<CrmContractProcessInfoVO>();
                    for (ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO : list1) {
                        CrmContractProcessInfoVO crmContractProcessInfoVO = HyperBeanUtils.copyPropertiesByJackson(contractReviewMainBaseInfoDTO, CrmContractProcessInfoVO.class);
                        crmContractProcessInfoVO.setContractId(contractReviewMainBaseInfoDTO.getId());
                        crmContractProcessInfoVOS.add(crmContractProcessInfoVO);
                    }
                }
            }
        } else if (contractBadDebtMain.getProcessState().intValue() == AgentapprovalStatusEnum.YSP.getCode()) {
            //查询快照
            List<ContractBadDebtExecuteSnapshot> snapshots = contractBadDebtExecuteSnapshotService.query().eq("process_instance_id", processInstanceId).list();
            //查询流程信息
            List<ContractReviewMainBaseInfoDTO> rmbis = contractReviewMainService.getByContractNumberBatch(snapshots.stream().map(ContractBadDebtExecuteSnapshot::getContractNumber).collect(Collectors.toSet()));

            for (ContractBadDebtDetail contractBadDebtDetail : list) {
                ContractBadDebtExecuteSnapshot snapshot = snapshots.stream().filter(c -> c.getContractNumber().equals(contractBadDebtDetail.getContractNumber())).findFirst().orElse(null);
                //快照数据的合同执行状态统一展示“坏账”
                snapshot.setContractExecuteStatus(2);
                //补充原销售单位字段
                JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(snapshot.getSaleId());
                if(byId.isSuccess() && byId.getObjEntity() != null){
                    snapshot.setSaleDeptName(byId.getObjEntity().getDept() != null ? byId.getObjEntity().getDept().getName() : "");
                }
                contractBadDebtDetail.setContractExecuteVO(HyperBeanUtils.copyPropertiesByJackson(snapshot, CrmContractExecuteVO.class));

                //5.查询合同明细
                List<ContractReviewMainBaseInfoDTO> list1 = rmbis.stream().filter(e -> e.getProcessInstanceId() != null).filter(e -> e.getContractNumber().equals(contractBadDebtDetail.getContractNumber())).toList();
                List<CrmContractProcessInfoVO> crmContractProcessInfoVOS = new ArrayList<CrmContractProcessInfoVO>();
                for (ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO : list1) {
                    CrmContractProcessInfoVO crmContractProcessInfoVO = HyperBeanUtils.copyPropertiesByJackson(contractReviewMainBaseInfoDTO, CrmContractProcessInfoVO.class);
                    crmContractProcessInfoVO.setContractId(contractReviewMainBaseInfoDTO.getId());
                    crmContractProcessInfoVOS.add(crmContractProcessInfoVO);
                }

                contractBadDebtDetail.setContractProcessInfoVOS(crmContractProcessInfoVOS);
                System.out.println(1);
            }
        }
        //2.2查询合同毛利
        LocalDateTime dd1 = LocalDateTime.now();
        JsonObject<Map<String, CrmProjectDirectlyPriceStatisticsVO>> obj = remoteContractExecuteService.getContractPriceStatisticsBatch(list.stream().map(ContractBadDebtDetail::getContractNumber).collect(Collectors.toSet()));
        LocalDateTime dd2 = LocalDateTime.now();
        System.out.println("dd2-dd1=============耗时"+ DateUtil.getMillisDifferenceByDuration(dd1,dd2));
        if(obj.isSuccess() && obj.getObjEntity() != null) {
            Map<String, CrmProjectDirectlyPriceStatisticsVO> objEntity = obj.getObjEntity();
            for (ContractBadDebtDetail contractBadDebtDetail : list) {
                CrmProjectDirectlyPriceStatisticsVO crmProjectDirectlyPriceStatisticsVO = objEntity.get(contractBadDebtDetail.getContractNumber());
                contractBadDebtDetail.setGrossMargin(crmProjectDirectlyPriceStatisticsVO.getTotal().getGrossMargin());
            }
        }
        cbdmv.setDetailVos(HyperBeanUtils.copyListPropertiesByJackson(list, ContractBadDebtDetailVo.class));


        //3.查询附件列表
        List<ContractBadDebtAttachment> attachments = contractBadDebtAttachmentService.query().eq("process_instance_id", processInstanceId).list();
        cbdmv.setAttachmentDTOS(HyperBeanUtils.copyListPropertiesByJackson(attachments, ContractBadDebtAttachmentDTO.class));

        return new JsonObject<>(cbdmv);
    }

}
