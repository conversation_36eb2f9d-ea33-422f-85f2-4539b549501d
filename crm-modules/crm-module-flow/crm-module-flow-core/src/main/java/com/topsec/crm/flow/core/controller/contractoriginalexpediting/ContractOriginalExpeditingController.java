package com.topsec.crm.flow.core.controller.contractoriginalexpediting;


import com.topsec.crm.flow.api.dto.contractexpediting.vo.ExpeditInfoVO;
import com.topsec.crm.flow.api.dto.contractexpediting.vo.UpdateExpeditingVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.service.ContractOriginalExpeditingService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 催交合同原件表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@RestController
@RequestMapping("/contractExpediting")
@Tag(name = "催交合同原件-流程业务接口", description = "contractExpediting")
@RequiredArgsConstructor
@Validated
public class ContractOriginalExpeditingController extends BaseController {

    @Resource
    private ContractOriginalExpeditingService contractOriginalExpeditingService;

    @PreFlowPermission(hasAnyNodes = {"sid-7DF8A52B-1E56-47BE-B1C6-D5DFC18B1F49"})
    @PostMapping("/fillInstructions")
    @Operation(summary = "催交合同原件流程——01-填写催交说明")
    public JsonObject<Boolean> fillInstructions(@RequestBody UpdateExpeditingVO updateExpeditingVO) {
        return new JsonObject<>(contractOriginalExpeditingService.fillInstructions(updateExpeditingVO));
    }

    @PreFlowPermission(hasAnyNodes = {"sid-7DF8A52B-1E56-47BE-B1C6-D5DFC18B1F49"})
    @GetMapping("/checkInstructions")
    @Operation(summary = "催交合同原件流程——01-办理完毕前的校验")
    public JsonObject<Boolean> checkInstructions(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractOriginalExpeditingService.checkInstructions(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/selectInfoById")
    @Operation(summary = "流程查询合同原件详情", method = "GET")
    public JsonObject<ExpeditInfoVO> selectInfoById(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractOriginalExpeditingService.selectInfoById(processInstanceId));
    }

}

