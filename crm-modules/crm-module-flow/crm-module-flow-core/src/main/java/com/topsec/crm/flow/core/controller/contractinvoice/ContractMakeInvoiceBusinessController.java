package com.topsec.crm.flow.core.controller.contractinvoice;

import com.github.pagehelper.PageHelper;
import com.topsec.crm.contract.api.ContractProductDetailQuery;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.CrmContractProductAllDetailVO;
import com.topsec.crm.contract.api.entity.CrmRevenueRecognitionVO;
import com.topsec.crm.flow.api.RemoteContractSignVerifyMainService;
import com.topsec.crm.flow.api.RemoteContractUnconfirmedDetailService;
import com.topsec.crm.flow.api.dto.contractInvoice.ContractInfoDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.ContractProductDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.ContractMakeInvoiceDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.MakeInvoicePageVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractSignVerifyAttachmentDTO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedDetailVo;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.core.process.impl.TfsProcessService;
import com.topsec.crm.flow.core.service.ContractInvoiceExtendService;
import com.topsec.crm.flow.core.service.ContractMakeInvoiceService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsTaskClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.task.TaskHistoryVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("business/contractMakeInvoice")
@Tag(name = "【合同开票】-业务",description = "business/contractMakeInvoice")
public class ContractMakeInvoiceBusinessController extends BaseController {

    private final ContractMakeInvoiceService makeInvoiceService;
    private final RemoteContractExecuteService remoteContractExecuteService;
    private final RemoteContractUnconfirmedDetailService remoteContractUnconfirmedDetailService;
    private final RemoteContractSignVerifyMainService remoteContractSignVerifyMainService;
    private final ContractInvoiceExtendService contractInvoiceExtendService;
    private final TfsTaskClient tfsTaskClient;
    private final TfsProcessService tfsProcessService;

    @PostMapping("/getMakeInvoicePage")
    @Operation(summary = "合同开票流程分页列表-列表")
    @PreAuthorize(hasPermission = "crm_contract_invoice", dataScope = "crm_contract_invoice")
    public JsonObject<TableDataInfo> getMakeInvoicePage(@RequestBody MakeInvoicePageVO query){
        startPage();
        return new JsonObject<>(makeInvoiceService.getProcessPage(query));
    }

    @PostMapping("/exportMakeInvoicePage")
    @Operation(summary = "合同开票流程导出-列表")
    @PreAuthorize(hasPermission = "crm_contract_invoice", dataScope = "crm_contract_invoice")
    public void exportMakeInvoicePage(@RequestBody MakeInvoicePageVO query) throws IOException {
        ExcelUtil<MakeInvoicePageVO> excelUtil = new ExcelUtil<>(MakeInvoicePageVO.class);
        List<MakeInvoicePageVO> processList = makeInvoiceService.exportMakeInvoicePage(query);
        excelUtil.exportExcel(response, processList,"合同开票");
    }

    @GetMapping("/getProcessInfo")
    @Operation(summary = "获取合同开票流程信息-详情",parameters = {@Parameter(name = "processInstanceId",description = "流程实例ID")})
    @PreAuthorize(hasPermission = "crm_contract_invoice", dataScope = "crm_contract_invoice")
    public JsonObject<ContractMakeInvoiceDTO> getProcessInfo(@RequestParam String processInstanceId){
        return new JsonObject<>(makeInvoiceService.getProcessInfo(processInstanceId));
    }

    @GetMapping("/getContractInfo")
    @Operation(summary = "获取合同信息-详情",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_invoice", dataScope = "crm_contract_invoice")
    public JsonObject<ContractInfoDTO> getContractInfo(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(contractInvoiceExtendService.getContractInfo(contractNumber));
    }

    @GetMapping("/getRevenueRecognition")
    @Operation(summary = "获取收入确认列表-详情",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_invoice", dataScope = "crm_contract_invoice")
    public JsonObject<PageUtils<CrmRevenueRecognitionVO>> getRevenueRecognition(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        PageUtils<CrmRevenueRecognitionVO> objEntity = remoteContractExecuteService.pageRevenueRecognitionByContractNumber(contractNumber).getObjEntity();
        return new JsonObject<>(objEntity);
    }

    @GetMapping("/getPaymentTerms")
    @Operation(summary = "获取付款条款-详情",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_invoice", dataScope = "crm_contract_invoice")
    public JsonObject<List<PaymentProvisionDTO>> getPaymentTerms(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        List<PaymentProvisionDTO> list = remoteContractExecuteService.pagePaymentProvisionByContractNumber(contractNumber).getObjEntity().getList();
        return new JsonObject<>(Objects.requireNonNullElseGet(list, ArrayList::new));
    }

    @GetMapping("/getAttach")
    @Operation(summary = "获取签验收单据-详情",description ="附件",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_invoice", dataScope = "crm_contract_invoice")
    public JsonObject<List<ContractSignVerifyAttachmentDTO>> getAttach(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        List<ContractSignVerifyAttachmentDTO> list = remoteContractSignVerifyMainService.getAttachAfter01(List.of(contractNumber)).getObjEntity().get(contractNumber);
        return new JsonObject<>(Objects.requireNonNullElseGet(list, ArrayList::new));
    }

    @PostMapping("/getContractProductList")
    @Operation(summary = "合同产品-详情",description = "合同中的所有产品行签验收情况")
    @PreAuthorize(hasPermission = "crm_contract_invoice", dataScope = "crm_contract_invoice")
    public JsonObject<List<ContractProductDTO>> getContractProductList(@RequestBody ContractProductDetailQuery query){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(query.getContractNumber(), UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        query.setNeedGrossMargin(true);
        query.setNeedSpecialCode(true);
        query.setNeedSn(true);
        List<CrmContractProductAllDetailVO> entity = remoteContractExecuteService.queryContractProductDetailByCondition(query).getObjEntity();
        List<ContractProductDTO> contractProductDTOS = HyperBeanUtils.copyListProperties(entity, ContractProductDTO::new);
        return new JsonObject<>(contractProductDTOS);
    }

    @GetMapping("/getContractProcessPage")
    @Operation(summary = "发票详情-分页列表-详情")
    @PreAuthorize(hasPermission = "crm_contract_invoice", dataScope = "crm_contract_invoice")
    public JsonObject<TableDataInfo> getContractProcessPage(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        startPage();
        return new JsonObject<>(contractInvoiceExtendService.getInvoiceProcessPage(contractNumber, PageHelper.getLocalPage().getPageSize(), PageHelper.getLocalPage().getPageNum()));
    }

    @GetMapping("/queryInvoiceAuthCommentList")
    @Operation(summary = "根据流程实例id查询审批进度以及评论回复",parameters = {@Parameter(example = "52e30c06-3227-11f0-a9ac-0242ac12000d")})
    @PreAuthorize
    public JsonObject<List<TaskHistoryVo>> queryInvoiceAuthCommentList(@RequestParam String processInstanceId) {
        String currentAccountId = getCurrentAccountId();
        return Optional.ofNullable(tfsTaskClient.getTaskHistoryList(processInstanceId)).map(
                        taskHistoryVo->{
                            tfsProcessService.authCommentList(taskHistoryVo.getObjEntity(),currentAccountId);
                            return taskHistoryVo;
                        })
                .orElseThrow(() -> new CrmException("查询审批意见失败"));
    }

}
