package com.topsec.crm.flow.core.controller.contractreview;

import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.RevenueRecognitionDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.ReviewRetentionMoneyDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.service.ContractReviewPaymentProvisionService;
import com.topsec.crm.flow.core.service.ContractReviewRetentionMoneyService;
import com.topsec.crm.flow.core.service.ContractReviewRevenueRecognitionService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合同评审条款相关controller
 *
 * <AUTHOR>
 * @date 2024/7/20 16:37
 */
@RestController
@RequestMapping("/contractTerm")
@Tag(name = "合同评审条款相关", description = "/contractTerm")
@RequiredArgsConstructor
@Validated
public class ContractReviewTermController extends BaseController {



}
