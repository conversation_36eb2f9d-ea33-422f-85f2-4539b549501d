package com.topsec.crm.flow.core.validator;

import com.topsec.crm.framework.common.exception.CrmException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public interface CheckCondition<T> {
    Logger log = LoggerFactory.getLogger(CheckCondition.class);
    boolean check(T t) throws CrmException;

    default void checkWithThrow(T t){
        try {
            boolean check = check(t);
            if (!check){
                throw new CrmException(defaultFailureReason());
            }
        }catch (CrmException e){
            throw e;
        }catch (Exception e){
            log.error("内部异常",e);
            throw new CrmException("内部异常");
        }
    }

    default String defaultFailureReason() {
        return StringUtils.EMPTY;
    }
}
