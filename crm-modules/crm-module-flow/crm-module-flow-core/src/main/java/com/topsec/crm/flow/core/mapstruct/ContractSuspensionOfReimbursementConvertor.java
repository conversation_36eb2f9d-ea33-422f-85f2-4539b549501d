package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.contractSuspensionOfReimbursement.ContractSuspensionOfReimbursementVO;
import com.topsec.crm.flow.core.entity.ContractDebt;
import com.topsec.crm.flow.core.entity.ContractSuspensionOfReimbursement;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ContractSuspensionOfReimbursementConvertor {
    ContractSuspensionOfReimbursementConvertor INSTANCE = Mappers.getMapper(ContractSuspensionOfReimbursementConvertor.class);


    @Mapping(target = "freeDate", ignore = true)
    @Mapping(target = "free", ignore = true)
    @Mapping(target = "contractTime", ignore = true)
    @Mapping(target = "contractCompanyName", ignore = true)
    @Mapping(target = "contractCompanyId", ignore = true)
    @Mapping(target = "periods", ignore = true)
    @Mapping(target = "remindClassification", ignore = true)
    @Mapping(target = "proportionOfReimbursement", ignore = true)
    @Mapping(target = "id", ignore = true)
    ContractSuspensionOfReimbursement debtToContractSuspensionOfReimbursement(ContractDebt contractDebt);

    List<ContractSuspensionOfReimbursement> debtToContractSuspensionOfReimbursement(List<ContractDebt> contractDebt);

    ContractSuspensionOfReimbursementVO toVO(ContractSuspensionOfReimbursement contractSuspensionOfReimbursement);
}
