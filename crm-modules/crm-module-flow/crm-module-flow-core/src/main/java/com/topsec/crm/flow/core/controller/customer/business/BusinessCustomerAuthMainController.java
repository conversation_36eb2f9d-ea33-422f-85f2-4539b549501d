package com.topsec.crm.flow.core.controller.customer.business;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.dto.customer.CustomerAuthMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CustomerAuthMain;
import com.topsec.crm.flow.core.entity.CustomerNaMain;
import com.topsec.crm.flow.core.entity.CustomerNaSaleman;
import com.topsec.crm.flow.core.service.ICustomerAuthMainService;
import com.topsec.crm.flow.core.service.ICustomerNaMainService;
import com.topsec.crm.flow.core.service.ICustomerNaSalemanService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.operation.api.RemoteIndustryService;
import com.topsec.crm.operation.api.entity.CrmIndustryVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.constant.TbsConstants;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/business")
@RequiredArgsConstructor
@Validated
public class BusinessCustomerAuthMainController extends BaseController {

    private final ICustomerAuthMainService customerAuthMainService;
    private final RemoteCustomerService remoteCustomerService;
    private final RemoteIndustryService remoteIndustryService;

    @PostMapping("/customerAuthMain/saleSelectInfo")
    @Operation(summary = "（销售、部门负责人）查询认证审批申请详情")
    @PreAuthorize(hasPermission = "crm_customer_noauth",dataScope = "crm_customer_noauth")
    public JsonObject<CustomerAuthMainVo> saleSelectInfo(@RequestBody CustomerAuthMainVo customerAnotherNameMainVo){
        PreFlowPermissionAspect.checkProcessInstanceId(customerAnotherNameMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //1.查询流程信息
        CustomerAuthMain customerAuthMain = customerAuthMainService.getOne(new QueryWrapper<CustomerAuthMain>().eq("process_instance_id", customerAnotherNameMainVo.getProcessInstanceId()));
        //数据范围权限校验
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        if(dataScopeParam.getPersonIdList() != null && !dataScopeParam.getPersonIdList().contains(customerAuthMain.getCreateUser())){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        CustomerAuthMainVo result = HyperBeanUtils.copyPropertiesByJackson(customerAuthMain, CustomerAuthMainVo.class);
        if(customerAuthMain != null){
            //2.查询客户信息
            JsonObject<CrmCustomerVo> jsonObject = remoteCustomerService.getCustomerInfo(customerAuthMain.getCustomerId(), UserInfoHolder.getCurrentPersonId(),false);

            if(jsonObject.isSuccess() && jsonObject.getObjEntity() != null){
                CrmCustomerVo crmCustomerVo = jsonObject.getObjEntity();
                //查询行业类型
                if(StringUtils.isNotEmpty(crmCustomerVo.getIndustryId())) {
                    JsonObject<CrmIndustryVO> jObject = remoteIndustryService.industryByUuid(crmCustomerVo.getIndustryId());
                    if (jObject.isSuccess()) {
                        crmCustomerVo.setIndustryType(jObject.getObjEntity().getType());
                    }
                }
                result.setCustomerVo(crmCustomerVo);
            }
        }

        return new JsonObject<>(result);
    }

    @PostMapping("/customerAuthMain/agentSelectInfo")
    @Operation(summary = "其他人员（销售、部门负责人、渠道商）查询认证审批申请详情")
    @PreAuthorize(hasAnyPermission = "crm_customer_noauth_agent",dataScope = "crm_customer_noauth_agent")
    public JsonObject<CustomerAuthMainVo> agentSelectInfo(@RequestBody CustomerAuthMainVo customerAnotherNameMainVo){
        PreFlowPermissionAspect.checkProcessInstanceId(customerAnotherNameMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //1.查询流程信息
        CustomerAuthMain customerAuthMain = customerAuthMainService.getOne(new QueryWrapper<CustomerAuthMain>().eq("process_instance_id", customerAnotherNameMainVo.getProcessInstanceId()));
        //数据范围权限校验
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        if(dataScopeParam.getPersonIdList() != null && !dataScopeParam.getPersonIdList().contains(customerAuthMain.getCreateUser())){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        CustomerAuthMainVo result = HyperBeanUtils.copyPropertiesByJackson(customerAuthMain, CustomerAuthMainVo.class);
        if(customerAuthMain != null){
            //2.查询客户信息
            JsonObject<CrmCustomerVo> jsonObject = remoteCustomerService.getCustomerInfo(customerAuthMain.getCustomerId(), UserInfoHolder.getCurrentPersonId(),false);

            if(jsonObject.isSuccess() && jsonObject.getObjEntity() != null){
                CrmCustomerVo crmCustomerVo = jsonObject.getObjEntity();
                //查询行业类型
                if(StringUtils.isNotEmpty(crmCustomerVo.getIndustryId())) {
                    JsonObject<CrmIndustryVO> jObject = remoteIndustryService.industryByUuid(crmCustomerVo.getIndustryId());
                    if (jObject.isSuccess()) {
                        crmCustomerVo.setIndustryType(jObject.getObjEntity().getType());
                    }
                }
                result.setCustomerVo(crmCustomerVo);
            }
        }

        return new JsonObject<>(result);
    }

    @PostMapping("/customerAuthMain/adminSelectInfo")
    @Operation(summary = "管理员查询认证审批申请详情")
    @PreAuthorize(hasPermission = "crm_customer_admin_auth_manage",dataScope = "crm_customer_admin_auth_manage")
    public JsonObject<CustomerAuthMainVo> adminSelectInfo(@RequestBody CustomerAuthMainVo customerAnotherNameMainVo){
        PreFlowPermissionAspect.checkProcessInstanceId(customerAnotherNameMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //1.查询流程信息
        CustomerAuthMain customerAuthMain = customerAuthMainService.getOne(new QueryWrapper<CustomerAuthMain>().eq("process_instance_id", customerAnotherNameMainVo.getProcessInstanceId()));
        CustomerAuthMainVo result = HyperBeanUtils.copyPropertiesByJackson(customerAuthMain, CustomerAuthMainVo.class);
        if(customerAuthMain != null){
            //2.查询客户信息
            JsonObject<CrmCustomerVo> jsonObject = remoteCustomerService.getCustomerInfo(customerAuthMain.getCustomerId(), UserInfoHolder.getCurrentPersonId(),false);

            if(jsonObject.isSuccess() && jsonObject.getObjEntity() != null){
                CrmCustomerVo crmCustomerVo = jsonObject.getObjEntity();
                //查询行业类型
                if(StringUtils.isNotEmpty(crmCustomerVo.getIndustryId())) {
                    JsonObject<CrmIndustryVO> jObject = remoteIndustryService.industryByUuid(crmCustomerVo.getIndustryId());
                    if (jObject.isSuccess()) {
                        crmCustomerVo.setIndustryType(jObject.getObjEntity().getType());
                    }
                }
                result.setCustomerVo(crmCustomerVo);
            }
        }

        return new JsonObject<>(result);
    }
}
