package com.topsec.crm.flow.core.controller.borrowForSellRevoke;


import com.topsec.crm.flow.api.dto.borrowForSellRevoke.BorrowForSellRevokeLaunchDTO;
import com.topsec.crm.flow.api.dto.borrowForSellRevoke.RevokeProcessDetailVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.process.impl.BorrowForSellRevokeProcessService;
import com.topsec.crm.flow.core.service.BorrowForSellRevokeService;
import com.topsec.crm.flow.core.service.IBorrowForSellProductService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/borrowForSellRevoke")
@Tag(name = "借转销撤销", description = "/borrowForSellRevoke")
@RequiredArgsConstructor
@Validated
public class BorrowForSellRevokeController extends BaseController {


    private final BorrowForSellRevokeService borrowForSellRevokeService;

    private final BorrowForSellRevokeProcessService borrowForSellRevokeProcessService;

    private final IBorrowForSellProductService borrowForSellProductService;

    @PostMapping("/launch")
    //@PreFlowPermission
    @Operation(summary = "发起借转销撤销流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody BorrowForSellRevokeLaunchDTO launchDTO) {
        return new JsonObject<>(borrowForSellRevokeProcessService.launch(launchDTO));
    }


    @PostMapping("/launchAndReturnProcessInstanceId")
    @Operation(summary = "发起借转销撤销流程并返回流程id")
    public JsonObject<String> launchAndReturnProcessInstanceId(@Valid @RequestBody BorrowForSellRevokeLaunchDTO launchDTO) {
        return new JsonObject<>(borrowForSellRevokeProcessService.launchAndReturnProcessInstanceId(launchDTO));
    }



    //@PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_revoke",dataScope = "crm_flow_borrow_for_sell_revoke")
    @GetMapping("/detail/{processInstanceId}")
    @Operation(summary = "查看借转销撤销流程详情")
    @PreFlowPermission
    public JsonObject<RevokeProcessDetailVO> borrowForSellRevokeProcessDetail(@PathVariable String processInstanceId) {
        processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(borrowForSellRevokeService.borrowForSellRevokeProcessDetail(processInstanceId));
    }


}

