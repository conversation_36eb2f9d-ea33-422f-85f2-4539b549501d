package com.topsec.crm.flow.core.controller.contractForecast;

import com.topsec.crm.flow.api.dto.contractForecast.ContractForecastFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractForecast.ContractForecastStatisticsVO;
import com.topsec.crm.flow.api.dto.contractForecast.ContractForecastUserProjectProductVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ContractForecast;
import com.topsec.crm.flow.core.entity.ContractForecastStatistics;
import com.topsec.crm.flow.core.entity.ContractForecastUserProjectProduct;
import com.topsec.crm.flow.core.service.IContractForecastService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @version V1.0
 * @Description: 合同预测流程接口
 * @ClassName: com.topsec.crm.flow.core.controller.contractForecast.ContractForecastController.java
 * @Copyright 天融信 - Powered By 企业软件研发中心
 * @author: leo
 * @date: 2025-07-25 10:43
 */
@RestController
@RequestMapping("/contractForecast")
@Tag(name = "合同预测流程接口", description = "/contractForecast")
public class ContractForecastController extends BaseController {

    @Resource
    private IContractForecastService contractForecastService;

    @PreFlowPermission
    @GetMapping("/detail/{processInstanceId}")
    @Operation(summary = "查看合同预测流程详情")
    public JsonObject<ContractForecastFlowLaunchDTO> contractForecastDetail(@PathVariable String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ContractForecast contractForecast = contractForecastService.contractForecastDetail(processInstanceId);
        return new JsonObject<ContractForecastFlowLaunchDTO>(HyperBeanUtils.copyPropertiesByJackson(contractForecast, ContractForecastFlowLaunchDTO.class));
    }

    @PreFlowPermission
    @GetMapping("/contractForecastChartData")
    @Operation(summary = "查看合同预测图表数据")
    public JsonObject<ContractForecastStatisticsVO> contractForecastChartData(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ContractForecastStatistics contractForecastStatistics = contractForecastService.contractForecastChartData(processInstanceId);
        return new JsonObject<ContractForecastStatisticsVO>(HyperBeanUtils.copyPropertiesByJackson(contractForecastStatistics, ContractForecastStatisticsVO.class));
    }

    @PreFlowPermission
    @PutMapping("/updateProductForecastData")
    @Operation(summary = "修改项目产品的预测数据")
    public JsonObject<Boolean> updateProductForecastData(@RequestBody List<ContractForecastUserProjectProductVO> productForecastData, @RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<ContractForecastUserProjectProduct> contractForecastUserProjectProducts = HyperBeanUtils.copyListPropertiesByJackson(productForecastData, ContractForecastUserProjectProduct.class);
        Boolean result = contractForecastService.updateProductForecastData(contractForecastUserProjectProducts,processInstanceId);
        return new JsonObject<Boolean>(result);
    }
}
