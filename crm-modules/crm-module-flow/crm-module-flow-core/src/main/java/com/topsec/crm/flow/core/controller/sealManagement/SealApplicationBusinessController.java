package com.topsec.crm.flow.core.controller.sealManagement;

import com.topsec.crm.flow.api.vo.sealApplication.SealApplicationInfoVO;
import com.topsec.crm.flow.api.vo.sealApplication.SealApplicationQueryVO;
import com.topsec.crm.flow.core.service.SealApplicationService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.operation.api.entity.Seal.CrmSealProcessConfigVO;
import com.topsec.crm.operation.api.entity.Seal.CrmSealTypeProcessVO;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/business/sealApplication")
@Tag(name = "印鉴申请-业务接口", description = "/business/sealApplication")
@RequiredArgsConstructor
@Validated
public class SealApplicationBusinessController extends BaseController {
    private final SealApplicationService sealApplicationService;


    @PostMapping("/page")
    @Operation(summary = "分页查询")
    @PreAuthorize(hasPermission = "crm_seal_apply",dataScope="crm_seal_apply")
    public JsonObject<TableDataInfo> page(@RequestBody SealApplicationQueryVO queryVO){
        startPage();
        DataScopeParam scopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdList = scopeParam !=null ? scopeParam.getPersonIdList() : Collections.EMPTY_SET;
        return new JsonObject<>(sealApplicationService.getSealApplicationInfoByQuery(queryVO, personIdList));
    }

    @GetMapping("/getSealInfoByProcessInstanceId")
    @Operation(summary = "获取印鉴详情信息")
    @PreAuthorize(hasPermission = "crm_seal_apply")
    public JsonObject<SealApplicationInfoVO> getSealInfoByProcessInstanceId(@RequestParam String processInstanceId){
        return new JsonObject<>(sealApplicationService.getSealInfoByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/getProcessConfigVO")
    @Operation(summary = "获取所有印鉴类型")
    @PreAuthorize(hasPermission = "crm_seal_apply")
    public JsonObject<List<CrmSealProcessConfigVO>> getSealInfoBgetProcessConfigVOyProcessInstanceId(){
        return new JsonObject<>(sealApplicationService.getProcessConfigVO());
    }

    @GetMapping("/getSealTypeProcessVO")
    @Operation(summary = "获取所有印鉴可申请印鉴类型")
    @PreAuthorize(hasPermission = "crm_seal_apply")
    public JsonObject<List<CrmSealTypeProcessVO>> getSealInfoBgetProcessConfigVOyProcessInstanceId(@RequestParam String processDefinitionKey){
        return new JsonObject<>(sealApplicationService.getSealTypeProcessVO(processDefinitionKey));
    }


}
