package com.topsec.crm.flow.core.controller.borrowForForward;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.flow.api.dto.borrowForForward.BorrowForForwardDTO;
import com.topsec.crm.flow.api.dto.borrowForForward.BorrowForForwardFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.borrowForForward.BorrowForForwardProductDTO;
import com.topsec.crm.flow.api.dto.customer.CustomerNaApproveFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.customer.CustomerVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BorrowForForward;
import com.topsec.crm.flow.core.entity.BorrowForForwardProduct;
import com.topsec.crm.flow.core.entity.BorrowForProbation;
import com.topsec.crm.flow.core.process.impl.BorrowForForwardProcessService;
import com.topsec.crm.flow.core.service.BorrowForProbationService;
import com.topsec.crm.flow.core.service.IBorrowForForwardProductService;
import com.topsec.crm.flow.core.service.IBorrowForForwardService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteBorrowForProbationClient;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.entity.CrmBorrowForProbationDeviceVO;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.TosDepartmentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 转借产品控制器
 */
@RestController
@RequestMapping("/borrowForForwardProduct")
@Tag(name = "转借产品控制器", description = "/borrowForForwardProduct")
public class BorrowForForwardProductController extends BaseController {

    @Autowired
    private IBorrowForForwardService borrowForForwardService;
    @Autowired
    private IBorrowForForwardProductService borrowForForwardProductService;
    @Autowired
    private RemoteBorrowForProbationClient remoteBorrowForProbationClient;

    @PostMapping("/page")
    @Operation(summary = "转借产品分页列表")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<PageUtils<BorrowForForwardProductDTO>> listCustomer(@RequestBody BorrowForForwardDTO borrowForForwardDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(borrowForForwardDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询转借流程信息
        BorrowForForward bff = borrowForForwardService.query()
                .eq("process_instance_id", borrowForForwardDTO.getProcessInstanceId()).one();

        startPage();
        //查询产品关联关系列表信息
        List<BorrowForForwardProduct> forwardProducts = borrowForForwardProductService.query().eq("forward_id", bff.getId()).list();
        List<BorrowForForwardProductDTO> borrowForForwardProductDTOS = HyperBeanUtils.copyListPropertiesByJackson(forwardProducts, BorrowForForwardProductDTO.class);
        //查询具体产品信息
        for (BorrowForForwardProductDTO borrowForForwardProductDTO : borrowForForwardProductDTOS) {
            String probationId = borrowForForwardProductDTO.getProjectProbationDeviceId();
            JsonObject<CrmBorrowForProbationDeviceVO> jObj = remoteBorrowForProbationClient.getBorrowForProbationDeviceById(probationId);
            if(jObj.isSuccess()){
                CrmBorrowForProbationDeviceVO deviceVO = jObj.getObjEntity();
                HyperBeanUtils.copyProperties(deviceVO, borrowForForwardProductDTO);
            }
        }

        //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
        PageUtils dataTable = getDataTable(forwardProducts,borrowForForwardProductDTOS);
        return new JsonObject<>(dataTable);
    }
}
