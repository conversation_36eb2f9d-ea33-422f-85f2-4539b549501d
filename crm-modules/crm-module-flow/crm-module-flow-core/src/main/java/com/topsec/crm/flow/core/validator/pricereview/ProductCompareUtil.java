package com.topsec.crm.flow.core.validator.pricereview;

import com.topsec.crm.flow.api.dto.pricereview.PriceReviewOutsourcingServiceDTO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductThirdDTO;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementProductVo;
import com.topsec.crm.project.api.entity.CrmProjectOutsourcingServiceVo;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ProductCompareUtil {
    /**
     * 首先按productId 分组
     * map key- productId
     * value - 对应的 行 list
     */
    @SneakyThrows
    public static<T> Map<String, List<T>> convert(List<T> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return Collections.emptyMap();
        }
        T first = productList.get(0);
        Class<?> aClass = first.getClass();

        if(!(first instanceof PriceReviewProductOwnDTO) &&
                !(first instanceof PriceReviewProductThirdDTO)&&
                !(first instanceof PriceReviewOutsourcingServiceDTO) &&
                !(first instanceof CrmProjectOutsourcingServiceVo) &&
                !(first instanceof SalesAgreementProductVo)){
            throw new IllegalArgumentException("不支持的类");
        }

        Field productIdField = aClass.getDeclaredField("productId");
        productIdField.setAccessible(true);

        return productList.stream()
                .collect(Collectors.groupingBy(
                        product->{
                            try {
                                return (String) productIdField.get(product);
                            } catch (IllegalAccessException e) {
                                throw new RuntimeException(e);
                            }
                        }));

    }




    @SneakyThrows
    public static<T> boolean validate(List<T> current, List<T>  snapshot, String compareField){
        Map<String, List<T>> currentMap = convert(current);
        Map<String, List<T>> snapshotMap = convert(snapshot);

        return validate(currentMap, snapshotMap, compareField);
    }
    @SneakyThrows
    public static<T> boolean validate(Map<String, List<T>> currentMap , Map<String, List<T>>   snapshotMap, String compareField){

        return validate(currentMap, snapshotMap, compareField, null);
    }

    public static<T> boolean validate(List<T> current, List<T>  snapshot, String compareField ,
                                      BiFunction<Map<Object, Integer>,Map<Object, Integer>,Boolean> biFunction){
        Map<String, List<T>> currentMap = convert(current);
        Map<String, List<T>> snapshotMap = convert(snapshot);
        return validate(currentMap, snapshotMap,compareField,biFunction);
    }

    @SneakyThrows
    public static<T> boolean validate(Map<String, List<T>> currentMap , Map<String, List<T>>   snapshotMap, String compareField ,
                                      BiFunction<Map<Object, Integer>,Map<Object, Integer>,Boolean> biFunction){

        // 如果productId 不同，直接返回false
        if (!currentMap.keySet().equals(snapshotMap.keySet())) {
            return false;
        }

        if (MapUtils.isEmpty(currentMap)) return true;
        List<T> ts = currentMap.values().stream().findFirst().orElse(Collections.emptyList());
        Object first = ts.get(0);
        Class<?> aClass = first.getClass();
        Field field = aClass.getDeclaredField(compareField);
        field.setAccessible(true);

        for (Map.Entry<String, List<T>> entry : currentMap.entrySet()) {
            String key = entry.getKey();
            List<T> currentRecordList = entry.getValue();
            List<T> snapshotRecordList = snapshotMap.get(key);
            // key-compareValue value-productNum
            Map<Object, Integer> currentCompareMap = groupByCompareValue(currentRecordList, field);
            Map<Object, Integer> snapshotCompareMap = groupByCompareValue(snapshotRecordList, field);


            if (biFunction!=null) {
                Boolean apply = biFunction.apply(currentCompareMap, snapshotCompareMap);
                if (!apply){
                    return false;
                }
            }else {
                //不传入比较函数，则比较key和value是否相等
                if (!(currentCompareMap.keySet().equals(snapshotCompareMap.keySet()))) return false;
                for (Map.Entry<Object, Integer> compareValueEntry : currentCompareMap.entrySet()) {
                    Object compareValue = compareValueEntry.getKey();
                    Integer currentProductNum = compareValueEntry.getValue();
                    Integer snapshotProductNum = snapshotCompareMap.get(compareValue);
                    if (!currentProductNum.equals(snapshotProductNum)) return false;
                }
            }
        }

        return true;
    }


    private static  <T> Map<Object,Integer> groupByCompareValue(List<T> recordList, Field field){

        return recordList.stream()
                .filter(p->{
                    Object compareValue;
                    try {
                        compareValue = field.get(p);
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                    return compareValue != null;
                })
                .collect(Collectors.groupingBy(r -> {
                    Object compareValue;
                    try {
                        compareValue = field.get(r);
                        if (compareValue instanceof BigDecimal decimal) {
                            return decimal.setScale(6, RoundingMode.HALF_UP);
                        }
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                    return compareValue;
                }, Collectors.summingInt(r -> {
                    try {
                        Class<?> aClass = r.getClass();
                        Field productNumField = aClass.getDeclaredField("productNum");
                        productNumField.setAccessible(true);
                        return ((Number) productNumField.get(r)).intValue();
                    } catch (IllegalAccessException | NoSuchFieldException e) {
                        throw new RuntimeException(e);
                    }
                })));

    }

    public static <T> boolean validate(Map<String, List<T>> currentMap, Map<String, List<T>>  snapshotMap, BiFunction<List<T>,List<T>,Boolean> biFunction){

        // 如果大小不同，直接返回false
        if (!currentMap.keySet().equals(snapshotMap.keySet())) {
            return false;
        }

        for (Map.Entry<String, List<T>> entry : currentMap.entrySet()) {
            String key = entry.getKey();
            List<T> currentSet = entry.getValue();
            List<T> snapshotSet = snapshotMap.get(key);
            //相同key 对应的value set，set 中有一个false 则返回false
            Boolean result=biFunction.apply(currentSet, snapshotSet);
            if (!result) return false;
        }
        return true;
    }
}
