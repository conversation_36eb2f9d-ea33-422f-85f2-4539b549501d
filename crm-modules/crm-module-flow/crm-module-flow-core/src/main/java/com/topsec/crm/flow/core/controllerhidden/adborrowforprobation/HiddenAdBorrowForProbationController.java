package com.topsec.crm.flow.core.controllerhidden.adborrowforprobation;

import com.topsec.crm.flow.core.service.AdBorrowForProbationService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/hidden/adBorrowForProbation")
@Tag(name = "样机借试用")
@RequiredArgsConstructor
public class HiddenAdBorrowForProbationController extends BaseController {

    private final AdBorrowForProbationService adBorrowForProbationService;

    @GetMapping("/flow/selectSnOccupyList")
    @Operation(summary = "根据总代ID查询序列号占用列表")
    public JsonObject<List<String>> selectSnOccupyList(@RequestParam(required = false) String agentId) {
        return new JsonObject<>(adBorrowForProbationService.selectSnOccupyList(agentId));
    }
}
