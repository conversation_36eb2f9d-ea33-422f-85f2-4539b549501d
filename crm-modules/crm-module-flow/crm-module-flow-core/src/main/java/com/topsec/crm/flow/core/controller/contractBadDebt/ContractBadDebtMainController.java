package com.topsec.crm.flow.core.controller.contractBadDebt;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.entity.CrmContractBaseInfoVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.erp.api.RemoteAuditRecordService;
import com.topsec.crm.erp.api.entity.CrmAuditRecordVO;
import com.topsec.crm.flow.api.dto.contractBadDebt.*;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.CrmContractProcessInfoVO;
import com.topsec.crm.flow.api.enums.AgentapprovalStatusEnum;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.process.impl.ContractBadDebtProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.date.DateUtil;
import com.topsec.crm.framework.common.util.date.DateUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyPriceStatisticsVO;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contractBadDebtMain")
@Tag(name = "合同坏账相关Controller", description = "/contractBadDebtMain/flow")
@RequiredArgsConstructor
@Validated
public class ContractBadDebtMainController extends BaseController {
    @Autowired
    private ContractBadDebtProcessService contractBadDebtProcessService;
    @Autowired
    private IContractBadDebtMainService contractBadDebtMainService;
    @Autowired
    private IContractBadDebtDetailService contractBadDebtDetailService;
    @Autowired
    private IContractBadDebtAttachmentService contractBadDebtAttachmentService;
    @Autowired
    private IContractBadDebtExecuteSnapshotService contractBadDebtExecuteSnapshotService;
    @Autowired
    private RemoteContractExecuteService remoteContractExecuteService;
    @Autowired
    private RemoteAuditRecordService remoteAuditRecordService;
    @Autowired
    private RemoteAgentService remoteAgentService;
    @Autowired
    private ContractReviewMainService contractReviewMainService;
    @Autowired
    private RemoteContractReviewService remoteContractReviewService;

    @PostMapping("/launch")
    @Operation(summary = "发起合同坏账审批流程")
    @PreAuthorize(hasPermission = "crm_contract_bad_debt_add")
//    @Audit(eventName = "launch" ,eventDesc = "发起合同坏账审批流程", eventType = "NA客户申请",getMethodData = true)
    public JsonObject<Boolean> launch(@Valid @RequestBody ContractBadDebtApproveFlowLaunchDTO launchDTO) {
        Assert.isFalse(CollectionUtil.isEmpty(launchDTO.getDetailVos()),"至少选择一条合同信息。");

        //权限校验
        for (ContractBadDebtDetailVo detailVo : launchDTO.getDetailVos()) {
            JsonObject<Boolean> booleanJsonObject = remoteContractExecuteService.hasRight(detailVo.getContractNumber(), UserInfoHolder.getCurrentPersonId());
            if(!booleanJsonObject.isSuccess() || booleanJsonObject.getObjEntity() == false){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }

        //1.重复发起校验
        Set<String> contractNumbers = launchDTO.getDetailVos().stream().map(ContractBadDebtDetailVo::getContractNumber).collect(Collectors.toSet());
        List<ContractBadDebtDetail> exists = contractBadDebtDetailService.query().in("contract_number", contractNumbers).list();
        StringBuilder errorMsg = new StringBuilder();
        for (ContractBadDebtDetail exist : exists) {
            errorMsg.append(exist.getContractNumber() + "、");
        }
        if(CollectionUtil.isNotEmpty(exists)) {
            Assert.isFalse(true, errorMsg.substring(0, errorMsg.length() - 1) + "，已经存在合同坏账，请勿重复发起。");
        }

        /* 2.校验坏账发起条件（and关系）：
            1、合同评审过了03步；
            2、合同未执行完成（合同执行完成：收入确认办结，应收款已回款，款项金额全开发票，合同原件全部上交，涉及退换货协议全部上交）；
            3、签约单位不为渠道商（中间商）；
            4、当前登录人有发起权限且在对应的数据范围内的合同（人个合同数据、管辖部门合同数据等）
         */
        //2.1只要在合同执行中的，都是过了03步
        JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(contractNumbers);
        if(byContractNumberBatch.isSuccess() && byContractNumberBatch.getObjEntity() != null){
            List<CrmContractExecuteVO> executes = byContractNumberBatch.getObjEntity();
            Assert.isFalse(CollectionUtil.isEmpty(executes), "当前所有合同评审都未通过03步，无法发起合同坏账");
            for (String contractNumber : contractNumbers) {
                CrmContractExecuteVO exist = executes.stream().filter(e -> e.getContractNumber().equals(contractNumber)).findFirst().orElse(null);
                Assert.isFalse(exist == null, "存在合同的合同评审"+exist.getContractNumber()+"未通过03步，无法发起合同坏账");
            }

            for (CrmContractExecuteVO execute : executes) {
                //2.2查询是否有未执行完的合同
                Assert.isFalse(execute.getIsExecuteComplete() == null || execute.getIsExecuteComplete() , "存在合同"+execute.getContractNumber()+"已经执行完成，无法发起合同坏账");

                //2.3签约单位不为渠道商
                String contractCompanyId = execute.getContractCompanyId();
                JsonObject<CrmAgentVo> agentInfo = remoteAgentService.getAgentInfo(contractCompanyId);
                if(agentInfo.isSuccess() && agentInfo.getObjEntity() != null){
                    Assert.isFalse(agentInfo.getObjEntity() != null , "存在合同："+execute.getContractNumber()+"签约单位为渠道商，无法发起合同坏账");
                }
            }

        }

        //3.发起流程
        contractBadDebtProcessService.launch(launchDTO);
        return new JsonObject<>(true);
    }

    @GetMapping("/info")
    @Operation(summary = "合同坏账详情信息")
    @PreFlowPermission
    public JsonObject<ContractBadDebtMainVo> info(@RequestParam String processInstanceId) {
        //ProcessInstanceId校验
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //1.查询流程基本信息
        ContractBadDebtMain contractBadDebtMain = contractBadDebtMainService.query().eq("process_instance_id", processInstanceId).one();
        ContractBadDebtMainVo cbdmv = HyperBeanUtils.copyPropertiesByJackson(contractBadDebtMain, ContractBadDebtMainVo.class);

        //2.查询合同坏账明细
        List<ContractBadDebtDetail> list = contractBadDebtDetailService.query().eq("process_instance_id", processInstanceId).list();
        Set<String> contractNumbers = list.stream().map(ContractBadDebtDetail::getContractNumber).collect(Collectors.toSet());
        //2.1查询合同执行信息,流程审批中的-实时查询；审批通过的-查询快照
        if (contractBadDebtMain.getProcessState().intValue() == AgentapprovalStatusEnum.SPZ.getCode()) {
            //批量查询合同执行信息
            JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(contractNumbers);
            if (byContractNumberBatch.isSuccess()) {
                //按合同号对合同流程做分组
                List<CrmContractExecuteVO> contractInfos = byContractNumberBatch.getObjEntity();
                //查询用户信息
                JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(contractInfos.stream().map(CrmContractExecuteVO::getSaleId).toList());
                List<EmployeeVO> users = byIds.getObjEntity();
                //查询流程信息
                List<ContractReviewMainBaseInfoDTO> rmbis = contractReviewMainService.getByContractNumberBatch(contractInfos.stream().map(CrmContractExecuteVO::getContractNumber).collect(Collectors.toSet()));

                for (ContractBadDebtDetail contractBadDebtDetail : list) {
                    CrmContractExecuteVO crmContractExecuteVO = contractInfos.stream().filter(c -> c.getContractNumber().equals(contractBadDebtDetail.getContractNumber())).findFirst().orElse(null);
                    //补充原销售单位字段
                    EmployeeVO employeeVO = users.stream().filter(e -> e.getUuid().equals(crmContractExecuteVO.getSaleId())).findFirst().orElse(null);
                    crmContractExecuteVO.setSaleDeptName(employeeVO.getDept() != null ? employeeVO.getDept().getName() : "");
                    contractBadDebtDetail.setContractExecuteVO(crmContractExecuteVO);

                    //5.查询合同明细
                    List<ContractReviewMainBaseInfoDTO> list1 = rmbis.stream().filter(e -> e.getProcessInstanceId() != null).filter(e -> e.getContractNumber().equals(contractBadDebtDetail.getContractNumber())).toList();
                    List<com.topsec.crm.flow.api.dto.contractreview.CrmContractProcessInfoVO> crmContractProcessInfoVOS = new ArrayList<com.topsec.crm.flow.api.dto.contractreview.CrmContractProcessInfoVO>();
                    for (ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO : list1) {
                        com.topsec.crm.flow.api.dto.contractreview.CrmContractProcessInfoVO crmContractProcessInfoVO = HyperBeanUtils.copyPropertiesByJackson(contractReviewMainBaseInfoDTO, CrmContractProcessInfoVO.class);
                        crmContractProcessInfoVO.setContractId(contractReviewMainBaseInfoDTO.getId());
                        crmContractProcessInfoVOS.add(crmContractProcessInfoVO);
                    }
                }
            }
        } else if (contractBadDebtMain.getProcessState().intValue() == AgentapprovalStatusEnum.YSP.getCode()) {
            //查询快照
            List<ContractBadDebtExecuteSnapshot> snapshots = contractBadDebtExecuteSnapshotService.query().eq("process_instance_id", processInstanceId).list();
            //查询流程信息
            List<ContractReviewMainBaseInfoDTO> rmbis = contractReviewMainService.getByContractNumberBatch(snapshots.stream().map(ContractBadDebtExecuteSnapshot::getContractNumber).collect(Collectors.toSet()));


            for (ContractBadDebtDetail contractBadDebtDetail : list) {
                ContractBadDebtExecuteSnapshot snapshot = snapshots.stream().filter(c -> c.getContractNumber().equals(contractBadDebtDetail.getContractNumber())).findFirst().orElse(null);
                //快照数据的合同执行状态统一展示“坏账”
                snapshot.setContractExecuteStatus(2);
                //补充原销售单位字段
                JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(snapshot.getSaleId());
                if(byId.isSuccess() && byId.getObjEntity() != null){
                    snapshot.setSaleDeptName(byId.getObjEntity().getDept() != null ? byId.getObjEntity().getDept().getName() : "");
                }
                contractBadDebtDetail.setContractExecuteVO(HyperBeanUtils.copyPropertiesByJackson(snapshot, CrmContractExecuteVO.class));

                //5.查询合同明细
                List<ContractReviewMainBaseInfoDTO> list1 = rmbis.stream().filter(e -> e.getProcessInstanceId() != null).filter(e -> e.getContractNumber().equals(contractBadDebtDetail.getContractNumber())).toList();
                List<CrmContractProcessInfoVO> crmContractProcessInfoVOS = new ArrayList<CrmContractProcessInfoVO>();
                for (ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO : list1) {
                    CrmContractProcessInfoVO crmContractProcessInfoVO = HyperBeanUtils.copyPropertiesByJackson(contractReviewMainBaseInfoDTO, CrmContractProcessInfoVO.class);
                    crmContractProcessInfoVO.setContractId(contractReviewMainBaseInfoDTO.getId());
                    crmContractProcessInfoVOS.add(crmContractProcessInfoVO);
                }
            }
        }

        LocalDateTime dd1 = LocalDateTime.now();
        //2.2查询合同毛利
        JsonObject<Map<String, CrmProjectDirectlyPriceStatisticsVO>> obj = remoteContractExecuteService.getContractPriceStatisticsBatch(list.stream().map(ContractBadDebtDetail::getContractNumber).collect(Collectors.toSet()));
        LocalDateTime dd2 = LocalDateTime.now();
        System.out.println("dd2-dd1=============耗时"+ DateUtil.getMillisDifferenceByDuration(dd1,dd2));
        if(obj.isSuccess() && obj.getObjEntity() != null) {
            Map<String, CrmProjectDirectlyPriceStatisticsVO> objEntity = obj.getObjEntity();
            for (ContractBadDebtDetail contractBadDebtDetail : list) {
                CrmProjectDirectlyPriceStatisticsVO crmProjectDirectlyPriceStatisticsVO = objEntity.get(contractBadDebtDetail.getContractNumber());
                contractBadDebtDetail.setGrossMargin(crmProjectDirectlyPriceStatisticsVO.getTotal().getGrossMargin());
            }
        }

        cbdmv.setDetailVos(HyperBeanUtils.copyListPropertiesByJackson(list, ContractBadDebtDetailVo.class));


        //3.查询附件列表
        List<ContractBadDebtAttachment> attachments = contractBadDebtAttachmentService.query().eq("process_instance_id", processInstanceId).list();
        cbdmv.setAttachmentDTOS(HyperBeanUtils.copyListPropertiesByJackson(attachments, ContractBadDebtAttachmentDTO.class));

        return new JsonObject<>(cbdmv);
    }

    @PostMapping("/update")
    @Operation(summary = "修改申请")
    @PreFlowPermission
    public JsonObject<Boolean> update(@Valid @RequestBody ContractBadDebtApproveFlowLaunchDTO launchDTO) {
        //ProcessInstanceId校验
        PreFlowPermissionAspect.checkProcessInstanceId(launchDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        List<ContractBadDebtDetailVo> detailVos = launchDTO.getDetailVos();
        Assert.isFalse(CollectionUtil.isEmpty(detailVos),"至少选择一条合同信息。");
        String processInstanceId = launchDTO.getProcessInstanceId();

        //1.重复发起校验
        List<String> contractNumbers = detailVos.stream().map(ContractBadDebtDetailVo::getContractNumber).toList();
        List<ContractBadDebtDetail> exists = contractBadDebtDetailService.query()
                .in("contract_number", contractNumbers)
                .ne("process_instance_id", processInstanceId)
                .list();
        StringBuilder errorMsg = new StringBuilder();
        for (ContractBadDebtDetail exist : exists) {
            errorMsg.append(exist.getContractNumber() + ",");
        }
        if(CollectionUtil.isNotEmpty(exists)) {
            Assert.isFalse(true, errorMsg.substring(0, errorMsg.length() - 1) + "，已经存在合同坏账，请勿重复发起。");
        }

        //2.查询流程基本信息
        ContractBadDebtMain contractBadDebtMain = contractBadDebtMainService.query().eq("process_instance_id", processInstanceId).one();

        //3.删除不在提交内的合同明细，修改已经变更的，并添加最新的明细
        List<String> updateIds = new ArrayList<String>();
        for (ContractBadDebtDetailVo detail : detailVos) {
            if(StringUtils.isNotBlank(detail.getId())){
                updateIds.add(detail.getId());
            }
        }
        contractBadDebtDetailService.remove(new QueryWrapper<ContractBadDebtDetail>()
                .eq("process_instance_id", processInstanceId)
                .notIn(CollectionUtil.isNotEmpty(updateIds),"id", updateIds)
        );
        //3.1新增或者编辑合同坏账明细
        List<ContractBadDebtDetail> details = HyperBeanUtils.copyListPropertiesByJackson(detailVos, ContractBadDebtDetail.class);
        for (ContractBadDebtDetail detail : details) {
            detail.setId(StringUtils.isNotBlank(detail.getId()) ? detail.getId() :UUID.randomUUID().toString());
            detail.setProcessInstanceId(processInstanceId);
            detail.setProcessNumber(contractBadDebtMain.getProcessNumber());
        }
        contractBadDebtDetailService.saveOrUpdateBatch(details);

        //4.删除历史附件，保存附件信息
        contractBadDebtAttachmentService.remove(new QueryWrapper<ContractBadDebtAttachment>().eq("process_instance_id", processInstanceId));
        List<ContractBadDebtAttachment> attachments = HyperBeanUtils.copyListPropertiesByJackson(launchDTO.getAttachmentDTOS(), ContractBadDebtAttachment.class);
        for (ContractBadDebtAttachment attachment : attachments) {
            attachment.setId(UUID.randomUUID().toString());
            attachment.setProcessInstanceId(processInstanceId);
            attachment.setCreateUserName(NameUtils.getName(UserInfoHolder.getCurrentPersonId()));
        }
        if(CollectionUtil.isNotEmpty(attachments)) {
            contractBadDebtAttachmentService.saveBatch(attachments);
        }

        return new JsonObject<>(true);
    }

    @PostMapping("/punish")
    @Operation(summary = "坏账处罚")
    @PreFlowPermission(hasAnyNodes = {"contractBadDebt_02"})
    public JsonObject<Boolean> punish(@Valid @RequestBody ContractBadDebtApproveFlowLaunchDTO launchDTO) {
        //ProcessInstanceId校验
        PreFlowPermissionAspect.checkProcessInstanceId(launchDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.坏账处罚
        List<ContractBadDebtDetailVo> detailVos = launchDTO.getDetailVos();
        for (ContractBadDebtDetailVo detailVo : detailVos) {
            contractBadDebtDetailService.update(
                    new UpdateWrapper<ContractBadDebtDetail>().eq("contract_number",detailVo.getContractNumber())
                    .set("xsfzc_rate", detailVo.getXsfzcRate())
                    .set("bmfzr_rate", detailVo.getBmfzrRate())
                    .set("xs_rate", detailVo.getXsRate())
            );
        }
        return new JsonObject<>(true);
    }

    @PostMapping("/returnCheck")
    @Operation(summary = "流程退回审定时间和合同状态校验")
    @PreFlowPermission
    public JsonObject<Boolean> returnCheck(@RequestParam String processInstanceId) {
        //ProcessInstanceId校验
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //查询最新的审计时间
        JsonObject<CrmAuditRecordVO> latestAuditRecord = remoteAuditRecordService.getLatestAuditRecord();
        Assert.isFalse(!latestAuditRecord.isSuccess(),"暂获取不到最新审计时间，无法发起退回。");

        CrmAuditRecordVO objEntity = latestAuditRecord.getObjEntity();
        LocalDateTime auditTime = objEntity.getAuditTime();

        //1.对比合同明细办结时间与审计的时间-审定时间超过办结时间不允许退回、流程办结超出2个月不允许退回（需要支持办结退回，办结退回需要反写相关数据）
        //1.1.查询流程基本信息
        ContractBadDebtMain contractBadDebtMain = contractBadDebtMainService.query().eq("process_instance_id", processInstanceId).one();
        LocalDateTime passTime = contractBadDebtMain.getPassTime();//流程办结时间
        if(passTime != null) {
            Assert.isFalse(auditTime.isAfter(passTime), "审定时间超过办结时间不允许退回。");

            LocalDateTime now = DateUtil.dateToLocalDateTime(DateUtils.addMonths(new Date(), -2));
            Assert.isFalse(passTime.isBefore(now), "流程办结超出2个月不允许退回。");
        }

        //2.查询合同执行的所有流程状态是不是都是“坏账”状态
        //2.查询合同坏账明细-默认合同坏账中的合同是一定存在与合同执行中的。
        List<ContractBadDebtDetail> list = contractBadDebtDetailService.query().eq("process_instance_id", processInstanceId).list();
        JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(list.stream().map(ContractBadDebtDetail::getContractNumber).collect(Collectors.toSet()));
        if(byContractNumberBatch.isSuccess()){
            Assert.isFalse(CollectionUtil.isEmpty(byContractNumberBatch.getObjEntity()), "存在合同当前状态不为“坏账/中止”状态，不允许退回");
            for (CrmContractExecuteVO ccev : byContractNumberBatch.getObjEntity()) {
                Assert.isFalse(ccev.getContractExecuteStatus() != 2 && ccev.getContractExecuteStatus() != 3, "存在合同当前状态不为“坏账/中止”状态，不允许退回");
            }
        }

        return new JsonObject<>(true);
    }

}
