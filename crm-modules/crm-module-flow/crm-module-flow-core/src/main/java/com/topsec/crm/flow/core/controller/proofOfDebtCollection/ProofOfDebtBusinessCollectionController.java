package com.topsec.crm.flow.core.controller.proofOfDebtCollection;

import com.topsec.crm.flow.api.dto.proofOfDebtCollection.ProofOfDebtCollectionVO;
import com.topsec.crm.flow.core.service.ProofOfDebtCollectionService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/proofOfDebtCollection")
@Tag(name = "催款证据-业务", description = "催款证据-业务")
@RequiredArgsConstructor
public class ProofOfDebtBusinessCollectionController {

    private final ProofOfDebtCollectionService proofOfDebtCollectionService;

    @PostMapping("/detail")
    @Operation(summary = "催款证据流程详情")
    @PreAuthorize(hasPermission = "crm_contract_receivable_collection_proof", dataScope="crm_contract_receivable_collection_proof")
    public JsonObject<ProofOfDebtCollectionVO> detail(@RequestParam String processInstanceId) {
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        Set<String> personIds = SetUtils.emptyIfNull(dataScopeParam.getPersonIdList());
        ProofOfDebtCollectionVO detail = proofOfDebtCollectionService.detail(processInstanceId);
        String contractOwnerId = detail.getContractOwnerId();
        if (StringUtils.isBlank(contractOwnerId)){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        if (!dataScopeParam.getAllScope() &&!personIds.contains(contractOwnerId)){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        NameUtils.setName(detail);
        return new JsonObject<>(detail);
    }



}
