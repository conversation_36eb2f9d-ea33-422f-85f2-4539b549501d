package com.topsec.crm.flow.core.controller.salesAgreement;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.topsec.crm.flow.api.vo.ProcessAttachmentVo;
import com.topsec.crm.flow.api.vo.salesAgreement.*;
import com.topsec.crm.flow.api.vo.sealApplication.SealApplicationInfoVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.SalesAgreementMembers;
import com.topsec.crm.flow.core.entity.SalesAgreementMembersReviewMain;
import com.topsec.crm.flow.core.entity.SalesAgreementPriceReviewMain;
import com.topsec.crm.flow.core.entity.SalesAgreementReviewMain;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.ExecutionException;


/**
 * 销售协议流程主表
 *
 * <AUTHOR>
 * @email
 * @date 2024-12-31 10:52:58
 */
@RestController
@RequestMapping("/business/salesAgreement")
@Tag(name = "【销售协议业务数据查询】", description = "salesAgreement")
@RequiredArgsConstructor
@Validated
public class SalesAgreementBusinessController extends BaseController {
    private final SalesAgreementReviewMainService salesAgreementReviewMainService;

    private final SalesAgreementPriceReviewMainService salesAgreementPriceReviewMainService;

    private final SalesAgreementMembersService salesAgreementMembersService;

    private final SalesAgreementMembersReviewMainService salesAgreementMembersReviewMainService;

    private final SalesAgreementProductService salesAgreementProductService;

    private final ThreeProcurementPaymentReviewMainService threeProcurementPaymentReviewMainService;

    private final TfsNodeClient tfsNodeClient;

    private final SealApplicationService sealApplicationService;




    @PostMapping("/findSalesAgreementProcessPage")
    @Operation(summary = "销售协议列表")
    @PreAuthorize(hasPermission = "crm_sales_agreements",dataScope = "crm_sales_agreements")
    public JsonObject<PageUtils<SalesAgreementReviewMainVo>>findSalesAgreementProcessPage(@RequestBody SalesAgreementProcessQuery salesAgreementProcessQuery){
        String currentPersonId = UserInfoHolder.getCurrentPersonId();

        List<String> processInstanceIdList = salesAgreementMembersService.findProcessIds(currentPersonId,getDepartmentId());

        salesAgreementProcessQuery.setProcessInstanceIds(processInstanceIdList);
        salesAgreementProcessQuery.setPersonId(currentPersonId);
        Page<SalesAgreementReviewMain> salesAgreementProcessPage = salesAgreementReviewMainService.findSalesAgreementProcessPage(salesAgreementProcessQuery);
        IPage<SalesAgreementReviewMainVo> page = salesAgreementProcessPage.convert(crmThreeProcurement -> {
            return HyperBeanUtils.copyPropertiesByJackson(crmThreeProcurement, SalesAgreementReviewMainVo.class);
        });
        if (CollectionUtils.isNotEmpty(page.getRecords())){
            List<String> processInstanceIds = page.getRecords().stream().map(SalesAgreementReviewMainVo::getProcessInstanceId).toList();
            Map<String, Set<ApproveNode>> stringSetMap = Optional.ofNullable(tfsNodeClient.queryNodeByProcessInstanceIdList(ListUtils.emptyIfNull(processInstanceIds)))
                    .map(JsonObject::getObjEntity).orElseThrow(() -> new RuntimeException("审批节点查询失败"));
            page.getRecords().forEach(salesAgreementReviewMainVo -> {
                salesAgreementReviewMainVo.setApprovalNode(stringSetMap.get(salesAgreementReviewMainVo.getProcessInstanceId()));
            });
        }
        return new JsonObject<>(new PageUtils<>(page));
    }



    @GetMapping("/querySalesAgreementBaseInfo")
    @Operation(summary = "销售协议基本信息")
    @PreAuthorize(hasPermission = "crm_sales_agreements",dataScope = "crm_sales_agreements")
    public JsonObject<SalesAgreementReviewMainVo> querySalesAgreementBaseInfo(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        List<String> processIds = salesAgreementMembersService.findProcessIds(UserInfoHolder.getCurrentPersonId(),getDepartmentId());
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(processInstanceId);
        if(CollectionUtils.isNotEmpty(processIds)){
            if(!processIds.contains(processInstanceId)){
                PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(),salesAgreement.getCreateUser());
            }
        }

        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(salesAgreement, SalesAgreementReviewMainVo.class));


    }



    @GetMapping("/querySalesAgreementProductList")
    @Operation(summary = "查询销售协议主流程自有产品")
    @PreAuthorize(hasPermission = "crm_sales_agreements",dataScope = "crm_sales_agreements")
    public JsonObject<SalesAgreementOwnProductVo> querySalesAgreementProductList(@RequestParam String processInstanceId) throws ExecutionException {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        List<String> processIds = salesAgreementMembersService.findProcessIds(UserInfoHolder.getCurrentPersonId(),getDepartmentId());
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(processInstanceId);
        if(CollectionUtils.isNotEmpty(processIds)){
            if(!processIds.contains(processInstanceId)){
                PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(),salesAgreement.getCreateUser());
            }
        }
        SalesAgreementOwnProductVo salesAgreementOwnProductVo = salesAgreementProductService.convertToSalesAgreementOwnProductVo(processInstanceId,true);
        return new JsonObject<>(salesAgreementOwnProductVo);
    }


    @PostMapping("/querySalesAgreementPriceProcessPage")
    @Operation(summary = "分页查询协议价格审批")
    @PreAuthorize(hasPermission = "crm_sales_agreements",dataScope = "crm_sales_agreements")
    public JsonObject<PageUtils<SalesAgreementProcessVo>> querySalesAgreementPriceProcessPage(@RequestBody SalesAgreementProcessQuery salesAgreementProcessQuery){
        String parentProcessInstanceId = salesAgreementProcessQuery.getParentProcessInstanceId();
        CrmAssert.hasText(parentProcessInstanceId, "流程实例id不能为空");
        List<String> processIds = salesAgreementMembersService.findProcessIds(UserInfoHolder.getCurrentPersonId(),getDepartmentId());
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(parentProcessInstanceId);
        if(CollectionUtils.isNotEmpty(processIds)){
            if(!processIds.contains(parentProcessInstanceId)){
                PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(),salesAgreement.getCreateUser());
            }
        }
        Page<SalesAgreementPriceReviewMain> salesAgreementPriceProcessPage = salesAgreementPriceReviewMainService.findSalesAgreementPriceProcessPage(salesAgreementProcessQuery);
        IPage<SalesAgreementProcessVo> convert = salesAgreementPriceProcessPage.convert(crmThreeProcurement -> {
            return HyperBeanUtils.copyPropertiesByJackson(crmThreeProcurement, SalesAgreementProcessVo.class);
        });
        NameUtils.setName(convert.getRecords());
        return new JsonObject<>(new PageUtils<>(convert));
    }



    @PostMapping("/querySalesAgreementMembersProcessPage")
    @Operation(summary = "查询协议成员流程审批")
    @PreAuthorize(hasPermission = "crm_sales_agreements",dataScope = "crm_sales_agreements")
    public JsonObject<PageUtils<SalesAgreementProcessVo>> querySalesAgreementMembersProcessPage(@RequestBody SalesAgreementProcessQuery salesAgreementProcessQuery) {
        String parentProcessInstanceId = salesAgreementProcessQuery.getParentProcessInstanceId();
        CrmAssert.hasText(parentProcessInstanceId, "流程实例id不能为空");
        List<String> processIds = salesAgreementMembersService.findProcessIds(UserInfoHolder.getCurrentPersonId(),getDepartmentId());
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(parentProcessInstanceId);
        if(CollectionUtils.isNotEmpty(processIds)){
            if(!processIds.contains(parentProcessInstanceId)){
                PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(),salesAgreement.getCreateUser());
            }
        }
        Page<SalesAgreementMembersReviewMain> salesAgreementMembersPage = salesAgreementMembersReviewMainService.findSalesAgreementMembersPage(salesAgreementProcessQuery);
        IPage<SalesAgreementProcessVo> convert = salesAgreementMembersPage.convert(crmThreeProcurement -> {
            return HyperBeanUtils.copyPropertiesByJackson(crmThreeProcurement, SalesAgreementProcessVo.class);
        });
        NameUtils.setName(convert.getRecords());
        return new JsonObject<>(new PageUtils<>(convert));


    }


    @GetMapping("/querySalesAgreementMembersList")
    @Operation(summary = "查询销售协议主流程协议成员列表")
    @PreAuthorize(hasPermission = "crm_sales_agreements",dataScope = "crm_sales_agreements")
    public JsonObject<List<SalesAgreementMembersVo> > querySalesAgreementMembersList(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        List<String> processIds = salesAgreementMembersService.findProcessIds(UserInfoHolder.getCurrentPersonId(),getDepartmentId());
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(processInstanceId);
        if(CollectionUtils.isNotEmpty(processIds)){
            if(!processIds.contains(processInstanceId)){
                PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(),salesAgreement.getCreateUser());
            }
        }
        List<SalesAgreementMembersVo> salesAgreementMembersVoList = salesAgreementMembersService.convertSalesAgreementMembersList(processInstanceId);
        return new JsonObject<>(salesAgreementMembersVoList);
    }


    @GetMapping("/querySalesAgreementProjects")
    @Operation(summary = "销售协议关联项目", method = "GET")
    public JsonObject<List<SalesAgreementProjectVo>> querySalesAgreementProjects(@RequestParam String processInstanceId) {
        SalesAgreementReviewMain agreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(processInstanceId);
        if (agreement == null) {
            throw new CrmException("销售协议不存在");
        }
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        List<String> processIds = salesAgreementMembersService.findProcessIds(UserInfoHolder.getCurrentPersonId(),getDepartmentId());
        if(CollectionUtils.isNotEmpty(processIds)){
            if(!processIds.contains(processInstanceId)){
                PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(),agreement.getCreateUser());
            }
        }
        List<SalesAgreementProjectVo> salesAgreementProjectVos = salesAgreementReviewMainService.selectSalesAgreementProjects(agreement.getId());
        return new JsonObject<>(salesAgreementProjectVos);

    }





    @GetMapping("/selectSalesAgreementAttachmentList")
    @Operation(summary = "查询销售协议以及子流程附件列表  type: 0 销售协议主流程 1：协议价格审批 2：协议成员变更")
    @PreAuthorize(hasPermission = "crm_sales_agreements",dataScope = "crm_sales_agreements")
    public JsonObject<List<ProcessAttachmentVo>> selectSalesAgreementAttachmentList(@RequestParam String processInstanceId, @RequestParam int type) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.getProcessInstanceId(processInstanceId, type);
        List<String> processIds = salesAgreementMembersService.findProcessIds(UserInfoHolder.getCurrentPersonId(),getDepartmentId());
        if(CollectionUtils.isNotEmpty(processIds)){
            if(!processIds.contains(processInstanceId)){
                PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(),salesAgreement.getCreateUser());
            }
        }
        List<ProcessAttachmentVo> listThreeProcurementAttachment = threeProcurementPaymentReviewMainService.getListThreeProcurementAttachment(salesAgreement.getAttachmentIds());
        return new JsonObject<>(listThreeProcurementAttachment);
    }



    @GetMapping("/querySealListByProcessInstanceId")
    @Operation(summary = "主流程详情查询印鉴列表")
    public JsonObject<List<SealApplicationInfoVO>> querySealListByProcessInstanceId(@RequestParam String processInstanceId){
        SalesAgreementReviewMain agreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(processInstanceId);
        if (agreement == null) {
            throw new CrmException("销售协议不存在");
        }
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        List<String> processIds = salesAgreementMembersService.findProcessIds(UserInfoHolder.getCurrentPersonId(),getDepartmentId());
        if(CollectionUtils.isNotEmpty(processIds)){
            if(!processIds.contains(processInstanceId)){
                PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(),agreement.getCreateUser());
            }
        }
        return new JsonObject<>(sealApplicationService.listSealInfoByParentProcessInstanceId(processInstanceId));
    }








}
