package com.topsec.crm.flow.core.controllerhidden;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.api.dto.BorrowForProbationDeviceOccupyStateVO;
import com.topsec.crm.flow.api.dto.ProductLockStateVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.ProcessExtensionInfo;
import com.topsec.crm.flow.core.mapper.ProcessExtensionInfoMapper;
import com.topsec.crm.flow.core.mapstruct.ProcessExtensionInfoConvertor;
import com.topsec.crm.flow.core.service.CommonService;
import com.topsec.crm.flow.core.service.FlowService;
import com.topsec.crm.flow.core.service.ProcessExtensionInfoService;
import com.topsec.crm.framework.common.ProcessExtensionInfoQuery;
import com.topsec.crm.framework.common.bean.CrmProjectProgressVO;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.bean.ProcessExtensionInfoVO;
import com.topsec.enums.ProcessDefinitionKeyEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/commonFlow")
@Tag(name = "流程公用的")
@RequiredArgsConstructor
public class FlowController {

    private final FlowService flowService;
    private final CommonService commonService;

    private final ProcessExtensionInfoService processExtensionInfoService;
    private final ProcessExtensionInfoMapper processExtensionInfoMapper;

    @GetMapping("/queryLockState")
    @Operation(summary = "查询产品记录锁定状态")
    public JsonObject<Map<String, ProductLockStateVO>> queryLockState(@RequestParam String projectId, @RequestParam Set<String> productRecordIdSet) {
        return new JsonObject<>(flowService.queryLockState(projectId, productRecordIdSet));
    }

    @GetMapping("/isProjectProductLocked")
    @Operation(summary = "查询项目产品锁定状态")
    public JsonObject<Boolean> isProjectProductLocked(@RequestParam String projectId) {
        return new JsonObject<>(flowService.isProjectProductLocked(projectId));
    }

    @PostMapping("/hasLockedProductOfProject")
    @Operation(summary = "检查项目中是否有锁定的产品")
    JsonObject<Boolean>  hasLockedProductOfProject(@RequestParam String projectId, @RequestBody Set<String> productRecordIdSet) {
        return new JsonObject<>(flowService.hasLockedProductOfProject(projectId,productRecordIdSet));
    }

    @PostMapping("/queryBorrowForProbationDeviceOccupyState")
    @Operation(summary = "查询借试用产品占用状态")
    public JsonObject<Map<String, BorrowForProbationDeviceOccupyStateVO>> queryBorrowForProbationDeviceOccupyState(@RequestBody Set<String> deviceIds) {
        return new JsonObject<>(flowService.queryBorrowForProbationDeviceOccupyState(deviceIds));
    }

    @GetMapping("/queryProjectFlowStatus")
    @Operation(summary = "查询项目主要流程状态")
    public JsonObject<List<CrmProjectProgressVO>> queryProjectFlowStatus(@RequestParam String projectId) {
        return new JsonObject<>(flowService.queryProjectFlowStatus(projectId));
    }

    @GetMapping("/hasContractOfProject")
    @Operation(summary = "检查项目中是否已经发起了合同评审或业绩上报")
    JsonObject<Boolean> hasContractOfProject(@RequestParam String projectId) {
        return new JsonObject<>(flowService.hasContractOfProject(projectId));
    }

    @PostMapping("/hasContractOfProjectByRecordIds")
    @Operation(summary = "检查项目中是否已经发起了合同评审或业绩上报")
    JsonObject<Boolean> hasContractOfProjectByRecordIds(@RequestParam String projectId, @RequestBody Set<String> productRecordIdSet) {
        return new JsonObject<>(flowService.hasContractOfProject(projectId,productRecordIdSet));
    }

    @PostMapping("/queryRecordHasContract")
    JsonObject<Set<String>> queryRecordHasContract(@RequestParam String projectId, @RequestBody Set<String> productRecordIdSet){
        return new JsonObject<>(flowService.queryRecordHasContract(projectId,productRecordIdSet));
    }

    @GetMapping("/hasPriceReviewUnderwayOfProject")
    @Operation(summary = "检查项目中是否有进行中的价格评审")
    JsonObject<Boolean> hasPriceReviewUnderwayOfProject(@RequestParam String projectId){
        return new JsonObject<>(flowService.hasPriceReviewUnderwayOfProject(projectId));
    }

    @PostMapping("/listProcessExtensionInfo")
    @Operation(summary = "查询流程拓展信息")
    public JsonObject<List<ProcessExtensionInfoVO>> listProcessExtensionInfo(@RequestBody @Valid @NotNull ProcessExtensionInfoQuery query){
        List<ProcessExtensionInfo> list = processExtensionInfoService.list(new QueryWrapper<ProcessExtensionInfo>()
                .in(CollectionUtils.isNotEmpty(query.getProcessInstanceId()), "process_instance_id", query.getProcessInstanceId())
                .in(CollectionUtils.isNotEmpty(query.getProcessNumber()), "process_number", query.getProcessNumber())
                .in(CollectionUtils.isNotEmpty(query.getProcessState()), "process_state", query.getProcessState())
                .eq(StringUtils.isNotEmpty(query.getProcessDefinitionKey()), "process_definition_key", query.getProcessDefinitionKey())
        );
        return new JsonObject<>(ProcessExtensionInfoConvertor.INSTANCE.toVOList(list));
    }

    @Operation(summary = "查询项目是否发起过流程")
    @PostMapping("/checkProjectHasProcess")
    public JsonObject<Boolean> checkProjectHasProcess(@RequestParam String projectId,@RequestParam(required = false) List<ProcessDefinitionKeyEnum> excludeProcessDefineKeys) {

        Set<String> defineKeys = ListUtils.emptyIfNull(excludeProcessDefineKeys).stream().map(ProcessDefinitionKeyEnum::getValue).collect(Collectors.toSet());
        boolean exists = processExtensionInfoMapper.exists(new QueryWrapper<ProcessExtensionInfo>()
                .eq("project_id", projectId)
                .notIn(CollectionUtils.isNotEmpty(defineKeys),"process_definition_key",defineKeys)

        );
        return new JsonObject<>(exists);
    }


    @PostMapping("/querySuperiorsUntilCenterLeader")
    @Operation(summary = "逐级审批，当审批人为营销中心领导时，该审批人审批后结束")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> querySuperiorsUntilCenterLeader() {
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        List<FlowPerson> flowPeople = commonService.queryFlowSuperiors(currentPersonId, null, employeeReportRelVO -> {
            return commonService.isCenterLeader(employeeReportRelVO.getUuid());
        });
        return new JsonObject<>(flowPeople);
    }
}
