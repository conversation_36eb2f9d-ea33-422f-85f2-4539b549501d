package com.topsec.crm.flow.core.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.receivableamount.CrmPaymentVO;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.file.api.RemoteFsmDocService;
import com.topsec.crm.flow.api.dto.collectionhandle.CollectionLettersHandleDTO;
import com.topsec.crm.flow.api.dto.contractreceivable.*;
import com.topsec.crm.flow.api.dto.contractreceivable.check.ReceivableConditionDTO;
import com.topsec.crm.flow.api.dto.legalAffairsMain.VO.LegalAffairsMainInfoVO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteVO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportReceivableQuery;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.error.ErrorDetail;
import com.topsec.crm.flow.core.error.ErrorHandler;
import com.topsec.crm.flow.core.error.impl.SimpleErrorHandler;
import com.topsec.crm.flow.core.mapper.ContractReceivableFollowMapper;
import com.topsec.crm.flow.core.mapstruct.ReceivableConvertor;
import com.topsec.crm.flow.core.process.impl.ContractReceivableChildProcessService;
import com.topsec.crm.flow.core.process.impl.ContractReceivableMainProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.constant.ReceivableConstants;
import com.topsec.crm.framework.common.enums.ReceivableEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NamePair;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageIterator;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.sql.SqlUtil;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.tos.common.vo.TosDepartmentVO;
import com.topsec.vo.node.ApproveNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应收催款相关
 *
 * <AUTHOR>
 * @date 2024/12/16 15:33
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContractReceivableServiceImpl implements ContractReceivableService {

    private final ContractDebtService contractDebtService;
    private final ContractReceivableMainService receivableMainService;
    private final ContractReceivableDetailService detailService;
    private final RemoteContractExecuteService remoteContractExecuteService;
    private final RemoteContractReviewService remoteContractReviewService;
    private final ContractReceivableMainService contractReceivableMainService;
    private final ContractReceivableChildService childService;
    private final ContractReceivableFollowService followService;
    private final ContractReceivableMainProcessService receivableMainProcessService;
    private final ContractReceivableChildProcessService receivableChildProcessService;
    private final RemoteFsmDocService remoteFsmDocService;
    private final TfsNodeClient tfsNodeClient;
    private final TosEmployeeClient tosEmployeeClient;
    private final TosDepartmentClient tosDepartmentClient;
    private final LegalAffairsMainService legalAffairsMainService;
    private final ContractCollectionLetterMainService contractCollectionLetterMainService;
    private final ContractCollectionLettersHandleService contractCollectionLettersHandleService;
    private final PerformanceReportDebtService performanceReportDebtService;
    private final PerformanceReportReceivableAmountService performanceReportReceivableAmountService;
    private final PerformanceExecuteService performanceExecuteService;

    @Override
    @Transactional
    public void generateReceivable() {
        // 取合同执行中，欠款大于0的 写入ContractDebt 中
        contractDebtService.remove(new LambdaQueryWrapper<ContractDebt>().apply("1=1"));
        CrmContractAfterQuery query = new CrmContractAfterQuery();
        CrmContractAfterQuery.ReceivableAmountQuery receivableAmountQuery = new CrmContractAfterQuery.ReceivableAmountQuery();
        receivableAmountQuery.setOverdueStatus(1);
        query.setReceivableAmountQuery(receivableAmountQuery);
        List<CrmContractExecuteVO> crmContractExecuteVOS = ListUtils.emptyIfNull(PageIterator.iteratePageToList(PageIterator.DEFAULT_PAGE_SIZE, (pageNo, pageSize) -> {
            query.setPageSize(pageSize);
            query.setPageNum(pageNo);
            return Optional.ofNullable(remoteContractExecuteService.pageByCondition(query)).map(JsonObject::getObjEntity).orElse(null);
        }, true));
        if (CollectionUtils.isEmpty(crmContractExecuteVOS)) {
            return;
        }


        // 1. 写入合同欠款表 contract_debt
        crmContractExecuteVOS.forEach(crmContractExecuteVO -> {
            ContractDebt contractDebt = HyperBeanUtils.copyProperties(crmContractExecuteVO, ContractDebt::new);
            contractDebt.setId(null);
            if (crmContractExecuteVO.getContractOwnerDeptId() != null && StringUtils.isEmpty(crmContractExecuteVO.getContractOwnerDeptName())) {
                TosDepartmentVO departmentVO = tosDepartmentClient.findById(crmContractExecuteVO.getContractOwnerDeptId()).getObjEntity();
                if (departmentVO != null) {
                    contractDebt.setContractOwnerDeptName(departmentVO.getName());
                }
            }
            contractDebtService.save(contractDebt);
        });
        // 2. 发起应收催款流程
        List<ContractDebt> list = contractDebtService.list(new LambdaQueryWrapper<ContractDebt>()
                .eq(ContractDebt::getDelFlag, false));
        // 系统管理员
        launchReceivable(list, crmContractExecuteVOS, "b051b2e8a22aaa814cd0ad384bffa318", 1);
    }

    @Override
    @Transactional
    public void generatePerformanceReportReceivable() {
        // 取业绩执行中，欠款大于0的 PerformanceReportDebt 中
        performanceReportDebtService.remove(new LambdaQueryWrapper<PerformanceReportDebt>().apply("1=1"));
        PerformanceReportReceivableQuery query = new PerformanceReportReceivableQuery();
        List<PerformanceExecuteVO> performanceExecuteVOS = ListUtils.emptyIfNull(PageIterator.iteratePageToList(PageIterator.DEFAULT_PAGE_SIZE, (pageNo, pageSize) -> {
            query.setPageSize(pageSize);
            query.setPageNum(pageNo);
            return Optional.ofNullable(performanceReportReceivableAmountService.pageByCondition(query)).orElse(null);
        }, true));
        if (CollectionUtils.isEmpty(performanceExecuteVOS)) {
            return;
        }
        //业绩执行转合同执行
        List<CrmContractExecuteVO> crmContractExecuteVOS = ReceivableConvertor.INSTANCE.toCrmContractExecuteVOList(performanceExecuteVOS);

        // 1. 写入业绩上报欠款表 performance_report_debt
        performanceExecuteVOS.forEach(crmContractExecuteVO -> {
            PerformanceReportDebt performanceReportDebt = HyperBeanUtils.copyProperties(crmContractExecuteVO, PerformanceReportDebt::new);
            performanceReportDebt.setId(null);
            performanceReportDebtService.save(performanceReportDebt);
        });

        List<PerformanceReportDebt> list = performanceReportDebtService.list(new LambdaQueryWrapper<PerformanceReportDebt>()
                .eq(PerformanceReportDebt::getDelFlag, false));
        // 2. 转成合同欠款数据
        List<ContractDebt> contractDebtList = ReceivableConvertor.INSTANCE.toContractDebtList(list);
        // 3. 发起应收催款流程
        // 系统管理员
        launchReceivable(contractDebtList, crmContractExecuteVOS, "b051b2e8a22aaa814cd0ad384bffa318", 2);
    }

    private void launchReceivable(List<ContractDebt> list, List<CrmContractExecuteVO> crmContractExecuteVOS, String creatorPersonId, Integer source) {
        // source 1 合同 2 业绩上报
        assert source != null;
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, CrmContractExecuteVO> crmContractExecuteVOMap = crmContractExecuteVOS.stream()
                .collect(Collectors.toMap(CrmContractExecuteVO::getContractNumber, item -> item, (i1, i2) -> i2));
        Map<String, String> contractMainIdByDeptIdMap = new HashMap<>();
        list.stream().collect(Collectors.groupingBy(item -> Pair.of(item.getContractOwnerDeptId(), item.getContractOwnerDeptName()))).keySet().forEach(contractDebt -> {
            ContractReceivableFlowLaunchDTO dto = new ContractReceivableFlowLaunchDTO();
            dto.setContractOwnerDeptId(contractDebt.getLeft());
            dto.setContractOwnerDeptName(contractDebt.getRight());
            dto.setReceivableDate(LocalDate.now());
//            if (StringUtils.isBlank(contractDebt.getRight())) {
//                dto.setMatter(Optional.ofNullable(tosEmployeeClient.findById(contractDebt.getLeft()).getObjEntity()).map(EmployeeVO::getName).orElse("") + "应收催款");
//            }  else {
            dto.setMatter(contractDebt.getRight() + "应收催款");
//            }
            if (StringUtils.isEmpty(creatorPersonId)) {
                // 系统管理员
                dto.setCreatorPersonId("b051b2e8a22aaa814cd0ad384bffa318");
            } else {
                dto.setCreatorPersonId(creatorPersonId);
            }
            dto.setSource(source);
            receivableMainProcessService.launch(dto);
            contractMainIdByDeptIdMap.put(contractDebt.getLeft(), dto.getId());
        });

        int batchSize = 500;
        // 3.根据 contract_debt 计算应收催款明细
        list.stream().collect(Collectors.groupingBy(item -> list.indexOf(item) / batchSize)).values().forEach(batchList -> {
            // 优化成批量的
            // 计算律师函的数据
            Map<String, ContractReceivableFollowDTO> receivableFollowDTOMap;
            if (source == 1) {
                // 合同计算催款意见
                List<ContractReceivableFollowDTO> followDTOS = remoteContractExecuteService.calcReceivableDetail(HyperBeanUtils.copyListProperties(batchList, ContractDebtDTO::new)).getObjEntity();
                receivableFollowDTOMap= followDTOS.stream().collect(Collectors.toMap(ContractReceivableFollowDTO::getContractNumber, item -> item, (i1, i2) -> i2));
            } else {
                receivableFollowDTOMap = new HashMap<>();
            }
            Set<String> contractIds = new HashSet<>();
            Set<String> contractNumbers = new HashSet<>();
            batchList.forEach(contractDebt -> {
                contractIds.add(contractDebt.getContractId());
                contractNumbers.add(contractDebt.getContractNumber());
            });
            Map<String, CrmPaymentVO> paymentVOMap;
            if (source == 1) {
                List<CrmPaymentVO> paymentVOS = remoteContractReviewService.getFirstPaymentByContractIds(contractIds).getObjEntity();
                paymentVOMap = paymentVOS.stream().collect(Collectors.toMap(CrmPaymentVO::getContractReviewMainId, item -> item, (i1, i2) -> i2));
            } else {
                paymentVOMap = new HashMap<>();
            }
            // 第一个付款条款的数据
            // 取这批数据 是否发起过律师函、催款函
            Map<String, List<LegalAffairsMainInfoVO>> legalAffairsMainInfoVOMap = selectLegalAffairs(contractNumbers);
            // 发过催款函的
            Map<String, CollectionLettersHandleDTO> letterHandleDTOMap = selectCollectionLetters(contractNumbers);
            // 历史的应收催款流程
            List<ContractReceivableDetailDTO> receivableLatelyByContractNumberBatch = getReceivableLatelyByContractNumberBatch(contractNumbers);
            Map<String, ContractReceivableDetailDTO> detailDTOMap = receivableLatelyByContractNumberBatch.stream().collect(Collectors.toMap(ContractReceivableDetailDTO::getContractNumber, item -> item, (i1, i2) -> i2));

            batchList.forEach(contractDebt -> {
                // 根据合同号 算出相关数据 写入
                ContractReceivableDetail detail = new ContractReceivableDetail();
                HyperBeanUtils.copyProperties(contractDebt, detail);
                // 付款条件
                CrmPaymentVO crmPaymentVO = paymentVOMap.get(contractDebt.getContractId());
                if (crmPaymentVO != null) {
                    detail.setPaymentTerms(crmPaymentVO.getPaymentTerms());
                }

                // 签约单位
                CrmContractExecuteVO crmContractExecuteVO = crmContractExecuteVOMap.get(detail.getContractNumber());
                detail.setId(null);
                detail.setContractCompanyName(Optional.ofNullable(crmContractExecuteVO).map(CrmContractExecuteVO::getContractCompanyName).orElse(null));
                detail.setSaleId(Optional.ofNullable(crmContractExecuteVO).map(CrmContractExecuteVO::getSaleId).orElse(null));
                detail.setSaleName(Optional.ofNullable(crmContractExecuteVO).map(CrmContractExecuteVO::getSaleName).orElse(null));
                detail.setContractTime(Optional.ofNullable(crmContractExecuteVO).map(CrmContractExecuteVO::getContractTime).orElse(null));
                detail.setInvoicedAmount(Optional.ofNullable(crmContractExecuteVO).map(CrmContractExecuteVO::getInvoicedAmount).orElse(null));
                detail.setRevenueRecognitionAmount(Optional.ofNullable(crmContractExecuteVO).map(CrmContractExecuteVO::getRevenueRecognitionAmount).orElse(null));
                ContractReceivableMain contractReceivableMain = receivableMainService.getByContractOwnerDeptId(contractDebt.getContractOwnerDeptId());
                // 明细主表id
                String id = contractMainIdByDeptIdMap.get(contractDebt.getContractOwnerDeptId());
                detail.setContractReceivableMainId(id);
                detailService.save(detail);

                // 之前的催缴的建议
                ContractReceivableDetailDTO detailDTO = detailDTOMap.get(contractDebt.getContractNumber());
                LocalDate promisePaymentDate = null;
                List<ReceivableFileInfoVO> docs = null;

                // 判断一下 承诺时间和今天比较，如果今天小于承诺时间，把这个承诺时间和附件带过来
                if  (detailDTO != null && detailDTO.getOverdueAmount().compareTo(BigDecimal.ZERO) > 0   && detailDTO.getPromisePaymentDate() != null && LocalDate.now().isBefore(detailDTO.getPromisePaymentDate())) {
                    promisePaymentDate = detailDTO.getPromisePaymentDate();
                    docs = detailDTO.getDocs();
                }

                // 兜底的
                boolean saveFlag = false;
                // 计算要发的
                ContractReceivableFollowDTO receivableFollowDTO = receivableFollowDTOMap.get(contractDebt.getContractNumber());
                if (receivableFollowDTO != null) {
                    // 不为空 则计算出处理建议了
                    receivableFollowDTO.setId(null);
                    receivableFollowDTO.setReceivableDetailId(detail.getId());
                    receivableFollowDTO.setReceivableSource(1);
                    initPaymentDateAndDocs(receivableFollowDTO, docs, promisePaymentDate);
                    followService.save(HyperBeanUtils.copyProperties(receivableFollowDTO, ContractReceivableFollow::new));
                    saveFlag = true;
                }
                if (source == 2) {
                    // 业绩上报直接算
                    ContractReceivableFollowDTO receivableFollowDTO1 = new ContractReceivableFollowDTO();
                    receivableFollowDTO1.setId(null);
                    receivableFollowDTO1.setContractNumber(detail.getContractNumber());
                    receivableFollowDTO1.setHandleSuggest(calcHandleSuggest(detail.getOverdueDate()));
                    receivableFollowDTO1.setReceivableDetailId(detail.getId());
                    receivableFollowDTO1.setReceivableSource(1);
                    initPaymentDateAndDocs(receivableFollowDTO1, docs, promisePaymentDate);
                    followService.save(HyperBeanUtils.copyProperties(receivableFollowDTO1, ContractReceivableFollow::new));
                    saveFlag = true;
                }
                // 计算已发的
                List<LegalAffairsMainInfoVO> legalAffairsMainInfoVO = legalAffairsMainInfoVOMap.get(contractDebt.getContractNumber());
                // 律师函
                Optional<LegalAffairsMainInfoVO> first = ListUtils.emptyIfNull(legalAffairsMainInfoVO).stream().filter(item -> item.getCollectionSuggestion() == 0).findFirst();
                // 诉讼
                Optional<LegalAffairsMainInfoVO> second = ListUtils.emptyIfNull(legalAffairsMainInfoVO).stream().filter(item -> item.getCollectionSuggestion() == 1).findFirst();
                CollectionLettersHandleDTO handleDTO = letterHandleDTOMap.get(contractDebt.getContractNumber());

                boolean isSave = false;

                if (second.isPresent() && second.get().getSendLetterTime() != null && second.get().getCollectionSuggestion() == 1) {
                    // 已发诉讼 SEND_LAWSUIT
                    saveReceivableFollow(detail, ReceivableEnum.ReceivableHandleSuggest.SEND_LAWSUIT, docs, promisePaymentDate);
                    saveFlag = true;
                    isSave = true;
                }
                if (first.isPresent() && first.get().getSendLetterTime() != null && first.get().getCollectionSuggestion() == 0) {
                    // 已发律师函 SEND_LAWYER_LETTER
                    if (!isSave) {
                        saveReceivableFollow(detail, ReceivableEnum.ReceivableHandleSuggest.SEND_LAWYER_LETTER, docs, promisePaymentDate);
                        saveFlag = true;
                        isSave = true;
                    }
                }
                if (handleDTO != null && handleDTO.getLetterDate() != null) {
                    // 已发催款函 最低优先级
                    if (!isSave) {
                        saveReceivableFollow(detail, ReceivableEnum.ReceivableHandleSuggest.SEND_COLLECTION_LETTER, docs, promisePaymentDate);
                        saveFlag = true;
                        isSave = true;
                    }
                }

                if (!saveFlag) {
                    // 没有处理建议的 如果有附件 新增一个
                    if (docs != null) {
                        saveReceivableFollow(detail, null, docs, promisePaymentDate);
                    }
                }
            });
        });
    }

    private Integer calcHandleSuggest(Integer overdueDate) {
        /**
         * a.超期天数<=60天，自动生成【形成原因】为“销售部门继续催收”的跟进数据；
         * b.60天<超期天数<=90天， 自动生成【形成原因】为“发催款函”的跟进数据；
         * c.90天<超期天数<=120天， 自动生成【形成原因】为“发律师函””的跟进数据；
         * d.超期天数>120天，自动生成【形成原因】为“诉讼”的跟进数据。
         */
        if (overdueDate <= 60) {
            return ReceivableEnum.ReceivableHandleSuggest.SALE_CONTINUE_COLLECTION.getCode();
        } else if (overdueDate <= 90) {
            return ReceivableEnum.ReceivableHandleSuggest.SEND_COLLECTION_LETTER_AGAIN.getCode();
        } else if (overdueDate <= 120) {
            return ReceivableEnum.ReceivableHandleSuggest.SEND_LAWYER_LETTER_AGAIN.getCode();
        } else {
            return ReceivableEnum.ReceivableHandleSuggest.LAWSUIT.getCode();
        }
    }

    private void initPaymentDateAndDocs(ContractReceivableFollowDTO dto, List<ReceivableFileInfoVO> docs, LocalDate promisePaymentDate) {
        if (CollectionUtils.isNotEmpty(docs)) {
            docs.forEach(item -> {
                item.setCanDelete(false);
            });
        }
        dto.setDocs(docs);
        dto.setPromisePaymentDate(promisePaymentDate);
    }

    private void saveReceivableFollow(ContractReceivableDetail detail, ReceivableEnum.ReceivableHandleSuggest handleSuggest, List<ReceivableFileInfoVO> docs, LocalDate promisePaymentDate) {
        if (detail == null) {
            throw new IllegalArgumentException("Detail cannot be null");
        }

        ContractReceivableFollowDTO receivableFollow = new ContractReceivableFollowDTO();
        receivableFollow.setReceivableDetailId(detail.getId());
        receivableFollow.setReceivableSource(1);
        if (handleSuggest != null) {
            receivableFollow.setHandleSuggest(handleSuggest.getCode());
        }
        initPaymentDateAndDocs(receivableFollow, docs, promisePaymentDate);

        followService.save(HyperBeanUtils.copyProperties(receivableFollow, ContractReceivableFollow::new));
    }

    private Map<String, List<LegalAffairsMainInfoVO>> selectLegalAffairs(Set<String> contractNumbers){
        List<LegalAffairsMainInfoVO> legalAffairsMainInfoVOS = legalAffairsMainService.getByContractNumberBatch(contractNumbers, true);
        return legalAffairsMainInfoVOS.stream()
                .collect(Collectors.groupingBy(LegalAffairsMainInfoVO::getContractNumber));
    }

    public Map<String, CollectionLettersHandleDTO> selectCollectionLetters(Set<String> contractNumbers){
        List<ContractCollectionLetterMain> contractCollectionLetterMains = contractCollectionLetterMainService.getByContractNumberBatch(contractNumbers);
        Map<String, ContractCollectionLetterMain> letterMainMap = contractCollectionLetterMains.stream()
                .collect(Collectors.toMap(ContractCollectionLetterMain::getProcessInstanceId, item -> item, (i1, i2) -> i2));

        Set<String> processInstanceIds = contractCollectionLetterMains.stream().map(ContractCollectionLetterMain::getProcessInstanceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<CollectionLettersHandleDTO> handleDTOS = contractCollectionLettersHandleService.getByProcessInstanceIdBatch(processInstanceIds);
        // key contractNumber value handle
        Map<String, CollectionLettersHandleDTO> handleDTOMap = new HashMap<>();
        handleDTOS.forEach(handleDTO -> {
            ContractCollectionLetterMain contractCollectionLetterMain = letterMainMap.get(handleDTO.getProcessInstanceId());
            handleDTOMap.put(contractCollectionLetterMain.getContractNumber(), handleDTO);
        });
        return handleDTOMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateReceivableChild(String processInstanceId, List<ContractReceivableDetail> receivableDetails) {
        ContractReceivableMainDTO receivableMainDTO = contractReceivableMainService.getByProcessInstanceId(processInstanceId);
        // 把主应收的明细 再根据合同负责人分一下
        if (CollectionUtils.isEmpty(receivableDetails)) {
            return;
        }
        Map<Pair<String, String>, List<ContractReceivableDetail>> receivableDetailsMap = receivableDetails.stream()
                .collect(Collectors.groupingBy(item -> Pair.of(item.getContractOwnerId(), item.getContractOwnerName())));
        receivableDetailsMap.forEach((item,details) -> {
            ContractReceivableChildLaunchDTO dto = new ContractReceivableChildLaunchDTO();
            dto.setContractOwnerId(item.getLeft());
            dto.setContractOwnerName(item.getRight());
            dto.setReceivableDate(LocalDate.now());
            dto.setMainProcessInstanceId(processInstanceId);
            dto.setMatter(item.getRight() + "子应收催款");
            // 系统管理员
            dto.setCreatorPersonId("b051b2e8a22aaa814cd0ad384bffa318");
            dto.setParentProcessInstanceId(processInstanceId);
            receivableChildProcessService.launch(dto);

            // 更新明细表的子应收催款的id
            List<String> detailsId = details.stream().map(ContractReceivableDetail::getId).toList();
            detailService.updateDetails(detailsId, dto.getReceivableChildId());
        });
    }

    @Override
    public PageUtils<ContractReceivableDetailDTO> pageReceivableMainDetail(String receivableMainId) {
        List<ContractReceivableDetail> list = detailService.getByReceivableMainId(receivableMainId);
        PageUtils<ContractReceivableDetailDTO> pageUtils = new PageUtils<>();
        if (CollectionUtils.isEmpty(list)) {
            pageUtils.setTotalCount(0);
            return pageUtils;
        }

        List<String> detailIds = new ArrayList<>();
        Set<String> contractNumbers = new HashSet<>();
        list.forEach(item -> {
            contractNumbers.add(item.getContractNumber());
            detailIds.add(item.getId());
        });
        pageUtils.setTotalCount((int) new PageInfo<>(list).getTotal());
        pageUtils.setList(buildReceivableFollowDTO(list, detailIds, contractNumbers, null, false));
        return pageUtils;
    }

    @Override
    public PageUtils<ContractReceivableDetailDTO> pageReceivableChildDetail(String receivableChildId) {
        List<ContractReceivableDetail> list = detailService.getByReceivableChildId(receivableChildId);
        PageUtils<ContractReceivableDetailDTO> pageUtils = new PageUtils<>();
        if (CollectionUtils.isEmpty(list)) {
            pageUtils.setTotalCount(0);
            return pageUtils;
        }
        pageUtils.setTotalCount((int) new PageInfo<>(list).getTotal());
        List<String> detailIds = new ArrayList<>();
        Set<String> contractNumbers = new HashSet<>();
        list.forEach(item -> {
            contractNumbers.add(item.getContractNumber());
            detailIds.add(item.getId());
        });
        pageUtils.setList(buildReceivableFollowDTO(list, detailIds, contractNumbers, null, true));
        return pageUtils;
    }


    @Override
    public Boolean saveOrUpdateReceivableFollow(ContractReceivableDetailDTO receivableDetailDTO) {
        // 根据id获取详情
        ContractReceivableDetail detail = detailService.getById(receivableDetailDTO.getId());
        // 01 02 步
        if (ReceivableConstants.RECEIVABLE_MAIN_01.equals(receivableDetailDTO.getCurrentActId()) || ReceivableConstants.RECEIVABLE_MAIN_01_PR.equals(receivableDetailDTO.getCurrentActId()) || ReceivableConstants.RECEIVABLE_MAIN_02.equals(receivableDetailDTO.getCurrentActId())) {
            BigDecimal overdueAmount = detail.getOverdueAmount() == null ? BigDecimal.ZERO : detail.getOverdueAmount();
            if (overdueAmount.compareTo(BigDecimal.ZERO) > 0) {
                CrmAssert.notNull(receivableDetailDTO.getHandleSuggest(), "处理建议不能为空");
            }
            if (detail.getOverdueDate() > 60) {
                CrmAssert.notNull(receivableDetailDTO.getDemandSuggest(), "超期天数超过60天，催款情况不能为空");
            }
        } else {
            // 03 04步 则其他字段也不能为空
            checkSaleCondition(receivableDetailDTO, detail);
        }

        return saveOrUpdateFollow(receivableDetailDTO, detail, 1);
    }

    /**
     * 销售催缴校验
     * @param receivableDetailDTO 页面的dto
     * @param detail 应收催款详情
     */
    private void checkSaleCondition(ContractReceivableDetailDTO receivableDetailDTO, ContractReceivableDetail detail) {
        CrmAssert.notNull(receivableDetailDTO.getDebtConfirmation(), "欠款确认函不能为空");
        CrmAssert.notNull(receivableDetailDTO.getPromisePaymentDate(), "承诺付款时间不能为空");
        // 如果超期天数大于0，则必填形成的原因
        if (detail.getOverdueDate() > 0) {
            CrmAssert.notNull(receivableDetailDTO.getCauseBy(), "形成的原因不能为空");
        }
        if (receivableDetailDTO.getDebtConfirmation() == 1) {
            CrmAssert.notNull(receivableDetailDTO.getDocs(), "欠款确认函为是，则必须上传确认函文件");
        }
    }

    private Boolean saveOrUpdateFollow(ContractReceivableDetailDTO receivableDetailDTO, ContractReceivableDetail detail, Integer source){
        // 获取这条明细的催缴跟进
        ContractReceivableFollow follow = followService.getByReceivableDetailId(receivableDetailDTO.getId(), source);
        ContractReceivableFollow contractReceivableFollow = HyperBeanUtils.copyProperties(receivableDetailDTO, ContractReceivableFollow::new);
        if (follow != null) {
            // 修改
            contractReceivableFollow.setId(follow.getId());
        } else {
            contractReceivableFollow.setId(null);
        }
        contractReceivableFollow.setReceivableDetailId(detail.getId());
        contractReceivableFollow.setReceivableSource(source);
        return followService.saveOrUpdate(contractReceivableFollow);
    }

    @Override
    public Boolean saveOrUpdateReceivableChildFollow(ContractReceivableDetailDTO receivableDetailDTO) {
        ContractReceivableDetail detail = detailService.getById(receivableDetailDTO.getId());
        checkSaleCondition(receivableDetailDTO, detail);
        return saveOrUpdateFollow(receivableDetailDTO, detail, 2);
    }

    @Override
    public List<ErrorDetail> checkReceivableCondition(ReceivableConditionDTO receivableConditionDTO) {
        ErrorHandler errorHandler = new SimpleErrorHandler();
        String currentActId = receivableConditionDTO.getCurrentActId();
        String nextActId = receivableConditionDTO.getNextActId();
        String processInstanceId = receivableConditionDTO.getProcessInstanceId();


        // 01步的判断
        if ((ReceivableConstants.RECEIVABLE_MAIN_01.equals(currentActId) || ReceivableConstants.RECEIVABLE_MAIN_01_PR.equals(currentActId)) && !checkAllMainFollow(processInstanceId)) {
            errorHandler.addError(new ErrorDetail("500", "催交跟进状态全部为已跟进，才可办理完毕"));
        }
        if (ReceivableConstants.RECEIVABLE_MAIN_03.equals(currentActId) && !childService.checkChildStatus(processInstanceId)) {
            errorHandler.addError(new ErrorDetail("500", "子应收催款必须全部办结，才可办理完毕"));
        }
        return errorHandler.getErrors();
    }

    @Override
    public List<ErrorDetail> checkReceivableChildCondition(ReceivableConditionDTO receivableConditionDTO) {
        ErrorHandler errorHandler = new SimpleErrorHandler();
        String currentActId = receivableConditionDTO.getCurrentActId();
        String nextActId = receivableConditionDTO.getNextActId();
        String processInstanceId = receivableConditionDTO.getProcessInstanceId();


        // 01步的判断
        if (ReceivableConstants.RECEIVABLE_CHILD_01.equals(currentActId) && !checkAllChildFollow(processInstanceId)) {
            errorHandler.addError(new ErrorDetail("500", "催交跟进状态全部为已跟进，才可办理完毕"));
        }
        return errorHandler.getErrors();
    }

    @Override
    public PageUtils<ContractReceivableChildDTO> pageChildReceivable(String mainProcessInstanceId) {
        List<ContractReceivableChild> receivableChildren = childService.getByMainProcessInstanceId(mainProcessInstanceId);
        PageUtils<ContractReceivableChildDTO> pageUtils = new PageUtils<>();
        if (CollectionUtils.isEmpty(receivableChildren)) {
            pageUtils.setTotalCount(0);
            return pageUtils;
        }
        Map<String, Set<ApproveNode>> nodesById = tfsNodeClient.queryNodeByProcessInstanceIdList(ListUtils.emptyIfNull(receivableChildren).stream()
                .map(ContractReceivableChild::getProcessInstanceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList())).getObjEntity();
        List<ContractReceivableChildDTO> list = receivableChildren.stream().map(item -> {
            ContractReceivableChildDTO childDTO = HyperBeanUtils.copyProperties(item, ContractReceivableChildDTO::new);
            if (nodesById != null) {
                childDTO.setProcessState(nodesById.get(item.getProcessInstanceId()));
            }
            return childDTO;
        }).toList();
        pageUtils.setTotalCount((int) new PageInfo<>(receivableChildren).getTotal());
        pageUtils.setList(list);
        return pageUtils;
    }

    @Override
    public ContractReceivableMainDTO receivableChildBasicInfo(String childProcessInstanceId) {
        ContractReceivableChild receivableChild = childService.getByProcessInstanceId(childProcessInstanceId);
        if (receivableChild == null) {
            return null;
        }
        ContractReceivableMainDTO receivableMainDTO = receivableMainService.getByProcessInstanceId(receivableChild.getMainProcessInstanceId());
        initDeptLeaderName(receivableMainDTO, tosDepartmentClient);
        return receivableMainDTO;
    }

    public static void initDeptLeaderName(ContractReceivableMainDTO receivableMainDTO, TosDepartmentClient tosDepartmentClient) {
        if (receivableMainDTO != null) {
            TosDepartmentVO departmentVO = tosDepartmentClient.findById(receivableMainDTO.getContractOwnerDeptId()).getObjEntity();
            receivableMainDTO.setDeptLeaderName(Optional.ofNullable(departmentVO).map(TosDepartmentVO::getLeaders).map(item -> {
                if (CollectionUtils.isEmpty(item)) {
                    return null;
                } else {
                    return item.get(0);
                }
            }).map(employeeVO -> {
                NamePair namePair = NameUtils.getNamePair(employeeVO.getUuid());
                return namePair.getName() == null ? null : namePair.getName();
            }).orElse(null));
        }
    }

    @Override
    public PageUtils<ContractReceivableDetailDTO> pageReceivableDetailByContractNumber(String contractNumber) {
        List<ContractReceivableDetail> list = detailService.getByContractNumber(contractNumber);

        PageUtils<ContractReceivableDetailDTO> pageUtils = new PageUtils<>();
        if (CollectionUtils.isEmpty(list)) {
            pageUtils.setTotalCount(0);
            return pageUtils;
        }
        pageUtils.setTotalCount((int) new PageInfo<>(list).getTotal());

        List<String> detailIds = new ArrayList<>();
        List<String> receivableMainIds = new ArrayList<>();
        list.forEach(item ->{
            detailIds.add(item.getId());
            receivableMainIds.add(item.getContractReceivableMainId());
        });
        pageUtils.setList(buildReceivableFollowDTO(list, detailIds, null, receivableMainIds, false));
        pageUtils.getList().sort(Comparator.comparing(ContractReceivableDetailDTO::getReceivableDate).reversed());
        return pageUtils;
    }

    @Override
    public PageUtils<ContractReceivableDetailDTO> pageReceivableDetailByCondition(ContractReceivableQuery query) {
        ContractReceivableFollowMapper followMapper = SpringUtil.getBean(ContractReceivableFollowMapper.class);
        String orderBy = SqlUtil.escapeOrderBySql(query.getOrderBy());
        if (query.getPageNum() != null && query.getPageSize() != null) {
            PageHelper.startPage(query.getPageNum(), query.getPageSize(), orderBy);
        }
        List<ContractReceivableDetailDTO> follows = followMapper.getFollowByCondition(query);
        PageUtils<ContractReceivableDetailDTO> pageUtils = new PageUtils<>();
        if (CollectionUtils.isEmpty(follows)) {
            pageUtils.setTotalCount(0);
            return pageUtils;
        }
        pageUtils.setTotalCount((int) new PageInfo<>(follows).getTotal());
        // 组装数据
        Set<String> contractNumbers = new HashSet<>();
        Set<String> saleIds = new HashSet<>();
        follows.forEach(follow -> {
            contractNumbers.add(follow.getContractNumber());
            saleIds.add(follow.getContractOwnerId());
        });
        List<CrmContractExecuteVO> contractExecutes = remoteContractExecuteService.getByContractNumberBatch(new HashSet<>(contractNumbers)).getObjEntity();
        Map<String, CrmContractExecuteVO> crmContractExecuteVOMap = ListUtils.emptyIfNull(contractExecutes).stream().collect(Collectors.toMap(CrmContractExecuteVO::getContractNumber, v -> v, (v1, v2) -> v1));
        List<EmployeeVO> personVOList = tosEmployeeClient.findByIds(new ArrayList<>(saleIds)).getObjEntity();
        Map<String, EmployeeVO> personVOMap = personVOList.stream().collect(Collectors.toMap(EmployeeVO::getUuid, e -> e));


        follows.forEach(follow -> {
            CrmContractExecuteVO crmContractExecuteVO = crmContractExecuteVOMap.get(follow.getContractNumber());
            // 合同时间
            follow.setContractTime(Optional.ofNullable(crmContractExecuteVO).map(CrmContractExecuteVO::getContractTime).orElse(null));
            // 原销售部门
            EmployeeVO personVO = personVOMap.get(follow.getSaleId());
            follow.setSaleDeptName(Optional.ofNullable(personVO).map(EmployeeVO::getDept).map(TosDepartmentVO::getName).orElse(null));
        });

        pageUtils.setList(follows);
        return pageUtils;
    }

    @Override
    public Boolean syncReceivableMainFollowByChild(String childProcessInstanceId) {
        ContractReceivableChild receivableChild = childService.getByProcessInstanceId(childProcessInstanceId);
        // 获取这个子应收的详情
        List<ContractReceivableDetail> details = detailService.getByReceivableChildId(receivableChild.getId());
        // 获取子应收的跟进
        if (CollectionUtils.isEmpty(details)) {
            return true;
        }
        List<String> list = details.stream().map(ContractReceivableDetail::getId).toList();
        List<ContractReceivableFollow> childFollows = followService.getByReceivableDetailIdBatch(list, 2);
        if (CollectionUtils.isEmpty(childFollows)) {
            return true;
        }
        Map<String, ContractReceivableFollow> childFollowMap = childFollows.stream().collect(Collectors.toMap(ContractReceivableFollow::getReceivableDetailId, item -> item, (i1, i2) -> i1));

        // 明细对应的主应收催款 跟进
        List<ContractReceivableFollow> mainFollow = followService.getByReceivableDetailIdBatch(list, 1);
        mainFollow.forEach(item -> {
            ContractReceivableFollow childFollow = childFollowMap.get(item.getReceivableDetailId());
            if (childFollow != null) {
                // 这里只考虑update的原因是 主应收的意见，01步肯定已经填了
                ContractReceivableFollow contractReceivableFollow = HyperBeanUtils.copyProperties(childFollow, ContractReceivableFollow::new);
                // 01步的意见不会被覆盖，因为为空的 不会构建set的sql
                contractReceivableFollow.setId(item.getId());
                contractReceivableFollow.setReceivableSource(item.getReceivableSource());
                followService.updateById(contractReceivableFollow);
            }
        });
        return true;
    }

    @Override
    public List<ContractReceivableDetailDTO> getReceivableLatelyByContractNumberBatch(Set<String> contractNumbers) {
        List<ContractReceivableDetail> list = detailService.getByContractNumberBatch(contractNumbers);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<String> detailIds = new ArrayList<>();
        List<String> receivableMainIds = new ArrayList<>();
        list.forEach(item ->{
            detailIds.add(item.getId());
            receivableMainIds.add(item.getContractReceivableMainId());
        });
        List<ContractReceivableDetailDTO> detailDTOS = buildReceivableFollowDTO(list, detailIds, null, receivableMainIds, false);
        // 根据催缴时间 取最新的一次
        return detailDTOS.stream()
                .collect(Collectors.toMap(ContractReceivableDetailDTO::getContractNumber, item -> item, (i1, i2) -> {
                    if (i1.getReceivableDate().isAfter(i2.getReceivableDate())) {
                        return i1;
                    } else {
                        return i2;
                    }
                })).values().stream().toList();
    }

    @Override
    public Set<String> getContractNumberByCondition(Integer handleSuggest) {
        ContractReceivableFollowMapper followMapper = SpringUtil.getBean(ContractReceivableFollowMapper.class);
        return followMapper.getContractNumberByCondition(handleSuggest);
    }

    @Override
    public Boolean launchReceivable(String contractNumber, Integer ignoreCheck) {
        JsonObject<CrmContractExecuteVO> response = remoteContractExecuteService.getByContractNumber(contractNumber);
        if (!response.isSuccess() || response.getObjEntity() == null) {
            log.error("获取合同执行接口失败", response.getMessage());
            throw new CrmException("获取合同执行接口失败");
        }
        CrmContractExecuteVO contractExecuteVO = response.getObjEntity();
        // 判断这个合同是否有、催款函、律师函和诉讼
        checkContractHasCollectionLetterOrLegalAffair(Set.of(contractNumber));
        if (ignoreCheck == null || ignoreCheck != 1) {
            // 检查
            checkLaunchReceivable(List.of(contractExecuteVO));

            checkContractHasReceivable(Set.of(contractNumber));
        }

        // 发起流程
        ContractDebt contractDebt = new ContractDebt();
        HyperBeanUtils.copyProperties(contractExecuteVO, contractDebt);
        launchReceivable(List.of(contractDebt), List.of(contractExecuteVO), UserInfoHolder.getCurrentPersonId(), 1);
        return true;
    }

    @Override
    public Boolean launchPerformanceReportReceivable(String contractNumber, Integer ignoreCheck) {
        PerformanceExecuteVO byContractNumber = performanceExecuteService.getPerformanceExecuteByContractNumber(contractNumber);
        if (byContractNumber == null) {
            throw new CrmException("未获取到业绩执行信息");
        }
        CrmContractExecuteVO contractExecuteVO = ReceivableConvertor.INSTANCE.toCrmContractExecuteVO(byContractNumber);

        // 判断这个合同是否有、催款函、律师函和诉讼
        checkContractHasCollectionLetterOrLegalAffair(Set.of(contractNumber));
        if (ignoreCheck == null || ignoreCheck != 1) {
            // 检查
            checkLaunchReceivable(List.of(contractExecuteVO));

            checkContractHasReceivable(Set.of(contractNumber));
        }

        // 发起流程
        ContractDebt contractDebt = new ContractDebt();
        HyperBeanUtils.copyProperties(contractExecuteVO, contractDebt);
        launchReceivable(List.of(contractDebt), List.of(contractExecuteVO), UserInfoHolder.getCurrentPersonId(), 2);
        return true;
    }

    private void checkLaunchReceivable(List<CrmContractExecuteVO> contractExecuteVO) {
        /**
         * 1、该合同存在应收催款；
         * 2、该合同不存在正在审批的处理流程（应收催款流程、催款函、律师函和诉讼）
         */
        if (CollectionUtils.isEmpty(contractExecuteVO)) {
            return;
        }
        String collect = contractExecuteVO.stream().filter(item -> item.getDebtAmount().compareTo(BigDecimal.ZERO) <= 0).map(CrmContractExecuteVO::getContractNumber).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(collect)) {
            // 不存在应收金额
            throw new CrmException("该合同不存在欠款，无法发起催款流程！");
        }
    }

    private void checkContractHasReceivable(Set<String> contractNumbers) {
        // 判断合同是否有正在走的应收催款流程
        List<ContractReceivableDetail> details = detailService.getByContractNumberBatch(contractNumbers);
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        // 判断主表的流程状态 取最后一次的这个合同的应收催款流程，判断是否正在进行审批 key=contractNumber value=receivableMainId
        Map<String, String> mainIdByContractNumber = details.stream().collect(Collectors.toMap(ContractReceivableDetail::getContractNumber, i -> i,
                (v1, v2) -> v1.getCreateTime().isAfter(v2.getCreateTime()) ? v1 : v2)).entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, item -> item.getValue().getContractReceivableMainId()));
        List<ContractReceivableMain> mains = contractReceivableMainService.getByIds(new HashSet<>(mainIdByContractNumber.values()));
        // key=receivableMainId value=processState
        Map<String, Integer> stateByMainId = new HashMap<>();
        // key=receivableMainId value=main
        Map<String, ContractReceivableMain> mainByMainId = new HashMap<>();
        mains.forEach(main -> {
            stateByMainId.put(main.getId(), main.getProcessState());
            mainByMainId.put(main.getId(), main);
        });
        Pair<String, Set<String>> contractNumberInProcessAndMainIds = getContractNumbersByState(mainIdByContractNumber, stateByMainId, 1);
        String contractNumberInProcess = contractNumberInProcessAndMainIds.getKey();
        Set<String> mainIdsInProcess = contractNumberInProcessAndMainIds.getValue();
        if (StringUtils.isNotBlank(contractNumberInProcess)) {
            //该合同存在正在审批中的催款处理流程（流程编号：HTKP2024030395），请勿重复发起！
            if (contractNumbers.size() == 1) {
                // 单个
                throw new CrmException("该合同存在正在审批中的催款处理流程（流程编号：" + mainByMainId.get(mainIdsInProcess.stream().findFirst().get()).getProcessNumber() + "），请勿重复发起！");
            }
            String processNumberInProcess = mainIdsInProcess.stream().map(mainId -> mainByMainId.get(mainId).getProcessNumber()).collect(Collectors.joining(","));
            throw new CrmException("以下合同存在正在审批中的催款处理流程（流程编号：" + processNumberInProcess + "），请勿重复发起！" + contractNumberInProcess);
        }
        Pair<String, Set<String>> contractNumbersByState = getContractNumbersByState(mainIdByContractNumber, stateByMainId, 2);
        String contractNumberEnd= contractNumbersByState.getKey();
        Set<String> endMainIdsInProcess = contractNumbersByState.getValue();
        if (StringUtils.isNotBlank(contractNumberEnd)) {
            // 单个 需要二次确认
            //该合同存在已办结的催款处理流程（流程编号：HTKP2024030395），请确认是否需要再次发起？
            if (contractNumbers.size() == 1) {
                throw new CrmException("该合同存在已办结的催款处理流程（流程编号：" + mainByMainId.get(endMainIdsInProcess.stream().findFirst().get()).getProcessNumber() + "），请确认是否需要再次发起？");
            }
            String processNumberInProcess = endMainIdsInProcess.stream().map(mainId -> mainByMainId.get(mainId).getProcessNumber()).collect(Collectors.joining(","));
            // 批量的直接报错
            throw new CrmException("以下合同存在已办结的催款处理流程（流程编号：" + processNumberInProcess + "），请勿重复发起!" + contractNumberEnd);
        }
    }

    private void checkContractHasCollectionLetterOrLegalAffair(Set<String> contractNumbers) {
        // 取催款函
        List<ContractCollectionLetterMain> contractCollectionLetterMains = contractCollectionLetterMainService.getByContractNumberBatch(contractNumbers);
        List<LegalAffairsMainInfoVO> legalAffairsMainInfoVOS = legalAffairsMainService.getByContractNumberBatch(contractNumbers, null);
        if (CollectionUtils.isEmpty(contractCollectionLetterMains) && CollectionUtils.isEmpty(legalAffairsMainInfoVOS)) {
            return;
        }
        // 业务上说只有一个 如果有多个取最后一个
        Map<String, ContractCollectionLetterMain> letterMainMap = contractCollectionLetterMains.stream().collect(Collectors.toMap(ContractCollectionLetterMain::getContractNumber, i -> i, (v1, v2) -> v1.getCreateTime().isAfter(v2.getCreateTime()) ? v1 : v2));
        String contractLetter = letterMainMap.entrySet().stream().filter(entry -> {
            ContractCollectionLetterMain collectionLetterMain = entry.getValue();
            Integer processState = collectionLetterMain.getProcessState();
            // 审批中的
            return processState == 1;
        }).map(Map.Entry::getKey).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(contractLetter)) {
            throw new CrmException("合同[%s]有正在走的催款函流程，请先办结流程".formatted(contractLetter));
        }

        Map<String, LegalAffairsMainInfoVO> legalAffairsMainInfoVOMap = legalAffairsMainInfoVOS.stream().collect(Collectors.toMap(LegalAffairsMainInfoVO::getContractNumber, i -> i, (v1, v2) -> v1.getCreateTime().isAfter(v2.getCreateTime()) ? v1 : v2));
        String contractLegal = legalAffairsMainInfoVOMap.entrySet().stream().filter(entry -> {
            LegalAffairsMainInfoVO legalAffairsMainInfoVO = entry.getValue();
            Integer processState = legalAffairsMainInfoVO.getProcessState();
            // 审批中的
            return processState == 1;
        }).map(Map.Entry::getKey).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(contractLegal)) {
            throw new CrmException("合同[%s]有正在走的法律事务流程，请先办结流程".formatted(contractLegal));
        }
    }

    ;

    private Pair<String, Set<String>> getContractNumbersByState(Map<String, String> mainIdByContractNumber, Map<String, Integer> stateByMainId, int targetState) {
        Set<String> mainIdSet = new HashSet<>();
        return Pair.of(mainIdByContractNumber.entrySet().stream()
                .filter(entry -> {
                    String mainId = entry.getValue();
                    Integer state = stateByMainId.getOrDefault(mainId, null);
                    if (state != null && state == targetState) {
                        mainIdSet.add(mainId);
                    }
                    return state != null && state == targetState;
                })
                .map(Map.Entry::getKey)
                .collect(Collectors.joining(",")), mainIdSet);
    }

    @Override
    public Boolean launchReceivableBatch(Set<String> contractNumber, Integer ignoreCheck) {
        if (CollectionUtils.isEmpty(contractNumber)) {
            return false;
        }
        JsonObject<List<CrmContractExecuteVO>> response = remoteContractExecuteService.getByContractNumberBatch(contractNumber);
        if (!response.isSuccess() || CollectionUtils.isEmpty(response.getObjEntity())) {
            log.error("获取合同执行接口失败", response.getMessage());
            throw new CrmException("获取合同执行接口失败");
        }
        List<CrmContractExecuteVO> contractExecutes = response.getObjEntity();
        // 判断这个合同是否有应收催款流程、催款函、律师函和诉讼
        checkContractHasCollectionLetterOrLegalAffair(contractNumber);
        if (ignoreCheck == null || ignoreCheck != 1) {
            // 检查
            checkLaunchReceivable(contractExecutes);

            checkContractHasReceivable(contractNumber);
        }

        List<ContractDebt> contractDebts = HyperBeanUtils.copyListProperties(contractExecutes, ContractDebt::new);
        launchReceivable(contractDebts, contractExecutes, UserInfoHolder.getCurrentPersonId(), 1);
        return true;
    }

    @Override
    public Boolean launchPerformanceReportReceivableBatch(Set<String> contractNumber, Integer ignoreCheck) {
        if (CollectionUtils.isEmpty(contractNumber)) {
            return false;
        }
        List<PerformanceExecuteVO> byContractNumberBatch = performanceExecuteService.getByContractNumberBatch(contractNumber);
        if (CollectionUtils.isEmpty(byContractNumberBatch)) {
            throw new CrmException("未获取到业绩执行信息");
        }
        List<CrmContractExecuteVO> contractExecutes = ReceivableConvertor.INSTANCE.toCrmContractExecuteVOList(byContractNumberBatch);
        // 判断这个合同是否有应收催款流程、催款函、律师函和诉讼
        checkContractHasCollectionLetterOrLegalAffair(contractNumber);
        if (ignoreCheck == null || ignoreCheck != 1) {
            // 检查
            checkLaunchReceivable(contractExecutes);

            checkContractHasReceivable(contractNumber);
        }

        List<ContractDebt> contractDebts = HyperBeanUtils.copyListProperties(contractExecutes, ContractDebt::new);
        launchReceivable(contractDebts, contractExecutes, UserInfoHolder.getCurrentPersonId(), 2);
        return true;
    }

    /**
     *  根据合同号查询 催缴次数
     */
    private Map<String, Integer> getCountByContractNumber(Set<String> contractNumbers){
        List<ContractReceivableDetail> receivableDetails = detailService.getByContractNumberBatch(contractNumbers);
        return receivableDetails.stream()
                .collect(Collectors.groupingBy(ContractReceivableDetail::getContractNumber, Collectors.summingInt(item -> 1)));
    }

    /**
     * 按需构建页面数据
     * @param list 数据源
     * @param detailIds 合同基础信息 必填 暴露在外面是下面几个参数都需要循环 避免多次循环
     * @param contractNumbers 合同号 选填 如果需要拼这个历史催缴次数 要穿
     * @param receivableMainIds 主表id 选填 如果需要拼这个催缴的发起时间则要传
     * @param isChild 是否是子应收的列表 选填 如果要用字应收的意见覆盖主应收的则要填
     * @return
     */
    private List<ContractReceivableDetailDTO> buildReceivableFollowDTO(List<ContractReceivableDetail> list,
                                                                       List<String> detailIds,
                                                                       Set<String> contractNumbers,
                                                                       List<String> receivableMainIds,
                                                                       Boolean isChild) {
        // 获取催缴跟进信息 拼接
        List<ContractReceivableFollow> followList = followService.getByReceivableDetailIdBatch(detailIds, 1);
        Map<String, ContractReceivableFollow> contractReceivableFollowMap = ListUtils.emptyIfNull(followList).stream()
                .collect(Collectors.toMap(ContractReceivableFollow::getReceivableDetailId, item -> item, (i1, i2) -> i2));
        Map<String, Integer> countByContractNumber;
        Map<String, LocalDate> receivableDateMap;
        Map<String, ContractReceivableFollow> contractReceivableFollowChildMap;
        if (contractNumbers != null) {
            countByContractNumber = getCountByContractNumber(contractNumbers);
        } else {
            countByContractNumber = null;
        }
        if (receivableMainIds != null) {
            // 还需要拼主流程的发起时间
            List<ContractReceivableMain> receivableMains = receivableMainService.listByIds(receivableMainIds);
            receivableDateMap = receivableMains.stream().collect(Collectors.toMap(ContractReceivableMain::getId, ContractReceivableMain::getReceivableDate));
        } else {
            receivableDateMap = null;
        }
        if (isChild) {
            List<ContractReceivableFollow> followChildList = followService.getByReceivableDetailIdBatch(detailIds, 2);
            contractReceivableFollowChildMap = ListUtils.emptyIfNull(followChildList).stream()
                    .collect(Collectors.toMap(ContractReceivableFollow::getReceivableDetailId, item -> item, (i1, i2) -> i2));
        } else {
            contractReceivableFollowChildMap = null;
        }
        return list.stream().map(item -> {
            ContractReceivableDetailDTO detailDTO = HyperBeanUtils.copyProperties(item, ContractReceivableDetailDTO::new);
            ContractReceivableFollow follow = contractReceivableFollowMap.get(item.getId());
            if (follow != null) {
                // 赋值
                HyperBeanUtils.copyProperties(follow, detailDTO);
                detailDTO.setId(item.getId());
                detailDTO.setIsFollow(true);
                // 催款函文件
                detailDTO.setDocs(follow.getDocs());
            } else {
                // 未跟进
                detailDTO.setIsFollow(false);
            }
            if (countByContractNumber != null) {
                detailDTO.setHistoryFollow(countByContractNumber.get(detailDTO.getContractNumber()));
            }
            if (receivableDateMap != null) {
                detailDTO.setReceivableDate(receivableDateMap.get(item.getContractReceivableMainId()));
            }
            if (isChild) {
                ContractReceivableFollow followChild = contractReceivableFollowChildMap.get(item.getId());
                if (followChild != null) {
                    HyperBeanUtils.copyProperties(followChild, detailDTO);
                    // 子应收判断欠款确认函和承诺付款时间是不是空就行
                    if (followChild.getDebtConfirmation() != null && followChild.getPromisePaymentDate() != null) {
                        detailDTO.setIsFollow(true);
                    }
                    detailDTO.setId(item.getId());
                } else {
                    detailDTO.setIsFollow(false);
                }
            }
            return detailDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 主应收催款01步 是否全部跟进 根据明细 判断主应收是否有记录 有记录就算跟进
     * 备注：产品沟通 如果是系统自动生成的催缴跟进 就不用判断那些 催款建议 直接判断表里是否有数据就行
     */
    private Boolean checkAllMainFollow(String processInstanceId) {
        ContractReceivableMainDTO receivableMainDTO = receivableMainService.getByProcessInstanceId(processInstanceId);
        List<ContractReceivableDetail> list = detailService.getByReceivableMainId(receivableMainDTO.getId());
        // 判断这些明细是否都有主应收的跟进记录
        return checkFollow(list, 1);
    }

    private Boolean checkAllChildFollow(String processInstanceId){
        ContractReceivableChild receivableChild = childService.getByProcessInstanceId(processInstanceId);
        List<ContractReceivableDetail> list = detailService.getByReceivableChildId(receivableChild.getId());
        return checkFollow(list, 2);
    }

    private Boolean checkFollow(List<ContractReceivableDetail> list, Integer source) {
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        Map<String, ContractReceivableDetail> detailById = list.stream().collect(Collectors.toMap(ContractReceivableDetail::getId, item -> item, (i1, i2) -> i2));
        List<String> detailIds = new ArrayList<>(detailById.keySet());
        List<ContractReceivableFollow> follows = followService.getByReceivableDetailIdBatch(detailIds, source);
        if (CollectionUtils.isEmpty(follows)) {
            return false;
        }
        if (source == 1) {
            // 主应收催款 是否全部跟进 判断的是 handleSuggest是否有值
            Set<String> followDetailIds = follows.stream().filter(item -> item.getHandleSuggest() != null || detailById.get(item.getReceivableDetailId()).getOverdueAmount().compareTo(BigDecimal.ZERO) == 0).map(ContractReceivableFollow::getReceivableDetailId).collect(Collectors.toSet());
            return followDetailIds.containsAll(detailIds);
        }
        if (source == 2) {
            // 子应收催款 判断的是 销售自己填的那些值是不是空的(这里校验两个值就行，假设他这个每天其他的就是空的，就要填，保存的时候已经校验过具体的逻辑了)
            Set<String> followDetailIds = follows.stream().filter(item -> item.getDebtConfirmation() != null
                    && item.getPromisePaymentDate() != null).map(ContractReceivableFollow::getReceivableDetailId).collect(Collectors.toSet());
            return followDetailIds.containsAll(detailIds);
        }
        return true;
    }

}

