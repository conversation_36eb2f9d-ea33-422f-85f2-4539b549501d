package com.topsec.crm.flow.core.controller.threeProcurement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.topsec.crm.flow.api.vo.ThreeProcurementReviewPaymentProvisionVo;
import com.topsec.crm.flow.api.vo.ThreeProcurementReviewQuery;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ThreeProcurementReviewPaymentProvision;
import com.topsec.crm.flow.core.service.ThreeProcurementReviewPaymentProvisionService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 第三方采购-付款条款Controller
 *
 * @date 2024-09-02
 */
@RestController
@RequestMapping("/threeProcurementReviewPaymentProvision")
@Tag(name = "【第三方采购-付款条款】", description = "threeProcurementReviewPaymentProvision")
public class ThreeProcurementReviewPaymentProvisionController extends BaseController
{
    @Autowired
    private ThreeProcurementReviewPaymentProvisionService threeProcurementReviewPaymentProvisionService;

    /**
     * 分页查询【第三方采购-付款条款】
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询第三方采购-付款条款")
    @PreFlowPermission
    public JsonObject<PageUtils<ThreeProcurementReviewPaymentProvisionVo>> page(@RequestBody ThreeProcurementReviewQuery threeProcurementReviewQuery)
    {
        Page<ThreeProcurementReviewPaymentProvision> threeProcurementReviewPaymentProvisionPage = threeProcurementReviewPaymentProvisionService.pageThreeProcurementReviewPaymentProvision(threeProcurementReviewQuery);
        IPage<ThreeProcurementReviewPaymentProvisionVo> convert = threeProcurementReviewPaymentProvisionPage.convert(threeProcurementReviewPaymentProvision -> {
            return HyperBeanUtils.copyPropertiesByJackson(threeProcurementReviewPaymentProvision, ThreeProcurementReviewPaymentProvisionVo.class);
        });
        return new JsonObject<>(new PageUtils<>(convert));
    }


    /**
     * 查询付款条款列表-不分页【第三方采购-付款条款】
     */
    @PostMapping("/list")
    @Operation(summary = "查询付款条款列表-不分页")
    @PreFlowPermission
    public JsonObject<List<ThreeProcurementReviewPaymentProvisionVo>> list(@RequestBody String processInstanceId)
    {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<ThreeProcurementReviewPaymentProvision> threeProcurementReviewPaymentProvisionList = threeProcurementReviewPaymentProvisionService
                .selectThreeProcurementReviewPaymentProvisionByProcessInstanceId(processInstanceId);
        List<ThreeProcurementReviewPaymentProvisionVo> threeProcurementReviewPaymentProvisionVos = HyperBeanUtils.copyListPropertiesByJackson(threeProcurementReviewPaymentProvisionList, ThreeProcurementReviewPaymentProvisionVo.class);
        return new JsonObject<>(threeProcurementReviewPaymentProvisionVos);
    }


    /**
     * 新增【第三方采购-付款条款】
     */
    @PostMapping("/add")
    @Operation(summary = "新增【第三方采购-付款条款】")
    @PreAuthorize
    public JsonObject<Boolean> add(@RequestBody ThreeProcurementReviewPaymentProvisionVo threeProcurementReviewPaymentProvisionVo)
    {
        ThreeProcurementReviewPaymentProvision threeProcurementReviewPaymentProvision = HyperBeanUtils.copyProperties(threeProcurementReviewPaymentProvisionVo, ThreeProcurementReviewPaymentProvision::new);

        return new JsonObject<>(threeProcurementReviewPaymentProvisionService.insertThreeProcurementReviewPaymentProvision(threeProcurementReviewPaymentProvision));
    }

    /**
     * 修改【第三方采购-付款条款】
     */
    @PostMapping("/edit")
    @Operation(summary = "修改【第三方采购-付款条款】")
    @PreFlowPermission
    public JsonObject<Boolean> edit(@RequestBody ThreeProcurementReviewPaymentProvisionVo threeProcurementReviewPaymentProvisionVo)
    {
        ThreeProcurementReviewPaymentProvision threeProcurementReviewPaymentProvision = HyperBeanUtils.copyProperties(threeProcurementReviewPaymentProvisionVo, ThreeProcurementReviewPaymentProvision::new);

        return new JsonObject<>(threeProcurementReviewPaymentProvisionService.updateThreeProcurementReviewPaymentProvision(threeProcurementReviewPaymentProvision));
    }

    /**
     * 删除【第三方采购-付款条款】
     */
    @PostMapping("/remove")
    @Operation(summary = "删除【第三方采购-付款条款】")
    @PreFlowPermission
    public JsonObject<Boolean> remove(@RequestBody String[] ids)
    {
        return new JsonObject<>(threeProcurementReviewPaymentProvisionService.deleteThreeProcurementReviewPaymentProvisionByIds(ids));
    }






}
