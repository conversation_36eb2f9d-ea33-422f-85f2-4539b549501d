package com.topsec.crm.flow.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.flow.api.dto.borrowForSellBack.BorrowForSellBackFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.borrowForSellBack.BorrowForSellBackPageQuery;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.BorrowForSellBackMapper;
import com.topsec.crm.flow.core.service.IBorrowForSellAttachmentService;
import com.topsec.crm.flow.core.service.IBorrowForSellBackDeviceService;
import com.topsec.crm.flow.core.service.IBorrowForSellBackService;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.Query;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.vo.node.ApproveNode;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 借转销归还/丢失赔偿信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
public class BorrowForSellBackServiceImpl extends ServiceImpl<BorrowForSellBackMapper, BorrowForSellBack> implements IBorrowForSellBackService {

    @Autowired
    private IBorrowForSellBackService borrowForSellBackService;

    @Autowired
    private IBorrowForSellBackDeviceService borrowForSellBackDeviceService;

    @Resource
    private IBorrowForSellAttachmentService borrowForSellAttachmentService;

    @Resource
    private TosEmployeeClient tosEmployeeClient;

    @Resource
    private TfsNodeClient tfsNodeClient;

    /**
     * 借转销归还/丢失赔偿流程详情
     *
     * @param processInstanceId
     * @return
     */
    @Override
    public BorrowForSellBackFlowLaunchDTO borrowForSellBackDetail(String processInstanceId) {
        BorrowForSellBack borrowForSellBack = borrowForSellBackService.getOne(new LambdaQueryWrapper<BorrowForSellBack>().eq(BorrowForSellBack::getProcessInstanceId, processInstanceId));
        if (borrowForSellBack != null) {
            borrowForSellBack.setSalesman(NameUtils.getName(borrowForSellBack.getSalesmanPersonId()));
            JsonObject<EmployeeVO> employeeVOJsonObject = tosEmployeeClient.findById(borrowForSellBack.getSalesmanPersonId());
            if(employeeVOJsonObject.isSuccess() && null != employeeVOJsonObject.getObjEntity().getDept()){
                borrowForSellBack.setSalesmanDept(employeeVOJsonObject.getObjEntity().getDept().getName());
            }
            List<BorrowForSellBackDevice> deviceList = borrowForSellBackDeviceService.list(new LambdaQueryWrapper<BorrowForSellBackDevice>().eq(BorrowForSellBackDevice::getBorrowBackId, borrowForSellBack.getId()));
            borrowForSellBack.setDeviceList(deviceList);
            List<BorrowForSellAttachment> borrowForSellAttachments = borrowForSellAttachmentService.list(new LambdaQueryWrapper<BorrowForSellAttachment>().eq(BorrowForSellAttachment::getBorrowId, borrowForSellBack.getId()));
            borrowForSellBack.setAttachments(borrowForSellAttachments);
            BorrowForSellBackFlowLaunchDTO crmBorrowForSellBackDTO = HyperBeanUtils.copyPropertiesByJackson(borrowForSellBack, BorrowForSellBackFlowLaunchDTO.class);
            return crmBorrowForSellBackDTO;
        }
        return null;
    }

    /**
     * 借转销归还/丢失赔偿流程分页
     *
     * @param borrowForSellBackPageQuery
     * @return
     */
    @Override
    public PageUtils<BorrowForSellBack> borrowForSellBackPage(BorrowForSellBackPageQuery borrowForSellBackPageQuery) {
        LocalDate start = borrowForSellBackPageQuery.getStart();
        LocalDate end = borrowForSellBackPageQuery.getEnd();
        String personId = borrowForSellBackPageQuery.getPersonId();
        String processNumber = borrowForSellBackPageQuery.getProcessNumber();
        Set<String> personIdList = borrowForSellBackPageQuery.getDataScopeParam().getPersonIdList();
        LambdaQueryWrapper<BorrowForSellBack> queryWrapper = new LambdaQueryWrapper<BorrowForSellBack>()
                .eq(StringUtils.isNotBlank(personId),BorrowForSellBack::getSalesmanPersonId, personId)
                .eq(StringUtils.isNotBlank(processNumber),BorrowForSellBack::getProcessNumber, processNumber)
                .between(start != null && end != null, BorrowForSellBack::getCreateTime, start, end)
                .in(CollectionUtils.isNotEmpty(personIdList), BorrowForSellBack::getSalesmanPersonId, personIdList)
                .orderByDesc(BorrowForSellBack::getUpdateTime);
        Page<BorrowForSellBack> page = borrowForSellBackService.page(new Query<BorrowForSellBack>().getPage(borrowForSellBackPageQuery), queryWrapper);
        List<BorrowForSellBack> records = page.getRecords();
        if(CollectionUtils.isNotEmpty(records)){
            List<String> processInstanceIds = records.stream().map(BorrowForSellBack::getProcessInstanceId).toList();
            JsonObject<Map<String, Set<ApproveNode>>> mapJsonObject = tfsNodeClient.queryNodeByProcessInstanceIdList(processInstanceIds);
            if(mapJsonObject.isSuccess()){
                Map<String, Set<ApproveNode>> map = mapJsonObject.getObjEntity();
                records.forEach(borrowForSellBack -> {
                    Set<ApproveNode> approveNodes = map.get(borrowForSellBack.getProcessInstanceId());
                    borrowForSellBack.setApprovalNode(approveNodes);
                });
            }
        }
        return new PageUtils<>(page);
    }
}
