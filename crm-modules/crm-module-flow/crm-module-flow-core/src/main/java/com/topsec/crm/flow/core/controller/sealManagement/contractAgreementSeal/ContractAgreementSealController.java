package com.topsec.crm.flow.core.controller.sealManagement.contractAgreementSeal;

import com.topsec.crm.flow.api.dto.sealApplication.SealApplicationFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.process.impl.ContractAgreementSealProcessService;
import com.topsec.crm.flow.core.service.SealApplicationRelService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.DictItemVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/contractAgreementSeal")
@Tag(name = "合同、协议类文件法律审核及印鉴使用申请", description = "/contractAgreementSeal")
@RequiredArgsConstructor
@Validated
public class ContractAgreementSealController {

    private final ContractAgreementSealProcessService contractAgreementSealProcessService;

    private final SealApplicationRelService sealApplicationRelService;

    @PostMapping("/launch")
    @Operation(summary = "发起合同、协议类文件法律审核及印鉴使用申请流程")
    @PreAuthorize(hasAnyPermission = {"crm_flow_seal","crm_seal_apply_add"})
    public JsonObject<Boolean> launch(@Valid @RequestBody SealApplicationFlowLaunchDTO launchDTO) {
        return new JsonObject<>(contractAgreementSealProcessService.launch(launchDTO));
    }

    @GetMapping("/getSealProcessAccountIds")
    @Operation(summary = "根据事项分类查询审批人员AccountId")
    @PreFlowPermission
    public JsonObject<List<String>> getSealProcessAccountIds(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(sealApplicationRelService.getAccountIdByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/getmatterTypeInfo")
    @Operation(summary = "获取事项分类")
    @PreAuthorize(hasPermission = "crm_seal_apply_add")
    public JsonObject<List<DictItemVO>> getmatterTypeInfo(){
        return new JsonObject<>(sealApplicationRelService.getmatterTypeInfo());
    }



}
