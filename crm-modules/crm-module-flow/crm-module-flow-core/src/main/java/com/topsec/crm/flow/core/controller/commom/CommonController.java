package com.topsec.crm.flow.core.controller.commom;

import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.service.FlowService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.bean.HomeNameValue;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.operation.api.RemoteContractReviewConfigService;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectMemberClient;
import com.topsec.crm.project.api.client.RemoteProjectProductOwnClient;
import com.topsec.crm.project.api.dto.ProjectDirectlyPageQuery;
import com.topsec.crm.project.api.entity.CrmProjectCommonDataVO;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.vo.TfsFormContentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/common")
@Tag(name = "业务通用Controller", description = "/common")
@RequiredArgsConstructor
@Validated
public class CommonController extends BaseController {

    private final FlowService flowService;

    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final RemoteProjectProductOwnClient remoteProjectProductOwnClient;
    private final RemoteProjectMemberClient remoteProjectMemberClient;

    private final RemoteContractReviewConfigService remoteContractReviewConfigService;

    private final TfsFormContentClient tfsformContentClient;

    @PreFlowPermission
    @GetMapping("/hasContractOfProjectByProjectId")
    @Operation(summary = "检查项目中是否已经发起了合同评审或业绩上报")
    JsonObject<Boolean>  hasContractOfProjectByProjectId(@RequestParam String projectId) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(projectId.equals(tfsFormContentVo.getProjectId())){
                return new JsonObject<>(flowService.hasContractOfProject(projectId));
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }


    @PreFlowPermission
    @PostMapping("/hasContractOfProject")
    @Operation(summary = "检查项目中是否已经发起了合同评审或业绩上报")
    JsonObject<Boolean>  hasContractOfProject(@RequestParam String projectId, @RequestBody Set<String> productRecordIdSet) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(projectId.equals(tfsFormContentVo.getProjectId())){
                return new JsonObject<>(flowService.hasContractOfProject(projectId,productRecordIdSet));
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @PostMapping("/directly/page")
    @Operation(summary = "分页查询公司项目")
    public JsonObject<PageUtils<CrmProjectDirectlyVo>> page(@RequestBody ProjectDirectlyPageQuery projectDirectlyPageQuery) {
        projectDirectlyPageQuery.setCurrentPersonId(getCurrentPersonId());
        DataScopeParam dataScopeParam = new DataScopeParam();
        dataScopeParam.setPersonIdList(Collections.singleton(getCurrentPersonId()));
        projectDirectlyPageQuery.setDataScopeParam(dataScopeParam);
        return remoteProjectDirectlyClient.directlyPageByParams(projectDirectlyPageQuery);
    }

    @PreFlowPermission
    @GetMapping("/directly/{projectId}")
    @Operation(summary = "根据项目ID获取项目信息")
    JsonObject<CrmProjectDirectlyVo> getProjectInfo(@PathVariable String projectId) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(projectId.equals(tfsFormContentVo.getProjectId())){
                return remoteProjectDirectlyClient.getProjectInfo(projectId);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @GetMapping("/directly/queryProjectDirectlyCommonDataByProjectNo")
    @Operation(summary = "根据项目编号查询公司项目通用数据")
    public JsonObject<List<CrmProjectCommonDataVO>> queryProjectDirectlyCommonDataByProjectNo(@RequestParam String projectNo) {
        List<CrmProjectCommonDataVO> result = new ArrayList<>();
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        JsonObject<List<CrmProjectCommonDataVO>> listJsonObject = remoteProjectDirectlyClient.queryProjectDirectlyCommonDataByProjectNo(projectNo);
        if(listJsonObject.isSuccess() && CollectionUtils.isNotEmpty(listJsonObject.getObjEntity())){
            for (CrmProjectCommonDataVO crmProjectCommonDataVO : listJsonObject.getObjEntity()){
                if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
                    TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
                    if(crmProjectCommonDataVO.getId().equals(tfsFormContentVo.getProjectId())){
                        result.add(crmProjectCommonDataVO);
                    }
                }
            }
        }
        return new JsonObject<>(result);
    }

    @PreFlowPermission
    @GetMapping("/hasLockedProductOfProject")
    @Operation(summary = "检查项目(公司项目、国代项目和渠道项目)中是否有锁定的产品")
    public JsonObject<Boolean> hasLockedProductOfProject(@RequestParam String projectId) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(projectId.equals(tfsFormContentVo.getProjectId())){
                JsonObject<Set<String>> setJsonObject = remoteProjectDirectlyClient.queryRecordIds(projectId);
                if(setJsonObject.isSuccess() && CollectionUtils.isNotEmpty(setJsonObject.getObjEntity())){
                    return new JsonObject<>(flowService.hasLockedProductOfProject(projectId,setJsonObject.getObjEntity()));
                }else{
                    return new JsonObject(false);
                }
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @GetMapping("/queryProjectProductOwnStore")
    @Operation(summary = "零金额续保查询产品列表")
    public JsonObject<List<CrmProjectProductOwnVO>> queryProjectProductOwnStore(@RequestParam String projectId) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(projectId.equals(tfsFormContentVo.getProjectId())){
                return remoteProjectProductOwnClient.queryProjectProductOwnStore(projectId);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    /**
     * 签订公司列表下拉查询
     * 公共基础数据接口，只验证登录权限
     * @param companyName
     * @return
     */
    @PreAuthorize
    @GetMapping("/signCompanySelect")
    @Operation(summary = "签订公司列表下拉查询")
    public JsonObject<List<HomeNameValue<String, String>>> signCompanySelect(@RequestParam(required = false) String companyName){
        return remoteContractReviewConfigService.signCompanySelect(companyName);
    }

    @GetMapping("/projectMemberPage")
    @Operation(summary = "项目成员列表")
    @PreFlowPermission
    public JsonObject<TableDataInfo> projectMemberPage(@RequestParam String projectId){
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(projectId.equals(tfsFormContentVo.getProjectId())){
                return remoteProjectMemberClient.page(projectId);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }
}
