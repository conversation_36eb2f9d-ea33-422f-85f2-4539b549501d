package com.topsec.crm.flow.core.controller.flow;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.topsec.crm.flow.api.vo.FlowProcessReturnQuery;
import com.topsec.crm.flow.api.vo.FlowProcessReturnVo;
import com.topsec.crm.flow.api.vo.FlowProcessVo;
import com.topsec.crm.flow.core.entity.FlowProcessReturn;
import com.topsec.crm.flow.core.service.FlowProcessReturnService;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.enums.FormTypeEnum;
import com.topsec.query.FormPageQuery;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.TfsFormContentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;
import java.util.Optional;


/**
 * 流程办结/退回记录表
 *
 * <AUTHOR>
 * @email 
 * @date 2025-07-19 14:28:16
 */
@RestController
@RequestMapping("/business/flowProcessReturn")
@Tag(name = "流程办结退回Controller", description = "/flowProcessReturn")
@RequiredArgsConstructor
@Validated
public class FlowProcessReturnController {

    private  final FlowProcessReturnService flowProcessReturnService;

    private final TfsFormContentClient tfsFormContentClient;


    /**
     * 分页查询办结退回记录
     */
    @PostMapping("/pageFlowProcessReturn")
    @Operation(summary = "分页查询办结退回记录")
    public JsonObject<PageUtils<FlowProcessReturnVo>> pageFlowProcessReturn(@Valid @RequestBody FlowProcessReturnQuery flowProcessReturnQuery) {
        Page<FlowProcessReturn> flowProcessReturnPage = flowProcessReturnService.pageFlowProcessReturn(flowProcessReturnQuery);
        IPage<FlowProcessReturnVo> convert = flowProcessReturnPage.convert(threeProcurement ->
                HyperBeanUtils.copyPropertiesByJackson(threeProcurement, FlowProcessReturnVo.class)
        );
        NameUtils.setName(convert);
        return new JsonObject<>(new PageUtils<>(convert));
    }

    /**
     * 新增退回办结记录
     */
    @PostMapping("/addFlowProcessReturn")
    @Operation(summary = "新增退回办结记录")
    public JsonObject<Boolean> addFlowProcessReturn(@Valid @RequestBody FlowProcessReturnVo flowProcessReturnVo) {
        FlowProcessReturn flowProcessReturn = HyperBeanUtils.copyPropertiesByJackson(flowProcessReturnVo, FlowProcessReturn.class);
        return new JsonObject<>(flowProcessReturnService.save(flowProcessReturn));
    }

    /**
     * 根基流程类型和流程编号查询流程
     */
    @GetMapping("/queryCrmFlowProcess")
    @Operation(summary = "根基流程类型和流程编号查询流程")
    public JsonObject<FlowProcessVo> queryFlowProcessReturn(@RequestParam Integer processType, @RequestParam String processNumber) {
        CrmAssert.hasText(processNumber,"流程编号不能为空");
        CrmAssert.notNull(processType,"流程类型不能为空");
        TfsFormContentVo tfsFormContentVo = Optional.ofNullable(tfsFormContentClient.formInfo(processNumber))
                .map(JsonObject::getObjEntity)
                .orElseThrow(() -> new CrmException("流程不存在"));
        if (!Objects.equals(tfsFormContentVo.getType(), processType) && !Objects.equals(tfsFormContentVo.getType(), FormTypeEnum.CONTRACT_REVIEW.getType())){
            throw new CrmException("流程类型错误");
        }
        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(tfsFormContentVo, FlowProcessVo.class));
    }

}
