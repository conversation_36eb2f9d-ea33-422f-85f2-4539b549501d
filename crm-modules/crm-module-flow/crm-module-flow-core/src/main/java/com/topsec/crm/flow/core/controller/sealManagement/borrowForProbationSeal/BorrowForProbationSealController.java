package com.topsec.crm.flow.core.controller.sealManagement.borrowForProbationSeal;


import com.topsec.crm.flow.api.dto.sealApplication.SealApplicationFlowLaunchDTO;
import com.topsec.crm.flow.core.entity.BorrowForProbationAttachment;
import com.topsec.crm.flow.core.process.impl.BorrowForProbationSealProcessService;
import com.topsec.crm.flow.core.service.BorrowForProbationAttachmentService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.entity.CrmBorrowForProbationAttachmentVO;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @version V1.0
 * @Description: 借试用印鉴申请流程
 * @ClassName: com.topsec.crm.flow.core.controller.SealManagement.borrowForProbationSeal.BorrowForProbationSealController.java
 * @Copyright 天融信 - Powered By 企业软件研发中心
 * @author: leo
 * @date: 2025-05-20 09:25
 */
@RestController
@RequestMapping("/borrowForProbationSeal")
@Tag(name = "借试用印鉴申请流程", description = "/borrowForProbationSeal")
public class BorrowForProbationSealController extends BaseController {

    @Autowired
    private BorrowForProbationSealProcessService borrowForProbationSealProcessService;

    @Autowired
    private BorrowForProbationAttachmentService borrowForProbationAttachmentService;

    @PreAuthorize(hasPermission = "crm_flow_device_probation")
    @PostMapping("/launch")
    @Operation(summary = "发起借试用印鉴申请流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody SealApplicationFlowLaunchDTO launchDTO) {
        if(null != launchDTO.getBeanInfoLaunchDTO() && getCurrentPersonId().equals(launchDTO.getBeanInfoLaunchDTO().getCreateUser())){
            return new JsonObject<>(borrowForProbationSealProcessService.launch(launchDTO));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_device_probation")
    @GetMapping("/queryBorrowForProbationContractAttachment")
    @Operation(summary = "查询借试用流程中的借试用合同附件")
    public JsonObject<List<CrmBorrowForProbationAttachmentVO>> queryBorrowForProbationContractAttachment(@RequestParam String processInstanceId) {
        List<BorrowForProbationAttachment> borrowForProbationAttachments = borrowForProbationAttachmentService.queryBorrowForProbationContractAttachment(processInstanceId);
        return new JsonObject<>(HyperBeanUtils.copyListPropertiesByJackson(borrowForProbationAttachments, CrmBorrowForProbationAttachmentVO.class
        ));
    }
}
