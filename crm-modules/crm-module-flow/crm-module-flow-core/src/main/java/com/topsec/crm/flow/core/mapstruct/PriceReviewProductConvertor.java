package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnSubServVO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductThirdDTO;
import com.topsec.crm.flow.api.dto.pricereview.ProductOwnServiceRangeVO;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.framework.common.bean.CrmProjectProductOwnServiceRangeVo;
import com.topsec.crm.framework.common.bean.CrmProjectProductOwnServiceVo;
import com.topsec.crm.project.api.entity.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 如果改了mapper class名字造成NoClassDefFoundError，项目rebuild下或者maven clean下
 * <AUTHOR>
 */
@Mapper
public interface PriceReviewProductConvertor {

    PriceReviewProductConvertor INSTANCE = Mappers.getMapper(PriceReviewProductConvertor.class);




    @Mapping(target = "id", ignore = true)
    @Mapping(target = "productPeriodStart", ignore = true)
    @Mapping(target = "productPeriodEnd", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "recordId", source = "id")
    PriceReviewProductThird toThird(CrmProjectProductThirdVo crmProjectProductThirdVo);
    List<PriceReviewProductThird> toThirdList(List<CrmProjectProductThirdVo> crmProjectProductThirdVoList);


    @Mapping(target = "state", ignore = true)
    PriceReviewProductThirdDTO toThirdDTO(PriceReviewProductThird third);
    List<PriceReviewProductThirdDTO> toThirdDTOList(List<PriceReviewProductThird> thirdList);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "productPeriodStart", ignore = true)
    @Mapping(target = "productPeriodEnd", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "recordId", source = "id")
    PriceReviewProductThirdDTO crmToThirdDTO(CrmProjectProductThirdVo crmProjectProductThirdVo);
    List<PriceReviewProductThirdDTO> crmToThirdDTOList(List<CrmProjectProductThirdVo> crmProjectProductThirdVoList);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "recordId", source = "id")
    PriceReviewProductServiceOutsourcing toOutsourcing(CrmProjectOutsourcingServiceVo crmProjectOutsourcingServiceVo);
    List<PriceReviewProductServiceOutsourcing> toOutsourcingList(List<CrmProjectOutsourcingServiceVo> crmProjectProductThirdVoList);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "recordId", source = "id")
    @Mapping(target = "associatedSerialNumbers", ignore = true)
    @Mapping(target = "sellinPrice",ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "tempCostAddedPersonId", ignore = true)
    @Mapping(target = "tempCost", ignore = true)
    @Mapping(target = "isTempCostAdded", ignore = true)
    // @Mapping(target = "isProductInfoAdded", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "approveType", ignore = true)
    @Mapping(target = "approveSpecialOperation", ignore = true)
    @Mapping(target = "approveMode", ignore = true)
    @Mapping(target = "approveGroupId", ignore = true)
    @Mapping(target = "approvalOfCandidateList", ignore = true)
    @Mapping(source = "crmProjectProductSn", target = "productSn")
    @Mapping(target = "serviceRange", source = "crmProjectProductOwnServiceRange")
    @Mapping(target = "subServiceList", source = "crmProjectProductOwnService")
    @Mapping(source = "crmProjectProductMaintain.operationMaintenanceRatio", target = "operationMaintenanceWorkloadRatio")
    @Mapping(source = "crmProjectProductMaintain.supportMode", target = "onSiteSupportMode")
    @Mapping(source = "crmProjectProductMaintain.evaluation", target = "evaluationResult")
    @Mapping(source = "crmProjectProductMaintain.relatedDeptId", target = "relatedDepartmentId")
    @Mapping(source = "crmProjectProductMaintain.crmProjectProductMaintainInvestment", target = "maintainInvestment")
    PriceReviewProductOwnDTO crmToOwnDTO(CrmProjectProductOwnVO ownVo);
    List<PriceReviewProductOwnDTO> crmToOwnDTOList(List<CrmProjectProductOwnVO> ownVoList);



    @Mapping(target = "frontEdit", ignore = true)
    @Mapping(target = "inspection", ignore = true)
    @Mapping(ignore = true, target = "fission")
    @Mapping(ignore = true, target = "productLine1")
    @Mapping(target = "priceState", ignore = true)
//    @Mapping(target = "inContract", ignore = true)
    @Mapping(target = "saleAccess", ignore = true)
    @Mapping(target = "supplier", ignore = true)
    @Mapping(target = "state", ignore = true)
    @Mapping(target = "reportStatus", ignore = true)
    @Mapping(target = "productStatus", ignore = true)
    @Mapping(target = "processInfoList", ignore = true)
    @Mapping(target = "hasChild", ignore = true)
    @Mapping(target = "expiryDate", ignore = true)
    @Mapping(target = "customizedType", ignore = true)
    @Mapping(target = "crmProjectProductSn", source = "productSn")
    @Mapping(target = "crmProjectProductOwnServiceRange", source = "serviceRange")
    @Mapping(target = "crmProjectProductOwnService", source = "subServiceList")
    @Mapping(target = "id", source = "recordId")
    @Mapping(target = "crmProjectProductMaintain.operationMaintenanceRatio", source = "operationMaintenanceWorkloadRatio")
    @Mapping(target = "crmProjectProductMaintain.supportMode", source = "onSiteSupportMode")
    @Mapping(target = "crmProjectProductMaintain.evaluation", source = "evaluationResult")
    @Mapping(target = "crmProjectProductMaintain.relatedDeptId", source = "relatedDepartmentId")
    @Mapping(target = "crmProjectProductMaintain.crmProjectProductMaintainInvestment", source = "maintainInvestment")
    @Mapping(target = "crmProjectProductMaintain.recordId", source = "recordId")
    CrmProjectProductOwnVO dtoToOwnCrm(PriceReviewProductOwnDTO own);
    List<CrmProjectProductOwnVO> dtoToOwnCrmList(List<PriceReviewProductOwnDTO> owns);


    @Mapping(target = "inContract", ignore = true )
    @Mapping( target = "frontEdit", ignore = true)
    @Mapping(target = "priceState", ignore = true)
    @Mapping(target = "expiryDate", ignore = true)
    @Mapping(target = "productStatus", ignore = true)
    @Mapping(target = "processInfoList", ignore = true)
    @Mapping(target = "dealTotalPrice", ignore = true)
    @Mapping(target = "state", ignore = true)
    @Mapping(target = "id", source = "recordId")
    CrmProjectProductThirdVo dtoToThirdCrm(PriceReviewProductThirdDTO third);
    List<CrmProjectProductThirdVo> dtoToThirdCrmList(List<PriceReviewProductThirdDTO> third);

    @Mapping(ignore = true, target = "processInfoList")
    @Mapping(target = "priceState", ignore = true)
    @Mapping(target = "expiryDate", ignore = true)
    @Mapping(target = "productStatus", ignore = true)
    @Mapping(target = "state", ignore = true)
    @Mapping(target = "id", source = "recordId")
    CrmProjectOutsourcingServiceVo toOutsourcingCrm(PriceReviewProductServiceOutsourcing own);




    @Mapping(target = "tempCostAddedPersonId", ignore = true)
    @Mapping(target = "tempCost", ignore = true)
    @Mapping(target = "relatedDepartmentId", ignore = true)
    @Mapping(target = "operationMaintenanceWorkloadRatio", ignore = true)
    @Mapping(target = "onSiteSupportMode", ignore = true)
    @Mapping(target = "maintainInvestment", ignore = true)
    @Mapping(target = "isTempCostAdded", ignore = true)
    // @Mapping(target = "isProductInfoAdded", ignore = true)
    @Mapping(target = "evaluationResult", ignore = true)
    @Mapping(target = "associatedSerialNumbers", ignore = true)
    @Mapping(target = "state", ignore = true)
    @Mapping(target = "subServiceList", ignore = true)
    @Mapping(target = "approveType", ignore = true)
    @Mapping(target = "approveSpecialOperation", ignore = true)
    @Mapping(target = "approveMode", ignore = true)
    @Mapping(target = "approveGroupId", ignore = true)
    @Mapping(target = "approvalOfCandidateList", ignore = true)
    PriceReviewProductOwnDTO toOwnDTO(PriceReviewProductOwn own);
    List<PriceReviewProductOwnDTO> toOwnDTOList(List<PriceReviewProductOwn> ownList);


    @Mapping(target = "dataScopeParam", ignore = true)
    PriceReviewProductOwn dtoToOwn(PriceReviewProductOwnDTO own);
    List<PriceReviewProductOwn> dtoToOwnList(List<PriceReviewProductOwnDTO> ownList);


    @Mapping(target = "dataScopeParam", ignore = true)
    PriceReviewProductThird dtoToThird(PriceReviewProductThirdDTO own);
    List<PriceReviewProductThird> dtoToThirdList(List<PriceReviewProductThirdDTO> ownList);



    @Mapping(target = "supportMode", source = "onSiteSupportMode")
    @Mapping(target = "relatedDeptId", source = "relatedDepartmentId")
    @Mapping(target = "recordId", source = "recordId")
    @Mapping(target = "operationMaintenanceRatio", source = "operationMaintenanceWorkloadRatio")
    @Mapping(target = "evaluation", source = "evaluationResult")
    @Mapping(target = "crmProjectProductMaintainInvestment", source = "maintainInvestment")
    @Mapping(target = "id", expression = "java(com.topsec.tbscommon.utils.UUIDUtils.generateUUID())")
    CrmProjectProductMaintainVO toMaintainCrm(PriceReviewSpecialCode own);


    CrmProjectProductOwnServiceVo toCrmSubServ(PriceReviewProductOwnSubServ priceReviewProductOwnSubServ);
    List<CrmProjectProductOwnServiceVo> toCrmSubServList(List<PriceReviewProductOwnSubServ> priceReviewProductOwnSubServ);

    PriceReviewProductOwnSubServVO toSubServVO(PriceReviewProductOwnSubServ priceReviewProductOwnSubServ);
    List<PriceReviewProductOwnSubServVO> toSubServVOList(List<PriceReviewProductOwnSubServ> priceReviewProductOwnSubServ);

    @Mapping(target = "dataScopeParam", ignore = true)
    PriceReviewProductOwnSubServ voToSubServ(PriceReviewProductOwnSubServVO priceReviewProductOwnSubServ);
    List<PriceReviewProductOwnSubServ> voToSubServList(List<PriceReviewProductOwnSubServVO> priceReviewProductOwnSubServ);


    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "id", ignore = true)
    PriceReviewProductOwnSubServVO crmToSubServVO(CrmProjectProductOwnServiceVo priceReviewProductOwnSubServ);
    List<PriceReviewProductOwnSubServVO> crmToSubServVOList(List<CrmProjectProductOwnServiceVo> priceReviewProductOwnSubServ);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    void mergeSpecialCode(PriceReviewSpecialCode specialCode, @MappingTarget PriceReviewProductOwnDTO productOwnDTO);


    CrmProjectProductOwnServiceRangeVo toCrmServiceRange(ProductOwnServiceRangeVO priceReviewProductOwnSubServ);
    List<CrmProjectProductOwnServiceRangeVo> totoCrmServiceRangeList(List<ProductOwnServiceRangeVO> priceReviewProductOwnSubServ);

    ProductOwnServiceRangeVO toServiceRangeVO(CrmProjectProductOwnServiceRangeVo priceReviewProductOwnSubServ);
    List<ProductOwnServiceRangeVO> toServiceRangeVOList(List<CrmProjectProductOwnServiceRangeVo> priceReviewProductOwnSubServ);

}
