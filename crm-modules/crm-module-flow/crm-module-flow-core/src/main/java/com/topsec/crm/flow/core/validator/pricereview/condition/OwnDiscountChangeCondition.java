package com.topsec.crm.flow.core.validator.pricereview.condition;

import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.core.validator.CheckCondition;
import com.topsec.crm.flow.core.validator.pricereview.PriceReviewCheckContext;
import com.topsec.crm.flow.core.validator.pricereview.ProductCompareUtil;
import org.apache.commons.collections4.ListUtils;

import java.util.List;

/**
 * 自有产品中折扣变化
 * <AUTHOR>
 */
public class OwnDiscountChangeCondition implements CheckCondition<PriceReviewCheckContext> {


    @Override
    public boolean check(PriceReviewCheckContext context)  {
        List<PriceReviewProductOwnDTO> currentOwnList = ListUtils.emptyIfNull(context.getProjectDetail().getProductOwnList());
        List<PriceReviewProductOwnDTO> snapshotOwnList = ListUtils.emptyIfNull(context.getProjectSnapshot().getProductOwnList());
        return ProductCompareUtil.validate(currentOwnList, snapshotOwnList, "discount");
    }

    @Override
    public String defaultFailureReason() {
        return "自有产品折扣变化";
    }


}
