package com.topsec.crm.flow.core.controller.legalAffairs.businessauth;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemotePaymentVerificationService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.paymentcollection.PaymentVerificationDTO;
import com.topsec.crm.flow.api.RemoteContractSignVerifyMainService;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractSignVerifyMainVo;
import com.topsec.crm.flow.api.dto.contractoriginal.vo.ContractDocVO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewNoticeArriveDTO;
import com.topsec.crm.flow.api.dto.legalAffairsMain.VO.LegalAffairsMainInfoVO;
import com.topsec.crm.flow.api.dto.proofOfDebtCollection.ProofOfDebtCollectionQuery;
import com.topsec.crm.flow.api.dto.proofOfDebtCollection.ProofOfDebtCollectionVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ContractReviewMain;
import com.topsec.crm.flow.core.entity.ContractReviewNoticeArrive;
import com.topsec.crm.flow.core.entity.LegalAffairsMain;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.page.PageDomain;
import com.topsec.crm.framework.common.web.page.TableSupport;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@RestController
@AllArgsConstructor
@RequestMapping("/business/legalAffairs")
@Tag(name = "法律事务业务接口",  description = "/business/legalAffairs")
public class LegalAffairsBusinessController {

    private final LegalAffairsMainService legalAffairsMainService;
    private final RemoteContractExecuteService remoteContractExecuteService;
    private final ContractReviewMainService mainService;
    private final ContractReviewNoticeArriveService noticeArriveService;
    private final ContractOriginalDocumentService contractOriginalDocumentService;
    private final RemotePaymentVerificationService remotePaymentVerificationService;
    private final ProofOfDebtCollectionService proofOfDebtCollectionService;
    private final RemoteContractSignVerifyMainService remoteContractSignVerifyMainService;

    @GetMapping("/getLegalAffairsMainByProcessInstanceId")
    @Operation(summary = "根据流程ID查询信息")
    @PreAuthorize(hasPermission = "crm_contract_receivable_legal_affairs", dataScope = "crm_contract_receivable_legal_affairs")
    public JsonObject<LegalAffairsMainInfoVO> getLegalAffairsMainByProcessInstanceId(@RequestParam String processInstanceId){
        checkLegalAffairAuthByProcessInstanceId(processInstanceId);
        return new JsonObject<>(legalAffairsMainService.getLegalAffairsMainByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/getNoticeArriveByProcessInstanceId")
    @Operation(summary = "根据流程实例id查询通知和送达信息")
    @PreAuthorize(hasPermission = "crm_contract_receivable_legal_affairs", dataScope = "crm_contract_receivable_legal_affairs")
    public JsonObject<List<ContractReviewNoticeArriveDTO>> getNoticeArriveByProcessInstanceId(@RequestParam String processInstanceId) {
        LegalAffairsMain affairsMain = legalAffairsMainService.getByProcessInstanceId(processInstanceId);
        String contractNumber = affairsMain.getContractNumber();
        checkLegalAffairsAuthByContractNumber(contractNumber);
        ContractReviewMain main = mainService.getOne(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getContractNumber, contractNumber)
                .eq(ContractReviewMain::getDelFlag, 0).last("limit 1"));

        return new JsonObject<>(HyperBeanUtils.copyListProperties(noticeArriveService.list(new LambdaQueryWrapper<ContractReviewNoticeArrive>()
                .eq(ContractReviewNoticeArrive::getContractReviewMainId, main.getId())
                .eq(ContractReviewNoticeArrive::getDelFlag, 0)), ContractReviewNoticeArriveDTO::new));
    }

    @PostMapping("/listDocumentByContractNumber")
    @Operation(summary = "根据流程ID获取合同原件信息")
    @PreAuthorize(hasPermission = "crm_contract_receivable_legal_affairs", dataScope = "crm_contract_receivable_legal_affairs")
    public JsonObject<List<ContractDocVO>> getListDocumentByProcessInstanceId(@RequestParam String processInstanceId) {
        LegalAffairsMainInfoVO infoVO = legalAffairsMainService.getLegalAffairsMainByProcessInstanceId(processInstanceId);
        String contractNumber = infoVO.getContractNumber();
        checkLegalAffairsAuthByContractNumber(contractNumber);
        return new JsonObject<>(contractOriginalDocumentService.listDocumentByContractNumber(contractNumber));
    }

    @GetMapping("/getVerificationListByBusinessNumber")
    @Operation(summary = "获取合同回款信息(根据合同号)", method = "GET")
    @PreAuthorize(hasPermission = "crm_contract_receivable_legal_affairs", dataScope = "crm_contract_receivable_legal_affairs")
    public JsonObject<List<PaymentVerificationDTO>> getVerificationListByBusinessNumber(@RequestParam String processInstanceId) {
        LegalAffairsMain main = legalAffairsMainService.getByProcessInstanceId(processInstanceId);
        if (main == null) {
            throw new CrmException("参数有误");
        }
        String contractNumber = main.getContractNumber();
        checkLegalAffairsAuthByContractNumber(contractNumber);
        return remotePaymentVerificationService.getVerificationListByBusinessNumber(contractNumber);
    }

    @GetMapping("/getProofOfDebtCollectionByProcessInstanceId")
    @Operation(summary = "查询催款证据", method = "GET")
    @PreAuthorize(hasPermission = "crm_contract_receivable_legal_affairs", dataScope = "crm_contract_receivable_legal_affairs")
    public JsonObject<PageUtils<ProofOfDebtCollectionVO>> getProofOfDebtCollection(@RequestParam String processInstanceId) {
        LegalAffairsMain main = legalAffairsMainService.getByProcessInstanceId(processInstanceId);
        if (main == null) {
            throw new CrmException("参数有误");
        }
        String contractNumber = main.getContractNumber();
        checkLegalAffairsAuthByContractNumber(contractNumber);
        ProofOfDebtCollectionQuery proofOfDebtCollectionQuery = new ProofOfDebtCollectionQuery();
        proofOfDebtCollectionQuery.setContractNumber(contractNumber);
        PageDomain pageDomain = TableSupport.buildPageRequest();
        proofOfDebtCollectionQuery.setPageSize(pageDomain.getPageSize());
        proofOfDebtCollectionQuery.setPageNum(pageDomain.getPageNum());
        return new JsonObject<>(proofOfDebtCollectionService.page(proofOfDebtCollectionQuery));

    }

    @GetMapping("/getContractSignVerifyByProcessInstanceId")
    @Operation(summary = "查询签验收单信息", method = "GET")
    @PreAuthorize(hasPermission = "crm_contract_receivable_legal_affairs", dataScope = "crm_contract_receivable_legal_affairs")
    public JsonObject<List<ContractSignVerifyMainVo>> get(@RequestParam String processInstanceId) {
        LegalAffairsMain main = legalAffairsMainService.getByProcessInstanceId(processInstanceId);
        if (main == null) {
            throw new CrmException("参数有误");
        }
        checkLegalAffairsAuthByContractNumber(main.getContractNumber());
        return remoteContractSignVerifyMainService.list(main.getContractNumber());
    }

    private void checkLegalAffairsAuthByContractNumber(String contractNumber) {
        check(contractNumber, remoteContractExecuteService);
    }

    private void checkLegalAffairAuthByProcessInstanceId(String processInstanceId) {
        LegalAffairsMain main = legalAffairsMainService.getByProcessInstanceId(processInstanceId);
        String contractNumber = main.getContractNumber();
        checkLegalAffairsAuthByContractNumber(contractNumber);
    }

    public static void check(String contractNumber, RemoteContractExecuteService remoteContractExecuteService) {
        JsonObject<CrmContractExecuteVO> result = remoteContractExecuteService.getByContractNumber(contractNumber);
        if (!result.isSuccess() || result.getObjEntity() == null) {
            throw new CrmException("参数信息有误");
        }
        CrmContractExecuteVO executeVO = result.getObjEntity();
        String contractOwnerId = executeVO.getContractOwnerId();
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        if (personIdList == null) {
            return;
        }
        if (!personIdList.contains(contractOwnerId)) {
            // 不包含合同负责人的id 代表没权限
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

}
