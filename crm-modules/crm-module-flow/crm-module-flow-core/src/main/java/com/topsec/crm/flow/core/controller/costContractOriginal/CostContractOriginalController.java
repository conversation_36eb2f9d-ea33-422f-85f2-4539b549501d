package com.topsec.crm.flow.core.controller.costContractOriginal;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.api.dto.costContarct.CostContractAttachmentInfoDTO;
import com.topsec.crm.flow.api.dto.costContractOriginal.CostContractOriginalFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.costContractOriginal.CostContractOriginalInfoDTO;
import com.topsec.crm.flow.api.dto.costContractOriginal.CostContractOriginalSignatureInfoDTO;
import com.topsec.crm.flow.api.dto.costContractOriginal.VO.CostContractOriginalInfoVO;
import com.topsec.crm.flow.api.dto.costContractOriginal.VO.CostContractOriginalVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.process.impl.CostContractOriginalProcessService;
import com.topsec.crm.flow.core.service.CostContractAttachmentService;
import com.topsec.crm.flow.core.service.CostContractOriginalService;
import com.topsec.crm.flow.core.service.CostContractOriginalSignatureService;
import com.topsec.crm.flow.core.service.CostContractService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;

import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 费用合同原件
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/costContractOriginal")
@Tag(name = "【费用合同原件-流程接口】", description = "costContractOriginal")
@RequiredArgsConstructor
@Validated
public class CostContractOriginalController {

    private final CostContractOriginalService costContractOriginalService;

    private final CostContractOriginalProcessService costContractOriginalProcessService;

    private final CostContractOriginalSignatureService costContractOriginalSignatureService;

    private final CostContractAttachmentService costContractAttachmentService;

    private final CostContractService costContractService;


    @PreAuthorize(hasPermission="crm_flow_cost_contract")
    @PostMapping("/launch")
    @Operation(summary = "发起费用合同原件流程")
    public JsonObject<Boolean> launch(@RequestBody CostContractOriginalFlowLaunchDTO launchDTO){
        return new JsonObject<>(costContractOriginalProcessService.launch(launchDTO));
    }

    @PreFlowPermission
    @GetMapping("/getByProcessInstanceId")
    @Operation(summary = "根据流程ID查询原件信息")
    public JsonObject<CostContractOriginalInfoVO> getByProcessInstanceId(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(costContractOriginalService.getByProcessInstanceId(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/getSignatureByOriginalId")
    @Operation(summary = "根据原件表ID查询签名集合")
    public JsonObject<List<CostContractOriginalSignatureInfoDTO>> getSignatureByOriginalId(@RequestParam String originalId){
        Optional<CostContractOriginal> optional = Optional.ofNullable(costContractOriginalService.getById(originalId));
        if (optional.isPresent()){
            CostContractOriginal original = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(original.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractOriginalSignatureService.getSignatureByOriginalId(originalId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission(hasAnyNodes = {"sid-B396F0E8-4444-4BDC-89E4-128F7C88D562","sid-4B990E4E-6826-49C8-9006-20CC7A60DA62"})
    @PostMapping("/updateOriginalInfo")
    @Operation(summary = "01 、02 修改附件档案编号信息")
    public JsonObject<Boolean> updateOriginalInfo(@RequestBody CostContractOriginalInfoDTO originalInfoDTO){
        Optional<CostContractOriginal> optional = Optional.ofNullable(costContractOriginalService.getById(originalInfoDTO.getId()));
        if (optional.isPresent()){
            CostContractOriginal original = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(original.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractOriginalService.updateOriginalInfo(originalInfoDTO));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission
    @PostMapping("/saveSignature")
    @Operation(summary = "新增用户签名")
    public JsonObject<Boolean> saveSignature(@RequestBody CostContractOriginalSignatureInfoDTO originalSignatureInfoDTO){
        Optional<CostContractOriginal> optional = Optional.ofNullable(costContractOriginalService.getById(originalSignatureInfoDTO.getOriginalId()));
        if (optional.isPresent()){
            CostContractOriginal original = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(original.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractOriginalSignatureService.saveSignature(originalSignatureInfoDTO));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission
    @GetMapping("/deleteSignature")
    @Operation(summary = "删除用户签名")
    public JsonObject<Boolean> deleteSignature(@RequestParam String id){
        Optional<CostContractOriginalSignature> optional = Optional.ofNullable(costContractOriginalSignatureService.getById(id));
        if (optional.isPresent()){
            CostContractOriginal original = costContractOriginalService.getById(optional.get().getOriginalId());
            PreFlowPermissionAspect.checkProcessInstanceId(original.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractOriginalSignatureService.deleteSignature(id));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission
    @GetMapping("/updateOriginalIs")
    @Operation(summary = "01、02办理后调用接口修改是否确认（step为步骤名称前两位01/02）")
    public JsonObject<Boolean> updateOriginalIs(@RequestParam String originalId,@RequestParam String step){
        Optional<CostContractOriginal> optional = Optional.ofNullable(costContractOriginalService.getById(originalId));
        if (optional.isPresent()){
            CostContractOriginal original = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(original.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractOriginalService.updateOriginalIs(originalId,step));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission
    @GetMapping("/getByCostContractId")
    @Operation(summary = "查询合同原件附件信息")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getByCostContractId(@RequestParam String costId, @RequestParam(required = false) String isFinalQuery){
        Optional<CostContractOriginal> optional = Optional.ofNullable(costContractOriginalService.getById(costId));
        if (optional.isPresent()){
            CostContractOriginal original = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(original.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractAttachmentService.getByCostContractId(costId,isFinalQuery));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission
    @GetMapping("/getAttachmentByProcessInstanceId")
    @Operation(summary = "查询费用合同附件信息")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getAttachmentByProcessInstanceId(@RequestParam String processInstanceId,@RequestParam(required = false) String isFinalQuery){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        Optional<CostContractOriginal> optional = Optional.ofNullable(costContractOriginalService.getOne(new QueryWrapper<CostContractOriginal>().lambda()
                .eq(CostContractOriginal::getDelFlag,0).eq(CostContractOriginal::getProcessInstanceId,processInstanceId)));
        if (optional.isPresent()){
            CostContractOriginal costContractOriginal = optional.get();
            CostContract costContract = costContractService.getById(costContractOriginal.getCostContractId());
            return new JsonObject<>(costContractAttachmentService.getByCostContractId(costContract.getId(),isFinalQuery));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission
    @PostMapping("/saveOriginalAttachment")
    @Operation(summary = "新增费用合同原件附件")
    public JsonObject<Boolean> saveAttachment(@RequestBody CostContractAttachmentInfoDTO attachmentInfoDTO){
        CrmAssert.hasText(attachmentInfoDTO.getCostId(),"主表ID不能为空");
        Optional<CostContractOriginal> optional = Optional.ofNullable(costContractOriginalService.getById(attachmentInfoDTO.getCostId()));
        if (optional.isPresent()){
            CostContractOriginal original = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(original.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            attachmentInfoDTO.setCostType("费用合同原件");
            return new JsonObject<>(costContractAttachmentService.saveAttachment(attachmentInfoDTO));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission
    @GetMapping("/deleteOriginalAttachment")
    @Operation(summary = "删除费用合同原件附件信息")
    public JsonObject<Boolean> deleteAttachment(@RequestParam String id){
        Optional<CostContractAttachment> optional = Optional.ofNullable(costContractAttachmentService.getById(id));
        if (optional.isPresent()){
            CostContractOriginal original = costContractOriginalService.getById(optional.get().getCostId());
            PreFlowPermissionAspect.checkProcessInstanceId(original.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractAttachmentService.deleteAttachment(id));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }


}
