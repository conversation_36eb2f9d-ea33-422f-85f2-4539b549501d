package com.topsec.crm.flow.core.controller.adborrowforprobation;

import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationDeliveryDetailVO;
import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationProductSnVO;
import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.AdBorrowForProbation;
import com.topsec.crm.flow.core.service.AdBorrowForProbationDeliveryDetailService;
import com.topsec.crm.flow.core.service.AdBorrowForProbationProductSnService;
import com.topsec.crm.flow.core.service.AdBorrowForProbationService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.product.api.RemoteProductAgentInventoryService;
import com.topsec.crm.product.api.dto.CrmModelMachineProductQuery;
import com.topsec.crm.product.api.entity.CrmMatchMachineSerialVO;
import com.topsec.crm.product.api.entity.CrmModelMachineProductAgentInventoryVO;
import com.topsec.crm.product.api.entity.CrmModelMachineSerialNumberVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.logistics.api.client.RemoteLogisticsClient;
import com.topsec.logistics.api.domain.BatchSubParam;
import com.topsec.logistics.api.domain.LogisticsInfo;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 样机借试用信息 业务
 */
@RestController
@RequestMapping("/business/adBorrowForProbation")
@Tag(description = "/business/adBorrowForProbation", name = "样机借试用业务流程查询")
public class AdBorrowForProbationBussinessController extends BaseController {

    @Resource
    private AdBorrowForProbationService adBorrowForProbationService;

    @Resource
    private AdBorrowForProbationProductSnService adBorrowForProbationProductSnService;

    @Resource
    private RemoteAgentService remoteAgentService;

    @Resource
    private RemoteProductAgentInventoryService remoteProductAgentInventoryService;

    @Resource
    private AdBorrowForProbationDeliveryDetailService adBorrowForProbationDeliveryDetailService;

    @Resource
    private RemoteLogisticsClient remoteLogisticsClient;

    @PostMapping("/flow/page")
    @Operation(summary = "样机借试用流程列表查询")
    @PreAuthorize(hasPermission = "crm_sample_probation",dataScope = "crm_sample_probation")
    public JsonObject<TableDataInfo> page(@RequestBody AdBorrowForProbationFlowLaunchDTO launchDTO) {
        startPage();
        TableDataInfo tableDataInfo = adBorrowForProbationService.page(launchDTO);
        return new JsonObject<>(tableDataInfo);
    }

    @GetMapping("/flow/detail/{processInstanceId}")
    @Operation(summary = "查看样机借试用流程详情")
    @PreAuthorize(hasPermission = "crm_sample_probation",dataScope = "crm_sample_probation")
    public JsonObject<AdBorrowForProbationVO> selectAdBorrowForProbationDetail(@PathVariable String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        isHavePermission(processInstanceId);
        return new JsonObject<>(adBorrowForProbationService.selectAdBorrowForProbationDetail(processInstanceId));
    }

    @GetMapping("/flow/getValidList")
    @Operation(summary = "查询当前登录人已生效借试用列表(可还)")
    @PreAuthorize(hasAnyPermission = {"crm_sample_probation","crm_sample_back"})
    JsonObject<List<AdBorrowForProbationVO>> getValidList() {
        return new JsonObject<>(adBorrowForProbationService.getValidByPersonId(UserInfoHolder.getCurrentPersonId()));
    }

    @PostMapping("/selectGDandSDSelect")
    @Operation(summary = "查询国代和省代列表")
    @PreAuthorize(hasAnyPermission = {"crm_sample_probation","crm_sample_back"})
    JsonObject<List<CrmAgentVo>> selectGDandSDSelect(CrmAgentVo crmAgentVo) {
        return remoteAgentService.selectGDandSDSelect(crmAgentVo);
    }

    @PostMapping("/flow/selectSnListByProcessId/{processInstanceId}")
    @Operation(summary = "样机归还选择借试用里样机序列号相关信息查询")
    @PreAuthorize(hasPermission = "crm_sample_probation",dataScope = "crm_sample_probation")
    public JsonObject<List<AdBorrowForProbationProductSnVO>> selectSnListByProcessId(@PathVariable String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        isHavePermission(processInstanceId);
        return new JsonObject<>(adBorrowForProbationProductSnService.selectSnListByProcessId(processInstanceId));
    }

    @PostMapping("/flow/selectDetailsByDDId/{ddId}")
    @Operation(summary = "根据发货详情ID查询发货样机序列号相关信息")
    @PreAuthorize(hasPermission = "crm_sample_probation",dataScope = "crm_sample_probation")
    public JsonObject<AdBorrowForProbationProductSnController.LogisticsInfoVO> selectDetailsByDDId(@PathVariable String ddId) throws Exception {
        AdBorrowForProbationDeliveryDetailVO detailVO = adBorrowForProbationDeliveryDetailService.selectDetailsByDDId(ddId);
        if (detailVO==null) return JsonObject.errorT("物流信息不存在");
        AdBorrowForProbation adBorrowForProbation = adBorrowForProbationService.getBaseMapper().selectById(detailVO.getAdBorrowId());
        isHavePermission(adBorrowForProbation.getProcessInstanceId());
        BatchSubParam batchSubParam=new BatchSubParam();
        batchSubParam.setCompany(detailVO.getCompanyName());
        batchSubParam.setKdybCom(detailVO.getCompanyCode());
        batchSubParam.setNumber(detailVO.getDeliveryNo());
        JsonObject<LogisticsInfo> info = remoteLogisticsClient.getLogisticsInfo(batchSubParam);

        AdBorrowForProbationProductSnController.LogisticsInfoVO logisticsInfoVO=new AdBorrowForProbationProductSnController.LogisticsInfoVO();
        if (info.isSuccess()){
            logisticsInfoVO.setLogisticsInfo(info.getObjEntity());
        }
        logisticsInfoVO.setDetailVO(detailVO);
        return new JsonObject<>(logisticsInfoVO);
    }

    private void isHavePermission(String processInstanceId) {
        AdBorrowForProbation adBorrowForProbation = adBorrowForProbationService.getAdEntityByProcessInstanceId(processInstanceId);
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        if(StringUtils.isBlank(UserInfoHolder.getCurrentAgentId())){
            PreAuthorizeAspect.checkPersonIdDataScope(dataScopeParam,adBorrowForProbation.getCreateUser());
        }else {
            if(adBorrowForProbation.getAgentIdZd().equals(UserInfoHolder.getCurrentAgentId())
                    || adBorrowForProbation.getAgentIdCreate().equals(UserInfoHolder.getCurrentAgentId())){
            }else {
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }
    }

    @PostMapping("/modelMachineProductPage2")
    @Operation(summary = "样机信息")
    @PreAuthorize(hasAnyPermission = {"crm_sample_probation","crm_sample_back"})
    JsonObject<PageUtils<CrmModelMachineProductAgentInventoryVO>> modelMachineProductPage2(@RequestBody CrmModelMachineProductQuery query) {
        return remoteProductAgentInventoryService.modelMachineProductPage2(query);
    }

    @PostMapping("/getModelMachineProductSn")
    @Operation(summary = "可借样机序列号")
    @PreAuthorize(hasAnyPermission = {"crm_sample_probation","crm_sample_back"})
    JsonObject<PageUtils<CrmModelMachineSerialNumberVO>> getModelMachineProductSn(@RequestBody CrmModelMachineProductQuery modelMachineProductQuery) {
        return remoteProductAgentInventoryService.getModelMachineProductSn(modelMachineProductQuery);
    }

    @PostMapping("/getSampleProductSn")
    @Operation(summary = "匹配样机序列号")
    @PreAuthorize(hasAnyPermission = {"crm_sample_probation","crm_sample_back"})
    JsonObject<List<CrmMatchMachineSerialVO>> getSampleProductSn(@RequestBody List<CrmMatchMachineSerialVO> machineSerialNumberVOS) {
        return remoteProductAgentInventoryService.getSampleProductSn(machineSerialNumberVOS);
    }

    @PostMapping(value="/countModelMachineProduct")
    @Operation(summary = "统计总代名称-物料代码集合的可借数量")
    @PreAuthorize(hasAnyPermission = {"crm_sample_probation","crm_sample_back"})
    JsonObject<List<CrmModelMachineProductAgentInventoryVO>> countModelMachineProduct(@RequestBody CrmModelMachineProductQuery query){
        return remoteProductAgentInventoryService.countModelMachineProduct(query);
    }

}
