package com.topsec.crm.flow.core.controller.projectApproval;

import com.topsec.crm.flow.api.dto.projectApproval.ProjectApprovalMilestoneVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ProjectApprovalMilestone;
import com.topsec.crm.flow.core.service.ProjectApprovalMilestoneService;
import com.topsec.crm.flow.core.service.ProjectApprovalService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-07-18  15:22
 * @Description:  立项审批里程碑
 */
@RestController
@RequestMapping("/projectApprovalMilestone")
@Tag(name = "立项审批里程碑", description = "/projectApprovalMilestone")
@RequiredArgsConstructor
@Validated
public class ProjectApprovalMilestoneController extends BaseController {

    private final ProjectApprovalMilestoneService projectApprovalMilestoneService;

    private final ProjectApprovalService projectApprovalService;

    @GetMapping("/deleteById")
    @Operation(summary = "根据id删除")
    @PreFlowPermission
    public JsonObject<Boolean> deleteById(@RequestParam String id) {
        CrmAssert.notNull(id,"id不能为空");
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ProjectApprovalMilestone projectApprovalMilestone = Optional.ofNullable(projectApprovalMilestoneService.getById(id)).orElseThrow(() -> new CrmException(ResultEnum.NULL_OBJ_ENTITY));
        String processInstanceId = projectApprovalService.getById(projectApprovalMilestone.getProjectApprovalId()).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(projectApprovalMilestoneService.deleteById(id)) ;
    }

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "新增或修改")
    @PreFlowPermission
    public JsonObject<Boolean> saveOrUpdate(@RequestBody ProjectApprovalMilestoneVO projectApprovalMilestoneVO) {
        if (StringUtils.isNotBlank(projectApprovalMilestoneVO.getProjectApprovalId())){
            String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
            String processInstanceId = projectApprovalService.getById(projectApprovalMilestoneVO.getProjectApprovalId()).getProcessInstanceId();
            PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        }
        return new JsonObject<>(projectApprovalMilestoneService.saveOrUpdate(projectApprovalMilestoneVO)) ;
    }

    @GetMapping("/selectByProjectApprovalId")
    @Operation(summary = "根据立项审批id查询")
    @PreFlowPermission
    public JsonObject<List<ProjectApprovalMilestoneVO>> selectByProjectApprovalId(@RequestParam String projectApprovalId) {
        CrmAssert.notNull(projectApprovalId,"立项审批id不能为空");
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = projectApprovalService.getById(projectApprovalId).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(projectApprovalMilestoneService.selectByProjectApprovalId(projectApprovalId)) ;
    }

}
