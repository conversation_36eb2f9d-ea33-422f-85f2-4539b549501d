package com.topsec.crm.flow.core.controller.sealManagement.costContractSeal;

import com.topsec.crm.flow.api.dto.sealApplication.SealApplicationFlowLaunchDTO;
import com.topsec.crm.flow.core.process.impl.CostContractSealProcessService;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/costContractSeal")
@Tag(name = "费用合同印鉴申请", description = "/costContractSeal")
@RequiredArgsConstructor
@Validated
public class CostContractSealController {

    private final CostContractSealProcessService costContractSealProcessService;

    @PostMapping("/launch")
    @Operation(summary = "发起费用合同印鉴申请")
    @PreAuthorize(hasAnyPermission = {"crm_cost_contract_add","crm_flow_cost_contract"})
    public JsonObject<Boolean> launch(@Valid @RequestBody SealApplicationFlowLaunchDTO launchDTO) {
        return new JsonObject<>(costContractSealProcessService.launch(launchDTO));
    }
}
