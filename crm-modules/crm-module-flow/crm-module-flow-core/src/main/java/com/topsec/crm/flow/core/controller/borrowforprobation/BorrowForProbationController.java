package com.topsec.crm.flow.core.controller.borrowforprobation;


import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.api.dto.BorrowForProbationDeviceFlowVO;
import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellAttachmentDTO;
import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowForProbationAttachmentDTO;
import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowForProbationFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowForProbationProductDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BorrowForProbation;
import com.topsec.crm.flow.core.process.impl.BorrowForProbationProcessService;
import com.topsec.crm.flow.core.service.BorrowForProbationService;
import com.topsec.crm.flow.core.service.FlowService;
import com.topsec.crm.framework.common.constant.BorrowForProbationConstants;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.product.api.RemoteProductSeparationRelService;
import com.topsec.crm.product.api.entity.CrmProductSeparationRelVo;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient ;
import com.topsec.crm.project.api.dto.BorrowForProbationDevicePageQuery;
import com.topsec.crm.project.api.entity.*;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 借试用信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@RestController
@RequestMapping("/borrowForProbation")
@Tag(name = "借试用流程", description = "/borrowForProbation")
public class BorrowForProbationController extends BaseController {

    @Autowired
    private BorrowForProbationProcessService borrowedForProbationProcessService;

    @Autowired
    private BorrowForProbationService borrowForProbationService;

    @Autowired
    private FlowService flowService;

    @Resource
    private TfsNodeClient tfsNodeClient;

    @Autowired
    private TosEmployeeClient tosEmployeeClient;

    @Resource
    private RemoteProductSeparationRelService remoteProductSeparationRelService;

    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    @PreAuthorize(hasPermission = "crm_flow_device_probation")
    @GetMapping("/flow/dept/situation/{personId}/launch")
    @Operation(summary = "根据用户personId查询部门已生效的借试用情况(流程发起时调用)")
    public JsonObject<DeptBorrowForProbationVO> deptBorrowForProbationSituationOfLaunch(@PathVariable String personId) {
        if(getCurrentPersonId().equals(personId)){
            return new JsonObject<DeptBorrowForProbationVO>(borrowForProbationService.deptBorrowForProbationSituation(personId));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @GetMapping("/flow/dept/situation/{personId}")
    @Operation(summary = "根据用户personId查询部门已生效的借试用情况")
    public JsonObject<DeptBorrowForProbationVO> deptBorrowForProbationSituation(@PathVariable String personId) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        BorrowForProbation borrowForProbation = borrowForProbationService.getOne(new LambdaQueryWrapper<BorrowForProbation>().eq(BorrowForProbation::getProcessInstanceId, processInstanceId));
        if(null != borrowForProbation && borrowForProbation.getPersonId().equals(personId)){
            return new JsonObject<DeptBorrowForProbationVO>(borrowForProbationService.deptBorrowForProbationSituation(personId));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @PostMapping("/flow/dept/deptBorrowForProbationDevicePage")
    @Operation(summary = "查询已生效借试用设备信息列表-借试用")
    JsonObject<PageUtils<CrmBorrowForProbationDeviceVO>> deptBorrowForProbationDevicePage(@RequestBody BorrowForProbationDevicePageQuery borrowForProbationPageQuery) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        BorrowForProbation borrowForProbation = borrowForProbationService.getOne(new LambdaQueryWrapper<BorrowForProbation>().eq(BorrowForProbation::getProcessInstanceId, processInstanceId));
        if(null != borrowForProbation && StringUtils.isNotBlank(borrowForProbation.getPersonId())){
            JsonObject<EmployeeVO> employee = tosEmployeeClient.findById(borrowForProbation.getPersonId());
            if(employee.isSuccess() && null != employee.getObjEntity() && null != employee.getObjEntity().getDept()){
                if(borrowForProbation.getPersonId().equals(borrowForProbationPageQuery.getPersonId()) || employee.getObjEntity().getDept().getUuid().equals(borrowForProbation.getDeptId())){
                    return new JsonObject<PageUtils<CrmBorrowForProbationDeviceVO>>(borrowForProbationService.deptBorrowForProbationDevicePage(borrowForProbationPageQuery));
                }else{
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_device_probation")
    @PostMapping("/launch")
    @Operation(summary = "发起借试用流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody BorrowForProbationFlowLaunchDTO launchDTO) {
        if(getCurrentPersonId().equals(launchDTO.getPersonId())){
            // 填充实际的物料代码数据
            if(CollectionUtils.isNotEmpty(launchDTO.getProductList())){
                JsonObject<CrmProjectDirectlyVo> projectInfo = remoteProjectDirectlyClient.getProjectInfo(launchDTO.getProjectId());
                for (BorrowForProbationProductDTO product : launchDTO.getProductList()){
                    product.setActualStuffCode(product.getStuffCode());
                    // 判断是否需要变更推送生产的物料代码
                    if(projectInfo.isSuccess() && projectInfo.getObjEntity().getSigningType() == 0){
                        JsonObject<CrmProductSeparationRelVo> crmProductSeparationRelDefaultHardware = remoteProductSeparationRelService.getCrmProductSeparationRelDefaultHardware(product.getStuffCode());
                        if(crmProductSeparationRelDefaultHardware.isSuccess() || crmProductSeparationRelDefaultHardware.getResult() == 2){
                            if(null != crmProductSeparationRelDefaultHardware.getObjEntity()){
                                product.setActualStuffCode(crmProductSeparationRelDefaultHardware.getObjEntity().getSeparationStuffCode());
                            }
                        }else{
                            throw new CrmException("获取一体机默认基础型硬件物料代码失败");
                        }
                    }
                }
            }
            return new JsonObject<>(borrowedForProbationProcessService.launch(launchDTO));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @GetMapping("/flow/detail/{processInstanceId}")
    @Operation(summary = "查看借试用流程详情")
    public JsonObject<CrmBorrowForProbationVO> borrowForProbationDetail(@PathVariable String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<CrmBorrowForProbationVO>(borrowForProbationService.borrowForProbationDetail(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/flow/detailCommon/{processInstanceId}")
    @Operation(summary = "查看借试用流程通用信息")
    public JsonObject<CrmBorrowForProbationCommonVO> borrowForProbationDetailCommon(@PathVariable String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<CrmBorrowForProbationCommonVO>(borrowForProbationService.borrowForProbationDetailCommonByProcessInstanceId(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/flow/detail/situation/{personId}")
    @Operation(summary = "根据用户personId查询借试用情况")
    public JsonObject<CrmBorrowForProbationSituationVO> borrowForProbationSituation(@PathVariable String personId,@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        JsonObject<Set<String>> setJsonObject = tfsNodeClient.queryAssigneeAccountIdList(processInstanceId, BorrowForProbationConstants.BORROW_FOR_PROBATION);
        if(setJsonObject.isSuccess() && setJsonObject.getObjEntity().contains(getCurrentAccountId())){
            BorrowForProbation borrowForProbation = borrowForProbationService.getOne(new LambdaQueryWrapper<BorrowForProbation>().eq(BorrowForProbation::getProcessInstanceId, processInstanceId));
            if(null != borrowForProbation && borrowForProbation.getPersonId().equals(personId)){
                return new JsonObject<CrmBorrowForProbationSituationVO>(borrowForProbationService.borrowForProbationSituation(personId,processInstanceId));
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            CrmBorrowForProbationSituationVO build = CrmBorrowForProbationSituationVO.builder().hiden(true).build();
            return new JsonObject<CrmBorrowForProbationSituationVO>(build);
        }
    }

    @PreFlowPermission(hasAnyNodes = {"borrowingTrial_03_1","borrowingTrial_03_2","borrowingTrial_04"})
    @PutMapping("/flow/updateSignCompany")
    @Operation(summary = "修改签订公司")
    public JsonObject<Boolean> updateSignCompany(@RequestParam String processInstanceId, @RequestParam String newSignCompany) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<Boolean>(borrowForProbationService.updateSignCompany(processInstanceId,newSignCompany));
    }

    @PreAuthorize(hasPermission = "crm_device_probation")
    @GetMapping("/flow/collectBorrowForProbationDeviceFlow")
    @Operation(summary = "查询借试用产品借用进展")
    public JsonObject<List<BorrowForProbationDeviceFlowVO>> collectBorrowForProbationDeviceFlow(@RequestParam String deviceId) {
        return new JsonObject<>(flowService.collectBorrowForProbationDeviceFlow(deviceId));
    }

    @PreFlowPermission
    @PostMapping("/saveBorrowForProbationAttachment")
    @Operation(summary = "保存借试用流程附件信息")
    public JsonObject<Boolean> saveBorrowForProbationAttachment(@RequestParam String processInstanceId, @RequestBody List<BorrowForProbationAttachmentDTO> attachments) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(borrowForProbationService.saveBorrowForProbationAttachment(processInstanceId,attachments));
    }
}
