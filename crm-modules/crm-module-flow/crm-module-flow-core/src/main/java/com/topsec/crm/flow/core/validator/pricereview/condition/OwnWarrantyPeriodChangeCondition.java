package com.topsec.crm.flow.core.validator.pricereview.condition;

import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.core.validator.CheckCondition;
import com.topsec.crm.flow.core.validator.pricereview.PriceReviewCheckContext;
import com.topsec.crm.flow.core.validator.pricereview.ProductCompareUtil;
import org.apache.commons.collections4.ListUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 自有产品 已审保修期大于等于新保修期价审不失效,已审保修期小于新保修期价审失效

 * 首先排除掉没有保修期的产品
 * 按照物料代码分组比较数量
 * 展开按保修期排序，从索引0开始依次比较大小，只要存在项目中较大的就失效
 * <AUTHOR>
 */
public class OwnWarrantyPeriodChangeCondition implements CheckCondition<PriceReviewCheckContext> {
    @Override
    public boolean check(PriceReviewCheckContext context)  {

        List<PriceReviewProductOwnDTO> currentOwnList = ListUtils.emptyIfNull(context.getProjectDetail().getProductOwnList());
        List<PriceReviewProductOwnDTO> snapshotOwnList = ListUtils.emptyIfNull(context.getProjectSnapshot().getProductOwnList());
        return ProductCompareUtil.validate(currentOwnList, snapshotOwnList,"productPeriod",(currentCompareMap, snapshotCompareMap) -> {
            Integer currentCount = currentCompareMap.values().stream().mapToInt(Integer::intValue).sum();
            Integer snapshotCount = snapshotCompareMap.values().stream().mapToInt(Integer::intValue).sum();

            if(!currentCount.equals(snapshotCount)) return false;

            List<Integer> currentExpandedList = expandAndSortMap(currentCompareMap);
            List<Integer> snapshotExpandedList = expandAndSortMap(snapshotCompareMap);

            return compareLists(currentExpandedList, snapshotExpandedList);
        });
    }
    private static List<Integer> expandAndSortMap(Map<Object, Integer> map) {
        // 对列表进行排序
        return map.entrySet().stream()
                .flatMapToInt(entry -> IntStream.generate(() -> {
                    Object key = entry.getKey();
                    return (Integer) key;
                }).limit(entry.getValue()))
                .boxed().sorted().collect(Collectors.toList());
    }

    private boolean compareLists(List<Integer> current, List<Integer> snapshot) {
        for (int i = 0; i < current.size(); i++) {
            Integer c = current.get(i);
            Integer s = snapshot.get(i);
            if (c>s) return false;
        }
        return true;
    }



    @Override
    public String defaultFailureReason() {
        return "新保修期大于已审保修期";
    }
}
