package com.topsec.crm.flow.core.controller.contractinvoice;

import com.github.pagehelper.PageHelper;
import com.topsec.crm.contract.api.ContractProductDetailQuery;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.CrmContractProductAllDetailVO;
import com.topsec.crm.contract.api.entity.CrmRevenueRecognitionVO;
import com.topsec.crm.flow.api.RemoteContractSignVerifyMainService;
import com.topsec.crm.flow.api.RemoteContractUnconfirmedDetailService;
import com.topsec.crm.flow.api.dto.contractInvoice.ContractInfoDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.ContractProductDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.ContractMakeInvoiceDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.ContractMakeInvoiceFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.MakeInvoiceProductDTO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractSignVerifyAttachmentDTO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedDetailVo;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ContractMakeInvoice;
import com.topsec.crm.flow.core.process.impl.ContractMakeInvoiceProcessService;
import com.topsec.crm.flow.core.service.ContractInvoiceExtendService;
import com.topsec.crm.flow.core.service.ContractMakeInvoiceProductService;
import com.topsec.crm.flow.core.service.ContractMakeInvoiceService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/contractMakeInvoice")
@Tag(name = "【合同开票】-流程",description = "/contractMakeInvoice")
public class ContractMakeInvoiceController extends BaseController {

    private final ContractMakeInvoiceProcessService processService;
    private final ContractMakeInvoiceService makeInvoiceService;
    private final RemoteContractExecuteService remoteContractExecuteService;
    private final RemoteContractSignVerifyMainService remoteContractSignVerifyMainService;
    private final ContractInvoiceExtendService contractInvoiceExtendService;
    private final ContractMakeInvoiceProductService makeInvoiceProductService;

    @PostMapping("/launch")
    @Operation(summary = "发起合同开票流程")
    @PreAuthorize(hasPermission = "crm_contract_execute_invoice_apply")
    public JsonObject<Boolean> launch(@RequestBody @Validated ContractMakeInvoiceFlowLaunchDTO req){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(req.getContractMakeInvoiceDTO().getContractNumber(), UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        req.getContractMakeInvoiceDTO().setInvoiceSource(ContractMakeInvoiceDTO.InvoiceSourceEnum.CONTRACT_SINGLE.getCode());
        return new JsonObject<>(processService.launch(req));
    }

    @PostMapping("/updateProcess02")
    @Operation(summary = "更新合同开票流程信息")
    @PreFlowPermission(hasAnyNodes = {"sid-EED295B5-C6A6-4C04-A8A9-409542314C3B"})
    public JsonObject<Boolean> updateProcess02(@RequestBody ContractMakeInvoiceFlowLaunchDTO req){
        CrmAssert.hasText(req.getProcessInstanceId(),"流程实例ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(req.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(makeInvoiceService.updateInfoAndProduct(req));
    }

    @GetMapping("/getLaunchInfo")
    @Operation(summary = "获取合同开票流程信息",parameters = {@Parameter(name = "processInstanceId",description = "流程实例ID")})
    @PreFlowPermission
    public JsonObject<ContractMakeInvoiceDTO> getProcessInfo(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程实例ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(makeInvoiceService.getProcessInfo(processInstanceId));
    }

    @GetMapping("/getMakeInvoiceProduct")
    @Operation(summary = "获取可开票产品列表",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_execute_invoice_apply")
    public JsonObject<TableDataInfo> getMakeInvoiceProduct(@RequestParam String contractNumber,@RequestParam(required = false) String materialCode){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        startPage();
        TableDataInfo makeInvoiceProduct = contractInvoiceExtendService.getMakeInvoiceProduct(contractNumber,materialCode);
        return new JsonObject<>(makeInvoiceProduct);
    }

    @GetMapping("/validateContractInvoiceRule")
    @Operation(summary = "校验合同开票流程发起规则",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_execute_invoice_apply")
    public JsonObject<Boolean> validateContractInvoiceRule(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        Boolean validateContractInvoiceRule = makeInvoiceService.validateContractInvoiceRule(contractNumber);
        return new JsonObject<>(validateContractInvoiceRule);
    }

    @GetMapping("/exportInvoiceProduct")
    @Operation(summary = "导出合同开票产品",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_execute_invoice_apply")
    public void exportInvoiceProduct(@RequestParam String contractNumber) throws IOException{
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        ExcelUtil<MakeInvoiceProductDTO> excelUtil = new ExcelUtil<>(MakeInvoiceProductDTO.class);
        List<MakeInvoiceProductDTO> makeInvoiceProductList = contractInvoiceExtendService.getMakeInvoiceProductList(contractNumber);
        excelUtil.exportExcel(response, makeInvoiceProductList,"合同开票产品");
    }

    @PostMapping("/importInvoiceProduct")
    @Operation(summary = "导入合同开票产品")
    @PreAuthorize(hasPermission = "crm_contract_execute_invoice_apply")
    public JsonObject<List<MakeInvoiceProductDTO>> importInvoiceProduct(@RequestParam MultipartFile file){
        List<MakeInvoiceProductDTO> productDTOList = makeInvoiceProductService.importInvoiceProduct(file);
        return new JsonObject<>(productDTOList);
    }

    @GetMapping("/initMakeInvoiceInfo")
    @Operation(summary = "获取合同签订公司对应的工商信息",
            description = "初始化开票信息",
            parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "19520250300001")})
    @PreAuthorize(hasPermission = "crm_contract_execute_invoice_apply")
    public JsonObject<ContractMakeInvoiceDTO> initMakeInvoiceInfo(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(makeInvoiceService.getCustomerInfo(contractNumber));
    }

    @GetMapping("/getContractInfo")
    @Operation(summary = "获取合同信息-详情",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreFlowPermission
    public JsonObject<ContractInfoDTO> getContractInfo(@RequestParam String contractNumber){
        return new JsonObject<>(contractInvoiceExtendService.getContractInfo(contractNumber));
    }

    @GetMapping("/getRevenueRecognition")
    @Operation(summary = "获取收入确认列表-详情",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreFlowPermission
    public JsonObject<PageUtils<CrmRevenueRecognitionVO>> getRevenueRecognition(@RequestParam String contractNumber){
        PageUtils<CrmRevenueRecognitionVO> objEntity = remoteContractExecuteService.pageRevenueRecognitionByContractNumber(contractNumber).getObjEntity();
        return new JsonObject<>(objEntity);
    }

    @GetMapping("/getPaymentTerms")
    @Operation(summary = "获取付款条款-详情",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreFlowPermission
    public JsonObject<List<PaymentProvisionDTO>> getPaymentTerms(@RequestParam String contractNumber){
        List<PaymentProvisionDTO> list = remoteContractExecuteService.pagePaymentProvisionByContractNumber(contractNumber).getObjEntity().getList();
        return new JsonObject<>(Objects.requireNonNullElseGet(list, ArrayList::new));
    }

    @GetMapping("/getAttach")
    @Operation(summary = "获取签验收单据-详情",description ="附件",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreFlowPermission
    public JsonObject<List<ContractSignVerifyAttachmentDTO>> getAttach(@RequestParam String contractNumber){
        List<ContractSignVerifyAttachmentDTO> list = remoteContractSignVerifyMainService.getAttachAfter01(List.of(contractNumber)).getObjEntity().get(contractNumber);
        return new JsonObject<>(Objects.requireNonNullElseGet(list, ArrayList::new));
    }

    @GetMapping("/getContractProcessPage")
    @Operation(summary = "发票详情-分页列表-详情")
    @PreFlowPermission
    public JsonObject<TableDataInfo> getContractProcessPage(@RequestParam String contractNumber){
        startPage();
        return new JsonObject<>(contractInvoiceExtendService.getInvoiceProcessPage(contractNumber, PageHelper.getLocalPage().getPageSize(), PageHelper.getLocalPage().getPageNum()));
    }

    @PostMapping("/getContractProductList")
    @Operation(summary = "合同产品-详情",description = "合同中的所有产品行签验收情况")
    @PreFlowPermission
    public JsonObject<List<ContractProductDTO>> getContractProductList(@RequestBody ContractProductDetailQuery query){
        query.setNeedGrossMargin(true);
        query.setNeedSpecialCode(true);
        query.setNeedSn(true);
        List<CrmContractProductAllDetailVO> entity = remoteContractExecuteService.queryContractProductDetailByCondition(query).getObjEntity();
        List<ContractProductDTO> contractProductDTOS = HyperBeanUtils.copyListProperties(entity, ContractProductDTO::new);
        return new JsonObject<>(contractProductDTOS);
    }

}
