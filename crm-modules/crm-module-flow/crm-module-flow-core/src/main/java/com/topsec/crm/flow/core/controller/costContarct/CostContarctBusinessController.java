package com.topsec.crm.flow.core.controller.costContarct;

import com.topsec.crm.flow.api.dto.costContarct.*;
import com.topsec.crm.flow.api.dto.costContarct.VO.ArriveInfoVO;
import com.topsec.crm.flow.api.dto.costContarct.VO.CostContractVo;
import com.topsec.crm.flow.api.dto.costFiling.CostFilingDetailInfo;
import com.topsec.crm.flow.api.dto.costFiling.CostFilingInfo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CostContract;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractSignCompanyVO;
import com.topsec.crm.project.api.dto.ProjectDirectlyPersonIdPageQuery;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;

import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.vo.TosDepartmentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@RestController
@RequestMapping("/business/costContract")
@Tag(name = "【费用合同-业务接口】", description = "costContract")
@RequiredArgsConstructor
@Validated
public class CostContarctBusinessController extends BaseController {

    private final CostContractService costContractService;

    private final CostContractReService costContractReService;

    private final ICostFilingDetailService iCostFilingDetailService;

    private final ICostFilingService iCostFilingService;

    private final CostContractPaymentClauseService costContractPaymentClauseService;

    private final CostContractAttachmentService costContractAttachmentService;

    @PreAuthorize(hasPermission="crm_cost_contract_apply_list")
    @PostMapping("/filingDetailConvertContractRe")
    @Operation(summary = "根据选择费用备案记录返回费用合同记录")
    public JsonObject<List<CostContractReInfoDTO>> filingDetailConvertContractRe(@RequestBody List<CostFilingDetailDTO> costFilingDetailDTOS){
        return new JsonObject<>(costContractReService.filingDetailConvertContractRe(costFilingDetailDTOS));
    }

    @PreAuthorize(hasPermission="crm_cost_contract_add")
    @PostMapping("/updateCostContractReInfo")
    @Operation(summary = "修改税率后调整费用合同明细可申请金额")
    public JsonObject<List<CostContractReInfoDTO>> updateCostContractReInfo(@RequestBody List<CostContractReInfoDTO> reInfoDTOS){
        return new JsonObject<>(costContractReService.updateCostContractReInfo(reInfoDTOS));
    }

    @PreAuthorize(hasPermission="crm_cost_contract_apply_list",dataScope="crm_cost_contract_apply_list")
    @PostMapping("/page")
    @Operation(summary = "分页查询费用合同")
    public JsonObject<TableDataInfo> launch(@RequestBody CostContractVo contarctVo){
        startPage();
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdList = dataScopeParam != null ? dataScopeParam.getPersonIdList(): Collections.EMPTY_SET;
        return new JsonObject<>(costContractService.selectCostContractVO(contarctVo,personIdList));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @GetMapping("/selectCostContractById")
    @Operation(summary = "根据流程ID查询费用合同详情")
    public JsonObject<CostContractInfoDTO> selectCostContractById(@RequestParam String processInstanceId){
        return new JsonObject<>(costContractService.selectCostContractByProcessInstanceId(processInstanceId));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @PostMapping("/getCostFilingByPersonnelId")
    @Operation(summary = "根据创建人员查询费用备案信息")
    public JsonObject<TableDataInfo> getCostFilingByPersonnelId(@RequestBody CostFilingInfo costFilingInfo){
        CrmAssert.hasText(costFilingInfo.getCreateUser(),"创建人ID不能为空");
        startPage();
        return new JsonObject<>(iCostFilingService.getCostFilingByPersonnelId(costFilingInfo));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @PostMapping("/selectDetailByCInfo")
    @Operation(summary = "分页选择费用备案明细")
    public JsonObject<TableDataInfo> selectDetailByCInfo(@RequestBody CostFilingDetailInfo costFilingDetailInfo){
        return new JsonObject<>(iCostFilingDetailService.selectDetailByCInfo(costFilingDetailInfo));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @GetMapping("/selectCostContractReByCostContarctId")
    @Operation(summary = "根据主表ID分页查询记录")
    public JsonObject<List<CostContractReInfoDTO>> selectCostContractReByCostContarctId(@RequestParam String costContarctId){
        return new JsonObject<>(costContractReService.selectCostContractReByCostContarctId(costContarctId));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @GetMapping("/selectPaymentClauseByCostContractId")
    @Operation(summary = "根据主表ID分页查询付款条款")
    public JsonObject<List<CostContractPaymentClauseInfoDTO>> selectPaymentClauseByCostContractId(@RequestParam String costContarctId){
        return new JsonObject<>(costContractPaymentClauseService.selectPaymentClauseByCostContractId(costContarctId));
    }

    @PreAuthorize(hasPermission="crm_cost_contract_add")
    @GetMapping("/deletePaymentClause")
    @Operation(summary = "根据ID删除付款条款信息")
    public JsonObject<Boolean> deletePaymentClause(@RequestParam String id){
        return new JsonObject<>(costContractPaymentClauseService.deletePaymentClause(id));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @GetMapping("/getArriveInfoVOInfo")
    @Operation(summary = "根据ID查询送达信息")
    public JsonObject<ArriveInfoVO> getArriveInfoVOInfo(@RequestParam String costContarctId){
        return new JsonObject<>(costContractAttachmentService.getArriveInfoVOInfo(costContarctId));
    }

    @PreAuthorize(hasPermission = "crm_cost_contract_add")
    @PostMapping("/saveAttachment")
    @Operation(summary = "新增费用合同附件")
    public JsonObject<Boolean> saveAttachment(@RequestBody CostContractAttachmentInfoDTO attachmentInfoDTO){
        CrmAssert.hasText(attachmentInfoDTO.getCostId(),"主表ID不能为空");
        attachmentInfoDTO.setCostType("费用备案");
        return new JsonObject<>(costContractAttachmentService.saveAttachment(attachmentInfoDTO));
    }

    @PreAuthorize(hasPermission = "crm_cost_contract_add")
    @GetMapping("/deleteAttachment")
    @Operation(summary = "删除费用合同附件信息")
    public JsonObject<Boolean> deleteAttachment(@RequestParam String id){
        return new JsonObject<>(costContractAttachmentService.deleteAttachment(id));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @GetMapping("/getByCostContractId")
    @Operation(summary = "查询费用合同附件信息")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getByCostContractId(@RequestParam String costId,@RequestParam(required = false) String isFinalQuery){
        return new JsonObject<>(costContractAttachmentService.getByCostContractId(costId,isFinalQuery));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @PostMapping("/costContractSignCompanyPage")
    @Operation(summary = "费用合同选择签订公司")
    public JsonObject<List<ContractSignCompanyVO>> costContractSignCompanyPage(@RequestBody ContractSignCompanyVO contractSignCompanyVO){
        return new JsonObject<>(costContractService.costContractSignCompanyPage(contractSignCompanyVO));
    }

}
