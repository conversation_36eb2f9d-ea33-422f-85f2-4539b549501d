package com.topsec.crm.flow.core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.flow.api.vo.*;
import com.topsec.crm.flow.core.entity.ThreeProcurementReviewMain;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractSignCompanyVO;
import com.topsec.crm.operation.api.entity.SupplierVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 第三方采购Service接口
 *
 * @date 2024-09-02
 */
public interface ThreeProcurementReviewMainService extends IService<ThreeProcurementReviewMain>
{

    /**
     * 根据流程实例查询第三方采购
     * @param processInstanceId
     * @return
     */
    public ThreeProcurementReviewBaseInfoVo selectThreeProcurementReviewBaseInfoVoByProcessInstanceId(String processInstanceId);

    public  ThreeProcurementReviewMain selectThreeProcurementReviewMainByProcessInstanceId(String processInstanceId);


    public  List<ThreeProcurementReviewMain> selectThreeProcurementReviewMainByProcessInstanceIdList(List<String> processInstanceIds);


    List<ThreeProcurementReviewMain> selectThreeProcurementReviewMainByParentProcessInstanceId(String parentProcessInstanceId);

    ThreeProcurementReviewMainVo selectThreeProcurementReviewMainByPurchaseNumber(String purchaseNumber);

    /**
     * 查询合同下发起的采购流程
     * @param contractId
     * @return
     */
    List<ThreeProcurementReviewMain> selectThreeProcurementReviewMainContractId(String contractId);

    /**
     * 分页查询第三方采购
     * @param threeProcurementReviewQuery
     * @return
     */
    Page<ThreeProcurementReviewMain> pageThreeProcurementReview(ThreeProcurementReviewQuery threeProcurementReviewQuery);


    /**
     * 第三方采购审批列表
     * @param threeProcurementReviewQuery
     * @return
     */
    Page<ThreeProcurementReviewMain> pageThreeProcurementApprove(ThreeProcurementReviewQuery threeProcurementReviewQuery);



    /**
     * 修改第三方采购
     * 
     * @param threeProcurementReviewBaseInfoVo 第三方采购
     * @return 结果
     */
    public boolean updateThreeProcurement(ThreeProcurementReviewBaseInfoVo threeProcurementReviewBaseInfoVo);


    public int uploadThreeProcurementReviewMain(ThreeProcurementReviewMain threeProcurementReviewMain);

    /**
     * 批量删除第三方采购
     * 
     * @param ids 需要删除的第三方采购ID
     * @return 结果
     */
    public int deleteThreeProcurementReviewMainByIds(String[] ids);


    /**
     * 获取第三方采购产品
     * @param contractId
     * @param supplierId
     * @return
     */
    ThreeProcurementProductInfoVo queryThirdProductInfo(String contractId, String supplierId,boolean returnAndExchange);

    /**
     * 获取自有产品
     * @param contractId
     * @param personId
     * @return
     */
    List<ThreeProcurementProductVo>  queryOwnProductList(String contractId, String personId,boolean returnAndExchange);

    /**
     * 关联Po号
     * @param associatedPoVO
     * @return
     */
    boolean updatePo(ThreeProcurementAssociatedPoVo associatedPoVO);


    /**
     * 批量关联物料代码
     */
    public boolean batchUpdateMaterialCode(List<ThreeProcurementStuffCodeVo> threeProcurementStuffCodeVoList);

    public IPage<ThreeProcurementReviewMainVo> getThreeProcurementReviewMainVoIPage(ThreeProcurementReviewQuery threeProcurementReviewQuery);
    /**
     * 采购总价
     * @param processInstanceId
     * @return
     */
    BigDecimal selectTotalPurchasePrice(String processInstanceId);

    Map<String, BigDecimal> selectTotalPurchasePriceList(List<String> processInstanceIds);

    public SupplierVO getSupplierVO(String supplierId);

    public Map<String, SupplierVO> getSupplierVOList(List<String> supplierIds);

    public ContractSignCompanyVO getContractSignCompanyVO(String signCompany);

    public Map<String, ContractSignCompanyVO> getContractSignCompanyVOList(List<String> signCompany);


    IPage<ThreeProcurementReviewMainVo>  convertThreeProcurementApprove(Page<ThreeProcurementReviewMain> threeProcurementReviewMainPage);


    void  findThreeProcurementProductStatus(String processInstanceId,List<ThreeProcurementProductVo> threeProcurementProductVoList);
}
