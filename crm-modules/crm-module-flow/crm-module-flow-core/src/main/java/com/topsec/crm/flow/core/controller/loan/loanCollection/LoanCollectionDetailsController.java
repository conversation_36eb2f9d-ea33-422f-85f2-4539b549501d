package com.topsec.crm.flow.core.controller.loan.loanCollection;

import com.topsec.crm.flow.api.dto.loan.loanCollection.LoanCollectionMainQueryPageDTO;
import com.topsec.crm.flow.api.dto.loan.loanCollection.vo.LoanCollectionDetailsVO;
import com.topsec.crm.flow.core.service.ILoanCollectionDetailsService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.page.CrmPageQuery;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Set;

/**
 *  前端控制器
 *
 * <AUTHOR>
 * @since 2025-07-28 18:58
 */
@RestController
@RequestMapping("/loan-collection-details")
public class LoanCollectionDetailsController extends CrmPageQuery {
    @Resource
    ILoanCollectionDetailsService iLoanCollectionDetailsService;
    @PostMapping("/Page")
    @Operation(summary = "借款催记录分页",description = "借款申请")
    @PreAuthorize(hasPermission = "crm_loan_demand",dataScope ="crm_loan_demand" )
    public JsonObject<PageUtils<LoanCollectionDetailsVO>> Page(@RequestBody LoanCollectionMainQueryPageDTO loanCollectionMainQueryPageDTO){
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdsOfPermission = dataScopeParam != null ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;
        return new JsonObject<>(iLoanCollectionDetailsService.Page(loanCollectionMainQueryPageDTO,personIdsOfPermission));
    }
}
