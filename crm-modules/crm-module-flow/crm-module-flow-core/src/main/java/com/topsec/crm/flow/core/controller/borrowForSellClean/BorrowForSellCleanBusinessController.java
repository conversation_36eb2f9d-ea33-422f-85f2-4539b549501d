package com.topsec.crm.flow.core.controller.borrowForSellClean;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.util.WebFilenameUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.api.dto.borrowForSellClean.*;
import com.topsec.crm.flow.core.entity.BorrowForSellClean;
import com.topsec.crm.flow.core.process.impl.BorrowForSellCleanProcessService;
import com.topsec.crm.flow.core.service.BorrowForSellCleanDeviceService;
import com.topsec.crm.flow.core.service.BorrowForSellCleanService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2025-03-04  13:34
 * @Description:
 */

@RestController
@RequestMapping("/business/borrowForSellClean")
@Tag(name = "借转销催办业务", description = "/borrowForSellClean")
@RequiredArgsConstructor
@Validated
public class BorrowForSellCleanBusinessController extends BaseController {

    private final BorrowForSellCleanService borrowForSellCleanService;

    private final BorrowForSellCleanProcessService borrowForSellCleanProcessService;
    private final BorrowForSellCleanDeviceService borrowForSellCleanDeviceService;





    //@PreAuthorize
    @PostMapping("/cleanPage")
    @Operation(summary = "借转销清理列表分页查询")
    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_clean",dataScope = "crm_flow_borrow_for_sell_clean")
    public JsonObject<PageUtils<BorrowForSellCleanDTO>> borrowForSellCleanPage(@RequestBody BorrowForSellCleanQuery query) {
        return new JsonObject<>(borrowForSellCleanService.borrowForSellCleanPage(query));
    }


    //@PreAuthorize
    @PostMapping("/feedbackPage")
    @Operation(summary = "借转销催办清理反馈列表分页查询")
    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_clean",dataScope = "crm_flow_borrow_for_sell_clean")
    public JsonObject<PageUtils<BorrowForSellCleanDeviceDTO>> borrowForSellCleanFeedbackPage(@RequestBody BorrowForSellCleanQuery query) {
        return new JsonObject<>(borrowForSellCleanService.borrowForSellCleanFeedbackPage(query));
    }


    @GetMapping("/detail/{processInstanceId}")
    @Operation(summary = "查看借转销催办流程详情")
    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_clean",dataScope = "crm_flow_borrow_for_sell_clean")
    public JsonObject<BorrowForSellCleanLaunchDTO> borrowForSellCleanDetail(@PathVariable String processInstanceId) {
        BorrowForSellClean one = borrowForSellCleanService.getOne(new LambdaQueryWrapper<BorrowForSellClean>().eq(BorrowForSellClean::getProcessInstanceId, processInstanceId).eq(BorrowForSellClean::getDelFlag, 0));
        if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList()) || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(one.getSalesmanPersonId()) ){
            return new JsonObject<>(borrowForSellCleanService.borrowForSellCleanDetail(processInstanceId));
        }else {
            return new JsonObject(false);
        }

    }



    @PostMapping("/export")
    @Operation(summary = "导出借转销清理反馈")
    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_clean_export",dataScope = "crm_flow_borrow_for_sell_clean_export")
    public void export(@RequestBody BorrowForSellCleanQuery query, HttpServletResponse response)   {
        query.setPageNum(1);
        query.setPageSize(Integer.MAX_VALUE);
        PageUtils<BorrowForSellCleanDeviceDTO> page = borrowForSellCleanService.borrowForSellCleanFeedbackPage(query);
        List<BorrowForSellCleanDeviceDTO> BorrowForSellCleanDeviceDTOs = page.getList();
        List<BorrowForSellCleanDeviceExportDTO> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(BorrowForSellCleanDeviceDTOs)){
            list = BorrowForSellCleanDeviceDTOs.stream().map(borrowForSellCleanDeviceDTO -> {
                BorrowForSellCleanDeviceExportDTO borrowForSellCleanDeviceExportDTO = HyperBeanUtils.copyPropertiesByJackson(borrowForSellCleanDeviceDTO, BorrowForSellCleanDeviceExportDTO.class);
                BorrowForSellClean byId = borrowForSellCleanService.getById(borrowForSellCleanDeviceDTO.getBorrowForSellCleanId());
                borrowForSellCleanDeviceExportDTO.setSigningType(borrowForSellCleanDeviceDTO.getSigningType() == 1 ? "直签" : "非直签");
                borrowForSellCleanDeviceExportDTO.setSalesman(byId.getSalesman());
                borrowForSellCleanDeviceExportDTO.setSalesmanDept(byId.getSalesmanDept());
                return borrowForSellCleanDeviceExportDTO;
            }).collect(Collectors.toList());

            ExportParams exportParams=new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setSheetName("借转销清理反馈");
            Map<String,Object> sheet1Map = new HashMap<>();
            sheet1Map.put("title",exportParams);
            sheet1Map.put("data", list);
            sheet1Map.put("entity", BorrowForSellCleanDeviceExportDTO.class);
            List<Map<String, Object>> sheets = new ArrayList<>();
            sheets.add(sheet1Map);
            try (Workbook workbook = ExcelExportUtil.exportExcel(sheets, ExcelType.XSSF)) {
                response.addHeader(HttpHeaders.CONTENT_DISPOSITION, WebFilenameUtils.disposition("借转销清理反馈.xlsx"));
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
                workbook.write(response.getOutputStream());
            }catch (Exception e){
                throw new CrmException("导出失败", ResultEnum.FAIL.getResult(), e);
            }
        }
    }


}