package com.topsec.crm.flow.core.controller.customer;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.topsec.crm.customer.api.RemoteCustomerAnotherService;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerAnotherVo;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.dto.customer.CustomerAnotherNameApproveFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.customer.CustomerAnotherNameMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BorrowForCompensate;
import com.topsec.crm.flow.core.entity.CustomerAnotherNameMain;
import com.topsec.crm.flow.core.entity.CustomerAuthMain;
import com.topsec.crm.flow.core.process.impl.CustomerAnotherNameProcessService;
import com.topsec.crm.flow.core.service.ICustomerAnotherNameMainService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.operation.api.RemoteIndustryService;
import com.topsec.crm.operation.api.entity.CrmIndustryVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbsapi.client.TbsPersonClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/customerAnotherNameMain")
@Tag(name = "客户别名审批相关Controller", description = "/customerAnotherNameMain/flow")
@RequiredArgsConstructor
@Validated
public class CustomerAnotherNameMainController extends BaseController {
    @Autowired
    private ICustomerAnotherNameMainService customerAnotherNameMainService;
    @Autowired
    private CustomerAnotherNameProcessService customerAnotherNameProcessService;
    @Autowired
    private RemoteCustomerService remoteCustomerService;
    @Autowired
    private RemoteCustomerAnotherService remoteCustomerAnotherService;
    @Autowired
    private TbsPersonClient tbsPersonClient;
    @Autowired
    private RemoteIndustryService remoteIndustryService;

    @PostMapping("/launch")
    @Operation(summary = "发起客户别名审批流程")
    @PreAuthorize(hasAnyPermission = {"crm_customer_query","crm_customer_query_agent"})
//    @Audit(eventName = "launch" ,eventDesc = "发起客户认证审批流程", eventType = "NA客户申请",getMethodData = true)
    public JsonObject<Boolean> launch(@Valid @RequestBody CustomerAnotherNameApproveFlowLaunchDTO launchDTO) {
        //查询客户信息
        Assert.isFalse(CollectionUtil.isEmpty(launchDTO.getNewNames()), "别名列表不能为空");
        //查询客户信息
        JsonObject<CrmCustomerVo> cObj = remoteCustomerService.getCustomerInfo(launchDTO.getCustomerId(), UserInfoHolder.getCurrentPersonId(),false);
        Assert.isFalse(!cObj.isSuccess() || cObj.getObjEntity() == null, "客户信息不存在。");

        //客户别名重名校验
        List<String> names = launchDTO.getNewNames();
        List<String> sysNames = new ArrayList<String>();
        List<String> sysAnotherNames = new ArrayList<String>();
        List<String> flowNames = new ArrayList<String>();
        if(CollectionUtil.isNotEmpty(names)){
            for (int i=0;i<names.size();++i) {
                String newName = names.get(i);
                Assert.isFalse(newName.equals(cObj.getObjEntity().getName()), "别名不可与公司名称相同。");

                for (int j = i+1; j< names.size(); ++j) {
                    if(newName.equals(names.get(j))){
                        Assert.isFalse(true, "该客户存在相同别名，请重新编辑。");
                    }
                }

                //查询系统中是否有重名客户
                JsonObject<CrmCustomerVo> bn = remoteCustomerService.findByName(newName);
                if(bn.isSuccess() && bn.getObjEntity() != null){
                    sysNames.add(newName);
                }

                //查询系统中是否有重名的别名（已生效）
                JsonObject<List<CrmCustomerAnotherVo>> byName1 = remoteCustomerAnotherService.findByName(newName);
                if(byName1.isSuccess() && byName1.getObjEntity() != null && CollectionUtil.isNotEmpty(byName1.getObjEntity())){
                    sysAnotherNames.add(newName);
                }

                //查询系统中是否有重名的别名（流程中）
                List<CustomerAnotherNameMain> existFlows = customerAnotherNameMainService.selectFlowName(newName);
                if(CollectionUtil.isNotEmpty(existFlows)){
                    flowNames.add(newName);
                }
            }
        }
        if(CollectionUtil.isNotEmpty(sysNames)){
            String collect = sysNames.stream().map(String::valueOf).collect(Collectors.joining(","));
            Assert.isFalse(true, "系统中存在相同名称的客户名称:"+collect);
        }
        if(CollectionUtil.isNotEmpty(sysAnotherNames)){
            String collect = sysAnotherNames.stream().map(String::valueOf).collect(Collectors.joining(","));
            Assert.isFalse(true, "系统中存在相同名称的客户别名:"+collect);
        }
        if(CollectionUtil.isNotEmpty(flowNames)){
            String collect = flowNames.stream().map(String::valueOf).collect(Collectors.joining(","));
            Assert.isFalse(true, "审批流程中存在同名的客户别名:"+collect);
        }

        //发起别名审批流程
        customerAnotherNameProcessService.launch(launchDTO);

        return new JsonObject<>(true);
    }

    /**
     * 客户别名审批记录列表
     */
    @PostMapping("/selectPage")
    @Operation(summary = "客户别名审批记录")
    @PreAuthorize(hasPermission = "crm_customer_admin_alias_approve_list")
    public JsonObject<PageUtils<CustomerAnotherNameMainVo>> selectPage(@RequestBody CustomerAnotherNameMainVo customerAnotherNameMainVo) {
        CrmCustomerVo customerVo = HyperBeanUtils.copyPropertiesByJackson(customerAnotherNameMainVo.getCustomerVo(),CrmCustomerVo.class);

        String nameForSearch;
        if(StringUtils.isNotBlank(customerVo.getName())){
            nameForSearch = StringUtils.replaceBlank(customerVo.getName())
                    .replaceAll("（","(")
                    .replaceAll("）",")");
        } else {
            nameForSearch = "";
        }

        startPage();
        List<CustomerAnotherNameMain> list = customerAnotherNameMainService.query()
                .eq(StringUtils.isNotNull(customerAnotherNameMainVo.getProcessState()),"process_state", customerAnotherNameMainVo.getProcessState())
                .eq(StringUtils.isNotNull(customerVo.getDataSource()),"data_source", customerVo.getDataSource())
                .in(CollectionUtil.isNotEmpty(customerVo.getIndustries()),"industry_id_two", customerVo.getIndustries())
                .and(StringUtils.isNotBlank(nameForSearch), wrapper -> {
                    wrapper.like("name_for_search", nameForSearch)
                            .or().apply("JSON_CONTAINS(old_names,CONCAT('\"',{0},'\"'))", customerVo.getName())
                            .or().apply("JSON_CONTAINS(new_names,CONCAT('\"',{0},'\"'))", customerVo.getName());
                }).orderByDesc("create_time")
                .list();

        List<CustomerAnotherNameMainVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, CustomerAnotherNameMainVo.class);
        if(CollectionUtil.isNotEmpty(listVo)){
            List<String> customerIds = listVo.stream().map(CustomerAnotherNameMainVo::getCustomerId).toList();
            JsonObject<Map<String,CrmCustomerVo>> cObj = remoteCustomerService.batchGetCustomerInfo(customerIds,false,false);
            if(cObj != null && CollectionUtil.isNotEmpty(cObj.getObjEntity())){
                Map<String,CrmCustomerVo> customerVos = cObj.getObjEntity();
                for (CustomerAnotherNameMainVo anmv : listVo) {
                    CrmCustomerVo ccv = customerVos.get(anmv.getCustomerId());
                    anmv.setCustomerVo(ccv);
                }
            }
            //查询用户信息
            List<String> collect = listVo.stream().map(CustomerAnotherNameMainVo::getCreateUser).toList();
            JsonObject<List<PersonVO>> byIds = tbsPersonClient.listByIds(collect.toArray(new String[]{}));
            if(byIds != null && CollectionUtil.isNotEmpty(byIds.getObjEntity())){
                List<PersonVO> personVOS = byIds.getObjEntity();

                for (CustomerAnotherNameMainVo anmv : listVo) {
                    PersonVO personVO = personVOS.stream().filter(e -> e.getUuid().equals(anmv.getCreateUser())).findFirst().orElse(null);
                    anmv.setCreateUserName(personVO != null ? NameUtils.getNameByPersonVO(personVO) : "");
                }
            }
        }

        //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
        PageUtils dataTable = getDataTable(list,listVo);
        return new JsonObject<>(dataTable);
    }

    @PostMapping("/selectInfo")
    @Operation(summary = "查询别名审批申请详情")
    @PreFlowPermission
    public JsonObject<CustomerAnotherNameMainVo> selectInfo(@RequestBody CustomerAnotherNameMainVo customerAnotherNameMainVo){
        PreFlowPermissionAspect.checkProcessInstanceId(customerAnotherNameMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询流程信息
        CustomerAnotherNameMain customerAnotherNameMain = customerAnotherNameMainService.getOne(new QueryWrapper<CustomerAnotherNameMain>().eq("process_instance_id", customerAnotherNameMainVo.getProcessInstanceId()));
        CustomerAnotherNameMainVo result = HyperBeanUtils.copyPropertiesByJackson(customerAnotherNameMain, CustomerAnotherNameMainVo.class);

        if(customerAnotherNameMain != null){
            //2.查询客户信息
            JsonObject<CrmCustomerVo> jsonObject = remoteCustomerService.getCustomerInfo(customerAnotherNameMain.getCustomerId(), UserInfoHolder.getCurrentPersonId(),true);

            if(jsonObject.isSuccess() && jsonObject.getObjEntity() != null){
                CrmCustomerVo crmCustomerVo = jsonObject.getObjEntity();
                //2.查询行业信息
                if(StringUtils.isNotEmpty(crmCustomerVo.getIndustryId())) {
                    JsonObject<CrmIndustryVO> jObject = remoteIndustryService.industryByUuid(crmCustomerVo.getIndustryId());
                    if (jObject.isSuccess()) {
                        CrmIndustryVO industryVO = jObject.getObjEntity();
                        crmCustomerVo.setIndustryType(industryVO.getType());
                    }
                }
                result.setCustomerVo(crmCustomerVo);
            }
        }

        return new JsonObject<>(result);
    }

    @PostMapping("/update")
    @Operation(summary = "修改别名审批申请")
    @PreAuthorize(hasPermission = "crm_customer_admin_alias_approve_list")
    @PreFlowPermission
    public JsonObject<Boolean> update(@Valid @RequestBody CustomerAnotherNameApproveFlowLaunchDTO launchDTO){
        PreFlowPermissionAspect.checkProcessInstanceId(launchDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询流程信息
        CustomerAnotherNameMain customerAnotherNameMain = customerAnotherNameMainService.getOne(new QueryWrapper<CustomerAnotherNameMain>().eq("process_instance_id", launchDTO.getProcessInstanceId()));
        //2.查询客户信息
        JsonObject<CrmCustomerVo> customerInfo = remoteCustomerService.getCustomerInfo(customerAnotherNameMain.getCustomerId(), UserInfoHolder.getCurrentPersonId(), false);
        Assert.isFalse(!customerInfo.isSuccess() || customerInfo.getObjEntity() == null,"查询不到关联的客户信息，请联系管理员处理。");

        //客户别名重名校验
        List<String> names = launchDTO.getNewNames();
        if(CollectionUtil.isNotEmpty(names)){
            for (int i=0;i<names.size();++i) {
                String newName = names.get(i);
                Assert.isFalse(newName.equals(customerInfo.getObjEntity().getName()), "别名不可与公司名称相同。");
                //查询系统中是否有重名的别名（已生效）
                JsonObject<List<CrmCustomerAnotherVo>> byName1 = remoteCustomerAnotherService.findByName(newName);
                Assert.isFalse(byName1.isSuccess() && CollectionUtil.isNotEmpty(byName1.getObjEntity()), "系统中存在相同名称的客户别名。");
                //查询系统中是否有重名的别名（流程中-排除当前流程）
                List<CustomerAnotherNameMain> existFlows = customerAnotherNameMainService.selectFlowNotNowFlow(newName,launchDTO.getProcessInstanceId());
                Assert.isFalse(CollectionUtil.isNotEmpty(existFlows), "审批流程中存在同名的客户别名。");

                for (int j = i+1; j< names.size(); ++j) {
                    if(newName.equals(names.get(j))){
                        Assert.isFalse(true, "该客户存在相同别名，请重新编辑。");
                    }
                }
            }
        }

        //修改信息
        customerAnotherNameMain.setNewNames(launchDTO.getNewNames());
        customerAnotherNameMain.setReason(launchDTO.getReason());
        customerAnotherNameMainService.updateById(customerAnotherNameMain);

        return new JsonObject<>(true);
    }

}
