package com.topsec.crm.flow.core.validator.pricereview.condition;

import com.topsec.crm.flow.core.validator.CheckCondition;

import com.topsec.crm.flow.core.validator.pricereview.PriceReviewCheckContext;

/**
 * 签约类型变更
 * 是否直签发生变更,价审失效(例外情况:毛利,成交价不变的情况下,支持渠道变更为直签更符合公司利益,价审不失效)
 * <AUTHOR>
 */
public class SigningTypeChangeCondition implements CheckCondition<PriceReviewCheckContext> {
    @Override
    public boolean check(PriceReviewCheckContext context)  {
        Integer currentType = context.getProjectDetail().getProjectInfo().getSigningType();
        Integer snapshotType = context.getProjectSnapshot().getPriceReviewMain().getSigningType();

        // 直签变为渠道失效
        if (snapshotType.equals(1) && currentType.equals(0) ) {
            return false;
        }
        return true;
        // 渠道变为直签
        // if (snapshotType.equals(0) && currentType.equals(1) ) {
        //     // 例外情况:成交单价&外包服务费没变
        //     List<PriceReviewProductOwnDTO> currentOwnList = context.getProjectDetail().getProductOwnList();
        //     // 按物料代码、最终用户价、成交单价 分组
        //     Map<ProductOwnPriceComparable, Integer> currentMap = currentOwnList.stream().map(productOwn -> {
        //         return new ProductOwnPriceComparable(productOwn.getProductId(), productOwn.getDealPrice(),
        //                 productOwn.getSplitOutsourcePrice(), productOwn.getProductNum()
        //         );
        //     }).collect(Collectors.groupingBy(v -> v, Collectors.summingInt(ProductOwnPriceComparable::getTotalNum)));
        //
        //
        //     List<PriceReviewProductOwnDTO> snapshotOwnList = ListUtils.emptyIfNull(context.getProjectSnapshot().getProductOwnList());
        //
        //     Map<ProductOwnPriceComparable, Integer> snapshotMap = snapshotOwnList.stream().map(productOwn -> {
        //         return new ProductOwnPriceComparable(productOwn.getProductId(), productOwn.getDealPrice(),
        //                 productOwn.getSplitOutsourcePrice(), productOwn.getProductNum()
        //         );
        //     }).collect(Collectors.groupingBy(v -> v, Collectors.summingInt(ProductOwnPriceComparable::getTotalNum)));
        //     return MapCompareUtils.areMapsEqual(currentMap, snapshotMap);
        //
        // }
        // return false;
    }

    @Override
    public String defaultFailureReason() {
        return "是否直签发生变更";
    }


}
