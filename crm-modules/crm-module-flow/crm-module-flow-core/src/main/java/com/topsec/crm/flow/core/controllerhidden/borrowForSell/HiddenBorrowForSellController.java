package com.topsec.crm.flow.core.controllerhidden.borrowForSell;

import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowFlowPartVO;
import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowFlowQuery;
import com.topsec.crm.flow.core.service.IBorrowForSellProductSnService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnSelectVO;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/hidden/borrowForSell")
@Tag(name = "【借转销内部调用接口】", description = "HiddenBorrowForSellController")
public class HiddenBorrowForSellController extends BaseController {

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @Autowired
    private IBorrowForSellProductSnService borrowForSellProductSnService;

    /**
     * 查询项目可以出货的借转销设备序列号
     * @param stuffCodes
     * @param personId
     * @param projectId
     * @return
     */
    @PostMapping("/queryBorrowForSellDevicePsn")
    JsonObject<List<CrmProjectProductSnSelectVO.ProductSnSelect>> queryBorrowForSellDevicePsn(@RequestBody List<String> stuffCodes, @RequestParam String personId, @RequestParam String projectId){
        List<CrmProjectProductSnSelectVO.ProductSnSelect> result = borrowForSellProductSnService.queryBorrowForSellDevicePsn(stuffCodes,personId,projectId);
        return new JsonObject<>(result);
    }

    /**
     * 定时退回借转销流程
     */
    @GetMapping(value = "/checkAndBackBorrowForSell")
    void checkAndBackBorrowForSell(){
        borrowForSellProductSnService.checkAndBackBorrowForSell();
    }

    @PostMapping("/borrowForSellList")
    JsonObject<List<BorrowFlowPartVO>> borrowForSellList(@RequestBody BorrowFlowQuery borrowFlowQuery){
        List<BorrowFlowPartVO> result = borrowForSellService.borrowForSellList(borrowFlowQuery);
        return new JsonObject<>(result);
    }
}
