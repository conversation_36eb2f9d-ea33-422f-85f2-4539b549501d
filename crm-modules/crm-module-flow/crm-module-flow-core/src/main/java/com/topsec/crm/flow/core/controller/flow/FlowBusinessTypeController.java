package com.topsec.crm.flow.core.controller.flow;

import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsBusinessTypeClient;
import com.topsec.vo.TfsBusinessTypeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/business")
@Tag(name = "查询流程业务类型相关Controller", description = "/business")
@RequiredArgsConstructor
@Validated
public class FlowBusinessTypeController {
    private  final TfsBusinessTypeClient tfsBusinessTypeClient;


    @GetMapping("/findBusinessTypeList")
    @Operation(summary = "查询流程业务类型数据字典")
    @PreAuthorize
    public JsonObject<List<TfsBusinessTypeVo>> findBusinessTypeList(){
        return  tfsBusinessTypeClient.queryBusinessTypeList();
    }


}
