package com.topsec.crm.flow.core.controller.threeProcurement;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.topsec.crm.flow.api.vo.ThreeProcurementProductPeriodVo;
import com.topsec.crm.flow.api.vo.ThreeProcurementProductVo;
import com.topsec.crm.flow.api.vo.ThreeProcurementSupplierVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ThreeProcurementProduct;
import com.topsec.crm.flow.core.entity.ThreeProcurementReviewMain;
import com.topsec.crm.flow.core.service.ReturnExchangeProductThirdService;
import com.topsec.crm.flow.core.service.ThreeProcurementProductService;
import com.topsec.crm.flow.core.service.ThreeProcurementReviewMainService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.operation.api.RemoteSupplierContactService;
import com.topsec.crm.operation.api.RemoteSupplierProductService;
import com.topsec.crm.operation.api.dto.SupplierContactsQuery;
import com.topsec.crm.operation.api.entity.SupplierContactsVO;
import com.topsec.crm.operation.api.entity.SupplierVO;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 第三方采购产品Controller
 *
 * @date 2024-09-02
 */
@RestController
@RequestMapping("/threeProcurementProduct")
@Tag(name = "【第三方采购产品】", description = "threeProcurementProduct")
@RequiredArgsConstructor
@Validated
public class ThreeProcurementProductController extends BaseController
{
    private final ThreeProcurementProductService threeProcurementProductService;

    private final RemoteSupplierProductService remoteSupplierProductService;

    private final ThreeProcurementReviewMainService threeProcurementReviewMainService;

    private final ReturnExchangeProductThirdService returnExchangeProductThirdService;


    /**
     * 查询【第三方采购产品】
     */
    @GetMapping("/queryThreeProcurementProductList")
    @Operation(summary = "08中试部审批-查询没有物料代码的第三方采购产品")
    @PreFlowPermission(hasAnyNodes = {"sid-2A33C3CF-D099-4A95-9DDE-5DB2904D9A65"})
    public JsonObject<List<ThreeProcurementProductVo>> queryThreeProcurementProductList(@RequestParam  String processInstanceId)
    {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementReviewMain threeProcurementReviewMain = threeProcurementReviewMainService.selectThreeProcurementReviewMainByProcessInstanceId(processInstanceId);
        boolean thirdProduct = threeProcurementReviewMain.getType().equals(1);
        List<ThreeProcurementProduct> threeProcurementProductList = threeProcurementProductService.selectThreeProcurementProductList(processInstanceId);
        List<ThreeProcurementProduct> procurementProductList = threeProcurementProductList.stream().filter(product -> product.getStatus() == 1).toList();
        List<ThreeProcurementProductVo> procurementProductVoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(procurementProductList)){
           procurementProductVoList = threeProcurementProductService.convertProductVoList(thirdProduct,threeProcurementReviewMain.isReturnAndExchange(), procurementProductList);
        }
        return new JsonObject<>(procurementProductVoList);
    }







    @PostMapping("/editPeriod")
    @Operation(summary = "01采购专员审批-修改【第三方采购-保修期】")
    @PreFlowPermission(hasAnyNodes = {"sid-8207D1C5-32F9-4312-9C0F-56CCE5ED12B1"})
    public JsonObject<Boolean> editPeriod(@RequestBody ThreeProcurementProductPeriodVo threeProcurementProductPeriodVo)
    {
        threeProcurementProductPeriodVo.setProcurementPeriodEndTime(threeProcurementProductPeriodVo.getProcurementPeriodStartTime().plusMonths(threeProcurementProductPeriodVo.getProcurementPeriod()));
        return new JsonObject<>(threeProcurementProductService.updateThreeProcurementProductPeriod(threeProcurementProductPeriodVo));
    }




    @GetMapping("/findSupplierByProcessInstanceId")
    @Operation(summary = "根据合同id查询第三方产品的供应商-合同详情发起普通采购使用 ")
    @PreFlowPermission
    public JsonObject<List<SupplierVO>> findSupplierByProcessInstanceId(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程实例ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementReviewMain threeProcurementReviewMain = threeProcurementReviewMainService.selectThreeProcurementReviewMainByProcessInstanceId(processInstanceId);
        if (Objects.isNull(threeProcurementReviewMain)){
            throw new CrmException("未查询到流程信息");
        }
        boolean returnAndExchange = threeProcurementReviewMain.isReturnAndExchange();
        //退换货
        if (returnAndExchange) {
            return new JsonObject<>(returnExchangeProductThirdService.selectSupplierByReturnExchangeId(threeProcurementReviewMain.getReturnAndExchangeProcessId()));
        }
        return new JsonObject<>(threeProcurementProductService.selectSupplierByContractId(threeProcurementReviewMain.getContractId()));
    }





    @GetMapping("/findSupplierList")
    @Operation(summary = "根据申请人查询对应的供应商列表")
//    @PreFlowPermission
    @PreAuthorize
    public JsonObject<List<SupplierVO>> findSupplierList(){
        return remoteSupplierProductService.getSupplierByContactIds(Collections.singletonList(UserInfoHolder.getCurrentPersonId()));
    }

}
