package com.topsec.crm.flow.core.controller.accessSwitching;

import com.topsec.crm.flow.api.dto.accessSwitching.AccessSwitchingDetailInfo;
import com.topsec.crm.flow.api.dto.accessSwitching.AccessSwitchingFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.accessSwitching.AccessSwitchingInfo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.AccessSwitchingDetail;
import com.topsec.crm.flow.core.process.impl.AccessSwitchingProcessService;
import com.topsec.crm.flow.core.service.AccessSwitchingDetailService;
import com.topsec.crm.flow.core.service.AccessSwitchingService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteAccessConfigClient;
import com.topsec.crm.project.api.entity.CrmProjectProductAccessSwitchingVo;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/accessSwitching")
@Tag(name = "【产品通路转换】", description = "accessSwitching")
@RequiredArgsConstructor
@Validated
public class AccessSwitchingController extends BaseController {
    @Resource
    private AccessSwitchingService accessSwitchingService;
    @Resource
    private AccessSwitchingDetailService accessSwitchingDetailService;

    @Resource
    private AccessSwitchingProcessService accessSwitchingProcessService;

    @Resource
    private RemoteAccessConfigClient accessConfigClient;

    @PreAuthorize(hasPermission="crm_flow_product_passage_convert")
    @PostMapping("/launch")
    @Operation(summary = "发起产品通路转换流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody AccessSwitchingFlowLaunchDTO accessSwitchingFlowLaunchDTO){
        return new JsonObject<>(accessSwitchingProcessService.launch(accessSwitchingFlowLaunchDTO));
    }

//    @PreFlowPermission
//    @GetMapping("/selectAccessSwitchingById")
//    @Operation(summary = "查询通路转换主表信息")
//    public JsonObject<AccessSwitchingInfo> selectAccessSwitchingById(String id){
//        return new JsonObject<>(accessSwitchingService.selectAccessSwitchingById(id));
//    }

    @PreFlowPermission
    @PostMapping("/selectAccessSwitchingDetailInfo")
    @Operation(summary = "分页查询转换产品明细")
    public JsonObject<PageUtils<AccessSwitchingDetail>> selectAccessSwitchingDetailInfo(@RequestParam String processInstanceId, @RequestParam Integer productType){
        CrmAssert.hasText(processInstanceId,"流程ID不能为空！");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<AccessSwitchingDetailInfo> accessSwitchingInfos = accessSwitchingDetailService.selectAccessSwitchingDetailInfo(processInstanceId,productType);
        PageUtils dataTable = getDataTable(accessSwitchingInfos,accessSwitchingInfos);
        return new JsonObject<>(dataTable);
    }

    @PreFlowPermission
    @GetMapping("/selectAccessSwitchingInfo")
    @Operation(summary = "根据ID查询通路转换流程")
    public JsonObject<CrmProjectProductAccessSwitchingVo> selectAccessSwitchingInfo(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程ID不能为空！");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
//        processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(accessSwitchingService.selectAccessSwitchingInfo(processInstanceId));
    }

    @PreAuthorize(hasPermission="crm_flow_product_passage_convert")
    @GetMapping("/getAccessProjectProduct")
    @Operation(summary = "查询项目可转换产品")
    public JsonObject<TableDataInfo> getAccessProjectProduct(@RequestParam String projectId, @RequestParam Integer targerSwitching){
        return new JsonObject<>(accessSwitchingService.getAccessProjectProduct(projectId, targerSwitching));
    }

//    @PreAuthorize
//    @GetMapping("/getAccessProjectProductTest")
//    @Operation(summary = "查询项目可转换产品(新测试中)")
//    public JsonObject<Boolean> getAccessProjectProductTest(String processInstanceId){
//        CrmProjectProductAccessSwitchingVo crmProjectProductAccessSwitchingVo = accessSwitchingService.selectAccessSwitchingInfo(processInstanceId);
//        return accessConfigClient.saveAccessSwitching(crmProjectProductAccessSwitchingVo);
//    }


}
