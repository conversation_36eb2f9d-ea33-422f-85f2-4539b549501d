package com.topsec.crm.flow.core.controller.borrowForSellInventory;

import com.topsec.crm.flow.api.dto.borrowForSellInventory.BorrowForSellInventoryDTO;
import com.topsec.crm.flow.api.dto.borrowForSellInventory.BorrowForSellInventoryExportDTO;
import com.topsec.crm.flow.api.dto.borrowForSellInventory.BorrowForSellInventoryQuery;
import com.topsec.crm.flow.core.service.BorrowForSellInventoryService;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2025-03-17  13:50
 * @Description:
 */
@RestController
@RequestMapping("/borrowForSellInventory")
@Tag(name = "借转销库存", description = "/borrowForSellInventory")
@RequiredArgsConstructor
@Validated
public class BorrowForSellInventoryController extends BaseController {

    private final BorrowForSellInventoryService borrowForSellInventoryService;


    @PostMapping("/pageQuery")
    @Operation(summary = "分页查询借转销库存")
    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_inventory",dataScope = "crm_flow_borrow_for_sell_inventory")
    public JsonObject<PageUtils<BorrowForSellInventoryDTO>> BorrowForSellInventoryPage(@RequestBody BorrowForSellInventoryQuery query) {
        return new JsonObject<>(borrowForSellInventoryService.borrowForSellInventoryPage(query));
    }


    @PostMapping("/BorrowForSellInventoryExport")
    @Operation(summary = "借转销库存导出")
    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_inventory_export",dataScope = "crm_flow_borrow_for_sell_inventory_export")
    public void BorrowForSellInventoryExport(@RequestBody BorrowForSellInventoryQuery query) throws Exception{
        PageUtils<BorrowForSellInventoryDTO> borrowForSellInventoryDTOPageUtils = borrowForSellInventoryService.borrowForSellInventoryPage(query);
        List<BorrowForSellInventoryDTO> list = borrowForSellInventoryDTOPageUtils.getList();
        List<BorrowForSellInventoryExportDTO> borrowForSellInventoryExportDTOS = HyperBeanUtils.copyListPropertiesByJackson(list, BorrowForSellInventoryExportDTO.class);
        ExcelUtil<BorrowForSellInventoryExportDTO> excelUtil = new ExcelUtil<>(BorrowForSellInventoryExportDTO.class);
        excelUtil.exportExcel(response, borrowForSellInventoryExportDTOS,"借转销库存");
    }


}