package com.topsec.crm.flow.core.controller.borrowforprobation;


import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.service.BorrowForProbationService;
import com.topsec.crm.flow.core.service.IBorrowForProbationStatisticalSnapshotService;
import com.topsec.crm.framework.common.constant.BorrowForProbationConstants;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.project.api.entity.CrmBorrowForProbationSituationVO;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 借试用相关流程审批通过时的统计快照 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@RestController
@RequestMapping("/borrowForProbationStatisticalSnapshot")
@Tag(name = "借试用相关流程审批通过时的统计快照", description = "/borrowForProbationStatisticalSnapshot")
public class BorrowForProbationStatisticalSnapshotController extends BaseController {

    @Autowired
    private IBorrowForProbationStatisticalSnapshotService borrowForProbationStatisticalSnapshotService;

    @Autowired
    private BorrowForProbationService borrowForProbationService;

    @Resource
    private TfsNodeClient tfsNodeClient;

    @PreFlowPermission
    @GetMapping("/flow/{processInstanceId}")
    @Operation(summary = "根据流程主键ID查询借试用统计快照数据")
    public JsonObject<CrmBorrowForProbationSituationVO> borrowForProbationSituationOfSnapshot(@PathVariable String processInstanceId, @RequestParam String type) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<String> activityIds = new ArrayList<>();
        switch (type){
            case "借试用":
                activityIds = BorrowForProbationConstants.BORROW_FOR_PROBATION;break;
            case "转借":
                activityIds = BorrowForProbationConstants.BORROW_FOR_FORWARD;break;
            case "续借":
                activityIds = BorrowForProbationConstants.BORROW_FOR_RENEW;break;
            case "归还":
                activityIds = BorrowForProbationConstants.BORROW_FOR_BACK;break;
            default:
                CrmBorrowForProbationSituationVO build = CrmBorrowForProbationSituationVO.builder().hiden(true).build();
                return new JsonObject<CrmBorrowForProbationSituationVO>(build);
        }
        JsonObject<Set<String>> setJsonObject = tfsNodeClient.queryAssigneeAccountIdList(processInstanceId, activityIds);
        if(setJsonObject.isSuccess() && setJsonObject.getObjEntity().contains(getCurrentAccountId())){
            return new JsonObject<CrmBorrowForProbationSituationVO>(borrowForProbationStatisticalSnapshotService.borrowForProbationSituationOfSnapshot(processInstanceId));
        }else{
            CrmBorrowForProbationSituationVO build = CrmBorrowForProbationSituationVO.builder().hiden(true).build();
            return new JsonObject<CrmBorrowForProbationSituationVO>(build);
        }
    }

    /*@PutMapping("/flow/save")
    @ApiOperation(value = "根据流程主键ID查询借试用统计快照数据")
    public JsonObject<Boolean> save(@RequestParam String personId) {
        CrmBorrowForProbationSituationVO crmBorrowForProbationSituationVO = borrowForProbationService.borrowForProbationSituation("3c3bbca4bdcc4fe4b02c1c86dc2b7f57", "8d72473b-69d2-11ef-b11d-0242ac12000b");
        // 保存借试用审核通过时的统计快照
        BorrowForProbationStatisticalSnapshot build = BorrowForProbationStatisticalSnapshot.builder().borrowId("0f1bf1ea-3e76-4ea9-9097-17b39f9191c6").processInstanceId("8d72473b-69d2-11ef-b11d-0242ac12000b").processNumber("JSY2024090300002").snapshot(crmBorrowForProbationSituationVO).build();
        return new JsonObject<Boolean>(borrowForProbationStatisticalSnapshotService.saveSnapshot(build));
    }*/
}
