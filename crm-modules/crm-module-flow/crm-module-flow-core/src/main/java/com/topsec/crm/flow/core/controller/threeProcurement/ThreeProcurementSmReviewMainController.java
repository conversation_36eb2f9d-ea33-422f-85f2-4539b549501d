package com.topsec.crm.flow.core.controller.threeProcurement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.topsec.crm.account.api.client.RemoteAccountService;
import com.topsec.crm.flow.api.dto.contractreview.baseinfo.ContractBasicInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.vo.ThreeProcurementProductVo;
import com.topsec.crm.flow.api.vo.ThreeProcurementReviewMainVo;
import com.topsec.crm.flow.api.vo.ThreeProcurementReviewQuery;
import com.topsec.crm.flow.api.vo.ThreeProcurementSmReviewMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.ReturnExchangeProductThird;
import com.topsec.crm.flow.core.entity.ThreeProcurementSmReviewMain;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 【SM第三方采购】Controller
 *
 * @date 2024-09-02
 */
@RestController
@RequestMapping("/business/threeProcurementSm")
@Tag(name = "【【SM第三方采购】】", description = "threeProcurementSm")
@RequiredArgsConstructor
@Validated
public class ThreeProcurementSmReviewMainController extends BaseController
{

    private final ThreeProcurementSmReviewMainService threeProcurementSmReviewMainService;

//    private final ThreeProcurementSmReviewProcessService threeProcurementSmReviewProcessService;

    private final ContractReviewProductThirdService contractReviewProductThirdService;

    private final RemoteAccountService accountClient;

    private final TfsNodeClient tfsNodeClient;
    private final ThreeProcurementReviewMainService threeProcurementReviewMainService;

    private final ContractReviewMainService contractReviewMainService;

    private final ContractReviewFlowService contractReviewFlowService;

    private final ReturnExchangeProductThirdService returnExchangeProductThirdService;





    @GetMapping("/queryThirdProductByContractId")
    @Operation(summary = "根据合同id查询合同中的第三方产品信息")
    @PreAuthorize(hasPermission = "crm_third_purchase_sm_todo",dataScope = "crm_third_purchase_sm_todo")
    public JsonObject<List<ThreeProcurementProductVo>> queryThirdProductByContractId(@RequestParam String contractId,@RequestParam String processInstanceId) {
        CrmAssert.hasText(contractId, "合同id不能为空");
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        ThreeProcurementSmReviewMain threeProcurementSmReviewMain = threeProcurementSmReviewMainService.queryThreeProcurementSmReviewMain(processInstanceId);
        if (threeProcurementSmReviewMain == null){
            throw new RuntimeException("未查询到流程信息");
        }
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementSmReviewMain.getCreateUser());
        List<ThreeProcurementProductVo> procurementProductVoList = null;
        if (threeProcurementSmReviewMain.isReturnAndExchange()) {
            List<ReturnExchangeProductThird> returnExchangeProductThirds = returnExchangeProductThirdService.listNewProductByProcessInstanceId(threeProcurementSmReviewMain.getReturnAndExchangeProcessId());
            procurementProductVoList = HyperBeanUtils.copyListPropertiesByJackson(returnExchangeProductThirds, ThreeProcurementProductVo.class);
        }else {
            List<ContractProductThirdDTO> productThirdDTOList = contractReviewProductThirdService.productThirdInfoByContractId(contractId, true);
            procurementProductVoList = HyperBeanUtils.copyListPropertiesByJackson(productThirdDTOList, ThreeProcurementProductVo.class);
        }

        if (CollectionUtils.isNotEmpty(procurementProductVoList)){
            Map<String, ThreeProcurementProductVo> productVoMap = procurementProductVoList.stream()
                    .collect(Collectors.toMap(
                            ThreeProcurementProductVo::getProductId,
                            product -> product
                    ));
            threeProcurementReviewMainService.findThreeProcurementProductStatus(processInstanceId, procurementProductVoList);
            procurementProductVoList.forEach(procurementProductVo -> {
                if(procurementProductVo.getProcurementStatus().equals(0)){
                    procurementProductVo.setProcurementPrice(productVoMap.get(procurementProductVo.getProductId()).getPurchasePrice());
                    procurementProductVo.setProcurementTaxRate(productVoMap.get(procurementProductVo.getProductId()).getTaxRate());
                }
            });
        }
        return new JsonObject<>(procurementProductVoList);
    }


    /**
     * 分页查询【【SM第三方采购】】
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询【SM第三方采购提醒】")
    @PreAuthorize(hasPermission = "crm_third_purchase_sm_todo",dataScope = "crm_third_purchase_sm_todo")
    public JsonObject<PageUtils<ThreeProcurementSmReviewMainVo>> page(@RequestBody ThreeProcurementReviewQuery threeProcurementReviewQuery)
    {
        Page<ThreeProcurementSmReviewMain> threeProcurementSmReviewMainPage = threeProcurementSmReviewMainService.pageThreeProcurementSmReviewMain(threeProcurementReviewQuery);
        IPage<ThreeProcurementSmReviewMainVo> convert = threeProcurementSmReviewMainPage.convert(threeProcurementSmReviewMain -> {
            return HyperBeanUtils.copyPropertiesByJackson(threeProcurementSmReviewMain, ThreeProcurementSmReviewMainVo.class);
        });
        List<ThreeProcurementSmReviewMainVo> convertRecords = convert.getRecords();
        List<String> stringList = convertRecords.stream().map(ThreeProcurementSmReviewMainVo::getProcessInstanceId).toList();
        Map<String, Set<ApproveNode>> stringSetMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(stringList)){
            stringSetMap = Optional.ofNullable(tfsNodeClient.queryNodeByProcessInstanceIdList(ListUtils.emptyIfNull(stringList)))
                    .map(JsonObject::getObjEntity).orElseThrow(() -> new RuntimeException("审批节点查询失败"));
        }

        List<String> ids = convert.getRecords().stream().map(ThreeProcurementSmReviewMainVo::getCreateUser).toList();
        Map<String, String> nameMap = NameUtils.getNameMap(ids);
        if (CollectionUtils.isNotEmpty(ids)){
            Map<String, Set<ApproveNode>> finalStringSetMap = stringSetMap;
            convert.getRecords().forEach(threeProcurementSmReviewMainVo -> {
                threeProcurementSmReviewMainVo.setCreateUserName(nameMap.get(threeProcurementSmReviewMainVo.getCreateUser()));
                threeProcurementSmReviewMainVo.setApprovalNode(finalStringSetMap.get(threeProcurementSmReviewMainVo.getProcessInstanceId()));
            });
        }

        return new JsonObject<>(new PageUtils<>(convert));
    }



    @GetMapping("/queryThreeProcurementSmContract")
    @Operation(summary = "根据流程实例id查询【SM第三方采购提醒】合同id")
    @PreAuthorize(hasPermission = "crm_third_purchase_sm_todo",dataScope = "crm_third_purchase_sm_todo")
    public JsonObject<ContractBasicInfoDTO> queryThreeProcurementSmContract(@RequestParam String processInstanceId){
        ThreeProcurementSmReviewMain threeProcurementSmReviewMain = threeProcurementSmReviewMainService.queryThreeProcurementSmReviewMain(processInstanceId);
        if (threeProcurementSmReviewMain == null){
            throw new RuntimeException("未查询到流程信息");
        }
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementSmReviewMain.getCreateUser());
        return new JsonObject<>(contractReviewMainService.contractBasicInfo(threeProcurementSmReviewMain.getContractId(), true));

    }


    @GetMapping("/contractAmount")
    @Operation(summary = "合同总金额")
    @PreAuthorize(hasPermission = "crm_third_purchase_sm_todo",dataScope = "crm_third_purchase_sm_todo")
    public JsonObject<BigDecimal> contractAmount(@RequestParam String processInstanceId) {
        ThreeProcurementSmReviewMain threeProcurementSmReviewMain = threeProcurementSmReviewMainService.queryThreeProcurementSmReviewMain(processInstanceId);
        if (threeProcurementSmReviewMain == null){
            throw new RuntimeException("未查询到流程信息");
        }
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementSmReviewMain.getCreateUser());

        return new JsonObject<>(contractReviewFlowService.contractAmountTotal(threeProcurementSmReviewMain.getContractId()));
    }


    @PostMapping("/pageThreeProcurement")
    @Operation(summary = "SM采购提醒详情分页查询第三方采购审批列表")
    @PreAuthorize(hasPermission = "crm_third_purchase_sm_todo",dataScope = "crm_third_purchase_sm_todo")
    public JsonObject<PageUtils<ThreeProcurementReviewMainVo>> pageThreeProcurement(@RequestBody ThreeProcurementReviewQuery threeProcurementReviewQuery)
    {
        if (Objects.isNull(threeProcurementReviewQuery)){
            throw new CrmException("参数不能为空");
        }
        if (Objects.isNull(threeProcurementReviewQuery.getProcessInstanceId())){
            throw new CrmException("参数不能为空");
        }
        ThreeProcurementSmReviewMain threeProcurementSmReviewMain = threeProcurementSmReviewMainService.queryThreeProcurementSmReviewMain(threeProcurementReviewQuery.getProcessInstanceId());
        if (threeProcurementSmReviewMain == null){
            throw new RuntimeException("未查询到流程信息");
        }
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementSmReviewMain.getCreateUser());
        threeProcurementReviewQuery.setCreateUser(UserInfoHolder.getCurrentPersonId());
        IPage<ThreeProcurementReviewMainVo> convert = threeProcurementReviewMainService.getThreeProcurementReviewMainVoIPage(threeProcurementReviewQuery);

        return new JsonObject<>(new PageUtils<>(convert));
    }











}
