package com.topsec.crm.flow.core.controllerhidden.accessSwitching;


import com.topsec.crm.flow.core.service.AccessSwitchingService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/hidden/accessSwitching")
@Tag(name = "产品通路转换")
@RequiredArgsConstructor
public class HiddenAccessSwitchingController extends BaseController {

    private final AccessSwitchingService accessSwitchingService;


    @GetMapping("/getProductOwnThirdIdByProjectNo")
    @Operation(summary = "查询转换中产品行ID")
    public JsonObject<List<String>> getProductOwnThirdIdByProjectNo(String projectNo){
        return new JsonObject<>(accessSwitchingService.getProductOwnThirdIdByProjectNo(projectNo));
    }
}
