package com.topsec.crm.flow.core.controller.returnExchange;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.account.api.client.RemoteAccountService;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteKeyIndustrySerialAssignService;
import com.topsec.crm.contract.api.entity.CrmContractProductOwnVO;
import com.topsec.crm.contract.api.entity.CrmContractProductThirdVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.keyindustryproduct.ReturnChangePageVO;
import com.topsec.crm.contract.api.entity.keyindustryproduct.ReturnChangeQuery;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.flow.api.RemoteFlowContractInvoiceService;
import com.topsec.crm.flow.api.dto.contractInvoice.ContractInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDetailDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractReviewSpecialCodeDTO;
import com.topsec.crm.flow.api.dto.contractreview.request.SpecialCodePageQuery;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.RevenueRecognitionDTO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.api.dto.returnexchange.*;
import com.topsec.crm.flow.api.dto.returnexchange.contract.ContractReturnExchangeSelectQuery;
import com.topsec.crm.flow.api.vo.ProcessFileInfoVO;
import com.topsec.crm.flow.api.vo.costTransferMinus.CostTransferMinusInfoVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.handler.right.ProjectRightHandler;
import com.topsec.crm.flow.core.mapstruct.ReturnExchangeConvertor;
import com.topsec.crm.flow.core.process.impl.ContractReviewReturnExchangeProcessService;
import com.topsec.crm.flow.core.process.impl.PriceReviewProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.flow.core.service.impl.ReturnExchangeProductServiceImpl;
import com.topsec.crm.flow.core.service.impl.ReturnExchangeServiceImpl;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.project.api.RemoteProjectDirectlyService;
import com.topsec.crm.project.api.RemoteProjectDynastyService;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.utils.WebFilenameUtils;
import com.topsec.vo.node.ApproveNode;
import com.topsec.vo.node.TfsNextNodeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RestController
@RequestMapping("/contractReturnExchange")
@Tag(name = "合同评审退换货", description = "合同评审退换货")
@RequiredArgsConstructor
public class ContractReturnExchangeController extends BaseController {

    private final RemoteContractExecuteService executeService;
    private final ContractReviewReturnExchangeService contractReviewReturnExchangeService;
    private final RemoteProjectDirectlyService remoteProjectDirectlyService;
    private final RemoteProjectDynastyService remoteProjectDynastyService;
    private final ReturnExchangeService returnExchangeService;
    private final ReturnExchangeProductService returnExchangeProductService;
    private final ReturnExchangeProductThirdService returnExchangeProductThirdService;
    private final ContractReviewPaymentProvisionService contractReviewPaymentProvisionService;
    private final ContractReviewRevenueRecognitionService contractReviewRevenueRecognitionService;
    private final ContractReviewReturnExchangeProcessService contractReviewReturnExchangeProcessService;
    private final ContractReviewMainService contractReviewMainService;
    private final ProcessFileInfoService processFileInfoService;
    private final ContractReviewSpecialCodeService contractReviewSpecialCodeService;
    private final PriceReviewProcessService priceReviewProcessService;
    private final RemoteAgentService remoteAgentService;
    private final RemoteKeyIndustrySerialAssignService remoteKeyIndustrySerialAssignService;
    private final ProcessReleaseService processReleaseService;
    private final RemoteAccountService remoteAccountService;
    private final ContractReviewDeliveryService contractReviewDeliveryService;
    private final CostTransferMinusService costTransferMinusService;
    private final ReturnExchangeFeeService returnExchangeFeeService;
    private final ProcessExtensionInfoService processExtensionInfoService;
    private final RemoteFlowContractInvoiceService remoteFlowContractInvoiceService;
    private final ContractInvoiceExtendService contractInvoiceExtendService;
    private final TfsNodeClient tfsNodeClient;
    private final RedissonClient redissonClient;

    private static final String REDIS_KEY_PREFIX = "contractReviewReturnExchange:";

    @PostMapping("/launch")
    @Operation(summary = "发起")
    public JsonObject<Boolean> launch(@Valid @RequestBody ContractReviewReturnExchangeLaunchDTO launchDTO) {
        String contractNumber = launchDTO.getContractNumber();
        String redisKey = REDIS_KEY_PREFIX + contractNumber;
        RLock rlock = redissonClient.getLock(redisKey);
        try {
            rlock.lock();
            boolean exists = contractReviewReturnExchangeService.exists(new LambdaQueryWrapper<ContractReviewReturnExchange>()
                    .eq(ReturnExchange::getContractNumber, contractNumber)
                    .eq(FlowBaseEntity::getProcessState, ApprovalStatusEnum.SPZ.getCode()));
            if (exists) {
                return JsonObject.errorT("当前合同[%s]存在审批中的退换货流程，请勿重复发起".formatted(contractNumber));
            }
            boolean launch = contractReviewReturnExchangeProcessService.launch(launchDTO);
            return new JsonObject<>(launch);
        } finally {
            rlock.unlock();
        }
    }

    @PostMapping("/queryPossibleNextNodeIdListNew")
    @Operation(summary = "查节点")
    @PreFlowPermission
    public JsonObject<LinkedHashSet<ApproveNode>> queryPossibleNextNodeIdListNew(@RequestBody TfsNextNodeVo tfsNextNodeVo){
        PreFlowPermissionAspect.checkProcessInstanceId(tfsNextNodeVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        JsonObject<LinkedHashSet<ApproveNode>> linked = tfsNodeClient.queryPossibleNextNodeIdsNew(tfsNextNodeVo);
        boolean b = contractReviewReturnExchangeProcessService.show03F(tfsNextNodeVo.getProcessInstanceId());
        if (!b) {
            LinkedHashSet<ApproveNode> objEntity = linked.getObjEntity();
            objEntity.removeIf(next -> ContractReviewReturnExchangeProcessService.node03FId.equals(next.getNodeId()));
        }
        return linked;
    }

    @PostMapping("/writeDraftWithoutValidate")
    @Operation(summary = "写草稿-不校验")
    // @PreAuthorize(hasPermission = "crm_contract_receivable_collection_proof")
    public JsonObject<Boolean> writeDraftWithoutValidate(@RequestBody ContractReviewReturnExchangeLaunchDTO launchDTO) {
        Integer step = launchDTO.getStep();
        if (step == null){
            throw new CrmException("step不能为空");
        }
        returnExchangeService.writeDraft(launchDTO,UserInfoHolder.getCurrentPersonId());
        return new JsonObject<>(true);
    }

    @GetMapping("/readDraft")
    @Operation(summary = "读草稿")
    // @PreAuthorize(hasPermission = "crm_contract_receivable_collection_proof")
    public JsonObject<ContractReviewReturnExchangeLaunchDTO> readDraft(@RequestParam @Schema(description = "合同编号")
                                                                          String inputKey) {
        String draftKey = returnExchangeService.generateDraftKey(inputKey, UserInfoHolder.getCurrentPersonId());
        return new JsonObject<>(returnExchangeService.readDraft(draftKey, ContractReviewReturnExchangeLaunchDTO.class));
    }

    @GetMapping("/existDraft")
    @Operation(summary = "是否存在草稿")
    // @PreAuthorize(hasPermission = "crm_contract_receivable_collection_proof")
    public JsonObject<Boolean> existDraft(@RequestParam @Schema(description = "合同编号") String inputKey) {
        String draftKey = returnExchangeService.generateDraftKey(inputKey, UserInfoHolder.getCurrentPersonId());
        return new JsonObject<>(returnExchangeService.existDraft(draftKey));
    }

    @GetMapping("/isShowThreeProcurement")
    @Operation(summary = "是否展示第三方采购")
    public JsonObject<Boolean> isShowThreeProcurement(String processInstanceId) {
        // 非SM合同下的第三方产品
        return new JsonObject<>(contractReviewReturnExchangeService.isShowThreeProcurement(processInstanceId, UserInfoHolder.getCurrentPersonId()));
    }

    @PostMapping("/pageContractExecuteByCondition")
    @Operation(summary = "分页查询合同执行（发起退换货查询合同号的接口）", method = "POST")
    // todo 先用公司项目的 等后面方案确定了再改
    @PreAuthorize(hasPermission = "crm_contract_execute_company", dataScope = "crm_contract_execute_company")
    public JsonObject<PageUtils<ReturnExchangeBaseVO>> pageContractExecuteByCondition(@RequestBody ContractReturnExchangeSelectQuery query) {
        // 判断当前登录人是否是渠道
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        // 为空是所有人的数据
        Set<String> personIdList = dataScopeParam.getPersonIdList();

        CrmContractAfterQuery afterQuery = CrmContractAfterQuery.buildBaseQuery();
        CrmContractAfterQuery.BaseQuery baseQuery = afterQuery.getBaseQuery();
        afterQuery.setPageNum(query.getPageNum());
        afterQuery.setPageSize(query.getPageSize());

        // 如果不为空就添加进去 为空不添加代表查所有人的
        if (personIdList != null) {
            baseQuery.setContractOwnerIds(new ArrayList<>(personIdList));
        }
        CrmContractAfterQuery.SelectContractQuery selectContractQuery = new CrmContractAfterQuery.SelectContractQuery();
        selectContractQuery.setContractCompanyName(StringUtils.isEmpty(query.getKeyWord()) ? null : query.getKeyWord());
        selectContractQuery.setFinalCustomerName(StringUtils.isEmpty(query.getKeyWord()) ? null : query.getKeyWord());
        selectContractQuery.setContractNumber(StringUtils.isEmpty(query.getKeyWord()) ? null : query.getKeyWord());
        afterQuery.setSelectContractQuery(selectContractQuery);
        // 筛选正在走的合同的退换货
        List<ContractReviewReturnExchange> inProcessReturnExchange = contractReviewReturnExchangeService.getInProcessReturnExchange();
        if (CollectionUtils.isNotEmpty(inProcessReturnExchange)) {
            Set<String> inProcessContractNumbers = inProcessReturnExchange.stream().map(ContractReviewReturnExchange::getContractNumber).collect(Collectors.toSet());
            afterQuery.setContractNumbersNotIn(inProcessContractNumbers);
        }

        JsonObject<PageUtils<CrmContractExecuteVO>> result = executeService.pageByCondition(afterQuery);
        // 转换字段 避免全部字段暴露
        PageUtils<CrmContractExecuteVO> pageUtils = result.getObjEntity();
        List<CrmContractExecuteVO> list = pageUtils.getList();
        PageUtils<ReturnExchangeBaseVO> returnExchangeBaseVOPageUtils = new PageUtils<>();
        List<ReturnExchangeBaseVO> returnExchangeBaseVOS = ReturnExchangeConvertor.INSTANCE.toReturnExchangeBaseVO(list);
        Set<String> contractNumbers = ListUtils.emptyIfNull(returnExchangeBaseVOS).stream().map(ReturnExchangeBaseVO::getContractNumber).collect(Collectors.toSet());
        List<ContractReviewMainBaseInfoDTO> contractMains = contractReviewMainService.getByContractNumberBatch(contractNumbers);
        Map<String, ContractReviewMainBaseInfoDTO> contractByContractNumber = ListUtils.emptyIfNull(contractMains).stream()
                .collect(Collectors.toMap(ContractReviewMainBaseInfoDTO::getContractNumber, v -> v, (v1, v2) -> v1));
        ListUtils.emptyIfNull(returnExchangeBaseVOS).forEach(returnExchangeBaseVO -> {
            ContractReviewMainBaseInfoDTO baseInfoDTO = contractByContractNumber.get(returnExchangeBaseVO.getContractNumber());
            returnExchangeBaseVO.setProjectId(baseInfoDTO == null ? null : baseInfoDTO.getProjectId());
        });
        returnExchangeBaseVOPageUtils.setList(returnExchangeBaseVOS);
        returnExchangeBaseVOPageUtils.setPageNum(pageUtils.getPageNum());
        returnExchangeBaseVOPageUtils.setPageSize(pageUtils.getPageSize());
        returnExchangeBaseVOPageUtils.setTotalCount(pageUtils.getTotalCount());
        return new JsonObject<>(returnExchangeBaseVOPageUtils);
    }

    @GetMapping("/pageOwnProductByContractNumber")
    @Operation(summary = "分页查询合同自有产品（第一步的产品接口）", method = "GET")
    public JsonObject<List<CrmContractProductOwnVO>> pageOwnProductByContractNumber(@RequestParam String contractNumber) {
        // todo 合同权限
        return executeService.getProductOwnByContractNumber(contractNumber);
    }

    @GetMapping("/pageThirdProductByContractNumber")
    @Operation(summary = "分页查询合同第三方产品（第一步的产品接口）", method = "GET")
    public JsonObject<List<CrmContractProductThirdVO>> pageThirdProductByContractNumber(@RequestParam String contractNumber) {
        // todo 合同权限
        return executeService.getProductThirdByContractNumber(contractNumber);
    }

    @GetMapping("/oldProductSelectionForLaunchPage")
    @Operation(summary = "退换货旧的自有产品选择（发起页面使用）")
    public JsonObject<List<ReturnExchangeProductVO>> oldProductSelectionForLaunchPage(@RequestParam String contractNumber){
        List<ReturnExchangeProductVO> returnExchangeProductVOS = contractReviewReturnExchangeService.queryContractProductOwnList(contractNumber);
        return new JsonObject<>(returnExchangeProductVOS);
    }

    @GetMapping("/oldThirdProductSelectionForLaunchPage")
    @Operation(summary = "退换货旧的第三方产品选择（发起页面使用）")
    public JsonObject<List<ReturnExchangeProductThirdVO>> oldThirdProductSelectionForLaunchPage(@RequestParam String contractNumber){
        List<ReturnExchangeProductThirdVO> returnExchangeProductThirdVOS = contractReviewReturnExchangeService.queryContractProductThirdList(contractNumber);
        return new JsonObject<>(returnExchangeProductThirdVOS);
    }

    @PostMapping("/pageDirectOwnProducts")
    @Operation(summary = "新自有产品选择分页-公司项目")
    public JsonObject<PageUtils<CrmProductVo>> pageDirectOwnProducts(@RequestParam Integer pageNum, @RequestParam Integer pageSize,
                                                                     @RequestBody CrmProductVo crmProductVo) {
        JsonObject<PageUtils<CrmProductVo>> pageUtilsJsonObject = remoteProjectDirectlyService.pageOwnProducts(pageNum, pageSize, crmProductVo);

        Optional.ofNullable(pageUtilsJsonObject)
                .map(JsonObject::getObjEntity)
                .ifPresent(pageUtils -> {
                    List<CrmProductVo> crmProductVos = ListUtils.emptyIfNull(pageUtils.getList());
                    for (CrmProductVo productVo : crmProductVos) {
                        String materialCode = productVo.getMaterialCode();
                        if (StringUtils.isNotBlank(materialCode) && ReturnExchangeProductServiceImpl.disableCode.contains(materialCode)){
                            productVo.setCodeDisable(true);
                        }
                    }
                });
        return pageUtilsJsonObject;
    }

    @PostMapping("/pageDynastyOwnProducts")
    @Operation(summary = "新产品选择分页-zd项目")
    public JsonObject<PageUtils<CrmProductVo>> pageAgentOwnProducts(@RequestParam Integer pageNum, @RequestParam Integer pageSize,
                                                                    @RequestBody CrmProductVo crmProductVo) {
        JsonObject<PageUtils<CrmProductVo>> pageUtilsJsonObject = remoteProjectDynastyService.pageDynastyProducts(pageNum, pageSize, crmProductVo);
        Optional.ofNullable(pageUtilsJsonObject)
                .map(JsonObject::getObjEntity)
                .ifPresent(pageUtils -> {
                    List<CrmProductVo> crmProductVos = ListUtils.emptyIfNull(pageUtils.getList());
                    for (CrmProductVo productVo : crmProductVos) {
                        String materialCode = productVo.getMaterialCode();
                        if (StringUtils.isNotBlank(materialCode) && ReturnExchangeProductServiceImpl.disableCode.contains(materialCode)){
                            productVo.setCodeDisable(true);
                        }
                    }
                });
        return pageUtilsJsonObject;
    }

    @PostMapping("/filterProductHasSn")
    @Operation(summary = "选择出货序列号产品列表" ,description = "与PageProductChooseShipmentSn返回保持一致，对应total>0表示需要跳转【关联借试用、借转销、重点行业序列号页面】")
    @PreAuthorize(rightHandler = ProjectRightHandler.class,rightHandlerExtractArgsEL = {"#query.projectId"})
    public JsonObject<PageUtils<PriceReviewProductOwnDTO>> filterProductHasSn(@RequestBody @Valid ReturnExchangeSnPageQuery query) {
        query.setPersonId(UserInfoHolder.getCurrentPersonId());
        PageUtils<PriceReviewProductOwnDTO> paginate = PageUtils
                .paginate(returnExchangeService.filterProductHasSn(query), query.getPageSize(), query.getPageNum());
        return new JsonObject<>(paginate);
    }

    @GetMapping("/detail")
    @Operation(summary = "退换货流程基础信息")
    @PreFlowPermission
    public JsonObject<ReturnExchangeDetailVO> returnExchangeContractInfo() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ContractReviewReturnExchange returnExchangeBaseVO = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        ReturnExchangeDetailVO returnExchangeDetailVO = HyperBeanUtils.copyProperties(returnExchangeBaseVO, ReturnExchangeDetailVO::new);
        String createUser = returnExchangeBaseVO.getCreateUser();
        Map<String, PersonVO> personVOMap = remoteAccountService.queryPersonByIdsWithDept(List.of(createUser)).getObjEntity().stream().collect(Collectors.toMap(PersonVO::getUuid, item -> item));;
        returnExchangeDetailVO.setCreateUserDeptId(personVOMap.get(createUser).getDepartmentVO().getUuid());
        returnExchangeDetailVO.setNewProductAmount(returnExchangeProductService.getNewProductAmount(processInstanceId));
        returnExchangeDetailVO.setReturnedProductAmount(returnExchangeProductService.getOldProductAmount(processInstanceId));

        //产品线是否需要审批 -审批人反显用/退换货信息 字段赋值
        List<ReturnExchange.ProductLineRequired> productLineRequiredList =ListUtils.emptyIfNull(returnExchangeBaseVO.getProductLineRequiredList()) ;
        Map<String, Boolean> map = productLineRequiredList.stream().collect(Collectors.toMap(ReturnExchange.ProductLineRequired::getPersonId,
                ReturnExchange.ProductLineRequired::getRequired));

        returnExchangeDetailVO.setProductLineRequiredFinal(MapUtils.isNotEmpty(map)?map.containsValue(true):null);
        returnExchangeDetailVO.setProductLineRequiredForApply(map.getOrDefault(UserInfoHolder.getCurrentPersonId(),true));

        ProcessExtensionInfo one = processExtensionInfoService.getOne(new LambdaQueryWrapper<ProcessExtensionInfo>().eq(ProcessExtensionInfo::getProcessInstanceId, processInstanceId)
                .select(ProcessExtensionInfo::getProjectId));
        returnExchangeDetailVO.setProjectId(one.getProjectId());
        return new JsonObject<>(returnExchangeDetailVO);
    }

    @GetMapping("/returnExchangeReProduct")
    @Operation(summary = "查询退货换货旧产品详情")
    @PreFlowPermission
    public JsonObject<List<ReturnExchangeProduct>> returnExchangeDetail() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(returnExchangeProductService.listOldProductByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/returnExchangeNewProduct")
    @Operation(summary = "查询退货换货新产品详情")
    @PreFlowPermission
    public JsonObject<List<ReturnExchangeProduct>> returnExchangeNewProduct() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/returnExchangeReProductThird")
    @Operation(summary = "查询退货换货旧第三方产品详情")
    @PreFlowPermission
    public JsonObject<List<ReturnExchangeProductThird>> returnExchangeReProductThird() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(returnExchangeProductThirdService.listOldProductByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/returnExchangeNewProductThird")
    @Operation(summary = "查询退货换货新第三方产品详情")
    @PreFlowPermission
    public JsonObject<List<ReturnExchangeProductThird>> returnExchangeNewProductThird() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(returnExchangeProductThirdService.listNewProductByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/paymentProvisions")
    @Operation(summary = "查询合同付款条款")
    @PreFlowPermission
    public JsonObject<List<PaymentProvisionDTO>> payment() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<PaymentProvisionDTO> paymentProvisionDTOS = HyperBeanUtils.copyListProperties(contractReviewPaymentProvisionService.getByReturnExchangeProcessInstanceId(processInstanceId), PaymentProvisionDTO::new);
        NameUtils.setName(paymentProvisionDTOS);
        return new JsonObject<>(paymentProvisionDTOS);
    }

    @GetMapping("/revenueRecognitions")
    @Operation(summary = "查询新产品的收入确认条款")
    @PreFlowPermission
    public JsonObject<List<RevenueRecognitionDTO>> revenueRecognition() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<ContractReviewRevenueRecognition> byReturnExchangeProcessInstanceId = contractReviewRevenueRecognitionService.getByReturnExchangeProcessInstanceId(processInstanceId);
        List<RevenueRecognitionDTO> revenueRecognitionDTOS = contractReviewRevenueRecognitionService.initProductInfoByReturnExchange(byReturnExchangeProcessInstanceId);
        // 要过滤出是新产品的
        List<ReturnExchangeProduct> returnExchangeProducts = returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId);
        List<ReturnExchangeProductThird> returnExchangeProductThirds = returnExchangeProductThirdService.listNewProductByProcessInstanceId(processInstanceId);
        Set<String> returnExchangeProductIds = new HashSet<>();
        ListUtils.emptyIfNull(returnExchangeProducts).forEach(returnExchangeProduct -> returnExchangeProductIds.add(returnExchangeProduct.getId()));
        ListUtils.emptyIfNull(returnExchangeProductThirds).forEach(returnExchangeProductThird -> returnExchangeProductIds.add(returnExchangeProductThird.getId()));
        List<RevenueRecognitionDTO> list = revenueRecognitionDTOS.stream().filter(revenueRecognitionDTO -> returnExchangeProductIds.contains(revenueRecognitionDTO.getReturnExchangeOwnId()) || returnExchangeProductIds.contains(revenueRecognitionDTO.getReturnExchangeThirdId())).toList();
        return new JsonObject<>(list);
    }

    // todo 合同发货

    @GetMapping("/files")
    @Operation(summary = "退换货附件")
    @PreFlowPermission
    public JsonObject<List<ProcessFileInfoVO>> files(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(processFileInfoService.queryByProcessInstanceId(processInstanceId, null, null));
    }

    @GetMapping("/getCostTransferMinus")
    @Operation(summary = "获取费用调减")
    @PreFlowPermission
    public JsonObject<CostTransferMinusInfoVO> getCostTransferMinus(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(costTransferMinusService.getInfoByreFundProcessInstanceId(processInstanceId));
    }

    @GetMapping("/queryAndMergeProcessFee")
    @Operation(summary = "获取退换货费用")
    @PreFlowPermission
    public JsonObject<List<ReturnExchangeFeeVO>> queryAndMergeProcessFee(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(returnExchangeFeeService.queryAndMergeProcessFee(processInstanceId));
    }

    @PostMapping("/00edit")
    @Operation(summary = "修改退换货")
    @PreFlowPermission
    public JsonObject<ProcessExtensionInfo> edit(@Valid @RequestBody ContractReviewReturnExchangeLaunchDTO launchDTO){
        return new JsonObject<>(contractReviewReturnExchangeProcessService.edit00(launchDTO));
    }

    @PostMapping("/saveOrUpdateRevenueRecognition")
    @Operation(summary = "修改或保存收入确认条款(实时逻辑)")
    @PreFlowPermission
    public JsonObject<Boolean> saveOrUpdateRevenueRecognition(@RequestBody RevenueRecognitionDTO revenueRecognitionDTO) {
        assert revenueRecognitionDTO != null;
        String returnExchangeProcessInstanceId = revenueRecognitionDTO.getReturnExchangeProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(returnExchangeProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        if (revenueRecognitionDTO.getId() == null) {
            // 新增的时候直接判断contractId

            } else {
            // 修改的时候 要先根据id查出来
            ContractReviewRevenueRecognition revenueRecognition = contractReviewRevenueRecognitionService.getById(revenueRecognitionDTO.getId());
            if (revenueRecognition == null) {
                throw new CrmException("参数有误");
            }
            revenueRecognitionDTO.setReturnExchangeProcessInstanceId(null);
        }
        return new JsonObject<>(contractReviewRevenueRecognitionService.saveOrUpdate(revenueRecognitionDTO));
    }

    @GetMapping("/deletePaymentProvisionById")
    @Operation(summary = "根据id删除付款条款")
    @PreFlowPermission
    public JsonObject<Boolean> deletePaymentProvisionById(@RequestParam String id) {
        // 判断这个id 是不是这个人加的
        ContractReviewPaymentProvision contractReviewPaymentProvision = contractReviewPaymentProvisionService.getById(id);
        if (contractReviewPaymentProvision == null || contractReviewPaymentProvision.getDelFlag() == 1) {
            throw new CrmException("条款不存在");
        }
        String returnExchangeProcessInstanceId = contractReviewPaymentProvision.getReturnExchangeProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(returnExchangeProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewPaymentProvisionService.deleteById(id));
    }

    @PutMapping("/fillInProductLineRequired")
    @Operation(summary = "是否需要产品线分析")
    @PreFlowPermission
    public JsonObject<Boolean> fillInProductLineRequired(@RequestParam Boolean productLineRequired){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        boolean b = returnExchangeService.fillInProductLineRequired(processInstanceId,productLineRequired, UserInfoHolder.getCurrentPersonId());
        return new JsonObject<>(b);
    }

    @GetMapping("/feeAccountingList")
    @Operation(summary = "退换货产品费用核算列表")
    @PreFlowPermission
    public JsonObject<List<ReturnExchangeProductVO>> feeAccountingList(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<ReturnExchangeProductVO> returnExchangeProductVOS = returnExchangeService
                .feeAccountingList(processInstanceId);
        return new JsonObject<>(returnExchangeProductVOS);
    }

    @PostMapping("/fillInFee")
    @Operation(summary = "填写费用核算")
    @PreFlowPermission
    public JsonObject<Boolean> fillInFee(@RequestBody @NotEmpty List<@Valid ReturnExchangeFeeVO> returnExchangeFeeVOList){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        for (ReturnExchangeFeeVO returnExchangeFeeVO : returnExchangeFeeVOList) {
            returnExchangeFeeVO.setProcessInstanceId(processInstanceId);
        }
        return new JsonObject<>(returnExchangeService.fillInFee(returnExchangeFeeVOList));
    }


    @GetMapping("/tabFinal")
    @Operation(summary = "标记终稿")
    @PreFlowPermission
    public JsonObject<Boolean> tabFinal(@RequestParam String docId) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ContractReviewReturnExchange one = contractReviewReturnExchangeService.getOne(new LambdaQueryWrapper<ContractReviewReturnExchange>()
                .eq(ContractReviewReturnExchange::getProcessInstanceId, processInstanceId)
                .select(ReturnExchange::getId, ReturnExchange::getFinalDocId)
        );
        Set<String> finalDocId = SetUtils.emptyIfNull(one.getFinalDocId());
        Set<String> input = Stream.of(docId).collect(Collectors.toSet());
        input.addAll(finalDocId);
        one.setFinalDocId(input);
        return new JsonObject<>(contractReviewReturnExchangeService.updateById(one));
    }

    @GetMapping("/cancelFinal")
    @Operation(summary = "取消标记")
    @PreFlowPermission
    public JsonObject<Boolean> cancelFinal(@RequestParam String docId) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ContractReviewReturnExchange one = contractReviewReturnExchangeService.getOne(new LambdaQueryWrapper<ContractReviewReturnExchange>()
                .eq(ContractReviewReturnExchange::getProcessInstanceId, processInstanceId)
                .select(ReturnExchange::getId, ReturnExchange::getFinalDocId)
        );
        Set<String> finalDocId = one.getFinalDocId();
        if (CollectionUtils.isEmpty(finalDocId)) {
            return new JsonObject<>(true);
        }else {
            finalDocId.remove(docId);
        }
        one.setFinalDocId(finalDocId);
        return new JsonObject<>(contractReviewReturnExchangeService.updateById(one));
    }

    @PostMapping("/pageSpecialCode")
    @PreFlowPermission(hasAnyNodes = "reviewReturnAndExchange_03J")
    @Operation(summary = "分页查询03J的特殊代码确认")
    public JsonObject<TableDataInfo> pageAfterSecurityCode(@RequestBody SpecialCodePageQuery query) {
        assert query != null;
        PreFlowPermissionAspect.checkProcessInstanceId(query.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewReturnExchangeService.pageAfterSpecialCode(query));
    }

    @GetMapping("/hasSpecialCode")
    @PreFlowPermission
    @Operation(summary = "是否有特殊代码")
    public JsonObject<Boolean> hasSpec() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(contractReviewReturnExchangeService.hasSpecialCode(processInstanceId));
    }

    @GetMapping("/queryKeyIndustryApprover")
    @PreFlowPermission
    @Operation(summary = "查询重点行业审批人")
    public JsonObject queryKeyIndustryApprover() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        // 查出流程的申请人
        ContractReviewReturnExchange contractReviewReturnExchange = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        String createUser = contractReviewReturnExchange.getCreateUser();
        return new JsonObject<>(priceReviewProcessService.queryKeyIndustryApprover(createUser));
    }

    @GetMapping("/show03F")
    @PreFlowPermission
    @Operation(summary = "是否展示03F")
    public JsonObject<Boolean> show03F() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(contractReviewReturnExchangeProcessService.show03F(processInstanceId));
    }


    @PostMapping("/queryReturnChangeSnPage")
    @PreFlowPermission(hasAnyNodes = "reviewReturnAndExchange_03F")
    @Operation(summary = "03F查询重点行业序列号")
    public JsonObject<PageUtils<ReturnChangePageVO>> queryReturnChangeSnPage(@RequestBody ReturnChangeQuery query){
        // 取审批人和签订公司 此接口未分页
        String approvePersonId = UserInfoHolder.getCurrentPersonId();
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ContractReviewReturnExchange contractReviewReturnExchange = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        if (contractReviewReturnExchange == null) {
            throw new CrmException("参数信息有误");
        }
        query.setApprovePersonId(approvePersonId);
        query.setSigningCompanyId(contractReviewReturnExchange.getSignCompanyId());
        return remoteKeyIndustrySerialAssignService.getReturnChangeSnPage(query);
    }

    @PostMapping("/updatePsnById")
    @PreFlowPermission(hasAnyNodes = "reviewReturnAndExchange_03J")
    @Operation(summary = "03F关联序列号")
    public JsonObject<Boolean> updatePsnById(@RequestBody ReturnExchangeProductVO returnExchangeProductVO) {
        String approvePersonId = UserInfoHolder.getCurrentPersonId();
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ContractReviewReturnExchange contractReviewReturnExchange = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        if (contractReviewReturnExchange == null) {
            throw new CrmException("参数信息有误");
        }
        List<ReturnExchangeSnVO> psn = returnExchangeProductVO.getPsn();
        if (CollectionUtils.isEmpty(psn)) {
            return new JsonObject<>(true);
        }
        // 判断这个sn是不是
        ReturnChangeQuery returnChangeQuery = new ReturnChangeQuery();
        returnChangeQuery.setApprovePersonId(approvePersonId);
        returnChangeQuery.setSigningCompanyId(contractReviewReturnExchange.getSignCompanyId());
        List<ReturnChangePageVO> list = remoteKeyIndustrySerialAssignService.getReturnChangeSnPage(returnChangeQuery).getObjEntity().getList();
        if (CollectionUtils.isEmpty(list)) {
            return new JsonObject<>(true);
        }
        Set<String> snList = list.stream().map(ReturnChangePageVO::getSn).collect(Collectors.toSet());
        Set<String> inWeb = psn.stream().map(ReturnExchangeSnVO::getPsn).collect(Collectors.toSet());
        if (!snList.containsAll(inWeb)) {
            throw new CrmException("参数信息有误");
        }
        return new JsonObject<>(returnExchangeProductService.updatePsnById(List.of(returnExchangeProductVO), contractReviewReturnExchange.getCreateUser()));
    }

    @GetMapping("/showBack")
    @PreFlowPermission(hasAnyNodes = "reviewReturnAndExchange_04")
    @Operation(summary = "04步是否可以退回")
    public JsonObject<Boolean> showBack() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(processReleaseService.isCanReturnRelease(processInstanceId));
    }

    @PostMapping("/updateSaleCode")
    @Operation(summary = "03J代码确认")
    @PreFlowPermission(hasAnyNodes = "reviewReturnAndExchange_03J")
    public JsonObject<Boolean> updateSaleCode(@RequestBody List<ContractReviewSpecialCodeDTO> contractReviewSpecialCodeDTO) {
        if (CollectionUtils.isEmpty(contractReviewSpecialCodeDTO)) {
            return new JsonObject<>(ResultEnum.SUCCESS.getResult(), true);
        }
        Set<String> ids = contractReviewSpecialCodeDTO.stream().map(ContractReviewSpecialCodeDTO::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(ids)) {
            List<ContractReviewSpecialCode> specialCodes = contractReviewSpecialCodeService.listByIds(ids);
            Set<String> processInstanceIds = ListUtils.emptyIfNull(specialCodes).stream().filter(item -> item.getDelFlag() == 0).map(ContractReviewSpecialCode::getReturnExchangeProcessInstanceId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(processInstanceIds) || processInstanceIds.size() > 1) {
                throw new CrmException("参数有误，请检查参数");
            }
            String processInstanceId = processInstanceIds.stream().findFirst().orElseThrow(() -> new CrmException("参数有误，请检查参数"));
            PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }
        contractReviewSpecialCodeDTO.forEach(item -> {
            if (item.getId() != null) {
                // 修改的时候 不让修改的内容，
                item.setReturnExchangeProcessInstanceId(null);
            }
        });
        return new JsonObject<>(contractReviewSpecialCodeService.updateSaleCode(contractReviewSpecialCodeDTO));
    }

    @GetMapping("/getDeliveryInfoList")
    @PreFlowPermission
    @Operation(summary = "根据退换货流程实例id查询退换货发货信息", description = "退换货发货")
    public JsonObject<List<ContractDeliveryDTO>> getDeliveryInfoList() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ContractReviewReturnExchange contractReviewReturnExchange = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        return new JsonObject<>(contractReviewDeliveryService.getReturnChangeDeliveryInfoList(contractReviewReturnExchange.getId()));
    }

    @PostMapping("/saveDeliveryInfo")
    @Operation(summary = "发货信息保存", description = "退换货发货")
    @PreFlowPermission
    public JsonObject<Boolean> saveDeliveryInfo(@Valid @RequestBody ContractDeliveryDTO deliveryDTO){
        return new JsonObject<>(contractReviewDeliveryService.saveReturnDeliveryInfo(deliveryDTO));
    }

    @PostMapping("/saveOrUpdateDeliveryDetail")
    @Operation(summary = "发货产品明细保存或修改", description = "退换货发货")
    @PreFlowPermission
    public JsonObject<Boolean> saveOrUpdateDeliveryDetail(@RequestBody ContractDeliveryDTO deliveryDTO){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ContractReviewReturnExchange contractReviewReturnExchange = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        deliveryDTO.setReturnChangeId(contractReviewReturnExchange.getId());
        return new JsonObject<>(contractReviewDeliveryService.saveOrUpdateReturnChangeDetail(deliveryDTO));
    }

    @GetMapping("/deleteDeliveryById")
    @Operation(summary = "删除发货", description = "退换货发货")
    @PreFlowPermission
    public JsonObject<Boolean> deleteById(@RequestParam String id){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ContractReviewDelivery delivery = contractReviewDeliveryService.getById(id);
        if (delivery == null || delivery.getDelFlag()) {
            throw new CrmException("发货信息不存在");
        }
        ContractReviewReturnExchange contractReviewReturnExchange = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        List<ContractDeliveryDTO> returnChangeDeliveryInfoList = contractReviewDeliveryService.getReturnChangeDeliveryInfoList(contractReviewReturnExchange.getId());
        Set<String> collect = returnChangeDeliveryInfoList.stream().map(ContractDeliveryDTO::getId).collect(Collectors.toSet());
        if (!collect.contains(id)) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(contractReviewDeliveryService.deleteById(id));
    }

    @GetMapping("/getDeliveryProductPageByContractId")
    @Operation(summary = "根据退换货ID获取合同产品列表", description = "退换货发货")
    @PreFlowPermission
    public JsonObject<PageUtils<ContractDeliveryDetailDTO>> getProductPageByContractId(@RequestParam String deliveryId){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(contractReviewDeliveryService.getProductPageByReturnChangeId(processInstanceId, deliveryId));
    }

    @GetMapping("/contractDelivery/getDeliveredProductPage")
    @Operation(summary = "根据发货ID获取发货分页列表", description = "退换货发货")
    @PreFlowPermission
    public JsonObject<TableDataInfo> getDeliveredProductPage(@RequestParam String deliveryId){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        // 判断这个id 是不是这个人加的
        ContractReviewDelivery delivery = contractReviewDeliveryService.getById(deliveryId);
        if (delivery == null || delivery.getDelFlag()) {
            throw new CrmException("发货信息不存在");
        }
        ContractReviewReturnExchange contractReviewReturnExchange = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        List<ContractDeliveryDTO> returnChangeDeliveryInfoList = contractReviewDeliveryService.getReturnChangeDeliveryInfoList(contractReviewReturnExchange.getId());
        Set<String> collect = returnChangeDeliveryInfoList.stream().map(ContractDeliveryDTO::getId).collect(Collectors.toSet());
        if (!collect.contains(deliveryId)) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        startPage();
        return new JsonObject<>(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage(),contractReviewDeliveryService.getReturnChangeDeliveryProductPage(deliveryId));
    }

    @PostMapping("/page")
    @PreAuthorize(hasPermission = "crm_refund",dataScope = "crm_refund")
    @Operation(summary = "合同评审退换货流程列表")
    public JsonObject<PageUtils<ReturnExchangeItem>> page(@RequestBody ReturnExchangePageQuery query) {
        DataScopeParam dataScopeParam = query.getDataScopeParam();
        if (dataScopeParam!=null){
            String currentAgentId = UserInfoHolder.getCurrentAgentId();
            if (StringUtils.isNotEmpty(currentAgentId)) {
                CrmAgentVo agentInfo = remoteAgentService.getAgentInfo(currentAgentId).getObjEntity();
                String agentName = agentInfo.getAgentName();
                if (StringUtils.isNotEmpty(agentName)) {
                    query.setContractCompanyName(agentName);
                }
            }
            if (!dataScopeParam.getAllScope()){
                Set<String> personIdList = dataScopeParam.getPersonIdList();
                query.setPersonIds(personIdList);
            }
        }
        return new JsonObject<>(contractReviewReturnExchangeService.page(query));
    }

    @PostMapping("/export")
    @PreAuthorize(hasAllPermission ={"crm_refund_export","crm_refund"} ,dataScope = "crm_refund")
    @Operation(summary = "合同评审退换货流程列表导出")
    @SneakyThrows
    public void export(@RequestBody ReturnExchangePageQuery query, HttpServletResponse response) {
        DataScopeParam dataScopeParam = query.getDataScopeParam();
        if (dataScopeParam!=null){
            String currentAgentId = UserInfoHolder.getCurrentAgentId();
            if (StringUtils.isNotEmpty(currentAgentId)) {
                CrmAgentVo agentInfo = remoteAgentService.getAgentInfo(currentAgentId).getObjEntity();
                String agentName = agentInfo.getAgentName();
                if (StringUtils.isNotEmpty(agentName)) {
                    query.setContractCompanyName(agentName);
                }
            }
            if (!dataScopeParam.getAllScope()){
                Set<String> personIdList = dataScopeParam.getPersonIdList();
                query.setPersonIds(personIdList);
            }
        }
        query.setPageSize(Integer.MAX_VALUE);
        PageUtils<ReturnExchangeItem> page = contractReviewReturnExchangeService.page(query);
        List<ReturnExchangeItem> list = page.getList();
        List<ReturnExchangeServiceImpl.ReturnExchangeExcelItem> returnExchangeExcelItems = ReturnExchangeConvertor.INSTANCE.toReturnExchangeExcelItem(list);

        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), ReturnExchangeServiceImpl.ReturnExchangeExcelItem.class).build()){
            response.addHeader(HttpHeaders.CONTENT_DISPOSITION, WebFilenameUtils.disposition("合同评审退换货列表.xlsx",false));
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            excelWriter.write(returnExchangeExcelItems, EasyExcel.writerSheet("合同评审退换货").build());
        }
    }

    @GetMapping("/getInvoiceProcessPage")
    @Operation(summary = "根据退换货流程ID获取开票列表", description = "开票")
    @PreFlowPermission
    public JsonObject<TableDataInfo> getInvoiceProcessPage(@RequestParam Integer pageSize, @RequestParam Integer pageNum) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ContractReviewReturnExchange returnExchangeBaseVO = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        return remoteFlowContractInvoiceService.getInvoiceProcessPage(returnExchangeBaseVO.getContractNumber(), pageSize, pageNum);
    }

    @GetMapping("/getInvoiceAmount")
    @Operation(summary = "根据退换货流程ID获取开票金额", description = "开票金额")
    @PreFlowPermission
    public JsonObject<ContractInfoDTO> getInvoiceAmount() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ContractReviewReturnExchange returnExchangeBaseVO = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        ContractInfoDTO result = new ContractInfoDTO();
        CompletableFuture.allOf(CompletableFuture.supplyAsync(() ->
                        contractInvoiceExtendService.getMakeInvoiceAmountTotal(returnExchangeBaseVO.getContractNumber())).thenAccept(result::setMakeInvoiceAmount),
                CompletableFuture.supplyAsync(() ->
                        contractInvoiceExtendService.getReturnInvoiceAmountTotal(returnExchangeBaseVO.getContractNumber())).thenAccept(result::setReturnInvoiceAmount)
        );
        return new JsonObject<>(result);
    }


}
