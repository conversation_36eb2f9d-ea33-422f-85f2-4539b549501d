package com.topsec.crm.flow.core.controller.paymentverification;

import com.topsec.crm.flow.api.dto.paymentverification.VerificationConvertGoodsFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.process.impl.VerificationConvertGoodsProcessService;
import com.topsec.crm.flow.core.service.VerificationConvertGoodsService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/verificationConvertGoods")
@Tag(name = "回款核销")
@RequiredArgsConstructor
@Validated
public class VerificationConvertGoodsController {

    private final VerificationConvertGoodsProcessService processService;

    private final VerificationConvertGoodsService verificationConvertGoodsService;

    @PostMapping("/launch")
    @Operation(summary = "发起押金转货款流程")
    @PreAuthorize(hasPermission = "crm_repayment_verify_prepayment_convert_goods_payment")
    public JsonObject<Boolean> launch(@RequestBody VerificationConvertGoodsFlowLaunchDTO req){
        return new JsonObject<>(processService.launch(req));
    }

    @GetMapping("/getLaunchInfo")
    @Operation(summary = "获取押金转货款流程信息")
    @PreFlowPermission
    public JsonObject<VerificationConvertGoodsFlowLaunchDTO> getLaunchInfo(@RequestParam String processInstanceId){
        processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        return new JsonObject<>(verificationConvertGoodsService.getLaunchInfo(processInstanceId));
    }
}
