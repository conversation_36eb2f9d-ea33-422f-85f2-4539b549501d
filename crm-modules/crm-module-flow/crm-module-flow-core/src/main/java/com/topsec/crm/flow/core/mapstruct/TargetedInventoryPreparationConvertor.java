package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.targetedinventorypreparation.TargetedInventoryPreparationProductOwnDTO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.TargetedInventoryPreparationProductOwnVO;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 专项备货信息转换器
 * <AUTHOR>
 * @since 2024-07-26 18:21
 */
@Mapper
public interface TargetedInventoryPreparationConvertor {

    TargetedInventoryPreparationConvertor INSTANCE = Mappers.getMapper(TargetedInventoryPreparationConvertor.class);

    @Mapping(target = "id" , ignore = true)
    @Mapping(target = "projectProductOwnId", source = "id")
    @Mapping(target = "productId", source = "productId")
    @Mapping(target = "productLic", source = "productLic")
    @Mapping(target = "productNum", ignore = true)
    TargetedInventoryPreparationProductOwnDTO toOwn(CrmProjectProductOwnVO crmProjectProductOwnVo);

    @Mapping(target = "id" , ignore = true)
    @Mapping(target = "projectProductOwnId", source = "id")
    @Mapping(target = "productId", source = "productId")
    @Mapping(target = "productLic", source = "productLic")
    @Mapping(target = "productNum", source = "productNum")
    @Mapping(target = "stuffCode", source = "stuffCode")
    @Mapping(target = "productName", source = "productName")
    @Mapping(target = "pnCode", source = "pnCode")
    @Mapping(target = "inspection", source = "inspection")
    @Mapping(target = "productStatus", source = "productStatus")
    TargetedInventoryPreparationProductOwnVO toOwnVo(CrmProjectProductOwnVO crmProjectProductOwnVo);

}
