package com.topsec.crm.flow.core.controller.borrowForSell;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.util.WebFilenameUtils;
import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellAgentPsnInputExcelVO;
import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellProductDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BorrowForSell;
import com.topsec.crm.flow.core.entity.BorrowForSellProduct;
import com.topsec.crm.flow.core.service.IBorrowForSellProductService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <p>
 * 借转销产品信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/borrowForSellProduct")
@Tag(name = "借转销产品接口", description = "/borrowForSellProduct")
public class BorrowForSellProductController extends BaseController {

    @Autowired
    private IBorrowForSellProductService borrowForSellProductService;

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @PreFlowPermission
    @GetMapping("/queryBorrowForSellProduct")
    @Operation(summary = "查询借转销产品信息")
    public JsonObject<List<BorrowForSellProductDTO>> queryBorrowForSellProduct(@RequestParam String borrowId) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            List<BorrowForSellProduct> result =  borrowForSellProductService.queryBorrowForSellProduct(borrowId);
            return new JsonObject<List<BorrowForSellProductDTO>>(HyperBeanUtils.copyListPropertiesByJackson(result, BorrowForSellProductDTO.class));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @GetMapping("/queryBorrowForSellProductOfAgent")
    @Operation(summary = "查询借转销产品信息（国代节点）")
    public JsonObject<List<BorrowForSellProductDTO>> queryBorrowForSellProductOfAgent(@RequestParam String borrowId) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            List<BorrowForSellProduct> result =  borrowForSellProductService.queryBorrowForSellProductOfAgent(borrowId);
            return new JsonObject<List<BorrowForSellProductDTO>>(HyperBeanUtils.copyListPropertiesByJackson(result, BorrowForSellProductDTO.class));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @GetMapping("/queryBorrowForSellProductWithSelectable")
    @Operation(summary = "查询借转销产品的国代可选产品信息")
    public JsonObject<List<BorrowForSellProductDTO>> queryBorrowForSellProductWithSelectable(@RequestParam String borrowId, @RequestParam String customerName) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            List<BorrowForSellProduct> result =  borrowForSellProductService.queryBorrowForSellProductWithSelectable(borrowId,customerName);
            return new JsonObject<List<BorrowForSellProductDTO>>(HyperBeanUtils.copyListPropertiesByJackson(result, BorrowForSellProductDTO.class));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @PostMapping("/saveBorrowForSellSelectableProduct")
    @Operation(summary = "保存借转销产品的国代可借信息")
    public JsonObject<Boolean> saveBorrowForSellSelectableProduct(@RequestParam String borrowId, @RequestParam Integer canBorrow, @RequestParam String customerName,@RequestParam String customerId, @RequestBody List<BorrowForSellProductDTO> productList) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            Boolean result =  borrowForSellProductService.saveBorrowForSellSelectableProduct(borrowId,canBorrow,customerName,customerId, productList);
            return new JsonObject<Boolean>(result);
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @PostMapping("/exportBorrowForSellAgentPsn")
    @Operation(summary = "导出借转销国代出货序列号信息")
    public void exportBorrowForSellAgentPsn(String borrowId, HttpServletResponse response)   {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        List<BorrowForSellAgentPsnInputExcelVO> list = borrowForSellProductService.queryBorrowForSellPsnOfAgent(borrowId);
        if(CollectionUtils.isNotEmpty(list)){
            ExportParams exportParams=new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setSheetName("借转销国代出货序列号信息");
            Map<String,Object> sheet1Map = new HashMap<>();
            sheet1Map.put("title",exportParams);
            sheet1Map.put("data", list);
            sheet1Map.put("entity", BorrowForSellAgentPsnInputExcelVO.class);
            try (Workbook workbook = ExcelExportUtil.exportExcel(List.of(sheet1Map),ExcelType.XSSF)){
                response.addHeader(HttpHeaders.CONTENT_DISPOSITION, WebFilenameUtils.disposition("借转销国代出货序列号信息.xlsx"));
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
                workbook.write(response.getOutputStream());
            }catch (Exception e){
                throw new CrmException("导出失败", ResultEnum.FAIL.getResult(), e);
            }
        }
    }
}

