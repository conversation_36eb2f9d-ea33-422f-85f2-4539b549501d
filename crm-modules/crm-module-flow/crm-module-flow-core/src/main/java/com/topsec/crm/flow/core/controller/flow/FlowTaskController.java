package com.topsec.crm.flow.core.controller.flow;

import com.topsec.crm.flow.api.dto.contractreview.check.ContractConditionDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.error.ErrorDetail;
import com.topsec.crm.flow.core.mapper.ProcessExtensionInfoMapper;
import com.topsec.crm.flow.core.process.ProcessStateHandler;
import com.topsec.crm.flow.core.process.ProcessStateHandlerUtils;
import com.topsec.crm.flow.core.process.impl.TfsProcessService;
import com.topsec.crm.flow.core.service.ContractReviewFlowService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.PriceReviewProductLineActivityEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.enums.ActionTypeEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.query.FormTaskQuery;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.tfs.api.client.TfsHistoryClient;
import com.topsec.tfs.api.client.TfsTaskClient;
import com.topsec.tfs.api.client.TfsTodoClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.enums.RelationTypeEnum;
import com.topsec.tos.common.vo.process.DetailBaseVO;
import com.topsec.vo.FormContentTotal;
import com.topsec.vo.TfsFormContentVo;
import com.topsec.vo.node.ApproveNode;
import com.topsec.vo.task.TaskCommentVo;
import com.topsec.vo.task.TaskHistoryVo;
import com.topsec.vo.task.TfsTaskVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/task")
@Tag(name = "流程任务相关Controller", description = "/task")
@RequiredArgsConstructor
@Validated
public class FlowTaskController  extends BaseController {

    private final TfsTaskClient tfsTaskClient;

    private final TfsTodoClient tfsTodoClient;

    private final TfsFormContentClient tfsFormContentClient;

    private final TfsHistoryClient tfsHistoryClient;
    private final TosEmployeeClient tosEmployeeClient;


    private final ContractReviewFlowService contractReviewFlowService;

    private final TfsProcessService tfsProcessService;
    private final ProcessExtensionInfoMapper processExtensionInfoMapper;


    @GetMapping("/queryPriceReviewCommentList")
    @Operation(summary = "根据流程实例id查询价审的历史审批进度以及评论回复",
            description = "产品线审批的审批意见相互不可见（判断一级部门是否为同一个一级部门，不相同不可见）")
    @PreFlowPermission
    public JsonObject<List<TaskHistoryVo>> queryPriceReviewCommentList(@RequestParam String processInstanceId) {
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        JsonObject<List<TaskHistoryVo>> taskHistoryListOpt = tfsTaskClient.getTaskHistoryList(processInstanceId);
        List<TaskHistoryVo> taskHistoryVosList = Optional.ofNullable(taskHistoryListOpt)
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item->{
                    String activityId = item.getActivityId();
                    return PriceReviewProductLineActivityEnum.ids.contains(activityId);
                }).toList();
        //判断当前登录人是不是产品线审批节点的审批人
        List<TaskCommentVo> taskCommentVoList = taskHistoryVosList.stream()
                .map(TaskHistoryVo::getApprovalCommentList)
                .flatMap(item -> {
                    return ListUtils.emptyIfNull(item).stream();
                }).toList();
        Set<String> taskCommentPersonIds = taskCommentVoList.stream().map(TaskCommentVo::getPersonId).collect(Collectors.toSet());
        boolean onTheSpecialNode=taskCommentPersonIds.contains(currentPersonId);
        //不是特殊节点的审批人不过滤评论信息
        if (!onTheSpecialNode) return taskHistoryListOpt;

        //产品线审批节点审批人id
        taskCommentPersonIds.add(currentPersonId);
        List<RelationTypeEnum> relationTypeEnumList=List.of(RelationTypeEnum.MEMBER);
        Map<String, String> personIdDeptIdMap = Optional.ofNullable(tosEmployeeClient.findByIds(new ArrayList<>(taskCommentPersonIds), relationTypeEnumList))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(DetailBaseVO::getUuid, item -> {
                    return Optional.ofNullable(item.getDept())
                            .map(dept -> {
                                return dept.getDeptIdByLevel(1);
                            }).orElse("");
                }));
        String currentDeptId = personIdDeptIdMap.get(currentPersonId);
        for (TaskCommentVo taskCommentVo : taskCommentVoList) {
            String personId = taskCommentVo.getPersonId();
            String deptId = personIdDeptIdMap.get(personId);
            // 判断一级部门是否为同一个一级部门，不相同不可见
            if (!currentDeptId.equals(deptId)){
                taskCommentVo.setMessage("");
            }
        }

        return taskHistoryListOpt;
    }


    @GetMapping("/queryCommentList")
    @Operation(summary = "根据流程实例id查询历史审批进度以及评论回复")
    @PreFlowPermission
    public JsonObject<List<TaskHistoryVo>> queryCommentList(@RequestParam String processInstanceId) {
        return tfsTaskClient.getTaskHistoryList(processInstanceId);
    }

    @GetMapping("/queryContractAuthCommentList")
    @Operation(summary = "根据流程实例id查询合同评审历史审批进度以及评论回复(有节点权限验证)")
    @PreFlowPermission
    public JsonObject<List<TaskHistoryVo>> queryContractAuthCommentList(@RequestParam String processInstanceId) {
        String currentAccountId = getCurrentAccountId();
        return Optional.ofNullable(tfsTaskClient.getTaskHistoryList(processInstanceId)).map(
                        taskHistoryVo->{
                    tfsProcessService.authCommentList(taskHistoryVo.getObjEntity(),currentAccountId);
                    return taskHistoryVo;
                })
                .orElseThrow(() -> new CrmException("查询审批意见失败"));
    }

    @PostMapping("/todoList")
    @Operation(summary = "查询待审批列表")
    @PreAuthorize
    public JsonObject<PageUtils<TfsFormContentVo>> todoList(@RequestBody FormTaskQuery formTaskQuery){
        formTaskQuery.setPlatformCode(getCurrentAudience());
        formTaskQuery.setUserId(getCurrentAccountId());

        return tfsProcessService.updateBatchCompanyName(tfsTodoClient.todolist(formTaskQuery).convert(PageUtils::new));
    }



    @GetMapping("/findFormContentTotal")
    @Operation(summary = "查询待审批数量")
    @PreAuthorize
    public JsonObject<FormContentTotal> findFormContentTotal(){
        return tfsTodoClient.findFormContentTotal(getCurrentAccountId(),getCurrentAudience());
    }


    @PostMapping("/queryListUserHistoricalApplications")
    @Operation(summary = "查询申请记录")
    @PreAuthorize(hasPermission = "crm_flow_apply_list",dataScope = "crm_flow_apply_list")
    public JsonObject<PageUtils<TfsFormContentVo>>queryListUserHistoricalApplications(@RequestBody FormTaskQuery formTaskQuery){
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        formTaskQuery.setPlatformCode(getCurrentAudience());
        if (!dataScopeParam.getAllScope()){
            Set<String> deptIdList = dataScopeParam.getDeptIdList();
            if (CollectionUtils.isNotEmpty(deptIdList)){
                formTaskQuery.setDeptIdList(new ArrayList<>(deptIdList));
            }else {
                formTaskQuery.setUserId(getCurrentAccountId());
            }
        }

        return tfsProcessService.updateBatchCompanyName(tfsHistoryClient.findListUserHistoricalApplications(formTaskQuery).convert(PageUtils::new));
    }



    @PostMapping("/queryMyApplicationList")
    @Operation(summary = "查询我的申请列表")
    @PreAuthorize
    public JsonObject<PageUtils<TfsFormContentVo>>queryMyApplicationList(@RequestBody FormTaskQuery formTaskQuery){
        formTaskQuery.setPlatformCode(getCurrentAudience());
        formTaskQuery.setUserId(getCurrentAccountId());
        return tfsProcessService.updateBatchCompanyName(tfsFormContentClient.myApplicationList(formTaskQuery).convert(PageUtils::new));
    }


    @PostMapping("/doneList")
    @Operation(summary = "查询已审批列表")
    @PreAuthorize
    public JsonObject<PageUtils<TfsFormContentVo>> doneList(@RequestBody FormTaskQuery formTaskQuery){
        formTaskQuery.setPlatformCode(getCurrentAudience());
        formTaskQuery.setUserId(getCurrentAccountId());
        return tfsProcessService.updateBatchCompanyName(tfsHistoryClient.doneList(formTaskQuery).convert(PageUtils::new));
    }

    @PostMapping("/completeTask")
    @Operation(summary = "流程审批")
    @PreAuthorize
    public JsonObject<Void> completeTask(@RequestBody TfsTaskVo tfsTaskVo){
        tfsTaskVo.setActionTypeEnum(ActionTypeEnum.APPROVAL);
        processCheck(tfsTaskVo);
        tfsTaskVo.setUserId(getCurrentAccountId());
        return tfsTaskClient.completeTask(tfsTaskVo);
    }


    /**
     * 流程驳回、流程终止
     * @param tfsTaskVo
     * @return
     */
    @PostMapping("/rejectTask")
    @Operation(summary = "流程终止")
    @PreAuthorize
    JsonObject<Void> rejectTask(@RequestBody TfsTaskVo tfsTaskVo){
        tfsTaskVo.setUserId(getCurrentAccountId());
        return tfsTaskClient.rejectTask(tfsTaskVo);
    }


    /**
     * 流程直接办结
     * @param tfsTaskVo
     * @return
     */
    @PostMapping("/stopTask")
    @Operation(summary = "流程办结")
    @PreAuthorize
    JsonObject<Void> stopTask(@RequestBody TfsTaskVo tfsTaskVo){
        tfsTaskVo.setUserId(getCurrentAccountId());
        return tfsTaskClient.stopTask(tfsTaskVo);
    }


    @PostMapping("/handleCompletedProcessRejection")
    @Operation(summary = "流程办结退回")
    @PreAuthorize
    JsonObject<Boolean> handleCompletedProcessRejection(@RequestBody TfsTaskVo tfsTaskVo){
        tfsTaskVo.setUserId(getCurrentAccountId());
        tfsTaskVo.setActionTypeEnum(ActionTypeEnum.ENDBACK);
        return tfsTaskClient.processRollBack(tfsTaskVo.getProcessInstanceId());
    }

    /**
     * 流程加签转办
     * @param tfsTaskVo
     * @return
     */
    @PostMapping(value = "/addSignTask")
    @Operation(summary = "流程转办")
    @PreAuthorize
    JsonObject<Void> addSignTask(@RequestBody TfsTaskVo tfsTaskVo){
        List<String> signPersonIds = tfsTaskVo.getSignAccountIds();
        if (CollectionUtils.isEmpty(signPersonIds)){
            return JsonObject.error("请选择转办人");
        }
        if (signPersonIds.size() > 1){
            return JsonObject.error("请选择一个转办人");
        }
        Integer addSignType = tfsTaskVo.getAddSignType();
        if (addSignType == null){
            return JsonObject.error("请选择加签类型");
        }
        tfsTaskVo.setUserId(getCurrentAccountId());
        return tfsTaskClient.addSignTask(tfsTaskVo);
    }


//    /**
//     * 流程拒单跳转
//     *
//     * @param tfsTaskVo
//     * @return
//     */
//    @PostMapping(value = "/taskJump")
//    JsonObject<Void> taskJump(@RequestBody TfsTaskVo tfsTaskVo){
//        tfsTaskVo.setUserId(getCurrentAccountId());
//        String currentNodeId = tfsTaskVo.getCurrentNodeId();
//        if (currentNodeId.equals("sid-9D4E9504-3166-4E0A-B0A3-767A830141E1")){
//            //省代拒单
//            return tfsTaskClient.taskJump(tfsTaskVo);
//        }else if (currentNodeId.equals("sid-0D23126B-4A0A-45CC-BF90-84A18C8BC86C")){
//            //国代拒单
//            return tfsTaskClient.stopTask(tfsTaskVo);
//        }
//        return JsonObject.error("拒单失败");
//    }

    /**
     * 流程退回
     * @param tfsTaskVo
     * @return
     */
    @PostMapping(value = "/returnTask")
    @Operation(summary = "流程退回")
    @PreAuthorize
    JsonObject<Boolean> returnTask(@RequestBody TfsTaskVo tfsTaskVo){
        tfsTaskVo.setActionTypeEnum(ActionTypeEnum.BACK);
        processCheck(tfsTaskVo);
        tfsTaskVo.setUserId(getCurrentAccountId());
        return tfsTaskClient.returnTask(tfsTaskVo);
    }

    private void processCheck(TfsTaskVo tfsTaskVo) {
        String processInstanceId = tfsTaskVo.getProcessInstanceId();
        ProcessStateHandler processStateHandler = ProcessStateHandlerUtils.getHandlerByProcessInstanceId(processInstanceId);
        Pair<Boolean, String> result = processStateHandler.checkFilling(tfsTaskVo);
        if (result!=null && !result.getLeft()) {
            throw new CrmException(result.getRight());
        }
    }

    @PostMapping(value = "/queryBackNodeIdList")
    @Operation(summary = "退回节点列表")
    @PreAuthorize
    JsonObject<List<ApproveNode> > queryBackNodeIdList(@RequestBody TfsTaskVo tfsTaskVo){
        tfsTaskVo.setUserId(getCurrentAccountId());
        return tfsTaskClient.queryBackNodeIdList(tfsTaskVo);
    }


    @PostMapping(value = "/checkContractCondition")
    @Operation(summary = "合同评审审批校验")
    @PreAuthorize
    JsonObject<List<ErrorDetail> > checkContractCondition(@RequestBody ContractConditionDTO contractConditionDTO){
        List<ErrorDetail> errorDetails = contractReviewFlowService.checkContractConditions(contractConditionDTO);
        return new JsonObject<>(errorDetails);
    }

    @GetMapping("/isTaskApproved")
    @Operation(summary = "查询流程某个节点是否审批过")
    @PreAuthorize
    public JsonObject<Boolean> isTaskApproved(@RequestParam String processInstanceId, @RequestParam String activityId){
        return tfsHistoryClient.isTaskApproved(processInstanceId,activityId);
    }



    @PostMapping(value = "/addUTaskTag")
    @PreAuthorize
    @Operation(summary = "添加任务标签")
    public JsonObject<Boolean> addUTaskTag(@RequestParam String taskId, @RequestParam String tag){
        CrmAssert.hasText(taskId, "taskId不能为空");
        CrmAssert.hasText(tag, "标签不能为空");
        return tfsTaskClient.addUTaskTag(taskId,tag);
    }



    @PostMapping(value = "/updateTaskTag")
    @PreAuthorize
    @Operation(summary = "修改任务标签")
    public JsonObject<Boolean> updateTaskTag(@RequestParam String taskId, @RequestParam String newTag){
        CrmAssert.hasText(taskId, "taskId不能为空");
        CrmAssert.hasText(newTag, "标签不能为空");
        return tfsTaskClient.updateTaskTag(taskId,newTag);
    }


    @PostMapping(value = "/removeTaskTag")
    @PreAuthorize
    @Operation(summary = "删除任务标签")
    public JsonObject<Boolean> removeTaskTag(@RequestParam String taskId){
        CrmAssert.hasText(taskId, "taskId不能为空");
        return tfsTaskClient.removeTaskTag(taskId);
    }





}
