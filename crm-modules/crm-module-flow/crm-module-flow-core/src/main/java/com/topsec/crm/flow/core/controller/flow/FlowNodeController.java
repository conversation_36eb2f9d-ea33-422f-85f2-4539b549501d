package com.topsec.crm.flow.core.controller.flow;

import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.vo.node.ApproveNode;
import com.topsec.vo.node.ProcessVariableNameVo;
import com.topsec.vo.node.TfsNextNodeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/node")
@Tag(name = "流程节点相关Controller", description = "/node")
@RequiredArgsConstructor
@Validated
public class FlowNodeController  extends BaseController {

    private final TfsNodeClient tfsNodeClient;
    @PostMapping(value = "/queryNodeId")
    @Operation(summary = "查询下一节点信息")
    @PreFlowPermission
    public JsonObject<Set<ApproveNode>> queryNodeId(@RequestBody TfsNextNodeVo tfsNextNodeVo){
        PreFlowPermissionAspect.checkProcessInstanceId(tfsNextNodeVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return tfsNodeClient.queryNextNode(tfsNextNodeVo);
    }


    @PostMapping("/showCurrentNode")
    @Operation(summary = "查询当前节点信息")
    @PreFlowPermission
    public JsonObject<ApproveNode> showCurrentNode(@RequestParam String taskId){
        return tfsNodeClient.showCurrentNode(taskId);

    }

    @Operation(summary = "查询当前节点可选择的节点列表")
    @PostMapping(value = "queryPossibleNextNodeIdList")
    @PreFlowPermission
    public JsonObject<LinkedHashSet<ApproveNode>> queryPossibleNextNodeIdList(@RequestBody TfsNextNodeVo tfsNextNodeVo){
        PreFlowPermissionAspect.checkProcessInstanceId(tfsNextNodeVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return tfsNodeClient.queryPossibleNextNodeIds(tfsNextNodeVo);
    }


    @Operation(summary = "查询当前节点可选择的节点列表")
    @PostMapping(value = "queryPossibleNextNodeIdListNew")
    @PreFlowPermission
    public JsonObject<LinkedHashSet<ApproveNode>> queryPossibleNextNodeIdListNew(@RequestBody TfsNextNodeVo tfsNextNodeVo){
        PreFlowPermissionAspect.checkProcessInstanceId(tfsNextNodeVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return tfsNodeClient.queryPossibleNextNodeIdsNew(tfsNextNodeVo);
    }

    @Operation(summary = "查询当前节点网关变量的值")
    @PostMapping(value = "/queryProcessVariableName")
    @PreFlowPermission
    public JsonObject<Map<String, Object>> queryProcessVariableName(@RequestBody ProcessVariableNameVo processVariableNameVo){
        PreFlowPermissionAspect.checkProcessInstanceId(processVariableNameVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return tfsNodeClient.queryProcessVariableName(processVariableNameVo);
    }


}
