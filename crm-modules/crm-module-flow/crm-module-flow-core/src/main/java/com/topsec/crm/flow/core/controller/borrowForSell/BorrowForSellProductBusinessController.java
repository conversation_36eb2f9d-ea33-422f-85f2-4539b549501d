package com.topsec.crm.flow.core.controller.borrowForSell;

import com.topsec.crm.flow.api.dto.borrowForSell.*;
import com.topsec.crm.flow.core.entity.BorrowForSell;
import com.topsec.crm.flow.core.entity.BorrowForSellProduct;
import com.topsec.crm.flow.core.service.IBorrowForSellProductService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <p>
 * 借转销产品信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/business/borrowForSellProduct")
@Tag(name = "借转销产品业务接口", description = "/business/borrowForSellProduct")
public class BorrowForSellProductBusinessController extends BaseController {

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @Autowired
    private IBorrowForSellProductService borrowForSellProductService;

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_details", dataScope = "crm_flow_borrow_for_sell_details")
    @GetMapping("/queryBorrowForSellProduct")
    @Operation(summary = "查询借转销产品信息")
    public JsonObject<List<BorrowForSellProductDTO>> queryBorrowForSellProduct(@RequestParam String borrowId) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList())
                || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(byId.getSalesmanPersonId())
                || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(byId.getCreateUser())){
            List<BorrowForSellProduct> result =  borrowForSellProductService.queryBorrowForSellProduct(borrowId);
            return new JsonObject<List<BorrowForSellProductDTO>>(HyperBeanUtils.copyListPropertiesByJackson(result, BorrowForSellProductDTO.class));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_gd_details", dataScope = "crm_flow_borrow_for_sell_gd_details")
    @GetMapping("/queryBorrowForSellProductOfAgent")
    @Operation(summary = "查询借转销产品信息（国代节点）")
    public JsonObject<List<BorrowForSellProductDTO>> queryBorrowForSellProductOfAgent(@RequestParam String borrowId) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList())
                || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(byId.getSalesmanPersonId())
                || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(byId.getCreateUser())){
            List<BorrowForSellProduct> result =  borrowForSellProductService.queryBorrowForSellProductOfAgent(borrowId);
            return new JsonObject<List<BorrowForSellProductDTO>>(HyperBeanUtils.copyListPropertiesByJackson(result, BorrowForSellProductDTO.class));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }
}

