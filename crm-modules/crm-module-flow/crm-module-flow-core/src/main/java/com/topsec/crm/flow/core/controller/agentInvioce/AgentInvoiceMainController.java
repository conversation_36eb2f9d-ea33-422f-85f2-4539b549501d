package com.topsec.crm.flow.core.controller.agentInvioce;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.flow.api.dto.agentInvoice.AgentInvoiceCollectionInfo;
import com.topsec.crm.flow.api.dto.agentInvoice.AgentInvoiceLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.AgentInvoiceAttachment;
import com.topsec.crm.flow.core.entity.AgentInvoiceMain;
import com.topsec.crm.flow.core.process.impl.AgentInvoiceProcessService;
import com.topsec.crm.flow.core.service.AgentInvoiceAttachmentService;
import com.topsec.crm.flow.core.service.AgentInvoiceMainService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/agentInvioceContact")
@Tag(name = "总代开票", description = "/agentInvioceContact")
@RequiredArgsConstructor
public class AgentInvoiceMainController extends BaseController {

    private final AgentInvoiceMainService agentInvoiceMainService;

    private final AgentInvoiceProcessService agentInvoiceProcessService;

    private final AgentInvoiceAttachmentService attachmentService;
    @PostMapping("/launch")
    @Operation(summary = "发起总代开票流程")
    @PreAuthorize(hasPermission = "crm_dynasty_invoice", dataScope = "crm_dynasty_invoice")
    public JsonObject<Boolean> launch(@RequestBody AgentInvoiceLaunchDTO launchDTO) {
       return new JsonObject<>(agentInvoiceProcessService.launch(launchDTO));
    }

    @GetMapping("/getAgentInvoiceDetailByProcessInstanceId")
    @Operation(summary = "根据总代开票实例ID查询流程详细信息")
    @PreFlowPermission
    public JsonObject<AgentInvoiceLaunchDTO> getAgentInvoiceDetailByProcessInstanceId(String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(agentInvoiceMainService.getAgentInvoiceDetailByProcessInstanceId(processInstanceId));

    }
    @PostMapping("/fillCollectionInfo")
    @Operation(summary = "总代开票-填写开票信息")
    @PreFlowPermission
    public JsonObject<Boolean> fillCollectionInfo(@RequestBody AgentInvoiceCollectionInfo info) {
        PreFlowPermissionAspect.checkProcessInstanceId(info.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        UpdateWrapper<AgentInvoiceMain> updateWrapper = new UpdateWrapper<AgentInvoiceMain>()
                .eq("process_instance_id", info.getProcessInstanceId());

        if (info.getInvoiceNo() != null) {
            updateWrapper.set("invoice_no", info.getInvoiceNo());
        }

        if (info.getDeliveryNo() != null) {
            updateWrapper.set("delivery_no", info.getDeliveryNo());
        }

        //3.保存附件信息
        List<AgentInvoiceAttachment> attachments = HyperBeanUtils.copyListPropertiesByJackson(info.getAttachmentDTOs(), AgentInvoiceAttachment.class);
        for (AgentInvoiceAttachment attachment : attachments) {
            attachment.setProcessInstanceId(HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            attachment.setCreateUserName(NameUtils.getName(UserInfoHolder.getCurrentPersonId()));
        }
        if(CollectionUtil.isNotEmpty(attachments)) {
            attachmentService.saveBatch(attachments);
        }

        return new JsonObject<>(agentInvoiceMainService.update(updateWrapper));
    }
}
