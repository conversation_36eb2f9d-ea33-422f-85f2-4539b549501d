package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.contract.api.entity.CrmContractProductOwnVO;
import com.topsec.crm.contract.api.entity.CrmContractProductThirdVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDetailDTO;
import com.topsec.crm.flow.api.dto.performancereport.ContractDeliveryProductVO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteVO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.returnexchange.*;
import com.topsec.crm.flow.api.vo.ProcessFileInfoVO;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.service.impl.ReturnExchangeServiceImpl;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.crm.project.api.entity.CrmProjectProductThirdVo;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 退换货转换器
 * <AUTHOR>
 */
@Mapper
public interface ReturnExchangeConvertor {
    ReturnExchangeConvertor INSTANCE = Mappers.getMapper(ReturnExchangeConvertor.class);


    @Mapping(target = "parentId", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "type", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    ReturnExchangeProduct toReturnExchangeProduct(ReturnExchangeProductVO dto);

    List<ReturnExchangeProduct> toReturnExchangeProduct(List<ReturnExchangeProductVO> dto);

    @Mapping(target = "delFlag", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "type", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    ReturnExchangeProductThird toReturnExchangeThird(ReturnExchangeProductThirdVO dto);

    List<ReturnExchangeProductThird> toReturnExchangeThird(List<ReturnExchangeProductThirdVO> dto);

    @Mapping(target = "warehouseDelivery", ignore = true)
    @Mapping(target = "returnNum", ignore = true)
    @Mapping(target = "oldProductNum", ignore = true)
    @Mapping(target = "child", ignore = true)
    @Mapping(target = "fees", ignore = true)
    @Mapping(target = "updateUserName", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    ReturnExchangeProductVO toReturnExchangeProductVO(ReturnExchangeProduct baseInfo);

    List<ReturnExchangeProductVO> toReturnExchangeProductVO(List<ReturnExchangeProduct> baseInfo);

    @AfterMapping
    default void setName(@MappingTarget ReturnExchangeProductVO vo) {
        NameUtils.setName(vo);
    }





    @Mapping(target = "supplierCustomId", ignore = true)
    @Mapping(target = "productLineRequiredList", ignore = true)
    @Mapping(target = "rebateReplaceOs", ignore = true)
    @Mapping(target = "finalDocId", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "processState", ignore = true)
    @Mapping(target = "processNumber", ignore = true)

    @Mapping(target = "sm", ignore = true)
    @Mapping(target = "invoicedAmount", ignore = true)
    @Mapping(target = "agentConfirmReceiving", ignore = true)

    @Mapping(target = "signCompanyName", source = "execute.supplierName")
    @Mapping(target = "signCompanyId", source = "execute.supplierId")
    @Mapping(target = "contractCompanyName", source = "execute.channelCompanyName")
    @Mapping(target = "contractCompanyId", source = "execute.channelCompanyId")
    @Mapping(target = "contractNumber",source = "launch.contractNumber")
    PerformanceReportReturnExchange toPerformanceReturnExchange(PerformanceReportReturnExchangeLaunchDTO launch,
                                                                PerformanceExecute execute);


    @Mapping(target = "passed03", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "processState", ignore = true)
    @Mapping(target = "processNumber", ignore = true)

    @Mapping(target = "sm", ignore = true)

    @Mapping(target = "signCompanyId", source = "execute.signingCompany")
    @Mapping(target = "signCompanyName", source = "execute.signingCompanyName")
    @Mapping(target = "saleDeptId", source = "execute.contractOwnerDeptId")
    @Mapping(target = "saleDeptName", source = "execute.contractOwnerDeptName")
    @Mapping(target = "saleId", source = "execute.contractOwnerId")
    @Mapping(target = "saleName", source = "execute.contractOwnerName")
    @Mapping(target = "contractNumber", source = "launch.contractNumber")
    ContractReviewReturnExchange toContractReturnExchange(ContractReviewReturnExchangeLaunchDTO launch,
                                                          CrmContractExecuteVO execute);


    @Mapping(target = "fees", ignore = true)
    @Mapping(target = "updateUserName", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "id", ignore = true)

    @Mapping(target = "invoicedAmount", ignore = true)
    @Mapping(target = "psn", ignore = true)

    @Mapping(target = "recordId", source = "projectProductOwnId")
    ReturnExchangeProductVO contractToReturnExchangeProductVO(CrmContractProductOwnVO own);

    List<ReturnExchangeProductVO> contractToReturnExchangeProductVO(List<CrmContractProductOwnVO> own);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "contractReviewMainId", ignore = true)
    @Mapping(target = "dueDate", source = "shouldPayTime")
    @Mapping(target = "payableAmount", source = "shouldPayMoney")
    @Mapping(target = "provisionType", source = "paymentType")
    @Mapping(target = "paymentTerms", source = "paymentCondition")
    @Mapping(target = "paymentProvision", source = "paymentClause")
    @Mapping(target = "remark", source = "remarks")
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    ContractReviewPaymentProvision returnExchangePaymentToContractPayment(ReturnExchangePaymentClauseVO paymentClauseVOS);

    List<ContractReviewPaymentProvision> returnExchangePaymentToContractPayment(List<ReturnExchangePaymentClauseVO> paymentClauseVOS);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "returnExchangeOwnId", source = "recordOwnId")
    @Mapping(target = "returnExchangeThirdId", source = "recordThirdId")
    ContractReviewRevenueRecognition returnExchangeRevenueToContractRevenue(ReturnExchangeRevenueRecognitionVO vo);

    List<ContractReviewRevenueRecognition> returnExchangeRevenueToContractRevenue(List<ReturnExchangeRevenueRecognitionVO> vo);

    @Mapping(target = "fees", ignore = true)
    @Mapping(target = "updateUserName", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "id", ignore = true)

    @Mapping(target = "invoicedAmount", ignore = true)

    @Mapping(target = "recordId", source = "projectProductThirdId")
    ReturnExchangeProductThirdVO contractToReturnExchangeProductThirdVO(CrmContractProductThirdVO thirdVO);

    List<ReturnExchangeProductThirdVO> contractToReturnExchangeProductThirdVO(List<CrmContractProductThirdVO> thirdVOS);

    @Mapping(target = "payWay", expression = "java(own.getFeeWaiver()?null:own.getPayWay())")
    ReturnExchangeFeeVO toReturnExchangeFeeVO(ReturnExchangeFee own);
    List<ReturnExchangeFeeVO> toReturnExchangeFeeVO(  List<ReturnExchangeFee> own);

    @Mapping(target = "projectId", ignore = true)
    @Mapping(target = "tempProductNum", ignore = true)
    @Mapping(target = "supplier", ignore = true)
    @Mapping(target = "state", ignore = true)
    @Mapping(target = "splitOutsourcePrice", ignore = true)
    @Mapping(target = "specialPreparation", ignore = true)
    @Mapping(target = "saleAccess", ignore = true)
    @Mapping(target = "reportStatus", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "rebatePrice", ignore = true)
    @Mapping(target = "qpvs", ignore = true)
    @Mapping(target = "purchasePrice", ignore = true)
    @Mapping(target = "profile", ignore = true)
    @Mapping(target = "productType", ignore = true)
    @Mapping(target = "productStatus", ignore = true)
    @Mapping(target = "productSpecification", ignore = true)
    @Mapping(target = "productLine1", ignore = true)
    @Mapping(target = "productCategory", ignore = true)
    @Mapping(target = "processInfoList", ignore = true)
    @Mapping(target = "priceState", ignore = true)
    @Mapping(target = "inspection", ignore = true)
    @Mapping(target = "hostSerialNumber", ignore = true)
    @Mapping(target = "hasDistributionRel", ignore = true)
    @Mapping(target = "hasChild", ignore = true)
    @Mapping(target = "grossMarginRatio", ignore = true)
    @Mapping(target = "grossMargin", ignore = true)
    @Mapping(target = "frontEdit", ignore = true)
    @Mapping(target = "fission", ignore = true)
    @Mapping(target = "financialGrossMargin", ignore = true)
    @Mapping(target = "expiryDate", ignore = true)
    @Mapping(target = "customizedType", ignore = true)
    @Mapping(target = "crmProjectProductSn", ignore = true)
    @Mapping(target = "crmProjectProductOwnServiceRange", ignore = true)
    @Mapping(target = "crmProjectProductOwnService", ignore = true)
    @Mapping(target = "crmProjectProductMaintain", ignore = true)
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "attr", ignore = true)
    CrmProjectProductOwnVO toCrmProjectProductOwnVO(ReturnExchangeProduct returnExchangeProduct);
    List<CrmProjectProductOwnVO> toCrmProjectProductOwnVO(List<ReturnExchangeProduct> returnExchangeProduct);

    @Mapping(target = "delFlag", ignore = true)
    CrmProjectProductThirdVo toCrmProjectProductThirdVO(ReturnExchangeProductThird returnExchangeProduct);

    List<CrmProjectProductThirdVo> toCrmProjectProductThirdVO(List<ReturnExchangeProductThird> returnExchangeProduct);


    @Mapping(target = "projectId", ignore = true)
    @Mapping(target = "isClassified", ignore = true)
    @Mapping(target = "invoiceAmount", ignore = true)
    @Mapping(target = "contractId", ignore = true)

    @Mapping(target = "signCompanyName", source = "supplierName")
    @Mapping(target = "processInstanceId", source = "performanceProcessInstanceId")
    @Mapping(target = "contractOwnerName", source = "saleName")
    @Mapping(target = "contractOwnerId", source = "saleId")
    @Mapping(target = "contractOwnerDeptName", source = "saleDeptName")
    @Mapping(target = "contractOwnerDeptId", source = "saleDeptId")
    @Mapping(target = "contractCompanyName", source = "channelCompanyName")
    @Mapping(target = "contractCompanyId", source = "channelCompanyId")
    ReturnExchangeBaseVO toReturnExchangeBaseVO(PerformanceExecuteVO performanceExecuteVO);

    @Mapping(target = "signCompanyName", source = "signingCompanyName")
    ReturnExchangeBaseVO toReturnExchangeBaseVO(CrmContractExecuteVO contractExecuteVO);

    List<ReturnExchangeBaseVO> toReturnExchangeBaseVO(List<CrmContractExecuteVO> contractExecuteVO);

    @Mapping(target = "warehouseDelivery", ignore = true)
    @Mapping(target = "customizedType", ignore = true)
    @Mapping(target = "updateUserName", ignore = true)
    @Mapping(target = "psn", ignore = true)
    @Mapping(target = "invoicedAmount", ignore = true)
    @Mapping(target = "fees", ignore = true)
    @Mapping(target = "crmProjectProductOwnServiceRange", ignore = true)
    @Mapping(target = "crmProjectProductOwnService", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "child", ignore = true)
    @Mapping(target = "id", ignore = true)

    @Mapping(target = "recordId", source = "projectRecordId")
    @Mapping(target = "productNum", source = "productNum")
    @Mapping(target = "oldProductNum", source = "productNum")
    ReturnExchangeProductVO performanceToReturnExchangeProductVO(PerformanceReportProductOwn performanceReportProductOwn);


    @Mapping(target = "productLineRequiredForApply", ignore = true)
    @Mapping(target = "productLineRequiredFinal", ignore = true)
    @Mapping(target = "createUserDeptId", ignore = true)
    @Mapping(target = "reThirdProduct", ignore = true)
    @Mapping(target = "newThirdProduct", ignore = true)
    @Mapping(target = "revenueRecognition", ignore = true)

    @Mapping(target = "returnedProductAmount",expression ="java(com.topsec.crm.flow.core.util.PriceUtils.getReturnExchangeVOTotalPrice(reProduct,null))")
    @Mapping(target = "newProductAmount", expression ="java(com.topsec.crm.flow.core.util.PriceUtils.getReturnExchangeVOTotalPrice(newProduct,null))")
    ReturnExchangeDetailVO performanceDetail(PerformanceReportReturnExchange returnExchange,
                                             List<ReturnExchangeProductVO> reProduct,
                                             List<ReturnExchangeProductVO> newProduct,
                                             List<PerformanceReportContractDeliveryDTO> contractDeliveryList,
                                             List<PerformanceReportPaymentTerms> paymentClause,
                                             List<ProcessFileInfoVO> attachments,
                                             List<ReturnExchangeFeeVO> fees,
                                             String performanceReportId,
                                             String projectId);

    @Mapping(target = "splitOutsourcePrice", ignore = true)
    @Mapping(target = "specificationId", ignore = true)
    @Mapping(target = "scarceGoodsOrderNum", ignore = true)
    @Mapping(target = "returnNum", ignore = true)
    @Mapping(target = "reportStatus", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "rebateTotalPrice", ignore = true)
    @Mapping(target = "rebatePrice", ignore = true)
    @Mapping(target = "productSpecification", ignore = true)
    @Mapping(target = "isSpecialItem", ignore = true)
    @Mapping(target = "isBorrowForForward", ignore = true)
    @Mapping(target = "hostSerialNumber", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    @Mapping(target = "attr", ignore = true)

    @Mapping(target = "projectRecordId", source = "recordId")
    @Mapping(target = "returnExchangeProcessInstanceId", source = "processInstanceId")
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "productNum",  expression = "java(source.getType()!=4?source.getProductNum()*-1:source.getProductNum())")
    PerformanceReportProductOwn reToProductOwn(ReturnExchangeProduct source);
    List<PerformanceReportProductOwn> reToProductOwn(List<ReturnExchangeProduct> returnExchangeProduct);


    @Mapping(target = "returnExchangeProcessInstanceId", source = "processInstanceId")
    @Mapping(target = "termType", expression = "java(paymentTermsDTO.getPaymentType() == 0 ? 1 : 0)")
    @Mapping(target = "termDescription", source = "paymentClause")
    @Mapping(target = "amountDue", source = "shouldPayMoney")
    @Mapping(target = "dueDate", source = "shouldPayTime")

    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    PerformanceReportPaymentTerms reToTerms(ReturnExchangePaymentClauseVO paymentTermsDTO);

    List<PerformanceReportPaymentTerms> reToTerms(List<ReturnExchangePaymentClauseVO> paymentTermsDTO);


    @Mapping(target = "updateUserName", ignore = true)
    @Mapping(target = "remarks", ignore = true)
    @Mapping(target = "paymentCondition", ignore = true)
    @Mapping(target = "createUserName", ignore = true)

    @Mapping(target = "processInstanceId", source = "returnExchangeProcessInstanceId")
    @Mapping(target = "paymentType", expression = "java(terms.getTermType() == 0 ? 1 : 0)")
    @Mapping(target = "paymentClause", source = "termDescription")
    @Mapping(target = "shouldPayMoney", source = "amountDue")
    @Mapping(target = "shouldPayTime", source = "dueDate")
    ReturnExchangePaymentClauseVO termsToRe(PerformanceReportPaymentTerms terms);

    // ContractDeliveryDTO发货信息 ContractDeliveryDetailDTO发货产品

    @Mapping(target = "returnChangeId", ignore = true)
    @Mapping(target = "performanceReportId", ignore = true)
    @Mapping(target = "performanceDeliveryId", ignore = true)
    @Mapping(target = "originalContractNumber", ignore = true)
    @Mapping(target = "logisticsDTOS", ignore = true)
    @Mapping(target = "isInvalidContract", ignore = true)
    @Mapping(target = "deliveryTimeliness", ignore = true)
    @Mapping(target = "deliveryQuantity", ignore = true)
    @Mapping(target = "contractId", ignore = true)

    @Mapping(target = "detailDTOS", source = "source.deliveryProducts")
    ContractDeliveryDTO toContractDelivery(PerformanceReportContractDeliveryDTO source);

    List<ContractDeliveryDTO> toContractDelivery(List<PerformanceReportContractDeliveryDTO> source);


    @Mapping(target = "tipProcessNumber", ignore = true)
    @Mapping(target = "stuffCode", ignore = true)
    @Mapping(target = "productType", ignore = true)
    @Mapping(target = "productQuantity", ignore = true)
    @Mapping(target = "productName", ignore = true)
    @Mapping(target = "pnCode", ignore = true)
    @Mapping(target = "optionalQuantity", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deliveryId", ignore = true)
    @Mapping(target = "contractProductId", ignore = true)
    @Mapping(target = "returnChangeProductId", ignore = true)

    @Mapping(target = "projectProductId", source = "projectProductRecordId")
    @Mapping(target = "deliveryQuantity", source = "num")
    ContractDeliveryDetailDTO toContractDeliveryProductVO(ContractDeliveryProductVO source);





    @Mapping(target = "reProductAmount", ignore = true)
    @Mapping(target = "newProductAmount", ignore = true)
    @Mapping(target = "approvalNode", ignore = true)
    ReturnExchangeItem toReturnExchangeItem(ReturnExchange source);


    @Mapping(target = "approvalNode", expression = "java(com.topsec.crm.flow.core.process.ProcessUtils.approveNodeToText(source.getApprovalNode()))")
    @Mapping(target = "type", expression = "java(com.topsec.crm.framework.common.enums.ReturnExchangeTypeEnum.getDescriptionByCode(source.getType()))")
    @Mapping(target = "returnReason", expression = "java(com.topsec.crm.framework.common.enums.ReturnExchangeReasonEnum.getDescriptionByCode(source.getReturnReason()))")
    ReturnExchangeServiceImpl.ReturnExchangeExcelItem toReturnExchangeExcelItem(ReturnExchangeItem source);
    List<ReturnExchangeServiceImpl.ReturnExchangeExcelItem> toReturnExchangeExcelItem(List<ReturnExchangeItem> source);
}
