package com.topsec.crm.flow.core.controller.borrowForCompensate;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.flow.api.dto.borrowForCompensate.BorrowForCompensateDTO;
import com.topsec.crm.flow.api.dto.borrowForCompensate.BorrowForCompensateFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.borrowForCompensate.BorrowForCompensateProductDTO;
import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowForProbationAttachmentDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BorrowForCompensate;
import com.topsec.crm.flow.core.entity.BorrowForProbationAttachment;
import com.topsec.crm.flow.core.process.impl.BorrowForCompensateProcessService;
import com.topsec.crm.flow.core.service.BorrowForProbationAttachmentService;
import com.topsec.crm.flow.core.service.IBorrowForCompensateService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.BorrowForProbationConstants;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.project.api.client.RemoteBorrowForProbationClient;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.operation.api.RemoteContractReviewConfigService;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractSignCompanyVO;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 丢失赔偿流程控制器
 */
@RestController
@RequestMapping("/borrowForCompensate")
@Tag(name = "丢失赔偿流程", description = "/borrowForCompensate")
public class BorrowForCompensateController extends BaseController {

    @Autowired
    private BorrowForCompensateProcessService borrowedForCompensateProcessService;
    @Autowired
    private IBorrowForCompensateService borrowForCompensateService;
    @Autowired
    private BorrowForProbationAttachmentService borrowForProbationAttachmentService;
    @Autowired
    private TosEmployeeClient tosEmployeeClient;
    @Autowired
    private TosDepartmentClient tosDepartmentClient;
    @Autowired
    private RemoteContractReviewConfigService remoteContractReviewConfigService;
    @Autowired
    private TfsNodeClient tfsNodeClient;
    @Autowired
    private RemoteBorrowForProbationClient remoteBorrowForProbationClient;

    @PostMapping("/launch")
    @Operation(summary = "发起丢失赔偿流程")
    @PreAuthorize(hasPermission = "crm_device_probation_compensate")
    public JsonObject<Boolean> launch(@Valid @RequestBody BorrowForCompensateFlowLaunchDTO launchDTO) {
        //权限校验
        List<BorrowForCompensateProductDTO> productList = launchDTO.getProductList();
        Assert.isFalse(CollectionUtil.isEmpty(productList),"产品信息不能为空");

        Set<String> ids = productList.stream().map(BorrowForCompensateProductDTO::getProjectProbationDeviceId).collect(Collectors.toSet());
        JsonObject<Map<String, Boolean>> mapJsonObject = remoteBorrowForProbationClient.checkBorrowForProbationDeviceByIds(ids, UserInfoHolder.getCurrentPersonId());
        if(mapJsonObject.isSuccess() && mapJsonObject.getObjEntity() != null){
            if(mapJsonObject.getObjEntity().containsValue(false)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        return new JsonObject<>(borrowedForCompensateProcessService.launch(launchDTO));
    }

    @GetMapping("/info")
    @Operation(summary = "查看丢失赔偿流程详情")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<BorrowForCompensateDTO> info(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询丢失赔偿流程信息
        BorrowForCompensate bff = borrowForCompensateService.query()
                .eq("process_instance_id", processInstanceId).one();
        BorrowForCompensateDTO borrowForCompensateDTO = HyperBeanUtils.copyPropertiesByJackson(bff, BorrowForCompensateDTO.class);

        //2.补充信息
        //2.1补充发起人信息
        JsonObject<EmployeeVO> jObj = tosEmployeeClient.findById(bff.getCreateUser());
        if(jObj.isSuccess()){
            EmployeeVO employeeVO = jObj.getObjEntity();
            borrowForCompensateDTO.setCreateUserName(NameUtils.getNameByEmployeeVO(employeeVO));
            borrowForCompensateDTO.setCreateUserDeptName(employeeVO.getDept() != null ? employeeVO.getDept().getName() : "");
        }
        //2.2补充责任人信息
        JsonObject<EmployeeVO> jObj2 = tosEmployeeClient.findById(bff.getResponsibleManId());
        if(jObj.isSuccess()){
            EmployeeVO employeeVO = jObj2.getObjEntity();
            borrowForCompensateDTO.setResponsibleManDeptName(employeeVO.getDept() != null ? employeeVO.getDept().getName() : "");
        }

        //2.3查询附件信息
        List<BorrowForProbationAttachment> attachments = borrowForProbationAttachmentService.query().eq("borrow_id", bff.getId()).list();
        borrowForCompensateDTO.setAttachments(HyperBeanUtils.copyListPropertiesByJackson(attachments, BorrowForProbationAttachmentDTO.class));

        //2.4产品公司套账
        JsonObject<ContractSignCompanyVO> bySignCompanyId = remoteContractReviewConfigService.getBySignCompanyId(bff.getProductCompanyAccount());
        if(bySignCompanyId.isSuccess() && bySignCompanyId.getObjEntity() != null){
            ContractSignCompanyVO objEntity = bySignCompanyId.getObjEntity();
            borrowForCompensateDTO.setProductCompanyAccount(objEntity.getCompanyName());
        }else {
            borrowForCompensateDTO.setProductCompanyAccount("");
        }

        //对字段做权限过滤
        filterData(borrowForCompensateDTO);

        return new JsonObject<>(borrowForCompensateDTO);
    }

    private void filterData(BorrowForCompensateDTO borrowForCompensateDTO) {
        /**
         * 赔偿流程核定金额、参考金额可见节点
         * 01、02、05、05A
         */
        List<String> activityIds = BorrowForProbationConstants.BORROW_FOR_COM_YJ;
        //可见ID
        JsonObject<Set<String>> setJsonObject = tfsNodeClient.queryAssigneeAccountIdList(borrowForCompensateDTO.getProcessInstanceId(), activityIds);
        if(setJsonObject.isSuccess()){
            if(!UserInfoHolder.getCurrentPersonId().equals(borrowForCompensateDTO.getCreateUser())
                    && !setJsonObject.getObjEntity().contains(UserInfoHolder.getCurrentAccountId())){
                borrowForCompensateDTO.setApprovedPrice(null);
            }
        }
    }

    @GetMapping("/selectResponsibleManAccountId")
    @Operation(summary = "查询丢失赔偿责任人AccountId")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<FlowPerson> selectResponsibleManAccountId(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询丢失赔偿流程信息
        BorrowForCompensate bff = borrowForCompensateService.query()
                .eq("process_instance_id", processInstanceId).one();

        List<FlowPerson> flowPeople = AccountAccquireUtils.convertGetAccountByPersonId(Collections.singletonList(bff.getResponsibleManId()));
        return new JsonObject<>(flowPeople.get(0));
    }

    @GetMapping("/selectResponsibleManLeader")
    @Operation(summary = "查询责任人所在部门负责人")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> selectResponsibleManLeader(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询丢失赔偿流程信息
        BorrowForCompensate bff = borrowForCompensateService.query()
                .eq("process_instance_id", processInstanceId).one();

        JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(bff.getResponsibleManId());
        if(byId.isSuccess()){
            //查询所在部门负责人
            EmployeeVO employeeVO = byId.getObjEntity();
            if(employeeVO.getDept() != null){
                JsonObject<List<EmployeeVO>> byDeptLeader = tosDepartmentClient.findByDeptLeader(employeeVO.getDept().getUuid());
                if(byDeptLeader.isSuccess()){
                    List<EmployeeVO> objEntity = byDeptLeader.getObjEntity();
                    List<String> personIds = objEntity.stream().map(EmployeeVO::getUuid).collect(Collectors.toList());
                    List<FlowPerson> flowPeople = AccountAccquireUtils.convertGetAccountByPersonId(personIds);
                    return new JsonObject<>(flowPeople);
                }
            }
        }
        return new JsonObject<>(null);
    }

    @PostMapping("/editApprovedPrice")
    @Operation(summary = "02.填写核定赔偿金额")
    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"sid-C5ABB294-C333-4F02-B1E4-5BD81C926036"})
    public JsonObject<Boolean> editApprovedPrice(@RequestBody BorrowForCompensateDTO borrowForCompensateDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(borrowForCompensateDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        BorrowForCompensate borrowForCompensate = HyperBeanUtils.copyPropertiesByJackson(borrowForCompensateDTO, BorrowForCompensate.class);
        borrowForCompensateService.update(new UpdateWrapper<BorrowForCompensate>().eq("process_instance_id",borrowForCompensate.getProcessInstanceId())
                .set("approved_price", borrowForCompensate.getApprovedPrice())
        );

        return new JsonObject<Boolean>(ResultEnum.SUCCESS.getResult(), ResultEnum.SUCCESS.getMessage(),true);
    }

    @PostMapping("/editPayablePrice")
    @Operation(summary = "05.填写应缴金额")
    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"reparation_05"})
    public JsonObject<Boolean> editPayablePrice(@RequestBody BorrowForCompensateDTO borrowForCompensateDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(borrowForCompensateDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        BorrowForCompensate borrowForCompensate = HyperBeanUtils.copyPropertiesByJackson(borrowForCompensateDTO, BorrowForCompensate.class);
        borrowForCompensateService.update(new UpdateWrapper<BorrowForCompensate>().eq("process_instance_id",borrowForCompensate.getProcessInstanceId())
                .set("payable_price", borrowForCompensate.getPayablePrice())
        );

        return new JsonObject<Boolean>(ResultEnum.SUCCESS.getResult(), ResultEnum.SUCCESS.getMessage(),true);
    }

    @PostMapping("/editReceivePrice")
    @Operation(summary = "05.填写收款金额")
    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"reparation_05A"})
    public JsonObject<Boolean> editReceivePrice(@RequestBody BorrowForCompensateDTO borrowForCompensateDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(borrowForCompensateDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        BorrowForCompensate borrowForCompensate = HyperBeanUtils.copyPropertiesByJackson(borrowForCompensateDTO, BorrowForCompensate.class);
        borrowForCompensateService.update(new UpdateWrapper<BorrowForCompensate>().eq("process_instance_id",borrowForCompensate.getProcessInstanceId())
                .set("receive_price", borrowForCompensate.getReceivePrice())
        );

        return new JsonObject<Boolean>(ResultEnum.SUCCESS.getResult(), ResultEnum.SUCCESS.getMessage(),true);
    }

}
