package com.topsec.crm.flow.core.controller.agentExamineSd;

import com.topsec.crm.agent.api.entity.CrmAgentListVo;
import com.topsec.crm.flow.api.dto.agentExamineSd.*;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.service.AgentExamineSdService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/agentExamineSd")
@Tag(name = "省级总经销商MBO考核", description = "/agentExamineSd")
@RequiredArgsConstructor
@Validated
public class AgentExamineSdController extends BaseController {

    private final AgentExamineSdService agentExamineSdService;

    @PostMapping("/getAgentExamineSdList")
    @Operation(summary = "MBO考核流程列表")
    @PreAuthorize(hasPermission = "crm_agent_examine_sd",dataScope="crm_agent_examine_sd")
    public JsonObject<PageUtils<AgentExamineSdVO>> getAgentExamineSdList(@RequestBody(required = false) AgentExamineSdQuery agentExamineSdQuery){
        if (agentExamineSdQuery == null) {
            agentExamineSdQuery = new AgentExamineSdQuery();
        }
        startPage();
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        List<AgentExamineSdVO> agentExamineSdVOS = agentExamineSdService.selectAgentExamineSdList(agentExamineSdQuery, dataScopeParam);
        PageUtils dataTable = listToPage(agentExamineSdVOS);
        return new JsonObject<>(dataTable);
    }

    @GetMapping("/getExamineSdInfo")
    @Operation(summary = "查询分配考核省代公司")
    @PreFlowPermission(hasAnyNodes = {"sid-0B3AC147-6753-4F12-BAEF-9C5D0F751D92"})
    public JsonObject<List<CrmAgentListVo>> getExamineSdInfo(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(agentExamineSdService.selectExamineSdInfo(processInstanceId));
    }

    @PostMapping("/addExamineSdInfo")
    @Operation(summary = "新增分配考核信息")
    @PreFlowPermission(hasAnyNodes = {"sid-0B3AC147-6753-4F12-BAEF-9C5D0F751D92"})
    public JsonObject<Boolean> addExamineSdInfo(@RequestBody List<AgentExamineSdDetailDTO> agentExamineSdDetailDTOS){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(agentExamineSdService.insertExamineSdInfo(processInstanceId, agentExamineSdDetailDTOS));
    }

    @GetMapping("/getAgentExamineSdDetailByProcessInstanceId")
    @Operation(summary = "根据MBO考核实例ID查询流程详细信息")
    @PreFlowPermission
    public JsonObject<AgentExamineSdFlowDetailDTO> getAgentExamineSdDetailByProcessInstanceId(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(agentExamineSdService.selectAgentExamineSdDetailByProcessInstanceId(processInstanceId));
    }

    @PostMapping("/getAgentExamineSdDetailList")
    @Operation(summary = "考核信息列表")
    @PreAuthorize(hasPermission = "crm_agent_examine_sd",dataScope="crm_agent_examine_sd")
    public JsonObject<PageUtils<AgentExamineSdDetailInfoDTO>> getAgentExamineSdDetailList(@RequestBody(required = false) AgentExamineSdDetailQuery agentExamineSdDetailQuery){
        if (agentExamineSdDetailQuery == null) {
            agentExamineSdDetailQuery = new AgentExamineSdDetailQuery();
        }
        startPage();
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        List<AgentExamineSdDetailInfoDTO> agentExamineSdDetailVOS = agentExamineSdService.selectAgentExamineSdDetailList(agentExamineSdDetailQuery, dataScopeParam);
        PageUtils dataTable = listToPage(agentExamineSdDetailVOS);
        return new JsonObject<>(dataTable);
    }

    @GetMapping(value="/export")
    @Operation(summary = "导出考核信息列表")
    @PreAuthorize(hasPermission = "crm_agent_examine_sd",dataScope="crm_agent_examine_sd")
    public void exportAgentExamineSdDetailList(@RequestBody(required = false) AgentExamineSdDetailQuery agentExamineSdDetailQuery) throws Exception{
        if (agentExamineSdDetailQuery == null) {
            agentExamineSdDetailQuery = new AgentExamineSdDetailQuery();
        }
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        List<ExportAgentExamineSdDetail> exportAgentExamineSdDetails = agentExamineSdService.exportAgentExamineSdDetailList(agentExamineSdDetailQuery, dataScopeParam);
        ExcelUtil<ExportAgentExamineSdDetail> excelUtil = new ExcelUtil<>(ExportAgentExamineSdDetail.class);
        excelUtil.exportExcel(response, exportAgentExamineSdDetails,"考核信息");
    }

    @GetMapping("/getAgentExamineSdDetailInfoById")
    @Operation(summary = "查询考核信息详情页")
    @PreAuthorize(hasPermission = "crm_agent_examine_sd",dataScope="crm_agent_examine_sd")
    public JsonObject<AgentExamineSdDetailInfoDTO> getAgentExamineSdDetailInfoById(@RequestParam String id){
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();

        return new JsonObject<>(agentExamineSdService.selectAgentExamineSdDetailInfoById(id, dataScopeParam));
    }

    @GetMapping("/getDynastyAccountId")
    @Operation(summary = "查询03省代审批人")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> getDynastyAccountId() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(agentExamineSdService.selectDynastyAccountId(processInstanceId));
    }

    @GetMapping("/completionConditions")
    @Operation(summary = "01步办理完毕判断是否分配考核")
    @PreFlowPermission(hasAnyNodes = {"sid-0B3AC147-6753-4F12-BAEF-9C5D0F751D92"})
    public JsonObject<Boolean> completionConditions(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(agentExamineSdService.completionConditions(processInstanceId));
    }
}
