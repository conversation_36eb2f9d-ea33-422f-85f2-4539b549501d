package com.topsec.crm.flow.core.controller.agentauthentication;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.file.api.RemoteFsmDocService;
import com.topsec.crm.flow.api.dto.agentauthentication.*;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.AgentAuthPaymentDisburse;
import com.topsec.crm.flow.core.entity.FlowBaseEntity;
import com.topsec.crm.flow.core.mapstruct.AgentAuthPaymentDisburseConvertor;
import com.topsec.crm.flow.core.process.impl.AgentAuthPaymentDisburseProcessService;
import com.topsec.crm.flow.core.service.AgentAuthPaymentDisburseService;
import com.topsec.crm.framework.common.bean.CrmFsmDoc;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.enums.FormTypeEnum;
import com.topsec.query.CommonProcessQuery;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbsapi.client.TbsCrmClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.AccountAgentRelVO;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.vo.TfsFormContentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agentAuthPaymentDisburse")
@Tag(name = "付款缴纳")
@RequiredArgsConstructor
@Validated
public class AgentAuthPaymentDisburseController {

    private final AgentAuthPaymentDisburseProcessService agentAuthenticationProcessService;
    private final AgentAuthPaymentDisburseService agentAuthPaymentDisburseService;
    private final RemoteAgentService remoteAgentService;
    private final RemoteFsmDocService remoteFsmDocService;
    private final TbsCrmClient tbsCrmClient;
    private final TfsFormContentClient tfsFormContentClient;

    @PostMapping("/launch")
    @Operation(summary = "渠道认证付款缴纳-发起")
    @PreFlowPermission
    public JsonObject<Boolean> launch(@Valid @RequestBody AgentAuthPaymentDisburseLaunchDTO launchDTO){
        launchDTO.setParentProcessInstanceId(HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        String recipientMainAccountId = getRecipientMainAccountId(launchDTO.getRecipientCompanyId());
        launchDTO.setAssigneeList(Collections.singleton(recipientMainAccountId));
        return new JsonObject<>(agentAuthenticationProcessService.launch(launchDTO));
    }

    @PostMapping("/launchPayment")
    @Operation(summary = "付款缴纳-发起")
    @PreAuthorize(hasPermission = "crm_agent_payment")
    public JsonObject<Boolean> launchPayment(@Valid @RequestBody AgentAuthPaymentDisburseLaunchDTO launchDTO){
//        launchDTO.setAssigneeList(getRecipientAccountIds(launchDTO.getRecipientCompanyId()));
        String recipientMainAccountId = getRecipientMainAccountId(launchDTO.getRecipientCompanyId());
        launchDTO.setAssigneeList(Collections.singleton(recipientMainAccountId));
        return new JsonObject<>(agentAuthenticationProcessService.launch(launchDTO));
    }

    @PostMapping("/fillCollectionInfo")
    @Operation(summary = "付款缴纳-填写收款信息")
    @PreFlowPermission
    public JsonObject<Boolean> fillCollectionInfo(@Valid @RequestBody AgentAuthPaymentCollectionInfo info){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        boolean update = agentAuthPaymentDisburseService.update(
                new UpdateWrapper<AgentAuthPaymentDisburse>().eq("process_instance_id", processInstanceId)
                        .set("receipt_number", info.getReceiptNumber())
                        .set("receipt_date", info.getReceiptDate())
        );
        return new JsonObject<>(update);
    }

    @GetMapping("/validate")
    @Operation(summary = "办结校验")
    @PreFlowPermission
    public JsonObject<Boolean> validate(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        AgentAuthPaymentDisburse one = agentAuthPaymentDisburseService.getOne(new QueryWrapper<AgentAuthPaymentDisburse>().eq("process_instance_id", processInstanceId));
        String receiptNumber = one.getReceiptNumber();
        LocalDate receiptDate = one.getReceiptDate();
        if (ObjectUtils.anyNull(receiptNumber, receiptDate)){
            return new JsonObject<>(false);
        }
        return new JsonObject<>(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "渠道认证付款缴纳-详情")
    @PreFlowPermission
    public JsonObject<AgentAuthPaymentDisburseDetailInfo> detail(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        AgentAuthPaymentDisburse one = agentAuthPaymentDisburseService.getOne(new QueryWrapper<AgentAuthPaymentDisburse>().eq("process_instance_id", processInstanceId));
        AgentAuthPaymentDisburseDetailInfo detailInfo = AgentAuthPaymentDisburseConvertor.INSTANCE.toDetailInfo(one);
        List<String> attachmentIds = one.getAttachmentIds();
        if (CollectionUtils.isNotEmpty(attachmentIds)){
            List<CrmFsmDoc> crmFsmDocs = Optional.ofNullable(remoteFsmDocService.batchSelectByDocId(attachmentIds))
                    .map(JsonObject::getObjEntity)
                    .orElse(Collections.emptyList());
            detailInfo.setAttachments(crmFsmDocs);
        }
        Optional.ofNullable(remoteAgentService.getAgentInfo(one.getRecipientCompanyId())).map(JsonObject::getObjEntity).ifPresent(
                agent -> {
                    detailInfo.setAddress(agent.getDetailAddress());
                    detailInfo.setBankName(agent.getDepositBank());
                    detailInfo.setBankAccount(agent.getBankAccount());
                });

        return new JsonObject<>(detailInfo);
    }

    @GetMapping("/paymentDetail")
    @Operation(summary = "付款缴纳-详情")
    @PreFlowPermission
    public JsonObject<AgentAuthPaymentDisburseVO> paymentDetail(@RequestParam String processInstanceId) {
        return new JsonObject<>(agentAuthPaymentDisburseService.paymentDetail(processInstanceId));
    }

    private String getRecipientMainAccountId(String recipientCompanyId) {
        return Optional.ofNullable(tbsCrmClient.listAccountAgentRelVOByAgentId(recipientCompanyId))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream()

                .filter(agentRelVO -> {
                    return Objects.equals(agentRelVO.getAccountType(),0);
                })
                .findFirst()
                .map(AccountAgentRelVO::getAccountId)
                .orElseThrow(() -> new RuntimeException("收款方不存在主账号"));
    }

//    private Set<String> getRecipientAccountIds(String recipientCompanyId) {
//        return Optional.ofNullable(tbsCrmClient.listAccountAgentRelVOByAgentId(recipientCompanyId))
//                .map(JsonObject::getObjEntity)
//                .orElse(Collections.emptyList())
//                .stream()
//                .map(AccountAgentRelVO::getAccountId).collect(Collectors.toSet());
//    }

    @PostMapping({"/queryProcessPage"})
    @Operation(summary = "渠道认证付款缴纳-列表")
    @PreFlowPermission
    public JsonObject<PageUtils<TfsFormContentVo>> queryProcessPage(@RequestBody AgentAuthPaymentQuery query){
        String parentProcessInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        query.setParentProcessInstanceId(parentProcessInstanceId);
        List<String> instanceIdList = agentAuthPaymentDisburseService
                .list(new QueryWrapper<AgentAuthPaymentDisburse>()
                        .eq("parent_process_instance_id", parentProcessInstanceId)
                        .select("process_instance_id")
                ).stream().map(FlowBaseEntity::getProcessInstanceId).toList();
        if (CollectionUtils.isEmpty(instanceIdList)){
            return new JsonObject<>(PageUtils.empty());
        }

        CommonProcessQuery commonProcessQuery=new CommonProcessQuery();
        commonProcessQuery.setPlatformCode(UserInfoHolder.getCurrentAudience());
        commonProcessQuery.setType(FormTypeEnum.PAYMENT_DISBURSE.getType());
        commonProcessQuery.setPageNum(query.getPageNum());
        commonProcessQuery.setPageSize(query.getPageSize());
        commonProcessQuery.setStatus(query.getStatus());
        commonProcessQuery.setProcessInstanceIds(instanceIdList);


        PageUtils<TfsFormContentVo> pageUtils = Optional.ofNullable(tfsFormContentClient.queryProcessPage(commonProcessQuery))
                .map(JsonObject::getObjEntity).map(PageUtils::new)
                .orElse(new PageUtils<>());

        return new JsonObject<>(pageUtils);
    }


}
