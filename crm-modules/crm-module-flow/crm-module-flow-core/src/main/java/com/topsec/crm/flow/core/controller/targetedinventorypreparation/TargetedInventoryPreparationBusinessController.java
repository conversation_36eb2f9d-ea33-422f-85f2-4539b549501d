package com.topsec.crm.flow.core.controller.targetedinventorypreparation;

import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.TargetedInventoryPreparationProductOwnVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.TargetedInventoryPreparationMain;
import com.topsec.crm.flow.core.service.ITargetedInventoryPreparationMainService;
import com.topsec.crm.flow.core.service.ITargetedInventoryPreparationProductOwnService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/business/targetedInventoryPreparationMain")
@RequiredArgsConstructor
@Validated
@Tag(name = "专项备货-业务接口", description = "/targetedInventoryPreparationMain")
public class TargetedInventoryPreparationBusinessController {


    private final ITargetedInventoryPreparationMainService iTargetedInventoryPreparationMainService;

    private final ITargetedInventoryPreparationProductOwnService iTargetedInventoryPreparationProductOwnService;

    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    @GetMapping("/getOwnPageByTargetedInventoryPreparationMainId")
    @Operation(summary = "根据主表ID分页查询明细")
    public JsonObject<List<TargetedInventoryPreparationProductOwnVO>> getOwnPageByTargetedInventoryPreparationMainId(@RequestParam String targetedInventoryPreparationMainId){
//        startPage();
        CrmAssert.hasText(targetedInventoryPreparationMainId, "专项备货Id不能为空");
        return new JsonObject<>(iTargetedInventoryPreparationProductOwnService.getOwnPageByTargetedInventoryPreparationMainId(targetedInventoryPreparationMainId));

    }
}
