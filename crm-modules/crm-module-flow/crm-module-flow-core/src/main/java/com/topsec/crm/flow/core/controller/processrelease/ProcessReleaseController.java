package com.topsec.crm.flow.core.controller.processrelease;

import com.topsec.crm.flow.api.vo.ProcessReleaseVO;
import com.topsec.crm.flow.core.service.ProcessReleaseService;
import com.topsec.crm.framework.common.bean.HomeNameValue;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/processRelease")
@Tag(name = "流程放行")
@RequiredArgsConstructor
@Validated
public class ProcessReleaseController extends BaseController {

    private final ProcessReleaseService processReleaseService;

    @PostMapping("/getReturnReleasePageList")
    @Operation(summary = "退回放行分页列表")
    @PreAuthorize(hasPermission = "crm_flow_pass_config")
    public JsonObject<TableDataInfo> getPageList(@RequestBody ProcessReleaseVO req) {
        startPage();
        return new JsonObject<>(processReleaseService.getReturnReleasePageList(req));
    }

    @PostMapping("/getFinishReleasePageList")
    @Operation(summary = "办结放行分页列表")
    @PreAuthorize(hasPermission = "crm_flow_pass_config")
    public JsonObject<TableDataInfo> getFinishReleasePageList(@RequestBody ProcessReleaseVO req) {
        startPage();
        return new JsonObject<>(processReleaseService.getFinishReleasePageList(req));
    }

    @PostMapping("/saveReturnRelease")
    @Operation(summary = "新增退回放行")
    @PreAuthorize(hasPermission = "crm_flow_pass_config")
    public JsonObject<Boolean> saveReturnRelease(@RequestBody ProcessReleaseVO req) {
        return new JsonObject<>(processReleaseService.saveReturnRelease(req));
    }

    @PostMapping("/saveFinishRelease")
    @Operation(summary = "新增办结放行")
    @PreAuthorize(hasPermission = "crm_flow_pass_config")
    public JsonObject<Boolean> saveFinishRelease(@RequestBody ProcessReleaseVO req) {
        return new JsonObject<>(processReleaseService.saveFinishRelease(req));
    }

    @PostMapping("/updateProcessRelease")
    @Operation(summary = "修改流程放行")
    @PreAuthorize(hasPermission = "crm_flow_pass_config")
    public JsonObject<Boolean> updateProcessRelease(@RequestBody ProcessReleaseVO req) {
        return new JsonObject<>(processReleaseService.updateProcessRelease(req));
    }

    @GetMapping("/deleteProcessRelease")
    @Operation(summary = "删除流程放行",parameters = {@Parameter (name = "id", description = "放行ID")})
    @PreAuthorize(hasPermission = "crm_flow_pass_config")
    public JsonObject<Boolean> deleteProcessRelease(@RequestParam String id) {
        return new JsonObject<>(processReleaseService.deleteProcessRelease(id));
    }

    @GetMapping("/getProcessInfoList")
    @Operation(summary = "根据流程编号模糊查询",description = "name-流程编号，value-流程ID",parameters = {@Parameter (name = "processNumber", description = "流程编号")})
    @PreAuthorize(hasPermission = "crm_flow_pass_config")
    public JsonObject<List<HomeNameValue<String,String>>> getProcessInfoList(@RequestParam String processNumber){
        return new JsonObject<>(processReleaseService.getProcessSelect(processNumber));
    }
}
