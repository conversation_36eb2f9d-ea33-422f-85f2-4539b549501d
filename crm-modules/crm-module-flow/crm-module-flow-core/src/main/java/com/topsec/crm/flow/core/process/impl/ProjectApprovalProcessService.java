package com.topsec.crm.flow.core.process.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.flow.api.dto.projectApproval.ProjectApprovalFlowLaunchDTO;
import com.topsec.crm.flow.core.entity.ProcessExtensionInfo;
import com.topsec.crm.flow.core.entity.ProjectApproval;
import com.topsec.crm.flow.core.process.AbstractProjectProcessService;
import com.topsec.crm.flow.core.process.ProcessTypeEnum;
import com.topsec.crm.flow.core.service.ProjectApprovalBudgetDetailsService;
import com.topsec.crm.flow.core.service.ProjectApprovalFinancialAnalysisDataService;
import com.topsec.crm.flow.core.service.ProjectApprovalMilestoneService;
import com.topsec.crm.flow.core.service.ProjectApprovalService;
import com.topsec.tbscommon.utils.UUIDUtils;
import com.topsec.vo.FlowStateInfoVo;
import com.topsec.vo.task.TfsTaskVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-07-22  14:37
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class ProjectApprovalProcessService extends AbstractProjectProcessService<ProjectApprovalFlowLaunchDTO> {

    private final ProjectApprovalService projectApprovalService;

    private final ProjectApprovalFinancialAnalysisDataService projectApprovalFinancialAnalysisDataService;

    private final ProjectApprovalMilestoneService projectApprovalMilestoneService;

    private final ProjectApprovalBudgetDetailsService projectApprovalBudgetDetailsService;

    @Override
    public ProcessTypeEnum processType() {
        return ProcessTypeEnum.PROJECT_APPROVAL;
    }

    @Override
    protected void preProcess(ProjectApprovalFlowLaunchDTO launchable) {
        Map<String,Object> variableMap= new HashMap<>();
        launchable.setProjectId(launchable.getProjectId());
        launchable.setProjectName(launchable.getProjectName());
        launchable.setIsSkip(false);
        launchable.setVariables(variableMap);

    }

    @Override
    protected String afterEngineLaunch(ProcessExtensionInfo processInfo, ProjectApprovalFlowLaunchDTO launchable) {
        String processInstanceId = processInfo.getProcessInstanceId();
        ProjectApproval projectApproval = new ProjectApproval();
        projectApproval.setProcessInstanceId(processInstanceId);
        projectApproval.setTeamBuildingProcessInstanceId(launchable.getTeamBuildingProcessInstanceId());
        projectApproval.setProjectId(launchable.getProjectId());
        projectApproval.setId(UUIDUtils.generateUUID());
        projectApproval.setAttachmentIds(launchable.getAttachmentIds());
        projectApproval.setProjectStatusOverview(launchable.getProjectStatusOverview());
        projectApprovalService.save(projectApproval);
        return projectApproval.getId();
    }

    @Override
    public void handleProcessing(FlowStateInfoVo flowInfo) {
        //更新通过时间
        projectApprovalService.update(new UpdateWrapper<ProjectApproval>().eq("process_instance_id",flowInfo.getProcessInstanceId())
                .set("process_pass_time", Optional.ofNullable(flowInfo.getChangeTime()).orElse(LocalDateTime.now()))
        );
    }

    @Override
    public void handlePass(FlowStateInfoVo flowInfo) {

    }



    @Override
    public void handlePassBack(FlowStateInfoVo flowInfo) {

    }

    @Override
    public void handleProcessingBack(FlowStateInfoVo flowInfo) {

    }

    @Override
        public Pair<Boolean, String> checkFilling(TfsTaskVo taskVo) {
        return Pair.of(true, null);
    }
}
