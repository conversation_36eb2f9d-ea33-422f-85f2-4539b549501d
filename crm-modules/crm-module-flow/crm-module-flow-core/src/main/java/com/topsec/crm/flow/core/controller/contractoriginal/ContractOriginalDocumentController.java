package com.topsec.crm.flow.core.controller.contractoriginal;


import com.topsec.crm.flow.api.dto.contractoriginal.ContractOriginalInfoDTO;
import com.topsec.crm.flow.api.dto.contractoriginal.ContractOriginalSignDTO;
import com.topsec.crm.flow.api.dto.contractoriginal.ContractOriginalTimeDTO;
import com.topsec.crm.flow.api.dto.contractoriginal.vo.ContractOriginalDocInfoVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.service.ContractOriginalDocumentService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 合同原件表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@RestController
@RequestMapping("/original")
@Tag(name = "合同原件-流程业务接口", description = "/business/original")
@RequiredArgsConstructor
@Validated
public class ContractOriginalDocumentController extends BaseController {

    @Resource
    private ContractOriginalDocumentService contractOriginalDocumentService;

    @PreFlowPermission
    @GetMapping("/getByOriginalId")
    @Operation(summary = "上交合同原件流程审批列表——查看详情")
    public JsonObject<ContractOriginalDocInfoVO> getByOriginalId(@RequestParam String originalId) {
        PreFlowPermissionAspect.checkProcessInstanceId(originalId,  HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractOriginalDocumentService.getByOriginalId(originalId));
    }

    @PreFlowPermission(hasAnyNodes = {"submitContract_01","submitContract_01A"})
    @PostMapping("/updateOriginalSign")
    @Operation(summary = "上交合同原件流程-01 01A修改用户签名")
    public JsonObject<Boolean> updateOriginalSign(@RequestBody @Valid ContractOriginalSignDTO contractOriginalSignDTO) {
        return new JsonObject<>(contractOriginalDocumentService.updateOriginalSign(contractOriginalSignDTO));
    }

    @PreFlowPermission(hasAnyNodes = {"submitContract_01"})
    @PostMapping("/updateOriginalSigningDate")
    @Operation(summary = "上交合同原件流程-01修改签订时间")
    public JsonObject<Boolean> updateOriginalSigningDate(@RequestBody @Valid ContractOriginalTimeDTO originalTimeDTO) {
        return new JsonObject<>(contractOriginalDocumentService.updateOriginalSigningDate(originalTimeDTO));
    }

    @PreFlowPermission(hasAnyNodes = {"submitContract_02"})
    @PostMapping("/updateOriginalInfo")
    @Operation(summary = "上交合同原件流程-02修改填写合同信息")
    public JsonObject<Boolean> updateOriginalInfo(@RequestBody ContractOriginalInfoDTO originalInfoDTO) {
        return new JsonObject<>(contractOriginalDocumentService.updateOriginalInfo(originalInfoDTO));
    }

    @PreFlowPermission(hasAnyNodes = {"submitContract_02"})
    @GetMapping("/checkOriginalInfo")
    @Operation(summary = "上交合同原件流程-02办结节点，校验合同信息是否填写完整")
    public JsonObject<Boolean> checkOriginalInfo(@RequestParam String originalId) {
        return new JsonObject<>(contractOriginalDocumentService.checkOriginalInfo(originalId));
    }

}

