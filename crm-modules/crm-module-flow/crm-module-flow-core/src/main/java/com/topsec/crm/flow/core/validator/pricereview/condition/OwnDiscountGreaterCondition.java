package com.topsec.crm.flow.core.validator.pricereview.condition;

import com.topsec.crm.flow.core.validator.CheckCondition;
import com.topsec.crm.project.api.entity.PriceStatisticsVO;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 整个折扣力度变大(折扣值变小)
 * <AUTHOR>
 */
public class OwnDiscountGreaterCondition implements CheckCondition<Void> {

    private final PriceStatisticsVO fromProject;
    private final PriceStatisticsVO snapshot;

    public OwnDiscountGreaterCondition(PriceStatisticsVO fromProject, PriceStatisticsVO snapshot) {
        this.fromProject = fromProject;
        this.snapshot = snapshot;
    }

    @Override
    public boolean check(Void v)  {
        BigDecimal fromProjectDiscount = Optional.ofNullable(fromProject.getDiscount()).orElse(BigDecimal.ZERO);
        BigDecimal snapshotDiscount = Optional.ofNullable(snapshot.getDiscount()).orElse(BigDecimal.ZERO);
        //折扣值变小则 返回false
        if (snapshotDiscount.compareTo(fromProjectDiscount) > 0) {
            return false;
        }
        return true;
    }

    @Override
    public String defaultFailureReason() {
        return "整单折扣力度变大";
    }


}
