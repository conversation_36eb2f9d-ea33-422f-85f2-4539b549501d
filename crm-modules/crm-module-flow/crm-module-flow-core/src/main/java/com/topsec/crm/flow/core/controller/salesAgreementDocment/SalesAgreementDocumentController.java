package com.topsec.crm.flow.core.controller.salesAgreementDocment;


import com.topsec.crm.flow.api.dto.saleagreementdocument.SaleAttachmentInfos;
import com.topsec.crm.flow.api.dto.saleagreementdocument.SaleOriginalInfoDTO;
import com.topsec.crm.flow.api.vo.ProcessAttachmentVo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementOwnProductVo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementReviewMainVo;
import com.topsec.crm.flow.api.vo.salesAgreementDocment.SalesAgreementBaseInfoVO;
import com.topsec.crm.flow.api.vo.salesAgreementDocment.SalesAgreementDocumentAttachmentVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.SalesAgreementDocument;
import com.topsec.crm.flow.core.entity.SalesAgreementReviewMain;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <p>
 * 销售协议原件表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@RestController
@RequestMapping("/salesAgreementDocument")
@Tag(name = "协议原件-流程业务接口", description = "salesAgreementDocument")
@RequiredArgsConstructor
@Validated
public class SalesAgreementDocumentController extends BaseController {
    @Resource
    private SalesAgreementDocumentService salesAgreementDocumentService;

    @Resource
    private SalesAgreementReviewMainService salesAgreementReviewMainService;

    @Resource
    private ThreeProcurementPaymentReviewMainService threeProcurementPaymentReviewMainService;
    @Resource
    private SalesAgreementDocumentAttachmentService salesAgreementDocumentAttachmentService;

    @Resource
    private SalesAgreementProductService salesAgreementProductService;

    //12
    @PreFlowPermission
    @GetMapping("/selectSalesAgreementAttachmentList")
    @Operation(summary = "协议原件详情-协议附件列表")
    public JsonObject<List<ProcessAttachmentVo>> selectSalesAgreementAttachmentList(@RequestParam String agreementProcessInstanceId) {
        CrmAssert.hasText(agreementProcessInstanceId, "销售协议的流程实例化id不能为空");
        SalesAgreementDocument agreementDocument = salesAgreementDocumentService.getByAgreementProcessInstanceId(agreementProcessInstanceId);
        CrmAssert.notNull(agreementDocument, "协议流程记录不存在");
        PreFlowPermissionAspect.checkProcessInstanceId(agreementDocument.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(agreementProcessInstanceId);
        List<ProcessAttachmentVo> listThreeProcurementAttachment = threeProcurementPaymentReviewMainService.getListThreeProcurementAttachment(salesAgreement.getAttachmentIds());
        return new JsonObject<>(listThreeProcurementAttachment);
    }

    @PreFlowPermission
    @GetMapping("/selectAgreementDocInfoById")
    @Operation(summary = "协议原件上交审批详情页-基本信息（包含协议基本信息）")
    public JsonObject<SalesAgreementBaseInfoVO> selectAgreementDocInfoById(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(salesAgreementDocumentService.selectAgreementDocInfoById(processInstanceId));
    }
    //12
    @PreFlowPermission
    @GetMapping("/queryAttachments")
    @Operation(summary = "协议原件详情-协议原件")
    public JsonObject<List<SalesAgreementDocumentAttachmentVO>> queryAttachments(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId,HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(salesAgreementDocumentAttachmentService.queryAttachmentList(processInstanceId));
    }


    @PreFlowPermission
    @GetMapping("/queryAttachmentList")
    @Operation(summary = "协议原件上交审批详情页-协议原件")
    public JsonObject<List<SalesAgreementDocumentAttachmentVO>> queryAttachmentList(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(salesAgreementDocumentAttachmentService.queryAttachmentList(processInstanceId));
    }


    @PreFlowPermission(hasAnyNodes = {"sid-17A6A5D1-2D5B-4248-B476-18C9EA5E974D"})
    @PostMapping("/updateOriginalInfo")
    @Operation(summary = "上交协议原件流程——02 填写上交信息")
    public JsonObject<Boolean> updateOriginalInfo(@RequestBody SaleOriginalInfoDTO salesAgreementDocumentVO) {
        SalesAgreementDocument salesAgreementDocument = salesAgreementDocumentService.getById(salesAgreementDocumentVO.getId());
        CrmAssert.notNull(salesAgreementDocument, "流程记录不存在");
        PreFlowPermissionAspect.checkProcessInstanceId(salesAgreementDocument.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(salesAgreementDocumentService.updateOriginalInfo(salesAgreementDocumentVO));
    }

    @PreFlowPermission(hasAnyNodes = {"sid-347FEA2F-5A66-405B-B621-7927A984E598"})
    @Operation(summary = "上交协议原件流程——01修改协议原件")
    @PostMapping("/saveAttachment")
    public JsonObject<Boolean> saveAttachment(@RequestBody @Validated SaleAttachmentInfos attachmentInfos) {
        SalesAgreementDocument salesAgreementDocument = salesAgreementDocumentService.getById(attachmentInfos.getOriginalId());
        CrmAssert.notNull(salesAgreementDocument, "流程记录不存在");
        PreFlowPermissionAspect.checkProcessInstanceId(salesAgreementDocument.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(salesAgreementDocumentAttachmentService.saveAttachments(attachmentInfos.getAttachmentInfoDTOs(), attachmentInfos.getOriginalId()));
    }

    @PreFlowPermission
    @GetMapping("/querySalesAgreementProductList")
    @Operation(summary = "协议原件详情-自有产品")
    public JsonObject<SalesAgreementOwnProductVo> querySalesAgreementProductList(@RequestParam String agreementProcessInstanceId) throws ExecutionException {
        CrmAssert.hasText(agreementProcessInstanceId, "销售协议的流程实例化id不能为空");
        horizontalPermissionVerification(agreementProcessInstanceId);
        SalesAgreementOwnProductVo salesAgreementOwnProductVo = salesAgreementProductService.convertToSalesAgreementOwnProductVo(agreementProcessInstanceId, true);
        return new JsonObject<>(salesAgreementOwnProductVo);
    }

    @PreFlowPermission
    @GetMapping("/selectAgreementInfoById")
    @Operation(summary = "协议原件详情-基本信息", method = "GET")
    public JsonObject<SalesAgreementReviewMainVo> selectContractInfoById(@RequestParam String agreementProcessInstanceId) {
        CrmAssert.hasText(agreementProcessInstanceId, "销售协议的流程实例化id不能为空");
        horizontalPermissionVerification(agreementProcessInstanceId);
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(agreementProcessInstanceId);
        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(salesAgreement, SalesAgreementReviewMainVo.class));
    }

    private void  horizontalPermissionVerification(String agreementProcessInstanceId){
        SalesAgreementDocument salesAgreement = salesAgreementDocumentService.getByAgreementProcessInstanceId(agreementProcessInstanceId);
        if(salesAgreement != null) {
            PreFlowPermissionAspect.checkProcessInstanceId(salesAgreement.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }else {
            throw new CrmException("协议数据不存在");
        }
    }
}

