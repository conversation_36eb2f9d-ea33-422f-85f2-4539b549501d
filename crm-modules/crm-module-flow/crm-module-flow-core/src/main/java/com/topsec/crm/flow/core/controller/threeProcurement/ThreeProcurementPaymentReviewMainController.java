package com.topsec.crm.flow.core.controller.threeProcurement;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.topsec.crm.account.api.client.RemoteAccountService;
import com.topsec.crm.account.api.entity.CrmSimpleEmployeeVO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewCustomerDTO;
import com.topsec.crm.flow.api.dto.contractreview.baseinfo.ContractBasicInfoDTO;
import com.topsec.crm.flow.api.vo.*;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ThreeProcurementAcceptance;
import com.topsec.crm.flow.core.entity.ThreeProcurementPaymentReviewMain;
import com.topsec.crm.flow.core.entity.ThreeProcurementReviewMain;
import com.topsec.crm.flow.core.process.impl.ThreeProcurementPaymentReviewProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.flow.core.util.AmountToChineseConverter;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsHistoryClient;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.TosDepartmentVO;
import com.topsec.vo.task.Approved;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 第三方采购付款
 *
 * <AUTHOR>
 * @email
 * @date 2024-11-01 17:26:59
 */
@RestController
@RequestMapping("threeprocurementpaymentreviewmain")
@Tag(name = "【第三方采购付款】", description = "threeprocurementpaymentreviewmain")
@RequiredArgsConstructor
@Validated
public class ThreeProcurementPaymentReviewMainController {

    private static final Logger log = LoggerFactory.getLogger(ThreeProcurementPaymentReviewMainController.class);
    private final ThreeProcurementPaymentReviewMainService threeProcurementPaymentReviewMainService;

    private final ThreeProcurementPaymentReviewProcessService threeProcurementPaymentReviewProcessService;

    private final ThreeProcurementReviewMainService threeProcurementReviewMainService;

    private final ThreeProcurementReviewInvoicingService threeProcurementReviewInvoicingService;

    private final ThreeProcurementReviewPaymentProvisionService threeProcurementReviewPaymentProvisionService;

    private final ThreeProcurementAcceptanceService threeProcurementAcceptanceService;

    private final ContractReviewCustomerService contractReviewCustomerService;

    private final TfsHistoryClient tfsHistoryClient;

    private final RemoteAccountService remoteAccountService;

    private final TosDepartmentClient tosDepartmentClient;

    private final ContractReviewMainService contractReviewMainService;




    @PostMapping("/launch")
    @Operation(summary = "发起付款审批流程")
    @PreAuthorize(hasAnyPermission = {"crm_third_purchase_payment_direction","crm_third_purchase_payment_normal"})
    public JsonObject<Boolean> launch(@Valid @RequestBody ThreeProcurementPaymentReviewLaunchVo threeProcurementPaymentReviewLaunchVo) {
        return new JsonObject<>(threeProcurementPaymentReviewProcessService.launch(threeProcurementPaymentReviewLaunchVo));
    }


    @GetMapping("/getThreeProcurementPaymentBaseInfo")
    @Operation(summary = "查询付款审批基本信息")
    @PreFlowPermission
    public JsonObject<ThreeProcurementPaymentReviewMainVo> getThreeProcurementPaymentBaseInfo(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementPaymentReviewMain threeProcurementPaymentBaseInfo = threeProcurementPaymentReviewMainService.getThreeProcurementPaymentBaseInfo(processInstanceId);
        ThreeProcurementPaymentReviewMainVo threeProcurementPaymentReviewMainVo = HyperBeanUtils.copyPropertiesByJackson(threeProcurementPaymentBaseInfo, ThreeProcurementPaymentReviewMainVo.class);
        ThreeProcurementReviewMain threeProcurementReviewMain = threeProcurementReviewMainService.selectThreeProcurementReviewMainByProcessInstanceId(threeProcurementPaymentReviewMainVo.getProcurementProcessId());
        BigDecimal totalPurchasePrice = threeProcurementReviewMainService.selectTotalPurchasePrice(threeProcurementPaymentReviewMainVo.getProcurementProcessId());
        String acceptanceId = threeProcurementReviewMain.getAcceptanceId();
        ThreeProcurementAcceptance procurementAcceptance = threeProcurementAcceptanceService.getById(acceptanceId);
        //采购开票金额
        BigDecimal invoiceAmount = threeProcurementReviewInvoicingService.selectInvoiceAmount(threeProcurementPaymentReviewMainVo.getProcurementProcessId());
        BigDecimal paidAmount = threeProcurementPaymentReviewMainService.selectPaymentAmount(threeProcurementPaymentBaseInfo.getPurchaseNumber());

        double paymentRatio = Optional.ofNullable(totalPurchasePrice)
                .filter(pa -> pa.compareTo(BigDecimal.ZERO) != 0)
                .map(pa -> paidAmount.divide(pa, 2, RoundingMode.HALF_UP)
                        .doubleValue())
                .orElse(0.0); // 或者返回其他默认值

        //采购开票比例

        double invoiceRatio = Optional.ofNullable(totalPurchasePrice)
                .filter(pa -> pa.compareTo(BigDecimal.ZERO) != 0)
                .map(pa -> invoiceAmount.divide(pa, 2, RoundingMode.HALF_UP)
                        .doubleValue())
                .orElse(0.0); // 或者返回其他默认值
        //付款金额
        threeProcurementPaymentReviewMainVo.setPaidAmount(paidAmount);
        //采购金额
        threeProcurementPaymentReviewMainVo.setPurchaseAmount(totalPurchasePrice);
        //已付款比例
        threeProcurementPaymentReviewMainVo.setPaymentRatio(paymentRatio);
        //采购开票金额
        threeProcurementPaymentReviewMainVo.setInvoiceAmount(invoiceAmount);
        //采购开票比例
        threeProcurementPaymentReviewMainVo.setInvoiceRatio(invoiceRatio);
        //验收结论
        if (procurementAcceptance != null){
            threeProcurementPaymentReviewMainVo.setConclusion(procurementAcceptance.getConclusion());
        }

        return new JsonObject<>(threeProcurementPaymentReviewMainVo);
    }


//    @PostMapping("/updateThreeProcurementPayment")
//    @Operation(summary = "修改付款审批信息")
//    @PreFlowPermission
//    public JsonObject<Boolean> updateThreeProcurementPayment(@RequestBody ThreeProcurementPaymentReviewMainVo threeProcurementPaymentReviewMainVo) {
//        return new JsonObject<>(threeProcurementPaymentReviewMainService.updateById(HyperBeanUtils.copyPropertiesByJackson(threeProcurementPaymentReviewMainVo, ThreeProcurementPaymentReviewMain.class)));
//    }


    @PostMapping("/queryThreeProcurementPaymentAttachmentList")
    @Operation(summary = "查询第三方付款审批附件列表")
    @PreFlowPermission
    public JsonObject<List<ProcessAttachmentVo>> queryThreeProcurementPaymentAttachmentList(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementPaymentReviewMain threeProcurementPaymentBaseInfo = threeProcurementPaymentReviewMainService.getThreeProcurementPaymentBaseInfo(processInstanceId);
        List<Attachment> attachmentIds = threeProcurementPaymentBaseInfo.getAttachmentIds();
        List<ProcessAttachmentVo> listThreeProcurementAttachment = List.of();
        if (CollectionUtils.isNotEmpty(attachmentIds)){
            listThreeProcurementAttachment = threeProcurementPaymentReviewMainService.getListThreeProcurementAttachment(attachmentIds);
        }
        return new JsonObject<>(listThreeProcurementAttachment);
    }



    @PostMapping("/uploadThreeProcurementPaymentFile")
    @Operation(summary = "02采购专员核实-付款审批附件上传")
    @PreFlowPermission(hasAnyNodes = {"paymentApproval_02","paymentApproval_03"})
    public JsonObject<Boolean> uploadThreeProcurementPaymentFile(@RequestBody ThreeProcurementPaymentReviewMainVo threeProcurementPaymentReviewMainVo) {
        if (Objects.isNull(threeProcurementPaymentReviewMainVo)) {
            throw  new CrmException("参数不能为空");
        }
        if (StringUtils.isEmpty(threeProcurementPaymentReviewMainVo.getProcessInstanceId())){
            throw  new CrmException("流程实例id不能为空");
        }
        PreFlowPermissionAspect.checkProcessInstanceId(threeProcurementPaymentReviewMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(threeProcurementPaymentReviewMainService.uploadThreeProcurementPaymentFile(threeProcurementPaymentReviewMainVo));
    }




    @PostMapping("/checkThreeProcurementPayment")
    @Operation(summary = "付款审批条件校验")
    @PreAuthorize
    public JsonObject<Void>  checkThreeProcurementPayment(@RequestBody ThreeProcurementPaymentReviewMainVo threeProcurementPaymentReviewMainVo){
        threeProcurementPaymentReviewMainVo.setCreateUser(UserInfoHolder.getCurrentPersonId());
        return threeProcurementPaymentReviewProcessService.checkBeforeFillingJsonObj(threeProcurementPaymentReviewMainVo);
    }




    @GetMapping("/queryThreeProcurementPaymentAssigneeList")
    @Operation(summary = "付款审批02和07节点采购专员审批人查询")
    @PreFlowPermission(hasAnyNodes = {"sid-5CCAF507-900B-41BC-8B34-1E3350C56B1E","paymentApproval_03","paymentApproval_04","paymentApproval_05","","paymentApproval_06"})
    public JsonObject<List<Approved>>  queryThreeProcurementPaymentAssigneeList(@RequestParam String procurementProcessId ,@RequestParam String activityId){
        return tfsHistoryClient.findHistoryApproved(procurementProcessId, activityId);
    }


    @GetMapping("/contractBasicInfo")
    @Operation(summary = "合同评审基础信息")
    @PreFlowPermission
    public JsonObject<ContractBasicInfoDTO> contractBasicInfo(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementPaymentReviewMain threeProcurementPaymentBaseInfo = threeProcurementPaymentReviewMainService.getThreeProcurementPaymentBaseInfo(processInstanceId);
        if (threeProcurementPaymentBaseInfo == null){
            throw new CrmException("付款流程不存在");
        }

        String contractId = threeProcurementPaymentBaseInfo.getContractId();
        return new JsonObject<>(contractReviewMainService.contractBasicInfo(contractId, true));
    }


    @PostMapping("/paymentConfirmation")
    @Operation(summary = "08财务专员付款确认-付款确认")
    @PreFlowPermission(hasAnyNodes = {"sid-5B8C625A-641F-46FF-A4CA-0CBD98DAACB0"})
    public JsonObject<Boolean> paymentConfirmation(@RequestBody ThreeProcurementPaymentReviewMainVo threeProcurementPaymentReviewMainVo){
        PreFlowPermissionAspect.checkProcessInstanceId(threeProcurementPaymentReviewMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        BigDecimal paymentAmount = threeProcurementPaymentReviewMainVo.getPaymentAmount();
        if (paymentAmount == null){
            throw new CrmException("付款金额不能为空");
        }
        ThreeProcurementPaymentReviewMain threeProcurementPaymentBaseInfo = threeProcurementPaymentReviewMainService.getThreeProcurementPaymentBaseInfo(threeProcurementPaymentReviewMainVo.getProcessInstanceId());
        if (threeProcurementPaymentBaseInfo == null){
            throw new CrmException("付款流程不存在");
        }
        BigDecimal payableAmount = threeProcurementReviewPaymentProvisionService.selectPayableAmount(threeProcurementPaymentBaseInfo.getProcurementProcessId(), threeProcurementPaymentBaseInfo.getPurchaseNumber(), true);
        int result = paymentAmount.compareTo(payableAmount);
        if (result > 0){
            throw new CrmException("付款金额不能大于应付金额");
        }
        ThreeProcurementPaymentReviewMain threeProcurementPaymentReviewMain = HyperBeanUtils.copyPropertiesByJackson(threeProcurementPaymentReviewMainVo, ThreeProcurementPaymentReviewMain.class);
        return new JsonObject<>(threeProcurementPaymentReviewMainService.paymentConfirmation(threeProcurementPaymentReviewMain));
    }


    @PostMapping("/exportReimbursementForm")
    @PreFlowPermission(hasAnyNodes = {"paymentApproval_07"})
    @Operation(summary = "付款审批07节点导出报销单")
    public void exportReimbursementForm(HttpServletResponse response,
                                        @RequestParam String processInstanceId,
                                        String purchaseNumber) throws IOException {

        // 获取当前登录用户信息
        String currentLoginName = UserInfoHolder.getCurrentLoginName();
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        CrmSimpleEmployeeVO crmSimpleEmployeeVO = getEmployeeInfo(currentPersonId);

        // 获取采购相关数据
        ThreeProcurementReviewMainVo reviewMainVo = threeProcurementReviewMainService.selectThreeProcurementReviewMainByPurchaseNumber(purchaseNumber);
        ThreeProcurementPaymentReviewMain paymentReviewBaseInfo = threeProcurementPaymentReviewMainService.getThreeProcurementPaymentBaseInfo(processInstanceId);
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<ThreeProcurementPaymentReviewMain> threeProcurementPaymentList = threeProcurementPaymentReviewMainService.getThreeProcurementPaymentList(purchaseNumber,null);
        ContractReviewCustomerDTO contractReviewCustomerDTO = contractReviewCustomerService.getByContractId(reviewMainVo.getContractId());
        String customerName = contractReviewCustomerDTO.getCustomerName();
        BigDecimal totalPurchasePrice = threeProcurementReviewMainService.selectTotalPurchasePrice(reviewMainVo.getProcessInstanceId());
        BigDecimal proposedPaymentAmount = paymentReviewBaseInfo.getProposedPaymentAmount();
        double ratio = Optional.ofNullable(totalPurchasePrice)
        .filter(pa -> pa.compareTo(BigDecimal.ZERO) != 0)
        .map(pa -> proposedPaymentAmount.divide(pa, 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .doubleValue())
        .orElse(0.0); // 或者返回其他默认值

        // 获取部门信息
        String deptName = crmSimpleEmployeeVO.getDeptName();
        TosDepartmentVO departmentVO = getDepartmentInfo(reviewMainVo.getDeptId());
        String departmentName = departmentVO.getName();

        // 创建报销单数据
        ReimbursementFormVo reimbursementFormVo = createReimbursementForm(currentLoginName, deptName, departmentName,
                paymentReviewBaseInfo, reviewMainVo,threeProcurementPaymentList,ratio,customerName);

        // 设置响应文件下载相关信息
        setUpResponseForFileDownload(response);

        // 使用 EasyExcel 生成 Excel 文件并写入响应流
        generateExcelFile(response.getOutputStream(), reimbursementFormVo);
    }

    private CrmSimpleEmployeeVO getEmployeeInfo(String personId) {
        return Optional.ofNullable(remoteAccountService.findSimpleEmployeeById(personId))
                .map(JsonObject::getObjEntity)
                .orElseThrow(() -> new CrmException("人员信息查询失败"));
    }

    private TosDepartmentVO getDepartmentInfo(String deptId) {
        return Optional.ofNullable(tosDepartmentClient.findById(deptId))
                .map(JsonObject::getObjEntity)
                .orElseThrow(() -> new CrmException("部门信息查询失败"));
    }

    private ReimbursementFormVo createReimbursementForm(String currentLoginName, String deptName, String departmentName,
                                                        ThreeProcurementPaymentReviewMain paymentReviewBaseInfo,
                                                        ThreeProcurementReviewMainVo reviewMainVo, List<ThreeProcurementPaymentReviewMain> threeProcurementPaymentList
                                    ,double ratio,String customerName) {
        // 创建报销单内容
        String processNumber = paymentReviewBaseInfo.getProcessNumber();
        String costBearer = getEmployeeInfo(reviewMainVo.getCreateUser()).getName();
        BigDecimal proposedPaymentAmount = paymentReviewBaseInfo.getProposedPaymentAmount();
        // 获取当前日期和时间
        LocalDateTime now = LocalDateTime.now();
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        // 将 LocalDateTime 转换为指定格式的字符串
        String formattedDate = now.format(formatter);
        return ReimbursementFormVo.builder()
                .name(currentLoginName)
                .deptName(deptName)
                .natureOfExpenditure("集成采购")
                .accountingDeptName(departmentName)
                .applicationTime(formattedDate)
                .natureOfFunds("汇款")
                .paymentFrequency("第"+threeProcurementPaymentList.size()+"次")
                .paymentRatio(ratio+"%")
                .orderNo(reviewMainVo.getPo())
                .contractNumber(reviewMainVo.getContractNumber())
                .costBearer(costBearer)
                .projectName(customerName)
                .projectNumber(processNumber)
                .paymentRecipient(reviewMainVo.getSupplierName())
                .money(proposedPaymentAmount)
                .bankName(paymentReviewBaseInfo.getBankName())
                .bankAccount(paymentReviewBaseInfo.getBankAccount())
                .amount(AmountToChineseConverter.convert(proposedPaymentAmount.doubleValue()))
                .build();
    }

    private void setUpResponseForFileDownload(HttpServletResponse response) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        long currentTime = System.currentTimeMillis();
        String fileName = URLEncoder.encode("报销单-" + currentTime, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
    }

    private void generateExcelFile(OutputStream outputStream, ReimbursementFormVo reimbursementFormVo){
        Resource resource = new ClassPathResource("excel/reimbursementForm.xlsx");
        try (InputStream inputStream = resource.getInputStream()) {
            ExcelWriterBuilder workBookWriter = EasyExcel.write(outputStream)
                    .withTemplate(inputStream);
            // 使用 sheet(0)，因为模板只有一个工作表
            workBookWriter.sheet(0)
                    .doFill(reimbursementFormVo);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }







}
