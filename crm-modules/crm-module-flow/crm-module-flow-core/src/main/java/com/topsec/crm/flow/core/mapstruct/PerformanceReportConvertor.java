package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.performancereport.*;
import com.topsec.crm.flow.core.entity.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PerformanceReportConvertor {
    PerformanceReportConvertor INSTANCE = Mappers.getMapper(PerformanceReportConvertor.class);

    PerformanceReportVO toReportVO(PerformanceReport baseInfo);


    @Mapping(target = "processState", ignore = true)
    @Mapping(target = "processNumber", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    PerformanceReport toReport(PerformanceReportVO baseInfoLaunchDTO);

    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    PerformanceReportContractDelivery toContractDelivery(PerformanceReportContractDeliveryDTO contractDeliveryDTO);
    List<PerformanceReportContractDelivery> toContractDeliveryList(List<PerformanceReportContractDeliveryDTO> contractDeliveryDTOs);

    @Mapping(target = "returnExchangeProcessInstanceId", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    PerformanceReportPaymentTerms toPaymentTerms(PerformanceReportPaymentTermsDTO paymentTermsLaunchDTO);
    List<PerformanceReportPaymentTerms> toPaymentTermsList(List<PerformanceReportPaymentTermsDTO> paymentTermsLaunchDTOS);



    @Mapping(target = "deliveryInfoMap", ignore = true)
    PerformanceReportContractDeliveryDTO  toContractDeliveryVO(PerformanceReportContractDelivery contractDelivery);
    List<PerformanceReportContractDeliveryDTO> toContractDeliveryVOList(List<PerformanceReportContractDelivery> contractDeliveryDTOs);


    PerformanceReportPaymentTermsDTO  toPaymentTermsVO(PerformanceReportPaymentTerms paymentTermsLaunchDTO);
    List<PerformanceReportPaymentTermsDTO> toPaymentTermsVOList(List<PerformanceReportPaymentTerms> paymentTermsLaunchDTOS);



    @Mapping(target = "returnNum", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "specificationId", ignore = true)
    @Mapping(target = "scarceGoodsOrderNum", ignore = true)
    @Mapping(target = "reportStatus", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "isSpecialItem", ignore = true)
    @Mapping(target = "isBorrowForForward", ignore = true)
    @Mapping(target = "hostSerialNumber", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "attr", ignore = true)
    @Mapping(target = "id", ignore = true)
    PerformanceReportProductOwn toProductOwn(PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO productOwnLaunchDTO );

    List<PerformanceReportProductOwn> toProductOwnList(List<PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO> productOwnLaunchDTOS);



    @Mapping(target = "zdProjectProductId", ignore = true)
    @Mapping(target = "wholesalePrice", ignore = true)
    @Mapping(target = "sellInPrice", ignore = true)
    @Mapping(target = "scarceGoodsNum", ignore = true)
    @Mapping(target = "replaceSoftware", ignore = true)
    @Mapping(target = "reTag", ignore = true)
    @Mapping(target = "projectId", ignore = true)
    @Mapping(target = "priceDifference", ignore = true)
    @Mapping(target = "performanceReportProductSeparationDTOS", ignore = true)
    @Mapping(target = "performanceReportProductOwnSnDTOS", ignore = true)
    @Mapping(target = "children", ignore = true)
    PerformanceReportProductOwnDTO toProductOwnDto(PerformanceReportProductOwn productOwnLaunchDTO );

    List<PerformanceReportProductOwnDTO> toProductOwnDtoList(List<PerformanceReportProductOwn> own);

    @Mapping(target = "storageTime", ignore = true)
    PerformanceReportProductOwnSnDTO toProductOwnSnVO(PerformanceReportProductOwnSn productOwnSn);
    List<PerformanceReportProductOwnSnDTO> toProductOwnSnVOList(List<PerformanceReportProductOwnSn> productOwnSns);


    PerformanceReportPaymentInfoDTO toPaymentInfoVO(PerformanceReportPaymentInfo paymentInfo);
    List<PerformanceReportPaymentInfoDTO> toPaymentInfoVOList(List<PerformanceReportPaymentInfo> paymentInfos);




    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    PerformanceReportDoc toDoc(PerformanceReportDocDTO docDTO);
    List<PerformanceReportDoc> toDocList(List<PerformanceReportDocDTO> docDTOS);


    PerformanceReportDocDTO toDocVO(PerformanceReportDoc doc);
    List<PerformanceReportDocDTO> toDocVOList(List<PerformanceReportDoc> docs);

    @Mapping(target = "deliveryInfoProductList",ignore = true)
    @Mapping(target = "isContractWaived", expression = "java(deliveryDTO.getIsContractWaived() ==1?\"是\":\"否\")")
    @Mapping(target = "deliveryMethod", expression = "java(com.topsec.crm.framework.common.enums.ShipmentMethodEnum.fromCode(deliveryDTO.getDeliveryMethod()).getDescription())")
    PerformanceReportDeliveryExportDTO toDeliveryExportDto(PerformanceReportContractDeliveryDTO deliveryDTO);



}
