package com.topsec.crm.flow.core.controller.contractreceivable;

import com.github.pagehelper.PageHelper;
import com.topsec.crm.flow.api.dto.contractreceivable.ContractReceivableDetailDTO;
import com.topsec.crm.flow.api.dto.contractreceivable.ContractReceivableMainDTO;
import com.topsec.crm.flow.api.dto.contractreceivable.check.ReceivableConditionDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ContractReceivableChild;
import com.topsec.crm.flow.core.entity.ContractReceivableDetail;
import com.topsec.crm.flow.core.entity.ContractReceivableMain;
import com.topsec.crm.flow.core.error.ErrorDetail;
import com.topsec.crm.flow.core.service.ContractReceivableChildService;
import com.topsec.crm.flow.core.service.ContractReceivableDetailService;
import com.topsec.crm.flow.core.service.ContractReceivableService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 子应收催款流程
 *
 * <AUTHOR>
 * @date 2024/12/20 10:33
 */
@RestController
@RequestMapping("/contractReceivableChild")
@Tag(name = "子应收催款流程", description = "/contractReceivableChild")
@RequiredArgsConstructor
public class ContractReceivableChildController extends BaseController {

    private final ContractReceivableChildService childService;

    private final ContractReceivableService receivableService;

    private final ContractReceivableDetailService detailService;

    @GetMapping("/receivableChildBasicInfo")
    @Operation(summary = "子应收催款基础信息", method = "GET")
    @PreFlowPermission
    public JsonObject<ContractReceivableMainDTO> receivableChildBasicInfo(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(receivableService.receivableChildBasicInfo(processInstanceId));
    }

    @GetMapping("/pageReceivableChildDetail")
    @Operation(summary = "子应收催款分页查询催缴跟进页面数据", method = "GET")
    @PreFlowPermission
    public JsonObject<PageUtils<ContractReceivableDetailDTO>> pageReceivableChildDetail(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        try {
            ContractReceivableChild child = childService.getByProcessInstanceId(processInstanceId);
            startPage();
            return new JsonObject<>(receivableService.pageReceivableChildDetail(child.getId()));
        } finally {
            PageHelper.clearPage();
        }
    }

    @PostMapping("/checkReceivableChildCondition")
    @Operation(summary = "子应收催款审批校验")
    @PreFlowPermission
    JsonObject<List<ErrorDetail>> checkReceivableChildCondition(@RequestBody ReceivableConditionDTO receivableConditionDTO){
        PreFlowPermissionAspect.checkProcessInstanceId(receivableConditionDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<ErrorDetail> errorDetails = receivableService.checkReceivableChildCondition(receivableConditionDTO);
        return new JsonObject<>(errorDetails);
    }

    @PostMapping("/saveOrUpdateReceivableChildFollow")
    @Operation(summary = "子应收催款保存或更新催缴跟进信息", method = "POST")
    @PreFlowPermission
    public JsonObject<Boolean> saveOrUpdateReceivableChildFollow(@RequestBody ContractReceivableDetailDTO dto) {
        ContractReceivableDetail detail = detailService.getById(dto.getId());
        String contractReceivableChildId = detail.getContractReceivableChildId();
        ContractReceivableChild receivableChild = childService.getById(contractReceivableChildId);
        if (receivableChild == null) {
            throw new CrmException("应收催款不存在");
        }
        PreFlowPermissionAspect.checkProcessInstanceId(receivableChild.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(receivableService.saveOrUpdateReceivableChildFollow(dto));
    }
}
