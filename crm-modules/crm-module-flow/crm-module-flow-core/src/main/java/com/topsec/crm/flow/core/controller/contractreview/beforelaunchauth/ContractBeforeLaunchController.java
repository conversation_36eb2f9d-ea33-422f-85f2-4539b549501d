package com.topsec.crm.flow.core.controller.contractreview.beforelaunchauth;

import cn.hutool.core.collection.CollectionUtil;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDetailDTO;
import com.topsec.crm.flow.api.dto.contractreview.fileinfo.ContractElectricContractDTO;
import com.topsec.crm.flow.api.dto.contractreview.fileinfo.ContractReviewAttachmentDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnSntVO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.dto.contractreview.response.ContractAttachmentStatus;
import com.topsec.crm.flow.api.dto.contractreview.salemaninfo.SalesSelectDTO;
import com.topsec.crm.flow.api.dto.contractreview.statistics.ContractExecuteStatisticsVO;
import com.topsec.crm.flow.api.dto.contractreview.statusinfo.ContractTermAndDeliveryStatusDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.RevenueRecognitionDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.ReviewRetentionMoneyDTO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteStatisticsVO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewMainInfo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementProcessQuery;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.HomeNameValue;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectSignAgentClient;
import com.topsec.crm.project.api.entity.AgentTreeSelect;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.CrmProjectSigningAgentVo;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@Tag(name = "合同评审发起之前的接口")
@RequiredArgsConstructor
@RequestMapping("/beforeLaunch")
public class ContractBeforeLaunchController extends BaseController {

    private final ContractReviewMainService contractReviewMainService;
    private final ContractReviewProductOwnService contractReviewProductOwnService;
    private final ContractReviewProductThirdService contractReviewProductThirdService;
    private final ContractReviewAttachmentService contractReviewAttachmentService;
    private final ContractReviewDeliveryService contractReviewDeliveryService;
    private final ContractReviewPaymentProvisionService contractReviewPaymentProvisionService;
    private final ContractReviewRevenueRecognitionService reviewRevenueRecognitionService;
    private final ContractReviewRetentionMoneyService contractReviewRetentionMoneyService;
    private final ContractReviewFlowService contractReviewFlowService;
    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final PriceReviewMainService priceReviewMainService;
    private final RemoteProjectSignAgentClient remoteProjectSignAgentClient;
    private final SalesAgreementReviewMainService salesAgreementReviewMainService;
    private final DeemedDirectRecordService deemedDirectRecordService;

    @GetMapping("/contractReview/salesSelect")
    @Operation(summary = "销售人员下拉")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<SalesSelectDTO> salesSelect(@RequestParam String projectId) {
        checkProjectAuth(projectId);
        return new JsonObject<>(contractReviewMainService.salesSelect(projectId));
    }

    @GetMapping("/contractReview/customerAndSignContractByProject")
    @Operation(summary = "客户和签约单位信息通过项目查询")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<ContractReviewMainFlowLaunchDTO> customerAndSignContractByProject(@RequestParam String projectId) {
        checkProjectAuth(projectId);
        return new JsonObject<>(contractReviewMainService.customerAndSignContractByProject(projectId));
    }

    @GetMapping("/contractReview/linkmanSelect")
    @Operation(summary = "联系人下拉")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<List<HomeNameValue<String, String>>> linkmanSelect(@RequestParam String projectId) {
        checkProjectAuth(projectId);
        return new JsonObject<>(contractReviewMainService.linkmanSelect(projectId));
    }

    @GetMapping("/contractReview/productOwnInfoByProjectId")
    @Operation(summary = "根据项目id取项目中的自有产品信息")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<TableDataInfo> productOwnInfoByProjectId(@RequestParam String projectId, @RequestParam(required = false) String contractId) {
        checkProjectAuth(projectId);
        return new JsonObject<>(contractReviewProductOwnService.productInfoByProjectId(projectId, contractId));
    }

    @GetMapping("/contractReview/productThirdInfoByProjectId")
    @Operation(summary = "根据项目id取项目中的第三方产品信息")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<TableDataInfo> productThirdInfoByProjectId(@RequestParam String projectId, @RequestParam(required = false) String contractId) {
        checkProjectAuth(projectId);
        return new JsonObject<>(contractReviewProductThirdService.productInfoByProjectId(projectId, contractId));
    }

    @PostMapping("/contractReview/saveContractInfo")
    @Operation(summary = "保存或修改业务数据,根据dto是否传id判断")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<String> saveOrUpdateContractInfo(@RequestBody ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO) {
        // 加一个项目的权限，根据当前登录人看看有没有项目的权限，发起之前的话 要根据当前人判断
        String projectId = contractReviewMainFlowLaunchDTO.getBaseInfoDTO().getProjectId();
        assert projectId != null;
        checkProjectAuth(projectId);
        ContractReviewMainBaseInfoDTO baseInfoDTO = contractReviewMainFlowLaunchDTO.getBaseInfoDTO();
        if (baseInfoDTO.getId() == null) {
            // 新增 不需要校验
        } else {
            // 修改，判断List中的id是否和合同id一致 其余都是根据contractId set的 批量的不是，
            checkAllSaveLaunchDTO(contractReviewMainFlowLaunchDTO);
        }
        return new JsonObject<>(contractReviewMainService.saveDTO(contractReviewMainFlowLaunchDTO));
    }


    @GetMapping("/contractReview/contractInfo")
    @Operation(summary = "合同评审信息")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<ContractReviewMainFlowLaunchDTO> contractInfo(@RequestParam String contractId) {
        // 草稿接口，待发起的时候，自己看自己的
        ContractReviewMainFlowLaunchDTO launchDTO = contractReviewMainService.contractInfo(contractId, false, true);
        checkCreateUserAndCurrentPersonId(launchDTO.getBaseInfoDTO().getCreateUser());
        List<ContractProductThirdDTO> contractProductThirdDTOS = launchDTO.getContractProductThirdDTOS();
        ListUtils.emptyIfNull(contractProductThirdDTOS).forEach(contractReviewProductThirdService::mosaicProductPrice);
        return new JsonObject<>(launchDTO);
    }


    @GetMapping("/contractReview/getExecuteStatistics")
    @Operation(summary = "合同评审统计信息")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<ContractExecuteStatisticsVO> getExecuteStatistics(@RequestParam String contractId) {
        // 草稿接口，待发起的时候，自己看自己的
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewMainService.getContractExecuteStatistics(contractId));
    }

    @GetMapping("/contractReview/getPerformanceExecuteStatistics")
    @Operation(summary = "业绩统计信息")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<PerformanceExecuteStatisticsVO> getPerformanceExecuteStatistics(@RequestParam String contractId) {
        // 草稿接口，待发起的时候，自己看自己的
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewMainService.getPerformanceExecuteStatistics(contractId));
    }


    @GetMapping("/contractAttachment/getAttachmentByContractId")
    @Operation(summary = "根据合同id获取附件")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<List<ContractReviewAttachmentDTO>> getByContractId(@RequestParam String contractId) {
        // 草稿接口，待发起的时候，自己看自己的
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewAttachmentService.getByContractId(contractId));
    }

    @GetMapping("/contractDelivery/getDeliveryInfoList")
    @Operation(summary = "根据合同ID获取合同发货信息", description = "合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<List<ContractDeliveryDTO>> getDeliveryInfoList(@RequestParam String contractId){
        // 草稿接口，待发起的时候，自己看自己的
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage(),contractReviewDeliveryService.getContractDeliveryInfoList(contractId));
    }

    @GetMapping("/contractTerm/pagePaymentProvisionByContractId")
    @Operation(summary = "根据合同id查询付款条款")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<TableDataInfo> pagePaymentProvisionByContractId(@RequestParam String contractId) {
        // 草稿接口，待发起的时候，自己看自己的
        checkCreateUserByContractId(contractId);
        startPage();
        return new JsonObject<>(contractReviewPaymentProvisionService.pageByContractId(contractId));
    }

    @GetMapping("/contractTerm/pageRevenueRecognitionByContractId")
    @Operation(summary = "根据合同id查询收入确认条款")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<TableDataInfo> pageByContractId(@RequestParam String contractId) {
        // 草稿接口，待发起的时候，自己看自己的
        checkCreateUserByContractId(contractId);
        startPage();
        return new JsonObject<>(reviewRevenueRecognitionService.pageByContractId(contractId));
    }

    @GetMapping("/contractTerm/pageRetentionMoneyByContractId")
    @Operation(summary = "根据合同id查询质保金条款")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<ReviewRetentionMoneyDTO> pageRetentionMoneyByContractId(@RequestParam String contractId) {
        // 草稿接口，待发起的时候，自己看自己的
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewRetentionMoneyService.getByContractId(contractId));
    }

    @GetMapping("/contractReview/getTermAndDeliveryStatus")
    @Operation(summary = "获取条款以及发货信息的填写状态")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<ContractTermAndDeliveryStatusDTO> getTermAndDeliveryStatus(@RequestParam String contractId) {
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewFlowService.getTermAndDeliveryStatus(contractId));
    }

    @PostMapping("/contractTerm/saveOrUpdatePaymentProvision")
    @Operation(summary = "修改或保存付款条款(实时逻辑)")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> saveOrUpdatePaymentProvisionBatch(@RequestBody PaymentProvisionDTO paymentProvisionDTO) {
        assert paymentProvisionDTO != null;
        if (paymentProvisionDTO.getId() == null) {
            // 新增的时候直接判断contractId
            String contractId = paymentProvisionDTO.getContractReviewMainId();
            checkCreateUserByContractId(contractId);
        } else {
            // 修改的时候 要先根据id查出来
            ContractReviewPaymentProvision paymentProvision = contractReviewPaymentProvisionService.getById(paymentProvisionDTO.getId());
            if (paymentProvision == null) {
                throw new CrmException("参数有误");
            }
            if (!paymentProvision.getContractReviewMainId().equals(paymentProvisionDTO.getContractReviewMainId())) {
                throw new CrmException("参数有误");
            }
            checkCreateUserByContractId(paymentProvision.getContractReviewMainId());
        }
        return new JsonObject<>(contractReviewPaymentProvisionService.saveOrUpdate(paymentProvisionDTO));
    }

    @PostMapping("/contractTerm/saveOrUpdateRevenueRecognition")
    @Operation(summary = "修改或保存收入确认条款(实时逻辑)")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> saveOrUpdateRevenueRecognition(@RequestBody RevenueRecognitionDTO revenueRecognitionDTO) {
        assert revenueRecognitionDTO != null;
        if (revenueRecognitionDTO.getId() == null) {
            // 新增的时候直接判断contractId
            String contractId = revenueRecognitionDTO.getContractReviewMainId();
            checkCreateUserByContractId(contractId);
        } else {
            // 修改的时候 要先根据id查出来
            ContractReviewRevenueRecognition revenueRecognition = reviewRevenueRecognitionService.getById(revenueRecognitionDTO.getId());
            if (revenueRecognition == null) {
                throw new CrmException("参数有误");
            }
            revenueRecognitionDTO.setContractReviewMainId(null);
            checkCreateUserByContractId(revenueRecognition.getContractReviewMainId());
        }
        return new JsonObject<>(reviewRevenueRecognitionService.saveOrUpdate(revenueRecognitionDTO));
    }

    @GetMapping("/contractTerm/deletePaymentProvisionById")
    @Operation(summary = "根据id删除付款条款")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> deletePaymentProvisionById(@RequestParam String id) {
        // 判断这个id 是不是这个人加的
        ContractReviewPaymentProvision contractReviewPaymentProvision = contractReviewPaymentProvisionService.getById(id);
        if (contractReviewPaymentProvision == null || contractReviewPaymentProvision.getDelFlag() == 1) {
            throw new CrmException("条款不存在");
        }
        String createUser = contractReviewPaymentProvision.getCreateUser();
        checkCreateUserAndCurrentPersonId(createUser);
        return new JsonObject<>(contractReviewPaymentProvisionService.deleteById(id));
    }

    @GetMapping("/contractTerm/deleteRevenueRecognitionById")
    @Operation(summary = "根据id删除收入确认条款")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> productValidStraightLine(@RequestParam String id) {
        ContractReviewRevenueRecognition revenueRecognition = reviewRevenueRecognitionService.getById(id);
        if (revenueRecognition == null || revenueRecognition.getDelFlag() == 1) {
            throw new CrmException("条款不存在");
        }
        String createUser = revenueRecognition.getCreateUser();
        checkCreateUserAndCurrentPersonId(createUser);
        return new JsonObject<>(reviewRevenueRecognitionService.deleteById(id));
    }

    @GetMapping("/contractReview/productOwnInfoTileByContractId")
    @Operation(summary = "根据合同id取合同中的自有产品信息（平铺）")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<List<ContractProductOwnDTO>> productOwnInfoTileByContractId(@RequestParam String contractId) {
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewProductOwnService.productOwnInfoTileByContractId(contractId, true));
    }


    @GetMapping("/contractReview/productOwnInfoByContractId")
    @Operation(summary = "根据合同id取合同中的自有产品信息（树结构）")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<List<ContractProductOwnDTO>> productOwnInfoByContractId(@RequestParam String contractId) {
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewProductOwnService.productInfoByContractId(contractId, true));
    }

    @GetMapping("/contractReview/contractInfoBasic")
    @Operation(summary = "合同评审信息")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<ContractReviewMainFlowLaunchDTO> contractInfoBasic(@RequestParam String contractId) {
        // 草稿接口，待发起的时候，自己看自己的
        ContractReviewMainFlowLaunchDTO launchDTO = contractReviewMainService.contractInfo(contractId, false, false);
        checkCreateUserAndCurrentPersonId(launchDTO.getBaseInfoDTO().getCreateUser());
        return new JsonObject<>(launchDTO);
    }

    @GetMapping("/contractReview/deleteContract")
    @Operation(summary = "删除合同")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> deleteContract(@RequestParam String contractId) {
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewFlowService.deleteContract(contractId));
    }

    @GetMapping("/contractReview/productThirdInfoByContractId")
    @Operation(summary = "根据合同id取合同中的第三方产品信息")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<List<ContractProductThirdDTO>> productThirdInfoByContractId(@RequestParam String contractId) {
        checkCreateUserByContractId(contractId);
        List<ContractProductThirdDTO> contractProductThirdDTOS = contractReviewProductThirdService.productThirdInfoByContractId(contractId, true);
        ListUtils.emptyIfNull(contractProductThirdDTOS).forEach(contractReviewProductThirdService::mosaicProductPrice);
        return new JsonObject<>(contractProductThirdDTOS);
    }

    @PostMapping("/contractDelivery/saveDeliveryInfo")
    @Operation(summary = "发货信息保存", description = "合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> saveDeliveryInfo(@Valid @RequestBody ContractDeliveryDTO deliveryDTO){
        assert deliveryDTO != null;
        String contractId = deliveryDTO.getContractId();
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewDeliveryService.saveDeliveryInfo(deliveryDTO));
    }

    @PostMapping("/contractDelivery/saveOrUpdateDeliveryDetail")
    @Operation(summary = "发货产品明细保存或修改", description = "合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> saveOrUpdateDeliveryDetail(@RequestBody ContractDeliveryDTO deliveryDTO){
        assert deliveryDTO != null;
        if (deliveryDTO.getId() == null) {
            // 新增的时候直接判断contractId
            String contractId = deliveryDTO.getContractId();
            checkCreateUserByContractId(contractId);
        } else {
            // 修改的时候 要先根据id查出来
            ContractReviewDelivery delivery = contractReviewDeliveryService.getById(deliveryDTO.getId());
            if (delivery == null) {
                throw new CrmException("参数有误");
            }
            deliveryDTO.setContractId(null);
            checkCreateUserByContractId(delivery.getContractId());
        }
        return new JsonObject<>(contractReviewDeliveryService.saveOrUpdateDeliveryDetail(deliveryDTO));
    }

    @GetMapping("/contractDelivery/deleteById")
    @Operation(summary = "删除发货", description = "合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> deleteById(@RequestParam String id){
        // 判断这个id 是不是这个人加的
        ContractReviewDelivery delivery = contractReviewDeliveryService.getById(id);
        if (delivery == null || delivery.getDelFlag()) {
            throw new CrmException("发货信息不存在");
        }
        String createUser = delivery.getCreateUser();
        checkCreateUserAndCurrentPersonId(createUser);
        return new JsonObject<>(contractReviewDeliveryService.deleteById(id));
    }

    @GetMapping("/contractDelivery/getProductPageByContractId")
    @Operation(summary = "根据合同ID获取合同产品列表", description = "合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<PageUtils<ContractDeliveryDetailDTO>> getProductPageByContractId(@RequestParam String contractId, String deliveryId, Integer pageNum, Integer pageSize){
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage(),contractReviewDeliveryService.getProductPageByContractId(contractId, deliveryId,pageNum, pageSize));
    }


    @GetMapping("/contractDelivery/getDeliveredProductPage")
    @Operation(summary = "根据发货ID获取发货分页列表", description = "合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<TableDataInfo> getDeliveredProductPage(@RequestParam String deliveryId){
        // 判断这个id 是不是这个人加的
        ContractReviewDelivery delivery = contractReviewDeliveryService.getById(deliveryId);
        if (delivery == null || delivery.getDelFlag()) {
            throw new CrmException("发货信息不存在");
        }
        String createUser = delivery.getCreateUser();
        checkCreateUserAndCurrentPersonId(createUser);
        startPage();
        return new JsonObject<>(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage(),contractReviewDeliveryService.getContractDeliveryProductPage(deliveryId));
    }

    @GetMapping("/contractDelivery/isAllDelivery")
    @Operation(summary = "判断合同是否全部发货", description = "合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> isAllDelivery(@RequestParam String contractId){
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewDeliveryService.isAllDelivery(contractId));
    }

    @GetMapping("/contractReview/changeStep")
    @Operation(summary = "下一步")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> step(@RequestParam Integer step, @RequestParam String contractId) {
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewMainService.changeStep(step, contractId));
    }

    @PostMapping("/contractAttachment/upload")
    @Operation(summary = "上传合同附件")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> upload(@RequestBody ContractReviewAttachmentDTO contractReviewAttachmentDTO) {
        assert contractReviewAttachmentDTO != null;
        checkCreateUserByContractId(contractReviewAttachmentDTO.getContractReviewMainId());
        return new JsonObject<>(contractReviewAttachmentService.saveContractAttachment(contractReviewAttachmentDTO));
    }

    @GetMapping("/contractAttachment/skipUpload")
    @Operation(summary = "跳过上传合同附件")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> skipUpload(@RequestParam String contractId) {
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewAttachmentService.skipUpload(contractId));
    }

    @GetMapping("/contractAttachment/delete")
    @Operation(summary = "删除附件")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> delete(@RequestParam String id) {
        ContractReviewAttachment attachment = contractReviewAttachmentService.getById(id);
        if (attachment == null || attachment.getDelFlag() == 1) {
            throw new CrmException("附件不存在");
        }
        checkCreateUserAndCurrentPersonId(attachment.getCreateUser());
        return new JsonObject<>(contractReviewAttachmentService.delete(id));
    }

    @PostMapping("/contractAttachment/generateElectricContract")
    @Operation(summary = "生成电子合同")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<String> generateElectricContract(@RequestBody ContractElectricContractDTO contractReviewMainFlowLaunchDTO) {
        assert contractReviewMainFlowLaunchDTO != null;
        checkCreateUserByContractId(contractReviewMainFlowLaunchDTO.getContractId());
        return new JsonObject<>(contractReviewAttachmentService.generateElectricContract(contractReviewMainFlowLaunchDTO, false, UserInfoHolder.getCurrentPersonId()));
    }

    @PostMapping("/contractAttachment/preViewElectricContract")
    @Operation(summary = "预览电子合同")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<String> preViewElectricContract(@RequestBody ContractElectricContractDTO contractReviewMainFlowLaunchDTO) {
        assert contractReviewMainFlowLaunchDTO != null;
        checkCreateUserByContractId(contractReviewMainFlowLaunchDTO.getContractId());
        return new JsonObject<>(contractReviewAttachmentService.generateElectricContract(contractReviewMainFlowLaunchDTO, true, UserInfoHolder.getCurrentPersonId()));
    }

    @GetMapping("/contractAttachment/checkAttachment")
    @Operation(summary = "查当前合同是否有电子合同和普通附件")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<ContractAttachmentStatus> checkAttachment(@RequestParam String contractId) {
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewAttachmentService.checkAttachment(contractId));
    }


    @GetMapping("/contractReview/deemedDirectCheck")
    @Operation(summary = "判断是不是要显示签约单位备案的入口")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> deemedDirectCheck(@RequestParam String contractId) {
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewFlowService.deemedDirectCheck(contractId));
    }

    @GetMapping("/contractReview/isRegional")
    @Operation(summary = "合同的销售人员是否为区域销售")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> isRegional(@RequestParam String contractId) {
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewFlowService.isRegional(contractId));
    }

    @GetMapping("/contractReview/taxRateIsChange")
    @Operation(summary = "税率是否改变")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> taxRateIsChange(@RequestParam String contractId) {
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewFlowService.taxRateIsChange(contractId));
    }

    @GetMapping("/contractReview/changeProjectTaxRate")
    @Operation(summary = "改变项目中的税率")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> changeProjectTaxRate(@RequestParam String contractId) {
        checkCreateUserByContractId(contractId);
        return new JsonObject<>(contractReviewFlowService.changeProjectTaxRate(contractId));
    }

    @PostMapping("/contractReview/productOwnSnByContractIds")
    @Operation(summary = "根据合同id查询出货产品详情")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<TableDataInfo> productOwnSnByContractId(@RequestBody ContractProductOwnSntVO contractProductOwnSntVO) {
        assert contractProductOwnSntVO != null;
        checkCreateUserByContractId(contractProductOwnSntVO.getContractId());
        return new JsonObject<>(contractReviewProductOwnService.productOwnSnByContractId(contractProductOwnSntVO));
    }

    @PostMapping("/contractTerm/saveOrUpdateRevenueRecognitionAll")
    @Operation(summary = "修改或保存收入确认条款多条(实时逻辑)")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> saveOrUpdateRevenueRecognition(@RequestBody List<RevenueRecognitionDTO> revenueRecognitionDTOS) {
        assert revenueRecognitionDTOS != null;
        // 新增的判断contractId
        Set<String> contractIds = revenueRecognitionDTOS.stream().filter(item -> item.getId() == null).map(RevenueRecognitionDTO::getContractReviewMainId).filter(Objects::nonNull).collect(Collectors.toSet());
        // 修改的根据id 查出来contractId 并且把contractId set成null
        Set<String> ids = revenueRecognitionDTOS.stream().filter(item -> item.getId() != null).map(RevenueRecognitionDTO::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(ids)) {
            List<ContractReviewRevenueRecognition> contractReviewRevenueRecognitions = reviewRevenueRecognitionService.listByIds(ids);
            Set<String> contractIds1 = ListUtils.emptyIfNull(contractReviewRevenueRecognitions).stream().filter(item -> item.getDelFlag() == 0).map(ContractReviewRevenueRecognition::getContractReviewMainId).filter(Objects::nonNull).collect(Collectors.toSet());
            contractIds.addAll(contractIds1);
        }
        revenueRecognitionDTOS.forEach(item -> {
            if (item.getId() != null) {
                // 修改的时候 不让修改的内容，
                item.setContractReviewMainId(null);
            }
        });

        checkCreateUserByContractIds(contractIds);
        return new JsonObject<>(reviewRevenueRecognitionService.saveOrUpdateAll(revenueRecognitionDTOS));
    }

    @PostMapping("/contractTerm/saveOrUpdateRetentionMoney")
    @Operation(summary = "修改或保存质保金条款")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> saveOrUpdateRetentionMoney(@RequestBody ReviewRetentionMoneyDTO retentionMoneyDTO) {
        assert retentionMoneyDTO != null;
        if (retentionMoneyDTO.getId() == null) {
            // 新增的时候直接判断contractId
            String contractId = retentionMoneyDTO.getContractReviewMainId();
            checkCreateUserByContractId(contractId);
        } else {
            // 修改的时候 要先根据id查出来
            ContractReviewRetentionMoney money = contractReviewRetentionMoneyService.getById(retentionMoneyDTO.getId());
            if (money == null) {
                throw new CrmException("参数有误");
            }
            retentionMoneyDTO.setContractReviewMainId(null);
            checkCreateUserByContractId(money.getContractReviewMainId());
        }
        return new JsonObject<>(contractReviewRetentionMoneyService.saveOrUpdate(retentionMoneyDTO));
    }

    @Operation(summary = "查询项目最后一个通过的价审信息")
    @GetMapping("/contractReview/queryLatestPassedPriceReviewMainByContractId")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<PriceReviewMainInfo> queryLatestPassedPriceReviewMainByProjectId(@RequestParam String contractId) {
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        checkCreateUserAndCurrentPersonId(contractInfo.getCreateUser());
        String projectId = contractInfo.getProjectId();
        return new JsonObject<>(priceReviewMainService.queryLatestPassedPriceReviewMainByProjectId(projectId));
    }

    @GetMapping("/contractReview/queryLatestPassedPriceReviewSnapshotByProjectId")
    @Operation(summary = "查询项目最后一个通过的价审快照")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<CrmProjectDirectlyVo> queryLatestPassedPriceReviewSnapshotByProjectId(@RequestParam String projectId) {
        PriceReviewMainInfo priceReviewMainInfo = priceReviewMainService.queryLatestPassedPriceReviewMainByProjectId(projectId);
        if (priceReviewMainInfo != null) {
            CrmProjectDirectlyVo crmProjectDirectlyVo = priceReviewMainService.queryProjectInfo(priceReviewMainInfo.getProcessInstanceId());
            return new JsonObject<>(crmProjectDirectlyVo);
        } else {
            return new JsonObject<>(new CrmProjectDirectlyVo());
        }
    }


    @GetMapping("/agent/tree/{contractId}")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<List<AgentTreeSelect>> tree(@PathVariable String contractId) {
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        checkCreateUserAndCurrentPersonId(contractInfo.getCreateUser());
        String projectId = contractInfo.getProjectId();
        return remoteProjectSignAgentClient.tree(projectId);
    }


    @GetMapping("/agent/selectSigningAgentDetail")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<CrmAgentVo> selectSigningAgentDetail(@RequestParam String contractId, @RequestParam String agentId) {
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        checkCreateUserByContractId(contractId);
        String projectId = contractInfo.getProjectId();
        //查询项目
        JsonObject<List<CrmProjectSigningAgentVo>> listJsonObject = remoteProjectSignAgentClient.listAgent(projectId);
        if(listJsonObject.isSuccess()){
            List<CrmProjectSigningAgentVo> signs = listJsonObject.getObjEntity();
            if(CollectionUtil.isEmpty(signs)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
            Set<String> collect = signs.stream().map(CrmProjectSigningAgentVo::getAgentId).collect(Collectors.toSet());
            if(!collect.contains(agentId)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }
        CrmProjectSigningAgentVo query = new CrmProjectSigningAgentVo();
        query.setAgentId(agentId);
        query.setProjectId(projectId);
        return remoteProjectSignAgentClient.selectSigningAgentDetail(query);
    }

    @GetMapping("/contractReview/getSalesAgreementsByProjectId")
    @Operation(description = "根据项目id获取价审中的关联的销售协议")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<List<SalesAgreementReviewMain>> getSalesAgreementsByProjectId(@RequestParam String projectId) {
        checkProjectAuth(projectId);
        Set<String> saleAgreements = priceReviewMainService.querySaleAgreementOfLatestPassedPriceReview(projectId);
        List<SalesAgreementReviewMain> salesAgreementByIds = salesAgreementReviewMainService.findSalesAgreementByIds(new ArrayList<>(saleAgreements));
        return new JsonObject<>(salesAgreementByIds);
    }

    @GetMapping("/contractReview/getSalesAgreementsByCondition")
    @Operation(description = "条件查询销售协议")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<List<SalesAgreementReviewMain>> getSalesAgreementsByCondition(SalesAgreementProcessQuery query){
        // new一个查询对象 避免查询透传
        SalesAgreementProcessQuery query1 = new SalesAgreementProcessQuery();
        query1.setAgreementName(query.getAgreementName());
        // todo 应该是当前人的id 用personId 和wly确认
        query1.setPersonId(UserInfoHolder.getCurrentPersonId());
        query1.setDeptId(getDepartmentId());
        List<SalesAgreementReviewMain> salesAgreementBusinessList = salesAgreementReviewMainService.findSalesAgreementBusinessList(query1);
        return new JsonObject<>(salesAgreementBusinessList);
    }

    @GetMapping("/contractReview/getSalesAgreementsById")
    @Operation(description = "根据合同id查询销售协议")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<List<SalesAgreementReviewMain>> getSalesAgreementsById(@RequestParam String contractId) {
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        if (contractInfo == null) {
            throw new CrmException("该合同信息不存在");
        }
        checkCreateUserAndCurrentPersonId(contractInfo.getCreateUser());
        List<String> salesAgreements = contractInfo.getSalesAgreements();
        if (CollectionUtil.isEmpty(salesAgreements)) {
            return new JsonObject<>(Collections.emptyList());
        }
        return new JsonObject<>(salesAgreementReviewMainService.findSalesAgreementByIds(salesAgreements));
    }


    @GetMapping("/queryDeemedDirect")
    @Operation(description = "根据签约单位判断是否备案过")
    @PreAuthorize(hasAnyPermission = {"crm_flow_contract_review", "crm_flow_contract_review_zd"})
    public JsonObject<Boolean> queryDeemedDirect(@RequestParam String companyCompanyName){
        return new JsonObject<>(deemedDirectRecordService.IsExistByCompanyName(companyCompanyName));
    }



    private void checkCreateUserByContractId(String contractId){
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        if (contractInfo == null) {
            throw new CrmException("该合同信息不存在");
        }
        checkCreateUserAndCurrentPersonId(contractInfo.getCreateUser());
    }

    private void checkCreateUserByContractIds(Set<String> contractIds){
        if (contractIds.size() > 1) {
            throw new CrmException("参数有误，请检查参数");
        }
        String contractId = contractIds.stream().findFirst().orElseThrow(() -> new CrmException("参数有误，请检查参数"));
        checkCreateUserByContractId(contractId);
    }

    private void checkCreateUserAndCurrentPersonId(String createUser){
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        if (!currentPersonId.equals(createUser)) {
            // 不一样，直接返回无权限
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    private void checkProjectAuth(String projectId){
        // 加一个项目的权限，根据当前登录人看看有没有项目的权限，发起之前的话 要根据当前人判断
        if (!remoteProjectDirectlyClient.hasRight(projectId, UserInfoHolder.getCurrentPersonId()).getObjEntity()) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        };
    }

    private void checkAllSaveLaunchDTO(ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO){
        // todo 校验子表的数据，如果是updateById 得先根据id把主表id查出来，然后和主表id对比 不一致不让改
    }


}
