package com.topsec.crm.flow.core.validator.salesAgreement.condition;

import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementProductVo;
import com.topsec.crm.flow.core.validator.CheckCondition;
import com.topsec.crm.flow.core.validator.pricereview.ProductCompareUtil;
import com.topsec.crm.flow.core.validator.salesAgreement.SalesAgreementProductCheckContext;

import java.util.List;
import java.util.Map;

/**
 * 自有产品保修期变化，则失效
 */
public class SalesAgreementOwnPeriodChangeCondition implements CheckCondition<SalesAgreementProductCheckContext> {



    @Override
    public boolean check(SalesAgreementProductCheckContext context) {

        Map<String, List<SalesAgreementProductVo>> currentMap = ProductCompareUtil.convert(context.getSalesAgreementProductVoList());
        Map<String, List<SalesAgreementProductVo>> snapshotMap = ProductCompareUtil.convert(context.getSalesAgreementProductSnapshotVoList());
        return ProductCompareUtil.validate(currentMap, snapshotMap, "productPeriod");
    }

    @Override
    public String defaultFailureReason() {
        return "自有产品保修期变化";
    }


}
