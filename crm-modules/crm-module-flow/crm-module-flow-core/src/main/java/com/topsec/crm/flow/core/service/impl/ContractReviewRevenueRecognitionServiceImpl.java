package com.topsec.crm.flow.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.contract.api.RemoteContractProductOwnService;
import com.topsec.crm.contract.api.RemoteContractProductThirdService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.entity.CrmContractProductOwnVO;
import com.topsec.crm.contract.api.entity.CrmContractProductThirdVO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.RevenueRecognitionDTO;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.flow.core.mapper.ContractReviewRevenueRecognitionMapper;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ContractReviewRevenueRecognitionServiceImpl extends ServiceImpl<ContractReviewRevenueRecognitionMapper, ContractReviewRevenueRecognition> implements ContractReviewRevenueRecognitionService{

    private final ContractReviewProductOwnService ownService;

    private final ContractReviewProductThirdService thirdService;

    private final RemoteContractProductOwnService contractProductOwnService;

    private final RemoteContractProductThirdService contractProductThirdService;

    private final ReturnExchangeProductService returnExchangeProductService;

    private final ReturnExchangeProductThirdService returnExchangeProductThirdService;

    @Override
    public TableDataInfo pageByContractId(String contractId) {
        List<ContractReviewRevenueRecognition> list = list(new LambdaQueryWrapper<ContractReviewRevenueRecognition>().eq(ContractReviewRevenueRecognition::getContractReviewMainId, contractId)
                .eq(ContractReviewRevenueRecognition::getDelFlag, 0)
        );
        TableDataInfo tableDataInfo = new TableDataInfo();
        if (CollectionUtils.isEmpty(list)) {
            tableDataInfo.setTotalCount(0);
            return tableDataInfo;
        }
        List<RevenueRecognitionDTO> result = HyperBeanUtils.copyListProperties(list, RevenueRecognitionDTO::new);
        Set<String> ownIds = new HashSet<>();
        Set<String> thirdIds = new HashSet<>();
        result.forEach(revenueRecognitionDTO -> {
            ownIds.add(revenueRecognitionDTO.getProductOwnId());
            thirdIds.add(revenueRecognitionDTO.getProductThirdId());
        });
        Map<String, ContractProductOwnDTO> byOwnIdBatch = ownService.getByOwnIdBatch(new ArrayList<>(ownIds))
                .stream().collect(Collectors.toMap(ContractProductOwnDTO::getId, item -> item, (o1, o2) -> o1));
        Map<String, ContractProductThirdDTO> byThirdIdBatch = thirdService.getByThirdIdBatch(new ArrayList<>(thirdIds))
                .stream().collect(Collectors.toMap(ContractProductThirdDTO::getId, item -> item, (o1, o2) -> o1));
        result.forEach(revenueRecognitionDTO -> {
            // 取产品表的信息
            if (!StringUtils.isEmpty(revenueRecognitionDTO.getProductOwnId())) {
                ContractProductOwnDTO ownDTO = byOwnIdBatch.get(revenueRecognitionDTO.getProductOwnId());
                if (ownDTO != null) {
                    revenueRecognitionDTO.setStuffCode(ownDTO.getStuffCode());
                    revenueRecognitionDTO.setPnCode(ownDTO.getPnCode());
                    revenueRecognitionDTO.setProductNum(ownDTO.getProductNum());
                    revenueRecognitionDTO.setDealPrice(ownDTO.getDealTotalPrice());
                    revenueRecognitionDTO.setTaxRate(ownDTO.getTaxRate());
                    revenueRecognitionDTO.setProductName(ownDTO.getProductName());
                    revenueRecognitionDTO.setProductCategory(ownDTO.getProductCategory());
                }
            }

            if (!StringUtils.isEmpty(revenueRecognitionDTO.getProductThirdId())) {
                ContractProductThirdDTO thirdDTO = byThirdIdBatch.get(revenueRecognitionDTO.getProductThirdId());
                if (thirdDTO != null) {
                    revenueRecognitionDTO.setStuffCode(thirdDTO.getStuffCode());
                    revenueRecognitionDTO.setProductNum(thirdDTO.getProductNum());
                    revenueRecognitionDTO.setDealPrice(thirdDTO.getDealTotalPrice());
                    revenueRecognitionDTO.setTaxRate(thirdDTO.getTaxRate());
                    revenueRecognitionDTO.setProductName(thirdDTO.getProductName());
                    revenueRecognitionDTO.setProductCategory(thirdDTO.getProductModel());
                }
            }
        });

        tableDataInfo.setList(result);
        tableDataInfo.setTotalCount(new PageInfo<>(list).getTotal());
        return tableDataInfo;
    }

    @Override
    public List<RevenueRecognitionDTO> getByContractId(String contractId) {
        if (StringUtils.isEmpty(contractId)) {
            return new ArrayList<>();
        }
        List<ContractReviewRevenueRecognition> list = list(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                .eq(ContractReviewRevenueRecognition::getContractReviewMainId, contractId)
                .eq(ContractReviewRevenueRecognition::getDelFlag, 0)
        );
        return HyperBeanUtils.copyListProperties(list, RevenueRecognitionDTO::new);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatch(List<RevenueRecognitionDTO> revenueRecognitionDTO) {
        if (CollectionUtils.isEmpty(revenueRecognitionDTO)) {
            return false;
        }
        Map<String, List<RevenueRecognitionDTO>> revenueRecognitionByContractId = revenueRecognitionDTO.stream()
                .filter(item -> item.getContractReviewMainId() != null)
                .collect(Collectors.groupingBy(RevenueRecognitionDTO::getContractReviewMainId));
        revenueRecognitionByContractId.values().forEach(revenueRecognitionDTOS -> {
            String contractReviewMainId = revenueRecognitionDTOS.get(0).getContractReviewMainId();
            Map<Triple<String, String, String>, List<RevenueRecognitionDTO>> revenueRecognitionMap = revenueRecognitionDTOS.stream().collect(Collectors.groupingBy(contractReviewRevenueRecognition ->
                    Triple.of(contractReviewRevenueRecognition.getProductOwnId(), contractReviewRevenueRecognition.getProductThirdId(), contractReviewRevenueRecognition.getStuffCode())
            ));
            // 判断集合是否只有一个默认确认 为 是 且必须有一个
            revenueRecognitionMap.forEach((k, v) -> {
                List<RevenueRecognitionDTO> defaultConfirmList = v.stream().filter(contractReviewRevenueRecognition -> contractReviewRevenueRecognition.getDefaultConfirm() == 1).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(defaultConfirmList) || defaultConfirmList.size() > 1) {
                    throw new CrmException("产品" + k.getRight() + "默认确认为“是”的收入确认条款，必须有且仅有一个");
                }

                // 收入确认条款同样的收入确认方式只能选择一次
                Map<Integer, List<RevenueRecognitionDTO>> dtoByConfirmType = v.stream().collect(Collectors.groupingBy(RevenueRecognitionDTO::getConfirmType));
                // 遍历 map 如果value 的size大于1 则提示
                dtoByConfirmType.forEach((k1, v1) -> {
                    if (v1.size() > 1) {
                        throw new CrmException("产品" + k.getRight() + "收入确认方式" + convertConfirmType(k1) + "的付款条款，只能选择一次");
                    }
                });
            });


            List<ContractReviewRevenueRecognition> revenueRecognitions = HyperBeanUtils.copyListProperties(revenueRecognitionDTOS, ContractReviewRevenueRecognition::new);

            // 数据库不存在的记录删除
             List<ContractReviewRevenueRecognition> dbRows = list(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                    .eq(ContractReviewRevenueRecognition::getContractReviewMainId, contractReviewMainId)
                    .eq(ContractReviewRevenueRecognition::getDelFlag, 0));
            List<ContractReviewRevenueRecognition> deleteRows = dbRows.stream().filter(dbRow -> !revenueRecognitionDTOS.stream().map(RevenueRecognitionDTO::getId).filter(id -> !StringUtils.isEmpty(id)).collect(Collectors.toList()).contains(dbRow.getId())).collect(Collectors.toList());
            deleteRows.forEach(row -> update(new LambdaUpdateWrapper<ContractReviewRevenueRecognition>().set(ContractReviewRevenueRecognition::getDelFlag, 1)
                    .eq(ContractReviewRevenueRecognition::getId, row.getId())));

            // saveOrUpdate
            saveOrUpdateBatch(revenueRecognitions);
            // 非阶段性验收需要清理stage，非直线摊销需要清理startDate和endDate
            ListUtils.emptyIfNull(revenueRecognitions).forEach(this::clearStageAndDate);
        });
        return true;
    }

    private void clearStageAndDate(ContractReviewRevenueRecognition revenueRecognition) {
        Integer confirmType = revenueRecognition.getConfirmType();
        if (confirmType != 2 && StringUtils.isNotEmpty(revenueRecognition.getId())) {
            update(new LambdaUpdateWrapper<ContractReviewRevenueRecognition>()
                    .eq(ContractReviewRevenueRecognition::getId, revenueRecognition.getId())
                    .set(ContractReviewRevenueRecognition::getStage, null));
        }
        if (confirmType != 3 && StringUtils.isNotEmpty(revenueRecognition.getId())) {
            update(new LambdaUpdateWrapper<ContractReviewRevenueRecognition>()
                    .eq(ContractReviewRevenueRecognition::getId, revenueRecognition.getId())
                    .set(ContractReviewRevenueRecognition::getStartDate, null)
                    .set(ContractReviewRevenueRecognition::getEndDate, null));
        }
    }

    @Override
    public boolean productValidStraightLine(String contractId, List<ContractProductOwnDTO> owns, List<ContractProductThirdDTO> thirds) {
        // 根据contractId 查所有的直线摊销条款的
        List<ContractReviewRevenueRecognition> list = list(new LambdaQueryWrapper<ContractReviewRevenueRecognition>().eq(ContractReviewRevenueRecognition::getContractReviewMainId, contractId)
                .eq(ContractReviewRevenueRecognition::getConfirmType, 3)
                .eq(ContractReviewRevenueRecognition::getDelFlag, 0)
        );
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        // product date > end date  就要提示
        if (list.stream().anyMatch(row -> row.getProductOwnId() != null && row.getProductThirdId() != null)) {
            throw new CrmException("合同" + contractId + "存在产品同时为配件和自有产品的直线摊销条款");
        }
        Map<String, ContractProductOwnDTO> productOwnDTOMap = ListUtils.emptyIfNull(owns).stream().collect(Collectors.toMap(ContractProductOwnDTO::getId, row -> row, (v1, v2) -> v1));
        Map<String, ContractProductThirdDTO> productThirdDTOMap = ListUtils.emptyIfNull(thirds).stream().collect(Collectors.toMap(ContractProductThirdDTO::getId, row -> row, (v1, v2) -> v1));

        list.forEach(row -> {
            if (row.getProductOwnId() != null) {
                ContractProductOwnDTO ownDTO = productOwnDTOMap.getOrDefault(row.getProductOwnId(), new ContractProductOwnDTO());
                JsonObject<CrmContractProductOwnVO> ownVO = contractProductOwnService.getByOwnId(ownDTO.getId());
                if (!ownVO.isSuccess()) {
                    throw new CrmException("合同服务出错");
                }
                if (ownVO.getObjEntity() != null && ownVO.getObjEntity().getValidDate() != null
                        && ownVO.getObjEntity().getValidDate().isAfter(row.getEndDate().atTime(LocalTime.of(0, 0)))) {
                    throw new CrmException("产品" + ownDTO.getStuffCode() + "的有效时间不能晚于直线摊销的结束时间");
                }
            }
            if (row.getProductThirdId() != null) {
                ContractProductThirdDTO thirdDTO = productThirdDTOMap.getOrDefault(row.getProductThirdId(), new ContractProductThirdDTO());
                JsonObject<CrmContractProductThirdVO> thirdVO = contractProductThirdService.getByThirdId(thirdDTO.getId());
                if (!thirdVO.isSuccess()) {
                    throw new CrmException("合同服务出错");
                }
                if (thirdVO.getObjEntity() != null && thirdVO.getObjEntity().getValidDate() != null
                        && thirdVO.getObjEntity().getValidDate().isAfter(row.getEndDate().atTime(LocalTime.of(0, 0)))) {
                    throw new CrmException("产品" + thirdDTO.getStuffCode() + "的有效时间不能晚于直线摊销的结束时间");
                }
            }
        });
        return true;
    }

    @Override
    public boolean checkRevenueRecognition(String contractId) {
        List<ContractProductOwnDTO> owns = ownService.productOwnInfoTileByContractId(contractId, false);
        List<ContractProductThirdDTO> thirds = thirdService.productThirdInfoByContractId(contractId, false);
        List<ContractReviewRevenueRecognition> revenueRecognitions = list(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                .eq(ContractReviewRevenueRecognition::getContractReviewMainId, contractId)
                .eq(ContractReviewRevenueRecognition::getDelFlag, 0));
        // 第三方产品的
        Map<String, ContractReviewRevenueRecognition> revenueRecognitionThirdMap = ListUtils.emptyIfNull(revenueRecognitions).stream().filter(row -> row.getProductThirdId() != null
                        && row.getDefaultConfirm() == 1)
                .collect(Collectors.toMap(ContractReviewRevenueRecognition::getProductThirdId, v -> v));
        // 自有产品的
        Map<String, ContractReviewRevenueRecognition> revenueRecognitionOwnMap = ListUtils.emptyIfNull(revenueRecognitions).stream().filter(row -> row.getProductOwnId() != null
                        && row.getDefaultConfirm() == 1)
                .collect(Collectors.toMap(ContractReviewRevenueRecognition::getProductOwnId, v -> v));

        // 判断是否每个产品都有一条默认确认的条款
        for (ContractProductOwnDTO own : ListUtils.emptyIfNull(owns)) {
            ContractReviewRevenueRecognition contractReviewRevenueRecognition = revenueRecognitionOwnMap.get(own.getId());
            if (contractReviewRevenueRecognition == null) {
                return false;
            }
        }
        for (ContractProductThirdDTO third : ListUtils.emptyIfNull(thirds)) {
            ContractReviewRevenueRecognition contractReviewRevenueRecognition = revenueRecognitionThirdMap.get(third.getId());
            if (contractReviewRevenueRecognition == null) {
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdate(RevenueRecognitionDTO revenueRecognitionDTO) {
        ContractReviewRevenueRecognition contractReviewRevenueRecognition = HyperBeanUtils.copyProperties(revenueRecognitionDTO, ContractReviewRevenueRecognition::new);
        // 收入确认条款中同一行产品有且必须只有一个“默认确认”为“是”的条款，可以有多个“否” 判断这个产品是否已经存在默认确认为是的条款
        long count = count(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                .eq(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getProductOwnId()), ContractReviewRevenueRecognition::getProductOwnId, contractReviewRevenueRecognition.getProductOwnId())
                .eq(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getProductThirdId()), ContractReviewRevenueRecognition::getProductThirdId, contractReviewRevenueRecognition.getProductThirdId())
                .eq(ContractReviewRevenueRecognition::getContractReviewMainId, contractReviewRevenueRecognition.getContractReviewMainId())
                .ne(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getId()), ContractReviewRevenueRecognition::getId, contractReviewRevenueRecognition.getId())
                .eq(ContractReviewRevenueRecognition::getDefaultConfirm, 1)
                .eq(ContractReviewRevenueRecognition::getDelFlag, 0));
        if (count != 0 && revenueRecognitionDTO.getDefaultConfirm() == 1) {
            throw new CrmException("同一行产品有且必须只有一个“默认确认”为“是”的条款");
        }
        // 判断这个产品之前是否有直线摊销 有就不能添加条款了
        ContractReviewRevenueRecognition record = getOne(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                .eq(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getProductOwnId()), ContractReviewRevenueRecognition::getProductOwnId, contractReviewRevenueRecognition.getProductOwnId())
                .eq(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getProductThirdId()), ContractReviewRevenueRecognition::getProductThirdId, contractReviewRevenueRecognition.getProductThirdId())
                .ne(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getId()), ContractReviewRevenueRecognition::getId, contractReviewRevenueRecognition.getId())
                .eq(ContractReviewRevenueRecognition::getContractReviewMainId, contractReviewRevenueRecognition.getContractReviewMainId())
                .eq(ContractReviewRevenueRecognition::getConfirmType, 3)
                .eq(ContractReviewRevenueRecognition::getDelFlag, 0));
        if (record != null) {
            throw new CrmException("同一行产品已经添加直线摊销条款， 不能添加其他条款");
        }

        // 查出数据库中是否存在相同收入确认方式的记录
        ContractReviewRevenueRecognition oldConfirmRow = getOne(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                .eq(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getProductOwnId()), ContractReviewRevenueRecognition::getProductOwnId, contractReviewRevenueRecognition.getProductOwnId())
                .eq(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getProductThirdId()), ContractReviewRevenueRecognition::getProductThirdId, contractReviewRevenueRecognition.getProductThirdId())
                .eq(ContractReviewRevenueRecognition::getContractReviewMainId, contractReviewRevenueRecognition.getContractReviewMainId())
                .eq(ContractReviewRevenueRecognition::getConfirmType, contractReviewRevenueRecognition.getConfirmType())
                .ne(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getId()), ContractReviewRevenueRecognition::getId, contractReviewRevenueRecognition.getId())
                .eq(ContractReviewRevenueRecognition::getDelFlag, 0));
        if (oldConfirmRow != null) {
            // 如果是修改 直接抛异常 表示只能存在一条相同的收入确认方式
            if (StringUtils.isNotEmpty(contractReviewRevenueRecognition.getId())) {
                throw new CrmException("同一行产品只能存在一条相同的收入确认方式");
            }
            // 如果是新增 直接设置id 为 旧记录的id 执行update
            contractReviewRevenueRecognition.setId(oldConfirmRow.getId());
        }
        saveOrUpdate(contractReviewRevenueRecognition);
        // 非阶段性验收需要清理stage，非直线摊销需要清理startDate和endDate
        clearStageAndDate(contractReviewRevenueRecognition);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateForReturnExchange(RevenueRecognitionDTO revenueRecognitionDTO) {
        ContractReviewRevenueRecognition contractReviewRevenueRecognition = HyperBeanUtils.copyProperties(revenueRecognitionDTO, ContractReviewRevenueRecognition::new);
        // 收入确认条款中同一行产品有且必须只有一个“默认确认”为“是”的条款，可以有多个“否” 判断这个产品是否已经存在默认确认为是的条款
        long count = count(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                .eq(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getProductOwnId()), ContractReviewRevenueRecognition::getProductOwnId, contractReviewRevenueRecognition.getProductOwnId())
                .eq(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getProductThirdId()), ContractReviewRevenueRecognition::getProductThirdId, contractReviewRevenueRecognition.getProductThirdId())
                .eq(ContractReviewRevenueRecognition::getReturnExchangeProcessInstanceId, contractReviewRevenueRecognition.getReturnExchangeProcessInstanceId())
                .ne(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getId()), ContractReviewRevenueRecognition::getId, contractReviewRevenueRecognition.getId())
                .eq(ContractReviewRevenueRecognition::getDefaultConfirm, 1)
                .eq(ContractReviewRevenueRecognition::getDelFlag, 0));
        if (count != 0 && revenueRecognitionDTO.getDefaultConfirm() == 1) {
            throw new CrmException("同一行产品有且必须只有一个“默认确认”为“是”的条款");
        }
        // 判断这个产品之前是否有直线摊销 有就不能添加条款了
        ContractReviewRevenueRecognition record = getOne(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                .eq(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getProductOwnId()), ContractReviewRevenueRecognition::getProductOwnId, contractReviewRevenueRecognition.getProductOwnId())
                .eq(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getProductThirdId()), ContractReviewRevenueRecognition::getProductThirdId, contractReviewRevenueRecognition.getProductThirdId())
                .ne(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getId()), ContractReviewRevenueRecognition::getId, contractReviewRevenueRecognition.getId())
                .eq(ContractReviewRevenueRecognition::getReturnExchangeProcessInstanceId, contractReviewRevenueRecognition.getReturnExchangeProcessInstanceId())
                .eq(ContractReviewRevenueRecognition::getConfirmType, 3)
                .eq(ContractReviewRevenueRecognition::getDelFlag, 0));
        if (record != null) {
            throw new CrmException("同一行产品已经添加直线摊销条款， 不能添加其他条款");
        }

        // 查出数据库中是否存在相同收入确认方式的记录
        ContractReviewRevenueRecognition oldConfirmRow = getOne(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                .eq(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getProductOwnId()), ContractReviewRevenueRecognition::getProductOwnId, contractReviewRevenueRecognition.getProductOwnId())
                .eq(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getProductThirdId()), ContractReviewRevenueRecognition::getProductThirdId, contractReviewRevenueRecognition.getProductThirdId())
                .eq(ContractReviewRevenueRecognition::getReturnExchangeProcessInstanceId, contractReviewRevenueRecognition.getReturnExchangeProcessInstanceId())
                .eq(ContractReviewRevenueRecognition::getConfirmType, contractReviewRevenueRecognition.getConfirmType())
                .ne(StringUtils.isNotEmpty(contractReviewRevenueRecognition.getId()), ContractReviewRevenueRecognition::getId, contractReviewRevenueRecognition.getId())
                .eq(ContractReviewRevenueRecognition::getDelFlag, 0));
        if (oldConfirmRow != null) {
            // 如果是修改 直接抛异常 表示只能存在一条相同的收入确认方式
            if (StringUtils.isNotEmpty(contractReviewRevenueRecognition.getId())) {
                throw new CrmException("同一行产品只能存在一条相同的收入确认方式");
            }
            // 如果是新增 直接设置id 为 旧记录的id 执行update
            contractReviewRevenueRecognition.setId(oldConfirmRow.getId());
        }
        saveOrUpdate(contractReviewRevenueRecognition);
        // 非阶段性验收需要清理stage，非直线摊销需要清理startDate和endDate
        clearStageAndDate(contractReviewRevenueRecognition);
        return true;
    }

    @Override
    public boolean deleteById(String id) {
        ContractReviewRevenueRecognition contractReviewRevenueRecognition = getById(id);
        if (contractReviewRevenueRecognition == null) {
            // 未查询到记录，交由统一异常处理
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
        // 逻辑删除
        contractReviewRevenueRecognition.setDelFlag(1);

        return updateById(contractReviewRevenueRecognition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateAll(List<RevenueRecognitionDTO> revenueRecognitionDTOS) {
        ListUtils.emptyIfNull(revenueRecognitionDTOS).forEach(this::saveOrUpdate);
        return true;
    }

    @Override
    public List<RevenueRecognitionDTO> getByContractProductIds(List<String> contractProductIds, List<String> thirdIds, Integer type) {
        List<ContractReviewRevenueRecognition> list = list(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                .eq(ContractReviewRevenueRecognition::getDelFlag, 0)
                .and(org.apache.commons.collections4.CollectionUtils.isNotEmpty(contractProductIds)
                        || org.apache.commons.collections4.CollectionUtils.isNotEmpty(thirdIds),
                wrapper -> {
                    if (type == 1) {
                        wrapper.in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(contractProductIds), ContractReviewRevenueRecognition::getProductOwnId, contractProductIds)
                                .or()
                                .in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(thirdIds), ContractReviewRevenueRecognition::getProductThirdId, thirdIds);
                    }
                    if (type == 2) {
                        wrapper.in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(contractProductIds), ContractReviewRevenueRecognition::getReturnExchangeOwnId, contractProductIds)
                                .or()
                                .in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(thirdIds), ContractReviewRevenueRecognition::getReturnExchangeThirdId, thirdIds);
                    }
                }));
        return HyperBeanUtils.copyListProperties(list, RevenueRecognitionDTO::new);
    }

    @Override
    public List<ContractReviewRevenueRecognition> getByReturnExchangeProcessInstanceId(String returnExchangeProcessInstanceId) {
        return list(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                .eq(ContractReviewRevenueRecognition::getReturnExchangeProcessInstanceId, returnExchangeProcessInstanceId)
                .eq(ContractReviewRevenueRecognition::getDelFlag, 0));
    }

    public List<RevenueRecognitionDTO> initProductInfoByReturnExchange(List<ContractReviewRevenueRecognition> contractReviewRevenueRecognitions){
        if (CollectionUtils.isEmpty(contractReviewRevenueRecognitions)){
            return new ArrayList<>();
        }
        Set<String> ownIds = new HashSet<>();
        Set<String> thirdIds = new HashSet<>();
        contractReviewRevenueRecognitions.forEach(revenueRecognitionDTO -> {
            ownIds.add(revenueRecognitionDTO.getReturnExchangeOwnId());
            thirdIds.add(revenueRecognitionDTO.getReturnExchangeThirdId());
        });

        List<ReturnExchangeProduct> returnExchangeProducts;
        if (CollectionUtils.isEmpty(ownIds)) {
            returnExchangeProducts = Collections.emptyList();
        } else {
            returnExchangeProducts = returnExchangeProductService.list(new LambdaQueryWrapper<ReturnExchangeProduct>()
                    .in(ReturnExchangeProduct::getId, ownIds)
                    .eq(ReturnExchangeProduct::getDelFlag, false));
        }
        List<ReturnExchangeProductThird> returnExchangeProductThirds;
        if (CollectionUtils.isEmpty(thirdIds)) {
            returnExchangeProductThirds = Collections.emptyList();
        } else {
            returnExchangeProductThirds = returnExchangeProductThirdService.list(new LambdaQueryWrapper<ReturnExchangeProductThird>()
                    .in(ReturnExchangeProductThird::getId, thirdIds)
                    .eq(ReturnExchangeProductThird::getDelFlag, false));
        }
        Map<String, ReturnExchangeProduct> byOwnIdBatch = returnExchangeProducts.stream().collect(Collectors.toMap(ReturnExchangeProduct::getId, item -> item, (o1, o2) -> o1));
        Map<String, ReturnExchangeProductThird> byThirdIdBatch = returnExchangeProductThirds.stream().collect(Collectors.toMap(ReturnExchangeProductThird::getId, item -> item, (o1, o2) -> o1));


        return contractReviewRevenueRecognitions.stream().map(item -> {
            RevenueRecognitionDTO revenueRecognitionDTO = HyperBeanUtils.copyProperties(item, RevenueRecognitionDTO::new);
            // 取产品表的信息
            if (!StringUtils.isEmpty(revenueRecognitionDTO.getReturnExchangeOwnId())) {
                ReturnExchangeProduct ownDTO = byOwnIdBatch.get(revenueRecognitionDTO.getReturnExchangeOwnId());
                if (ownDTO != null) {
                    revenueRecognitionDTO.setStuffCode(ownDTO.getStuffCode());
                    revenueRecognitionDTO.setPnCode(ownDTO.getPnCode());
                    revenueRecognitionDTO.setProductNum(ownDTO.getProductNum() == null ? 0 : Long.valueOf(ownDTO.getProductNum()));
                    revenueRecognitionDTO.setDealPrice(ownDTO.getDealTotalPrice());
                    revenueRecognitionDTO.setTaxRate(ownDTO.getTaxRate());
                    revenueRecognitionDTO.setProductName(ownDTO.getProductName());
                    revenueRecognitionDTO.setProductCategory(ownDTO.getProductCategory());
                }
            }

            if (!StringUtils.isEmpty(revenueRecognitionDTO.getReturnExchangeThirdId())) {
                ReturnExchangeProductThird thirdDTO = byThirdIdBatch.get(revenueRecognitionDTO.getReturnExchangeThirdId());
                if (thirdDTO != null) {
                    revenueRecognitionDTO.setStuffCode(thirdDTO.getStuffCode());
                    revenueRecognitionDTO.setProductNum(thirdDTO.getProductNum() == null ? 0 : Long.valueOf(thirdDTO.getProductNum()));
                    revenueRecognitionDTO.setDealPrice(thirdDTO.getDealTotalPrice());
                    revenueRecognitionDTO.setTaxRate(thirdDTO.getTaxRate());
                    revenueRecognitionDTO.setProductName(thirdDTO.getProductName());
                    revenueRecognitionDTO.setProductCategory(thirdDTO.getProductModel());
                }
            }
            return revenueRecognitionDTO;
        }).toList();
    }


    /**
     * 转换 确认方式
     * @param confirmType 确认方式
     * @return 确认方式名称
     */
    private String convertConfirmType(Integer confirmType) {
        switch (confirmType) {
            case 0:
                return "签收单";
            case 1:
                return "验收单";
            case 2:
                return "阶段性验收";
            case 3:
                return "直线摊销";
            case 4:
                return "开票确认";
            default:
                return "";
        }
    }
}




