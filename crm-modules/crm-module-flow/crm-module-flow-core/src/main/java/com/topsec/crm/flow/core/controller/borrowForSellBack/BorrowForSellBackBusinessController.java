package com.topsec.crm.flow.core.controller.borrowForSellBack;


import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellDeviceDTO;
import com.topsec.crm.flow.api.dto.borrowForSellBack.BorrowForSellBackFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.borrowForSellBack.BorrowForSellBackPageQuery;
import com.topsec.crm.flow.core.entity.BorrowForSell;
import com.topsec.crm.flow.core.entity.BorrowForSellBack;
import com.topsec.crm.flow.core.service.IBorrowForSellBackService;
import com.topsec.crm.flow.core.service.IBorrowForSellProductSnService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 借转销归还/丢失赔偿信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/business/borrowForSellBack")
@Tag(name = "借转销归还/丢失赔偿流程", description = "/business/borrowForSellBack")
public class BorrowForSellBackBusinessController  extends BaseController {

    @Autowired
    private IBorrowForSellBackService borrowForSellBackService;

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @Autowired
    private IBorrowForSellProductSnService borrowForSellProductSnService;

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_return_compensate", dataScope = "crm_flow_borrow_for_sell_return_compensate")
    @PostMapping("/borrowForSellBackPage")
    @Operation(summary = "分页查询借转销归还/丢失赔偿信息")
    public JsonObject<PageUtils<BorrowForSellBackFlowLaunchDTO>> borrowForSellBackPage(@RequestBody BorrowForSellBackPageQuery borrowForSellBackPageQuery) {
        PageUtils<BorrowForSellBack> borrowForSellBackPageUtils = borrowForSellBackService.borrowForSellBackPage(borrowForSellBackPageQuery);
        if(CollectionUtils.isNotEmpty(borrowForSellBackPageUtils.getList())){
            List<BorrowForSellBackFlowLaunchDTO> borrowForSellBackFlowLaunchDTOS = HyperBeanUtils.copyListPropertiesByJackson(borrowForSellBackPageUtils.getList(), BorrowForSellBackFlowLaunchDTO.class);
            return new JsonObject<>(new PageUtils<>(borrowForSellBackFlowLaunchDTOS,borrowForSellBackPageUtils.getTotalCount(),borrowForSellBackPageUtils.getPageSize(),borrowForSellBackPageUtils.getPageNum()));
        }else{
            return new JsonObject<>(new PageUtils<>(new ArrayList<>(),borrowForSellBackPageUtils.getTotalCount(),borrowForSellBackPageUtils.getPageSize(),borrowForSellBackPageUtils.getPageNum()));
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_return_compensate", dataScope = "crm_flow_borrow_for_sell_return_compensate")
    @GetMapping("/detail/{processInstanceId}")
    @Operation(summary = "查看借转销归还/丢失赔偿流程详情")
    public JsonObject<BorrowForSellBackFlowLaunchDTO> borrowForSellBackDetail(@PathVariable String processInstanceId) {
        BorrowForSellBackFlowLaunchDTO borrowForSellBackFlowLaunchDTO = borrowForSellBackService.borrowForSellBackDetail(processInstanceId);
        if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList()) || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(borrowForSellBackFlowLaunchDTO.getSalesmanPersonId())){
            return new JsonObject<BorrowForSellBackFlowLaunchDTO>(borrowForSellBackService.borrowForSellBackDetail(processInstanceId));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_return_compensate", dataScope = "crm_flow_borrow_for_sell_return_compensate")
    @PostMapping("/queryBorrowForSellDeviceList")
    @Operation(summary = "查询借转销产品以及序列号信息")
    public JsonObject<List<BorrowForSellDeviceDTO>> queryBorrowForSellDeviceList(@RequestParam String borrowId,@RequestParam Integer shipmentType) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId && byId.getSalesmanPersonId().equals(getCurrentPersonId())){
            List<BorrowForSellDeviceDTO> result =  borrowForSellProductSnService.queryBorrowForSellDeviceList(borrowId,shipmentType);
            return new JsonObject<List<BorrowForSellDeviceDTO>>(result);
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

}

