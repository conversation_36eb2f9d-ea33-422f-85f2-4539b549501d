package com.topsec.crm.flow.core.controller.commom.forward;

import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.operation.api.RemoteContractReviewConfigService;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/forward/customer")
@Tag(name = "转发渠道项目Controller", description = "/forward")
@RequiredArgsConstructor
@Validated
public class ForwardCustomerController extends BaseController {

    private final RemoteCustomerService remoteCustomerService;
    private final RemoteContractReviewConfigService remoteContractReviewConfigService;

    @PreFlowPermission
    @GetMapping("/{customerId}")
    @Operation(summary = "获取项目详情")
    JsonObject<CrmCustomerVo>  getCustomerInfo(@PathVariable("customerId") String customerId) {
        return remoteCustomerService.getCustomerInfo(customerId, UserInfoHolder.getCurrentPersonId(),false);
    }

    @PreFlowPermission
    @GetMapping("/flowSelectCustomerPage")
    @Operation(summary = "获取客户列表")
    JsonObject<PageUtils<CrmCustomerVo>>  flowSelectCustomerPage(@RequestParam("name") String name) {
        CrmCustomerVo crmCustomerVo = new CrmCustomerVo();
        crmCustomerVo.setName(name);
        return remoteCustomerService.pageForProject(crmCustomerVo);
    }

}
