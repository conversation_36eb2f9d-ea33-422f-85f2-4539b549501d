package com.topsec.crm.flow.core.controller.agentAgreementOriginalHandling;

import com.topsec.crm.flow.api.dto.agentAgreementOriginalHandling.AgentAgreementOriginalHandlingDetail;
import com.topsec.crm.flow.api.dto.agentAgreementOriginalHandling.AgentAgreementOriginalHandlingFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.agentAgreementOriginalHandling.AgentAgreementOriginalHandlingQuery;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.service.AgentAgreementOriginalHandlingService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/agentAgreementOriginalHandling")
@Tag(name = "渠道协议原件上交流程", description = "/agentAgreementOriginalHandling")
@RequiredArgsConstructor
@Validated
public class AgentAgreementOriginalHandlingController extends BaseController {

    private final AgentAgreementOriginalHandlingService agentAgreementOriginalHandlingService;

    @PostMapping("/getAgentAgreementOriginalHandlingList")
    @Operation(summary = "渠道协议原件上交列表")
    //@PreAuthorize(hasPermission = "crm_agent_authentication",dataScope="crm_agent_authentication")
    public JsonObject<PageUtils<AgentAgreementOriginalHandlingFlowLaunchDTO>> getAgentAgreementOriginalHandlingList(@RequestBody(required = false) AgentAgreementOriginalHandlingQuery agentAgreementOriginalHandlingQuery){
        if (agentAgreementOriginalHandlingQuery == null) {
            agentAgreementOriginalHandlingQuery = new AgentAgreementOriginalHandlingQuery();
        }
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        startPage();
        List<AgentAgreementOriginalHandlingFlowLaunchDTO> agentAgreementOriginalHandlingList = agentAgreementOriginalHandlingService.selectAgentAgreementOriginalHandlingList(agentAgreementOriginalHandlingQuery, dataScopeParam);
        PageUtils dataTable = listToPage(agentAgreementOriginalHandlingList);
        return new JsonObject<>(dataTable);
    }

    @GetMapping("/getDetailByProcessInstanceId")
    @Operation(summary = "渠道协议原件上交详情")
    //@PreAuthorize(hasPermission = "crm_agent_authentication",dataScope="crm_agent_authentication")
    public JsonObject<AgentAgreementOriginalHandlingDetail> getDetailByProcessInstanceId(@RequestParam String processInstanceId){
        return new JsonObject<>(agentAgreementOriginalHandlingService.selectDetailByProcessInstanceId(processInstanceId));
    }

    @PostMapping(value="/exportAgentAgreementOriginalHandlingList")
    @Operation(summary = "导出渠道协议原件上交列表")
    @PreFlowPermission
    public void exportAgentAgreementOriginalHandlingList(@RequestBody(required = false) AgentAgreementOriginalHandlingQuery agentAgreementOriginalHandlingQuery) throws Exception {
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();

        List<AgentAgreementOriginalHandlingFlowLaunchDTO> agentAgreementOriginalHandlingList = agentAgreementOriginalHandlingService.selectAgentAgreementOriginalHandlingList(agentAgreementOriginalHandlingQuery, dataScopeParam);
        ExcelUtil<AgentAgreementOriginalHandlingFlowLaunchDTO> excelUtil = new ExcelUtil<>(AgentAgreementOriginalHandlingFlowLaunchDTO.class);
        excelUtil.exportExcel(response, agentAgreementOriginalHandlingList,"渠道协议原件上交");
    }
}
