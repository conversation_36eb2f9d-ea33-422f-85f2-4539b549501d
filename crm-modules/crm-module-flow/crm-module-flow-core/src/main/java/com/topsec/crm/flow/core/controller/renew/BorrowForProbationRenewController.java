package com.topsec.crm.flow.core.controller.renew;

import com.topsec.crm.flow.api.dto.renew.ProbationRenewServiceFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.renew.RenewVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.process.impl.BorrowForProbationRenewProcessService;
import com.topsec.crm.flow.core.service.BorrowForProbationRenewService;
import com.topsec.crm.framework.common.constant.BorrowForProbationConstants;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.entity.CrmBorrowForProbationDeviceVO;
import com.topsec.crm.project.api.entity.CrmBorrowForProbationSituationVO;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-07-31  14:28
 * @Description:
 */

@RestController
@RequestMapping("/renew")
@Tag(name = "借试用续借", description = "/renew")
@RequiredArgsConstructor
@Validated
public class BorrowForProbationRenewController extends BaseController {


    private final BorrowForProbationRenewService borrowForProbationRenewService;

    private final BorrowForProbationRenewProcessService borrowForProbationRenewProcessService;

    @Resource
    private TfsNodeClient tfsNodeClient;


    @PostMapping("/launch")
    @Operation(summary = "发起借试用续借流程")
    @PreAuthorize(hasPermission = "crm_device_probation_renew",dataScope = "crm_device_probation_renew")
    public JsonObject<Boolean> launch(@Valid @RequestBody ProbationRenewServiceFlowLaunchDTO launchDTO) {
        return new JsonObject<>(borrowForProbationRenewProcessService.launch(launchDTO));
    }

    @PostMapping("/launchAndReturnProcessInstanceId")
    @PreAuthorize(hasPermission = "crm_device_probation_renew",dataScope = "crm_device_probation_renew")
    @Operation(summary = "发起借试用续借流程并返回流程id")
    public JsonObject<String> launchAndReturnProcessInstanceId(@Valid @RequestBody ProbationRenewServiceFlowLaunchDTO launchDTO) {
        return new JsonObject<>(borrowForProbationRenewProcessService.launchAndReturnProcessInstanceId(launchDTO));
    }

    @GetMapping("/renewalDetails/{processInstanceId}")
    @PreFlowPermission
    @Operation(summary = "续借详情")
    public JsonObject<PageUtils<CrmBorrowForProbationDeviceVO>> renewalDetails(@PathVariable String processInstanceId) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId,headerValue);
        return new JsonObject<PageUtils<CrmBorrowForProbationDeviceVO>>(borrowForProbationRenewService.renewalDetails(processInstanceId));
    }


    @GetMapping("/renewalInfo/{processInstanceId}")
    @PreFlowPermission
    @Operation(summary = "续借信息")
    public JsonObject<RenewVO> renewalInfo(@PathVariable String processInstanceId) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId,headerValue);
        return new JsonObject<RenewVO>(borrowForProbationRenewService.renewalInfo(processInstanceId));
    }




    @PreAuthorize
    @PreFlowPermission
    @GetMapping("/flow/detail/situation")
    @Operation(summary = "查询借试用统计情况")
    public JsonObject<CrmBorrowForProbationSituationVO> borrowForProbationSituation(@RequestParam String processInstanceId) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId,headerValue);
        JsonObject<Set<String>> setJsonObject = tfsNodeClient.queryAssigneeAccountIdList(processInstanceId, BorrowForProbationConstants.BORROW_FOR_RENEW);
        if(setJsonObject.isSuccess() && setJsonObject.getObjEntity().contains(getCurrentAccountId())){
            return new JsonObject<CrmBorrowForProbationSituationVO>(borrowForProbationRenewService.borrowForProbationRenewSituation(processInstanceId));
        }else{
            CrmBorrowForProbationSituationVO build = CrmBorrowForProbationSituationVO.builder().hiden(true).build();
            return new JsonObject<CrmBorrowForProbationSituationVO>(build);
        }
    }

//    @GetMapping("/test")
//    @ApiOperation(value = "续借办结调用")
//    public Boolean handlePass(@RequestParam String processInstanceId) {
//        FlowStateInfoVo flowStateInfoVo = new FlowStateInfoVo();
//        flowStateInfoVo.setProcessInstanceId(processInstanceId);
//        borrowForProbationRenewProcessService.handlePass(flowStateInfoVo);
//        return true;
//    }

}
