package com.topsec.crm.flow.core.controller.contractreview;

import com.topsec.crm.contract.api.RemoteContractReviewTransferService;
import com.topsec.crm.contract.api.entity.contractReviewTrans.CrmContractReviewTransferInfoVO;
import com.topsec.crm.contract.api.entity.contractReviewTrans.CrmContractReviewTransferQuery;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/contractReviewTransfer")
@Tag(name = "合同评审转办设置", description = "/contractReviewTransfer")
@RequiredArgsConstructor
@Validated
public class ContractReviewTransferController  extends BaseController {

    private final RemoteContractReviewTransferService remoteContractReviewTransferService;

    @PreFlowPermission
    @GetMapping("/getByContractId")
    @Operation(summary = "根据合同评审ID获取03步审批人信息")
    public JsonObject<List<CrmContractReviewTransferInfoVO>> getByContractId(@RequestParam String contractId){
        CrmAssert.hasText(contractId,"合同评审ID不能为空");
        return remoteContractReviewTransferService.getByContractId(contractId);
    }

    @PreFlowPermission
    @PostMapping("/getInfoByApprovalNodeNameAagreementId")
    @Operation(summary = "获取审批节点配置信息")
    public JsonObject<List<CrmContractReviewTransferInfoVO>> getInfoByApprovalNodeNameAagreementId(@RequestBody CrmContractReviewTransferQuery query){
        return remoteContractReviewTransferService.getInfoByApprovalNodeNameAagreementId(query);
    }
}
