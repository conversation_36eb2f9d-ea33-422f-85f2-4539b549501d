package com.topsec.crm.flow.core.controller.salesAgreement;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductPriceGapVO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductQuery;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewTempCostVO;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementOwnProductVo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementPriceReviewMainLaunchVo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementPriceReviewMainVo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementProductVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.SalesAgreementPriceReviewMain;
import com.topsec.crm.flow.core.process.impl.SalesAgreementPriceProcessService;
import com.topsec.crm.flow.core.service.PriceReviewFlowService;
import com.topsec.crm.flow.core.service.PriceReviewProductOwnService;
import com.topsec.crm.flow.core.service.SalesAgreementPriceReviewMainService;
import com.topsec.crm.flow.core.service.SalesAgreementProductService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.ExecutionException;


/**
 * 协议价格评审流程表
 *
 * <AUTHOR>
 * @email
 * @date 2024-12-31 10:52:58
 */
@RestController
@RequestMapping("/agreementPrice")
@Tag(name = "【销售协议-协议价格审批】", description = "agreementPrice")
@RequiredArgsConstructor
@Validated
public class SalesAgreementPriceReviewMainController {
    private final SalesAgreementPriceReviewMainService salesAgreementPriceReviewMainService;
    private final SalesAgreementPriceProcessService salesAgreementPriceProcessService;
    private final SalesAgreementProductService salesAgreementProductService;
    private final PriceReviewFlowService priceReviewFlowService;
    private final PriceReviewProductOwnService priceReviewProductOwnService;


    @PostMapping("/launch")
    @Operation(summary = "发起协议价格审批")
    @PreFlowPermission(hasAnyNodes = {"salesAgreement_01","salesAgreement_01A","salesAgreement_03"})
    public JsonObject<Boolean> launch(@Valid @RequestBody SalesAgreementPriceReviewMainLaunchVo salesAgreementPriceReviewMainLaunchVo) {


        return new JsonObject<>(salesAgreementPriceProcessService.launch(salesAgreementPriceReviewMainLaunchVo));
    }



    @GetMapping("/querySalesAgreementPriceBaseInfo")
    @Operation(summary = "协议价格审批基本信息查询")
    @PreFlowPermission
    public JsonObject<SalesAgreementPriceReviewMainVo> querySalesAgreementPriceBaseInfo(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        SalesAgreementPriceReviewMain salesAgreementPriceBaseInfo = salesAgreementPriceReviewMainService.findSalesAgreementPriceBaseInfo(processInstanceId);
        SalesAgreementPriceReviewMainVo salesAgreementPriceReviewMainVo = HyperBeanUtils.copyPropertiesByJackson(salesAgreementPriceBaseInfo, SalesAgreementPriceReviewMainVo.class);
        return new JsonObject<>(salesAgreementPriceReviewMainVo);
    }


    @GetMapping("/querySalesAgreementPriceProductList")
    @Operation(summary = "查询协议价格审批产品信息")
    @PreFlowPermission
    public JsonObject<SalesAgreementOwnProductVo> querySalesAgreementPriceProductList(@RequestParam String processInstanceId) throws ExecutionException {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        SalesAgreementOwnProductVo salesAgreementOwnProductVo = salesAgreementProductService.convertToSalesAgreementOwnProductVo(processInstanceId,false);
        return new JsonObject<>(salesAgreementOwnProductVo);
    }



    @Operation(summary = "逐级上级到能包下折扣和价差的或到1级，第一次查传personId，personId为该流程发起人；之后无需传入")
    @GetMapping("/querySalesAgreementSuperiorContainApproverList")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> querySalesAgreementSuperiorContainApproverList(@RequestParam String processInstanceId, @RequestParam(required = false) String personId) throws ExecutionException {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //是否逐级之前
        boolean before = org.apache.commons.lang3.StringUtils.isNotBlank(personId);
        //当前登录人
        String currentPeronId = UserInfoHolder.getCurrentPersonId();
        personId = !before ? currentPeronId : personId;
        SalesAgreementOwnProductVo salesAgreementOwnProductVo = salesAgreementProductService.convertToSalesAgreementOwnProductVo(processInstanceId,false);
        List<SalesAgreementProductVo> agreementProductList = salesAgreementOwnProductVo.getSalesAgreementProductList();
        List<PriceReviewProductPriceGapVO> priceReviewProductPriceGapVOS = HyperBeanUtils.copyListPropertiesByJackson(agreementProductList, PriceReviewProductPriceGapVO.class);
        return new JsonObject<>(priceReviewFlowService.queryCenterLeaderApproverList(priceReviewProductPriceGapVOS, personId, before));
    }

    /**
     * 人员是否 可以审批 折扣、价差 ,若没查到对应配置则算作不包含
     * @param processInstanceId 流程实例id
     * @param personId 人员id
     * @return 全部产品都符合才满足
     */
    @Operation(summary = "人员是否 可以审批 折扣、价差 ,若没查到对应配置则算作不包含")
    @GetMapping("/querySalesAgreementIncludeDiscountAndPriceGap")
    @PreFlowPermission
    public JsonObject<Boolean> querySalesAgreementIncludeDiscountAndPriceGap(@RequestParam String processInstanceId,@RequestParam  String personId) throws ExecutionException {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        SalesAgreementOwnProductVo salesAgreementOwnProductVo = salesAgreementProductService.convertToSalesAgreementOwnProductVo(processInstanceId,false);
        List<SalesAgreementProductVo> agreementProductList = salesAgreementOwnProductVo.getSalesAgreementProductList();
        List<PriceReviewProductPriceGapVO> priceReviewProductPriceGapVOS = HyperBeanUtils.copyListPropertiesByJackson(agreementProductList, PriceReviewProductPriceGapVO.class);
        return new JsonObject<>(priceReviewFlowService.includeDiscountAndPriceGap(personId,priceReviewProductPriceGapVOS));
    }


    @PostMapping("/pageSalesAgreementProductTempCost")
    @Operation(summary = "销售协议增加成本产品分页")
    @PreFlowPermission(hasAnyNodes = {"sid-D21D96A9-4EF0-4523-9614-9E13BA05B74B"})
    public JsonObject<PageUtils<SalesAgreementProductVo>> pageSalesAgreementProductTempCost(@RequestBody @Valid PriceReviewProductQuery query) throws ExecutionException, InterruptedException {
        PreFlowPermissionAspect.checkProcessInstanceId(query.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(salesAgreementProductService.pageProductTempCost(query));
    }



    @Operation(summary = "是否需要添加临时成本")
    @GetMapping("/needToAddSalesAgreementTemporaryCosts")
    @PreFlowPermission(hasAnyNodes = {"sid-D21D96A9-4EF0-4523-9614-9E13BA05B74B"})
    public JsonObject<Boolean> needToAddSalesAgreementTemporaryCosts(@RequestParam String processInstanceId) throws ExecutionException {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        SalesAgreementOwnProductVo salesAgreementOwnProductVo = salesAgreementProductService.convertToSalesAgreementOwnProductVo(processInstanceId,false);
        List<SalesAgreementProductVo> agreementProductList = salesAgreementOwnProductVo.getSalesAgreementProductList();
        List<PriceReviewTempCostVO> priceReviewTempCostVOList = HyperBeanUtils.copyListPropertiesByJackson(agreementProductList, PriceReviewTempCostVO.class);
        return new JsonObject<>(priceReviewProductOwnService.needToAddTemporaryCostsBase(priceReviewTempCostVOList));
    }

    @Operation(summary = "校验协议价格审批")
    @GetMapping("/checkPriceProcessCompleted")
    @PreFlowPermission
    public JsonObject<Boolean> checkPriceProcessCompleted(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<SalesAgreementPriceReviewMain> salesAgreementPriceByParentProcessInstanced = salesAgreementPriceReviewMainService.findSalesAgreementPriceByParentProcessInstanced(processInstanceId);
        if (CollectionUtils.isNotEmpty(salesAgreementPriceByParentProcessInstanced)){
            List<SalesAgreementPriceReviewMain> list = salesAgreementPriceByParentProcessInstanced.stream().filter(item -> item.getProcessState() == 1).toList();
            if (CollectionUtils.isNotEmpty(list)){
                return new JsonObject<>(false);
            }
        }

        return new JsonObject<>(true);

    }




}
