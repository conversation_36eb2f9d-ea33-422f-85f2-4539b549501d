package com.topsec.crm.flow.core.controller.legalAffairs;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemotePaymentVerificationService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.paymentcollection.PaymentVerificationDTO;
import com.topsec.crm.contract.api.entity.receivableamount.CrmBatchLaunchReceivableQuery;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.flow.api.RemoteContractSignVerifyMainService;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractSignVerifyMainVo;
import com.topsec.crm.flow.api.dto.contractoriginal.vo.ContractDocVO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewNoticeArriveDTO;
import com.topsec.crm.flow.api.dto.legalAffairsMain.LegalAffairsBeanInfoLaunchDTO;
import com.topsec.crm.flow.api.dto.legalAffairsMain.LegalAffairsMainFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.legalAffairsMain.VO.LegalAffairsMainInfoVO;
import com.topsec.crm.flow.api.dto.legalAffairsMain.VO.LegalAffairsMainPageVO;
import com.topsec.crm.flow.api.dto.legalAffairsMain.VO.LegalAffairsMainQueryVO;
import com.topsec.crm.flow.api.dto.proofOfDebtCollection.ProofOfDebtCollectionQuery;
import com.topsec.crm.flow.api.dto.proofOfDebtCollection.ProofOfDebtCollectionVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.controller.legalAffairs.businessauth.LegalAffairsBusinessController;
import com.topsec.crm.flow.core.entity.ContractCollectionLetterMain;
import com.topsec.crm.flow.core.entity.ContractReviewMain;
import com.topsec.crm.flow.core.entity.ContractReviewNoticeArrive;
import com.topsec.crm.flow.core.entity.LegalAffairsMain;
import com.topsec.crm.flow.core.process.impl.LegalAffairsMainProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.PageDomain;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.common.web.page.TableSupport;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/legalAffairs")
@Tag(name = "【法律事务】", description = "legalAffairs")
@RequiredArgsConstructor
@Validated
public class LegalAffairsController extends BaseController {

    private final LegalAffairsMainProcessService legalAffairsMainProcessService;

    private final LegalAffairsMainService legalAffairsMainService;
    private final RemoteContractExecuteService remoteContractExecuteService;
    private final ContractReviewMainService mainService;
    private final ContractReviewNoticeArriveService noticeArriveService;
    private final ContractOriginalDocumentService contractOriginalDocumentService;
    private final RemoteContractExecuteService executeService;
    private final RemoteContractSignVerifyMainService remoteContractSignVerifyMainService;
    private final ProofOfDebtCollectionService proofOfDebtCollectionService;
    private final RemotePaymentVerificationService remotePaymentVerificationService;

    @PostMapping("/launch")
    @Operation(summary = "发起法律事务流程")
    @PreAuthorize(hasPermission = "crm_contract_receivable_legal_affairs_add", dataScope = "crm_contract_receivable_legal_affairs_add")
    public JsonObject<Boolean> launch(@Valid @RequestBody LegalAffairsMainFlowLaunchDTO flowLaunchDTO){
        // 看这个合同的合同负责人 和 personIdList的关系
        assert flowLaunchDTO != null;
        String contractNumber = flowLaunchDTO.getBaseInfo().getContractNumber();
        checkLegalAffairsAuthByContractNumber(contractNumber);

        LegalAffairsBeanInfoLaunchDTO baseInfo = flowLaunchDTO.getBaseInfo();
        baseInfo.setIsGenerate(false);
        // 把合同执行的部门id查出来 流程发起的时候要用
        CrmContractExecuteVO contractExecuteVO = remoteContractExecuteService.getByContractNumber(baseInfo.getContractNumber()).getObjEntity();
        if (contractExecuteVO != null){
            baseInfo.setContractOwnerDeptId(contractExecuteVO.getContractOwnerDeptId());
            baseInfo.setExtensionDemandTime(contractExecuteVO.getSigningTime());
        }
        return new JsonObject<>(legalAffairsMainProcessService.launch(flowLaunchDTO));
    }

    @GetMapping("/getByContractNumber")
    @Operation(summary = "根据合同号查询合同信息", method = "POST")
    @PreAuthorize
    public JsonObject<CrmContractExecuteVO> getByContractNumber(@RequestParam String contractNumber) {
        return executeService.getByContractNumber(contractNumber);
    }

    @PostMapping("/pageContractExecuteByCondition")
    @Operation(summary = "分页查询合同执行（发起法律事务查询合同号的接口）", method = "POST")
    @PreAuthorize(hasPermission = "crm_contract_receivable_legal_affairs_add", dataScope = "crm_contract_execute_company")
    public JsonObject<PageUtils<CrmContractExecuteVO>> pageContractExecuteByCondition(@RequestBody CrmBatchLaunchReceivableQuery query) {
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        // 为空是所有人的数据
        Set<String> personIdList = dataScopeParam.getPersonIdList();

        CrmContractAfterQuery afterQuery = CrmContractAfterQuery.buildBaseQuery();
        CrmContractAfterQuery.BaseQuery baseQuery = afterQuery.getBaseQuery();
        afterQuery.setPageNum(query.getPageNum());
        afterQuery.setPageSize(query.getPageSize());
        if (query.getContractOwnerId() != null) {
            if (personIdList != null && !personIdList.contains(query.getContractOwnerId())) {
                // personIdList不为空 且不含该人，证明他没有查询这个人的权限，则返回空
                return new JsonObject<>(PageUtils.empty());
            }
            baseQuery.setContractOwnerIds(List.of(query.getContractOwnerId()));
        } else {
            // 如果不为空就添加进去 为空不添加代表查所有人的
            if (personIdList != null) {
                baseQuery.setContractOwnerIds(new ArrayList<>(personIdList));
            }
        }
        CrmContractAfterQuery.SelectContractQuery selectContractQuery = new CrmContractAfterQuery.SelectContractQuery();
        selectContractQuery.setContractCompanyName(StringUtils.isEmpty(query.getKeyWord()) ? null : query.getKeyWord());
        selectContractQuery.setFinalCustomerName(StringUtils.isEmpty(query.getKeyWord()) ? null : query.getKeyWord());
        baseQuery.setContractOwnerDeptId(StringUtils.isEmpty(query.getContractOwnerDeptId()) ? null : query.getContractOwnerDeptId());
        afterQuery.setSelectContractQuery(selectContractQuery);
        return executeService.pageByCondition(afterQuery);
    }


    @PostMapping("/page")
    @Operation(summary = "分页查询法律事务")
    @PreAuthorize(hasPermission = "crm_contract_receivable_legal_affairs", dataScope = "crm_contract_receivable_legal_affairs")
    public JsonObject<TableDataInfo> getLegalAffairsMainPage(@RequestBody LegalAffairsMainQueryVO queryVO){
        // todo 需要和产品确认 是申请人 还是合同得负责人 目前取的申请人
        startPage();
        return new JsonObject<>(legalAffairsMainService.getLegalAffairsMainPage(queryVO));
    }

    @PostMapping("/updateLegalAffairsMain")
    @Operation(summary = "修改法律事务信息")
    @PreFlowPermission
    public JsonObject<Boolean> updateLegalAffairsMain( @RequestBody LegalAffairsBeanInfoLaunchDTO launchDTO){
        String id = launchDTO.getId();
        checkLegalAffairFlowAuthByMainId(id, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(legalAffairsMainService.updateLegalAffairsMain(launchDTO));
    }

    @GetMapping("/getLegalAffairsMainById")
    @Operation(summary = "根据主表ID查询信息")
    @PreFlowPermission
    public JsonObject<LegalAffairsMainInfoVO> getLegalAffairsMainById(@RequestParam String legalAffairsMainId){
        checkLegalAffairFlowAuthByMainId(legalAffairsMainId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(legalAffairsMainService.getLegalAffairsMainById(legalAffairsMainId));
    }


    @GetMapping("/getLegalAffairsMainByProcessInstanceId")
    @Operation(summary = "根据流程ID查询信息")
    @PreFlowPermission
    public JsonObject<LegalAffairsMainInfoVO> getLegalAffairsMainByProcessInstanceId(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(legalAffairsMainService.getLegalAffairsMainByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/checkHandleSuggest")
    @Operation(summary = "自动发起的01步办结，检查处理意见")
    @PreFlowPermission
    public JsonObject<Boolean> checkHandleSuggest(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(legalAffairsMainService.checkHandleSuggest(processInstanceId));
    }

    @GetMapping("/checkBeApplied")
    @Operation(summary = "自动发起的03步办结，检查被申请单位")
    @PreFlowPermission
    public JsonObject<Boolean> checkBeApplied(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(legalAffairsMainService.checkBeApplied(processInstanceId));
    }

    @GetMapping("/checkSendLetterTime")
    @Operation(summary = "法务办结，检查发函时间")
    @PreFlowPermission
    public JsonObject<Boolean> checkSendLetterTime(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(legalAffairsMainService.checkSendLetterTime(processInstanceId));
    }

    @GetMapping("/getContractInfoByProcessInstanceId")
    @Operation(summary = "根据合同编号查询合同信息")
    @PreFlowPermission
    public JsonObject<CrmContractExecuteVO> getContractInfoByContractNumber(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        LegalAffairsMainInfoVO main = legalAffairsMainService.getLegalAffairsMainByProcessInstanceId(processInstanceId);
        String contractNumber = main.getContractNumber();
        return remoteContractExecuteService.getByContractNumber(contractNumber);
    }

    @GetMapping("/getNoticeArriveByProcessInstanceId")
    @Operation(summary = "根据流程实例id查询通知和送达信息")
    @PreFlowPermission
    public JsonObject<List<ContractReviewNoticeArriveDTO>> getNoticeArriveByProcessInstanceId(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        LegalAffairsMainInfoVO infoVO = legalAffairsMainService.getLegalAffairsMainByProcessInstanceId(processInstanceId);
        String contractNumber = infoVO.getContractNumber();
        ContractReviewMain main = mainService.getOne(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getContractNumber, contractNumber)
                .eq(ContractReviewMain::getDelFlag, 0).last("limit 1"));

        return new JsonObject<>(HyperBeanUtils.copyListProperties(noticeArriveService.list(new LambdaQueryWrapper<ContractReviewNoticeArrive>()
                .eq(ContractReviewNoticeArrive::getContractReviewMainId, main.getId())
                .eq(ContractReviewNoticeArrive::getDelFlag, 0)), ContractReviewNoticeArriveDTO::new));
    }

    @PostMapping("/exportLegalAffairsMainInfo")
    @Operation(summary = "导出法律事务列表数据")
    @PreAuthorize(hasAnyPermission = "crm_contract_receivable_legal_affairs_export", dataScope = "crm_contract_receivable_legal_affairs_export")
    public void exportLegalAffairsMainInfo(@RequestBody LegalAffairsMainQueryVO queryVO) throws IOException {
        List<LegalAffairsMainPageVO> exportList = legalAffairsMainService.exportLegalAffairsMainInfo(queryVO);
        ExcelUtil<LegalAffairsMainPageVO> excelUtil = new ExcelUtil<>(LegalAffairsMainPageVO.class);
        excelUtil.exportExcel(response,exportList,"法律事务列表数据");
    }

    @PostMapping("/listDocumentByContractNumber")
    @Operation(summary = "根据流程ID获取合同原件信息")
    @PreFlowPermission
    public JsonObject<List<ContractDocVO>> getListDocumentByProcessInstanceId(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        LegalAffairsMainInfoVO infoVO = legalAffairsMainService.getLegalAffairsMainByProcessInstanceId(processInstanceId);
        String contractNumber = infoVO.getContractNumber();
        return new JsonObject<>(contractOriginalDocumentService.listDocumentByContractNumber(contractNumber));
    }

    @GetMapping("/getContractSignVerifyByProcessInstanceId")
    @Operation(summary = "查询签验收单信息", method = "GET")
    @PreFlowPermission
    public JsonObject<List<ContractSignVerifyMainVo>> get(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        LegalAffairsMain main = legalAffairsMainService.getByProcessInstanceId(processInstanceId);
        if (main == null) {
            throw new CrmException("参数有误");
        }
        return remoteContractSignVerifyMainService.list(main.getContractNumber());
    }

    @GetMapping("/getProofOfDebtCollectionByProcessInstanceId")
    @Operation(summary = "查询催款证据", method = "GET")
    @PreFlowPermission
    public JsonObject<PageUtils<ProofOfDebtCollectionVO>> getProofOfDebtCollection(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        LegalAffairsMain main = legalAffairsMainService.getByProcessInstanceId(processInstanceId);
        if (main == null) {
            throw new CrmException("参数有误");
        }
        String contractNumber = main.getContractNumber();
        ProofOfDebtCollectionQuery proofOfDebtCollectionQuery = new ProofOfDebtCollectionQuery();
        proofOfDebtCollectionQuery.setContractNumber(contractNumber);
        PageDomain pageDomain = TableSupport.buildPageRequest();
        proofOfDebtCollectionQuery.setPageSize(pageDomain.getPageSize());
        proofOfDebtCollectionQuery.setPageNum(pageDomain.getPageNum());
        return new JsonObject<>(proofOfDebtCollectionService.page(proofOfDebtCollectionQuery));

    }

    @GetMapping("/getVerificationListByBusinessNumber")
    @Operation(summary = "获取合同回款信息(根据合同号)", method = "GET")
    @PreFlowPermission
    public JsonObject<List<PaymentVerificationDTO>> getVerificationListByBusinessNumber(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        LegalAffairsMain main = legalAffairsMainService.getByProcessInstanceId(processInstanceId);
        if (main == null) {
            throw new CrmException("参数有误");
        }
        String contractNumber = main.getContractNumber();
        return remotePaymentVerificationService.getVerificationListByBusinessNumber(contractNumber);
    }

    private void checkLegalAffairsAuthByContractNumber(String contractNumber) {
        LegalAffairsBusinessController.check(contractNumber, remoteContractExecuteService);
    }

    private void checkLegalAffairFlowAuthByMainId(String mainId, String processInstanceId){
        LegalAffairsMainInfoVO mainInfoVO = legalAffairsMainService.getLegalAffairsMainById(mainId);
        if (mainInfoVO == null) {
            throw new CrmException("参数信息有误");
        }
        PreFlowPermissionAspect.checkProcessInstanceId(mainInfoVO.getProcessInstanceId(), processInstanceId);
    }

}
