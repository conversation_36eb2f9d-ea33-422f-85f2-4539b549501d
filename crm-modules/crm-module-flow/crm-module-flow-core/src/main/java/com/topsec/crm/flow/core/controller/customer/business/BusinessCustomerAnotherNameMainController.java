package com.topsec.crm.flow.core.controller.customer.business;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.topsec.crm.customer.api.RemoteCustomerAnotherService;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerAnotherVo;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.dto.customer.CustomerAnotherNameApproveFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.customer.CustomerAnotherNameMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CustomerAnotherNameMain;
import com.topsec.crm.flow.core.process.impl.CustomerAnotherNameProcessService;
import com.topsec.crm.flow.core.service.ICustomerAnotherNameMainService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.operation.api.RemoteIndustryService;
import com.topsec.crm.operation.api.entity.CrmIndustryVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbsapi.client.TbsPersonClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/customerAnotherNameMain")
@Tag(name = "客户别名审批相关Controller", description = "/customerAnotherNameMain/flow")
@RequiredArgsConstructor
@Validated
public class BusinessCustomerAnotherNameMainController extends BaseController {
    @Autowired
    private ICustomerAnotherNameMainService customerAnotherNameMainService;
    @Autowired
    private CustomerAnotherNameProcessService customerAnotherNameProcessService;
    @Autowired
    private RemoteCustomerService remoteCustomerService;
    @Autowired
    private RemoteIndustryService remoteIndustryService;
    @Autowired
    private TbsPersonClient tbsPersonClient;

    @PostMapping("/selectInfo")
    @Operation(summary = "查询别名审批申请详情")
    @PreAuthorize(hasPermission = "crm_customer_admin_alias_approve_list", dataScope = "crm_customer_admin_alias_approve_list")
    public JsonObject<CustomerAnotherNameMainVo> selectInfo(@RequestBody CustomerAnotherNameMainVo customerAnotherNameMainVo){
        //1.查询流程信息
        CustomerAnotherNameMain customerAnotherNameMain = customerAnotherNameMainService.getOne(new QueryWrapper<CustomerAnotherNameMain>().eq("process_instance_id", customerAnotherNameMainVo.getProcessInstanceId()));
        CustomerAnotherNameMainVo result = HyperBeanUtils.copyPropertiesByJackson(customerAnotherNameMain, CustomerAnotherNameMainVo.class);

        if(customerAnotherNameMain != null){
            //2.查询客户信息
            JsonObject<CrmCustomerVo> jsonObject = remoteCustomerService.getCustomerInfo(customerAnotherNameMain.getCustomerId(), UserInfoHolder.getCurrentPersonId(),true);

            if(jsonObject.isSuccess() && jsonObject.getObjEntity() != null){
                CrmCustomerVo crmCustomerVo = jsonObject.getObjEntity();
                //2.查询行业信息
                JsonObject<CrmIndustryVO> jObject = remoteIndustryService.industryByUuid(crmCustomerVo.getIndustryId());
                if(jObject.isSuccess()){
                    CrmIndustryVO industryVO = jObject.getObjEntity();
                    crmCustomerVo.setIndustryType(industryVO.getType());
                }
                result.setCustomerVo(crmCustomerVo);
            }
        }

        return new JsonObject<>(result);
    }

}
