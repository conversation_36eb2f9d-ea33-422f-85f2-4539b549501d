package com.topsec.crm.flow.core.controllerhidden.backingmaterial;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewMainInfo;
import com.topsec.crm.flow.core.entity.BackingMaterial;
import com.topsec.crm.flow.core.process.impl.BackingMaterialProcessService;
import com.topsec.crm.flow.core.service.BackingMaterialService;
import com.topsec.crm.flow.core.service.PriceReviewMainService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 */
@RestController
@RequestMapping("/hidden/backingMaterial")
@Tag(name = "支撑材料", description = "/backingMaterial")
@RequiredArgsConstructor
public class HiddenBackingMaterialController extends BaseController {

    private final BackingMaterialService backingMaterialService;
    private final PriceReviewMainService priceReviewMainService;

    private final BackingMaterialProcessService backingMaterialProcessService;

    @PostMapping("/autoDeleteBackingMaterial")
    @Operation(summary = "定时若价审失效，判断延交流程是否办结，未办结则删除流程")
    JsonObject<Boolean> autoDeleteBackingMaterial(){
        List<BackingMaterial> list = backingMaterialService.list(new LambdaQueryWrapper<BackingMaterial>().eq(BackingMaterial::getDelFlag, 0)
                .isNotNull(BackingMaterial::getProcessInstanceId)
                .eq(BackingMaterial::getProcessState, 1));
        for (BackingMaterial backingMaterial : list) {
            PriceReviewMainInfo priceReviewMainInfo = priceReviewMainService.baseInfo(backingMaterial.getPriceReviewProcessInstanceId());
            // 判断价审是否失效
            boolean theLatestPassedPriceReviewValid = priceReviewMainService.isTheLatestPassedPriceReviewValid(priceReviewMainInfo.getProjectId());
            if (!theLatestPassedPriceReviewValid) {
                backingMaterialProcessService.remove(backingMaterial.getProcessInstanceId());
                backingMaterial.setRemark("价审失效系统自动删除");
                backingMaterial.setDelFlag(1);
                backingMaterialService.updateById(backingMaterial);
            }
        }
        return new JsonObject<>(true);
    }
}
