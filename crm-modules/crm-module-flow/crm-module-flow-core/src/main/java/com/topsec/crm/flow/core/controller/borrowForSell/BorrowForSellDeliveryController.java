package com.topsec.crm.flow.core.controller.borrowForSell;


import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellDeliveryDTO;
import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellDeliverySnDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BorrowForSell;
import com.topsec.crm.flow.core.entity.BorrowForSellDeliverySn;
import com.topsec.crm.flow.core.service.IBorrowForSellDeliveryService;
import com.topsec.crm.flow.core.service.IBorrowForSellDeliverySnService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 借转销发货信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/borrowForSellDelivery")
@Tag(name = "借转销流程发货信息", description = "/borrowForSellDelivery")
public class BorrowForSellDeliveryController extends BaseController {

    @Autowired
    private IBorrowForSellDeliveryService borrowForSellDeliveryService;

    @Autowired
    private IBorrowForSellDeliverySnService borrowForSellDeliverySnService;

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @PreFlowPermission(hasAnyNodes = {"sid-7EF44C1D-7306-4B8E-B06A-97EDE353FEA8","sid-D6F18F46-0AB8-405A-95F3-F9A51B290208","borrowingSelling_03","borrowingSelling_04","borrowingSelling_05","borrowingSelling_05A","borrowingSelling_06"})
    @PostMapping("/updateDelivery")
    @Operation(summary = "修改发货信息")
    public JsonObject<Boolean> updateDelivery(@RequestParam String borrowId, @RequestBody List<BorrowForSellDeliveryDTO> deliveryList) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<Boolean>(borrowForSellDeliveryService.updateDelivery(borrowId,deliveryList));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @PostMapping("/queryDeliverySnByIds")
    @Operation(summary = "根据发货单ID查询发货单详细信息")
    public JsonObject<List<BorrowForSellDeliverySnDTO>> queryDeliverySnByIds(@RequestParam String borrowId, @RequestBody List<String> ids) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            List<BorrowForSellDeliverySn> borrowForSellDeliverySns = borrowForSellDeliverySnService.listByIds(ids);
            return new JsonObject<List<BorrowForSellDeliverySnDTO>>(HyperBeanUtils.copyListPropertiesByJackson(borrowForSellDeliverySns, BorrowForSellDeliverySnDTO.class));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }
}

