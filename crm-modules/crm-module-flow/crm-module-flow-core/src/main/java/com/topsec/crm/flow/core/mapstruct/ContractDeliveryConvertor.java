package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDetailDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.dto.performancereport.ContractDeliveryProductVO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportContractDeliveryDTO;
import com.topsec.crm.flow.core.entity.ContractReviewDeliveryDetailOwn;
import com.topsec.crm.flow.core.entity.ContractReviewDeliveryDetailThird;
import com.topsec.crm.flow.core.entity.ReturnExchangeProduct;
import com.topsec.crm.flow.core.entity.ReturnExchangeProductThird;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ContractDeliveryConvertor {

    ContractDeliveryConvertor INSTANCE = Mappers.getMapper(ContractDeliveryConvertor.class);

    // 从 PerformanceReportContractDeliveryDTO 转换到 ContractDeliveryDTO
    @Mapping(target = "performanceDeliveryId", source = "id")
    @Mapping(target = "consigneeId", source = "consigneeId")
    @Mapping(target = "consigneeName", source = "consigneeName")
    @Mapping(target = "consigneePhone", source = "consigneePhone")
    @Mapping(target = "consigneeEmail", source = "consigneeEmail")
    @Mapping(target = "consigneeAddress", source = "consigneeAddress")
    @Mapping(target = "consigneeAreaCode", source = "consigneeAreaCode")
    @Mapping(target = "deliveryMethod", source = "deliveryMethod")
    @Mapping(target = "deliveryDate", source = "deliveryDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "deliveryTimeliness", source = "deliveryLeadTime")
    @Mapping(target = "isInvalidContract", source = "isContractWaived")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "originalContractNumber", source = "originalContractNo")
    ContractDeliveryDTO performanceToContractDeliveryDTO(PerformanceReportContractDeliveryDTO source);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deliveryQuantity", source = "num")
    @Mapping(target = "returnChangeProductId", source = "projectProductRecordId")
    ContractDeliveryDetailDTO performanceToDetailDTO(ContractDeliveryProductVO source);

    // 合同自有产品DTO --> 发货产品DTO
    @Mapping(target = "contractProductId", source = "id")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "productQuantity", source = "productNum")
    @Mapping(target = "projectProductId", source = "projectProductOwnId")
    ContractDeliveryDetailDTO contractToOwnDTO(ContractProductOwnDTO dto);

    // 合同三方产品DTO --> 发货产品DTO
    @Mapping(target = "contractProductId", source = "id")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "productQuantity", source = "productNum")
    @Mapping(target = "projectProductId", source = "projectProductThirdId")
    ContractDeliveryDetailDTO contractToThirdDTO(ContractProductThirdDTO dto);

    // 发货产品DTO --> 自有产品发货明细实体
    @Mapping(target = "contractProductOwnId", source = "contractProductId")
    ContractReviewDeliveryDetailOwn toOwnEntity(ContractDeliveryDetailDTO dto);

    // 发货产品DTO --> 三方产品发货明细实体
    @Mapping(target = "contractProductThirdId", source = "contractProductId")
    ContractReviewDeliveryDetailThird toThirdEntity(ContractDeliveryDetailDTO dto);

    // 自有产品发货明细实体 --> 发货产品DTO
    @Mapping(target = "contractProductId", source = "contractProductOwnId")
    ContractDeliveryDetailDTO ownEntityToDTO(ContractReviewDeliveryDetailOwn entity);

    // 三方产品发货明细实体 --> 发货产品DTO
    @Mapping(target = "contractProductId", source = "contractProductThirdId")
    ContractDeliveryDetailDTO thirdEntityToDTO(ContractReviewDeliveryDetailThird entity);

    // 退货换货自有产品 --> 发货产品DTO
    @Mapping(target = "id",ignore = true)
    @Mapping(source = "id",target = "returnChangeProductId")
    @Mapping(source = "productNum",target = "productQuantity")
    @Mapping(source = "recordId",target = "projectProductId")
    ContractDeliveryDetailDTO returnChangeToOwnDTO(ReturnExchangeProduct entity);

    // 退货换货第三方产品 --> 发货产品DTO
    @Mapping(target = "id",ignore = true)
    @Mapping(source = "id",target = "returnChangeProductId")
    @Mapping(source = "productNum",target = "productQuantity")
    @Mapping(source = "recordId",target = "projectProductId")
    ContractDeliveryDetailDTO returnChangeToThirdDTO(ReturnExchangeProductThird entity);

    // 合同-自有产品DTO列表 --> 发货产品DTO列表
    List<ContractDeliveryDetailDTO> contractToOwnDTOList(List<ContractProductOwnDTO> list);

    // 合同-三方产品DTO列表 --> 发货产品DTO列表
    List<ContractDeliveryDetailDTO> contractToThirdDTOList(List<ContractProductThirdDTO> list);

    // 自有产品发货明细实体列表 --> 发货产品DTO列表
    List<ContractDeliveryDetailDTO> entityToOwnDTOList(List<ContractReviewDeliveryDetailOwn> list);

    // 三方产品发货明细实体列表 --> 发货产品DTO列表
    List<ContractDeliveryDetailDTO> entityToThirdDTOList(List<ContractReviewDeliveryDetailThird> list);

    List<ContractDeliveryDetailDTO> toContractDeliveryDetailDTOList(List<ContractDeliveryProductVO> list);

    List<ContractDeliveryDetailDTO> returnChangetoOwnDTOList(List<ReturnExchangeProduct> list);

    List<ContractDeliveryDetailDTO> returnChangetoThirdDTOList(List<ReturnExchangeProductThird> list);

    List<ContractDeliveryDetailDTO> performanceToDetailDTOList(List<ContractDeliveryProductVO> list);
}
