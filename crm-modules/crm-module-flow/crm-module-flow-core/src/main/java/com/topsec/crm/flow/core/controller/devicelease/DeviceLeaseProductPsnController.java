package com.topsec.crm.flow.core.controller.devicelease;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.DeviceLease;
import com.topsec.crm.flow.core.entity.DeviceLeaseProduct;
import com.topsec.crm.flow.core.entity.DeviceLeaseProductPsn;
import com.topsec.crm.flow.core.service.DeviceLeaseProductPsnService;
import com.topsec.crm.flow.core.service.DeviceLeaseProductService;
import com.topsec.crm.flow.core.service.DeviceLeaseService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 借试用产品信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@RestController
@RequestMapping("/deviceLeaseProductPsn")
public class DeviceLeaseProductPsnController extends BaseController {

    @Autowired
    private DeviceLeaseProductPsnService deviceLeaseProductPsnService;

    @Autowired
    private DeviceLeaseService deviceLeaseService;

    @Autowired
    private DeviceLeaseProductService deviceLeaseProductService;

    @PreFlowPermission
    @PutMapping("/bindingSn")
    @Operation(summary = "绑定产品序列号")
    public JsonObject<Boolean> bindingSn(@RequestParam String productRecordId,@RequestBody List<String> psnList) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        DeviceLease one = deviceLeaseService.getOne(new LambdaQueryWrapper<DeviceLease>().eq(DeviceLease::getProcessInstanceId, processInstanceId));
        if(null != one){
            DeviceLeaseProduct exist = deviceLeaseProductService.getOne(new LambdaQueryWrapper<DeviceLeaseProduct>().eq(DeviceLeaseProduct::getLeaseId, one.getId()).eq(DeviceLeaseProduct::getId, productRecordId));
            if(null != exist){
                return new JsonObject<>(deviceLeaseProductPsnService.bindingSn(productRecordId,psnList));
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @DeleteMapping("/unbindingSn")
    @Operation(summary = "解绑产品序列号")
    public JsonObject<Boolean> unbindingSn(@RequestParam String psn) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        DeviceLease one = deviceLeaseService.getOne(new LambdaQueryWrapper<DeviceLease>().eq(DeviceLease::getProcessInstanceId, processInstanceId));
        DeviceLeaseProductPsn device = deviceLeaseProductPsnService.getOne(new LambdaQueryWrapper<DeviceLeaseProductPsn>().eq(DeviceLeaseProductPsn::getPsn, psn));
        if(null != one && null != device){
            DeviceLeaseProduct exist = deviceLeaseProductService.getOne(new LambdaQueryWrapper<DeviceLeaseProduct>().eq(DeviceLeaseProduct::getLeaseId, one.getId()).eq(DeviceLeaseProduct::getId, device.getProductRecordId()));
            if(null != exist){
                return new JsonObject<>(deviceLeaseProductPsnService.unbindingSn(psn));
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

}
