package com.topsec.crm.flow.core.controller.contractinvoice;

import com.github.pagehelper.PageHelper;
import com.topsec.crm.contract.api.ContractProductDetailQuery;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.CrmContractProductAllDetailVO;
import com.topsec.crm.contract.api.entity.CrmRevenueRecognitionVO;
import com.topsec.crm.flow.api.RemoteContractSignVerifyMainService;
import com.topsec.crm.flow.api.RemoteContractUnconfirmedDetailService;
import com.topsec.crm.flow.api.dto.contractInvoice.ContractProductDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.InvoiceDocDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.ContractMakeInvoiceDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.returnInvoice.ContractReturnInvoiceDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.returnInvoice.ContractReturnInvoiceFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.returnInvoice.MakeInvoiceInfoVO;
import com.topsec.crm.flow.api.dto.contractInvoice.returnInvoice.ReturnInvoiceProductDTO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractSignVerifyAttachmentDTO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedDetailVo;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.process.impl.ContractReturnInvoiceProcessService;
import com.topsec.crm.flow.core.service.ContractInvoiceExtendService;
import com.topsec.crm.flow.core.service.ContractMakeInvoiceService;
import com.topsec.crm.flow.core.service.ContractReturnInvoiceService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/contractReturnInvoice")
@Tag(name = "【合同退票】-流程",description = "/contractReturnInvoice")
public class ContractReturnInvoiceController extends BaseController {

    private final ContractReturnInvoiceProcessService processService;
    private final ContractMakeInvoiceService makeInvoiceService;
    private final ContractInvoiceExtendService extendService;
    private final RemoteContractExecuteService remoteContractExecuteService;
    private final ContractInvoiceExtendService contractInvoiceExtendService;
    private final RemoteContractSignVerifyMainService remoteContractSignVerifyMainService;
    private final RemoteContractUnconfirmedDetailService remoteContractUnconfirmedDetailService;
    private final ContractReturnInvoiceService contractReturnInvoiceService;

    @PostMapping("/launch")
    @Operation(summary = "发起合同退票流程")
    @PreAuthorize(hasPermission = "crm_contract_execute_invoice_refund_apply")
    public JsonObject<Boolean> launch(@RequestBody ContractReturnInvoiceFlowLaunchDTO req){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(req.getContractReturnInvoiceDTO().getContractNumber(), UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(processService.launch(req));
    }

    @GetMapping("/getMakeInvoiceProcess")
    @Operation(summary = "获取开票流程列表",parameters = {@Parameter(name = "contractNumber",description = "合同号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_execute_invoice_refund_apply")
    public JsonObject<TableDataInfo> getMakeInvoiceProcess(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return  new JsonObject<>(makeInvoiceService.getPageByContractNumber(contractNumber));
    }

    @GetMapping("/getMakeInvoiceInfo")
    @Operation(summary = "获取开票信息",parameters = {@Parameter(name = "processInstanceIds",description = "选择的开票流程实例IDList")})
    @PreAuthorize(hasPermission = "crm_contract_execute_invoice_refund_apply")
    public JsonObject<ContractMakeInvoiceDTO> getMakeInvoiceInfo(@RequestParam List<String> processInstanceIds){
        return new JsonObject<>(extendService.getLastMakeInvoiceInfo(processInstanceIds));
    }

    @GetMapping("/getMakeInvoiceInfoAfterLaunch")
    @Operation(summary = "获取开票信息-发起之后",parameters = {@Parameter(name = "processInstanceId",description = "退票流程实例ID")})
    @PreFlowPermission
    public JsonObject<MakeInvoiceInfoVO> getMakeInvoiceInfoAfterLaunch(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程实例ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractInvoiceExtendService.getLastMakeInvoiceInfo(processInstanceId));
    }

    @GetMapping("/getReturnInvoiceProduct")
    @Operation(summary = "获取可退票产品列表",parameters = {@Parameter(name = "processInstanceIds",description = "选择的开票流程实例IDList")})
    @PreAuthorize(hasPermission = "crm_contract_execute_invoice_refund_apply")
    public JsonObject<List<ReturnInvoiceProductDTO>> getReturnInvoiceProduct(@RequestParam List<String> processInstanceIds){
        return new JsonObject<>(extendService.getReturnInvoiceProduct(processInstanceIds));
    }

    @GetMapping("/getRevenueRecognition")
    @Operation(summary = "获取收入确认列表-详情",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreFlowPermission
    public JsonObject<PageUtils<CrmRevenueRecognitionVO>> getRevenueRecognition(@RequestParam String contractNumber){
        PageUtils<CrmRevenueRecognitionVO> objEntity = remoteContractExecuteService.pageRevenueRecognitionByContractNumber(contractNumber).getObjEntity();
        return new JsonObject<>(objEntity);
    }

    @GetMapping("/getPaymentTerms")
    @Operation(summary = "获取付款条款",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreFlowPermission
    public JsonObject<List<PaymentProvisionDTO>> getPaymentTerms(@RequestParam String contractNumber){
        List<PaymentProvisionDTO> list = remoteContractExecuteService.pagePaymentProvisionByContractNumber(contractNumber).getObjEntity().getList();
        return new JsonObject<>(Objects.requireNonNullElseGet(list, ArrayList::new));
    }

    @GetMapping("/getMakeInvoiceDoc")
    @Operation(summary = "获取电汇底单及其他单据",description ="附件",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreFlowPermission
    public JsonObject<List<InvoiceDocDTO>> getMakeInvoiceDoc(@RequestParam String contractNumber){
         return new JsonObject<>(extendService.getMakeInvoiceDoc(contractNumber));
    }

    @GetMapping("/getAttach")
    @Operation(summary = "获取签验收单据",description ="附件",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreFlowPermission
    public JsonObject<List<ContractSignVerifyAttachmentDTO>> getAttach(@RequestParam String contractNumber){
        List<ContractSignVerifyAttachmentDTO> list = remoteContractSignVerifyMainService.getAttachAfter01(List.of(contractNumber)).getObjEntity().get(contractNumber);
        return new JsonObject<>(Objects.requireNonNullElseGet(list, ArrayList::new));
    }

    @GetMapping("/getContractProcessPage")
    @Operation(summary = "开票/退票流程-分页列表")
    @PreFlowPermission
    public JsonObject<TableDataInfo> getContractProcessPage(@RequestParam String contractNumber){
        startPage();
        return new JsonObject<>(contractInvoiceExtendService.getInvoiceProcessPage(contractNumber, PageHelper.getLocalPage().getPageSize(), PageHelper.getLocalPage().getPageNum()));
    }

    @PostMapping("/getContractProductList")
    @Operation(summary = "合同产品",description = "合同中的所有产品行签验收情况")
    @PreFlowPermission
    public JsonObject<List<ContractProductDTO>> getContractProductList(@RequestBody ContractProductDetailQuery query){
        query.setNeedGrossMargin(true);
        query.setNeedSpecialCode(true);
        query.setNeedSn(true);
        List<CrmContractProductAllDetailVO> entity = remoteContractExecuteService.queryContractProductDetailByCondition(query).getObjEntity();
        List<ContractProductDTO> contractProductDTOS = HyperBeanUtils.copyListProperties(entity, ContractProductDTO::new);
        return new JsonObject<>(contractProductDTOS);
    }

    @GetMapping("/getProcessInfo")
    @Operation(summary = "获取合同退票流程信息",parameters = {@Parameter(name = "processInstanceId",description = "流程实例ID")})
    @PreFlowPermission
    public JsonObject<ContractReturnInvoiceDTO> getProcessInfo(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程实例ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReturnInvoiceService.getProcessInfo(processInstanceId));
    }

}
