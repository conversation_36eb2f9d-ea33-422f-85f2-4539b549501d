package com.topsec.crm.flow.core.controller.sealManagement;

import com.topsec.crm.flow.api.dto.sealApplication.SealApplicationFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.TargetedInventoryPreparationQuashFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.TargetedInventoryPreparationQuashBaseInfoVO;
import com.topsec.crm.flow.api.vo.sealApplication.SealApplicationInfoVO;
import com.topsec.crm.flow.core.process.impl.SealApplicationContractReviewProcessService;
import com.topsec.crm.flow.core.process.impl.SealApplicationReturnOrExchangeProcessService;
import com.topsec.crm.flow.core.service.ISealApplicationContractReviewService;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 印鉴申请主表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-27 16:02
 */
@RestController
@RequestMapping("/seal-application-contract-review")
@RequiredArgsConstructor
@Validated
public class SealApplicationContractReviewController extends BaseController {
    private final SealApplicationContractReviewProcessService sealApplicationContractReviewProcessService;
    private final ISealApplicationContractReviewService iSealApplicationContractReviewService;
    @PostMapping("/launch")
    @Operation(summary = "发起合同评审印鉴流程")
    @PreAuthorize(hasPermission = "crm_flow_seal")
    public JsonObject<Boolean> launch(@Valid @RequestBody SealApplicationFlowLaunchDTO launchDTO) {
        return new JsonObject<>(sealApplicationContractReviewProcessService.launch(launchDTO));
    }

    @GetMapping("/QuerySealApplicationByContractNumber")
    @Operation(summary = "根据合同号查询印鉴信息")
    public JsonObject<List<SealApplicationInfoVO>> queryTIPQuashInfoByTIPQuashMainId(@RequestParam String ContractNumber){
        CrmAssert.hasText(ContractNumber, "合同号不能为空");
        return new JsonObject<>(iSealApplicationContractReviewService.listSealInfoByContractNumber(ContractNumber));
    }


}
