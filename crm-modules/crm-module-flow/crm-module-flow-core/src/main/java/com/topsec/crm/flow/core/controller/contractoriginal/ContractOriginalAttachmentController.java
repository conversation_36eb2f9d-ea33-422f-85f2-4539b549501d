package com.topsec.crm.flow.core.controller.contractoriginal;

import com.topsec.crm.contract.api.entity.originaldocument.CrmContractOriginalDocumentVO;
import com.topsec.crm.flow.api.dto.contractoriginal.AttachmentInfos;
import com.topsec.crm.flow.api.dto.contractoriginal.ContractOriginalAttachmentInfoDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.service.ContractOriginalDocumentAttachmentService;
import com.topsec.crm.flow.core.service.ContractOriginalDocumentService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 费用合同附件
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contractOriginalAttachment")
@Tag(name = "【合同原件附件-流程业务接口】", description = "contractOriginalAttachment")
@RequiredArgsConstructor
@Validated
public class ContractOriginalAttachmentController extends BaseController {

    private final ContractOriginalDocumentAttachmentService contractOriginalDocumentAttachmentService;

    @Resource
    private ContractOriginalDocumentService contractOriginalDocumentService;

    @PreFlowPermission(hasAnyNodes = {"submitContract_01","submitContract_01A"})
    @PostMapping("/saveAttachment")
    @Operation(summary = "上交合同原件流程-01、01A上传合同原件附件")
    public JsonObject<Boolean> saveAttachment(@RequestBody @Validated AttachmentInfos attachmentInfos) {
        check(attachmentInfos.getOriginalId());
        return new JsonObject<>(contractOriginalDocumentAttachmentService.saveAttachments(attachmentInfos.getAttachmentInfoDTOs(), attachmentInfos.getOriginalId()));
    }

    @PreFlowPermission
    @GetMapping("/getByCostContractId")
    @Operation(summary = "查询合同原件附件信息")
    public JsonObject<List<ContractOriginalAttachmentInfoDTO>> getByCostContractId(@RequestParam String originalId) {
        check(originalId);
        return new JsonObject<>(contractOriginalDocumentAttachmentService.getByCostContractId(originalId));
    }

    @PreFlowPermission(hasAnyNodes = {"submitContract_01","submitContract_01A"})
    @GetMapping("/checkAttachment")
    @Operation(summary = "上交合同原件流程-01,01A办结节点，校验原件是否上交")
    public JsonObject<Boolean> checkAttachment(@RequestParam String originalId) {
        check(originalId);
        return new JsonObject<>(contractOriginalDocumentAttachmentService.checkAttachment(originalId));
    }

    private void check(String originalId){
        CrmContractOriginalDocumentVO contractOriginalDocument = contractOriginalDocumentService.getInfoById(originalId);
        CrmAssert.notNull(contractOriginalDocument, "合同原件记录不存在");
        PreFlowPermissionAspect.checkProcessInstanceId(contractOriginalDocument.getProcessInstanceId(),  HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
    }
}
