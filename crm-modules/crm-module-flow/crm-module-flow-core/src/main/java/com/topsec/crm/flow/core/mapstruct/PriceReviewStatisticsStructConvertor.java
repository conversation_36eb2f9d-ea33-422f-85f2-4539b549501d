package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.core.entity.PriceReviewStatistics;
import com.topsec.crm.project.api.entity.PriceStatisticsVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PriceReviewStatisticsStructConvertor {

    PriceReviewStatisticsStructConvertor INSTANCE= Mappers.getMapper(PriceReviewStatisticsStructConvertor.class);

    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "statisticsCategory", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    PriceReviewStatistics toStatistics(PriceStatisticsVO priceStatisticsVO);

    List<PriceReviewStatistics> toStatisticsList(List<PriceStatisticsVO> priceStatisticsVOS);
}
