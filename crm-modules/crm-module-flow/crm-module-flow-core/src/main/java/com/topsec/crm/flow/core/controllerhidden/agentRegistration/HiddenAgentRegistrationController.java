package com.topsec.crm.flow.core.controllerhidden.agentRegistration;

import com.alibaba.fastjson.JSONObject;
import com.topsec.crm.flow.api.dto.agentRegistration.AgentRegistrationFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.process.impl.AgentRegistrationProcessService;
import com.topsec.crm.flow.core.service.AgentRegistrationService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.MD5Util;
import com.topsec.crm.tyc.api.RemoteTycSelectService;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/hidden/agentRegistration")
@Tag(name = "渠道注册-内部")
@RequiredArgsConstructor
public class HiddenAgentRegistrationController {
    private final AgentRegistrationService registrationService;
    private final AgentRegistrationProcessService registrationProcessService;
    @Value("${tyc.certifiedToken}")
    private String certifiedToken;

    private final RemoteTycSelectService remoteTycSelectService;

    @PostMapping("/launch")
    @Operation(summary = "发起渠道注册流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody AgentRegistrationFlowLaunchDTO launchDTO) {
        return new JsonObject<>(registrationProcessService.launch(launchDTO));
    }
    @GetMapping("/getLaunchInfo")
    @Operation(summary = "渠道注册基础信息")
    public JsonObject<AgentRegistrationFlowLaunchDTO> getLaunchInfo(@RequestParam String processId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(registrationService.getLaunchInfo(processId));
    }

    @Operation(summary = "输入客户名称后，先查询本地数据库有没有这个客户的信息，如果没有调用天眼查的接口查询企业工商信息")
    @GetMapping("/getTYCBusinessInfoByName")
    public JsonObject<JSONObject> getTYCBusinessInfoByName(@RequestParam String agentName)
    {
        JsonObject<JSONObject> businessInfoByName = remoteTycSelectService.getBusinessInfoByName(agentName);
        if(businessInfoByName.isSuccess() && businessInfoByName.getObjEntity() != null){
            return businessInfoByName;
        }else{
            String sign = MD5Util.MD5(agentName + certifiedToken).toUpperCase();
            JsonObject<JSONObject> tycBusinessInfo = remoteTycSelectService.getTYCBusinessInfo(agentName, sign, null);
            if(tycBusinessInfo.isSuccess()){
                String busInfo = tycBusinessInfo.getObjEntity().toString();
                Integer errorCode = JSONObject.parseObject(busInfo).getInteger("error_code");
                //判断状态
                if (errorCode == null || errorCode == 0) {
                    JsonObject<JSONObject> queryNew = remoteTycSelectService.getBusinessInfoByName(agentName);
                    return queryNew;
                }
            }
        }
        return new JsonObject<>(null);
    }

    @PostMapping("/update")
    @Operation(summary = "信息修改")
    public JsonObject<Boolean> update(@RequestBody AgentRegistrationFlowLaunchDTO agentRegistrationDTO) {
        if (!agentRegistrationDTO.getAgentName().equals(agentRegistrationDTO.getAgentBusinessInfoDTO().getString("name"))) {
            throw new CrmException("渠道名称与公司名称不一致！");
        }
        return new JsonObject<>(registrationService.update(agentRegistrationDTO));
    }
}
