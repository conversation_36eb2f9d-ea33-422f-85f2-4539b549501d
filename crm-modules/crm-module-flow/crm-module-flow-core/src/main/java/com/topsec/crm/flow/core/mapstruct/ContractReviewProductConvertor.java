package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.dto.contractreview.projectinfo.CrmProjectProductMaintainInvestmentVO;
import com.topsec.crm.project.api.entity.CrmProjectProductMaintainVO;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.crm.project.api.entity.CrmProjectProductThirdVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 合同产品信息转换器
 *
 * <AUTHOR>
 * @date 2024/7/16 20:48
 */
@Mapper
public interface ContractReviewProductConvertor {

    ContractReviewProductConvertor INSTANCE = Mappers.getMapper(ContractReviewProductConvertor.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "priceReviewProcessInstanceId", source = "processInstanceId")
    @Mapping(target = "taxRate", ignore = true)
    @Mapping(target = "productPeriodStart", ignore = true)
    @Mapping(target = "productPeriodEnd", ignore = true)
    @Mapping(target = "taxRateProject", source = "taxRate")
    @Mapping(target = "projectProductOwnId", source = "id")
    @Mapping(target = "parentId", ignore = true)
    @Mapping(target = "crmProjectProductOwnService", ignore = true)
    @Mapping(target = "crmProjectProductOwnServiceRange", ignore = true)
    ContractProductOwnDTO toOwn(CrmProjectProductOwnVO crmProjectProductOwnVo);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "projectProductThirdId", source = "id")
    @Mapping(target = "taxRate", ignore = true)
    @Mapping(target = "productPeriodStart", ignore = true)
    @Mapping(target = "productPeriodEnd", ignore = true)
    @Mapping(target = "taxRateProject", source = "taxRate")
    ContractProductThirdDTO toThird(CrmProjectProductThirdVo crmProjectProductThirdVo);
}
