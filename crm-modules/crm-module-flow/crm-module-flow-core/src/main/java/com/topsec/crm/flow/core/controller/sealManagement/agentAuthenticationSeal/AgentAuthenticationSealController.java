package com.topsec.crm.flow.core.controller.sealManagement.agentAuthenticationSeal;

import com.topsec.crm.flow.api.dto.sealApplication.SealApplicationFlowLaunchDTO;
import com.topsec.crm.flow.core.process.impl.AgentAuthenticationSealProcessService;
import com.topsec.crm.flow.core.service.SealApplicationService;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsFormContentClient;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/agentAuthenticationSeal")
@Tag(name = "渠道认证印鉴申请", description = "/agentAuthenticationSeal")
public class AgentAuthenticationSealController {

    @Autowired
    private AgentAuthenticationSealProcessService agentAuthenticationSealProcessService;

    @Autowired
    private SealApplicationService sealApplicationService;

    @Autowired
    private TfsFormContentClient tfsFormContentClient;

    @PreAuthorize(hasPermission = "crm_agent_authentication_launch")
    @PostMapping("/launch")
    @Operation(summary = "发起渠道认证印鉴申请流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody SealApplicationFlowLaunchDTO launchDTO) {
        return new JsonObject<>(agentAuthenticationSealProcessService.launch(launchDTO));
    }

}
