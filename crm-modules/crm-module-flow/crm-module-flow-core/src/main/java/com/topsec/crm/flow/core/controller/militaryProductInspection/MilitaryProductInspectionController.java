package com.topsec.crm.flow.core.controller.militaryProductInspection;

import com.topsec.crm.flow.api.dto.militaryinspection.*;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.FactoryInspectionDetails;
import com.topsec.crm.flow.core.entity.MilitaryInspectionDetails;
import com.topsec.crm.flow.core.entity.MilitaryInspectionProduct;
import com.topsec.crm.flow.core.process.impl.MilitaryProductInspectionProcessService;
import com.topsec.crm.flow.core.service.FactoryInspectionDetailsService;
import com.topsec.crm.flow.core.service.MilitaryInspectionDetailsService;
import com.topsec.crm.flow.core.service.MilitaryProductInspectionService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-10-15  14:40
 * @Description:
 */

@RestController
@RequestMapping("/militaryInspection")
@Tag(name = "军品检验", description = "/militaryInspection")
@RequiredArgsConstructor
@Validated
public class MilitaryProductInspectionController extends BaseController {


    private final MilitaryProductInspectionProcessService militaryProductInspectionProcessService;

    private final MilitaryProductInspectionService militaryProductInspectionService;

    private final MilitaryInspectionDetailsService militaryInspectionDetailsService;

    private final FactoryInspectionDetailsService factoryInspectionDetailsService;

    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    @PostMapping("/launch")
    @PreAuthorize(hasPermission = "crm_flow_military_product_inspection",dataScope = "crm_flow_military_product_inspection")
    @Operation(summary = "发起军品检验流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody MilitaryInspectionFlowLaunchDTO launchDTO) {
        return new JsonObject<>(militaryProductInspectionProcessService.launch(launchDTO));
    }

    @PostMapping("/launchAndReturnProcessInstanceId")
    @PreAuthorize(hasPermission = "crm_flow_military_product_inspection",dataScope = "crm_flow_military_product_inspection")
    @Operation(summary = "发起军品检验流程并返回id")
    public JsonObject<String> launchAndReturnProcessInstanceId(@Valid @RequestBody MilitaryInspectionFlowLaunchDTO launchDTO) {
        return new JsonObject<>(militaryProductInspectionProcessService.launchAndReturnProcessInstanceId(launchDTO));
    }

//    @GetMapping("/checkIfInspectionProcessInitiatedWithNoEnd")
//    @Operation(summary = "检查军品检验流程是否已经发起且未结束")
//    @PreAuthorize(hasPermission = "crm_project_directly")
//    JsonObject<Boolean> checkIfInspectionProcessInitiatedWithNoEnd(@RequestParam String projectId){
//        Boolean objEntity = Optional.ofNullable(remoteProjectDirectlyClient.hasRight(projectId,getCurrentPersonId())).orElseThrow(() -> new CrmException(ResultEnum.NULL_OBJ_ENTITY)).getObjEntity();
//        if (!objEntity) {
//            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
//        }
//        return new JsonObject<>(militaryProductInspectionService.checkIfInspectionProcessInitiatedWithNoEnd(projectId));
//    }


    @PostMapping("/update")
    @Operation(summary = "军品检验修改")
    @PreFlowPermission(hasAnyNodes = {"military_02A"})
    public JsonObject<Boolean> update(@RequestBody MilitaryInspectionDTO militaryInspectionDTO) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.notNull(militaryInspectionDTO.getBaseInfo().getId(), "军品检验id不能为空");
        return new JsonObject<>(militaryProductInspectionService.update(militaryInspectionDTO));
    }

    @GetMapping("/militaryInspectionDetailInfo")
    @PreFlowPermission
    @Operation(summary = "军品检验详情")
    JsonObject<MilitaryProductInspectionVO> militaryInspectionDetailInfo(@RequestParam String processInstanceId){
        processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        return new JsonObject<>(militaryProductInspectionService.militaryInspectionDetailInfo(processInstanceId));
    }



    @PostMapping("/updateMilitaryInspectionDetail")
    @Operation(summary = "军检产品修改")
    @PreFlowPermission(hasAnyNodes = {"military_10"})
    public JsonObject<Boolean> updateMilitaryInspectionDetail(@RequestBody MilitaryInspectionDetailsVO militaryInspectionDetailsVO) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        CrmAssert.notNull(militaryInspectionDetailsVO.getId(), "军检产品id不能为空");
        MilitaryInspectionDetails militaryInspectionDetails = HyperBeanUtils.copyPropertiesByJackson(militaryInspectionDetailsVO, MilitaryInspectionDetails.class);
        return new JsonObject<>(militaryInspectionDetailsService.updateMilitaryInspectionDetail(militaryInspectionDetails));
    }


    @PostMapping("/updateFactoryInspectionDetail")
    @PreFlowPermission(hasAnyNodes = {"military_06"})
    @Operation(summary = "厂检产品修改")
    public JsonObject<Boolean> updateFactoryInspectionDetail(@RequestBody FactoryInspectionDetailsVO factoryInspectionDetailsVO) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        CrmAssert.notNull(factoryInspectionDetailsVO.getId(), "厂检产品id不能为空");
        FactoryInspectionDetails factoryInspectionDetails = HyperBeanUtils.copyPropertiesByJackson(factoryInspectionDetailsVO, FactoryInspectionDetails.class);
        return new JsonObject<>(factoryInspectionDetailsService.updateFactoryInspectionDetail(factoryInspectionDetails));
    }


    @GetMapping("/checkMilitaryInspectionAnyIncomplete")
    @PreFlowPermission
    @Operation(summary = "检验军检信息是否存在未编辑完成")
    JsonObject<Boolean> checkMilitaryInspectionAnyIncomplete(@RequestParam String processInstanceId){
        processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        return new JsonObject<>(militaryInspectionDetailsService.checkMilitaryInspectionAnyIncomplete(processInstanceId));
    }

    @GetMapping("/checkFactoryInspectionAnyIncomplete")
    @PreFlowPermission
    @Operation(summary = "检验厂检信息是否存在未编辑完成")
    JsonObject<Boolean> checkFactoryInspectionAnyIncomplete(@RequestParam String processInstanceId){
        processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        return new JsonObject<>(factoryInspectionDetailsService.checkFactoryInspectionAnyIncomplete(processInstanceId));
    }


    @PostMapping("/definedMilitaryInspectionTime")
    @PreFlowPermission(hasAnyNodes = {"military_07"})
    @Operation(summary = "确定军检时间")
    JsonObject<Boolean> definedMilitaryInspectionTime(@RequestBody DefinedMilitaryInspectionTimeDTO definedMilitaryInspectionTimeDTO){
        CrmAssert.notNull(definedMilitaryInspectionTimeDTO.getProcessInstanceId(), "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(definedMilitaryInspectionTimeDTO.getProcessInstanceId(),HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        CrmAssert.notNull(definedMilitaryInspectionTimeDTO.getDefinedMilitaryInspectionTime(), "确定军检时间不能为空");
        return new JsonObject<>(militaryProductInspectionService.definedMilitaryInspectionTime(definedMilitaryInspectionTimeDTO));
    }

    @GetMapping("/existsMilitaryProductInspectionProcess")
    @PreFlowPermission
    @Operation(summary = "是否存在军品检验流程")
    JsonObject<Boolean> existsMilitaryProductInspectionProcess(@RequestParam String projectId){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        return new JsonObject<>(militaryProductInspectionService.existsMilitaryProductInspectionProcess(projectId));
    }

    @GetMapping("/MilitaryInspectInitiatedProduct")
    @PreAuthorize(hasPermission = "crm_flow_military_product_inspection",dataScope = "crm_flow_military_product_inspection")
    @Operation(summary = "已发起的军品检验产品信息")
    JsonObject<List<MilitaryInspectionProduct>> MilitaryInspectInitiatedProduct(@RequestParam String projectId){
        return new JsonObject<>(militaryProductInspectionService.MilitaryInspectInitiatedProduct(projectId));
    }

}