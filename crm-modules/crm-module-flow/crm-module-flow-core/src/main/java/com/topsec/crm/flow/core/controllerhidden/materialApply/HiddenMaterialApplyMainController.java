package com.topsec.crm.flow.core.controllerhidden.materialApply;

import cn.hutool.core.collection.CollectionUtil;
import com.topsec.crm.flow.api.RemoteSalesAgreementProductService;
import com.topsec.crm.flow.api.dto.materialApply.ExistDisableProductVo;
import com.topsec.crm.flow.api.dto.materialApply.MaterialApplyMainVo;
import com.topsec.crm.flow.api.dto.materialApply.MaterialApplyProductDTO;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementProductVo;
import com.topsec.crm.flow.core.entity.MaterialApplyMain;
import com.topsec.crm.flow.core.entity.MaterialApplyProduct;
import com.topsec.crm.flow.core.service.IMaterialApplyMainService;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.product.api.RemoteMaterialControlService;
import com.topsec.crm.product.api.RemoteMaterialDisableService;
import com.topsec.crm.product.api.entity.CrmMaterialControlVo;
import com.topsec.crm.product.api.entity.CrmMaterialDisableVo;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/hidden/materialApplyMain")
@Tag(name = "代码权限申请", description = "/materialApplyMain")
@RequiredArgsConstructor
public class HiddenMaterialApplyMainController extends BaseController {
    @Autowired
    private IMaterialApplyMainService materialApplyMainService;
    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    @Autowired
    private RemoteMaterialDisableService remoteMaterialDisableService;
    @Autowired
    private RemoteMaterialControlService remoteMaterialControlService;
    @Autowired
    private RemoteSalesAgreementProductService remoteSalesAgreementProductService;

    /*
        查询项目/协议中 是否存在 产品禁用代码（排除掉流程审批通过且在生效时间之内的代码）
        不支持专项备货禁用查询
     */
    @PostMapping("/existDisableProduct")
    @Operation(summary = "查询项目/协议中 是否存在 产品禁用代码（排除掉流程审批通过且在生效时间之内的代码）")
    public JsonObject<ExistDisableProductVo> existDisableProduct(@RequestBody MaterialApplyMainVo materialApplyMainVo) {
        ExistDisableProductVo result = new ExistDisableProductVo();

        List<String> proProductIds = new ArrayList<String>();

        //如果有自定义传入的产品ID列表，则优先按照自定义产品列表查询规则来
        if (CollectionUtil.isNotEmpty(materialApplyMainVo.getProductIds())) {
            proProductIds = materialApplyMainVo.getProductIds();
        } else {
            if (materialApplyMainVo.getSourceType() == 1 || materialApplyMainVo.getSourceType() == 3 || materialApplyMainVo.getSourceType() == 4) {
                //来源于项目
                JsonObject<List<CrmProjectProductOwnVO>> listJsonObject = remoteProjectDirectlyClient.tiledProductOwnOfDirectlyProject(materialApplyMainVo.getProOrAgreementId());
                if (listJsonObject.isSuccess()) {
                    List<CrmProjectProductOwnVO> products = listJsonObject.getObjEntity();
                    //1.项目中产品Id列表
                    proProductIds = products.stream().map(CrmProjectProductOwnVO::getProductId).toList();
                }
            } else if (materialApplyMainVo.getSourceType() == 2) {
                JsonObject<List<SalesAgreementProductVo>> jObj = remoteSalesAgreementProductService.querySalesAgreementProductByAgreementId(materialApplyMainVo.getProOrAgreementId());
                if (jObj.isSuccess() && CollectionUtil.isNotEmpty(jObj.getObjEntity())) {
                    List<SalesAgreementProductVo> products = jObj.getObjEntity();
                    proProductIds = products.stream().map(SalesAgreementProductVo::getProductId).toList();
                }
            }
        }

        if (CollectionUtil.isEmpty(proProductIds)) {
            result.setDisable(Collections.EMPTY_LIST);
            result.setControl(Collections.EMPTY_LIST);
            return new JsonObject<>(result);
        }

        //2.查询该项目或者协议已经申请解禁，并且在生效时间之内的产品ID列表
        List<String> okProductIds = materialApplyMainService.selectUnDisableProducts(HyperBeanUtils.copyProperties(materialApplyMainVo, MaterialApplyMain::new));

        //3.查询禁用代码列表中，除了okProductIds，存不存在其他生效时间内的禁用产品
        CrmMaterialDisableVo disableParams = new CrmMaterialDisableVo();
        disableParams.setProductIds(proProductIds);
        disableParams.setPersonId(materialApplyMainVo.getPersonId());//流程发起人ID
        disableParams.setSourceType(materialApplyMainVo.getSourceType());
        disableParams.setIndustryOne(materialApplyMainVo.getIndustryOne());
        disableParams.setIndustryTwo(materialApplyMainVo.getIndustryTwo());
        disableParams.setSourceType(materialApplyMainVo.getSourceType());
        disableParams.setProOrAgreementId(materialApplyMainVo.getProOrAgreementId());
        //3.2排除流程解禁的产品ID
        disableParams.setOkProductIds(okProductIds);
        JsonObject<List<CrmMaterialDisableVo>> existDisableProducts = remoteMaterialDisableService.findExistDisableProducts(disableParams);
        result.setDisable(HyperBeanUtils.copyListPropertiesByJackson(existDisableProducts.getObjEntity(), com.topsec.crm.flow.api.vo.CrmMaterialDisableVo.class));

        //默认是查询
        JsonObject<List<CrmMaterialDisableVo>> existControlProducts = new JsonObject<>();
        if (materialApplyMainVo.getSearchControlFlag() == null || materialApplyMainVo.getSearchControlFlag() == true) {
            //4.查询控制代码列表中，除了okProductIds，存不存在其他生效时间内的禁用产品
            CrmMaterialControlVo controlParams = new CrmMaterialControlVo();
            controlParams.setProductIds(proProductIds);
            controlParams.setPersonId(materialApplyMainVo.getPersonId());//流程发起人ID
            controlParams.setSourceType(materialApplyMainVo.getSourceType());
            disableParams.setIndustryOne(materialApplyMainVo.getIndustryOne());
            disableParams.setIndustryTwo(materialApplyMainVo.getIndustryTwo());
            controlParams.setProOrAgreementId(materialApplyMainVo.getProOrAgreementId());
            //4.2排除流程控制的产品ID
            controlParams.setOkProductIds(okProductIds);
            existControlProducts = remoteMaterialControlService.findExistControlProducts(controlParams);
            result.setControl(HyperBeanUtils.copyListPropertiesByJackson(existControlProducts.getObjEntity(), com.topsec.crm.flow.api.vo.CrmMaterialDisableVo.class));
        }

        //5.查询产品最近一次审批通过的申请记录，不区分产品禁用和产品控制。
        if (CollectionUtil.isEmpty(existDisableProducts.getObjEntity()) && CollectionUtil.isEmpty(existControlProducts.getObjEntity())) {
            if (CollectionUtil.isNotEmpty(okProductIds)) {
                List<MaterialApplyProduct> lastProductFlow = new ArrayList<MaterialApplyProduct>();
                for (String okProductId : okProductIds) {
                    //查询已经申请解禁，的产品列表，所在的流程-所对应的产品申请信息
                    MaterialApplyProduct materialApplyProduct = materialApplyMainService.findProductInFlow(okProductId, materialApplyMainVo.getProOrAgreementId());
                    lastProductFlow.add(materialApplyProduct);
                }
                result.setLastProductFlow(HyperBeanUtils.copyListPropertiesByJackson(lastProductFlow, MaterialApplyProductDTO.class));
            }
        }

        return new JsonObject<>(result);
    }

    //自有产品选择时 查询产品控制的产品信息，需要排除掉不在范围之内和流程解禁生效期内的产品
    @PostMapping("/selectProductNoControl")
    @Operation(summary = "自有产品选择时 查询产品控制的产品信息，需要排除掉不在范围之内和流程解禁生效期内的产品")
    @Cacheable(cacheNames = "crm:control:controlList",key = "#materialApplyMainVo.proOrAgreementId+#materialApplyMainVo.personId")
    public JsonObject<ExistDisableProductVo> selectProductNoControl(@RequestBody MaterialApplyMainVo materialApplyMainVo){
        ExistDisableProductVo result = new ExistDisableProductVo();

        System.out.println("======================>start1-1");

        //1.查询已经申请解禁，并且在生效时间之内的产品ID列表
        List<String> okProductIds = new ArrayList<String>();
        if(StringUtils.isNotBlank(materialApplyMainVo.getProOrAgreementId())) {
            okProductIds = materialApplyMainService.selectUnDisableProducts(HyperBeanUtils.copyProperties(materialApplyMainVo, MaterialApplyMain::new));
        }

        //2.查询禁用代码列表中，除了okProductIds，存不存在其他生效时间内的禁用产品
        CrmMaterialControlVo controlParams = new CrmMaterialControlVo();
        controlParams.setOkProductIds(okProductIds);
        controlParams.setPersonId(materialApplyMainVo.getPersonId());//流程发起人ID
        controlParams.setSourceType(materialApplyMainVo.getSourceType());
        controlParams.setProOrAgreementId(materialApplyMainVo.getProOrAgreementId());
        controlParams.setIndustryOne(materialApplyMainVo.getIndustryOne());
        controlParams.setIndustryTwo(materialApplyMainVo.getIndustryTwo());
        JsonObject<List<CrmMaterialDisableVo>> existControlProducts = remoteMaterialControlService.findExistControlProducts(controlParams);
        result.setControl(HyperBeanUtils.copyListPropertiesByJackson(existControlProducts.getObjEntity(),com.topsec.crm.flow.api.vo.CrmMaterialDisableVo.class));

        System.out.println("======================>start1-2");

        return new JsonObject<>(result);
    }
}
