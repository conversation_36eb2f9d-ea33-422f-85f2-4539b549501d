package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.contract.api.entity.invoice.InvoiceProductDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.MakeInvoiceProductDTO;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

@Mapper
public interface InvoiceProductConvertor {

    InvoiceProductConvertor INSTANCE = Mappers.getMapper(InvoiceProductConvertor.class);

    @Mapping(source = "id",target = "invoiceProductId")
    @Mapping(target = "id",ignore = true)
    @Mapping(source = "notInvoicedAmount",target = "unInvoicedAmount")
    MakeInvoiceProductDTO toMakeInvoiceProduct(InvoiceProductDTO invoiceProductDTO);

    List<MakeInvoiceProductDTO> toMakeInvoiceProductList(List<InvoiceProductDTO> invoiceProductDTO);

    @Mapping(source = "id", target = "invoiceProductId")
    @Mapping(target = "id",ignore = true)
    @Mapping(source = "notInvoicedAmount",target = "unInvoicedAmount")
    MakeInvoiceProductDTO pageToMakeInvoiceProduct(Map<String, Object> map);

    @IterableMapping(elementTargetType = MakeInvoiceProductDTO.class)
    List<MakeInvoiceProductDTO> pageToMakeInvoiceProductList(List<Map<String, Object>> list);

    default String mapObjectToString(Object obj) {
        return obj == null ? null : obj.toString();
    }
}
