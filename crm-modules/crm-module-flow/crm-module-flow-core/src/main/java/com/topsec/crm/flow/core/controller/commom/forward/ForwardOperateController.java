package com.topsec.crm.flow.core.controller.commom.forward;

import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.operation.api.RemoteContractReviewConfigService;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractSignCompanyVO;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/forward/dict")
@Tag(name = "转发渠道项目Controller", description = "/forward")
@RequiredArgsConstructor
@Validated
public class ForwardOperateController extends BaseController {

    private final RemoteContractReviewConfigService remoteContractReviewConfigService;

    @PreAuthorize
    @PreFlowPermission
    @PostMapping("/contractReviewConfig/signCompanyPage")
    JsonObject<TableDataInfo>  signCompanyPage(@RequestBody ContractSignCompanyVO contractSignCompanyVO) {
        return remoteContractReviewConfigService.signCompanyPage(contractSignCompanyVO);
    }

}
