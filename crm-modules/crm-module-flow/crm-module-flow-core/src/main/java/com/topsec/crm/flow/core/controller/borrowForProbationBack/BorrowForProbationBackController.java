package com.topsec.crm.flow.core.controller.borrowForProbationBack;

import com.topsec.crm.flow.api.dto.borrowForProbationBack.BorrowForProbationBackDeviceVO;
import com.topsec.crm.flow.api.dto.borrowForProbationBack.BorrowForProbationBackHandingDTO;
import com.topsec.crm.flow.api.dto.borrowForProbationBack.BorrowForProbationBackVO;
import com.topsec.crm.flow.api.dto.borrowForProbationBack.ProbationBackServiceFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.process.impl.BorrowForProbationBackProcessService;
import com.topsec.crm.flow.core.service.BorrowForProbationBackService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-07-27  10:34
 * @Description:
 */
@RestController
@RequestMapping("/borrowForProbationBack")
@Tag(name = "借试用还回", description = "/borrowForProbationBack")
@RequiredArgsConstructor
@Validated
public class BorrowForProbationBackController extends BaseController {

    private final BorrowForProbationBackService borrowForProbationBackService;

    private final BorrowForProbationBackProcessService borrowForProbationBackProcessService;


    @PostMapping("/launch")
    @Operation(summary = "发起借试用还回流程")
    @PreAuthorize(hasPermission = "crm_device_probation_return",dataScope = "crm_device_probation_return")
    public JsonObject<Boolean> launch(@Valid @RequestBody ProbationBackServiceFlowLaunchDTO launchDTO) {
        return new JsonObject<>(borrowForProbationBackProcessService.launch(launchDTO));
    }

    @PostMapping("/launchAndReturnProcessInstanceId")
    @Operation(summary = "发起借试用还回流程并返回流程id")
    @PreAuthorize(hasPermission = "crm_device_probation_return",dataScope = "crm_device_probation_return")
    public JsonObject<String> launchAndReturnProcessInstanceId(@Valid @RequestBody ProbationBackServiceFlowLaunchDTO launchDTO) {
        return new JsonObject<>(borrowForProbationBackProcessService.launchAndReturnProcessInstanceId(launchDTO));
    }



    @GetMapping("/borrowForBackDetails/{processInstanceId}")
    @PreFlowPermission
    @Operation(summary = "还回详情")
    public JsonObject<PageUtils<BorrowForProbationBackDeviceVO>> borrowForBackDetails(@PathVariable String processInstanceId) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId,headerValue);
        return new JsonObject<PageUtils<BorrowForProbationBackDeviceVO>>(borrowForProbationBackService.borrowForBackDetails(processInstanceId));
    }


    @GetMapping("/borrowForBackInfo/{processInstanceId}")
    @PreFlowPermission
    @Operation(summary = "还回信息")
    public JsonObject<BorrowForProbationBackVO> borrowForBackInfo(@PathVariable String processInstanceId) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId,headerValue);
        return new JsonObject<BorrowForProbationBackVO>(borrowForProbationBackService.borrowForBackInfo(processInstanceId));
    }

    @PostMapping("/fillMaterialHandling")
    @PreFlowPermission(hasAnyNodes = {"sid-39C4D45E-4FA3-4DA4-945B-D314542A024F"})
    @Operation(summary = "填写物料搬运单")
    public JsonObject<Boolean> fillMaterialHandling(@RequestBody BorrowForProbationBackHandingDTO handingDTO) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.notNull(processInstanceId, "流程id不能为空");
        return new JsonObject<>(borrowForProbationBackService.fillMaterialHandling(handingDTO)) ;
    }



}
