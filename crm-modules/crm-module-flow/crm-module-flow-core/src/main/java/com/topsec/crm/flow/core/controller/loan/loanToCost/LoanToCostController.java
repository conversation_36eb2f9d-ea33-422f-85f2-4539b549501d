package com.topsec.crm.flow.core.controller.loan.loanToCost;

import com.topsec.crm.flow.api.dto.loan.loanApply.LoanApplyDTO;
import com.topsec.crm.flow.api.dto.loan.loanToCost.LoanToCostFlowBaseInfoLaunchDTO;
import com.topsec.crm.flow.api.dto.loan.loanToCost.LoanToCostFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.process.impl.LoanToCostProcessService;
import com.topsec.crm.flow.core.service.LoanApplyService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/loanToCost")
@Tag(name = "【借款转费用-流程】", description = "loanToCost")
@RequiredArgsConstructor
@Validated
public class LoanToCostController extends BaseController {

    @Resource
    private LoanToCostProcessService loanToCostProcessService;
    @Resource
    private LoanApplyService loanApplyService;

    @PostMapping("/launch")
    @Operation(summary = "发起借款转费用流程")
    @PreAuthorize(hasPermission = "crm_loan")
    public JsonObject<Boolean> launch(@RequestBody LoanToCostFlowLaunchDTO launchDTO){
        return new JsonObject<>(loanToCostProcessService.launch(launchDTO));
    }

    @PostMapping("/upLoanToCostInfo")
    @Operation(summary = "修改借款转费用流程")
    @PreFlowPermission(hasAnyNodes = {"sid-800005FC-79C2-445D-AE76-1A9EF31B5D44"})
    public JsonObject<Boolean> upLoanToCostInfo(@RequestBody LoanToCostFlowBaseInfoLaunchDTO launchDTO){

        return new JsonObject<>(loanApplyService.upLoanToCostInfo(launchDTO));
    }

    @GetMapping("/getLoanToCostInfo")
    @Operation(summary = "查询借款转费用流程")
    @PreAuthorize(hasPermission = "crm_loan_to_cost")
    public JsonObject<LoanApplyDTO> getLoanToCostInfo(@RequestParam String processInstanceId){
        return new JsonObject<>(loanApplyService.queryByProcessInstanceId(processInstanceId));
    }

}
