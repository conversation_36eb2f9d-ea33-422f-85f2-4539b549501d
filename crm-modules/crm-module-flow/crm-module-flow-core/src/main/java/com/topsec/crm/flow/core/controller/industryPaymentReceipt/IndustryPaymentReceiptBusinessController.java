package com.topsec.crm.flow.core.controller.industryPaymentReceipt;

import com.topsec.crm.flow.api.dto.industryPaymentReceipt.IndustryPaymentReceiptVO;
import com.topsec.crm.flow.core.service.IndustryPaymentReceiptService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.SetUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/industryPaymentReceipt")
@Tag(name = "行业回款-业务", description = "行业回款-业务")
@RequiredArgsConstructor
public class IndustryPaymentReceiptBusinessController {

    private final IndustryPaymentReceiptService industryPaymentReceiptService;


    @GetMapping("/detail")
    @Operation(summary = "行业回款详情")
    @PreAuthorize(hasPermission = "crm_industry_repayment",dataScope = "crm_industry_repayment")
    public JsonObject<IndustryPaymentReceiptVO> detail(@RequestParam String processInstanceId) {
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        Set<String> personIdList = SetUtils.emptyIfNull(dataScopeParam.getPersonIdList());
        IndustryPaymentReceiptVO industryPaymentReceiptVO = industryPaymentReceiptService.queryDetailByProcessInstanceId(processInstanceId);
        NameUtils.setName(industryPaymentReceiptVO);
        String createUser = industryPaymentReceiptVO.getCreateUser();
        if (!dataScopeParam.getAllScope() && !personIdList.contains(createUser)) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(industryPaymentReceiptVO);
    }

}
