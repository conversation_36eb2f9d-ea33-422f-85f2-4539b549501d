package com.topsec.crm.flow.core.controllerhidden.performancecreport;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportProductOwnDTO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportSelloutVO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportVO;
import com.topsec.crm.flow.core.entity.PerformanceReport;
import com.topsec.crm.flow.core.mapper.PerformanceReportMapper;
import com.topsec.crm.flow.core.service.PerformanceReportProductOwnService;
import com.topsec.crm.flow.core.service.PerformanceReportService;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.project.api.client.RemoteProjectProductSnClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/hidden/performanceReport")
@Tag(name = "业绩上报")
@RequiredArgsConstructor
public class HiddenPerformanceReportController extends BaseController {

    private final PerformanceReportService performanceReportService;
    private final PerformanceReportMapper performanceReportMapper;

    private final PerformanceReportProductOwnService performanceReportProductOwnService;
    private final RemoteProjectProductSnClient remoteProjectProductSnClient;

    @GetMapping("/getContractDeliveryVOList")
    @Operation(summary = "根据业绩上报主表查询流程发货信息")
    public JsonObject<List<PerformanceReportContractDeliveryDTO>> getContractDeliveryVOList(@RequestParam String performanceReportId){
        String processInstanceId = Optional.ofNullable(performanceReportService.getById(performanceReportId))
                .map(PerformanceReport::getProcessInstanceId)
                .orElseThrow(()-> new CrmException("业绩上报对应信息不存在"));
        List<PerformanceReportContractDeliveryDTO> contractDeliveryVOList = performanceReportService.getContractDeliveryVOList(processInstanceId);
        return new JsonObject<>(contractDeliveryVOList);
    }

    @GetMapping("/getPerformanceReportProcessNumberByIds")
    @Operation(summary = "根据业绩上报id获取业绩上报流程单号")
    public JsonObject<Map<String, String>> getPerformanceReportProcessNumberByIds(@RequestBody List<String> ids){
        return new JsonObject<>(performanceReportService.selectPerformanceReportProcessNumberByIds(ids));
    }

    @GetMapping("/getPerformanceReportSelloutVO")
    @Operation(summary = "根据业绩上报单号重新sellout数据")
    public JsonObject<PerformanceReportSelloutVO> getPerformanceReportSelloutVO(@RequestParam Integer lockType, @RequestParam String processNumber){
        return new JsonObject<>(performanceReportService.selectPerformanceReportSelloutVO(lockType,processNumber));
    }

    @GetMapping("/getPerformanceReportAvailableAdvancePayment")
    @Operation(summary = "查询业绩上报可用预付款")
    public JsonObject<BigDecimal> getPerformanceReportAvailableAdvancePayment(@RequestParam String channelCompanyId, @RequestParam String supplierId){
        BigDecimal availableAdvancePayment = performanceReportService.selectPerformanceReportAvailableAdvancePayment(channelCompanyId, supplierId);
        return new JsonObject<>(availableAdvancePayment);
    }

    @GetMapping("/selectProductOwnByProcessNumber")
    @Operation(summary = "根据业绩上报单号查询使用了返点的自有产品list")
    public JsonObject<List<PerformanceReportProductOwnDTO>> selectProductOwnByProcessNumber(@RequestParam String processNumber){
        return new JsonObject<>(performanceReportProductOwnService.selectProductOwnByProcessNumber(processNumber));
    }

    @GetMapping("/selectPerformanceReportByProcessNumber")
    @Operation(summary = "根据业绩上报单号查询业绩上报主表")
    public JsonObject<PerformanceReportVO> selectPerformanceReportByProcessNumber(@RequestParam String processNumber){
        PerformanceReport performanceReport = performanceReportService.getOne(new QueryWrapper<PerformanceReport>().eq("process_number", processNumber).last("limit 1"));
        return new JsonObject<>(HyperBeanUtils.copyProperties(performanceReport, PerformanceReportVO::new));
    }

    @GetMapping("/snInPerformanceReport")
    @Operation(summary = "查询序列号是否发起了业绩上报")
    public JsonObject<Boolean> snInPerformanceReport(@RequestParam String sn){
        return new JsonObject<>(performanceReportService.snInPerformanceReport(sn));
    }

    @GetMapping("/queryPerformanceReportBySaleId")
    @Operation(summary = "根据销售人员查询未办结的业绩上报")
    public JsonObject<List<String>> queryPerformanceReportBySaleId(@RequestParam String saleId){
        return new JsonObject<>(performanceReportService.queryPerformanceReportBySaleId(saleId));
    }
}
