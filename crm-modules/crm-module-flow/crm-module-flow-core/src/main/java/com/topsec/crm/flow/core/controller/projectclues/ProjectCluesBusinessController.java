package com.topsec.crm.flow.core.controller.projectclues;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.util.WebFilenameUtils;
import com.topsec.crm.flow.api.dto.projectclues.ProjectCluesExportDTO;
import com.topsec.crm.flow.api.dto.projectclues.ProjectCluesFlowLaunchDTO;
import com.topsec.crm.flow.core.entity.ProjectClues;
import com.topsec.crm.flow.core.service.IProjectCluesService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.bean.StatsResultVO;
import com.topsec.crm.framework.common.bean.StatsTimeSearchVO;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.enums.StatsTimeEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.project.api.dto.ProjectCluesPageQuery;

import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <p>
 * 项目线索信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@RestController
@RequestMapping("/business/projectClues")
@Tag(name = "项目线索商机业务接口", description = "/business/projectClues")
public class ProjectCluesBusinessController extends BaseController {

    @Autowired
    private IProjectCluesService projectCluesService;

    // JD管理员工号
    private static final String JD_MANGER_PERSON_ID = "df267417189546beaf5e63e1ca7166fd";

    // 非JD管理员工号
    private static final String UN_JD_MANGER_PERSON_ID = "572eae57356a49e6be5ce497349cac47";

    @PreAuthorize(hasPermission = "crm_project_clues",dataScope = "crm_project_clues")
    @PostMapping("/projectCluesPage")
    @Operation(summary = "分页查询项目线索数据-商机咨询")
    JsonObject<PageUtils<ProjectCluesFlowLaunchDTO>> projectCluesPage(@RequestBody ProjectCluesPageQuery projectCluesPageQuery) {
        if(JD_MANGER_PERSON_ID.equals(getCurrentPersonId())){
            projectCluesPageQuery.setJdManager(true);
            projectCluesPageQuery.setUnJdManager(false);
        }else if(UN_JD_MANGER_PERSON_ID.equals(getCurrentPersonId())){
            projectCluesPageQuery.setJdManager(false);
            projectCluesPageQuery.setUnJdManager(true);
        }else{
            projectCluesPageQuery.setJdManager(false);
            projectCluesPageQuery.setUnJdManager(false);
        }
        PageUtils<ProjectClues> projectCluesPage = projectCluesService.projectCluesPage(projectCluesPageQuery);
        List<ProjectCluesFlowLaunchDTO> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(projectCluesPage.getList())){
            list = HyperBeanUtils.copyListPropertiesByJackson(projectCluesPage.getList(), ProjectCluesFlowLaunchDTO.class);
        }
        return new JsonObject<>(new PageUtils<>(list, projectCluesPage.getTotalCount(), projectCluesPage.getPageSize(), projectCluesPage.getPageNum()));
    }

    @PreAuthorize(hasPermission = "crm_project_clues",dataScope = "crm_project_clues")
    @GetMapping("/detail/{processInstanceId}")
    @Operation(summary = "查看项目线索流程详情")
    public JsonObject<ProjectCluesFlowLaunchDTO> projectCluesDetail(@PathVariable String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        ProjectClues projectClues = projectCluesService.projectCluesDetail(processInstanceId);
        if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList()) || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(projectClues.getCreateUser()) || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(projectClues.getFollowUserId())){
            return new JsonObject<ProjectCluesFlowLaunchDTO>(HyperBeanUtils.copyPropertiesByJackson(projectClues, ProjectCluesFlowLaunchDTO.class));
        }else{
            return new JsonObject(false);
        }
    }

    @PreAuthorize(hasPermission = "crm_project_clues_export",dataScope = "crm_project_clues")
    @PostMapping("/exportProjectCluesInfo")
    @Operation(summary = "导出商机管理项目线索信息")
    public void exportProjectCluesInfo(@RequestBody ProjectCluesPageQuery projectCluesPageQuery, HttpServletResponse response)   {
        if(JD_MANGER_PERSON_ID.equals(getCurrentPersonId())){
            projectCluesPageQuery.setJdManager(true);
            projectCluesPageQuery.setUnJdManager(false);
        }else if(UN_JD_MANGER_PERSON_ID.equals(getCurrentPersonId())){
            projectCluesPageQuery.setJdManager(false);
            projectCluesPageQuery.setUnJdManager(true);
        }else{
            projectCluesPageQuery.setJdManager(false);
            projectCluesPageQuery.setUnJdManager(false);
        }
        projectCluesPageQuery.setPageNum(1);
        projectCluesPageQuery.setPageSize(Integer.MAX_VALUE);
        PageUtils<ProjectClues> projectCluesPage = projectCluesService.projectCluesPage(projectCluesPageQuery);
        List<ProjectCluesExportDTO> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(projectCluesPage.getList())){
            list = HyperBeanUtils.copyListPropertiesByJackson(projectCluesPage.getList(), ProjectCluesExportDTO.class);
            list.forEach(item->{
                Set<ApproveNode> approvalNode = item.getApprovalNode();
                if(CollectionUtils.isNotEmpty(approvalNode)){
                    StringBuilder approvalData = new StringBuilder();
                    approvalNode.forEach(node-> approvalData.append(node.getNodeName()).append(","));
                    item.setApprovalData(approvalData.substring(0,approvalData.length()-1));
                }else{
                    item.setApprovalData("流程已办结");
                }
                item.setEffective(null == item.getClueEffective() ? "":(item.getClueEffective() ? "是" : "否"));
                item.setFollowStatus(null == item.getFollow() ? "":(item.getFollow() ? "是" : "否"));
            });
            ExportParams exportParams=new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setSheetName("商机管理项目线索信息");
            Map<String,Object> sheet1Map = new HashMap<>();
            sheet1Map.put("title",exportParams);
            sheet1Map.put("data", list);
            sheet1Map.put("entity", ProjectCluesExportDTO.class);
            try (Workbook workbook = ExcelExportUtil.exportExcel(List.of(sheet1Map),ExcelType.XSSF)){
                response.addHeader(HttpHeaders.CONTENT_DISPOSITION, WebFilenameUtils.disposition("商机管理项目线索信息.xlsx"));
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
                workbook.write(response.getOutputStream());
            }catch (Exception e){
                throw new CrmException("导出失败", ResultEnum.FAIL.getResult(), e);
            }
        }
    }
}
