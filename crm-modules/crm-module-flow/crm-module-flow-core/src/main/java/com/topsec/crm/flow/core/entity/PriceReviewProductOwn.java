package com.topsec.crm.flow.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.topsec.crm.flow.api.dto.pricereview.ProductOwnServiceRangeVO;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnVO;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 价格评审流程-自有产品表快照
 * @TableName price_review_product_own
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="price_review_product_own",autoResultMap = true)
@Data
public class PriceReviewProductOwn extends BaseEntity implements Serializable {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 流程id
     */
    private String processInstanceId;
    //项目id
    private String projectId;

    //项目产品里的行记录id
    private String recordId;


    private String parentId;

    /**
     * 产品ID
     */
    private String productId;
    /**
     * 产品型号
     */
    private String productCategory;
    /**
     * 产品类型（0-产品，1-配件，2-服务，3-子服务）
     */
    private String productType;

    // PriceReviewProductOwn 和  PriceReviewComponentProductOwn 共有属性
    /**
     * 产品LIC数
     */
    private Integer productLic;

    private Long tempProductNum;
    /**
     * 产品数量
     */
    private Integer productNum;


    /**
     * 产品单价
     */
    private BigDecimal quotedPrice;

    /**
     * 产品总价
     */
    private BigDecimal quotedTotalPrice;

    /**
     * 产品规格
     */
    private String productSpecification;

    /**
     * 物料代码
     */
    private String stuffCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品pn
     */
    private String pnCode;

    /**
     * 宿主设备序列号
     */
    private String hostSerialNumber;

    /**
     * 成交单价
     */
    private BigDecimal dealPrice;

    /**
     * 成交总价
     */
    private BigDecimal dealTotalPrice;

    /**
     * 使用返点金额
     */
    private BigDecimal rebatePrice;

    /**
     * 最终用户单价
     */
    private BigDecimal finalPrice;

    /**
     * 最终用户总价
     */
    private BigDecimal finalTotalPrice;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 产品保修期(月)
     */
    private Integer productPeriod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分摊外包服务费
     */
    private BigDecimal splitOutsourcePrice;

    /**
     * 标记删除 0-未删除 1-已删除
     */
    private Integer delFlag;


    /**
     * 国代进价
     */
    private BigDecimal sellinPrice;

    /**
     * 产品属性 1-非分、2-分销
     */
    private Integer attr;

    /**
     * 毛利
     */
    private BigDecimal grossMargin;
    /**
     * 毛利率
     */
    private BigDecimal grossMarginRatio;

    /**
     * 产品的序列号
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<CrmProjectProductSnVO> productSn;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ProductOwnServiceRangeVO> serviceRange;

    @TableField(exist = false)
    private List<PriceReviewProductOwn> children;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}
