package com.topsec.crm.flow.core.controller.flow;

import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.query.CommentQuery;
import com.topsec.query.UnreadCommentQuery;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsCommentClient;
import com.topsec.tfs.api.client.TfsUnreadCommentClient;
import com.topsec.vo.TfsCommentVo;
import com.topsec.vo.comment.CommentMsg;
import com.topsec.vo.comment.TfsUnreadCommentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/comment")
@Tag(name = "流程评论回复Controller", description = "/comment")
@RequiredArgsConstructor
@Validated
public class FlowCommentController extends BaseController {

    private final TfsCommentClient tfsCommentClient;



    private final TfsUnreadCommentClient tfsUnreadCommentClient;






    @GetMapping("/queryProcessInstanceCommentList")
    @Operation(summary = "根据流程实例id查询评论区列表")
    @PreFlowPermission
    public JsonObject<List<TfsCommentVo>> queryProcessInstanceCommentList(String processInstanceId) {
        return tfsCommentClient.queryProcessInstanceCommentList(processInstanceId);
    }

    @PostMapping("/updateCommetRead")
    @Operation(summary = "评论消息更改为已读")
    public JsonObject<Void> updateCommetRead() {
        String currentPersonId = getCurrentPersonId();
        TfsUnreadCommentVo tssUnreadComment = TfsUnreadCommentVo.builder().personId(currentPersonId).isRead(1).build();
        return tfsUnreadCommentClient.updateCommentRead(tssUnreadComment);
    }


    @GetMapping("/queryUnreadMessageList")
    @Operation(summary = "查询未读评论消息列表")
    public JsonObject<List<CommentMsg>> queryUnreadMessageList(){
        String currentPersonId = getCurrentPersonId();
        return tfsCommentClient.findUnReadMsgList(currentPersonId);
    }


    @PostMapping("/queryReadMessageList")
    @Operation(summary = "查询审批已读消息列表")
    public JsonObject<PageUtils<CommentMsg>> queryReadMessageList(@RequestBody CommentQuery commentQuery){
        String currentPersonId = getCurrentPersonId();
        commentQuery.setPersonId(currentPersonId);
        commentQuery.setIsRead(1);
        return tfsCommentClient.findReadMsgList(commentQuery).convert(PageUtils::new);
    }


    @PostMapping("/updateBatchCommentRead")
    @Operation(summary = "批量将某个流程下的消息标记为已读")
    public JsonObject<Boolean> updateBatchCommentRead(@RequestBody  List<Integer> idList){
        CrmAssert.notEmpty(idList,"参数不能为空");
        UnreadCommentQuery unreadCommentQuery = UnreadCommentQuery.builder()
                .personId(getCurrentPersonId())
                .idList(idList).build();
        return tfsUnreadCommentClient.updateBatchCommentRead(unreadCommentQuery);
    }
    @PostMapping("/saveCommentAndReply")
    @Operation(summary = "流程新增评论或回复")
    @PreFlowPermission
    public JsonObject<Boolean> saveCommentAndReply( @RequestBody TfsCommentVo tfsCommentVo) {
        tfsCommentVo.setAccountId(UserInfoHolder.getCurrentAccountId());
        tfsCommentVo.setUserName(UserInfoHolder.getCurrentLoginName());
        tfsCommentVo.setPersonId(UserInfoHolder.getCurrentPersonId());
        return tfsCommentClient.saveComment(tfsCommentVo);
    }
}
