package com.topsec.crm.flow.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.topsec.crm.flow.core.entity.BackingMaterialDoc;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 支撑材料文件关系Mapper接口
 * @date 2024-04-25
 */
@Mapper
public interface BackingMaterialDocMapper extends BaseMapper<BackingMaterialDoc> {

    /**
     * 查询支撑资料文件
     * @param backingMaterialDoc 支撑材料文件
     * @return 支撑材料文件
     */
    List<BackingMaterialDoc> selectBackingMaterialDocList(BackingMaterialDoc backingMaterialDoc);

    /**
     * 查询支撑材料文件详细信息
     * @param materialId 支撑材料id
     * @return 支撑材料文件
     */
    List<BackingMaterialDoc> selectBackingMaterialDocById(String materialId);

    /**
     * 新增 支撑资料文件
     * @param backingMaterialDoc
     * @return
     */
    int insertBackingMaterialDoc(BackingMaterialDoc backingMaterialDoc);

    /**
     * 删除 支撑资料文件
     * @param id 支撑材料文件id
     * @return
     */
    int deleteBackingMaterialDoc(String id);

    int deleteBackingMaterialDocByMaterialId(String id);
}
