package com.topsec.crm.flow.core.controller.borrowForSellBack;


import com.topsec.crm.flow.api.dto.borrowForSellBack.BorrowForSellBackDeviceDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BorrowForSellBack;
import com.topsec.crm.flow.core.service.IBorrowForSellBackDeviceService;
import com.topsec.crm.flow.core.service.IBorrowForSellBackService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.seata.common.util.CollectionUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 借转销归还/丢失赔偿设备信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/borrowForSellBackDevice")
@Tag(name = "借转销归还/丢失赔偿设备信息", description = "/borrowForSellBackDevice")
public class BorrowForSellBackDeviceController extends BaseController {

    @Autowired
    private IBorrowForSellBackService borrowForSellBackService;

    @Autowired
    private IBorrowForSellBackDeviceService borrowForSellBackDeviceService;

    @PreFlowPermission
    @PostMapping("/updateBorrowForSellBackDeviceExpenses")
    @Operation(summary = "更新借转销归还/丢失赔偿设备费用信息")
    public JsonObject<Boolean> updateBorrowForSellBackDeviceExpenses(@RequestBody List<BorrowForSellBackDeviceDTO> deviceList) {
        if(CollectionUtils.isNotEmpty(deviceList)){
            long count = deviceList.stream().map(BorrowForSellBackDeviceDTO::getBorrowBackId).distinct().count();
            if (count == 1){
                String borrowBackId = deviceList.get(0).getBorrowBackId();
                BorrowForSellBack byId = borrowForSellBackService.getById(borrowBackId);
                if(null != byId){
                    PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
                    Boolean result =  borrowForSellBackDeviceService.updateBorrowForSellBackDeviceExpenses(deviceList);
                    return new JsonObject<Boolean>(result);
                }
            }
        }
        throw new CrmException(ResultEnum.AUTH_ERROR_500006);
    }
}

