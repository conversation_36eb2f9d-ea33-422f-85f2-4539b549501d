package com.topsec.crm.flow.core.controller.contractSignVerify;

import cn.hutool.core.collection.CollectionUtil;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.*;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewCustomerDTO;
import com.topsec.crm.flow.core.entity.ContractUnconfirmed;
import com.topsec.crm.flow.core.entity.ContractUnconfirmedUrge;
import com.topsec.crm.flow.core.process.impl.ContractUrgeSignVerifyProcessService;
import com.topsec.crm.flow.core.process.impl.ContractUrgeSignVerifySonProcessService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedUrgeService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.ContractEnum;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contractUnconfirmedUrge")
@Tag(name = "催交签验收单明细列表 相关Controller", description = "/contractUnconfirmedUrge")
@RequiredArgsConstructor
@Validated
public class ContractUnconfirmedUrgeController extends BaseController {
    @Autowired
    private IContractUnconfirmedService contractUnconfirmedService;
    @Autowired
    private IContractUnconfirmedUrgeService contractUnconfirmedUrgeService;
    @Autowired
    private RemoteContractExecuteService remoteContractExecuteService;
    @Autowired
    private ContractUrgeSignVerifyProcessService contractUrgeSignVerifyProcessService;
    @Autowired
    private ContractUrgeSignVerifySonProcessService contractUrgeSignVerifySonProcessService;

    /**
     * 催交签验收单明细列表
     */
    @PostMapping("/page")
    @Operation(summary = "催交签验收单明细列表")
    @PreAuthorize(hasPermission = "crm_contract_urge_sign_verify",dataScope = "crm_contract_urge_sign_verify")
    public JsonObject<PageUtils<ContractUnconfirmedUrgeVo>> page(@RequestBody ContractUnconfirmedUrgeVo contractUnconfirmedUrgeVo) {
        ContractUnconfirmedUrge urge = HyperBeanUtils.copyPropertiesByJackson(contractUnconfirmedUrgeVo,ContractUnconfirmedUrge.class);
        //数据权限过滤
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();

        startPage();
        List<ContractUnconfirmedUrge> list = contractUnconfirmedUrgeService.query()
                .in(CollectionUtil.isNotEmpty(dataScopeParam.getPersonIdList()), "contract_owner_id", dataScopeParam.getPersonIdList())
                .eq(StringUtils.isNotBlank(urge.getMaterialCode()), "material_code", urge.getMaterialCode())
                .eq(StringUtils.isNotBlank(urge.getContractNumber()), "contract_number", urge.getContractNumber())
                .eq(StringUtils.isNotNull(urge.getConfirmType()), "confirm_type", urge.getConfirmType())
                //如果是阶段性验收（2），需要查询阶段性验收报告
                .eq(StringUtils.isNotNull(urge.getConfirmType()) && urge.getConfirmType() == 2 && StringUtils.isNotNull(urge.getStage()), "stage", urge.getStage())
                .eq(StringUtils.isNotBlank(urge.getContractOwnerDeptId()), "contract_owner_dept_id", urge.getContractOwnerDeptId())
                .in(CollectionUtil.isNotEmpty(urge.getContractOwnerIds()), "contract_owner_id", urge.getContractOwnerIds())
                .orderByDesc("create_time").list();

        List<ContractUnconfirmedUrgeVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, ContractUnconfirmedUrgeVo.class);
        PageUtils dataTable = getDataTable(list,listVo);
        return new JsonObject<>(dataTable);
    }

    /**
     * 催交签验收单明细列表
     */
    @PostMapping("/export")
    @Operation(summary = "导出催交签验收单明细列表")
    @PreAuthorize(hasPermission = "crm_contract_urge_sign_verify_export",dataScope = "crm_contract_urge_sign_verify_export")
    public void export(@RequestBody ContractUnconfirmedUrgeVo contractUnconfirmedUrgeVo) throws Exception {
        ContractUnconfirmedUrge urge = HyperBeanUtils.copyPropertiesByJackson(contractUnconfirmedUrgeVo,ContractUnconfirmedUrge.class);
        //数据权限过滤
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();

        List<ContractUnconfirmedUrge> list = contractUnconfirmedUrgeService.query()
                .in(CollectionUtil.isNotEmpty(dataScopeParam.getPersonIdList()), "contract_owner_id", dataScopeParam.getPersonIdList())
                .eq(StringUtils.isNotBlank(urge.getMaterialCode()), "material_code", urge.getMaterialCode())
                .eq(StringUtils.isNotBlank(urge.getContractNumber()), "contract_number", urge.getContractNumber())
                .eq(StringUtils.isNotNull(urge.getConfirmType()), "confirm_type", urge.getConfirmType())
                //如果是阶段性验收（2），需要查询阶段性验收报告
                .eq(StringUtils.isNotNull(urge.getConfirmType()) && urge.getConfirmType() == 2 && StringUtils.isNotNull(urge.getStage()), "stage", urge.getStage())
                .eq(StringUtils.isNotBlank(urge.getContractOwnerDeptId()), "contract_owner_dept_id", urge.getContractOwnerDeptId())
                .in(CollectionUtil.isNotEmpty(urge.getContractOwnerIds()), "contract_owner_id", urge.getContractOwnerIds())
                .orderByDesc("create_time").list();

        List<ContractUnconfirmedUrgeVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, ContractUnconfirmedUrgeVo.class);
        for (ContractUnconfirmedUrgeVo urgeVo : listVo) {
            //设置收入确认条款
            String s = urgeVo.getConfirmType() == ContractEnum.CONFIRMTYPE.STOP.getCode() ? ContractEnum.CONFIRMTYPE.STOP.getValue()+"-"+ContractEnum.STAGE.getStage(urgeVo.getStage()) : ContractEnum.CONFIRMTYPE.getValue(urgeVo.getConfirmType());
            urgeVo.setConfirmTypeAndStage(s);
        }

        ExcelUtil<ContractUnconfirmedUrgeVo> excelUtil = new ExcelUtil<>(ContractUnconfirmedUrgeVo.class);
        excelUtil.exportExcel(response, listVo, "催交签验收单明细列表");
    }

}
