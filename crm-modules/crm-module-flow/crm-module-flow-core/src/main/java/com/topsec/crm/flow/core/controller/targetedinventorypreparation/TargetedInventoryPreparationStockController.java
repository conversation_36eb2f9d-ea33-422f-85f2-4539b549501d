package com.topsec.crm.flow.core.controller.targetedinventorypreparation;

import com.topsec.crm.flow.api.dto.targetedinventorypreparation.TIPStockQueryPageDTO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.TargetedInventoryPreparationStockVO;
import com.topsec.crm.flow.core.service.ITargetedInventoryPreparationStockService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 专项备货库存表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-01-15 15:13
 */
@RestController
@RequestMapping("/targeted-inventory-preparation-stock")
public class TargetedInventoryPreparationStockController extends BaseController {
    @Resource
    protected HttpServletResponse response;
    @Resource
    private ITargetedInventoryPreparationStockService iTargetedInventoryPreparationStockService;


    @PostMapping("/PageTIPStock")
    @Operation(summary = "备货库存明细分页",description = "专项备货")
    @PreAuthorize(hasPermission = "crm_special_stock_inventory_details",dataScope ="crm_special_stock_inventory_details" )
    public JsonObject<PageUtils<TargetedInventoryPreparationStockVO>> PageTIPStock(@RequestBody TIPStockQueryPageDTO tipStockQueryPageDTO){
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdsOfPermission = dataScopeParam != null ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;
        return new JsonObject<>(iTargetedInventoryPreparationStockService.TIPStockPage(tipStockQueryPageDTO,personIdsOfPermission));
    }

    @PostMapping("/exportTIPStock")
    @Operation(summary = "专项备货库存明细导出",description = "专项备货")
    @PreAuthorize(hasPermission = "crm_special_stock_inventory_details_export",dataScope ="crm_special_stock_inventory_details_export" )
    public void exportTIPStock(@RequestBody TIPStockQueryPageDTO tipStockQueryPageDTO) throws Exception{
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdsOfPermission = dataScopeParam != null ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;
        List<TargetedInventoryPreparationStockVO> list = iTargetedInventoryPreparationStockService.export(tipStockQueryPageDTO,personIdsOfPermission);
        ExcelUtil<TargetedInventoryPreparationStockVO> excelUtil = new ExcelUtil<>(TargetedInventoryPreparationStockVO.class);
        excelUtil.exportExcel(response, list,"专项备货库存明细");
    }
}
