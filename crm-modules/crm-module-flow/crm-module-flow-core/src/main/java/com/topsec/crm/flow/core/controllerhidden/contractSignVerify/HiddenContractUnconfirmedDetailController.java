package com.topsec.crm.flow.core.controllerhidden.contractSignVerify;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedDetailVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.ContractSignVerifyMain;
import com.topsec.crm.flow.core.entity.ContractUnconfirmed;
import com.topsec.crm.flow.core.entity.ContractUnconfirmedDetail;
import com.topsec.crm.flow.core.entity.ContractUnconfirmedDetailSnapshot;
import com.topsec.crm.flow.core.service.IContractSignVerifyMainService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedDetailService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedDetailSnapshotService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedService;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.tbsapi.client.TbsPersonClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/hidden/contractUnconfirmedDetail")
@RequiredArgsConstructor
@Validated
public class HiddenContractUnconfirmedDetailController extends BaseController {
    @Autowired
    private IContractUnconfirmedDetailService contractUnconfirmedDetailService;
    @Autowired
    private TbsPersonClient tbsPersonClient;

    /**
     * 收入确认核定列表记录
     *
     * 合同纬度，只传contractNumber
     * 未确认明细纬度，传入contractNumber和unconfirmedId
     */
    @PostMapping("/selectPage")
    public JsonObject<List<ContractUnconfirmedDetailVo>> selectPage(@RequestBody ContractUnconfirmedDetailVo contractUnconfirmedDetailVo) {
        //查询收入确认核定当前记录
        List<ContractUnconfirmedDetail> details = contractUnconfirmedDetailService.query()
                .eq("contract_number", contractUnconfirmedDetailVo.getContractNumber())
                .eq("del_flag", 0)
                .list();
        List<ContractUnconfirmedDetailVo> vos = HyperBeanUtils.copyListPropertiesByJackson(details, ContractUnconfirmedDetailVo.class);

        List<String> userIds = details.stream().map(ContractUnconfirmedDetail::getCreateUser).toList();
        JsonObject<List<PersonVO>> jsonObject = tbsPersonClient.listByIds(userIds.toArray(new String[]{}));
        if (jsonObject.isSuccess() && jsonObject.getObjEntity() != null){
            for (ContractUnconfirmedDetailVo vo : vos) {
                PersonVO personVO = jsonObject.getObjEntity().stream().filter(e -> e.getUuid().equals(vo.getCreateUser())).findFirst().orElse(null);
                if(personVO != null){
                    vo.setCreateUserName(personVO.getName());
                }
            }
        }

        return new JsonObject<>(vos);
    }

    /**
     * 插入收入确认明细（提供给退换货使用）
     */
    @PostMapping("/batchSaveReturnDetail")
    public JsonObject<Boolean> batchSaveReturnDetail(@RequestBody List<ContractUnconfirmedDetailVo> detailVos) {
        List<ContractUnconfirmedDetail> details = HyperBeanUtils.copyListPropertiesByJackson(detailVos, ContractUnconfirmedDetail.class);
        contractUnconfirmedDetailService.saveBatch(details);
        return new JsonObject<>(true);
    }

    /**
     * 根据退换货ID删除收入确认明细（提供给退换货使用）
     */
    @PostMapping("/deleteReturnDetail")
    public JsonObject<Boolean> deleteReturnDetail(@RequestParam String returnExchangeProcessInstanceId) {
        contractUnconfirmedDetailService.update(new LambdaUpdateWrapper<ContractUnconfirmedDetail>()
                .set(ContractUnconfirmedDetail::getDelFlag, 1)
                .eq(ContractUnconfirmedDetail::getReturnExchangeProcessInstanceId, returnExchangeProcessInstanceId));
        return new JsonObject<>(true);
    }

}
