package com.topsec.crm.flow.core.controller.borrowForSell;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.util.WebFilenameUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.api.dto.borrowForSell.*;
import com.topsec.crm.flow.core.entity.BorrowForSell;
import com.topsec.crm.flow.core.service.BorrowForSellRevokeService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.date.DateUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.TfsFormContentVo;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <p>
 * 借转销信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/business/borrowForSell")
@Tag(name = "借转销业务接口", description = "/business/borrowForSell")
public class BorrowForSellBusinessController extends BaseController {

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    @Autowired
    private BorrowForSellRevokeService borrowForSellRevokeService;

    @Autowired
    private TfsFormContentClient tfsformContentClient;

    @PreAuthorize(hasAnyPermission = {"crm_flow_borrow_for_sell_details","crm_flow_borrow_for_sell_gd_details"})
    @GetMapping("/directly/{projectId}")
    @Operation(summary = "根据项目ID获取项目信息")
    JsonObject<CrmProjectDirectlyVo> getProjectInfo(@PathVariable String projectId,  String processInstanceId) {
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(projectId.equals(tfsFormContentVo.getProjectId())){
                return remoteProjectDirectlyClient.getProjectInfo(projectId);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_details", dataScope = "crm_flow_borrow_for_sell_details")
    @GetMapping("/detail/{processInstanceId}")
    @Operation(summary = "查看借转销流程详情")
    public JsonObject<BorrowForSellFlowLaunchDTO> borrowForSellDetail(@PathVariable String processInstanceId) {
        BorrowForSell one = borrowForSellService.getOne(new LambdaQueryWrapper<BorrowForSell>().eq(BorrowForSell::getProcessInstanceId, processInstanceId));
        if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList())
                || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(one.getSalesmanPersonId())
                || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(one.getCreateUser())){
            return new JsonObject<BorrowForSellFlowLaunchDTO>(borrowForSellService.borrowForSellDetail(processInstanceId));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_gd_details", dataScope = "crm_flow_borrow_for_sell_gd_details")
    @GetMapping("/detailOfAgent/{processInstanceId}")
    @Operation(summary = "查看借转销流程详情(国代视角)")
    public JsonObject<BorrowForSellFlowLaunchDTO> borrowForSellDetailOfAgent(@PathVariable String processInstanceId) {
        BorrowForSell one = borrowForSellService.getOne(new LambdaQueryWrapper<BorrowForSell>().eq(BorrowForSell::getProcessInstanceId, processInstanceId));
        if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList())
                || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(one.getSalesmanPersonId())
                || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(one.getCreateUser())){
            return new JsonObject<BorrowForSellFlowLaunchDTO>(borrowForSellService.borrowForSellDetailOfAgent(processInstanceId));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasAnyPermission = {"crm_flow_borrow_for_sell_return_compensate"/*,"crm_flow_borrow_for_sell_revoke","crm_flow_borrow_for_sell_clean"*/})
    @GetMapping("/list")
    @Operation(summary = "查询当前登录用户的借转销流程基本信息")
    public JsonObject<List<BorrowForSellFlowLaunchDTO>> borrowForSellList(@RequestParam(required = false) Integer processState) {
        List<BorrowForSell> borrowForSells = borrowForSellService.list(new LambdaQueryWrapper<BorrowForSell>().eq(Objects.nonNull(processState),BorrowForSell::getProcessState, processState).eq(BorrowForSell::getSalesmanPersonId, getCurrentPersonId()));
        if(CollectionUtils.isNotEmpty(borrowForSells)){
            borrowForSells.forEach(borrowForSell -> borrowForSell.setSalesman(NameUtils.getName(borrowForSell.getSalesmanPersonId())));
            List<BorrowForSellFlowLaunchDTO> borrowForSellFlowLaunchDTOS = HyperBeanUtils.copyListPropertiesByJackson(borrowForSells, BorrowForSellFlowLaunchDTO.class);
            return new JsonObject<List<BorrowForSellFlowLaunchDTO>>(borrowForSellFlowLaunchDTOS);
        }else{
            return new JsonObject<List<BorrowForSellFlowLaunchDTO>>(new ArrayList<>());
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_details",dataScope = "crm_flow_borrow_for_sell_details")
    @GetMapping("/updateReceiveDate")
    @Operation(summary = "修改货运签收时间")
    public JsonObject<Boolean> updateReceiveDate(@RequestParam String borrowId,@RequestParam String productRecordId, @RequestParam String receiveDate) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList())
                || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(byId.getSalesmanPersonId())
                || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(byId.getCreateUser())){
            return new JsonObject<Boolean>(borrowForSellService.updateReceiveDate(borrowId, productRecordId, DateUtil.strToLocalDate(receiveDate)));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_execute",dataScope = "crm_flow_borrow_for_sell_execute")
    @PostMapping("/executionPage")
    @Operation(summary = "分页查询借转销执行")
    public JsonObject<PageUtils<BorrowForSellExecutionVO>> executionPage(@RequestBody BorrowForSellExecutionPageQuery borrowForSellExecutionPageQuery) {
        return new JsonObject<>(borrowForSellService.executionPage(borrowForSellExecutionPageQuery));
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_deliver",dataScope = "crm_flow_borrow_for_sell_deliver")
    @PostMapping("/deliveryPage")
    @Operation(summary = "分页查询借转销发货信息")
    public JsonObject<PageUtils<BorrowForSellDeliveryVO>> deliveryPage(@RequestBody BorrowForSellDeliveryPageQuery borrowForSellDeliveryPageQuery) {
        return new JsonObject<>(borrowForSellService.deliveryPage(borrowForSellDeliveryPageQuery));
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_details",dataScope = "crm_flow_borrow_for_sell_details")
    @PostMapping("/detailPage")
    @Operation(summary = "分页查询借转销明细信息")
    public JsonObject<PageUtils<BorrowForSellDetailVO>> detailPage(@RequestBody BorrowForSellDetailPageQuery borrowForSellDetailPageQuery) {
        borrowForSellDetailPageQuery.setAgentView(false);
        return new JsonObject<>(borrowForSellService.detailPage(borrowForSellDetailPageQuery));
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_gd_details",dataScope = "crm_flow_borrow_for_sell_gd_details")
    @PostMapping("/detailPageOfAgent")
    @Operation(summary = "分页查询借转销明细信息(国代视角)")
    public JsonObject<PageUtils<BorrowForSellDetailVO>> detailPageOfAgent(@RequestBody BorrowForSellDetailPageQuery borrowForSellDetailPageQuery) {
        borrowForSellDetailPageQuery.setAgentView(true);
        return new JsonObject<>(borrowForSellService.detailPage(borrowForSellDetailPageQuery));
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_execute_export",dataScope = "crm_flow_borrow_for_sell_execute")
    @PostMapping("/exportExecutionData")
    @Operation(summary = "导出借转销执行信息")
    public void exportExecutionData(@RequestBody BorrowForSellExecutionPageQuery borrowForSellExecutionPageQuery, HttpServletResponse response)   {
        borrowForSellExecutionPageQuery.setPageNum(1);
        borrowForSellExecutionPageQuery.setPageSize(Integer.MAX_VALUE);
        PageUtils<BorrowForSellExecutionVO> borrowForSellExecutionVOPage = borrowForSellService.executionPage(borrowForSellExecutionPageQuery);
        List<BorrowForSellExecutionVO> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(borrowForSellExecutionVOPage.getList())){
            list = borrowForSellExecutionVOPage.getList();
            list.forEach(item->{
                Set<ApproveNode> approvalNode = item.getApprovalNode();
                if(CollectionUtils.isNotEmpty(approvalNode)){
                    StringBuilder approvalData = new StringBuilder();
                    approvalNode.forEach(node-> approvalData.append(node.getNodeName()).append(","));
                    item.setApprovalData(approvalData.substring(0,approvalData.length()-1));
                }else{
                    item.setApprovalData("流程已办结");
                }
                item.setSigningTypeExport(null == item.getSigningType() ? "":(item.getSigningType() == 1 ? "是" : "否"));
                item.setSmTypeExport(null == item.getSmType() ? "":(item.getSmType() == 1 ? "是" : "否"));
                item.setAgentShipmentExport(null == item.getAgentShipment() ? "":(item.getAgentShipment() == 1 ? "是" : "否"));
                item.setReceiptFormExport(null == item.getReceiptForm() ? "":(item.getReceiptForm() ? "已传" : ""));
                item.setOriginalContractExport(null == item.getOriginalContract() ? "":(item.getOriginalContract() ? "已传" : ""));
                item.setInStockExport(null == item.getInStock() ? "":(item.getInStock() ? "已传" : ""));
            });
            ExportParams exportParams=new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setSheetName("借转销执行信息");
            Map<String,Object> sheet1Map = new HashMap<>();
            sheet1Map.put("title",exportParams);
            sheet1Map.put("data", list);
            sheet1Map.put("entity", BorrowForSellExecutionVO.class);
            try (Workbook workbook = ExcelExportUtil.exportExcel(List.of(sheet1Map),ExcelType.XSSF)){
                response.addHeader(HttpHeaders.CONTENT_DISPOSITION, WebFilenameUtils.disposition("借转销执行信息.xlsx"));
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
                workbook.write(response.getOutputStream());
            }catch (Exception e){
                throw new CrmException("导出失败", ResultEnum.FAIL.getResult(), e);
            }
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_deliver_export",dataScope = "crm_flow_borrow_for_sell_deliver")
    @PostMapping("/exportDeliveryData")
    @Operation(summary = "导出借转销发货信息")
    public void exportDeliveryData(@RequestBody BorrowForSellDeliveryPageQuery borrowForSellDeliveryPageQuery, HttpServletResponse response)   {
        borrowForSellDeliveryPageQuery.setPageNum(1);
        borrowForSellDeliveryPageQuery.setPageSize(Integer.MAX_VALUE);
        PageUtils<BorrowForSellDeliveryVO> borrowForSellDeliveryVOPage = borrowForSellService.deliveryPage(borrowForSellDeliveryPageQuery);
        List<BorrowForSellDeliveryVO> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(borrowForSellDeliveryVOPage.getList())){
            list = borrowForSellDeliveryVOPage.getList();
            list.forEach(item->{
                item.setSigningTypeExport(null == item.getSigningType() ? "":(item.getSigningType() == 1 ? "是" : "否"));
                item.setSmTypeExport(null == item.getSmType() ? "":(item.getSmType() == 1 ? "是" : "否"));
                item.setAgentShipmentExport(null == item.getAgentShipment() ? "":(item.getAgentShipment() == 1 ? "是" : "否"));
                item.setReceiptFormExport(null == item.getReceiptForm() ? "":(item.getReceiptForm() ? "已传" : ""));
                item.setOriginalContractExport(null == item.getOriginalContract() ? "":(item.getOriginalContract() ? "已传" : ""));
            });
            ExportParams exportParams=new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setSheetName("借转销发货信息");
            Map<String,Object> sheet1Map = new HashMap<>();
            sheet1Map.put("title",exportParams);
            sheet1Map.put("data", list);
            sheet1Map.put("entity", BorrowForSellDeliveryVO.class);
            try (Workbook workbook = ExcelExportUtil.exportExcel(List.of(sheet1Map),ExcelType.XSSF)){
                response.addHeader(HttpHeaders.CONTENT_DISPOSITION, WebFilenameUtils.disposition("借转销发货信息.xlsx"));
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
                workbook.write(response.getOutputStream());
            }catch (Exception e){
                throw new CrmException("导出失败", ResultEnum.FAIL.getResult(), e);
            }
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell")
    @PostMapping("/checkBorrowForSellProduct")
    @Operation(summary = "校验借转销产品信息")
    public JsonObject<Boolean> checkBorrowForSellProduct(@RequestBody BorrowForSellFlowLaunchDTO launchDTO) {
        return new JsonObject<>(borrowForSellService.checkBorrowForSellProduct(launchDTO));
    }
}

