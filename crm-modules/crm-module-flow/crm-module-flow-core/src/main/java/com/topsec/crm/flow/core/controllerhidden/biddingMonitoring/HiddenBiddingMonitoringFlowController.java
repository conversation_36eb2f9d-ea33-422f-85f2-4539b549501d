package com.topsec.crm.flow.core.controllerhidden.biddingMonitoring;

import com.topsec.crm.flow.api.dto.biddingMonitoring.VO.BiddingMonitoringInfoListVO;
import com.topsec.crm.flow.api.dto.biddingMonitoring.VO.BiddingMonitoringInfoVO;
import com.topsec.crm.flow.api.dto.biddingMonitoring.VO.BiddingMonitoringPageQuery;
import com.topsec.crm.flow.core.entity.BiddingMonitoringInfo;
import com.topsec.crm.flow.core.service.BiddingMonitoringFlowService;
import com.topsec.crm.flow.core.service.BiddingMonitoringInfoService;
import com.topsec.crm.framework.common.bean.StatsPersonTimeSearchVO;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.starter.redis.util.DistributedNumberGenerator;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.utils.UUIDUtils;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-12-26  15:13
 * @Description:
 */

@RestController
@RequestMapping("/hidden/biddingMonitoring")
@Tag(name = "招投标检测信息-不对外公开")
@RequiredArgsConstructor
public class HiddenBiddingMonitoringFlowController extends BaseController {

    @Resource
    private BiddingMonitoringInfoService biddingMonitoringInfoService;
    @Resource
    private BiddingMonitoringFlowService biddingMonitoringFlowService;

    @Resource
    private DistributedNumberGenerator distributedNumberGenerator;

    @PostMapping("/insert")
    @Operation(summary = "招投标信息监测新增")
    public JsonObject<Boolean> insert(@Valid @RequestBody BiddingMonitoringInfoVO biddingMonitoringInfoVo) {
        BiddingMonitoringInfo biddingMonitoringInfo = HyperBeanUtils.copyPropertiesByJackson(biddingMonitoringInfoVo, BiddingMonitoringInfo.class);
        biddingMonitoringInfo.setId(UUIDUtils.generateUUID());
        biddingMonitoringInfo.setBiddingMonitoringInfoNumber(distributedNumberGenerator.selfIncrNumberGenerate("ZTBJC" + DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now())));
        return new JsonObject<>(biddingMonitoringInfoService.save(biddingMonitoringInfo));
    }

    @PutMapping("/updateByInfoId")
    @Operation(summary = "招投标信息监测根据infoId修改")
    public JsonObject<Boolean> updateByInfoId(@Valid @RequestBody BiddingMonitoringInfoVO biddingMonitoringInfoVo) {
        BiddingMonitoringInfo biddingMonitoringInfo = HyperBeanUtils.copyPropertiesByJackson(biddingMonitoringInfoVo, BiddingMonitoringInfo.class);
        boolean update = biddingMonitoringInfoService.lambdaUpdate().eq(BiddingMonitoringInfo::getInfoId, biddingMonitoringInfo.getInfoId()).eq(BiddingMonitoringInfo::getDelFlag,0).update(biddingMonitoringInfo);
        return new JsonObject<>(update);
    }

    @GetMapping("/getByInfoId")
    @Operation(summary = "招投标信息监测根据infoId查询")
    public JsonObject<BiddingMonitoringInfoVO> getByInfoId(@Valid @RequestBody BiddingMonitoringInfoVO biddingMonitoringInfoVo) {
        BiddingMonitoringInfo one = biddingMonitoringInfoService.lambdaQuery().eq(BiddingMonitoringInfo::getInfoId, biddingMonitoringInfoVo.getInfoId()).eq(BiddingMonitoringInfo::getDelFlag,0).one();
        BiddingMonitoringInfoVO biddingMonitoringInfoVO = HyperBeanUtils.copyPropertiesByJackson(one, BiddingMonitoringInfoVO.class);
        return new JsonObject<>(biddingMonitoringInfoVO);
    }
    @GetMapping("/getCurrentCursorMark")
    @Operation(summary = "招投标信息监测根据infoId查询")
    public JsonObject<String> getCurrentCursorMark() {
        BiddingMonitoringInfo biddingMonitoringInfo = biddingMonitoringInfoService.lambdaQuery().eq(BiddingMonitoringInfo::getDelFlag,0).last("ORDER BY create_time DESC").last("LIMIT 1").one();
        if (biddingMonitoringInfo != null) {
            biddingMonitoringInfo.getCursorMark();
        }
        return new JsonObject<>(null);
    }

    @GetMapping("/getAllBiddingInfos")
    @Operation(summary = "获取所有已发起流程的招投标信息")
    JsonObject<List<BiddingMonitoringInfoListVO>> getAllBiddingInfos(){
        BiddingMonitoringPageQuery biddingMonitoringPageQuery = new BiddingMonitoringPageQuery();
        biddingMonitoringPageQuery.setExistProcess(true);
        PageUtils<BiddingMonitoringInfoListVO> biddingMonitoringInfoListVOPageUtils = biddingMonitoringFlowService.biddingMonitoringPage(biddingMonitoringPageQuery);
        return new JsonObject<>(biddingMonitoringInfoListVOPageUtils.getList());
    };

    @PostMapping("/countBiddingMonitoring")
    @Operation(summary = "获取人员跟进的招投标检测数量")
    JsonObject<Integer> countBiddingMonitoring(@RequestBody @Validated StatsPersonTimeSearchVO statsTimeSearchVO){
        return new JsonObject<>(biddingMonitoringFlowService.countBiddingMonitoring(statsTimeSearchVO));
    }


}
