package com.topsec.crm.flow.core.controller.customer.business;

import com.topsec.crm.flow.api.dto.customer.CustomerNaSalemanVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CustomerNa;
import com.topsec.crm.flow.core.entity.CustomerNaMain;
import com.topsec.crm.flow.core.entity.CustomerNaSaleman;
import com.topsec.crm.flow.core.entity.ProjectReportMain;
import com.topsec.crm.flow.core.service.ICustomerNaMainService;
import com.topsec.crm.flow.core.service.ICustomerNaSalemanService;
import com.topsec.crm.flow.core.service.ICustomerNaService;
import com.topsec.crm.flow.core.service.IProjectReportMainService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.project.api.client.RemoteProjectAgentClient;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.entity.CrmProjectAgentVo;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.constant.TbsConstants;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/business")
@RequiredArgsConstructor
@Validated
public class BusinessCustomerNaController extends BaseController {

    private final ICustomerNaMainService customerNaMainService;
    private final ICustomerNaSalemanService customerNaSalemanService;

    //部门负责人查看详情
    @PostMapping("/customerNaMain/findAllSalemanId")
    @Operation(summary = "F10.部门负责人查看详情")
    @PreAuthorize(hasAnyPermission = {"crm_customer_dept_na_apply","crm_customer_admin_na_apply_list"}, dataScope = "crm_customer_dept_na_apply")
    public JsonObject<List<String>> findAllSalemanId(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        CustomerNaMain cn = customerNaMainService.query().eq("process_instance_id", processInstanceId).one();

        //数据范围权限校验
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        if(dataScopeParam.getPersonIdList() != null && !dataScopeParam.getPersonIdList().contains("-1") && !dataScopeParam.getPersonIdList().contains(cn.getApplyUserId())){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        List<CustomerNaSaleman> cnss = customerNaSalemanService.query().eq("process_instance_id", processInstanceId).list();
        return new JsonObject<>(cnss.stream().map(CustomerNaSaleman::getSalemanId).collect(Collectors.toList()));
    }

    @Operation(summary = "F4. 01 02 03 -NA客户申请列表")
    @PostMapping(value = "/customerNa/naCustomerApplyPage")
    @PreAuthorize(hasAnyPermission = {"crm_customer_dept_na_apply","crm_customer_admin_na_apply_list"})
    public JsonObject<PageUtils<CustomerNaSalemanVo>> naCustomerApplyPage(@RequestBody CustomerNaSalemanVo customerNaSalemanVo)
    {
        PreFlowPermissionAspect.checkProcessInstanceId(customerNaSalemanVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        startPage();
        List<CustomerNaSaleman> list = customerNaSalemanService.naCustomerApplyPage(HyperBeanUtils.copyProperties(customerNaSalemanVo, CustomerNaSaleman::new));
        List<CustomerNaSalemanVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, CustomerNaSalemanVo.class);
        //前端渲染表头
        CustomerNaSalemanVo cnsv = listVo.get(0) != null ? listVo.get(0) : new CustomerNaSalemanVo();
        cnsv.setMountYears(Arrays.asList(LocalDateTime.now().plusYears(-2).getYear(),LocalDateTime.now().plusYears(-1).getYear(),LocalDateTime.now().getYear()));
        //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
        return new JsonObject<>(getDataTable(list,listVo));
    }

}
