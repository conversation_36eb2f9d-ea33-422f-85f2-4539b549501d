package com.topsec.crm.flow.core.controller.devicelease;


import com.topsec.crm.flow.api.dto.devicelease.DeviceLeaseFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.DeviceLease;
import com.topsec.crm.flow.core.process.impl.DeviceLeaseProcessService;
import com.topsec.crm.flow.core.service.DeviceLeaseService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.entity.CrmDeviceLeaseVO;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * <p>
 * 租赁信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@RestController
@RequestMapping("/deviceLease")
@Tag(name = "设备租赁流程", description = "/deviceLease")
public class DeviceLeaseController extends BaseController {

    @Autowired
    private DeviceLeaseService deviceLeaseService;

    @Autowired
    private DeviceLeaseProcessService deviceLeaseProcessService;

    @PreAuthorize(hasPermission = "crm_flow_device_lease")
    @PostMapping("/launch")
    @Operation(summary = "发起设备租赁流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody DeviceLeaseFlowLaunchDTO launchDTO) {
        if(getCurrentPersonId().equals(launchDTO.getPersonId())){
            return new JsonObject<>(deviceLeaseProcessService.launch(launchDTO));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @GetMapping("/flow/detail/{processInstanceId}")
    @Operation(summary = "查看租赁流程详情")
    public JsonObject<CrmDeviceLeaseVO> deviceLeaseDetail(@PathVariable String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        DeviceLease crmDeviceLease = deviceLeaseService.deviceLeaseDetail(processInstanceId);
        CrmDeviceLeaseVO crmDeviceLeaseVO = HyperBeanUtils.copyPropertiesByJackson(crmDeviceLease, CrmDeviceLeaseVO.class);
        return new JsonObject<CrmDeviceLeaseVO>(crmDeviceLeaseVO);
    }
}
