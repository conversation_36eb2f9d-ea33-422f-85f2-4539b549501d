package com.topsec.crm.flow.core.controller.projectReport.business;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.dto.projectRadio.ProjectRadioMainVo;
import com.topsec.crm.flow.api.dto.projectReport.ProjectReportFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.projectReport.ProjectReportMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ProjectRadioMain;
import com.topsec.crm.flow.core.entity.ProjectReportMain;
import com.topsec.crm.flow.core.process.impl.ProjectReportAgentProcessService;
import com.topsec.crm.flow.core.process.impl.ProjectReportCompanyProcessService;
import com.topsec.crm.flow.core.service.IProjectRadioMainService;
import com.topsec.crm.flow.core.service.IProjectReportMainService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ProjectMemberEnum;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.project.api.client.*;
import com.topsec.crm.project.api.dto.ProjectMemberQuery;
import com.topsec.crm.project.api.entity.*;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.jwt.UserInfoHolder;

import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbsapi.client.TbsCrmClient;
import com.topsec.tbsapi.client.TbsPersonClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.AccountAgentRelVO;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/business/projectReport")
@Tag(name = "项目报备", description = "/projectReport")
@RequiredArgsConstructor
public class BusinessProjectReportMainController extends BaseController {

    @Autowired
    private IProjectReportMainService projectReportMainService;
    @Autowired
    private ProjectReportCompanyProcessService projectReportCompanyProcessService;
    @Autowired
    private ProjectReportAgentProcessService projectReportAgentProcessService;
    @Autowired
    private IProjectRadioMainService projectRadioMainService;
    @Autowired
    private RemoteProjectProductOwnClient remoteProjectProductOwnClient;
    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    @Autowired
    private RemoteProjectAgentClient remoteProjectAgentClient;
    @Autowired
    private TosEmployeeClient tosEmployeeClient;
    @Autowired
    private RemoteCustomerService remoteCustomerService;
    @Autowired
    private RemoteProjectMemberClient remoteProjectMemberClient;
    @Autowired
    private RemoteAgentService remoteAgentService;
    @Autowired
    private TfsNodeClient tfsNodeClient;
    @Autowired
    private TbsCrmClient tbsCrmClient;
    @Autowired
    private TbsPersonClient tbsPersonClient;
    @Autowired
    private RemoteProjectSignAgentClient remoteProjectSignAgentClient;

    @GetMapping("/info")
    @Operation(summary = "项目报备详情信息")
    @PreAuthorize(hasPermission = "crm_project_report",dataScope = "crm_project_report")
    public JsonObject<ProjectReportMainVo> info(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        ProjectReportMain projectReportMain = projectReportMainService.query().eq("process_instance_id", processInstanceId).one();
        //数据范围校验
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        if(CollectionUtil.isNotEmpty(personIdList) && !personIdList.contains(projectReportMain.getCreateUser())){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        ProjectReportMainVo prmv = HyperBeanUtils.copyPropertiesByJackson(projectReportMain, ProjectReportMainVo.class);
        //补全信息
        //1.客户信息
        String customerId = "";
        if(prmv.getProjectType() == 1){
            //查询项目详细信息
            JsonObject<CrmProjectDirectlyVo> jObject = remoteProjectDirectlyClient.getProjectInfo(prmv.getProjectId());
            if(jObject.isSuccess()){
                CrmProjectDirectlyVo objEntity = jObject.getObjEntity();
                CrmProjectSigningInfoVo signingInfo = objEntity.getCrmProjectSigningInfo();
                customerId = signingInfo.getCustomerId();
            }
        }else if(prmv.getProjectType() == 2) {
            JsonObject<CrmProjectAgentVo> jObject = remoteProjectAgentClient.getInfo(prmv.getProjectId());
            if (jObject.isSuccess()) {
                customerId = jObject.getObjEntity().getFinalCustomId();
            }

            //查询销售人员名称
            JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(prmv.getSalemanId());
            if(byId.isSuccess()){
                prmv.setSalemanName(byId.getObjEntity() != null ? byId.getObjEntity().getName() : "");
            }
        }
        Assert.isFalse(StringUtils.isEmpty(customerId),"项目最终客户不存在，请联系管理员处理。");
        JsonObject<CrmCustomerVo> customerInfo = remoteCustomerService.getCustomerInfo(customerId, UserInfoHolder.getCurrentPersonId(),false);
        if(customerInfo.isSuccess() && customerInfo.getObjEntity() != null){
            CrmCustomerVo crmCustomerVo = customerInfo.getObjEntity();
            prmv.setCustomerProvinceCode(crmCustomerVo.getProvinceCode());
            prmv.setCustomerCityCode(crmCustomerVo.getCityCode());
            prmv.setCustomerCountyCode(crmCustomerVo.getCountyCode());
            prmv.setIsNa(crmCustomerVo.getIsNa());
            prmv.setNaSalemans(crmCustomerVo.getNaSalemans());
        }

        //2.查询项目报备次数
        List<ProjectReportMain> reports = projectReportMainService.query()
                .eq("project_id", prmv.getProjectId())
                .eq("del_flag", false)
                .orderByDesc("create_time")
                .list();

        //3.查询项目广播次数
        List<ProjectRadioMain> redios = projectRadioMainService.query()
                .eq("project_id", prmv.getProjectId())
                .eq("del_flag", false)
                .orderByDesc("create_time")
                .list();

        //4.查询用户信息
        List<String> createUserIDs = reports.stream().map(ProjectReportMain::getCreateUser).collect(Collectors.toList());
        createUserIDs.addAll(redios.stream().map(ProjectRadioMain::getCreateUser).collect(Collectors.toList()));
        createUserIDs.add(prmv.getCreateUser());

        JsonObject<List<PersonVO>> listJsonObject = tbsPersonClient.listByIds(createUserIDs.toArray(new String[]{}));
        if(listJsonObject.isSuccess()){
            List<PersonVO> byIds = listJsonObject.getObjEntity();

            for (ProjectReportMain report : reports) {
                //查询报备人信息
                List<PersonVO> collect = byIds.stream().filter(e -> e.getUuid().equals(report.getCreateUser())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(collect)){
                    PersonVO personVO = collect.get(0);
                    report.setCreateUserName(NameUtils.getNameByPersonVO(personVO));
                }
            }
            for (ProjectRadioMain redio : redios) {
                List<PersonVO> collect = byIds.stream().filter(e -> e.getUuid().equals(redio.getCreateUser())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(collect)) {
                    PersonVO personVO = collect.get(0);
                    redio.setCreateUserName(NameUtils.getNameByPersonVO(personVO));
                }
            }

            List<PersonVO> collect = byIds.stream().filter(e -> e.getUuid().equals(prmv.getCreateUser())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(collect)) {
                PersonVO personVO = collect.get(0);
                prmv.setCreateUserName(NameUtils.getNameByPersonVO(personVO));
                prmv.setDeptOrAgentName(personVO.getDepartmentVO() != null ? personVO.getDepartmentVO().getDeptName() : "");

                //如果是公司项目，需要查询创建人所在工作地
                if(projectReportMain.getProjectType() == 1) {
                    JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(personVO.getUuid());
                    if (byId.isSuccess()) {
                        EmployeeVO employeeVO = byId.getObjEntity();
                        prmv.setAddress(employeeVO.getWorkPlace());
                    }
                }
            }
        }

        //渠道名称
        String accountId = AccountAccquireUtils.getAccountIdByPersonId(prmv.getCreateUser());
        JsonObject<AccountAgentRelVO> accountAgentRelVOByAccountId = tbsCrmClient.getAccountAgentRelVOByAccountId(accountId);
        if(accountAgentRelVOByAccountId.isSuccess() && accountAgentRelVOByAccountId.getObjEntity() != null){
            String agentId = accountAgentRelVOByAccountId.getObjEntity().getAgentId();
            JsonObject<CrmAgentVo> agentInfo = remoteAgentService.getAgentInfo(agentId);
            if(agentInfo.isSuccess() && agentInfo.getObjEntity() != null) {
                prmv.setDeptOrAgentName(agentInfo.getObjEntity().getAgentName());

            }
        }
        prmv.setReports(HyperBeanUtils.copyListPropertiesByJackson(reports, ProjectReportMainVo.class));
        prmv.setRadios(HyperBeanUtils.copyListPropertiesByJackson(redios, ProjectRadioMainVo.class));

        return new JsonObject<>(prmv);
    }

}
