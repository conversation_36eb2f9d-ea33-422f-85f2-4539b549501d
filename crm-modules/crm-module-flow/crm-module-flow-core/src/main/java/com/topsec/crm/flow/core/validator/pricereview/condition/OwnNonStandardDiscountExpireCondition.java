package com.topsec.crm.flow.core.validator.pricereview.condition;

import cn.hutool.extra.spring.SpringUtil;
import com.topsec.crm.flow.core.entity.PriceReviewMain;
import com.topsec.crm.flow.core.entity.ProjectPriceReviewDetail;
import com.topsec.crm.flow.core.validator.CheckCondition;
import com.topsec.crm.flow.core.validator.pricereview.PriceReviewCheckContext;
import com.topsec.crm.flow.core.service.PriceReviewFlowService;

import java.time.LocalDateTime;
import java.util.Optional;


/**
 * 1.特价产品（非标准折扣的产品）超过审批失效时长，如果所有产品均为标准产品，则无失效时长；
 *
 * <AUTHOR>
 */
public class OwnNonStandardDiscountExpireCondition implements CheckCondition<PriceReviewCheckContext> {

    @Override
    public boolean check(PriceReviewCheckContext context)  {
        ProjectPriceReviewDetail projectSnapshot = context.getProjectSnapshot();

        PriceReviewMain priceReviewMain = projectSnapshot.getPriceReviewMain();
        Integer priceExpiryDays = Optional.ofNullable(priceReviewMain.getPriceExpiryDaysOfApproval()).orElse(priceReviewMain.getPriceExpiryDays());

        PriceReviewFlowService flowService = SpringUtil.getBean(PriceReviewFlowService.class);
        boolean allStandardDiscountProduct = flowService.allStandardDiscountProduct(priceReviewMain.getProcessInstanceId());
        // 存在非标准折扣产品
        if (!allStandardDiscountProduct){
            // 超时
            LocalDateTime approvalPassTime = projectSnapshot.getProcessExtensionInfo().getStateTime();
            return approvalPassTime.plusDays(priceExpiryDays).isAfter(LocalDateTime.now());
        }
        return true;

    }

    @Override
    public String defaultFailureReason() {
        return "特价产品（非标准折扣的产品）超过审批失效时长";
    }
}
