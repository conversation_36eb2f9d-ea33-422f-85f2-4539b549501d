package com.topsec.crm.flow.core.controllerhidden.contractreview;

import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.baseinfo.ContractBasicInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractReviewSpecialCodeDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractThirdUpdateDTO;
import com.topsec.crm.flow.api.dto.contractreview.response.ContractProcessInfo;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.RevenueRecognitionDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.ReviewRetentionMoneyDTO;
import com.topsec.crm.flow.core.entity.ContractReviewDelivery;
import com.topsec.crm.flow.core.entity.ContractReviewMain;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.project.api.entity.CrmProjectProductMaintainVO;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.enums.ResultEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * feign controller
 *
 * <AUTHOR>
 * @date 2024/8/16 14:45
 */
@RestController
@RequestMapping("/contractReviewFeign")
@Tag(name = "合同评审-不对外开放", description = "/contractReviewFeign")
@RequiredArgsConstructor
@Slf4j
public class HiddenContractReviewController {

    private final ContractReviewSpecialCodeService contractReviewSpecialCodeService;

    private final ContractReviewPaymentProvisionService paymentProvisionService;

    private final ContractReviewRevenueRecognitionService reviewRevenueRecognitionService;

    private final ContractReviewRetentionMoneyService retentionMoneyService;

    private final ContractReviewMainService mainService;

    private final ContractReviewFlowService flowService;

    private final ContractReviewProductOwnService ownService;

    private final ContractReviewProductThirdService thirdService;

    private final ContractReviewDeliveryService deliveryService;

    @PostMapping("/specialCodeInfo")
    @Operation(summary = "特殊代码确认")
    public JsonObject<Map<String, CrmProjectProductMaintainVO>> specialCodeInfo(@RequestBody List<String> productRecordIds, @RequestParam String projectId) {
        return new JsonObject<>(contractReviewSpecialCodeService.specialCodeInfo(productRecordIds, projectId));
    }

    @GetMapping("/contractBasicInfo")
    @Operation(summary = "合同基础信息")
    public JsonObject<ContractBasicInfoDTO> contractBasicInfo(@RequestParam String contractId) {
        return new JsonObject<>(mainService.contractBasicInfo(contractId, true));
    }

    @PostMapping("/queryStateBySnIndustry")
    @Operation(summary = "根据重点行业序列号查询序列号的状态 key:sn value:状态")
    public JsonObject<Map<String, Integer>> queryStateBySnIndustry(@RequestBody List<String> contractSn) {
        return new JsonObject<>(flowService.queryStateBySnIndustry(contractSn));
    }

    @GetMapping("/getContractInfoBySnIndustry")
    @Operation(summary = "根据重点行业序列号查询合同信息")
    public JsonObject<ContractReviewMainBaseInfoDTO> getContractInfoBySnIndustry(@RequestParam String contractSn) {
        return new JsonObject<>(flowService.getContractInfoBySnIndustry(contractSn));
    }

    @GetMapping("/productOwnInfoTileByContractId")
    @Operation(summary = "根据合同id取合同中的自有产品信息（平铺）")
    public JsonObject<List<ContractProductOwnDTO>> productOwnInfoTileByContractId(@RequestParam String contractId) {
        return new JsonObject<>(ownService.productOwnInfoTileByContractId(contractId, true));
    }

    @PostMapping("/updateThirdBatch")
    @Operation(summary = "批量更新第三方产品")
    public JsonObject<Boolean> updateThirdBatch(@RequestBody List<ContractThirdUpdateDTO> updateDTOS) {
        return new JsonObject<>(thirdService.updateThirdBatch(updateDTOS));
    }

    @GetMapping("/getProcessInfoByProcessInstanceIds")
    @Operation(summary = "根据流程实例id查询流程信息")
    public JsonObject<List<ContractProcessInfo>> getProcessInfoByProcessInstanceIds(@RequestParam List<String> processInstanceIds) {
        return new JsonObject<>(flowService.getProcessInfoByProcessInstanceIds(processInstanceIds));
    }

    @GetMapping("/getReturnPointProductByContractProcessNumber")
    @Operation(summary = "根据流程编号查询返点产品")
    public JsonObject<List<ContractProductOwnDTO>> getReturnPointProductByContractProcessNumber(@RequestParam String processNumber){
        return new JsonObject<>(flowService.getReturnPointProductByContractProcessNumber(processNumber));
    }

    @PostMapping("/saveOrUpdatePaymentProvisionBatch")
    @Operation(summary = "更新付款条款")
    public JsonObject<Boolean> saveOrUpdatePaymentProvisionBatch(@RequestBody List<PaymentProvisionDTO> paymentProvisionDTO) {
        try {
            return new JsonObject<>(paymentProvisionService.saveOrUpdateBatch(paymentProvisionDTO));
        } catch (Exception e) {
            log.error("更新付款条款", e);
            return new JsonObject<>(500, e.getMessage());
        }
    }

    @GetMapping("/getPaymentProvisionDTOByContractId")
    @Operation(summary = "根据合同id获取付款条款")
    public JsonObject<List<PaymentProvisionDTO>> getPaymentProvisionDTOByContractId(@RequestParam String contractId) {
        return new JsonObject<>(paymentProvisionService.getByContractId(contractId));
    }

    @GetMapping("/getRevenueRecognitionDTOByContractId")
    @Operation(summary = "根据合同id获取付款条款")
    public JsonObject<List<RevenueRecognitionDTO>> getRevenueRecognitionDTOByContractId(@RequestParam String contractId) {
        return new JsonObject<>(reviewRevenueRecognitionService.getByContractId(contractId));
    }

    @PostMapping("/saveOrUpdateRevenueRecognitionBatch")
    @Operation(summary = "更新收入确认条款")
    public JsonObject<Boolean> saveOrUpdateRevenueRecognitionBatch(@RequestBody List<RevenueRecognitionDTO> revenueRecognitionDTOS) {
        return new JsonObject<>(reviewRevenueRecognitionService.saveOrUpdateBatch(revenueRecognitionDTOS));
    }
    @PostMapping("/updateBaseInfoByProcessInstanceId")
    @Operation(summary = "更新合同评审相关信息")
    public JsonObject<Boolean> updateBaseInfoByProcessInstanceId(@RequestBody ContractReviewMainBaseInfoDTO baseInfoDTO) {
        return new JsonObject<>(mainService.changeBaseInfoByProcessInstanceId(baseInfoDTO));
    }

    @PostMapping("/updateRetentionMoney")
    @Operation(summary = "更新质保金条款信息")
    public JsonObject<Boolean> updateRetentionMoney(@RequestBody ReviewRetentionMoneyDTO baseInfoDTO, @RequestParam String processInstanceId) {
        if (processInstanceId == null) {
            throw new CrmException("流程实例id不能为空");
        }
        ContractReviewMain main = mainService.getByProcessInstanceId(processInstanceId);
        baseInfoDTO.setContractReviewMainId(main.getId());
        return new JsonObject<>(retentionMoneyService.saveOrUpdate(baseInfoDTO));
    }

    @GetMapping("/getDeliveryInfoListByProcessInstanceIds")
    @Operation(summary = "根据流程实例id查询发货信息")
    public JsonObject<List<ContractDeliveryDTO>> getDeliveryInfoListByProcessInstanceIds(@RequestParam List<String> processInstanceIds) {
        return new JsonObject<>(deliveryService.getDeliveryListByProcessInstanceIds(processInstanceIds));
    }

    @GetMapping("/getDeliveryDetailByDeliveryId")
    @Operation(summary = "根据发货id查询发货明细")
    public JsonObject<TableDataInfo> getDeliveryDetailByDeliveryId(@RequestParam String deliveryId) {
        return new JsonObject<>(deliveryService.getContractDeliveryProductPage(deliveryId));
    }

    @GetMapping("/isAllDelivery")
    @Operation(summary = "判断发货是否全部完成")
    public JsonObject<Boolean> isAllDelivery(@RequestParam String processInstanceId){
        ContractReviewMain main = mainService.getByProcessInstanceId(processInstanceId);
        return new JsonObject<>(deliveryService.isAllDelivery(main.getId()));
    }

    @GetMapping("/getContractNumberByDeliveryId")
    @Operation(summary = "根据发货id查询合同号")
    public JsonObject<String> getContractNumberByDeliveryId(@RequestParam String deliveryId) {
        ContractReviewDelivery delivery = deliveryService.getById(deliveryId);
        if (delivery == null) {
            return new JsonObject<>(ResultEnum.SUCCESS.getResult(), null);
        }
        String contractId = delivery.getContractId();
        if (StringUtils.isBlank(contractId)) {
            return new JsonObject<>(ResultEnum.SUCCESS.getResult(), null);
        }
        ContractReviewMain main = mainService.getById(contractId);
        if (main == null || main.getContractNumber() == null) {
            return new JsonObject<>(ResultEnum.SUCCESS.getResult(), null);
        }
        return new JsonObject<>(main.getContractNumber());
    }

    @GetMapping("/getContractReviewByProjectId")
    @Operation(summary = "根据项目id查询合同评审信息")
    public JsonObject<List<ContractReviewMainBaseInfoDTO>> getContractReviewByProjectId(@RequestParam String projectId) {
        return new JsonObject<>(mainService.getContractReviewByProjectId(projectId));
    }

    @GetMapping("/getContractReviewEndByProjectId")
    @Operation(summary = "根据项目id查询合同评审信息")
    public JsonObject<List<ContractReviewMainBaseInfoDTO>> getContractReviewEndByProjectId(@RequestParam String projectId) {
        return new JsonObject<>(mainService.getContractReviewEndByProjectId(projectId));
    }

    @GetMapping("/getSpecialCode")
    @Operation(summary = "根据产品id查特殊代码确认")
    public JsonObject<List<ContractReviewSpecialCodeDTO>> getSpecialCode(@RequestParam Set<String> contractRecordIds){
        return new JsonObject<>(contractReviewSpecialCodeService.getByProductOwnIdBatch(contractRecordIds));
    }

}
