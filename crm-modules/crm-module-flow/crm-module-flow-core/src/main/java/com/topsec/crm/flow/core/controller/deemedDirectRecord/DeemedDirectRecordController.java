package com.topsec.crm.flow.core.controller.deemedDirectRecord;

import com.topsec.crm.flow.api.dto.deemedDirectRecord.DeemedDirectRecordBaseInfoDTO;
import com.topsec.crm.flow.api.dto.deemedDirectRecord.DeemedDirectRecordFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.process.impl.DeemedDirectRecordProcessService;
import com.topsec.crm.flow.core.service.DeemedDirectRecordService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/deemedDirectRecord")
@Tag(name = "直签备案", description = "/deemedDirectRecord")
public class DeemedDirectRecordController  extends BaseController {

    @Resource
    private DeemedDirectRecordService deemedDirectRecordService;

    @Resource
    private DeemedDirectRecordProcessService deemedDirectRecordProcessService;

    @PostMapping("/launch")
    @Operation(summary = "提交直签备案流程")
    @PreAuthorize(hasPermission = "crm_flow_sign_company_filing")
    public JsonObject<Boolean> launch(@Valid @RequestBody DeemedDirectRecordFlowLaunchDTO deemedDirectRecordFlowLaunchDTO) {
        return new JsonObject<> (deemedDirectRecordProcessService.launch(deemedDirectRecordFlowLaunchDTO));
    }

    @GetMapping("/getLaunchInfo")
    @Operation(summary = "流程信息详情")
    @PreFlowPermission
    public JsonObject<DeemedDirectRecordBaseInfoDTO> getLaunchInfo(@RequestParam String processId){
        return new JsonObject<>(deemedDirectRecordService.getLaunchInfo(processId));
    }

    @GetMapping("/getLaunchInfoAll")
    @Operation(summary = "流程信息详情包含流程字段")
    @PreFlowPermission
    public JsonObject<DeemedDirectRecordFlowLaunchDTO> getLaunchInfoAll(@RequestParam String processId){
        return new JsonObject<>(deemedDirectRecordService.getLaunchInfoAll(processId));
    }

    @GetMapping("/IsExistByContractId")
    @Operation(summary = "通过合同评审id查询是否存在")
    public JsonObject<Boolean> IsExistByContractId(@RequestParam String contractId){
        return new JsonObject<>(deemedDirectRecordService.IsExistByContractId(contractId));
    }

    @GetMapping("/IsExistByCompanyName")
    @Operation(summary = "通过签约单位查询是否已审")
    public JsonObject<Boolean> IsExistByCompanyName(@RequestParam String companyName){
        return new JsonObject<>(deemedDirectRecordService.IsExistByCompanyName(companyName));
    }

}
