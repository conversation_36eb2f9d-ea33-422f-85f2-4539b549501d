package com.topsec.crm.flow.core.service.impl.deviceoccupy;

import com.topsec.crm.flow.api.dto.BorrowForProbationDeviceFlowVO;
import com.topsec.crm.flow.api.dto.BorrowForProbationDeviceOccupyStateVO;
import com.topsec.crm.flow.core.service.BorrowForProbationDeviceOccupy;
import com.topsec.crm.project.api.client.RemoteProjectProductOwnClient;
import com.topsec.crm.project.api.client.RemoteProjectProductSnClient;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnVO;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.tbscommon.JsonObject;
import io.seata.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @version V1.0
 * @Description: 项目占用设备
 * @ClassName: com.topsec.crm.flow.core.service.impl.deviceoccupy.ProjectUsedOccupy.java
 * @Copyright 天融信 - Powered By 企业软件研发中心
 * @author: leo
 * @date: 2024-10-21 16:22
 */
@Service
@RequiredArgsConstructor
public class ProjectUsedOccupy implements BorrowForProbationDeviceOccupy {

    private final RemoteProjectProductSnClient remoteProjectProductSnClient;

    private final RemoteProjectProductOwnClient remoteProjectProductOwnClient;

    /**
     * 查询借试用产品的占用状态
     *
     * @param deviceIds 借试用表的设备ID（主键）
     * @return key psn，value 占用状态
     */
    @Override
    public Map<String, BorrowForProbationDeviceOccupyStateVO> checkDeviceOccupyState(Set<String> deviceIds) {
        Map<String, BorrowForProbationDeviceOccupyStateVO> result = new HashMap<String, BorrowForProbationDeviceOccupyStateVO>();
        JsonObject<List<CrmProjectProductSnVO>> productSnByDeviceIds = remoteProjectProductSnClient.getProductSnByDeviceIds(deviceIds);
        if(productSnByDeviceIds.isSuccess()){
            List<CrmProjectProductSnVO> objEntity = productSnByDeviceIds.getObjEntity();
            if(CollectionUtils.isNotEmpty(objEntity)){
                List<String> list = objEntity.stream().map(CrmProjectProductSnVO::getRecordId).toList();
                JsonObject<List<CrmProjectProductOwnVO>> listJsonObject = remoteProjectProductOwnClient.queryProductOwnByProductRecordIds(list);
                for (String deviceId : deviceIds) {
                    Optional<CrmProjectProductSnVO> any = objEntity.stream().filter(item -> deviceId.equals(item.getDeviceId())).findAny();
                    if(any.isPresent()){
                        AtomicReference<String> projectId = new AtomicReference<>(null);
                        if (listJsonObject.isSuccess() && CollectionUtils.isNotEmpty(listJsonObject.getObjEntity())){
                            objEntity.stream().filter(item -> deviceId.equals(item.getDeviceId())).findFirst().ifPresent(crmProjectProductSnVO -> {
                                listJsonObject.getObjEntity().stream().filter(item -> crmProjectProductSnVO.getRecordId().equals(item.getId())).findFirst().ifPresent(crmProjectProductOwnVO -> {
                                    projectId.set(crmProjectProductOwnVO.getProjectId());
                                });
                            });
                        }
                        BorrowForProbationDeviceOccupyStateVO back = new BorrowForProbationDeviceOccupyStateVO(projectId.get(), deviceId,1,"项目绑定占用");
                        result.put(deviceId, back);
                    }else{
                        BorrowForProbationDeviceOccupyStateVO back = new BorrowForProbationDeviceOccupyStateVO(null,deviceId,0,null);
                        result.put(deviceId, back);
                    }
                }
            }else{
                for (String deviceId : deviceIds) {
                    BorrowForProbationDeviceOccupyStateVO back = new BorrowForProbationDeviceOccupyStateVO(null,deviceId,0,null);
                    result.put(deviceId, back);
                }
            }
        }
        return result;
    }

    /**
     * 查询借试用产品的占用状态
     *
     * @param deviceId 借试用表的设备ID（主键）
     * @return 借试用产品的占用状态
     */
    @Override
    public List<BorrowForProbationDeviceFlowVO> showBorrowForProbationDeviceFlow(String deviceId) {
        return null;
    }
}
