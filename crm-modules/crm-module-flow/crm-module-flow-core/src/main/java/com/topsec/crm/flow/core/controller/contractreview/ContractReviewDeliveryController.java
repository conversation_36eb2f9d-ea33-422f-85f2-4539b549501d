package com.topsec.crm.flow.core.controller.contractreview;


import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDetailDTO;
import com.topsec.crm.flow.core.service.ContractReviewDeliveryService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/contractDelivery")
@RequiredArgsConstructor
@Tag(name = "合同评审发货相关", description = "/contractDelivery")
public class ContractReviewDeliveryController extends BaseController{

}
