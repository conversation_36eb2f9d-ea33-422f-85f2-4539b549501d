package com.topsec.crm.flow.core.controller.contractreview.flowauth;

import cn.hutool.core.collection.CollectionUtil;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDetailDTO;
import com.topsec.crm.flow.api.dto.contractreview.fileinfo.ContractElectricContractDTO;
import com.topsec.crm.flow.api.dto.contractreview.fileinfo.ContractReviewAttachmentDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnSntVO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractReviewSpecialCodeDTO;
import com.topsec.crm.flow.api.dto.contractreview.request.SpecialCodePageQuery;
import com.topsec.crm.flow.api.dto.contractreview.response.ContractAttachmentStatus;
import com.topsec.crm.flow.api.dto.contractreview.salemaninfo.SalesSelectDTO;
import com.topsec.crm.flow.api.dto.contractreview.statistics.ContractExecuteStatisticsVO;
import com.topsec.crm.flow.api.dto.contractreview.statusinfo.ContractTermAndDeliveryStatusDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.RevenueRecognitionDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.ReviewRetentionMoneyDTO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteStatisticsVO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewMainInfo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementProcessQuery;
import com.topsec.crm.flow.api.vo.sealApplication.SealApplicationInfoVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.process.impl.ContractReviewProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.HomeNameValue;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteProjectSignAgentClient;
import com.topsec.crm.project.api.entity.AgentTreeSelect;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.CrmProjectSigningAgentVo;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@Tag(name = "合同评审流程中使用的接口")
@RequiredArgsConstructor
@RequestMapping("/inFlow")
public class ContractInFlowController extends BaseController {

    private final ContractReviewMainService contractReviewMainService;
    private final ContractReviewAttachmentService contractReviewAttachmentService;
    private final ContractReviewDeliveryService contractReviewDeliveryService;
    private final ContractReviewPaymentProvisionService contractReviewPaymentProvisionService;
    private final ContractReviewRevenueRecognitionService reviewRevenueRecognitionService;
    private final ContractReviewRetentionMoneyService contractReviewRetentionMoneyService;
    private final ContractReviewFlowService contractReviewFlowService;
    private final ContractReviewSpecialCodeService contractReviewSpecialCodeService;
    private final ContractReviewProductOwnService contractReviewProductOwnService;
    private final ContractReviewProductThirdService contractReviewProductThirdService;
    private final PriceReviewMainService priceReviewMainService;
    private final SalesAgreementReviewMainService salesAgreementReviewMainService;
    private final RemoteProjectSignAgentClient remoteProjectSignAgentClient;
    private final ContractReviewProcessService contractReviewProcessService;
    private final DeemedDirectRecordService deemedDirectRecordService;
    private final ISealApplicationContractReviewService iSealApplicationContractReviewService;

    @GetMapping("/contractReview/contractInfoByProcessInstanceId")
    @PreFlowPermission
    @Operation(summary = "根据流程id获取合同评审信息")
    public JsonObject<ContractReviewMainFlowLaunchDTO> contractInfoByProcessInstanceId(@RequestParam String processInstanceId) {
        String processInstanceId1 = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId1, processInstanceId);
        ContractReviewMainFlowLaunchDTO launchDTO = contractReviewMainService.contractInfoByProcessInstanceId(processInstanceId);
        List<ContractProductThirdDTO> contractProductThirdDTOS = launchDTO.getContractProductThirdDTOS();
        ListUtils.emptyIfNull(contractProductThirdDTOS).forEach(contractReviewProductThirdService::mosaicProductPrice);
        return new JsonObject<>(launchDTO);
    }

    @GetMapping("/contractReview/contractInfoBasicByProcessInstanceId")
    @Operation(summary = "根据流程id获取合同评审信息")
    @PreFlowPermission
    public JsonObject<ContractReviewMainFlowLaunchDTO> contractInfoBasicByProcessInstanceId(@RequestParam String processInstanceId) {
        String processInstanceId1 = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId1, processInstanceId);

        return new JsonObject<>(contractReviewMainService.contractInfoBasicByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/contractAttachment/getAttachmentByContractId")
    @Operation(summary = "根据合同id获取附件")
    @PreFlowPermission
    public JsonObject<List<ContractReviewAttachmentDTO>> getByContractId(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewAttachmentService.getByContractId(contractId));
    }

    @GetMapping("/contractDelivery/getDeliveryInfoList")
    @Operation(summary = "根据合同ID获取合同发货信息", description = "合同发货")
    @PreFlowPermission
    public JsonObject<List<ContractDeliveryDTO>> getDeliveryInfoList(@RequestParam String contractId){
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage(),contractReviewDeliveryService.getContractDeliveryInfoList(contractId));
    }

    @GetMapping("/contractTerm/pagePaymentProvisionByContractId")
    @Operation(summary = "根据合同id查询付款条款")
    @PreFlowPermission
    public JsonObject<TableDataInfo> pagePaymentProvisionByContractId(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        startPage();
        return new JsonObject<>(contractReviewPaymentProvisionService.pageByContractId(contractId));
    }

    @GetMapping("/contractTerm/pageRevenueRecognitionByContractId")
    @Operation(summary = "根据合同id查询收入确认条款")
    @PreFlowPermission
    public JsonObject<TableDataInfo> pageByContractId(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        startPage();
        return new JsonObject<>(reviewRevenueRecognitionService.pageByContractId(contractId));
    }

    @GetMapping("/contractTerm/pageRetentionMoneyByContractId")
    @Operation(summary = "根据合同id查询质保金条款")
    @PreFlowPermission
    public JsonObject<ReviewRetentionMoneyDTO> pageRetentionMoneyByContractId(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewRetentionMoneyService.getByContractId(contractId));
    }

    @PostMapping("/contractReview/pageSpecialCode")
    @PreFlowPermission(hasAnyNodes = "contractReview_03J")
    @Operation(summary = "分页查询03J的特殊代码确认")
    public JsonObject<TableDataInfo> pageAfterSecurityCode(@RequestBody SpecialCodePageQuery query) {
        assert query != null;
        PreFlowPermissionAspect.checkProcessInstanceId(query.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.pageAfterSpecialCode(query));
    }

    @PostMapping("/contractReview/updateSaleCode")
    @Operation(summary = "03J代码确认")
    @PreFlowPermission(hasAnyNodes = "contractReview_03J")
    public JsonObject<Boolean> updateSaleCode(@RequestBody List<ContractReviewSpecialCodeDTO> contractReviewSpecialCodeDTO) {
        if (CollectionUtils.isEmpty(contractReviewSpecialCodeDTO)) {
            return new JsonObject<>(ResultEnum.SUCCESS.getResult(), true);
        }
        Set<String> contractIds = new HashSet<>();
        Set<String> ids = contractReviewSpecialCodeDTO.stream().map(ContractReviewSpecialCodeDTO::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(ids)) {
            List<ContractReviewSpecialCode> specialCodes = contractReviewSpecialCodeService.listByIds(ids);
            Set<String> contractIds1 = ListUtils.emptyIfNull(specialCodes).stream().filter(item -> item.getDelFlag() == 0).map(ContractReviewSpecialCode::getContractReviewId).filter(Objects::nonNull).collect(Collectors.toSet());
            contractIds.addAll(contractIds1);
        }
        contractReviewSpecialCodeDTO.forEach(item -> {
            if (item.getId() != null) {
                // 修改的时候 不让修改的内容，
                item.setContractReviewId(null);
            }
        });
        checkFlowAuthByContractIds(contractIds, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewSpecialCodeService.updateSaleCode(contractReviewSpecialCodeDTO));
    }


    @PostMapping("/contractDelivery/saveDeliveryInfo")
    @Operation(summary = "发货信息保存", description = "合同发货")
    @PreFlowPermission
    public JsonObject<Boolean> saveDeliveryInfo(@Valid @RequestBody ContractDeliveryDTO deliveryDTO){
        assert deliveryDTO != null;
        String contractId = deliveryDTO.getContractId();
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewDeliveryService.saveDeliveryInfo(deliveryDTO));
    }

    @PostMapping("/contractDelivery/saveOrUpdateDeliveryDetail")
    @Operation(summary = "发货产品明细保存或修改", description = "合同发货")
    @PreFlowPermission
    public JsonObject<Boolean> saveOrUpdateDeliveryDetail(@RequestBody ContractDeliveryDTO deliveryDTO){
        assert deliveryDTO != null;
        if (deliveryDTO.getId() == null) {
            // 新增的时候直接判断contractId
            String contractId = deliveryDTO.getContractId();
            checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        } else {
            // 修改的时候 要先根据id查出来
            ContractReviewDelivery delivery = contractReviewDeliveryService.getById(deliveryDTO.getId());
            if (delivery == null) {
                throw new CrmException("参数有误");
            }
            deliveryDTO.setContractId(null);
            checkFlowAuthByContractId(delivery.getContractId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }
        return new JsonObject<>(contractReviewDeliveryService.saveOrUpdateDeliveryDetail(deliveryDTO));
    }

    @GetMapping("/contractDelivery/deleteById")
    @Operation(summary = "删除发货", description = "合同发货")
    @PreFlowPermission
    public JsonObject<Boolean> deleteById(@RequestParam String id){
        // 判断这个id 是不是这个人加的
        ContractReviewDelivery delivery = contractReviewDeliveryService.getById(id);
        if (delivery == null || delivery.getDelFlag()) {
            throw new CrmException("发货信息不存在");
        }
        checkFlowAuthByContractId(delivery.getContractId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewDeliveryService.deleteById(id));
    }

    @GetMapping("/contractDelivery/getProductPageByContractId")
    @Operation(summary = "根据合同ID获取合同产品列表", description = "合同发货")
    @PreFlowPermission
    public JsonObject<PageUtils<ContractDeliveryDetailDTO>> getProductPageByContractId(@RequestParam String contractId, String deliveryId, Integer pageNum, Integer pageSize){
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage(),contractReviewDeliveryService.getProductPageByContractId(contractId, deliveryId,pageNum, pageSize));
    }

    @GetMapping("/contractDelivery/getDeliveredProductPage")
    @Operation(summary = "根据发货ID获取发货分页列表", description = "合同发货")
    @PreFlowPermission
    public JsonObject<TableDataInfo> getDeliveredProductPage(@RequestParam String deliveryId){
        // 判断这个id 是不是这个人加的
        ContractReviewDelivery delivery = contractReviewDeliveryService.getById(deliveryId);
        if (delivery == null || delivery.getDelFlag()) {
            throw new CrmException("发货信息不存在");
        }
        checkFlowAuthByContractId(delivery.getContractId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        startPage();
        return new JsonObject<>(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage(),contractReviewDeliveryService.getContractDeliveryProductPage(deliveryId));
    }

    @GetMapping("/contractDelivery/isAllDelivery")
    @Operation(summary = "判断合同是否全部发货", description = "合同发货")
    @PreFlowPermission
    public JsonObject<Boolean> isAllDelivery(@RequestParam String contractId){
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewDeliveryService.isAllDelivery(contractId));
    }

    @GetMapping("/contractReview/salesSelect")
    @Operation(summary = "销售人员下拉")
    @PreFlowPermission(hasAnyNodes = "__initiator__")
    public JsonObject<SalesSelectDTO> salesSelect(@RequestParam String projectId) {
        return new JsonObject<>(contractReviewMainService.salesSelect(projectId));
    }

    @GetMapping("/contractReview/customerAndSignContractByProject")
    @Operation(summary = "客户和签约单位信息通过项目查询")
    @PreFlowPermission(hasAnyNodes = "__initiator__")
    public JsonObject<ContractReviewMainFlowLaunchDTO> customerAndSignContractByProject(@RequestParam String projectId) {
        return new JsonObject<>(contractReviewMainService.customerAndSignContractByProject(projectId));
    }

    @GetMapping("/contractReview/linkmanSelect")
    @Operation(summary = "联系人下拉")
    @PreFlowPermission(hasAnyNodes = "__initiator__")
    public JsonObject<List<HomeNameValue<String, String>>> linkmanSelect(@RequestParam String projectId) {
        return new JsonObject<>(contractReviewMainService.linkmanSelect(projectId));
    }

    @GetMapping("/contractReview/productOwnInfoByProjectId")
    @Operation(summary = "根据项目id取项目中的自有产品信息")
    @PreFlowPermission(hasAnyNodes = "__initiator__")
    public JsonObject<TableDataInfo> productOwnInfoByProjectId(@RequestParam String projectId, @RequestParam(required = false) String contractId) {
        if (StringUtils.isNotEmpty(contractId)) {
            checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }
        return new JsonObject<>(contractReviewProductOwnService.productInfoByProjectId(projectId, contractId));
    }

    @GetMapping("/contractReview/productThirdInfoByProjectId")
    @Operation(summary = "根据项目id取项目中的第三方产品信息")
    @PreFlowPermission(hasAnyNodes = "__initiator__")
    public JsonObject<TableDataInfo> productThirdInfoByProjectId(@RequestParam String projectId, @RequestParam(required = false) String contractId) {
        if (StringUtils.isNotEmpty(contractId)) {
            checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }
        return new JsonObject<>(contractReviewProductThirdService.productInfoByProjectId(projectId, contractId));
    }

    @PostMapping("/contractReview/saveContractInfo")
    @Operation(summary = "保存或修改业务数据,根据dto是否传id判断")
    @PreFlowPermission(hasAnyNodes = "__initiator__")
    public JsonObject<String> saveOrUpdateContractInfo(@RequestBody ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO) {
        assert contractReviewMainFlowLaunchDTO != null;
        ContractReviewMainBaseInfoDTO baseInfoDTO = contractReviewMainFlowLaunchDTO.getBaseInfoDTO();
        assert baseInfoDTO != null;
        // 流程里面只有修改
        // todo 嵌套修改 list需要根据子表id关联到主表id，和主表的id对比 不一致的不让改
        checkFlowAuthByContractId(baseInfoDTO.getId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewMainService.saveDTO(contractReviewMainFlowLaunchDTO));
    }

    @GetMapping("/contractReview/getExecuteStatistics")
    @Operation(summary = "合同评审统计信息")
    @PreFlowPermission
    public JsonObject<ContractExecuteStatisticsVO> getExecuteStatistics(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewMainService.getContractExecuteStatistics(contractId));
    }

    @GetMapping("/contractReview/getPerformanceExecuteStatistics")
    @Operation(summary = "业绩上报统计信息")
    @PreFlowPermission
    public JsonObject<PerformanceExecuteStatisticsVO> getPerformanceExecuteStatistics(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewMainService.getPerformanceExecuteStatistics(contractId));
    }

    @GetMapping("/contractReview/getTermAndDeliveryStatus")
    @Operation(summary = "获取条款以及发货信息的填写状态")
    @PreFlowPermission
    public JsonObject<ContractTermAndDeliveryStatusDTO> getTermAndDeliveryStatus(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.getTermAndDeliveryStatus(contractId));
    }

    @PostMapping("/contractTerm/saveOrUpdatePaymentProvision")
    @Operation(summary = "修改或保存付款条款(实时逻辑)")
    @PreFlowPermission(hasAnyNodes = {"__initiator__", "contractReview_03"})
    public JsonObject<Boolean> saveOrUpdatePaymentProvisionBatch(@RequestBody PaymentProvisionDTO paymentProvisionDTO) {
        assert paymentProvisionDTO != null;
        if (paymentProvisionDTO.getId() == null) {
            // 新增的时候直接判断contractId
            String contractId = paymentProvisionDTO.getContractReviewMainId();
            checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        } else {
            // 修改的时候 要先根据id查出来
            ContractReviewPaymentProvision paymentProvision = contractReviewPaymentProvisionService.getById(paymentProvisionDTO.getId());
            if (paymentProvision == null) {
                throw new CrmException("参数有误");
            }
            paymentProvisionDTO.setContractReviewMainId(paymentProvision.getContractReviewMainId());
            checkFlowAuthByContractId(paymentProvision.getContractReviewMainId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }

        //checkFlowAuthByContractId(paymentProvisionDTO.getContractReviewMainId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewPaymentProvisionService.saveOrUpdate(paymentProvisionDTO));
    }

    @PostMapping("/contractTerm/saveOrUpdateRevenueRecognition")
    @Operation(summary = "修改或保存收入确认条款(实时逻辑)")
    @PreFlowPermission(hasAnyNodes = {"__initiator__", "contractReview_03"})
    public JsonObject<Boolean> saveOrUpdateRevenueRecognition(@RequestBody RevenueRecognitionDTO revenueRecognitionDTO) {
        assert revenueRecognitionDTO != null;
        if (revenueRecognitionDTO.getId() == null) {
            // 新增的时候直接判断contractId
            String contractId = revenueRecognitionDTO.getContractReviewMainId();
            checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        } else {
            // 修改的时候 要先根据id查出来
            ContractReviewRevenueRecognition revenueRecognition = reviewRevenueRecognitionService.getById(revenueRecognitionDTO.getId());
            if (revenueRecognition == null) {
                throw new CrmException("参数有误");
            }
            revenueRecognitionDTO.setContractReviewMainId(null);
            checkFlowAuthByContractId(revenueRecognition.getContractReviewMainId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }
        return new JsonObject<>(reviewRevenueRecognitionService.saveOrUpdate(revenueRecognitionDTO));
    }

    @GetMapping("/contractTerm/deletePaymentProvisionById")
    @Operation(summary = "根据id删除付款条款")
    @PreFlowPermission(hasAnyNodes = {"__initiator__", "contractReview_03"})
    public JsonObject<Boolean> deletePaymentProvisionById(@RequestParam String id) {
        ContractReviewPaymentProvision contractReviewPaymentProvision = contractReviewPaymentProvisionService.getById(id);
        if (contractReviewPaymentProvision == null || contractReviewPaymentProvision.getDelFlag() == 1) {
            throw new CrmException("条款不存在");
        }
        checkFlowAuthByContractId(contractReviewPaymentProvision.getContractReviewMainId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewPaymentProvisionService.deleteById(id));
    }

    @GetMapping("/contractTerm/deleteRevenueRecognitionById")
    @Operation(summary = "根据id删除收入确认条款")
    @PreFlowPermission(hasAnyNodes = {"__initiator__", "contractReview_03"})
    public JsonObject<Boolean> deleteRevenueRecognitionById(@RequestParam String id) {
        ContractReviewRevenueRecognition contractReviewRevenueRecognition = reviewRevenueRecognitionService.getById(id);
        if (contractReviewRevenueRecognition == null || contractReviewRevenueRecognition.getDelFlag() == 1) {
            throw new CrmException("条款不存在");
        }
        checkFlowAuthByContractId(contractReviewRevenueRecognition.getContractReviewMainId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(reviewRevenueRecognitionService.deleteById(id));
    }

    @GetMapping("/contractReview/productOwnInfoTileByContractId")
    @Operation(summary = "根据合同id取合同中的自有产品信息（平铺）")
    @PreFlowPermission
    public JsonObject<List<ContractProductOwnDTO>> productOwnInfoTileByContractId(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewProductOwnService.productOwnInfoTileByContractId(contractId, true));
    }

    @GetMapping("/contractReview/productOwnInfoByContractId")
    @Operation(summary = "根据合同id取合同中的自有产品信息（树结构）")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<List<ContractProductOwnDTO>> productOwnInfoByContractId(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewProductOwnService.productInfoByContractId(contractId, true));
    }

    @GetMapping("/contractReview/deleteContract")
    @Operation(summary = "删除合同")
    @PreFlowPermission
    public JsonObject<Boolean> deleteContract(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.deleteContract(contractId));
    }

    @GetMapping("/contractReview/productThirdInfoByContractId")
    @Operation(summary = "根据合同id取合同中的第三方产品信息")
    @PreFlowPermission
    public JsonObject<List<ContractProductThirdDTO>> productThirdInfoByContractId(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<ContractProductThirdDTO> contractProductThirdDTOS = contractReviewProductThirdService.productThirdInfoByContractId(contractId, true);
        ListUtils.emptyIfNull(contractProductThirdDTOS).forEach(contractReviewProductThirdService::mosaicProductPrice);
        return new JsonObject<>(contractProductThirdDTOS);
    }


    @GetMapping("/contractReview/changeStep")
    @Operation(summary = "下一步")
    @PreFlowPermission(hasAnyNodes = "__initiator__")
    public JsonObject<Boolean> step(@RequestParam Integer step, @RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewMainService.changeStep(step, contractId));
    }

    @PostMapping("/contractAttachment/upload")
    @Operation(summary = "上传合同附件")
    @PreFlowPermission
    public JsonObject<Boolean> upload(@RequestBody ContractReviewAttachmentDTO contractReviewAttachmentDTO) {
        assert contractReviewAttachmentDTO != null;
        checkFlowAuthByContractId(contractReviewAttachmentDTO.getContractReviewMainId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewAttachmentService.saveContractAttachment(contractReviewAttachmentDTO));
    }

    @GetMapping("/contractAttachment/skipUpload")
    @Operation(summary = "跳过上传合同附件")
    @PreFlowPermission
    public JsonObject<Boolean> skipUpload(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewAttachmentService.skipUpload(contractId));
    }

    @GetMapping("/contractAttachment/delete")
    @Operation(summary = "删除附件")
    @PreFlowPermission
    public JsonObject<Boolean> delete(@RequestParam String id) {
        ContractReviewAttachment attachment = contractReviewAttachmentService.getById(id);
        if (attachment == null || attachment.getDelFlag() == 1) {
            throw new CrmException("附件不存在");
        }
        checkFlowAuthByContractId(attachment.getContractReviewMainId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewAttachmentService.delete(id));
    }

    @PostMapping("/contractAttachment/generateElectricContract")
    @Operation(summary = "生成电子合同")
    @PreFlowPermission
    public JsonObject<String> generateElectricContract(@RequestBody ContractElectricContractDTO contractReviewMainFlowLaunchDTO) {
        assert contractReviewMainFlowLaunchDTO != null;
        checkFlowAuthByContractId(contractReviewMainFlowLaunchDTO.getContractId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewAttachmentService.generateElectricContract(contractReviewMainFlowLaunchDTO, false, UserInfoHolder.getCurrentPersonId()));
    }

    @PostMapping("/contractAttachment/preViewElectricContract")
    @Operation(summary = "预览电子合同")
    @PreFlowPermission
    public JsonObject<String> preViewElectricContract(@RequestBody ContractElectricContractDTO contractReviewMainFlowLaunchDTO) {
        assert contractReviewMainFlowLaunchDTO != null;
        checkFlowAuthByContractId(contractReviewMainFlowLaunchDTO.getContractId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewAttachmentService.generateElectricContract(contractReviewMainFlowLaunchDTO, true, UserInfoHolder.getCurrentPersonId()));
    }

    @GetMapping("/contractAttachment/checkAttachment")
    @Operation(summary = "查当前合同是否有电子合同和普通附件")
    @PreFlowPermission
    public JsonObject<ContractAttachmentStatus> checkAttachment(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewAttachmentService.checkAttachment(contractId));
    }

    @GetMapping("/contractReview/isRegional")
    @Operation(summary = "合同的销售人员是否为区域销售")
    @PreFlowPermission(hasAnyNodes = {"__initiator__","sid-4A8F4D88-C102-4CDC-815B-56453A05CE10"})
    public JsonObject<Boolean> isRegional(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.isRegional(contractId));
    }

    @GetMapping("/contractReview/taxRateIsChange")
    @Operation(summary = "税率是否改变")
    @PreFlowPermission
    public JsonObject<Boolean> taxRateIsChange(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.taxRateIsChange(contractId));
    }

    @GetMapping("/contractReview/changeProjectTaxRate")
    @Operation(summary = "改变项目中的税率")
    @PreFlowPermission
    public JsonObject<Boolean> changeProjectTaxRate(@RequestParam String contractId) {
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewFlowService.changeProjectTaxRate(contractId));
    }

    @PostMapping("/contractReview/productOwnSnByContractIds")
    @Operation(summary = "根据合同id查询出货产品详情")
    @PreFlowPermission
    public JsonObject<TableDataInfo> productOwnSnByContractId(@RequestBody ContractProductOwnSntVO contractProductOwnSntVO) {
        assert contractProductOwnSntVO != null;
        checkFlowAuthByContractId(contractProductOwnSntVO.getContractId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(contractReviewProductOwnService.productOwnSnByContractId(contractProductOwnSntVO));
    }

    @PostMapping("/contractTerm/saveOrUpdateRetentionMoney")
    @Operation(summary = "修改或保存质保金条款")
    @PreFlowPermission(hasAnyNodes = {"__initiator__", "contractReview_03"})
    public JsonObject<Boolean> saveOrUpdateRetentionMoney(@RequestBody ReviewRetentionMoneyDTO retentionMoneyDTO) {
        assert retentionMoneyDTO != null;
        if (retentionMoneyDTO.getId() == null) {
            // 新增的时候直接判断contractId
            String contractId = retentionMoneyDTO.getContractReviewMainId();
            checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        } else {
            // 修改的时候 要先根据id查出来
            ContractReviewRetentionMoney retentionMoney = contractReviewRetentionMoneyService.getById(retentionMoneyDTO.getId());
            if (retentionMoney == null) {
                throw new CrmException("参数有误");
            }
            retentionMoneyDTO.setContractReviewMainId(null);
            checkFlowAuthByContractId(retentionMoney.getContractReviewMainId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }
        return new JsonObject<>(contractReviewRetentionMoneyService.saveOrUpdate(retentionMoneyDTO));
    }

    @PostMapping("/contractTerm/saveOrUpdateRevenueRecognitionAll")
    @Operation(summary = "修改或保存收入确认条款多条(实时逻辑)")
    @PreFlowPermission(hasAnyNodes = {"__initiator__", "contractReview_03"})
    public JsonObject<Boolean> saveOrUpdateRevenueRecognition(@RequestBody List<RevenueRecognitionDTO> revenueRecognitionDTOS) {
        assert revenueRecognitionDTOS != null;
        // 新增的判断contractId
        Set<String> contractIds = revenueRecognitionDTOS.stream().filter(item -> item.getId() == null).map(RevenueRecognitionDTO::getContractReviewMainId).filter(Objects::nonNull).collect(Collectors.toSet());
        // 修改的根据id 查出来contractId 并且把contractId set成null
        Set<String> ids = revenueRecognitionDTOS.stream().filter(item -> item.getId() != null).map(RevenueRecognitionDTO::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(ids)) {
            List<ContractReviewRevenueRecognition> contractReviewRevenueRecognitions = reviewRevenueRecognitionService.listByIds(ids);
            Set<String> contractIds1 = ListUtils.emptyIfNull(contractReviewRevenueRecognitions).stream().filter(item -> item.getDelFlag() == 0).map(ContractReviewRevenueRecognition::getContractReviewMainId).filter(Objects::nonNull).collect(Collectors.toSet());
            contractIds.addAll(contractIds1);
        }
        revenueRecognitionDTOS.forEach(item -> {
            if (item.getId() != null) {
                // 修改的时候 不让修改的内容，
                item.setContractReviewMainId(null);
            }
        });

        checkFlowAuthByContractIds(contractIds, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(reviewRevenueRecognitionService.saveOrUpdateAll(revenueRecognitionDTOS));
    }

    @Operation(summary = "查询项目最后一个通过的价审信息")
    @GetMapping("/contractReview/queryLatestPassedPriceReviewMainByContractId")
    @PreFlowPermission
    public JsonObject<PriceReviewMainInfo> queryLatestPassedPriceReviewMainByProjectId(@RequestParam String contractId) {
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        String processInstanceId1 = contractInfo.getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId1, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        String projectId = contractInfo.getProjectId();
        return new JsonObject<>(priceReviewMainService.queryLatestPassedPriceReviewMainByProjectId(projectId));
    }

    @GetMapping("/agent/tree/{contractId}")
    @PreFlowPermission
    public JsonObject<List<AgentTreeSelect>> tree(@PathVariable String contractId) {
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        String processInstanceId1 = contractInfo.getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId1, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        String projectId = contractInfo.getProjectId();
        return remoteProjectSignAgentClient.tree(projectId);
    }

    @GetMapping("/agent/selectSigningAgentDetail")
    @PreFlowPermission
    public JsonObject<CrmAgentVo> selectSigningAgentDetail(@RequestParam String contractId, @RequestParam String agentId) {
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        checkFlowAuthByContractId(contractId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        String projectId = contractInfo.getProjectId();
        //查询项目
        JsonObject<List<CrmProjectSigningAgentVo>> listJsonObject = remoteProjectSignAgentClient.listAgent(projectId);
        if(listJsonObject.isSuccess()){
            List<CrmProjectSigningAgentVo> signs = listJsonObject.getObjEntity();
            if(CollectionUtil.isEmpty(signs)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
            Set<String> collect = signs.stream().map(CrmProjectSigningAgentVo::getAgentId).collect(Collectors.toSet());
            if(!collect.contains(agentId)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }
        CrmProjectSigningAgentVo query = new CrmProjectSigningAgentVo();
        query.setAgentId(agentId);
        query.setProjectId(projectId);
        return remoteProjectSignAgentClient.selectSigningAgentDetail(query);
    }


    @PostMapping("/edit00Process")
    @Operation(summary = "修改流程业务数据")
    @PreFlowPermission
    public JsonObject<Boolean> edit00Process(@RequestBody ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO) {
        assert contractReviewMainFlowLaunchDTO != null;
        ContractReviewMainBaseInfoDTO baseInfoDTO = contractReviewMainFlowLaunchDTO.getBaseInfoDTO();
        assert baseInfoDTO != null;
        // 流程里面只有修改
        // todo 嵌套修改 list需要根据子表id关联到主表id，和主表的id对比 不一致的不让改
        checkFlowAuthByContractId(baseInfoDTO.getId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        contractReviewProcessService.edit00(contractReviewMainFlowLaunchDTO);
        return new JsonObject<>(true);
    }


    @GetMapping("/contractReview/getSalesAgreementsByProjectId")
    @Operation(description = "根据项目id获取价审中的关联的销售协议")
    @PreFlowPermission
    public JsonObject<List<SalesAgreementReviewMain>> getSalesAgreementsByProjectId(@RequestParam String projectId) {
        // 根据流程id 查项目id 判断是不是一个
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ContractReviewMain main = contractReviewMainService.getByProcessInstanceId(processInstanceId);
        if (main == null) {
            throw new CrmException("该合同信息不存在");
        }
        if (!Objects.equals(projectId, main.getProjectId())){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        Set<String> saleAgreements = priceReviewMainService.querySaleAgreementOfLatestPassedPriceReview(projectId);
        List<SalesAgreementReviewMain> salesAgreementByIds = salesAgreementReviewMainService.findSalesAgreementByIds(new ArrayList<>(saleAgreements));
        return new JsonObject<>(salesAgreementByIds);
    }

    @GetMapping("/contractReview/getSalesAgreementsByCondition")
    @Operation(description = "条件查询销售协议")
    @PreFlowPermission
    public JsonObject<List<SalesAgreementReviewMain>> getSalesAgreementsByCondition(SalesAgreementProcessQuery query){
        // new一个查询对象 避免查询透传
        SalesAgreementProcessQuery query1 = new SalesAgreementProcessQuery();
        query1.setAgreementName(query.getAgreementName());
        // todo 应该是当前人的id 用personId 和wly确认
        query1.setPersonId(UserInfoHolder.getCurrentPersonId());
        query1.setDeptId(getDepartmentId());
        List<SalesAgreementReviewMain> salesAgreementBusinessList = salesAgreementReviewMainService.findSalesAgreementBusinessList(query1);
        return new JsonObject<>(salesAgreementBusinessList);
    }

    @GetMapping("/contractReview/getSalesAgreementsById")
    @Operation(description = "根据合同id查询销售协议")
    @PreFlowPermission
    public JsonObject<List<SalesAgreementReviewMain>> getSalesAgreementsById(@RequestParam String contractId) {
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        if (contractInfo == null) {
            throw new CrmException("该合同信息不存在");
        }
        PreFlowPermissionAspect.checkProcessInstanceId(contractInfo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<String> salesAgreements = contractInfo.getSalesAgreements();
        if (CollectionUtil.isEmpty(salesAgreements)) {
            return new JsonObject<>(Collections.emptyList());
        }
        return new JsonObject<>(salesAgreementReviewMainService.findSalesAgreementByIds(salesAgreements));
    }

    @GetMapping("/contractReview/queryLatestPassedPriceReviewSnapshotByProjectId")
    @Operation(summary = "查询项目最后一个通过的价审快照")
    @PreFlowPermission
    public JsonObject<CrmProjectDirectlyVo> queryLatestPassedPriceReviewSnapshotByProjectId(@RequestParam String projectId) {
        // 根据流程id 查项目id 判断是不是一个
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ContractReviewMain main = contractReviewMainService.getByProcessInstanceId(processInstanceId);
        if (main == null) {
            throw new CrmException("该合同信息不存在");
        }
        if (!Objects.equals(projectId, main.getProjectId())){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        PriceReviewMainInfo priceReviewMainInfo = priceReviewMainService.queryLatestPassedPriceReviewMainByProjectId(projectId);
        if (priceReviewMainInfo != null) {
            CrmProjectDirectlyVo crmProjectDirectlyVo = priceReviewMainService.queryProjectInfo(priceReviewMainInfo.getProcessInstanceId());
            return new JsonObject<>(crmProjectDirectlyVo);
        } else {
            return new JsonObject<>(new CrmProjectDirectlyVo());
        }
    }


    @GetMapping("/queryDeemedDirect")
    @Operation(description = "根据签约单位判断是否备案过")
    @PreFlowPermission
    public JsonObject<Boolean> queryDeemedDirect(@RequestParam String companyCompanyName){
        return new JsonObject<>(deemedDirectRecordService.IsExistByCompanyName(companyCompanyName));
    }


    @GetMapping("/querySealApplicationByContractId")
    @Operation(description = "合同评审印鉴")
    @PreFlowPermission
    public JsonObject<List<SealApplicationInfoVO>> querySealApplicationByContractNumber(@RequestParam String contractId){
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        if (contractInfo == null) {
            throw new CrmException("该合同信息不存在");
        }
        PreFlowPermissionAspect.checkProcessInstanceId(contractInfo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        if (StringUtils.isEmpty(contractInfo.getContractNumber())) {
            return new JsonObject<>(Collections.emptyList());
        }
        return new JsonObject<>(iSealApplicationContractReviewService.listSealInfoByContractNumber(contractInfo.getContractNumber()));
    }

    private void checkFlowAuthByContractId(String contractId, String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        if (contractInfo == null) {
            throw new CrmException("该合同信息不存在");
        }
        String processInstanceId1 = contractInfo.getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId1, processInstanceId);
    }

    private void checkFlowAuthByContractIds(Set<String> contractIds, String processInstanceId){
        if (contractIds.size() > 1) {
            throw new CrmException("参数有误，请检查参数");
        }
        String contractId = contractIds.stream().findFirst().orElseThrow(() -> new CrmException("参数有误，请检查参数"));
        checkFlowAuthByContractId(contractId, processInstanceId);
    }


}
