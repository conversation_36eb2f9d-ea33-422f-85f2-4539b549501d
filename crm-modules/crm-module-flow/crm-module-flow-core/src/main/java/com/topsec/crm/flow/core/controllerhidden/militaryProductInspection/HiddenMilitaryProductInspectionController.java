package com.topsec.crm.flow.core.controllerhidden.militaryProductInspection;

import com.topsec.crm.flow.api.dto.militaryinspection.MilitaryProductInspectionVO;
import com.topsec.crm.flow.core.service.MilitaryProductInspectionService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/hidden/militaryInspection")
@Tag(name = "军品检验-不对外公开", description = "/hidden/militaryInspection")
@RequiredArgsConstructor
@Validated
public class HiddenMilitaryProductInspectionController extends BaseController {

    private final MilitaryProductInspectionService militaryProductInspectionService;


    @GetMapping("/existsMilitaryProductInspectionProcess")
    @Operation(summary = "是否存在军品检验流程")
    JsonObject<Boolean> existsMilitaryProductInspectionProcess(@RequestParam String projectId){
        return new JsonObject<>(militaryProductInspectionService.existsMilitaryProductInspectionProcess(projectId));
    }

    @GetMapping("/militaryInspectionDetailInfo")
    @Operation(summary = "军品检验详情")
    JsonObject<MilitaryProductInspectionVO> militaryInspectionDetailInfo(@RequestParam String processInstanceId){
        return new JsonObject<>(militaryProductInspectionService.militaryInspectionDetailInfo(processInstanceId));
    }

    @PostMapping("/militaryInspectionDetailInfoByProcessIds")
    @Operation(summary = "根据流程id批量获取军品检验信息")
    JsonObject<List<MilitaryProductInspectionVO>> militaryInspectionDetailInfoByProcessIds(@RequestBody List<String> processInstanceIds){
        return new JsonObject<>(militaryProductInspectionService.militaryInspectionDetailInfoByProcessIds(processInstanceIds));
    }


}