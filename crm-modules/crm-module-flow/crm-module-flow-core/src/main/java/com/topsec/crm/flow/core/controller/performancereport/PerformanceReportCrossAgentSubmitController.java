package com.topsec.crm.flow.core.controller.performancereport;

import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportCrossAgentSubmitDetailDTO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportCrossAgentSubmitFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.process.impl.PerformanceReportCrossAgentSubmitProcessService;
import com.topsec.crm.flow.core.service.PerformanceReportCrossAgentSubmitService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/performanceReportCrossAgentSubmit")
@Tag(name = "跨总代提交业绩上报", description = "/performanceReportCrossAgentSubmit")
public class PerformanceReportCrossAgentSubmitController {
    @Autowired
    private PerformanceReportCrossAgentSubmitService performanceReportCrossAgentSubmitService;
    @Autowired
    private PerformanceReportCrossAgentSubmitProcessService reportCrossAgentSubmitProcessService;
    @Autowired
    private RemoteCustomerService remoteCustomerService;



    @PostMapping("/launch")
    @PreAuthorize(hasAnyPermission  = {"crm_flow_over_dynasty_performance_report","crm_agent_flow_over_dynasty_performance_report"})
    @Operation(summary = "发起跨总代提交业绩上报流程")
    public JsonObject<Boolean> launch(@RequestBody PerformanceReportCrossAgentSubmitFlowLaunchDTO launchDTO) {
        return new JsonObject<>(reportCrossAgentSubmitProcessService.launch(launchDTO));
    }

    @GetMapping("/detail")
    @Operation(summary = "跨总代业绩上报流程详情")
    @PreFlowPermission
    public JsonObject<PerformanceReportCrossAgentSubmitDetailDTO> detail(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(performanceReportCrossAgentSubmitService.detail(processInstanceId));
    }

    @GetMapping("/customer")
    @Operation(summary = "获取客户信息")
    @PreFlowPermission
    public JsonObject<CrmCustomerVo> getCustomerIndustry(@RequestParam String customerId) {
        return remoteCustomerService.getCustomerIndustry(customerId);
    }
}
