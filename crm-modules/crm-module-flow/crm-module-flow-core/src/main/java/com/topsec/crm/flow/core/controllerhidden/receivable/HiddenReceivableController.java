package com.topsec.crm.flow.core.controllerhidden.receivable;

import com.topsec.crm.flow.api.dto.contractreceivable.ContractReceivableDetailDTO;
import com.topsec.crm.flow.api.dto.contractreceivable.ContractReceivableQuery;
import com.topsec.crm.flow.core.service.ContractReceivableService;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 应收催款远程调用
 *
 * <AUTHOR>
 * @date 2024/12/23 11:47
 */
@RestController
@RequestMapping("/receivableFeign")
@Tag(name = "应收催款-不对外开放", description = "/receivableFeign")
@RequiredArgsConstructor
@Slf4j
public class HiddenReceivableController extends BaseController {

    private final ContractReceivableService receivableService;

    @GetMapping("/pageReceivableDetailByContractNumber")
    @Operation(summary = "根据合同号查询这个合同所有的催缴跟进记录（历史催缴）")
    public JsonObject<PageUtils<ContractReceivableDetailDTO>> pageReceivableDetailByContractNumber(@RequestParam String contractNumber) {
        return new JsonObject<>(receivableService.pageReceivableDetailByContractNumber(contractNumber));
    }

    @GetMapping("/getReceivableLatelyByContractNumberBatch")
    @Operation(summary = "根据合同号集合，查询最近的应收催款记录")
    public JsonObject<List<ContractReceivableDetailDTO>> getReceivableLatelyByContractNumberBatch(@RequestParam Set<String> contractNumbers) {
        return new JsonObject<>(receivableService.getReceivableLatelyByContractNumberBatch(contractNumbers));
    }

    @GetMapping("/getContractNumberByCondition")
    @Operation(summary = "根据处理建议，查询符合条件的合同号集合")
    public JsonObject<Set<String>> getContractNumberByCondition(@RequestParam Integer handleSuggest) {
        return new JsonObject<>(receivableService.getContractNumberByCondition(handleSuggest));
    }

    @PostMapping("/pageReceivableDetailByCondition")
    @Operation(summary = "根据条件查询所有催缴跟进记录")
    public JsonObject<PageUtils<ContractReceivableDetailDTO>> pageReceivableDetailByCondition(@RequestBody ContractReceivableQuery query) {
        return new JsonObject<>(receivableService.pageReceivableDetailByCondition(query));
    }

    @GetMapping("/launchReceivable")
    @Operation(summary = "发起应收催款流程")
    public JsonObject<Boolean> launchReceivable(@RequestParam String contractNumber, @RequestParam Integer ignoreCheck) {
        try {
            return new JsonObject<>(receivableService.launchReceivable(contractNumber, ignoreCheck));
        } catch (Exception e) {
            log.error("发起应收催款流程失败", e);
            if (e.getMessage().contains("是否需要再次发起")) {
                return new JsonObject<>(3020, e.getMessage());
            }
            return new JsonObject<>(3010, e.getMessage());
        }
    }

    @PostMapping("/launchReceivableBatch")
    @Operation(summary = "发起应收催款流程（批量）")
    JsonObject<Boolean> launchReceivableBatch(@RequestBody Set<String> contractNumber, @RequestParam Integer ignoreCheck) {
        try {
            return new JsonObject<>(receivableService.launchReceivableBatch(contractNumber, ignoreCheck));
        } catch (Exception e) {
            log.error("发起应收催款流程失败（批量）", e);
            return new JsonObject<>(3010, e.getMessage());
        }
    };

    @GetMapping("/generateReceivable")
    public void generateReceivable(){
        receivableService.generateReceivable();
    }
    @GetMapping("/generatePerformanceReportReceivable")
    public void generatePerformanceReportReceivable(){
        receivableService.generatePerformanceReportReceivable();
    }
}
