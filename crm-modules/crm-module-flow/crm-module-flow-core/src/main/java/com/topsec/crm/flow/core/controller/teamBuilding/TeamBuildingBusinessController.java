package com.topsec.crm.flow.core.controller.teamBuilding;

import com.topsec.crm.flow.api.dto.teamBuilding.TeamBuildingDepartmentInfoVO;
import com.topsec.crm.flow.api.enums.ProductsAndServicesInvolved;
import com.topsec.crm.flow.api.enums.TeamBuildingProjectType;
import com.topsec.crm.flow.core.process.impl.TeamBuildingProcessService;
import com.topsec.crm.flow.core.service.TeamBuildingService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-06-13  10:37
 * @Description:
 */
@RestController
@RequestMapping("/business/teamBuilding")
@Tag(name = "团队组建业务", description = "/teamBuilding")
@RequiredArgsConstructor
@Validated
public class TeamBuildingBusinessController extends BaseController {

    private final TeamBuildingService teamBuildingService;

    private final TeamBuildingProcessService teamBuildingProcessService;





    @GetMapping("/departmentsInvolved")
    @PreAuthorize(hasPermission = "crm_flow_team_building",dataScope = "crm_flow_team_building")
    @Operation(summary = "00呈报人:预计涉及部门列表")
    public JsonObject<List<TeamBuildingDepartmentInfoVO>> getDepartmentsInvolved(){
        List<TeamBuildingDepartmentInfoVO> collect = teamBuildingService.listNotDeleted().stream().filter(t->t.getBelongingSystem()!=null).filter(teamBuilding -> teamBuilding.getBelongingSystem().contains("产品体系") || teamBuilding.getBelongingSystem().contains("运营体系")).collect(Collectors.toList());
        return new JsonObject<>(collect);
    }


    @GetMapping("/getBranchesInvolved")
    @PreAuthorize(hasPermission = "crm_flow_team_building",dataScope = "crm_flow_team_building")
    @Operation(summary = "00呈报人:预计分支机构列表")
    public JsonObject<List<TeamBuildingDepartmentInfoVO>> getBranchesInvolved(){
        List<TeamBuildingDepartmentInfoVO> collect = teamBuildingService.listNotDeleted().stream().filter(t->t.getBelongingSystem()!=null).filter(teamBuilding -> teamBuilding.getBelongingSystem().contains("营销体系")).collect(Collectors.toList());
        return new JsonObject<>(collect);
    }



    @GetMapping("/getProductsAndServicesInvolvedEnumValues")
    @PreAuthorize(hasPermission = "crm_flow_team_building",dataScope = "crm_flow_team_building")
    @Operation(summary = "00呈报人:获取所有涉及的产品和服务")
    public JsonObject<List<Map<String, Object>>> getProductsAndServicesInvolvedEnumValues(){
        return new JsonObject<>(ProductsAndServicesInvolved.getProductsAndServicesInvolvedEnumValues());
    }

    @GetMapping("/getTeamBuildingProjectTypeEnumValues")
    @PreAuthorize(hasPermission = "crm_flow_team_building",dataScope = "crm_flow_team_building")
    @Operation(summary = "00呈报人:获取所有项目类型")
    public JsonObject<List<Map<String, Object>>> getTeamBuildingProjectTypeEnumValues(){
        return new JsonObject<>(TeamBuildingProjectType.getTeamBuildingProjectTypeEnumValues());
    }

    @GetMapping("/getPreSales")
    @PreAuthorize(hasPermission = "crm_flow_team_building",dataScope = "crm_flow_team_building")
    @Operation(summary = "00呈报人:售前人员列表")
    public JsonObject< List<EmployeeVO>> getPreSales(@RequestParam String departmentId){
        return new JsonObject<>(teamBuildingService.getPreSales(departmentId));
    }





}
