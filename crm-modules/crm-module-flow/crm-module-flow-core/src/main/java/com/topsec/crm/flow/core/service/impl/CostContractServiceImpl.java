package com.topsec.crm.flow.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.file.api.RemoteFileService;
import com.topsec.crm.flow.api.dto.costContarct.*;
import com.topsec.crm.flow.api.dto.costContarct.VO.CostContractVo;
import com.topsec.crm.flow.api.dto.costContarct.VO.DeliverVO;
import com.topsec.crm.flow.api.dto.costPayment.VO.CostContractInfoVo;
import com.topsec.crm.flow.api.vo.sealApplication.SealApplicationInfoVO;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.*;
import com.topsec.crm.flow.core.service.CostContractAttachmentService;
import com.topsec.crm.flow.core.service.CostContractReService;
import com.topsec.crm.flow.core.service.CostContractService;
import com.topsec.crm.flow.core.service.SealApplicationService;
import com.topsec.crm.flow.core.util.MoneyUtil;
import com.topsec.crm.framework.common.bean.CrmFsmDoc;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.operation.api.RemoteContractReviewConfigService;
import com.topsec.crm.operation.api.RemoteSupplierProductService;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractSignCompanyVO;
import com.topsec.crm.operation.api.entity.SupplierVO;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectOutsourceServiceClient;
import com.topsec.crm.project.api.dto.ProjectDirectlyPersonIdPageQuery;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.enums.RelationTypeEnum;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.tos.common.vo.TosDepartmentVO;
import com.topsec.vo.node.ApproveNode;
import io.seata.common.util.CollectionUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.io.FileUtils;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *费用合同主表
 *<AUTHOR>
 */
@Service
public class CostContractServiceImpl extends ServiceImpl<CostContractMapper, CostContract> implements CostContractService{

    @Resource
    private CostContractMapper costContractMapper;
    @Resource
    private TfsNodeClient tfsNodeClient;
    @Resource
    private RemoteContractReviewConfigService remoteContractReviewConfigService;
    @Resource
    private CostContractReMapper costContractReMapper;
    @Resource
    private CostContractReService costContractReService;
    @Resource
    private RemoteSupplierProductService remoteSupplierProductService;
    @Resource
    private RemoteFileService remoteFileService;
    @Resource
    private CostContractAttachmentService costContractAttachmentService;
    @Resource
    private TosDepartmentClient tosDepartmentClient;
    @Resource
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    @Resource
    private CostContractOriginalMapper costContractOriginalMapper;
    @Resource
    private CostPaymentMapper costPaymentMapper;
    @Resource
    private CostContractPaymentClauseMapper costContractPaymentClauseMapper;
    @Resource
    private CostFilingMapper costFilingMapper;
//    private RemoteCostFilingService  remoteCostFilingService;
    @Resource
    private RemoteProjectOutsourceServiceClient remoteProjectOutsourceServiceClient;
    @Resource
    private TosEmployeeClient tosEmployeeClient;
    @Resource
    private SealApplicationService sealApplicationService;






    @Override
    public TableDataInfo selectCostContractVO(CostContractVo contarctVo, Set<String> personIdList) {

        String supplierId = null;
        if (StringUtils.isNotEmpty(contarctVo.getSupplierName())){
            JsonObject<SupplierVO> supplierObject = remoteSupplierProductService.selectCrmSupplierByName(contarctVo.getSupplierName());
            if (supplierObject.isSuccess() && supplierObject.getObjEntity()!=null){
                supplierId = supplierObject.getObjEntity().getId();
//                throw new CrmException("获取供应商信息失败");
            }else{
                return new TableDataInfo();
            }

        }
        List<CostContract> costContracts = costContractMapper.selectList(new QueryWrapper<CostContract>().lambda()
                .eq(StringUtils.isNotEmpty(contarctVo.getCostFilingNumber()),CostContract::getCostFilingNumber,contarctVo.getCostFilingNumber())
                .eq(StringUtils.isNotEmpty(contarctVo.getProcessNumber()),CostContract::getProcessNumber,contarctVo.getProcessNumber())
                .eq(StringUtils.isNotEmpty(supplierId),CostContract::getSupplierId,supplierId)
                .in(CollectionUtils.isNotEmpty(personIdList),CostContract::getCreateUser,personIdList)
                .eq(CostContract::getDelFlag,0).orderByDesc(CostContract::getCreateTime));
        if (CollectionUtils.isEmpty(costContracts)){
            return new TableDataInfo();
        }
        List<String> supplierIds =  costContracts.stream().map(CostContract::getSupplierId).collect(Collectors.toList());
        JsonObject<List<SupplierVO>> supplierJsonObject = remoteSupplierProductService.selectCrmSupplierByIds(supplierIds);
        if(!supplierJsonObject.isSuccess()){
            throw new CrmException("查询供应商信息失败");
        }
        List<SupplierVO> supplierVOS = supplierJsonObject.getObjEntity();
        Map<String,SupplierVO> supplierVOMap = supplierVOS.stream().collect(Collectors.toMap(SupplierVO::getId,(supplierVO->supplierVO)));

        List<CostFiling> costFilingList = costFilingMapper.selectList(new QueryWrapper<CostFiling>().lambda().eq(CostFiling::getDelFlag,0)
                .in(CostFiling::getId,costContracts.stream().map(CostContract::getCostFilingId).toList()));
        Map<String,CostFiling> collectMap = costFilingList.stream().collect(Collectors.toMap(CostFiling::getId,(o->o)));

        List<String> personnelIds = costContracts.stream().map(CostContract::getCreateUser).collect(Collectors.toList());
        Map<String,EmployeeVO> personVOMap= getPersonVO(personnelIds).stream().collect(Collectors.toMap(EmployeeVO::getUuid,(o->o)));

        List<String> processInstanceIds = costContracts.stream().map(CostContract::getProcessInstanceId).toList();
        Map<String, Set<ApproveNode>> approveNodeMap = tfsNodeClient.queryNodeByProcessInstanceIdList(processInstanceIds).getObjEntity();

        List<CostContractVo>  costContractVos = costContracts.stream().map(costContract->{
            CostContractVo costContractVo = HyperBeanUtils.copyProperties(costContract,CostContractVo::new);
            EmployeeVO employeeVO = personVOMap.get(costContract.getCreateUser());
            costContractVo.setCostFilingProcessInstanceId(collectMap.get(costContract.getCostFilingId()).getProcessInstanceId());
            costContractVo.setSupplierName(supplierVOMap.get(costContract.getSupplierId()).getSupplierName());
            costContractVo.setPersonnelId(employeeVO.getUuid());
            costContractVo.setPersonnelName(employeeVO.getName()+employeeVO.getJobNo());
            costContractVo.setDeptId(employeeVO.getDept()!=null? employeeVO.getDept().getUuid():"");
            costContractVo.setDeptName(employeeVO.getDept()!=null? employeeVO.getDept().getName():"");
            Set<ApproveNode> approveNodes = approveNodeMap.get(costContract.getProcessInstanceId());
            if(approveNodes!=null) costContractVo.setApprovalNode(approveNodes);
            costContractVo.setContactTime(costContract.getCreateTime());

            return costContractVo;
        }).collect(Collectors.toList());

        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setList(costContractVos);
        tableDataInfo.setTotalCount(new PageInfo(costContracts).getTotal());
        return tableDataInfo;
    }

    @Override
    public CostContractInfoDTO selectCostContractByProcessInstanceId(String processInstanceId) {
        CostContract costContract = getOne(new QueryWrapper<CostContract>().lambda().eq(CostContract::getDelFlag,0).eq(CostContract::getProcessInstanceId,processInstanceId));
        if (costContract==null){
            return new CostContractInfoDTO();
        }
        CostContractInfoDTO costContractInfoDTO = HyperBeanUtils.copyProperties(costContract, CostContractInfoDTO::new);
        JsonObject<SupplierVO> supplierVOJsonObject = remoteSupplierProductService.selectCrmSupplierById(costContract.getSupplierId());
        if (!supplierVOJsonObject.isSuccess()){
            throw new CrmException("获取签订公司失败");
        }
        SupplierVO supplierVO = supplierVOJsonObject.getObjEntity();
        costContractInfoDTO.setSupplierName(supplierVO.getSupplierName());
        JsonObject<ContractSignCompanyVO> companyVOJsonObject = remoteContractReviewConfigService.getBySignCompanyId(costContract.getCompanyId());
        if (!companyVOJsonObject.isSuccess()){
            throw new CrmException("获取签订公司失败");
        }
        List<SealApplicationInfoVO> sealApplicationInfoVOS = sealApplicationService.listSealInfoByParentProcessInstanceId(processInstanceId);
        if (CollectionUtils.isNotEmpty(sealApplicationInfoVOS)){
            costContractInfoDTO.setSealApplicationInfoVOS(sealApplicationInfoVOS);
        }
        List<String> personnelIds = new ArrayList<>(Stream.of(costContract.getCreateUser()).toList());
        Map<String,EmployeeVO> personVOMap= getPersonVO(personnelIds).stream().collect(Collectors.toMap(EmployeeVO::getUuid,(o->o)));
        ContractSignCompanyVO contractSignCompanyVO = companyVOJsonObject.getObjEntity();
        EmployeeVO employeeVO = personVOMap.get(costContract.getCreateUser());
        costContractInfoDTO.setCompanyName(contractSignCompanyVO.getCompanyName());
        costContractInfoDTO.setPersonnelId(costContract.getCreateUser());
        costContractInfoDTO.setPersonnelName(employeeVO.getName()+employeeVO.getJobNo());
        costContractInfoDTO.setDeptId(employeeVO.getDept()!=null ? employeeVO.getDept().getUuid():"");
        costContractInfoDTO.setDeptName(employeeVO.getDept()!=null ? employeeVO.getDept().getName():"");
        List<CostContractAttachment> costContractAttachments= costContractAttachmentService.list(new QueryWrapper<CostContractAttachment>().lambda().eq(CostContractAttachment::getCostId,costContract.getId()));
        costContractInfoDTO.setCostContractAttachmentInfoDTOS(HyperBeanUtils.copyListProperties(costContractAttachments, CostContractAttachmentInfoDTO::new));
        CostContractAttachment costContractAttachment = costContractAttachmentService.getOne(new QueryWrapper<CostContractAttachment>().lambda()
                .eq(CostContractAttachment::getDelFlag,0).eq(CostContractAttachment::getCostId,costContract.getId()).last(" limit 1 ")
                .orderByDesc(CostContractAttachment::getCreateTime));
        costContractInfoDTO.setDeliverVO(HyperBeanUtils.copyProperties(costContractAttachment, DeliverVO::new));
        return costContractInfoDTO;
    }


    @Transactional
    @Override
    public String generateElectronContractInfo(ElectronContractInfoDTO electronContractInfoDTO,Boolean isPreView) {
        HttpServletRequest request = new MockHttpServletRequest();
        ElectronContractOtherDTO electronContractOtherDTO = getElectronContractOtherDTO(electronContractInfoDTO.getProcessInstanceId());
        electronContractInfoDTO.setElectronContractOtherDTO(electronContractOtherDTO);

        String path = copyTempFile("word/costContract.docx",electronContractOtherDTO.getProcessNumber());
        String fileName = UUID.randomUUID().toString() + ".docx";
        String rootPath = request.getSession().getServletContext().getRealPath("/")+ fileName;

        try {
            Map<String,Object> dataMap = electronContractInfoDTO.getFileMap();
            //内容渲染
            XWPFTemplate template = XWPFTemplate.compile(path).render(dataMap);
            //保存到指定目录
            FileOutputStream fos = new FileOutputStream(rootPath);
            template.write(fos);
            fos.flush();
            fos.close();
            template.close();
            File file = new File(rootPath);
            FileInputStream input = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile("file", electronContractOtherDTO.getProcessNumber()+"电子合同.docx", "text/plain", input);
            JsonObject<CrmFsmDoc> crmFsmDocJsonObject = remoteFileService.uploadSmallFile(multipartFile,null,isPreView);
            if (!crmFsmDocJsonObject.isSuccess()){
                throw new CrmException("生成合同失败");
            }
            if (!isPreView) {
                CostContractAttachment costContractAttachment = HyperBeanUtils.copyProperties(electronContractInfoDTO.getElectronContractFillDTO(),CostContractAttachment::new);
                costContractAttachment.setIsFinal(0);
                costContractAttachment.setIsGenerate(1);
                costContractAttachment.setFileType("费用电子合同");
                costContractAttachment.setFileName(electronContractOtherDTO.getProcessNumber()+"电子合同.docx");
                costContractAttachment.setDocId(crmFsmDocJsonObject.getObjEntity().getDocId());
                costContractAttachment.setCostId(electronContractOtherDTO.getId());
                costContractAttachment.setCostType("费用合同");
                costContractAttachment.setDuplicate(electronContractInfoDTO.getElectronContractFillDTO().getDuplicate());
                costContractAttachment.setCopies(electronContractInfoDTO.getElectronContractFillDTO().getCopies());
                costContractAttachmentService.update(new LambdaUpdateWrapper<CostContractAttachment>().set(CostContractAttachment::getDelFlag,1)
                        .eq(CostContractAttachment::getCostId,electronContractOtherDTO.getId()).eq(CostContractAttachment::getFileType,"费用电子合同")
                        .eq(CostContractAttachment::getDelFlag,0));
                costContractAttachmentService.save(costContractAttachment);
            }
            return crmFsmDocJsonObject.getObjEntity().getDocId();
        } catch (Exception e) {
            e.printStackTrace();
            throw new CrmException("生成合同失败");
        } finally {
            //删除临时文件
            File file = new File(rootPath);
            file.delete();
            File copyFile = new File(path);
            copyFile.delete();
        }
//        return true;
    }

    @Override
    public List<TosDepartmentVO> getTosDepartmentVOByPersonId(String personId) {
        List<String> managedTypes = new ArrayList<>();
        managedTypes.add(RelationTypeEnum.LEADER.getRelationLabel());
        managedTypes.add(RelationTypeEnum.ASSISTANT.getRelationLabel());
        managedTypes.add(RelationTypeEnum.BP.getRelationLabel());
        managedTypes.add(RelationTypeEnum.REL_PROCESSOR.getRelationLabel());
        managedTypes.add(RelationTypeEnum.MANAGER.getRelationLabel());
        managedTypes.add(RelationTypeEnum.KNOWLEDGE_ADMIN.getRelationLabel());
        managedTypes.add(RelationTypeEnum.MEMBER.getRelationLabel());
        JsonObject<List<TosDepartmentVO>> listJsonObject = tosDepartmentClient.queryManagedDetailDept(personId,managedTypes,true);
        if (!listJsonObject.isSuccess()){
            throw new CrmException("查询失败");
        }

        return listJsonObject.getObjEntity().stream().filter(tosDepartmentVO -> (tosDepartmentVO.getUuidPath().size()-2)>=3).collect(Collectors.toList());

    }

    @Override
    public PageUtils<CrmProjectDirectlyVo> getProjectDirectlyByPersonId(ProjectDirectlyPersonIdPageQuery query) {
        JsonObject<PageUtils<CrmProjectDirectlyVo>> pageUtilsJsonObject = remoteProjectDirectlyClient.pageOfPersonIdByParams(query);
        if (!pageUtilsJsonObject.isSuccess()){
            throw new CrmException("获取项目信息失败");
        }
        return pageUtilsJsonObject.getObjEntity();
    }

    @Override
    public TableDataInfo getContractConfirm(CostContractInfoVo costContractInfoVo) {
        List<String> costContractIds = costContractOriginalMapper.selectList(new QueryWrapper<CostContractOriginal>().lambda().eq(CostContractOriginal::getDelFlag,0)
                .eq(CostContractOriginal::getIsContract,"已确认").eq(CostContractOriginal::getCreateUser,costContractInfoVo.getPersonId()))
                .stream().map(CostContractOriginal::getCostContractId).toList();
        if (CollectionUtils.isEmpty(costContractIds)){
            return new TableDataInfo();
        }
        List<CostContract> costContracts = list(new QueryWrapper<CostContract>().lambda().eq(CostContract::getDelFlag,0).eq(CostContract::getProcessState,2)
                .like(StringUtils.isNotEmpty(costContractInfoVo.getCostContractNumber()),CostContract::getProcessNumber,costContractInfoVo.getCostContractNumber())
                .in(CostContract::getId,costContractIds));
        if (CollectionUtils.isEmpty(costContracts)){
            return new TableDataInfo();
        }
        List<String> supplierIds = costContracts.stream().map(CostContract::getSupplierId).toList();
        List<String> companyIds = costContracts.stream().map(CostContract::getCompanyId).toList();
        Map<String,CostContractRe> costContractReMap = costContractReMapper.selectList(new QueryWrapper<CostContractRe>().select(" cost_contract_id,sum(apply_money) as apply_money").lambda().eq(CostContractRe::getDelFlag,0)
                .in(CostContractRe::getCostContractId,costContractIds).groupBy(CostContractRe::getCostContractId))
                .stream().collect(Collectors.toMap(CostContractRe::getCostContractId,(costContractRe->costContractRe)));
        Map<String,CostPayment> costPaymentMap = costPaymentMapper.selectList(new QueryWrapper<CostPayment>().select(" cost_contract_id,sum(payment_money) as payment_money ").lambda().eq(CostPayment::getDelFlag,0)
                .in(CostPayment::getCostContractId,costContractIds).groupBy(CostPayment::getCostContractId))
                .stream().collect(Collectors.toMap(CostPayment::getCostContractId,(costPayment->costPayment)));

        JsonObject<List<SupplierVO>> supplierJsonObject = remoteSupplierProductService.selectCrmSupplierByIds(supplierIds);
        if(!supplierJsonObject.isSuccess()){
            throw new CrmException("查询供应商信息失败");
        }
        Map<String,SupplierVO> supplierVOMap = supplierJsonObject.getObjEntity()
                .stream().collect(Collectors.toMap(SupplierVO::getId,(supplierVO->supplierVO)));

        JsonObject<List<ContractSignCompanyVO>> companyVOJsonObject = remoteContractReviewConfigService.getBySignCompanyIds(companyIds);
        if (!companyVOJsonObject.isSuccess()){
            throw new CrmException("获取签订公司失败");
        }
        Map<String,ContractSignCompanyVO> companyVOMap = companyVOJsonObject.getObjEntity().stream().collect(Collectors.toMap(ContractSignCompanyVO::getId,(contractSignCompanyVO->contractSignCompanyVO)));


        List<String> personnelIds = costContracts.stream().map(CostContract::getCreateUser).collect(Collectors.toList());
        Map<String,EmployeeVO> personVOMap = getPersonVO(personnelIds)
                .stream().collect(Collectors.toMap(EmployeeVO::getUuid,(o->o)));

        List<CostContractInfoVo> costContractInfoVos = costContracts.stream().map(costContract -> {
            CostContractInfoVo contractInfoVo = HyperBeanUtils.copyProperties(costContract,CostContractInfoVo::new);
            EmployeeVO employeeVO = personVOMap.get(costContract.getCreateUser());
            contractInfoVo.setCostContractId(costContract.getId());
            contractInfoVo.setCostContractNumber(costContract.getProcessNumber());
            contractInfoVo.setSalePersonId(costContract.getCreateUser());
            contractInfoVo.setSalePersonName(employeeVO.getName()+employeeVO.getJobNo());
            contractInfoVo.setDeptName(employeeVO.getDept()!=null ? employeeVO.getDept().getName():"");
            contractInfoVo.setDeptId(employeeVO.getDept()!=null ? employeeVO.getDept().getUuid():"");
            contractInfoVo.setSupplierName(supplierVOMap.get(costContract.getSupplierId()).getSupplierName());
            contractInfoVo.setCostMoney(costContractReMap.get(costContract.getId()).getApplyMoney());
            contractInfoVo.setCompanyName(companyVOMap.get(costContract.getCompanyId()).getCompanyName());
            CostPayment costPayment = costPaymentMap.get(costContract.getId());
            if (costPayment != null){
                contractInfoVo.setCanApplyMoney(costContractReMap.get(costContract.getId()).getApplyMoney().subtract(costPayment.getPaymentMoney()));
            }else{
                contractInfoVo.setCanApplyMoney(costContractReMap.get(costContract.getId()).getApplyMoney());
            }
            contractInfoVo.setPersonId(costContractInfoVo.getPersonId());
            return contractInfoVo;
        }).toList();

        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setTotalCount(new PageInfo<>(costContracts).getTotal());
        tableDataInfo.setList(costContractInfoVos);
        return tableDataInfo;
    }

    @Transactional
    @Override
    public Boolean updateCostContractInfo(CostContractUpdateDTO costContractUpdateDTO) {
        try{
            CostContract costContract = HyperBeanUtils.copyProperties(costContractUpdateDTO.getCostContractInfoDTO(),CostContract::new);
            List<CostContractRe> costContractRes = HyperBeanUtils.copyListProperties(costContractUpdateDTO.getCostContractReInfoDTOS(),CostContractRe::new);
            List<CostContractPaymentClause> costContractPaymentClauses = HyperBeanUtils.copyListProperties(costContractUpdateDTO.getCostContractPaymentClauseInfoDTOS(),CostContractPaymentClause::new);
            costContractMapper.updateById(costContract);
            if(CollectionUtils.isEmpty(costContractRes)){
                costContractReMapper.update(new LambdaUpdateWrapper<CostContractRe>().set(CostContractRe::getDelFlag,1).eq(CostContractRe::getDelFlag,0)
                        .eq(CostContractRe::getCostContractId,costContract.getId()));
            }else{
                List<String> reIds = costContractRes.stream().filter(costContractRe -> StringUtils.isNotEmpty(costContractRe.getId()))
                        .map(CostContractRe::getId).toList();
                costContractReMapper.update(new LambdaUpdateWrapper<CostContractRe>().set(CostContractRe::getDelFlag,1)
                        .eq(CostContractRe::getDelFlag,0).eq(CostContractRe::getCostContractId,costContract.getId())
                        .notIn(CollectionUtils.isNotEmpty(reIds),CostContractRe::getId,reIds));
                costContractReMapper.insertOrUpdate(costContractRes);
            }
            if(CollectionUtils.isEmpty(costContractPaymentClauses)){
                costContractPaymentClauseMapper.update(new LambdaUpdateWrapper<CostContractPaymentClause>().set(CostContractPaymentClause::getDelFlag,1)
                        .eq(CostContractPaymentClause::getDelFlag,0).eq(CostContractPaymentClause::getCostContractId,costContract.getId()));
            }else{
                List<String> paymentClausesIds = costContractPaymentClauses.stream().filter(costContractPaymentClause -> StringUtils.isNotEmpty(costContractPaymentClause.getId()))
                        .map(CostContractPaymentClause::getId).toList();
                costContractPaymentClauseMapper.update(new LambdaUpdateWrapper<CostContractPaymentClause>().set(CostContractPaymentClause::getDelFlag,1)
                        .eq(CostContractPaymentClause::getDelFlag,0).eq(CostContractPaymentClause::getCostContractId,costContract.getId())
                        .notIn(CollectionUtils.isNotEmpty(paymentClausesIds),CostContractPaymentClause::getId,paymentClausesIds));
                costContractPaymentClauseMapper.insertOrUpdate(costContractPaymentClauses);
            }
            return true;
        } catch (RuntimeException e) {
            throw new CrmException("修改失败");
        }
    }

    @Override
    public List<ContractSignCompanyVO> costContractSignCompanyPage(ContractSignCompanyVO contractSignCompanyVO) {
        JsonObject<List<ContractSignCompanyVO>> object = remoteContractReviewConfigService.getBySignCompanyQuery(contractSignCompanyVO);
        Boolean is = ObjectUtils.isEmpty(contractSignCompanyVO.getIsImportant())? false:contractSignCompanyVO.getIsImportant();
        if (!object.isSuccess()){
            throw new CrmException("查询签订公司失败");
        }
        return object.getObjEntity().stream().filter(singCompanyVO->singCompanyVO.getIsImportant()==contractSignCompanyVO.getIsImportant()).toList();
    }

    @Override
    public Boolean getCostContractMoneyByProcessInstanceId(String processInstanceId) {
        CostContract costContract = Optional.ofNullable(getOne(new QueryWrapper<CostContract>().lambda().eq(CostContract::getDelFlag,0).eq(CostContract::getProcessInstanceId,processInstanceId)))
                .orElseThrow(()->new CrmException("流程不存在！"));
//        JsonObject<CostFilingInfo> infoJsonObject = remoteCostFilingService.getCrmCostFilinInfo(costContract.getCostFilingId());
//        if (!infoJsonObject.isSuccess()){
//            throw new CrmException("获取费用备案信息失败");
//        }
        CostFiling costFiling = costFilingMapper.selectOne(new QueryWrapper<CostFiling>().lambda().eq(CostFiling::getDelFlag,0)
                .isNotNull(CostFiling::getProjectId).eq(CostFiling::getId,costContract.getCostFilingId()));
        if (costFiling==null){
            return true;
        }else{
            BigDecimal totalPrice = Optional.ofNullable(remoteProjectOutsourceServiceClient.totalAmount("d522323d8059578afb76aae5baa782fd")).map(JsonObject::getObjEntity)
                    .orElseThrow( () -> new CrmException("获取项目信息失败"));
            CostContractRe costContractRe = costContractReMapper.selectOne(new QueryWrapper<CostContractRe>().select(" cost_contract_id,sum(apply_money) as apply_money").lambda().eq(CostContractRe::getDelFlag,0)
                    .eq(CostContractRe::getCostContractId,costContract.getId()).groupBy(CostContractRe::getCostContractId));
            if(totalPrice.compareTo(costContractRe.getApplyMoney())<0){
                return false;
            }else{
                return true;
            }
        }
    }

    @Override
    public BigDecimal getCostContractMoney(String costFiliingId) {
        List<CostContract> costContracts = list(new QueryWrapper<CostContract>().lambda().eq(CostContract::getCostFilingId,costFiliingId));
        if (CollectionUtils.isEmpty(costContracts)){
            return new BigDecimal(0);
        }else{
            List<String> ids = costContracts.stream().map(CostContract::getId).toList();
            CostContractRe re = costContractReMapper.selectOne(new QueryWrapper<CostContractRe>().select("sum(apply_money) as apply_money").lambda().in(CostContractRe::getCostContractId,ids));
            return re!=null? re.getApplyMoney():new BigDecimal(0);

        }
    }


    /**
     * 将项目中的模板文件拷贝到根目录下
     * @return
     */
    public String copyTempFile(String templeFilePath,String number) {
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream(templeFilePath);
        String tempFileName = System.getProperty("user.home") + "/" + number+"temporary.docx";
        File tempFile = new File(tempFileName);
        try {
            FileUtils.copyInputStreamToFile(inputStream, tempFile);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return tempFile.getPath();
    }

    /**
     * 获取人员信息
     * @param personnelIds
     * @return
     */
    private List<EmployeeVO> getPersonVO(List<String> personnelIds){
        List<EmployeeVO> employeeVOList = Optional.ofNullable(tosEmployeeClient.findByIds(personnelIds).getObjEntity())
                .orElseThrow(()->new CrmException("获取人员信息失败"));

        return employeeVOList;
    }

    /**
     * 根据流程ID获取费用合同信息
     * @param processInstanceId
     * @return
     */
    private ElectronContractOtherDTO getElectronContractOtherDTO(String processInstanceId){
        CostContract costContract = getOne(new QueryWrapper<CostContract>().lambda().eq(CostContract::getDelFlag,0).eq(CostContract::getProcessInstanceId,processInstanceId));
        if (costContract==null){
            throw new CrmException("费用合同不存在");
        }
        CostContractRe costContractRe = costContractReService.getOne(new QueryWrapper<CostContractRe>().select(" sum(apply_money) apply_money ").lambda()
                .eq(CostContractRe::getDelFlag,0).eq(CostContractRe::getCostContractId,costContract.getId()).groupBy(CostContractRe::getCostContractId));
        String applyMoneyCapital = MoneyUtil.numberToUpperCase(costContractRe.getApplyMoney());
        JsonObject<ContractSignCompanyVO> companyVOJsonObject = remoteContractReviewConfigService.getBySignCompanyId(costContract.getCompanyId());
        if (!companyVOJsonObject.isSuccess()){
            throw new CrmException("获取签订公司信息失败");
        }
        JsonObject<SupplierVO> supplierVOJsonObject = remoteSupplierProductService.selectCrmSupplierById(costContract.getSupplierId());
        if (!supplierVOJsonObject.isSuccess()){
            throw new CrmException("获取供应商信息失败");
        }
        ElectronContractOtherDTO electronContractOtherDTO = HyperBeanUtils.copyProperties(costContract,ElectronContractOtherDTO::new);
        electronContractOtherDTO.setApplyMoney(costContractRe.getApplyMoney());
        electronContractOtherDTO.setApplyMoneyCapital(applyMoneyCapital);
        electronContractOtherDTO.setCorpName(companyVOJsonObject.getObjEntity().getCompanyName());
        electronContractOtherDTO.setCorpAddr(companyVOJsonObject.getObjEntity().getLinkAddress());
        electronContractOtherDTO.setCorpPhone(companyVOJsonObject.getObjEntity().getLinkPhone());
        electronContractOtherDTO.setSupplierName(supplierVOJsonObject.getObjEntity().getSupplierName());
        return electronContractOtherDTO;
    }





}
