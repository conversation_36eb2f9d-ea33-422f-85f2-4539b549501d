package com.topsec.crm.flow.core.controllerhidden.borrowforprobation;

import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowFlowDeviceVO;
import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowFlowPartVO;
import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowFlowQuery;
import com.topsec.crm.flow.core.service.BorrowForProbationProductService;
import com.topsec.crm.flow.core.service.BorrowForProbationService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/hidden/borrowForProbation")
@Tag(name = "【借试用内部调用接口】", description = "HiddenBorrowForProbationController")
public class HiddenBorrowForProbationController extends BaseController {

    @Autowired
    private BorrowForProbationService borrowForProbationService;

    @Autowired
    private BorrowForProbationProductService borrowForProbationProductService;

    @PostMapping("/borrowForProbationList")
    JsonObject<List<BorrowFlowPartVO>> borrowForProbationList(@RequestBody BorrowFlowQuery borrowFlowQuery){
        List<BorrowFlowPartVO> result = borrowForProbationService.borrowForProbationList(borrowFlowQuery);
        return new JsonObject<>(result);
    }

    @GetMapping("/getBorrowForProbationDevice")
    JsonObject<List<BorrowFlowDeviceVO>> getBorrowForProbationDevice(@RequestParam String processNumber){
        List<BorrowFlowDeviceVO> result = borrowForProbationProductService.getBorrowForProbationDevice(processNumber);
        return new JsonObject<>(result);
    }
}
