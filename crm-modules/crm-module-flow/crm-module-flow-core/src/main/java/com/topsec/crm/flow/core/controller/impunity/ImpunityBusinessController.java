package com.topsec.crm.flow.core.controller.impunity;

import com.topsec.crm.flow.api.dto.impunity.ImpunityVO;
import com.topsec.crm.flow.core.service.ImpunityService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/impunity")
@Tag(name = "免罚申请-业务", description = "免罚申请-业务")
@RequiredArgsConstructor
@Slf4j
public class ImpunityBusinessController {

    private final ImpunityService impunityService;

    @Operation(summary = "免罚申请详情")
    @PostMapping("/detail")
    @PreAuthorize(hasPermission = "crm_contract_receivable_impunity",dataScope = "crm_contract_receivable_impunity")
    public JsonObject<ImpunityVO> detail(@RequestParam String processInstanceId){
        ImpunityVO detail = impunityService.detail(processInstanceId);
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        Set<String> deptIdList = SetUtils.emptyIfNull(AuthorizeContextHolder.getDataScopeParam().getDeptIdList());
        String deptId = detail.getDeptId();
        Boolean allScope = dataScopeParam.getAllScope();
        if (allScope){
            return new JsonObject<>(detail);
        }else {
            if (deptIdList.contains(deptId)){
                return new JsonObject<>(detail);
            }else {
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }
    }
}
