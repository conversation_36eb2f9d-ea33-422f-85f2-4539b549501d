package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.contract.api.entity.contractexecute.CrmContractProcessInfoVO;
import com.topsec.crm.flow.api.dto.proofOfDebtCollection.ProofOfDebtCollectionVO;
import com.topsec.crm.flow.core.entity.ProofOfDebtCollection;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ProofOfDebtCollectionConvertor {
    ProofOfDebtCollectionConvertor INSTANCE= Mappers.getMapper(ProofOfDebtCollectionConvertor.class);
    @Mapping( target = "contractOwnerId",ignore = true)
    @Mapping(target = "updateUserName", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "signingCompanyName", ignore = true)
    @Mapping(target = "contractProcess", ignore = true)
    @Mapping(target = "saleName", ignore = true)
    @Mapping(target = "saleDeptName", ignore = true)
    @Mapping(target = "finalCustomerName", ignore = true)
    @Mapping(target = "contractCompanyName", ignore = true)
    @Mapping(target = "attachments", ignore = true)
    @Mapping(target = "approvalNode", ignore = true)
    ProofOfDebtCollectionVO entityToVO(ProofOfDebtCollection proofOfDebtCollection);


    ProofOfDebtCollectionVO.ContractProcessVO toContractProcessVO(CrmContractProcessInfoVO crmContractProcessInfoVO);
    List<ProofOfDebtCollectionVO.ContractProcessVO> toContractProcessVO(List<CrmContractProcessInfoVO> crmContractProcessInfoVO);

}
