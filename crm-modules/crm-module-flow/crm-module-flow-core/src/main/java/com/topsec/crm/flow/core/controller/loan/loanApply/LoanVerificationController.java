package com.topsec.crm.flow.core.controller.loan.loanApply;

import com.topsec.crm.flow.api.dto.loan.loanApply.LoanApplyDTO;
import com.topsec.crm.flow.api.dto.loan.loanApply.LoanApplyQuery;
import com.topsec.crm.flow.api.dto.loan.loanVerification.LoanVerificationDTO;
import com.topsec.crm.flow.api.dto.loan.loanVerification.LoanVerificationQuery;
import com.topsec.crm.flow.api.dto.loan.loanVerification.VerificationPageInfo;
import com.topsec.crm.flow.core.service.LoanApplyService;
import com.topsec.crm.flow.core.service.LoanVerificationService;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/business/loanVerification")
@Tag(name = "【借款流程-核销】", description = "loanVerification")
@RequiredArgsConstructor
@Validated
public class LoanVerificationController extends BaseController {

    private final LoanVerificationService loanVerificationService;

    private final LoanApplyService loanApplyService;

    @PostMapping("/verificationPage")
    @Operation(summary = "借款核销列表")
    public JsonObject<PageUtils<VerificationPageInfo>> verificationPage(@RequestBody LoanVerificationQuery req){
        startPage();
        return new JsonObject<>(loanApplyService.loanVerificationPage(req));
    }

    @PostMapping("/saveVerification")
    @Operation(summary = "保存核销信息")
    @PreAuthorize(hasPermission = "crm_loan_verify_update_details")
    public JsonObject<Boolean> saveVerification(@RequestBody LoanVerificationDTO req){
        return new JsonObject<>(loanVerificationService.saveVerification(req));
    }

    @PostMapping("/updateVerification")
    @Operation(summary = "修改核销信息")
    @PreAuthorize(hasPermission = "crm_loan_verify_update_details")
    public JsonObject<Boolean> updateVerification(@RequestBody LoanVerificationDTO req){
        return new JsonObject<>(loanVerificationService.updateVerification(req));
    }

    @GetMapping("/deleteVerification")
    @Operation(summary = "删除核销信息")
    @PreAuthorize(hasPermission = "crm_loan_verify_update_details")
    public JsonObject<Boolean> deleteVerification(@RequestParam String id){
        return new JsonObject<>(loanVerificationService.deleteVerification(id));
    }

    @GetMapping("/getListByLoanProcessId")
    @Operation(summary = "根据流程id查询核销列表")
    public JsonObject<List<LoanVerificationDTO> > getListByLoanProcessId(@RequestParam String loanProcessId){
        return new JsonObject<>(loanVerificationService.getListByLoanProcessId(loanProcessId));
    }

    @PostMapping("/updateLoan")
    @Operation(summary = "修改借款信息")
    @PreAuthorize(hasPermission = "crm_loan_verify_update")
    public JsonObject<Boolean> updateLoan(@RequestBody LoanApplyDTO req){
        return new JsonObject<>(loanApplyService.updateLoanInfo(req));
    }

}
