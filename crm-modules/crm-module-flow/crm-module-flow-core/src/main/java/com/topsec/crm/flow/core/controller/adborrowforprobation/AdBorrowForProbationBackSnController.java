package com.topsec.crm.flow.core.controller.adborrowforprobation;


import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationBackSnVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.service.AdBorrowForProbationBackSnService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 样机借试用产品序列号信息
 */
@RestController
@RequestMapping("/adBorrowForProbationBackSn")
@Tag(description = "/adBorrowForProbationBackSn", name = "样机归还序列号")
public class AdBorrowForProbationBackSnController extends BaseController {

    @Resource
    private AdBorrowForProbationBackSnService adBorrowForProbationBackSnService;

    @PostMapping("/flow/selectBackSnListByProcessId/{processInstanceId}")
    @Operation(summary = "归还流程里样机序列号相关信息列表查询")
    @PreFlowPermission
    public JsonObject<List<AdBorrowForProbationBackSnVO>> selectBackSnVOListByProcessId(@PathVariable String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<AdBorrowForProbationBackSnVO> snVOList = adBorrowForProbationBackSnService.selectBackSnVOListByProcessId(processInstanceId);
        return new JsonObject<>(snVOList);
    }

}
