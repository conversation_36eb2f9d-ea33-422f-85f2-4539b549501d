package com.topsec.crm.flow.core.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDetailDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryLogisticsDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.dto.performancereport.ContractDeliveryProductVO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.performancereport.ScarceGoodsOrderDeliveryProductDTO;
import com.topsec.crm.flow.core.entity.ContractReviewDelivery;
import com.topsec.crm.flow.core.entity.ContractReviewMain;
import com.topsec.crm.flow.core.entity.ReturnExchangeProduct;
import com.topsec.crm.flow.core.entity.ReturnExchangeProductThird;
import com.topsec.crm.flow.core.mapper.ContractReviewDeliveryMapper;
import com.topsec.crm.flow.core.mapstruct.ContractDeliveryConvertor;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CommonUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.tos.common.HyperBeanUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class ContractReviewDeliveryServiceImpl extends ServiceImpl<ContractReviewDeliveryMapper, ContractReviewDelivery> implements ContractReviewDeliveryService {

    @Resource
    private ContractReviewDeliveryDetailOwnService deliveryOwnService;

    @Resource
    private ContractReviewDeliveryDetailThirdService deliveryThirdService;

    @Resource
    private ContractReviewProductOwnService contractProductOwnService;

    @Resource
    private ContractReviewProductThirdService productThirdService;

    @Resource
    private PerformanceReportService performanceReportService;

    @Resource
    private ContractReviewMainService contractReviewMainService;

    @Resource
    private ReturnExchangeProductService returnExchangeOwnService;

    @Resource
    private ReturnExchangeProductThirdService returnExchangeThirdService;

    @Override
    public Boolean saveDeliveryInfo(ContractDeliveryDTO deliveryDTO) {
        String id = saveDelivery(deliveryDTO);
        return StringUtils.isNotEmpty(id);
    }

    @Override
    public Boolean saveReturnDeliveryInfo(ContractDeliveryDTO deliveryDTO) {
        String id = saveDelivery(deliveryDTO);
        return StringUtils.isNotEmpty(id);
    }

    private String saveDelivery(ContractDeliveryDTO dto) {
        final Integer isInvalidContract = dto.getIsInvalidContract();
        final String originalContractNumber = dto.getOriginalContractNumber();
        if (isInvalidContract == 0 && StringUtils.isNotBlank(originalContractNumber)) {
            throw new CrmException("不是作废合同，原合同号不可填入");
        }
        if (isInvalidContract == 1 && StringUtils.isBlank(originalContractNumber)) {
            throw new CrmException("作废合同，原合同号不能为空");
        }
        ContractReviewDelivery delivery = HyperBeanUtils.copyProperties(dto, ContractReviewDelivery::new);
        saveOrUpdate(delivery);
        return delivery.getId();
    }

    @Override
    @Transactional
    public Boolean saveOrUpdateDeliveryDetail(ContractDeliveryDTO deliveryDTO) {
        return executeDeliveryOperation(deliveryDTO, deliveryOwnService::saveBatch, deliveryThirdService::saveBatch);
    }

    @Override
    @Transactional
    public Boolean deleteById(String id) {
        ContractReviewDelivery delivery = baseMapper.selectById(id);
        if (delivery != null){
            if (deliveryOwnService.deleteByDeliveryId(id) && deliveryThirdService.deleteByDeliveryId(id)){
                delivery.setDelFlag(true);
                baseMapper.updateById(delivery);
                return true;
            }
        }
        return false;
    }

    @Override
    public PageUtils<ContractDeliveryDetailDTO> getProductPageByContractId(String contractId, String deliveryId, Integer pageNum, Integer pageSize) {
        List<ContractProductOwnDTO> ownProducts = contractProductOwnService.productOwnInfoTileByContractId(contractId, true);
        List<ContractProductThirdDTO> thirdProducts = productThirdService.productThirdInfoByContractId(contractId, true);

        List<ContractDeliveryDetailDTO> ownDetails = ContractDeliveryConvertor.INSTANCE.contractToOwnDTOList(ownProducts);
        List<ContractDeliveryDetailDTO> thirdDetails = ContractDeliveryConvertor.INSTANCE.contractToThirdDTOList(thirdProducts);

        // 自有产品
        processAndEnrichDetails(
                ownDetails,
                deliveryId,
                deliveryOwnService::getOwnListByDeliveryId,
                deliveryOwnService::getQuantityByContractProductId,
                deliveryOwnService::getDetailById,
                ContractDeliveryDetailDTO::getContractProductId,
                ContractDeliveryDetailDTO.ProductType.OWN.getCode()
        );

        // 第三方产品
        processAndEnrichDetails(
                thirdDetails,
                deliveryId,
                deliveryThirdService::getThirdListByDeliveryId,
                deliveryThirdService::getQuantityByContractProductId,
                deliveryThirdService::getDetailById,
                ContractDeliveryDetailDTO::getContractProductId,
                ContractDeliveryDetailDTO.ProductType.THIRD.getCode()
        );

        List<ContractDeliveryDetailDTO> allDetails = Stream.of(ownDetails, thirdDetails)
                .flatMap(List::stream)
                .toList();

        List<ContractDeliveryDetailDTO> pagedList = CommonUtils.subListPage(putTipProcessNumber(allDetails), pageSize, pageNum);
        return new PageUtils<>(pagedList, allDetails.size(), pageSize, pageNum);
    }

    @Override
    public PageUtils<ContractDeliveryDetailDTO> getProductPageByReturnChangeId(String processInstanceId, String deliveryId) {
        List<ReturnExchangeProduct> ownProducts = returnExchangeOwnService.listNewProductByProcessInstanceId(processInstanceId);
        List<ReturnExchangeProductThird> thirdProducts = returnExchangeThirdService.listNewProductByProcessInstanceId(processInstanceId);

        List<ContractDeliveryDetailDTO> ownDetails = ContractDeliveryConvertor.INSTANCE.returnChangetoOwnDTOList(ownProducts);
        List<ContractDeliveryDetailDTO> thirdDetails = ContractDeliveryConvertor.INSTANCE.returnChangetoThirdDTOList(thirdProducts);

        // 自有产品
        processAndEnrichDetails(
                ownDetails,
                deliveryId,
                deliveryOwnService::getOwnListByDeliveryId,
                deliveryOwnService::getQuantityByReturnChangeProductId,
                deliveryOwnService::getDetailById,
                ContractDeliveryDetailDTO::getReturnChangeProductId,
                ContractDeliveryDetailDTO.ProductType.OWN.getCode()
        );

        // 第三方产品
        processAndEnrichDetails(
                thirdDetails,
                deliveryId,
                deliveryThirdService::getThirdListByDeliveryId,
                deliveryThirdService::getQuantityByReturnChangeProductId,
                deliveryThirdService::getDetailById,
                ContractDeliveryDetailDTO::getReturnChangeProductId,
                ContractDeliveryDetailDTO.ProductType.THIRD.getCode()
        );

        List<ContractDeliveryDetailDTO> allDetails = Stream.of(ownDetails, thirdDetails)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        PageUtils<ContractDeliveryDetailDTO> pageUtils = new PageUtils<>();
        pageUtils.setTotalCount((int) new PageInfo<>(allDetails).getTotal());
        pageUtils.setList(HyperBeanUtils.copyListProperties(allDetails, ContractDeliveryDetailDTO::new));
        return pageUtils;
    }

    private <T> void processAndEnrichDetails(
            List<ContractDeliveryDetailDTO> detailList,
            String deliveryId,
            Function<String, List<ContractDeliveryDetailDTO>> existingDetailsService,
            Function<String, Long> quantityService,
            Function<String, ContractDeliveryDetailDTO> detailByIdService,
            Function<ContractDeliveryDetailDTO, String> productIdExtractor,
            String productType
    ) {
        // 获取已存在的产品信息
        Map<String, ContractDeliveryDetailDTO> existingMap = existingDetailsService.apply(deliveryId).stream()
                .collect(Collectors.toMap(productIdExtractor, item -> item));

        // 组装产品信息
        detailList.forEach(item -> {
            String productId = productIdExtractor.apply(item);
            ContractDeliveryDetailDTO existingItem = existingMap.get(productId);

            item.setId(existingItem != null ? existingItem.getId() : null);
            item.setProductType(productType);
            item.setDeliveryId(existingItem != null ? existingItem.getDeliveryId() : null);
            item.setDeliveryQuantity(existingItem != null ? existingItem.getDeliveryQuantity() : 0L);

            Long ownQuantity = quantityService.apply(productId);
            putOptionalQuantity(item, ownQuantity, detailByIdService.apply(item.getId()));
        });
    }

    // 处理可选数量
    private void putOptionalQuantity(ContractDeliveryDetailDTO item, Long ownQuantity, ContractDeliveryDetailDTO detailById) {
        item.setOptionalQuantity(Optional.ofNullable(item.getProductQuantity()).orElse(0L) - Optional.ofNullable(ownQuantity).orElse(0L));
        if (StringUtils.isNotEmpty(item.getId())){
            Long deliveryQuantity = detailById.getDeliveryQuantity();
            item.setOptionalQuantity(item.getOptionalQuantity() + Optional.ofNullable(deliveryQuantity).orElse(0L));
        }
    }

    /**
     * 添加专项备货流程单号
     */
    private List<ContractDeliveryDetailDTO> putTipProcessNumber(List<ContractDeliveryDetailDTO> list) {
        ITargetedInventoryPreparationMainService bean = SpringUtil.getBean(ITargetedInventoryPreparationMainService.class);
        if (list == null || list.isEmpty()) {
            return list;
        }
        List<String> productIDs = list.stream()
                .map(ContractDeliveryDetailDTO::getProjectProductId)
                .filter(productId -> productId != null && !productId.isEmpty())
                .toList();
        if (productIDs.isEmpty()) {
            return list;
        }
        Map<String, List<String>> stockUpNumbers = bean.getStockUpNumbers(productIDs);
        list.forEach(item -> {
            String productId = item.getProjectProductId();
            List<String> processNumbers = (productId != null) ? stockUpNumbers.getOrDefault(productId, Collections.emptyList()) : Collections.emptyList();
            item.setTipProcessNumber(processNumbers);
        });
        return list;
    }


    @Override
    public TableDataInfo getContractDeliveryProductPage(String deliveryId) {

        // 并发获取发货信息
        CompletableFuture<List<ContractDeliveryDetailDTO>> ownFuture = CompletableFuture.supplyAsync(() -> deliveryOwnService.getOwnListByDeliveryId(deliveryId));
        CompletableFuture<List<ContractDeliveryDetailDTO>> thirdFuture = CompletableFuture.supplyAsync(() -> deliveryThirdService.getThirdListByDeliveryId(deliveryId));

        List<ContractDeliveryDetailDTO> ownDTO = ownFuture.join();
        List<ContractDeliveryDetailDTO> thirdDTO = thirdFuture.join();

        // 并发获取合同产品信息
        CompletableFuture<Map<String, ContractProductOwnDTO>> ownMapFuture = CompletableFuture.supplyAsync(() ->
                contractProductOwnService.getByOwnIdBatch(ownDTO.stream().map(ContractDeliveryDetailDTO::getContractProductId).collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(ContractProductOwnDTO::getId, item -> item))
        );
        CompletableFuture<Map<String, ContractProductThirdDTO>> thirdMapFuture = CompletableFuture.supplyAsync(() ->
                productThirdService.getByThirdIdBatch(thirdDTO.stream().map(ContractDeliveryDetailDTO::getContractProductId).collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(ContractProductThirdDTO::getId, item -> item))
        );

        Map<String, ContractProductOwnDTO> ownMap = ownMapFuture.join();
        Map<String, ContractProductThirdDTO> thirdMap = thirdMapFuture.join();

        // 处理自有产品信息
        ownDTO.forEach(item -> {
            ContractProductOwnDTO ownProduct = ownMap.get(item.getContractProductId());
            if (ownProduct != null) {
                item.setProductType(ContractDeliveryDetailDTO.ProductType.OWN.getCode());
                item.setProductName(ownProduct.getProductName());
                item.setPnCode(ownProduct.getPnCode());
                item.setStuffCode(ownProduct.getStuffCode());
                item.setProductQuantity(ownProduct.getProductNum());
                item.setProductId(ownProduct.getProductId());
                item.setProjectProductId(ownProduct.getProjectProductOwnId());
            }
        });

        // 处理第三方产品信息
        thirdDTO.forEach(item -> {
            ContractProductThirdDTO thirdProduct = thirdMap.get(item.getContractProductId());
            if (thirdProduct != null) {
                item.setProductType(ContractDeliveryDetailDTO.ProductType.THIRD.getCode());
                item.setProductQuantity(thirdProduct.getProductNum());
                item.setProductName(thirdProduct.getProductName());
                item.setStuffCode(thirdProduct.getStuffCode());
                item.setProductId(thirdProduct.getProductId());
                item.setProjectProductId(thirdProduct.getProjectProductThirdId());
            }
        });

        // 合并并过滤数据
        List<ContractDeliveryDetailDTO> collect = Stream.of(ownDTO, thirdDTO)
                .flatMap(List::stream)
                .filter(item -> item.getProductQuantity() != null)
                .collect(Collectors.toList());

        if (!collect.isEmpty()) {
            putTipProcessNumber(collect);
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setTotalCount(collect.size());
            tableDataInfo.setList(collect);
            return tableDataInfo;
        }

        return new TableDataInfo();
    }

    @Override
    public TableDataInfo getReturnChangeDeliveryProductPage(String deliveryId) {

        // 并发获取发货信息
        CompletableFuture<List<ContractDeliveryDetailDTO>> ownFuture = CompletableFuture.supplyAsync(() -> deliveryOwnService.getOwnListByDeliveryId(deliveryId));
        CompletableFuture<List<ContractDeliveryDetailDTO>> thirdFuture = CompletableFuture.supplyAsync(() -> deliveryThirdService.getThirdListByDeliveryId(deliveryId));

        List<ContractDeliveryDetailDTO> ownDTO = ownFuture.join();
        List<ContractDeliveryDetailDTO> thirdDTO = thirdFuture.join();

        // 并发获取合同产品信息
        CompletableFuture<Map<String, ReturnExchangeProduct>> ownMapFuture = CompletableFuture.supplyAsync(() ->
                returnExchangeOwnService.listByReturnExchangeId(ownDTO.stream().map(ContractDeliveryDetailDTO::getReturnChangeProductId).collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(ReturnExchangeProduct::getId, item -> item))
        );
        CompletableFuture<Map<String, ReturnExchangeProductThird>> thirdMapFuture = CompletableFuture.supplyAsync(() ->
                returnExchangeThirdService.listByReturnExchangeId(thirdDTO.stream().map(ContractDeliveryDetailDTO::getReturnChangeProductId).collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(ReturnExchangeProductThird::getId, item -> item))
        );

        Map<String, ReturnExchangeProduct> ownMap = ownMapFuture.join();
        Map<String, ReturnExchangeProductThird> thirdMap = thirdMapFuture.join();
        // 处理自有产品信息
        ownDTO.forEach(item -> {
            ReturnExchangeProduct ownProduct = ownMap.get(item.getReturnChangeProductId());
            if (ownProduct != null) {
                item.setProductType(ContractDeliveryDetailDTO.ProductType.OWN.getCode());
                item.setProductName(ownProduct.getProductName());
                item.setPnCode(ownProduct.getPnCode());
                item.setStuffCode(ownProduct.getStuffCode());
                item.setProductQuantity(Long.valueOf(ownProduct.getProductNum()));
                item.setProductId(ownProduct.getProductId());
            }
        });

        // 处理第三方产品信息
        thirdDTO.forEach(item -> {
            ReturnExchangeProductThird thirdProduct = thirdMap.get(item.getReturnChangeProductId());
            if (thirdProduct != null) {
                item.setProductType(ContractDeliveryDetailDTO.ProductType.THIRD.getCode());
                item.setProductQuantity(Long.valueOf(thirdProduct.getProductNum()));
                item.setProductName(thirdProduct.getProductName());
                item.setStuffCode(thirdProduct.getStuffCode());
                item.setProductId(thirdProduct.getProductId());
            }
        });

        // 合并并过滤数据
        List<ContractDeliveryDetailDTO> collect = Stream.of(ownDTO, thirdDTO)
                .flatMap(List::stream)
                .filter(item -> item.getProductQuantity() != null)
                .collect(Collectors.toList());

        if (!collect.isEmpty()) {
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setTotalCount(collect.size());
            tableDataInfo.setList(collect);
            return tableDataInfo;
        }

        return new TableDataInfo();
    }

    @Override
    public List<ContractDeliveryDTO> getContractDeliveryInfoList(String contractId) {
        List<ContractReviewDelivery> deliveries = baseMapper.selectList(
                new LambdaQueryWrapper<ContractReviewDelivery>()
                        .eq(ContractReviewDelivery::getContractId, contractId)
                        .eq(ContractReviewDelivery::getDelFlag, false)
        );
        List<ContractDeliveryDTO> contractDeliveryDTOS = HyperBeanUtils.copyListProperties(deliveries, ContractDeliveryDTO::new);

        // TODO 暂时mock，与ERP对接后调整
        ListUtils.emptyIfNull(contractDeliveryDTOS).forEach(contractReviewDelivery -> {
            ArrayList<ContractDeliveryLogisticsDTO> logisticsDTOS = new ArrayList<>();
            ContractDeliveryLogisticsDTO contractDeliveryLogisticsDTO = new ContractDeliveryLogisticsDTO();
            contractDeliveryLogisticsDTO.setWaybillNumber("mockSF13456798132");
            contractDeliveryLogisticsDTO.setExpressCompany("mock顺丰速运");
            contractDeliveryLogisticsDTO.setSendingCity("mock湖北省武汉市");
            contractDeliveryLogisticsDTO.setReceivingCity("mock广东省深圳市");
            ArrayList<ContractDeliveryLogisticsDTO.ShippedProductDTO> shippedProductDTOS = new ArrayList<>();
            ContractDeliveryLogisticsDTO.ShippedProductDTO shippedProductDTO = new ContractDeliveryLogisticsDTO.ShippedProductDTO();
            shippedProductDTO.setProductName("mock产品名称");
            shippedProductDTO.setSn("mockSN123456789");
            shippedProductDTO.setPn("mockPN123456789");
            shippedProductDTOS.add(shippedProductDTO);
            contractDeliveryLogisticsDTO.setShippedProductDTOS(shippedProductDTOS);
            logisticsDTOS.add(contractDeliveryLogisticsDTO);
            contractReviewDelivery.setLogisticsDTOS(logisticsDTOS);
        });
        return contractDeliveryDTOS;
    }

    @Override
    public List<ContractDeliveryDTO> getReturnChangeDeliveryInfoList(String returnChangeId) {
        List<ContractReviewDelivery> deliveries = baseMapper.selectList(
                new LambdaQueryWrapper<ContractReviewDelivery>()
                        .eq(ContractReviewDelivery::getReturnChangeId, returnChangeId)
                        .eq(ContractReviewDelivery::getDelFlag, false)
        );
        return HyperBeanUtils.copyListProperties(deliveries, ContractDeliveryDTO::new);
    }

    @Override
    public Boolean isAllDelivery(String contractId) {
        long ownQuantity = getOwnProductQuantity(contractId);
        long thirdQuantity = getThirdProductQuantity(contractId);
        long totalProducts = ownQuantity + thirdQuantity;

        long deliveredQuantity = getDeliveredQuantity(getContractDeliveryInfoList(contractId));
        return totalProducts == deliveredQuantity;
    }

    @Override
    public Boolean returnChangeIsAllDelivery(String processInstanceId, String returnChangeId) {
        long ownQuantity = getReturnExchangeOwnQuantity(processInstanceId);
        long thirdQuantity = getReturnExchangeThirdQuantity(processInstanceId);
        long totalProducts = ownQuantity + thirdQuantity;

        long deliveredQuantity = getDeliveredQuantity(getReturnChangeDeliveryInfoList(returnChangeId));
        return totalProducts == deliveredQuantity;
    }

    private long getOwnProductQuantity(String contractId) {
        List<ContractProductOwnDTO> ownProducts = contractProductOwnService.productOwnInfoTileByContractId(contractId, true);
        return Optional.ofNullable(ownProducts).orElseGet(Collections::emptyList).stream()
                .mapToLong(ContractProductOwnDTO::getProductNum)
                .sum();
    }

    private long getThirdProductQuantity(String contractId) {
        List<ContractProductThirdDTO> thirdProducts = productThirdService.productThirdInfoByContractId(contractId, true);
        return Optional.ofNullable(thirdProducts).orElseGet(Collections::emptyList).stream()
                .mapToLong(ContractProductThirdDTO::getProductNum)
                .sum();
    }

    private long getReturnExchangeOwnQuantity(String processInstanceId) {
        List<ReturnExchangeProduct> ownProducts = returnExchangeOwnService.listNewProductByProcessInstanceId(processInstanceId);
        return Optional.ofNullable(ownProducts).orElseGet(Collections::emptyList).stream()
                .mapToInt(ReturnExchangeProduct::getProductNum)
                .sum();
    }

    private long getReturnExchangeThirdQuantity(String processInstanceId) {
        List<ReturnExchangeProductThird> thirdProducts = returnExchangeThirdService.listNewProductByProcessInstanceId(processInstanceId);
        return Optional.ofNullable(thirdProducts).orElseGet(Collections::emptyList).stream()
                .mapToInt(ReturnExchangeProductThird::getProductNum)
                .sum();
    }

    /**
     * 通用方法：从发货信息中提取发货总量
     */
    private long getDeliveredQuantity(List<ContractDeliveryDTO> deliveryInfoList) {
        return Optional.ofNullable(deliveryInfoList).orElseGet(Collections::emptyList).stream()
                .mapToLong(item -> item.getDeliveryQuantity() != null ? item.getDeliveryQuantity() : 0L)
                .sum();
    }

    @Override
    @Transactional
    public void autoOrderDeliverySave(String contractId,String performanceReportFlowId,List<ScarceGoodsOrderDeliveryProductDTO> productList) {
        if (StringUtils.isBlank(contractId) || StringUtils.isBlank(performanceReportFlowId)){
            throw new CrmException("合同ID或绩效报告ID为空");
        }
        if (CollectionUtils.isEmpty(productList)){
            throw new CrmException("订单产品为空");
        }
        // 业绩上报发货地址
        Map<String, PerformanceReportContractDeliveryDTO> performanceReportDeliveryMap = performanceReportService
                .getContractDeliveryVOList(performanceReportFlowId)
                .stream()
                .collect(Collectors.toMap(PerformanceReportContractDeliveryDTO::getId, item -> item));
        // 合同产品
        Map<String, ContractProductOwnDTO> contractProductOwnMap = contractProductOwnService
                .productOwnInfoTileByContractId(contractId, true)
                .stream()
                .collect(Collectors.toMap(ContractProductOwnDTO::getProjectProductOwnId, item -> item));
        // productList根据发货ID分组
        Map<String, List<ScarceGoodsOrderDeliveryProductDTO>> productListMap = productList.stream()
                .collect(Collectors.groupingBy(ScarceGoodsOrderDeliveryProductDTO::getDeliveryRecordId));
        // 遍历 productListMap 的每个 key
        for (String deliveryRecordId : productListMap.keySet()) {
            // 获取对应的 PerformanceReportContractDeliveryDTO
            PerformanceReportContractDeliveryDTO reportContractDeliveryDTO = performanceReportDeliveryMap.get(deliveryRecordId);
            if (reportContractDeliveryDTO == null) {
                throw new CrmException("业绩上报发货地址为空");
            }
            // 创建 ContractDeliveryDTO 对象
            ContractDeliveryDTO contractDeliveryDTO = ContractDeliveryConvertor.INSTANCE.performanceToContractDeliveryDTO(reportContractDeliveryDTO);
            contractDeliveryDTO.setContractId(contractId);
            contractDeliveryDTO.setPerformanceReportId(performanceReportFlowId);
            contractDeliveryDTO.setPerformanceDeliveryId(deliveryRecordId);
            contractDeliveryDTO.setIsInvalidContract(0);
            // 创建 List<ContractDeliveryDetailDTO> 用于存储明细数据
            List<ContractDeliveryDetailDTO> deliveryDetailDTOS = new ArrayList<>();
            // 遍历当前 deliveryRecordId 对应的产品列表
            for (ScarceGoodsOrderDeliveryProductDTO item : productListMap.get(deliveryRecordId)) {
                ContractProductOwnDTO productOwnDTO = contractProductOwnMap.get(item.getZdProjectProductId());
                if (productOwnDTO == null) {
                    throw new CrmException("合同产品为空");
                }
                // 创建 ContractDeliveryDetailDTO 对象
                ContractDeliveryDetailDTO detailDTO = new ContractDeliveryDetailDTO();
                detailDTO.setProductType(ContractDeliveryDetailDTO.ProductType.OWN.getCode());
                detailDTO.setProductId(productOwnDTO.getProductId());
                detailDTO.setProductQuantity(productOwnDTO.getProductNum());
                detailDTO.setContractProductId(productOwnDTO.getId());
                detailDTO.setDeliveryQuantity(item.getDeliveryQuantity().longValue());
                // 将 detailDTO 添加到 deliveryDetailDTOS 中
                deliveryDetailDTOS.add(detailDTO);
            }
            // 发货产品总数量
            contractDeliveryDTO.setDeliveryQuantity(deliveryDetailDTOS.stream().mapToLong(ContractDeliveryDetailDTO::getDeliveryQuantity).sum());
            ContractReviewDelivery delivery = HyperBeanUtils.copyProperties(contractDeliveryDTO, ContractReviewDelivery::new);
            // 保存ContractDeliveryDTO
            save(delivery);
            contractDeliveryDTO.setDetailDTOS(deliveryDetailDTOS);
            contractDeliveryDTO.setId(delivery.getId());
            saveOrUpdateDeliveryDetail(contractDeliveryDTO);
        }
    }

    @Override
    @Transactional
    public void returnChangeLaunchSave(List<PerformanceReportContractDeliveryDTO> deliveryList, String returnChangeId) {

        if (StringUtils.isBlank(returnChangeId)){
            throw new CrmException("退货换货ID为空");
        }
        if (deliveryList == null || deliveryList.isEmpty()){
            throw new CrmException("发货为空");
        }
        // 批量保存发货信息
        deliveryList.forEach(item ->{
            List<ContractDeliveryProductVO> products = item.getDeliveryProducts();
            if (products == null || products.isEmpty()){
                throw new CrmException("发货产品为空");
            }
            ContractDeliveryDTO deliveryDTO = ContractDeliveryConvertor.INSTANCE.performanceToContractDeliveryDTO(item);
            deliveryDTO.setReturnChangeId(returnChangeId);
            List<ContractDeliveryDetailDTO> deliveryDetailDTOS = ContractDeliveryConvertor.INSTANCE.performanceToDetailDTOList(products);
            deliveryDTO.setDetailDTOS(deliveryDetailDTOS);

            //发货信息保存
            String id = saveDelivery(deliveryDTO);
            if (StringUtils.isEmpty(id)) {
                throw new CrmException("保存发货信息失败");
            }
            // 发货明细保存
            deliveryDTO.setId(id);
            saveOrUpdateReturnChangeDetail(deliveryDTO);
        });
    }

    @Override
    @Transactional
    public void handleReturnChangeEffect(Map<String, String> ownMap, Map<String, String> thirdMap, String returnChangeId, String contractId) {
        if (StringUtils.isBlank(returnChangeId) || StringUtils.isBlank(contractId)) {
            throw new CrmException("退货换货ID或合同ID为空");
        }
        if ((ownMap == null || ownMap.isEmpty()) && (thirdMap == null || thirdMap.isEmpty())) {
            throw new CrmException("ownMap 和 thirdMap 不能同时为空");
        }
        if (ownMap != null && !ownMap.isEmpty()) {
            deliveryOwnService.handleReturnChangeEffect(ownMap);
        }
        if (thirdMap != null && !thirdMap.isEmpty()) {
            deliveryThirdService.handleReturnChangeEffect(thirdMap);
        }
        // 多个发货
        boolean update = lambdaUpdate()
                .eq(ContractReviewDelivery::getReturnChangeId, returnChangeId)
                .set(ContractReviewDelivery::getContractId, contractId)
                .update();
        if (!update) {
            throw new CrmException("发货生效失败，returnChangeId=" + returnChangeId + ", contractId=" + contractId);
        }
    }

    @Override
    @Transactional
    public void handleReturnChangeRollback(String returnChangeId) {

        List<ContractReviewDelivery> deliveries = baseMapper.selectList(new LambdaQueryWrapper<ContractReviewDelivery>()
                .eq(ContractReviewDelivery::getReturnChangeId, returnChangeId)
                .eq(ContractReviewDelivery::getDelFlag, false)
        );
        if (deliveries == null || deliveries.isEmpty()) {
            log.warn("未找到对应的交付记录，returnChangeId: {}", returnChangeId);
            return;
        }
        boolean update = lambdaUpdate()
                .eq(ContractReviewDelivery::getReturnChangeId, returnChangeId)
                .eq(ContractReviewDelivery::getDelFlag, false)
                .set(ContractReviewDelivery::getContractId, null)
                .update();
        if (!update) {
            log.warn("回滚退换货发货失败，returnChangeId: {}", returnChangeId);
        }
        List<String> ids = deliveries.stream().map(ContractReviewDelivery::getId).toList();
        deliveryOwnService.handleReturnChangeRollback(ids);
        deliveryThirdService.handleReturnChangeRollback(ids);
        log.info("退换货发货回滚完成，共处理 {} 条记录", deliveries.size());
    }

    @Override
    public List<ContractDeliveryDTO> getDeliveryListByProcessInstanceIds(List<String> processInstanceIdList) {
        // 根据流程编号获取所有合同ID
        List<String> list = contractReviewMainService.getByProcessInstanceIds(processInstanceIdList).stream().map(ContractReviewMain::getId).toList();
        ArrayList<ContractDeliveryDTO> result = new ArrayList<>();
        list.forEach(item ->{
            List<ContractDeliveryDTO> deliveryInfoList = getContractDeliveryInfoList(item);
            result.addAll(deliveryInfoList);
        });
        return result;
    }

    @Override
    @Transactional
    public void deleteByProductIds(List<String> ownProductIds, List<String> thirdProductIds) {
            // 删除自有产品并获取结果，如果 ownProductIds 为空，返回空 Map
            Map<String, Long> deletedOwnByProductIds = ownProductIds != null && !ownProductIds.isEmpty()
                    ? deliveryOwnService.deleteOwnByProductIds(ownProductIds)
                    : Collections.emptyMap();

            // 删除第三方产品并获取结果，如果 thirdProductIds 为空，返回空 Map
            Map<String, Long> deleteThirdByProductIds = thirdProductIds != null && !thirdProductIds.isEmpty()
                    ? deliveryThirdService.deleteThirdByProductIds(thirdProductIds)
                    : Collections.emptyMap();

            // 合并两个Map
            Map<String, Long> result = Stream.concat(
                    deletedOwnByProductIds.entrySet().stream(),
                    deleteThirdByProductIds.entrySet().stream()
            ).collect(Collectors.groupingBy(
                    Map.Entry::getKey,
                    Collectors.summingLong(Map.Entry::getValue)
            ));

            // 更新deliveryQuantity
            // TODO 删除的数量超过总发货数量时,提示异常
            result.forEach((key, value) -> {
                ContractReviewDelivery delivery = getById(key);
                if (delivery != null) {
                    delivery.setDeliveryQuantity(delivery.getDeliveryQuantity() - value);
                    updateById(delivery);
                }
            });
            // TODO 数量更新后如果为0,删除整个发货单
    }

    @Override
    @Transactional
    public Boolean saveOrUpdateReturnChangeDetail(ContractDeliveryDTO deliveryDTO) {
        return executeDeliveryOperation(deliveryDTO, (list, id) -> deliveryOwnService.returnChangeSave(list, id),
                (list, id) -> deliveryThirdService.returnChangeSave(list, id));
    }

    /**
     * 公共保存明细方法
     * @param deliveryDTO 保存明细数据
     * @param ownSaveFunc 自有产品保存方法
     * @param thirdSaveFunc 第三方产品保存方法
     * @return 保存结果
     */
    private Boolean executeDeliveryOperation(ContractDeliveryDTO deliveryDTO,
                                             BiFunction<List<ContractDeliveryDetailDTO>, String, Boolean> ownSaveFunc,
                                             BiFunction<List<ContractDeliveryDetailDTO>, String, Boolean> thirdSaveFunc) {

        // 拷贝数据
        ContractReviewDelivery delivery = HyperBeanUtils.copyProperties(deliveryDTO, ContractReviewDelivery::new);

        // 获取详情
        List<ContractDeliveryDetailDTO> detailDTOS = Optional.ofNullable(deliveryDTO.getDetailDTOS())
                .orElse(Collections.emptyList());

        // 根据type分类
        Map<Boolean, List<ContractDeliveryDetailDTO>> partitioned = detailDTOS.stream()
                .collect(Collectors.partitioningBy(item -> ContractDeliveryDetailDTO.ProductType.OWN.getCode().equals(item.getProductType())));

        // 获取自有产品
        List<ContractDeliveryDetailDTO> ownList = partitioned.get(true);

        // 获取第三方产品
        List<ContractDeliveryDetailDTO> thirdList = partitioned.get(false);

        // 执行保存
        if (ownSaveFunc.apply(ownList, deliveryDTO.getId()) && thirdSaveFunc.apply(thirdList, deliveryDTO.getId())) {
            delivery.setDeliveryQuantity(
                    deliveryOwnService.getOwnQuantityByDeliveryId(delivery.getId()) + deliveryThirdService.getThirdQuantityByDeliveryId(delivery.getId())
            );
            return updateById(delivery);
        }
        return false;
    }
}
