package com.topsec.crm.flow.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.topsec.crm.flow.api.dto.teamBuilding.*;
import com.topsec.crm.flow.api.dto.teamBuilding.input.TeamBuildingProjectManagerVO;
import com.topsec.crm.flow.api.dto.teamBuilding.input.TeamBuildingVO;
import com.topsec.crm.flow.core.entity.TeamBuilding;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.tos.common.vo.TosDepartmentVO;
import com.topsec.tos.common.vo.tree.Triple;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【team_building(团队组建流程主表)】的数据库操作Service
* @createDate 2024-06-12 20:14:59
*/
public interface TeamBuildingService extends IService<TeamBuilding> {

    /**
     * 获取项目信息
     * @param projectId 项目id
     * @return 项目信息
     */
    ProjectInfoVO getProjectInfoVO(String projectId);

    /**
     * 可以通过namePath字段，进行体系分组；一级部门namePath .size 为3，二级部门为4，以此类推
     * @return 获取部门信息
     */
    List<TeamBuildingDepartmentInfoVO> listNotDeleted();

    /**
     * 获取部门信息
     * @param deptId 部门id
     * @return
     */
    TosDepartmentVO findById(String deptId);

    /**
     * 各部门负责人
     * @param processInstanceId
     * @return
     */
    List<EmployeeVO> getDepartmentLeaders(String processInstanceId);



    /**
     * 还未指派角色的各部门负责人
     * @param processInstanceId
     * @return
     */
    List<EmployeeVO> getDepartmentLeadersNoAssign(String processInstanceId);
    /**
     * 获取部门售前人员列表
     * @param deptId
     * @return
     */

    List<EmployeeVO> getPreSales(String deptId);

    /**
     * 是否产线审批
     */
    boolean isProductLineApproval(String teamBuildingId);

    /**
     * 项目经理出自部门列表
     * @param teamBuildingId
     * @return
     */

    List<TeamBuildingProjectManagerVO> proposeProjectManagers(@RequestParam String teamBuildingId);


    /**
     * 建议项目经理部门
     * @param teamBuildingProjectManagerVO
     * @return
     */
    Boolean proposeProjectManagerDept(TeamBuildingProjectManagerVO teamBuildingProjectManagerVO);


    /**
     * 获取管辖部门
     * @param teamBuildingId
     * @return
     */
    Map<String,List<Triple<String,String,String>>> jurisdictionalDeptList(String teamBuildingId, String personId);


    /**
     * 获取团队组建资料
     * @param teamBuildingId
     * @return
     */
    TeamBuildingDetailVO teamBuildingBaseInfo(String teamBuildingId);

    /**
     * 08QA项目汇总
     * @param teamBuildingVO
     * @return
     */
    Boolean summaryDescription(TeamBuildingVO teamBuildingVO);

    /**
     * 是否发起了团队组建流程
     * @param projectId 项目id
     * @return
     */
    TeamBuildingProcessVO teamBuildingProcessIsLaunch(String projectId);

    /**
     *
     * @param processInstanceId
     * @return
     */

    TeamBuildingProcessVO getTeamBuildingProcessInfo(String processInstanceId);

    /**
     * 团队组建修改
     * @param launchDTO
     * @return
     */
    Boolean update(TeamBuildingDTO launchDTO);

    /**
     * 获取项目审批的项目经理
     * @param processInstanceId
     * @return
     */
    EmployeeVO getProjectManagerApproved(String processInstanceId);



    /**
     * 04步办理人
     * @param teamBuildingId
     * @return
     */
    List<EmployeeVO> applicantStepOfFourth(String teamBuildingId);

    /**
     * 获取涉及部门的一级负责人
     * @param processInstanceId
     * @return
     */
    List<EmployeeVO> applicantStepOfThird(String processInstanceId);

    /**
     * 07步办理人
     * @param processInstanceId
     * @return
     */
    EmployeeVO applicantStepOfSeventh(String processInstanceId);

    /**
     * 08步办理人
     * @param processInstanceId
     * @return
     */
    EmployeeVO applicantStepOfEighth(String processInstanceId);

    /**
     * 不能办理原因
     * @param teamBuildingId
     * @param personId
     * @return
     */

    ReasonForUnableVO reasonForUnapproved(String teamBuildingId, String personId);

}
