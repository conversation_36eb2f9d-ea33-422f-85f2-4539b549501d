package com.topsec.crm.flow.core.controller.biddingMonitoring;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.util.WebFilenameUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.dto.biddingMonitoring.BiddingMonitoringFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.biddingMonitoring.BiddingMonitoringInfoExportDTO;
import com.topsec.crm.flow.api.dto.biddingMonitoring.VO.BiddingMonitoringFlowDetailVO;
import com.topsec.crm.flow.api.dto.biddingMonitoring.VO.BiddingMonitoringInfoListVO;
import com.topsec.crm.flow.api.dto.biddingMonitoring.VO.BiddingMonitoringPageQuery;
import com.topsec.crm.flow.core.entity.BiddingMonitoringFlow;
import com.topsec.crm.flow.core.entity.BiddingMonitoringInfo;
import com.topsec.crm.flow.core.process.impl.BiddingMonitoringProcessService;
import com.topsec.crm.flow.core.service.BiddingMonitoringFlowService;
import com.topsec.crm.flow.core.service.BiddingMonitoringInfoService;
import com.topsec.crm.framework.common.bean.AreaVO;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.operation.api.RemoteAreaService;
import com.topsec.crm.operation.api.RemoteIndustryService;
import com.topsec.crm.operation.api.entity.CrmIndustryVO;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.TosDepartmentVO;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2025-03-21  11:41
 * @Description:
 */

@RestController
@RequestMapping("/business/biddingMonitoringFlow")
@Tag(name = "商机审批-招投标信息监测-业务", description = "/biddingMonitoringFlow")
@RequiredArgsConstructor
@Validated
public class BiddingMonitoringFlowBusinessController extends BaseController {

    private final BiddingMonitoringFlowService biddingMonitoringFlowService;

    private final BiddingMonitoringProcessService biddingMonitoringProcessService;

    private final BiddingMonitoringInfoService biddingMonitoringInfoService;

    private final RemoteCustomerService remoteCustomerService;


    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    private final RemoteIndustryService remoteIndustryService;

    private final RemoteAreaService remoteAreaService;

    private final TosDepartmentClient tosDepartmentClient;




    @GetMapping(value = "/findByName")
    @Operation(summary = "查询客户基本信息")
    @PreAuthorize(hasPermission = "crm_bidding_monitoring",dataScope = "crm_bidding_monitoring")
    public JsonObject<CrmCustomerVo> findByName(@RequestParam String name)
    {
       return remoteCustomerService.findByName(name);
    }



    @PreAuthorize(hasPermission = "crm_bidding_monitoring", dataScope = "crm_bidding_monitoring")
    @GetMapping("/menuDetail")
    @Operation(summary = "商机审批-招投标详情(菜单)")
    public JsonObject<BiddingMonitoringFlowDetailVO> menuDetail(String id) {
        BiddingMonitoringFlow byId = biddingMonitoringFlowService.getById(id);

        if (hasAccessToBiddingMonitoring(byId)) {
            BiddingMonitoringFlowDetailVO detailVO = biddingMonitoringFlowService.detail(id, null);
            return new JsonObject<>(detailVO);
        } else {
            return new JsonObject<>(null);
        }
    }

    private boolean hasAccessToBiddingMonitoring(BiddingMonitoringFlow flow) {
        List<String> personIdList = Optional.ofNullable(PreAuthorizeAspect.getDataScopeParam())
                .map(param -> param.getPersonIdList())
                .orElse(Collections.emptySet())
                .stream().collect(Collectors.toList());

        return CollectionUtils.isEmpty(personIdList)
                || Optional.ofNullable(flow).map(BiddingMonitoringFlow::getCreateUser).filter(personIdList::contains).isPresent()
                || Optional.ofNullable(flow).map(BiddingMonitoringFlow::getFollowUpPerson).filter(personIdList::contains).isPresent();
    }



    @PreAuthorize(hasPermission = "crm_bidding_monitoring_update",dataScope = "crm_bidding_monitoring_update")
    @PutMapping("/updateBiddingMonitoringInfo")
    @Operation(summary = "修改信息")
    public JsonObject<Boolean> updateBiddingMonitoringInfo(@RequestBody BiddingMonitoringFlowLaunchDTO launchDTO) {

        boolean update = biddingMonitoringInfoService.lambdaUpdate()
                .eq(BiddingMonitoringInfo::getId, launchDTO.getId())
                .set(StringUtils.isNotBlank(launchDTO.getCustomerName()), BiddingMonitoringInfo::getBusinessUltimate, launchDTO.getCustomerName())
                .set(StringUtils.isNotBlank(launchDTO.getCustomerId()), BiddingMonitoringInfo::getCustomerId, launchDTO.getCustomerId())
                .set(StringUtils.isNotBlank(launchDTO.getProvince()), BiddingMonitoringInfo::getProvince, launchDTO.getProvince())
                .set(StringUtils.isNotBlank(launchDTO.getCity()), BiddingMonitoringInfo::getCity, launchDTO.getCity())
                .set(StringUtils.isNotBlank(launchDTO.getRegion()), BiddingMonitoringInfo::getRegion, launchDTO.getRegion())
                .set(StringUtils.isNotBlank(launchDTO.getFirstLevelIndustryAffiliation()), BiddingMonitoringInfo::getFirstLevelIndustryAffiliation, launchDTO.getFirstLevelIndustryAffiliation())
                .set(StringUtils.isNotBlank(launchDTO.getSecondaryLevelIndustryAffiliation()), BiddingMonitoringInfo::getSecondaryLevelIndustryAffiliation, launchDTO.getSecondaryLevelIndustryAffiliation())
                .set(StringUtils.isNotBlank(launchDTO.getReleaseTime()), BiddingMonitoringInfo::getReleaseTime, launchDTO.getReleaseTime())
                .set(StringUtils.isNotBlank(launchDTO.getBiddingProjectName()), BiddingMonitoringInfo::getBiddingProjectName, launchDTO.getBiddingProjectName())
                .set(StringUtils.isNotBlank(launchDTO.getInvolvingProducts()), BiddingMonitoringInfo::getInvolvingProducts, launchDTO.getInvolvingProducts())
                .set(StringUtils.isNotBlank(launchDTO.getProjectBudget()), BiddingMonitoringInfo::getProjectBudget, launchDTO.getProjectBudget())
                .set(StringUtils.isNotBlank(launchDTO.getBiddingDeadline()), BiddingMonitoringInfo::getBiddingDeadline, launchDTO.getBiddingDeadline())
                .set(StringUtils.isNotBlank(launchDTO.getBiddingWebsite()), BiddingMonitoringInfo::getBiddingWebsite, launchDTO.getBiddingWebsite())
                .set(StringUtils.isNotBlank(launchDTO.getFollowUpDept()), BiddingMonitoringInfo::getFollowUpDept, launchDTO.getFollowUpDept())
                .set(StringUtils.isNotBlank(launchDTO.getFollowUpDeptLeader()),BiddingMonitoringInfo::getFollowUpDeptLeader,launchDTO.getFollowUpDeptLeader())
                .update();
        return new JsonObject<Boolean>(update);
    }



    @PreAuthorize(hasPermission = "crm_bidding_monitoring",dataScope = "crm_bidding_monitoring")
    @PostMapping("/biddingMonitoringPage")
    @Operation(summary = "分页查询招投标检测列表")
    JsonObject<PageUtils<BiddingMonitoringInfoListVO>> biddingMonitoringPage(@RequestBody BiddingMonitoringPageQuery biddingMonitoringPageQuery) {

        PageUtils<BiddingMonitoringInfoListVO> page = biddingMonitoringFlowService.biddingMonitoringPage(biddingMonitoringPageQuery);
        return new JsonObject<>(page);
    }

    @PreAuthorize(hasPermission = "crm_bidding_monitoring_update")
    @PostMapping("/refreshUltimate")
    @Operation(summary = "更新最终用户")
    JsonObject<Boolean> refreshUltimate(@RequestBody BiddingMonitoringPageQuery biddingMonitoringPageQuery) {
        PageUtils<BiddingMonitoringInfoListVO> page = biddingMonitoringFlowService.biddingMonitoringPage(biddingMonitoringPageQuery);
        if (CollectionUtils.isNotEmpty(page.getList())){
            List<BiddingMonitoringInfoListVO> list = page.getList();
            list.forEach(item->{
                if (StringUtils.isNotBlank(item.getBusinessUltimate())){
                    JsonObject<CrmCustomerVo> byName = remoteCustomerService.findByName(item.getBusinessUltimate());
                    if (byName.isSuccess() && byName.getObjEntity() != null){
                        CrmCustomerVo customerVo = byName.getObjEntity();
                        boolean update = biddingMonitoringInfoService.update(new LambdaUpdateWrapper<BiddingMonitoringInfo>()
                                .eq(BiddingMonitoringInfo::getId, item.getId())
                                .set(BiddingMonitoringInfo::getCustomerId, customerVo.getId())
                                .set(BiddingMonitoringInfo::getProvince, customerVo.getProvinceCode())
                                .set(BiddingMonitoringInfo::getCity, customerVo.getCityCode())
                                .set(BiddingMonitoringInfo::getRegion, customerVo.getCountyCode())
                                .set(BiddingMonitoringInfo::getFirstLevelIndustryAffiliation, customerVo.getIndustryId())
                                .set(BiddingMonitoringInfo::getSecondaryLevelIndustryAffiliation, customerVo.getIndustryIdTwo())
                        );
                    }
                }
            });
        }
        return new JsonObject<>(true);
    }




    @PreAuthorize(hasPermission = "crm_bidding_monitoring_update",dataScope = "crm_bidding_monitoring_update")
    @DeleteMapping("/deleteBatchBiddingInfo")
    @Operation(summary = "批量删除商机")
    public JsonObject<Boolean> deleteBatchBiddingInfo(@RequestBody List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            boolean update = biddingMonitoringInfoService.lambdaUpdate().in(BiddingMonitoringInfo::getId, ids)
                    .set(BiddingMonitoringInfo::getDelFlag, 1).update();
            return new JsonObject<Boolean>(update);
        }else{
            return new JsonObject<Boolean>(false);
        }

    }



    @PreAuthorize(hasPermission = "crm_bidding_monitoring_update",dataScope = "crm_bidding_monitoring_update")
    @PutMapping("/updateFollowUpDept")
    @Operation(summary = "修改项目跟进部门")
    public JsonObject<Boolean> updateFollowUpDept(@RequestBody BiddingMonitoringFlowLaunchDTO launchDTO) {
        boolean update = biddingMonitoringInfoService.lambdaUpdate()
                .eq(BiddingMonitoringInfo::getId,launchDTO.getId())
                .set(StringUtils.isNotBlank(launchDTO.getFollowUpDept()),BiddingMonitoringInfo::getFollowUpDept, launchDTO.getFollowUpDept())
                .set(StringUtils.isNotBlank(launchDTO.getFollowUpDeptLeader()),BiddingMonitoringInfo::getFollowUpDeptLeader,launchDTO.getFollowUpDeptLeader())
                .update();
        return new JsonObject<Boolean>(update);
    }



    @PreAuthorize(hasPermission = "crm_bidding_monitoring_export",dataScope = "crm_bidding_monitoring_export")
    @PostMapping("/exportBiddingMonitoringInfo")
    @Operation(summary = "导出招投标检测列表")
    public void exportBiddingMonitoringInfo(@RequestBody BiddingMonitoringPageQuery biddingMonitoringPageQuery, HttpServletResponse response)   {
        biddingMonitoringPageQuery.setPageNum(1);
        biddingMonitoringPageQuery.setPageSize(Integer.MAX_VALUE);
        PageUtils<BiddingMonitoringInfoListVO> page = biddingMonitoringFlowService.biddingMonitoringPage(biddingMonitoringPageQuery);
        List<BiddingMonitoringInfoListVO> biddingMonitoringInfoListVOS = page.getList();
        List<BiddingMonitoringInfoExportDTO> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(biddingMonitoringInfoListVOS)){
            list = HyperBeanUtils.copyListPropertiesByJackson(biddingMonitoringInfoListVOS, BiddingMonitoringInfoExportDTO.class);
            list.forEach(item->{
                Set<ApproveNode> approvalNode = item.getApprovalNode();

                Optional.ofNullable(item.getProjectBudget())
                        .map(budget -> "¥" + budget)
                        .ifPresent(item::setProjectBudget);

                if(CollectionUtils.isNotEmpty(approvalNode)){
                    StringBuilder approvalData = new StringBuilder();
                    approvalNode.forEach(node-> approvalData.append(node.getNodeName()).append(","));
                    item.setApprovalData(approvalData.substring(0,approvalData.length()-1));
                }
                if(StringUtils.isNotBlank(item.getFollowUpDept())){
                    Optional.of(item.getFollowUpDept())
                            .map(tosDepartmentClient::findById)
                            .filter(JsonObject::isSuccess)
                            .map(JsonObject::getObjEntity)
                            .map(TosDepartmentVO::getName)
                            .ifPresent(item::setFollowUpDept);
                }
                if (StringUtils.isNotBlank(item.getCustomerId())){
                    JsonObject<CrmCustomerVo> customerInfo = remoteCustomerService.getCustomerInfo(item.getCustomerId(), "", false);
                    if (customerInfo.isSuccess() && customerInfo.getObjEntity() != null){
                        item.setCustomerName(customerInfo.getObjEntity().getName());
                    }
                    item.setExistCustomer("是");
                }else {
                    item.setExistCustomer("否");
                }
                if(CollectionUtils.isNotEmpty(item.getBidderProvider())){
                    item.setBidderProviders(StringUtils.join(item.getBidderProvider(),","));
                }
                if (StringUtils.isNotBlank(item.getFirstLevelIndustryAffiliation())) {
                    Optional.of(item.getFirstLevelIndustryAffiliation())
                            .map(remoteIndustryService::industryByUuid)
                            .filter(JsonObject::isSuccess)
                            .map(JsonObject::getObjEntity)
                            .map(CrmIndustryVO::getName)
                            .ifPresent(item::setFirstLevelIndustryAffiliation);
                }

                if (StringUtils.isNotBlank(item.getSecondaryLevelIndustryAffiliation())) {
                    Optional.of(item.getSecondaryLevelIndustryAffiliation())
                            .map(remoteIndustryService::industryByUuid)
                            .filter(JsonObject::isSuccess)
                            .map(JsonObject::getObjEntity)
                            .map(CrmIndustryVO::getName)
                            .ifPresent(item::setSecondaryLevelIndustryAffiliation);
                }

                if (StringUtils.isNotBlank(item.getProvince())) {
                    Optional.of(item.getProvince())
                            .map(remoteAreaService::queryByCode)
                            .filter(JsonObject::isSuccess)
                            .map(JsonObject::getObjEntity)
                            .map(AreaVO::getName)
                            .ifPresent(item::setProvince);
                }

                if (StringUtils.isNotBlank(item.getCity())) {
                    Optional.of(item.getCity())
                            .map(remoteAreaService::queryByCode)
                            .filter(JsonObject::isSuccess)
                            .map(JsonObject::getObjEntity)
                            .map(AreaVO::getName)
                            .ifPresent(item::setCity);
                }

                if (StringUtils.isNotBlank(item.getRegion())) {
                    Optional.of(item.getRegion())
                            .map(remoteAreaService::queryByCode)
                            .filter(JsonObject::isSuccess)
                            .map(JsonObject::getObjEntity)
                            .map(AreaVO::getName)
                            .ifPresent(item::setRegion);
                }

            });
            ExportParams exportParams=new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setSheetName("招投标信息监测");
            Map<String,Object> sheet1Map = new HashMap<>();
            sheet1Map.put("title",exportParams);
            sheet1Map.put("data", list);
            sheet1Map.put("entity", BiddingMonitoringInfoExportDTO.class);
            List<Map<String, Object>> sheets = new ArrayList<>();
            sheets.add(sheet1Map);
            try (Workbook workbook = ExcelExportUtil.exportExcel(sheets,ExcelType.XSSF)){
                response.addHeader(HttpHeaders.CONTENT_DISPOSITION, WebFilenameUtils.disposition("招投标信息监测.xlsx"));
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
                workbook.write(response.getOutputStream());
            }catch (Exception e){
                throw new CrmException("导出失败", ResultEnum.FAIL.getResult(), e);
            }
        }
    }
}