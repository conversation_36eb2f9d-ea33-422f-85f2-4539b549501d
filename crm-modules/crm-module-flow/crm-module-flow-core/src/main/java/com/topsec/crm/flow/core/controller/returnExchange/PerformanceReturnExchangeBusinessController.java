package com.topsec.crm.flow.core.controller.returnExchange;

import com.topsec.crm.flow.api.dto.returnexchange.ReturnExchangeDetailVO;
import com.topsec.crm.flow.core.service.PerformanceReportReturnExchangeService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.Set;

/**
 * 业绩上报退换货
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/performanceReturnExchange")
@Tag(name = "业绩上报退换货business", description = "业绩上报退换货business")
@RequiredArgsConstructor
public class PerformanceReturnExchangeBusinessController extends BaseController {
    private final PerformanceReportReturnExchangeService performanceReportReturnExchangeService;


    @GetMapping("/detail")
    @Operation(summary = "业绩上报退换货business详情")
    @PreAuthorize(hasPermission = "crm_refund_performance_report",dataScope = "crm_refund_performance_report")
    public JsonObject<ReturnExchangeDetailVO> detail(@RequestParam String processInstanceId){
        ReturnExchangeDetailVO detail = performanceReportReturnExchangeService.detail(processInstanceId, UserInfoHolder.getCurrentPersonId());
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        if (!dataScopeParam.getAllScope()){
            String agentId = dataScopeParam.getAgentId();
            boolean agentUser= StringUtils.isNotBlank(agentId);

            if (!agentUser){
                String saleId = detail.getSaleId();
                Set<String> personIds = dataScopeParam.getPersonIdList();
                if (!personIds.contains(saleId)){
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
            }else {
                String contractCompanyId = detail.getContractCompanyId();
                if (Objects.equals(agentId, contractCompanyId)){
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
            }
        }
        return new JsonObject<>(detail);
    }

}
