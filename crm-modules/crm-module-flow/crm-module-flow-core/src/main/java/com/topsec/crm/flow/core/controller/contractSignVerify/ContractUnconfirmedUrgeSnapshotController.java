package com.topsec.crm.flow.core.controller.contractSignVerify;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedUrgeSnapshotVo;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedUrgeVo;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUrgeSignVerifySonFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.process.impl.ContractUrgeSignVerifySonProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contractUnconfirmedUrgeSnapshot")
@Tag(name = "催交流程中的快照数据 相关Controller", description = "/contractUnconfirmedUrgeSnapshot")
@RequiredArgsConstructor
@Validated
public class ContractUnconfirmedUrgeSnapshotController extends BaseController {
    @Autowired
    private IContractUnconfirmedUrgeSnapshotService contractUnconfirmedUrgeSnapshotService;
    @Autowired
    private IContractUrgeSignVerifySonMainService contractUrgeSignVerifySonMainService;
    @Autowired
    private IContractUrgeSignVerifyMainService contractUrgeSignVerifyMainService;

    /**
     * 销售查看自己负责的催交信息
     */
    @PostMapping("/pageForOwner")
    @Operation(summary = "销售查看自己负责的催交信息")
    @PreFlowPermission
    public JsonObject<PageUtils<ContractUnconfirmedUrgeSnapshotVo>> pageForOwner(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //1.查询流程信息
        ContractUrgeSignVerifySonMain urgeSignVerifySonMain = contractUrgeSignVerifySonMainService.query().eq("process_instance_id", processInstanceId).one();

        //销售查看自己负责的催交信息
        startPage();
        List<ContractUnconfirmedUrgeSnapshot> list = contractUnconfirmedUrgeSnapshotService.query()
                .eq("process_instance_id", processInstanceId)
                .eq("contract_owner_id", urgeSignVerifySonMain.getOwnerId())
                .list();
        List<ContractUnconfirmedUrgeSnapshotVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, ContractUnconfirmedUrgeSnapshotVo.class);
        PageUtils dataTable = getDataTable(list,listVo);
        return new JsonObject<>(dataTable);
    }

    /**
     * 部门负责人查看所有已办结销售的催交眼前收
     */
    @PostMapping("/pageForDept")
    @Operation(summary = "部门负责人查看所有已办结销售的催交验签收")
    @PreFlowPermission
    public JsonObject<PageUtils<ContractUnconfirmedUrgeSnapshotVo>> pageForDept(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //1.查询流程信息
        ContractUrgeSignVerifyMain verifyMain = contractUrgeSignVerifyMainService.query().eq("process_instance_id", processInstanceId).one();

        //1.查询该流程下，所有审批完的子流程
        List<String> okPids = contractUrgeSignVerifySonMainService.list(new QueryWrapper<ContractUrgeSignVerifySonMain>()
                        .eq("parent_process_instance_id", processInstanceId)
                        .eq("process_state", ApprovalStatusEnum.YSP.getCode())
                        .select("process_instance_id"))
                        .stream().map(ContractUrgeSignVerifySonMain::getProcessInstanceId).toList();
        if(CollectionUtil.isEmpty(okPids)){
            return new JsonObject<>(new PageUtils<>());
        }

        startPage();

        List<ContractUnconfirmedUrgeSnapshot> list = contractUnconfirmedUrgeSnapshotService.query()
                .in("process_instance_id", okPids)
                .list();
        List<ContractUnconfirmedUrgeSnapshotVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, ContractUnconfirmedUrgeSnapshotVo.class);
        PageUtils dataTable = getDataTable(list,listVo);
        return new JsonObject<>(dataTable);
    }

    /**
     * 批量修改催交信息
     */
    @PostMapping("/updateSnap")
    @Operation(summary = "批量修改催交信息")
    @PreFlowPermission
    public JsonObject<Boolean> updateSnap(@RequestParam String processInstanceId, @RequestBody List<ContractUnconfirmedUrgeSnapshotVo> snapshotVos) {
        //1.权限校验
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //2.数据范围校验
        //查询流程是催交还是子催交
        List<ContractUrgeSignVerifySonMain> list = contractUrgeSignVerifySonMainService.query().eq("process_instance_id", processInstanceId)
                .or().eq("parent_process_instance_id", processInstanceId).list();
        if(CollectionUtil.isNotEmpty(list)){
            Set<String> edits = snapshotVos.stream().map(ContractUnconfirmedUrgeSnapshotVo::getId).collect(Collectors.toSet());

            Boolean b = list.stream().map(e -> e.getProcessInstanceId().equals(processInstanceId)).findFirst().orElse(null);
            if(b){
                //1.查询流程信息
                ContractUrgeSignVerifySonMain urgeSignVerifySonMain = contractUrgeSignVerifySonMainService.query().eq("process_instance_id", processInstanceId).one();
                //判断要修改的列表主键ID是否都在覆盖范围之内
                Set<String> scopeIds = contractUnconfirmedUrgeSnapshotService.list(new QueryWrapper<ContractUnconfirmedUrgeSnapshot>()
                        .eq("process_instance_id", processInstanceId)
                        .eq("contract_owner_id", urgeSignVerifySonMain.getOwnerId())
                        .select("id"))
                        .stream().map(ContractUnconfirmedUrgeSnapshot::getId).collect(Collectors.toSet());
                if(!scopeIds.containsAll(edits)){
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
            }else{
                //父流程权限
                //1.查询该流程下，所有审批完的子流程
                List<String> okPids = contractUrgeSignVerifySonMainService.list(new QueryWrapper<ContractUrgeSignVerifySonMain>()
                                .eq("parent_process_instance_id", processInstanceId)
                                .eq("process_state", ApprovalStatusEnum.YSP.getCode())
                                .select("process_instance_id"))
                        .stream().map(ContractUrgeSignVerifySonMain::getProcessInstanceId).toList();
                if(CollectionUtil.isEmpty(okPids)){
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }else{
                    Set<String> scopeIds = contractUnconfirmedUrgeSnapshotService.list(new QueryWrapper<ContractUnconfirmedUrgeSnapshot>()
                            .in("process_instance_id", okPids)
                            .select("id"))
                            .stream().map(ContractUnconfirmedUrgeSnapshot::getId).collect(Collectors.toSet());
                    if(!scopeIds.containsAll(edits)){
                        throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                    }
                }
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        List<ContractUnconfirmedUrgeSnapshot> snapshots = HyperBeanUtils.copyListPropertiesByJackson(snapshotVos, ContractUnconfirmedUrgeSnapshot.class);
        contractUnconfirmedUrgeSnapshotService.saveOrUpdateBatch(snapshots);
        return new JsonObject<>(true);
    }

}
