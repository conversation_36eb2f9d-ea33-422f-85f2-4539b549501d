package com.topsec.crm.flow.core.controller.handoverProcess;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.topsec.crm.contract.api.entity.originaldocument.OriginalDocumentVO;
import com.topsec.crm.contract.api.entity.request.OriginalDocumentQuery;
import com.topsec.crm.flow.api.vo.ProcessAttachmentVo;
import com.topsec.crm.flow.api.vo.handoverProcess.*;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.HandoverProcessReviewMain;
import com.topsec.crm.flow.core.service.HandoverProcessReviewMainService;
import com.topsec.crm.flow.core.service.HandoverReviewContentService;
import com.topsec.crm.flow.core.service.ThreeProcurementPaymentReviewMainService;
import com.topsec.crm.framework.common.bean.HandoverProcessQuery;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageIterator;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.TosDepartmentVO;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 
 *
 * <AUTHOR>
 * @email 
 * @date 2025-07-21 15:49:31
 */
@RestController
@RequestMapping("/business")
@Tag(name = "交接流程业务数据查询Controller", description = "/handoverProcess")
@RequiredArgsConstructor
@Validated
public class HandoverProcessReviewMainBusinessController extends BaseController {


    private final HandoverProcessReviewMainService handoverProcessReviewMainService;

    private final TfsNodeClient tfsNodeClient;

    private final TosDepartmentClient tosDepartmentClient;

    private final ThreeProcurementPaymentReviewMainService threeProcurementPaymentReviewMainService;

    private final HandoverReviewContentService handoverReviewContentService;


    /**
     * 分页查询人员交接列表
     */
    @PostMapping("/handoverProcess/page")
    @Operation(summary = "分页查询人员交接列表")
    public JsonObject<PageUtils<HandoverProcessReviewMainVo>> page(@RequestBody HandoverProcessCommonQuery query) {
        Page<HandoverProcessReviewMain> page = handoverProcessReviewMainService.page(query);
        IPage<HandoverProcessReviewMainVo> convert = page.convert(handoverProcessReviewMain ->
                HyperBeanUtils.copyPropertiesByJackson(handoverProcessReviewMain, HandoverProcessReviewMainVo.class)
        );
        if (CollectionUtils.isNotEmpty(convert.getRecords())){
            List<String> processInstanceIds = convert.getRecords().stream().map(HandoverProcessReviewMainVo::getProcessInstanceId).toList();
            Map<String, Set<ApproveNode>> stringSetMap = Optional.ofNullable(tfsNodeClient.queryNodeByProcessInstanceIdList(ListUtils.emptyIfNull(processInstanceIds)))
                    .map(JsonObject::getObjEntity).orElseThrow(() -> new RuntimeException("审批节点查询失败"));
            convert.getRecords().forEach(threeProcurementReviewMainVo -> {
                threeProcurementReviewMainVo.setApprovalNode(stringSetMap.get(threeProcurementReviewMainVo.getProcessInstanceId()));
            });
        }
        return new JsonObject<>(new PageUtils<>(convert));
    }



    @PostMapping("/handoverProcess/handoverGenericInfoPage")
    @Operation(summary = "选择后-通用分页查询方法")
    public JsonObject<List<HandoverInfoVo>> handoverGenericInfoPage(String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        HandoverProcessQuery query = new HandoverProcessQuery();
        return handoverProcessReviewMainService.getInfoPage(query);
    }


    /**
     * 查询人员交接基本信息
     */
    @GetMapping("/handoverProcess/getHandoverProcessInfo")
    @Operation(summary = "查询人员交接基本信息")
    public HandoverProcessReviewMainVo getHandoverProcessInfo(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例ID不能为空");
        HandoverProcessReviewMain handoverProcessReviewMain = handoverProcessReviewMainService.getHandoverProcessInfoByProcessInstanceId(processInstanceId);
        return HyperBeanUtils.copyPropertiesByJackson(handoverProcessReviewMain, HandoverProcessReviewMainVo.class);
    }


    @PostMapping("/handoverProcess/selectAttachmentList")
    @Operation(summary = "查询交接附件列表")
    public JsonObject<List<ProcessAttachmentVo>> selectAttachmentList(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        HandoverProcessReviewMain handoverProcessReviewMain = handoverProcessReviewMainService.getHandoverProcessInfoByProcessInstanceId(processInstanceId);
        List<ProcessAttachmentVo> listThreeProcurementAttachment = threeProcurementPaymentReviewMainService.getListThreeProcurementAttachment(handoverProcessReviewMain.getAttachmentIds());
        return new JsonObject<>(listThreeProcurementAttachment);
    }


    /**
     * 查询成本划分
     */
    @GetMapping("/handoverProcess/getCostDivision")
    @Operation(summary = "查询成本划分")
    public JsonObject<CostAllocationVo> getCostDivision(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        return new JsonObject<>(handoverProcessReviewMainService.queryCostDivision(processInstanceId));
    }

    /**
     * 根据流程实例id查询评审内容列表
     */
    @GetMapping("/handoverReviewContent/findHandoverContentList")
    @Operation(summary = "查询评审内容列表")
    @PreFlowPermission
    public JsonObject<List<HandoverReviewContentVo>> findHandoverContentList(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        List<HandoverReviewContentVo> handoverReviewContentVos = HyperBeanUtils.copyListPropertiesByJackson(handoverReviewContentService.findHandoverContentList(processInstanceId), HandoverReviewContentVo.class);
        return new JsonObject<>(handoverReviewContentVos);
    }



    @PostMapping("/export")
    @Operation(summary = "导出人员交接列表", method = "POST")
    public void export(@RequestBody HandoverProcessCommonQuery query) throws Exception {
        BiFunction<Integer, Integer, PageUtils<HandoverProcessReviewMainVo>> call = (pageNo, pageSize) -> {
            Page<HandoverProcessReviewMain> page = handoverProcessReviewMainService.page(query);
            IPage<HandoverProcessReviewMainVo> convert = page.convert(handoverProcessReviewMain ->
                    HyperBeanUtils.copyPropertiesByJackson(handoverProcessReviewMain, HandoverProcessReviewMainVo.class)
            );
            return new PageUtils<>(convert);
        };
        List<HandoverProcessReviewMainVo> contentList = PageIterator.iteratePageToList(1000, call, true, new ForkJoinPool(50));
        List<TosDepartmentVO> tosDepartmentVOS = Optional.ofNullable(tosDepartmentClient.findByIds(contentList.stream().map(HandoverProcessReviewMainVo::getDeptId).toList()))
                .map(JsonObject::getObjEntity).orElse(Collections.emptyList());
        Map<Object, TosDepartmentVO> collect = tosDepartmentVOS.stream().collect(Collectors.toMap(TosDepartmentVO::getUuid, Function.identity()));
        contentList.parallelStream().forEach(handoverProcessReviewMainVo -> {
            handoverProcessReviewMainVo.setDeptName(collect.get(handoverProcessReviewMainVo.getDeptId()).getName());
        });
        ExcelUtil<HandoverProcessReviewMainVo> excelUtil = new ExcelUtil<>(HandoverProcessReviewMainVo.class);
        excelUtil.exportExcel(response, contentList, "合同原件");
    }





}
