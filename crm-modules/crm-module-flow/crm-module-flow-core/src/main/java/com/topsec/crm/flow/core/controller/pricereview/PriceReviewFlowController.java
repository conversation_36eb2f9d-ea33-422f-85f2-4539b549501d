package com.topsec.crm.flow.core.controller.pricereview;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewMainInfo;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductQuery;
import com.topsec.crm.flow.api.dto.pricereview.input.PriceReviewMaintainInvestmentInput;
import com.topsec.crm.flow.api.dto.pricereview.input.PriceReviewProductAddedInfoInput;
import com.topsec.crm.flow.api.dto.pricereview.input.PriceReviewProductTempCostInput;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementProcessQuery;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.handler.right.ProjectRightHandler;
import com.topsec.crm.flow.core.handler.right.Step00RemoveEditProcessRightHandler;
import com.topsec.crm.flow.core.mapper.ProcessExtensionInfoMapper;
import com.topsec.crm.flow.core.mapstruct.PriceReviewProductConvertor;
import com.topsec.crm.flow.core.process.ProcessTypeEnum;
import com.topsec.crm.flow.core.process.impl.PriceReviewProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ContractEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteProjectPerformanceNegotiationClient;
import com.topsec.crm.project.api.dto.ProjectProductOwnPageQuery;
import com.topsec.crm.project.api.entity.*;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.common.constants.TosConstants;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/priceReview")
@Tag(name = "价格评审", description = "/priceReview")
@RequiredArgsConstructor
@Validated
public class PriceReviewFlowController extends BaseController {

    private final PriceReviewFlowService priceReviewFlowService;
    private final PriceReviewSigningAgentService priceReviewSigningAgentService;

    private final PriceReviewProcessService priceReviewProcessService;
    private final PriceReviewSpecialCodeService priceReviewSpecialCodeService;
    private final PriceReviewStatisticsService priceReviewStatisticsService;

    private final PriceReviewMainService priceReviewMainService;
    private final ProcessExtensionInfoMapper processExtensionInfoMapper;
    private final PriceReviewProductOwnService priceReviewProductOwnService;
    private final PriceReviewProductThirdService priceReviewProductThirdService;
    private final PriceReviewProductServiceOutsourcingService priceReviewProductServiceOutsourcingService;
    private final RemoteProjectPerformanceNegotiationClient remoteProjectPerformanceNegotiationClient;
    private final SalesAgreementReviewMainService salesAgreementReviewMainService;
    private final PriceReviewSaleAgreementRelService priceReviewSaleAgreementRelService;
    private final ContractReviewMainService contractReviewMainService;
    private final TosDepartmentClient tosDepartmentClient;
    private final CommonService commonService;
    private final TfsNodeClient tfsNodeClient;

    private final RedissonClient redissonClient;

    @PostMapping("/launch")
    @Operation(summary = "发起价格评审流程")
    @PreAuthorize(hasPermission = "crm_flow_price_review")
    public JsonObject<Boolean> launch(@Valid @RequestBody PriceReviewFlowLaunchDTO launchDTO) {
        String projectId =StringUtils.firstNonBlank(launchDTO.getProjectId(),launchDTO.getBaseInfo().getProjectId());
        String lockKey = "priceReviewLock:"+projectId;
        RLock rLock = redissonClient.getLock(lockKey);
        try {
            rLock.lock();
            boolean exists = priceReviewMainService.exists(new LambdaQueryWrapper<PriceReviewMain>().eq(PriceReviewMain::getProjectId, projectId)
                    .eq(PriceReviewMain::getProcessState, ApprovalStatusEnum.SPZ.getCode()));
            if (exists){
                return JsonObject.errorT("当前项目有价格评审流程在审批中，请勿重复发起");
            }
            return new JsonObject<>(priceReviewProcessService.launch(launchDTO));
        }finally {
            rLock.unlock();
        }
    }

    @GetMapping("/baseInfo")
    @Operation(summary = "价格评审基础信息")
    // @PreFlowPermission
    public JsonObject<PriceReviewMainInfo> baseInfo() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(priceReviewMainService.baseInfo(processInstanceId));
    }


    @GetMapping("/queryProjectPriceStatistics")
    @Operation(summary = "产品报价汇总")
    // @PreFlowPermission
    public JsonObject<CrmProjectDirectlyPriceStatisticsVO> queryProjectPriceStatistics(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(priceReviewStatisticsService.queryProjectDirectlyPriceStatisticsByProcessInstanceId(processInstanceId,UserInfoHolder.getCurrentPersonId()));
    }

    @PostMapping("/queryOwnProductPriceDetails")
    @Operation(summary = "产品报价明细-自有产品")
    // @PreFlowPermission
    public JsonObject<PageUtils<CrmProjectProductOwnVO>> queryOwnProductPriceDetails(@RequestBody @Valid PriceReviewProductQuery query) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        query.setProcessInstanceId(processInstanceId);
        return new JsonObject<>(priceReviewProductOwnService.queryOwnProductPriceDetails(query));
    }


    @PostMapping("/queryThirdProductPriceDetails")
    @Operation(summary = "产品报价明细-第三方产品")
    @PreFlowPermission
    public JsonObject<PageUtils<CrmProjectProductThirdVo>> queryThirdProductPriceDetails(@RequestBody @Valid PriceReviewProductQuery query) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        query.setProcessInstanceId(processInstanceId);
        return new JsonObject<>(priceReviewProductThirdService.queryThirdProductPriceDetails(query));
    }

    @PostMapping("/queryOutsourcingProductPriceDetails")
    @Operation(summary = "产品报价明细-外包服务费")
    @PreFlowPermission
    public JsonObject<PageUtils<CrmProjectOutsourcingServiceVo>> queryOutsourcingProductPriceDetails(@RequestBody @Valid PriceReviewProductQuery query) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        query.setProcessInstanceId(processInstanceId);
        List<PriceReviewProductServiceOutsourcing> outsourcing = priceReviewProductServiceOutsourcingService
                .queryProjectOutsourcingByProcessInstanceId(processInstanceId);
        PageUtils<CrmProjectOutsourcingServiceVo> converted = PageUtils.paginate(outsourcing, query.getPageSize(), query.getPageNum())
                .convert(PriceReviewProductConvertor.INSTANCE::toOutsourcingCrm);
        return new JsonObject<>(converted);
    }

    @PostMapping("/pageProductTempCost")
    @Operation(summary = "增加成本产品分页")
    @PreFlowPermission
    public JsonObject<PageUtils<PriceReviewProductOwnDTO>> pageProductTempCost(@RequestBody @Valid PriceReviewProductQuery query) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        query.setProcessInstanceId(processInstanceId);
        return new JsonObject<>(priceReviewProductOwnService.pageProductTempCost(query));
    }

    @PostMapping("/updateProductTempCost")
    @Operation(summary = "更新产品成本")
    @PreFlowPermission
    public JsonObject<Boolean> updateProductTempCost(@RequestBody @Valid @NotEmpty List<PriceReviewProductTempCostInput> list) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        for (PriceReviewProductTempCostInput priceReviewProductTempCostInput : list) {
            priceReviewProductTempCostInput.setProcessInstanceId(processInstanceId);
        }
        return new JsonObject<>(priceReviewProductOwnService.updateProductTempCost(list));
    }


    // 检查折扣是否为1.1或更低
    @Operation(summary = "检查折扣是否为1.1或更低")
    @GetMapping("/isOnePointOneDiscountOrLess")
    @PreFlowPermission
    public JsonObject<Boolean> isOnePointOneDiscountOrLess() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(priceReviewFlowService.isOnePointOneDiscountOrLess(processInstanceId));
    }


    @PostMapping("/pageProductChooseShipmentSn")
    @Operation(summary = "选择出货序列号-发起价审流程页面使用")
    @PreAuthorize(rightHandler = ProjectRightHandler.class, rightHandlerExtractArgsEL = {"#query.projectId"})
    public JsonObject<PageUtils<PriceReviewProductOwnDTO>> pageProductChooseShipmentSn(@RequestBody @Valid ProjectProductOwnPageQuery query) {
        query.setCurrentPersonId(UserInfoHolder.getCurrentPersonId());
        PageUtils<PriceReviewProductOwnDTO> priceReviewProductOwnDTOPageUtils = priceReviewProductOwnService.pageProductChooseShipmentSn(query);
        List<PriceReviewProductOwnDTO> ownList = ListUtils.emptyIfNull(priceReviewProductOwnDTOPageUtils.getList()) ;
        for (PriceReviewProductOwnDTO ownDTO : ownList) {
            ownDTO.setId(ownDTO.getRecordId());
        }
        return new JsonObject<>(priceReviewProductOwnDTOPageUtils);
    }


    @PostMapping("/pageSpecialCodeApprovalSettings")
    @Operation(summary = "特殊代码审批人配置分页-价审发起或00步编辑流程页面使用")
    @PreAuthorize(rightHandler = ProjectRightHandler.class, rightHandlerExtractArgsEL = {"#query.projectId"})
    public JsonObject<PageUtils<PriceReviewProductOwnDTO>> pageSpecialCodeApprovalSettings(@RequestBody @Valid ProjectProductOwnPageQuery query) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        boolean launchedPageCall=StringUtils.isBlank(headerValue);
        query.setLaunchedPageCall(launchedPageCall);
        return new JsonObject<>(priceReviewProductOwnService.pageSpecialCodeApprovalSettings(query));
    }

    @PostMapping("/pageSpecial")
    @Operation(summary = "特殊代码审批产品信息添加操作分页-审批人页面使用")
    @PreFlowPermission
    public JsonObject<PageUtils<PriceReviewProductOwnDTO>> pageSpecial(@RequestBody @Valid PriceReviewProductQuery query) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        query.setProcessInstanceId(processInstanceId);
        return new JsonObject<>(priceReviewProductOwnService.pageSpecial(query, UserInfoHolder.getCurrentPersonId(), true));
    }

    @PostMapping("/updateProductInfo")
    @Operation(summary = "更新产品额外信息")
    @PreFlowPermission
    public JsonObject<Boolean> updateProductInfo(@Valid @NotEmpty @RequestBody List<@Valid PriceReviewProductAddedInfoInput> list) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        for (PriceReviewProductAddedInfoInput priceReviewProductAddedInfoInput : list) {
            priceReviewProductAddedInfoInput.setProcessInstanceId(processInstanceId);
        }
        return new JsonObject<>(priceReviewProductOwnService.updateProductInfo(list));
    }



    // 检查是否存在特殊代码
    @Operation(summary = "检查是否存在特殊材料代码")
    @GetMapping("/anySpecialMaterialCode")
    @PreFlowPermission
    public JsonObject<Boolean> anySpecialMaterialCode() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(priceReviewFlowService.anySpecialMaterialCode(processInstanceId));
    }

    // 检查是否已有销售协议
    @Operation(summary = "检查是否已有销售协议")
    @GetMapping("/existSalesAgreement")
    @PreFlowPermission
    public JsonObject<Boolean> existSalesAgreement() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(priceReviewFlowService.existSalesAgreement(processInstanceId));
    }

    @Operation(summary = "计算评估结果的选项，返回 1-只能选盈利 2-只能选亏损 3-都可以选")
    @PostMapping("/calEvaluationResultSelectType")
    @PreFlowPermission
    public JsonObject<Integer> calEvaluationResultSelectType(@RequestBody PriceReviewMaintainInvestmentInput input) {
        return new JsonObject<>(priceReviewProductOwnService.calEvaluationResultSelectType(input));
    }

    /**
     * 1.存在产品行的成交价<(si价格，标准折扣价中的最小值)
     * 2.存在产品行的成交价<标准折扣价
     * 3.是否包含需要对应role审批的特殊代码（这个是特殊代码配置里的）
     * 4.销售协议是否跳过营销中心领导：
     *      * ①.产品列表仅包含4、5开头的物料代码（5998去掉）
     *      * ②价审所选产品物料都在关联的销售协议产品范围内，成交单价大于或等于协议金额。
     *      * ③ 若存在多个协议,同物料代码取最低协议金额，成交单价需要大于等于最低协议金额。
     * 是否需要营销中心领导审批最终逻辑：
     * （1or2or3）and !4
     */
    // @Operation(summary = "是否需要中心领导审批-有营销中心领导审批人且需要营销中心领导审批则true-网关条件 ")
    // @GetMapping("/needCenterLeaderApproval")
    // @PreFlowPermission
    // public JsonObject<Boolean> needCenterLeaderApproval() {
    //     String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
    //
    //     ProjectPriceReviewDetail projectPriceReviewDetail = priceReviewFlowService.queryPriceReviewDetail(processInstanceId);
    //     PriceReviewMain priceReviewMain = projectPriceReviewDetail.getPriceReviewMain();
    //     String createUser = priceReviewMain.getCreateUser();
    //     List<FlowPerson> flowPeople = priceReviewFlowService.queryCenterLeaderApproverList(processInstanceId, createUser, true);
    //     boolean hasPeople = CollectionUtils.isNotEmpty(flowPeople);
    //     if (!hasPeople) return new JsonObject<>(false);
    //     boolean needed = priceReviewFlowService.needCenterLeaderApproval(projectPriceReviewDetail);
    //     //有营销中心领导审批人且需要营销中心领导审批则为需要
    //     return new JsonObject<>(needed);
    // }


    @Operation(summary = "是否需要中间商审批")
    @GetMapping("/needMiddlemanApproval")
    @PreFlowPermission
    public JsonObject<Boolean> needMiddlemanApproval() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(priceReviewFlowService.needMiddlemanApproval(processInstanceId));
    }

    @Operation(summary = "是否需要预先确认")
    @GetMapping("/needPreConfirmation")
    @PreFlowPermission
    public JsonObject<Boolean> needPreConfirmation() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(priceReviewFlowService.needPreConfirmation(processInstanceId));
    }

    @Operation(summary = "是否需要总裁审批")
    @GetMapping("/needPresidentApproval")
    @PreFlowPermission
    public JsonObject<Boolean> needPresidentApproval() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(priceReviewFlowService.needPresidentApproval(processInstanceId));
    }

    @Operation(summary = "是否需要添加临时成本")
    @GetMapping("/needToAddTemporaryCosts")
    @PreFlowPermission
    public JsonObject<Boolean> needToAddTemporaryCosts() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(priceReviewFlowService.needToAddTemporaryCosts(processInstanceId));
    }

    @Operation(summary = "查询中心领导审批人列表，第一次查传personId，personId为该流程发起人；之后无需传入")
    @GetMapping("/queryCenterLeaderApproverList")
    @PreFlowPermission
    @Deprecated
    public JsonObject<List<FlowPerson>> queryCenterLeaderApproverList(@RequestParam(required = false) String personId) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        //是否逐级之前
        boolean before = StringUtils.isNotBlank(personId);
        //当前登录人
        String currentPeronId = UserInfoHolder.getCurrentPersonId();
        personId = !before ? currentPeronId : personId;
        return new JsonObject<>(priceReviewFlowService.queryCenterLeaderApproverList(processInstanceId, personId,before));
    }

    @Operation(summary = "逐级上级到能包下折扣和价差的或到1级，第一次查传personId，personId为该流程发起人；之后无需传入")
    @GetMapping("/querySuperiorContainApproverList")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> querySuperiorContainApproverList(@RequestParam(required = false) String personId) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        //是否逐级之前
        boolean before = StringUtils.isNotBlank(personId);
        //当前登录人
        String currentPeronId = UserInfoHolder.getCurrentPersonId();
        personId = !before ? currentPeronId : personId;
        return new JsonObject<>(priceReviewFlowService.querySuperiorContainApproverList(processInstanceId, personId, before));
    }


    @Operation(summary = "查询【项目流程签约渠道】树形结构")
    @GetMapping("/querySignAgentTree")
    @PreFlowPermission
    public JsonObject<List<AgentTreeSelect>> querySignAgentTree() {
        String processInstanceIdH = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(priceReviewSigningAgentService.tree(processInstanceIdH));
    }



    // 查询关键行业审批人
    @Operation(summary = "查询关键行业审批人")
    @GetMapping("/queryKeyIndustryApprover")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> queryKeyIndustryApprover(@RequestParam String personId) {
        return new JsonObject<>(priceReviewProcessService.queryKeyIndustryApprover(personId));
    }



    @Operation(summary = "最新已通过价审是否有效，若从未发过价审算失效")
    @GetMapping("/isTheLatestPassedPriceReviewIsValid")
    @PreAuthorize(rightHandler = ProjectRightHandler.class)
    public JsonObject<Boolean> isTheLatestPassedPriceReviewValid(String projectId){
        return new JsonObject<>(priceReviewMainService.isTheLatestPassedPriceReviewValid(projectId));
    }

    @Operation(summary = "价审填写之前校验")
    @GetMapping("/checkBeforeFilling")
    @PreAuthorize(rightHandler = ProjectRightHandler.class)
    public JsonObject<Void> checkBeforeFilling(@RequestParam String projectId) {
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        return priceReviewProcessService.checkBeforeFillingJsonObj(Pair.of(projectId,currentPersonId));
    }

    @Operation(summary = "调整价格失效天数")
    @PutMapping("/updatePriceExpiryDays")
    @PreAuthorize(hasPermission = "crm_price_review_update_expiration_date")
    public JsonObject<Boolean> updatePriceExpiryDays(@RequestParam Integer priceExpiryDays,
                                                     @RequestParam(required = false) String priceExpiryDaysOfApprovalRemark) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);

        if (priceExpiryDays<=0) throw new CrmException("失效天数需大于零");
        PriceReviewMain one = priceReviewMainService.getOne(new LambdaQueryWrapper<PriceReviewMain>().eq(PriceReviewMain::getProcessInstanceId, processInstanceId));
        one.setPriceExpiryDaysOfApproval(priceExpiryDays);
        one.setPriceExpiryDaysOfApprovalRemark(priceExpiryDaysOfApprovalRemark);
        boolean b = priceReviewMainService.updateById(one);
        return new JsonObject<>(b);
    }

    @Operation(summary = "当前登录人是否是特殊代码审批人")
    @GetMapping("/isSpecialCodeApprovePerson")
    @PreFlowPermission
    public JsonObject<Boolean> isSpecialCodeApprovePerson() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        List<PriceReviewSpecialCode> specialCodeList = priceReviewSpecialCodeService.list(new QueryWrapper<PriceReviewSpecialCode>()
                .eq("process_instance_id", processInstanceId));
        return new JsonObject<>(priceReviewStatisticsService.isSpecialCodeApprovePerson(specialCodeList, currentPersonId));
    }


    @Operation(summary = "历史价格审批-展示该项目的近三次办结的价格评审流")
    @GetMapping("/queryHistoryPassedPriceReview")
    @PreFlowPermission
    public JsonObject<List<PriceReviewMainInfo>> queryHistoryPassedPriceReview(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(priceReviewMainService.queryHistoryPassedPriceReview(processInstanceId));
    }

    @PostMapping("/00edit")
    @Operation(summary = "00步编辑流程")
    @PreAuthorize(rightHandler = Step00RemoveEditProcessRightHandler.class)
    public JsonObject<Boolean> editProcess00(@RequestBody PriceReviewFlowLaunchDTO priceReviewFlowLaunchDTO){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        priceReviewFlowLaunchDTO.setProcessInstanceId(processInstanceId);
        priceReviewProcessService.edit00(priceReviewFlowLaunchDTO);
        return new JsonObject<>(true);
    }

    @GetMapping("/getNegotiation")
    @Operation(summary = "获取业绩协商")
    @PreFlowPermission
    public JsonObject<CrmProjectPerformanceNegotiationVo> getNegotiation(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ProcessExtensionInfo processExtensionInfo = processExtensionInfoMapper.selectOne(new LambdaQueryWrapper<ProcessExtensionInfo>().eq(ProcessExtensionInfo::getProcessInstanceId, processInstanceId));
        String projectId = processExtensionInfo.getProjectId();
        return remoteProjectPerformanceNegotiationClient.getCrmProjectPerformanceNegotiationByProjectId(projectId);
    }
    @GetMapping("/queryProjectInfo")
    @Operation(summary = "获取项目信息")
    @PreFlowPermission
    public JsonObject<CrmProjectDirectlyVo> queryProjectInfo(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmProjectDirectlyVo crmProjectDirectlyVo = priceReviewMainService.queryProjectInfo(processInstanceId);
        return new JsonObject<>(crmProjectDirectlyVo);
    }


    @Operation(summary = "查询销售协议信息--价审发起用")
    @GetMapping("/querySalesAgreementForLaunch")
    @PreAuthorize(rightHandler = ProjectRightHandler.class)
    public JsonObject<PriceReviewSalesAgreementForLaunch> querySalesAgreementForLaunch(@RequestParam String projectId){
        SalesAgreementProcessQuery salesAgreementProcessQuery=new SalesAgreementProcessQuery();
        salesAgreementProcessQuery.setPersonId(UserInfoHolder.getCurrentPersonId());
        salesAgreementProcessQuery.setDeptId(getDepartmentId());
        PriceReviewSalesAgreementForLaunch priceReviewSalesAgreementForLaunch=new PriceReviewSalesAgreementForLaunch();
        // 再次发起价审时，需要默认将合同评审中生效的销售协议带入到价审中，不能修改和删除已生效销售协议
        List<ContractReviewMainBaseInfoDTO> contractReviewList = contractReviewMainService.getContractReviewByProjectId(projectId);
        List<String> ids = ListUtils.emptyIfNull(contractReviewList).stream()
                .filter(contractReview ->
                        Objects.equals(contractReview.getContractState(), ContractEnum.ContractStateEnum.VALID.getCode()))
                .flatMap(item->{
                    List<String> salesAgreements = ListUtils.emptyIfNull(item.getSalesAgreements()) ;
                    return salesAgreements.stream();
                })
                .collect(Collectors.toList());
        List<SalesAgreementReviewMain> fromContract=Collections.emptyList();
        if (CollectionUtils.isNotEmpty(ids)){
            fromContract = salesAgreementReviewMainService.findSalesAgreementByIds(ids);
        }
        priceReviewSalesAgreementForLaunch.setFromContract(fromContract);
        List<SalesAgreementReviewMain> salesAgreementBusinessList = salesAgreementReviewMainService.findSalesAgreementBusinessList(salesAgreementProcessQuery);
        priceReviewSalesAgreementForLaunch.setFromSalesAgreement(salesAgreementBusinessList);
        return new JsonObject<>(priceReviewSalesAgreementForLaunch);
    }

    @Operation(summary = "查询销售协议信息--流程进行中用")
    @GetMapping("/querySalesAgreement")
    @PreFlowPermission
    public JsonObject<List<SalesAgreementReviewMain>> querySalesAgreement(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<String> saleAgreement = priceReviewSaleAgreementRelService
                .list(new LambdaQueryWrapper<PriceReviewSaleAgreementRel>()
                        .eq(PriceReviewSaleAgreementRel::getPriceReviewProcessInstanceId, processInstanceId)).stream()
                .map(PriceReviewSaleAgreementRel::getSaleAgreementId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saleAgreement)) return new JsonObject<>(Collections.emptyList());
        List<SalesAgreementReviewMain> fromSalesAgreement = salesAgreementReviewMainService.findSalesAgreementByIds(saleAgreement);
        return new JsonObject<>(fromSalesAgreement);
    }
    // 1、01节点审批人由上级领导审批改为逐级审批至一级部门负责人，审批办结时，需要查询下一级审批人是否在【营销中心领导】配置中，如果为营销中心领导，则01节点结束，否则，一直审批至一级部门负责人结束；
    // deprecated 暂时去掉 2、01节点和02节点审批人都需要根据配置价审折扣进行处理，未配置折扣的审批人不参与审批；
    // 3、01步后的营销中心领导前的网关需要调整，如果01步逐级审批中存在营销中心领导或01步逐级审批人员折扣无法覆盖价审折扣，则需要营销中心领导审批
    @Operation(summary = "价审01步查询符合条件的上级")
    @GetMapping("/querySuperior01")
    @PreFlowPermission(hasAnyNodes = "sid-256D96D6-DC2D-41F6-902A-F516FFA3D9D1")
    public JsonObject<List<FlowPerson>> querySuperior01(){
        String currentPersonId = UserInfoHolder.getCurrentPersonId();

        List<Integer> levels = Optional.ofNullable(tosDepartmentClient.queryManagedDeptLevel(currentPersonId, List.of(TosConstants.RelationType.LEADER)))
                .map(JsonObject::getObjEntity).orElse(Collections.emptyList());
        boolean currentIsFirstDeptLeader= levels.contains(1);

        List<FlowPerson> superior = commonService.queryFlowSuperiors(currentPersonId, emp -> {
            //若当前为一级部门负责人 ，则只查询上级为营销中心领导的
            if (currentIsFirstDeptLeader){
                return commonService.isCenterLeader(emp.getUuid());
            }
            return true;
        }, (current) -> {
            String projectId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROJECT_ID);
            //若包的住价折扣和价差，则不返回上级
            return priceReviewFlowService.includeDiscountAndPriceGap(current.getUuid(), projectId);
        });
        return new JsonObject<>(superior);
    }


    @Operation(summary = "价审02步查询符合条件的上级，营销中心领导过滤")
    @GetMapping("/querySuperior02")
    @PreFlowPermission(hasAnyNodes = "sid-8F7B9CB2-3E23-4A26-B149-3DB7976AF53D")
    public JsonObject<List<FlowPerson>> querySuperior02(){
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        List<FlowPerson> superior = commonService.queryFlowSuperiors(currentPersonId, emp -> {
            //是否营销中心领导
            return commonService.isCenterLeader(emp.getUuid());
        }, (current) -> {
            String projectId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROJECT_ID);
            //若包的住价折扣和价差，则不返回上级
            return priceReviewFlowService.includeDiscountAndPriceGap(current.getUuid(), projectId);
        });
        return new JsonObject<>(superior);
    }

    @Operation(summary = "查询项目其他已通过价审流程id、编号信息（排除当前的）")
    @GetMapping("/queryOtherCompletedProcessInfo")
    @PreFlowPermission
    public JsonObject<List<ProcessExtensionInfo>> queryOtherCompletedProcessInfo(){
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        String currentProcessInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String projectId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROJECT_ID);
        List<ProcessExtensionInfo> processExtensionInfos =
                processExtensionInfoMapper.selectList(new LambdaQueryWrapper<ProcessExtensionInfo>()
                        .eq(ProcessExtensionInfo::getProjectId, projectId)
                        .eq(ProcessExtensionInfo::getProcessDefinitionKey, ProcessTypeEnum.PRICE_REVIEW.getProcessDefinitionKey().getValue())
                        .eq(ProcessExtensionInfo::getProcessState, ApprovalStatusEnum.YSP.getCode())
                        .select(ProcessExtensionInfo::getId, ProcessExtensionInfo::getProcessNumber,
                                ProcessExtensionInfo::getProcessInstanceId)
                );
        List<ProcessExtensionInfo> list = processExtensionInfos.stream()
                .filter(item -> {
                    String processInstanceId = item.getProcessInstanceId();
                    boolean filterCurrentInstance = !processInstanceId.equals(currentProcessInstanceId);
                    Map<String, Set<ApproveNode>> map = Optional.ofNullable(tfsNodeClient.queryNodeByProcessInstanceIdList(List.of(processInstanceId)))
                            .map(JsonObject::getObjEntity)
                            .orElse(Collections.emptyMap());
                    Set<ApproveNode> approveNodes = SetUtils.emptyIfNull(map.get(processInstanceId));
                    // 判断当前登录人是否在approveNodes的currentApprovedList 中存在
                    boolean nodeAnyMatch = approveNodes.stream().anyMatch(node -> node.getCurrentApprovedList().stream()
                            .anyMatch(leader -> leader.getValue().equals(currentPersonId)));

                    return filterCurrentInstance && nodeAnyMatch;
                })
                .toList();
        return new JsonObject<>(list);
    }

}
