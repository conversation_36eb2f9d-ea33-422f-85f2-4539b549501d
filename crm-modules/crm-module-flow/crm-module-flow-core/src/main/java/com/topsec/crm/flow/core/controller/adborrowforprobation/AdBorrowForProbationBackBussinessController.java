package com.topsec.crm.flow.core.controller.adborrowforprobation;

import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationBackFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationBackVO;
import com.topsec.crm.flow.core.entity.AdBorrowForProbation;
import com.topsec.crm.flow.core.entity.AdBorrowForProbationBack;
import com.topsec.crm.flow.core.process.impl.AdBorrowForProbationBackProcessService;
import com.topsec.crm.flow.core.service.AdBorrowForProbationBackService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 样机归还信息 业务
 */
@RestController
@RequestMapping("/business/adBorrowForProbationBack")
@Tag(description = "/business/adBorrowForProbationBack", name = "样机归还业务流程查询")
@RequiredArgsConstructor
@Validated
public class AdBorrowForProbationBackBussinessController extends BaseController {

    @Resource
    private AdBorrowForProbationBackProcessService adBorrowForProbationBackProcessService;

    @Resource
    private AdBorrowForProbationBackService adBorrowForProbationBackService;

    @PostMapping("/flow/page")
    @Operation(summary = "样机归还流程列表查询")
    @PreAuthorize(hasPermission = "crm_sample_back",dataScope = "crm_sample_back")
    public JsonObject<TableDataInfo> page(@RequestBody AdBorrowForProbationBackFlowLaunchDTO launchDTO) {
        startPage();
        TableDataInfo tableDataInfo = adBorrowForProbationBackService.page(launchDTO);
        return new JsonObject<>(tableDataInfo);
    }

    @GetMapping("/flow/detail/{processInstanceId}")
    @Operation(summary = "查看样机归还流程详情")
    @PreAuthorize(hasPermission = "crm_sample_back",dataScope = "crm_sample_back")
    public JsonObject<AdBorrowForProbationBackVO> selectAdBorrowForProbationDetail(@PathVariable String processInstanceId) {
        AdBorrowForProbationBack adBorrowForProbationBack = adBorrowForProbationBackService.getAdBackEntityByProcessInstanceId(processInstanceId);
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        if(StringUtils.isBlank(UserInfoHolder.getCurrentAgentId())){
            PreAuthorizeAspect.checkPersonIdDataScope(dataScopeParam,adBorrowForProbationBack.getCreateUser());
        }else {
            if(adBorrowForProbationBack.getAgentIdZd().equals(UserInfoHolder.getCurrentAgentId())
                    || adBorrowForProbationBack.getAgentIdCreate().equals(UserInfoHolder.getCurrentAgentId())){
            }else {
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }
        return new JsonObject<>(adBorrowForProbationBackService.selectAdBorrowForProbationBackDetail(processInstanceId));
    }

}