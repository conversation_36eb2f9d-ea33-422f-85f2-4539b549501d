package com.topsec.crm.flow.core.controller.customer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.topsec.crm.customer.api.RemoteCustomerNaApplySetupService;
import com.topsec.crm.customer.api.RemoteCustomerNaService;
import com.topsec.crm.customer.api.RemoteSalemanService;
import com.topsec.crm.customer.api.entity.CrmCustomerNaVo;
import com.topsec.crm.customer.api.entity.CrmSalemanVo;
import com.topsec.crm.flow.api.dto.customer.CustomerNaApproveFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.customer.CustomerNaSalemanVo;
import com.topsec.crm.flow.core.entity.CustomerNa;
import com.topsec.crm.flow.core.entity.CustomerNaMain;
import com.topsec.crm.flow.core.entity.CustomerNaSaleman;
import com.topsec.crm.flow.core.process.ProcessTypeEnum;
import com.topsec.crm.flow.core.process.impl.CustomerNaApproveProcessService;
import com.topsec.crm.flow.core.service.ICustomerNaMainService;
import com.topsec.crm.flow.core.service.ICustomerNaSalemanService;
import com.topsec.crm.flow.core.service.ICustomerNaService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.PageDomain;
import com.topsec.crm.framework.common.web.page.TableSupport;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.jwt.UserInfoHolder;

import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.constant.TbsConstants;
import com.topsec.tos.common.query.PersonQuery;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/customerNaApprove")
@Tag(name = "NA客户审批流程相关Controller", description = "/customerNaApproveFlow")
@RequiredArgsConstructor
@Validated
public class CustomerNaApproveFlowController extends BaseController {
    @Autowired
    private CustomerNaApproveProcessService customerNaApproveProcessService;
    @Autowired
    private RemoteCustomerNaApplySetupService remoteCustomerNaApplySetupService;
    @Autowired
    private ICustomerNaService customerNaService;
    @Autowired
    private ICustomerNaMainService customerNaMainService;
    @Autowired
    private RemoteCustomerNaService remoteCustomerNaService;
    @Autowired
    private RemoteSalemanService remoteSalemanService;
    @Autowired
    private ICustomerNaSalemanService customerNaSalemanService;

    @PostMapping("/launch")
    @Operation(summary = "F1.发起NA客户审批流程")
    @PreAuthorize(hasPermission = "crm_customer_dept_na_apply",dataScope = "crm_customer_dept_na_apply")
    public JsonObject<Boolean> launch(@Valid @RequestBody CustomerNaApproveFlowLaunchDTO launchDTO) {
        //数据范围校验
        Assert.isFalse(CollectionUtil.isEmpty(launchDTO.getSalemanVos()),"请选择需要指名的销售");
        //查询能够选择的销售范围
        List<String> saleIds = launchDTO.getSalemanVos().stream().map(CustomerNaSalemanVo::getSalemanId).toList();
        JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(saleIds);
        if(byIds.isSuccess() && byIds.getObjEntity() != null){
            List<EmployeeVO> em = byIds.getObjEntity();
            //权限校验
            // 查询当前登录人下所属和管理的所有子部门IDs
            List<String> deptIds = queryDeptIdList(UserInfoHolder.getCurrentPersonId(), TbsConstants.Datapurview.DEPTDATA);
            if(CollectionUtil.isEmpty(deptIds)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
            for (EmployeeVO employeeVO : em) {
                if(employeeVO.getDept() == null){
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
                if(!deptIds.contains(employeeVO.getDept().getUuid())){
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        //验证是否开放了NA申请入口
        JsonObject booleanJsonObject = remoteCustomerNaApplySetupService.checkNaSetup();
        Assert.isFalse(!booleanJsonObject.isSuccess(),booleanJsonObject.getMessage());

        //验证销售是否有流程中的NA客户审批流程
        Set<String> pros = customerNaSalemanService.query().select("process_instance_id")
                .in("saleman_id", saleIds)
                .list().stream().map(CustomerNaSaleman::getProcessInstanceId).collect(Collectors.toSet());

        if(CollectionUtil.isNotEmpty(pros)){
            Set<String> flowUsers = new HashSet<String>();
            //查询审批中的流程
            List<CustomerNaMain> list = customerNaMainService.query().eq("process_state", 1).in("process_instance_id", pros).list();
            if(CollectionUtil.isNotEmpty(list)){
                for (CustomerNaMain customerNaMain : list) {
                    Set<String> flowUserIds = customerNaSalemanService.query()
                            .eq("process_instance_id", customerNaMain.getProcessInstanceId())
                            .list().stream().map(CustomerNaSaleman::getSalemanId).collect(Collectors.toSet());
                    Set<String> intersection = intersection(saleIds, flowUserIds);
                    flowUsers.addAll(intersection);
                }
            }
            if(CollectionUtil.isNotEmpty(flowUsers)){
                JsonObject<List<EmployeeVO>> byIds1 = tosEmployeeClient.findByIds(flowUsers.stream().toList());
                if(byIds1.isSuccess()){
                    String collect = byIds1.getObjEntity().stream().map(EmployeeVO::getName).collect(Collectors.joining("、"));
                    Assert.isFalse(true,"存在销售："+collect+"，有正在审批中的申请流程,请勿重新发起。");
                }
                Assert.isFalse(!byIds1.isSuccess(),booleanJsonObject.getMessage());
            }

        }

        //验证是否超过销售最大NA客户数
        checkOverNaNum(launchDTO);

        return new JsonObject<>(customerNaApproveProcessService.launch(launchDTO));
    }

    /**
     * 计算两个集合的交集（使用HashSet - 结果无序）
     * 时间复杂度：O(n+m) 空间复杂度：O(min(n,m))
     *
     * @param collection1 第一个集合
     * @param collection2 第二个集合
     * @return 包含共同元素的新集合（无重复）
     */
    public static <T> Set<T> intersection(Collection<T> collection1, Collection<T> collection2) {
        if (collection1 == null || collection2 == null) {
            return Collections.emptySet();
        }

        Set<T> set = new HashSet<>(collection1);
        set.retainAll(collection2);
        return set;
    }

    public void checkOverNaNum(CustomerNaApproveFlowLaunchDTO launchDTO) {
        boolean flag = false;
        StringBuilder errorMsg = new StringBuilder();
        errorMsg.append("提交审批失败，存在销售：");

        for (CustomerNaSalemanVo salaman : launchDTO.getSalemanVos()) {
            //查询该销售已经在流程审批中的数量
            List<CustomerNa> flowList = customerNaService.list(new QueryWrapper<CustomerNa>()
                    .inSql("process_instance_id","select process_instance_id from customer_na_main where process_state = 1")
                    .eq("na_saleman_id", salaman.getSalemanId()));

            //查询某个销售已经生效的NA数量
            JsonObject<List<CrmCustomerNaVo>> cObj = remoteCustomerNaService.salemanNalist(salaman.getSalemanId());

            //查询总NA数量
            JsonObject<CrmSalemanVo> jObj = remoteSalemanService.getInfo(salaman.getSalemanId());
            if(jObj.isSuccess() && cObj.isSuccess()){
                CrmSalemanVo salemanVo = jObj.getObjEntity();
                List<CrmCustomerNaVo> okList = cObj.getObjEntity();
                if((flowList.size() + okList.size()) >= salemanVo.getNaNum()) {
                    flag = true;
                    errorMsg.append("["+salemanVo.getName()+"]剩余可申请数"+(salemanVo.getNaNum()-okList.size()-flowList.size())+",");
                }
            }
        }
        com.baomidou.mybatisplus.core.toolkit.Assert.isFalse(flag,errorMsg.delete(errorMsg.length()-1,errorMsg.length()).append(".请重新编辑后提交").toString());
    }
}
