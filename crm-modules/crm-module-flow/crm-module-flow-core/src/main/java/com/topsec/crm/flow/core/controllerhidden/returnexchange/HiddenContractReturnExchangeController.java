package com.topsec.crm.flow.core.controllerhidden.returnexchange;

import com.topsec.crm.flow.api.dto.returnexchange.ReturnExchangeDetailVO;
import com.topsec.crm.flow.api.dto.returnexchange.ReturnExchangeProductVO;
import com.topsec.crm.flow.core.mapstruct.ReturnExchangeConvertor;
import com.topsec.crm.flow.core.service.ContractReviewReturnExchangeService;
import com.topsec.crm.flow.core.service.ReturnExchangeProductService;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/contractReturnExchangeFeign")
@Tag(name = "合同评审退换货-不对外开放", description = "/contractReturnExchangeFeign")
@RequiredArgsConstructor
@Slf4j
public class HiddenContractReturnExchangeController {

    private final ContractReviewReturnExchangeService contractReviewReturnExchangeService;
    private final ReturnExchangeProductService returnExchangeProductService;

    @PostMapping("/queryCancelByContractNumber")
    public JsonObject<List<ReturnExchangeDetailVO>> queryCancelByContractNumber(@RequestBody List<String> contractNumbers) {
        return new JsonObject<>(contractReviewReturnExchangeService.queryCancelByContractNumber(contractNumbers));
    }

    @PostMapping("/listByReturnExchangeProductId")
    public JsonObject<List<ReturnExchangeProductVO>> listByReturnExchangeProductId(@RequestBody Set<String> returnExchangeProductIds) {
        return new JsonObject<>(ReturnExchangeConvertor.INSTANCE.toReturnExchangeProductVO(returnExchangeProductService.listByReturnExchangeId(new ArrayList<>(returnExchangeProductIds))));
    }
}
