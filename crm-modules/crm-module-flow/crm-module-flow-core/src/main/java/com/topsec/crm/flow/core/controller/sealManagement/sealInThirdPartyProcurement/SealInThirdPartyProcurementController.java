package com.topsec.crm.flow.core.controller.sealManagement.sealInThirdPartyProcurement;

import com.topsec.crm.flow.api.dto.sealApplication.SealApplicationFlowLaunchDTO;
import com.topsec.crm.flow.api.vo.sealApplication.SealApplicationInfoVO;
import com.topsec.crm.flow.api.vo.sealApplication.SealInThirdPartyProcurementVo;
import com.topsec.crm.flow.core.process.impl.SealInThirdPartyProcurementProcessService;
import com.topsec.crm.flow.core.service.SealApplicationService;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.json.Json;

@RestController
@RequestMapping("/sealInThirdPartyProcurement")
@Tag(name = "【第三方采购印鉴申请】", description = "sealInThirdPartyProcurement")
@RequiredArgsConstructor
@Validated
public class SealInThirdPartyProcurementController extends BaseController {


    private final SealInThirdPartyProcurementProcessService sealInThirdPartyProcurementProcessService;
    private final SealApplicationService sealApplicationService;



    @PostMapping("/launch")
    @Operation(summary = "发起第三方采购印鉴流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody SealApplicationFlowLaunchDTO sealApplicationFlowLaunchDTO) {
        return new JsonObject<>(sealInThirdPartyProcurementProcessService.launch(sealApplicationFlowLaunchDTO));
    }


//    @GetMapping("/linkSealToProcess")
//    @Operation(summary = "第三方采购印鉴关联流程")
//    public JsonObject<SealInThirdPartyProcurementVo> linkSealToProcess(@RequestParam String processInstanceId) {
//        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
//        return new JsonObject<>(sealApplicationService.queryLinkSealToProcess(processInstanceId));
//    }



}
