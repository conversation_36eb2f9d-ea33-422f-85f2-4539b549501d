package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.core.entity.PriceReviewSigningAgent;
import com.topsec.crm.project.api.entity.CrmProjectSigningAgentVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PriceReviewSigningAgentConvertor {

    PriceReviewSigningAgentConvertor INSTANCE = Mappers.getMapper(PriceReviewSigningAgentConvertor.class);

    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "processInstanceId", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "id", ignore = true)
    PriceReviewSigningAgent toAgent(CrmProjectSigningAgentVo agentVO);
    List<PriceReviewSigningAgent> toAgentList(List<CrmProjectSigningAgentVo> agentVOList);
}
