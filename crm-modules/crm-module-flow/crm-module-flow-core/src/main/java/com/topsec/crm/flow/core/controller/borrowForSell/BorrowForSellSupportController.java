package com.topsec.crm.flow.core.controller.borrowForSell;

import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.flow.api.RemoteFlowPriceReviewService;
import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellProductSnDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.service.ITargetedInventoryPreparationMainService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.product.api.RemoteProductAgentInventoryService;
import com.topsec.crm.product.api.entity.AgentInventoryVO;
import com.topsec.crm.project.api.client.*;
import com.topsec.crm.project.api.entity.AgentTreeSelect;
import com.topsec.crm.project.api.entity.CrmProjectSigningAgentVo;
import com.topsec.crm.project.api.entity.CrmTtargetedInventoryPreparationMainVO;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.vo.TfsFormContentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 借转销支撑接口 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/borrowForSellSupport")
@Tag(name = "借转销流程支撑接口", description = "/borrowForSellSupport")
public class BorrowForSellSupportController extends BaseController {

    @Autowired
    private TfsFormContentClient tfsformContentClient;

    @Autowired
    private RemoteProjectSignAgentClient remoteProjectSignAgentClient;

    @Autowired
    private RemoteProductAgentInventoryService remoteProductAgentInventoryService;

    @Autowired
    private ITargetedInventoryPreparationMainService targetedInventoryPreparationMainService;

    @Autowired
    private RemoteFlowPriceReviewService remoteFlowPriceReviewService;

    @PreFlowPermission
    @GetMapping("/queryProjectSignInfoTree")
    @Operation(summary = "查询项目签约关系")
    public JsonObject<List<AgentTreeSelect>> queryProjectSignInfoTree(@RequestParam String projectId,@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(projectId.equals(tfsFormContentVo.getProjectId())){
                return remoteProjectSignAgentClient.tree(projectId);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @GetMapping("/queryStockProductSn")
    @Operation(summary = "根据物料代码查询国代库存序列号信息")
    public JsonObject<List<BorrowForSellProductSnDTO>> queryStockProductSn(@RequestParam String customerName, @RequestParam String stuffCode, @RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        JsonObject<List<AgentInventoryVO>> listJsonObject = remoteProductAgentInventoryService.queryProductAgentInventorySnInfo(customerName, stuffCode);
        List<BorrowForSellProductSnDTO> result = new ArrayList<>();
        if(listJsonObject.isSuccess() && CollectionUtils.isNotEmpty(listJsonObject.getObjEntity())){
            List<AgentInventoryVO> objEntity = listJsonObject.getObjEntity();
            objEntity.stream().forEach(item -> {
                BorrowForSellProductSnDTO borrowForSellProductSnAgentDTO = new BorrowForSellProductSnDTO();
                borrowForSellProductSnAgentDTO.setPsn(item.getInSerialNumber());
                borrowForSellProductSnAgentDTO.setStorageTime(item.getAcceptanceTime());
                result.add(borrowForSellProductSnAgentDTO);
            });
        }
        return new JsonObject<List<BorrowForSellProductSnDTO>>(result);
    }

    @PreFlowPermission
    @PostMapping("/selectSigningAgentDetail")
    @Operation(summary = "查询签约渠道详情")
    public JsonObject<CrmAgentVo> selectSigningAgentDetail(@RequestBody CrmProjectSigningAgentVo crmProjectSigningAgentVo, @RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(crmProjectSigningAgentVo.getProjectId().equals(tfsFormContentVo.getProjectId())){
                return remoteProjectSignAgentClient.selectSigningAgentDetail(crmProjectSigningAgentVo);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @PostMapping("/queryStockInfo")
    @Operation(summary = "根据专项备货stockId列表批量查询专项备货单号、销售人员、销售单位信息")
    public JsonObject<List<CrmTtargetedInventoryPreparationMainVO>> queryStockInfo(@RequestBody List<String> stockIds, @RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<CrmTtargetedInventoryPreparationMainVO> crmTtargetedInventoryPreparationMainVOS = targetedInventoryPreparationMainService.batchStockInfoByNumbers(stockIds);
        return new JsonObject<List<CrmTtargetedInventoryPreparationMainVO>>(crmTtargetedInventoryPreparationMainVOS);
    }

    @PreFlowPermission
    @PostMapping("/isTheLatestPassedPriceReviewValid")
    @Operation(summary = "查询项目中的价审是否有效")
    public JsonObject<Boolean> isTheLatestPassedPriceReviewValid(@RequestParam String projectId, @RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(projectId.equals(tfsFormContentVo.getProjectId())){
                return remoteFlowPriceReviewService.isTheLatestPassedPriceReviewValid(projectId);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }
}

