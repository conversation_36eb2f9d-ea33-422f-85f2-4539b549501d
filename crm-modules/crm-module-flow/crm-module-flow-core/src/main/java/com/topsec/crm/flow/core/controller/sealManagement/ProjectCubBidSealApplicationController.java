package com.topsec.crm.flow.core.controller.sealManagement;

import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.AgentBaseQuery;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.flow.api.dto.sealApplication.SealApplicationFlowLaunchDTO;
import com.topsec.crm.flow.core.process.impl.ProjectCubBidSealApplicationProcessService;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.query.BiddingProcessQuery;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsFormContentClient;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2025-05-19  17:43
 * @Description:
 */
@RestController
@RequestMapping("/projectCubBidSealApplication")
@Tag(name = "项目、客户、印鉴类申请", description = "/projectCubBidSealApplication")
@RequiredArgsConstructor
@Validated
public class ProjectCubBidSealApplicationController extends BaseController {

    private final ProjectCubBidSealApplicationProcessService projectCubBidSealApplicationProcessService;

    private final RemoteAgentService remoteAgentService;

    private final TfsFormContentClient tfsFormContentClient;





    @PostMapping("/launch")
    @Operation(summary = "发起项目、客户、印鉴类申请流程")
    @PreAuthorize(hasPermission = "crm_seal_apply_add",dataScope = "crm_seal_apply_add")
    public JsonObject<Boolean> launch(@Valid @RequestBody SealApplicationFlowLaunchDTO launchDTO) {
        return new JsonObject<>(projectCubBidSealApplicationProcessService.launch(launchDTO));
    }

    @PostMapping("/agentBaseInfoPage")
    @Operation(summary = "选择授权对象单位")
    @PreAuthorize(hasPermission = "crm_seal_apply_add",dataScope = "crm_seal_apply_add")
    public JsonObject<PageUtils<CrmAgentVo>> baseInfoPage(@RequestBody AgentBaseQuery query){
        return remoteAgentService.baseInfoPage(query);

    }
    @PostMapping("/findBiddingProcessList")
    @Operation(summary = "关联投标管理单号")
    @PreAuthorize(hasPermission = "crm_seal_apply_add",dataScope = "crm_seal_apply_add")
    JsonObject<List<Map<String, Object>>> findBiddingProcessList(@RequestBody BiddingProcessQuery biddingProcessQuery){
        biddingProcessQuery.setPersonId(UserInfoHolder.getCurrentPersonId());
        return tfsFormContentClient.findBiddingProcessList(biddingProcessQuery);
    }



}