package com.topsec.crm.flow.core.controller.collectionletters;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.dto.collectionhandle.CollectionLettersDTO;
import com.topsec.crm.flow.api.dto.collectionhandle.CollectionLettersExportBean;
import com.topsec.crm.flow.api.dto.collectionhandle.CollectionLettersHandleDTO;
import com.topsec.crm.flow.api.dto.collectionhandle.CollectionLettersQuery;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewNoticeArriveDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ContractCollectionLetterMain;
import com.topsec.crm.flow.core.entity.ContractCollectionLettersHandle;
import com.topsec.crm.flow.core.entity.ContractReviewMain;
import com.topsec.crm.flow.core.entity.ContractReviewNoticeArrive;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.constants.TosConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@RestController
@AllArgsConstructor
@RequestMapping("/collectionLetters")
@Tag(name = "催款函",  description = "/collectionLetters")
public class ContractCollectionLettersController extends BaseController {

    private final ContractCollectionLetterService letterService;
    private final ContractCollectionLettersHandleService handleService;
    private final RemoteContractExecuteService contractExecuteService;
    private final ContractCollectionLetterMainService collectionLetterMainService;
    private final ContractReviewMainService mainService;
    private final ContractReviewNoticeArriveService noticeArriveService;


    @GetMapping("/checkHandleSuggest")
    @Operation(summary = "01步办结，检查处理意见")
    @PreFlowPermission
    public JsonObject<Boolean> checkHandleSuggest(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(letterService.handleSuggest(processInstanceId));
    }

    @GetMapping("/checkLetterInfo")
    @Operation(summary = "02步办结，检查催款函信息")
    @PreFlowPermission
    public JsonObject<Boolean> checkLetterInfo(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(letterService.letterInfo(processInstanceId));
    }

    @PostMapping("/pageCollectionLetters")
    @Operation(summary = "催款函列表")
    @PreAuthorize(hasPermission = "crm_contract_receivable_collection_letter", dataScope = "crm_contract_receivable_collection_letter")
    public JsonObject<PageUtils<CollectionLettersDTO>> pageCollectionLetters(@RequestBody CollectionLettersQuery query) {
        startPage();
        return new JsonObject<>(letterService.pageCollectionLetters(query));
    }

    @PostMapping("/exportCollectionLetters")
    @Operation(summary = "催款函列表导出")
    @PreAuthorize(hasPermission = "crm_contract_receivable_collection_letter_export", dataScope = "crm_contract_receivable_collection_letter_export")
    public void exportCollectionLetters(@RequestBody CollectionLettersQuery query) throws IOException {
        PageUtils<CollectionLettersDTO> pageUtils = letterService.pageCollectionLetters(query);
        List<CollectionLettersDTO> list = pageUtils.getList();
        ExcelUtil<CollectionLettersExportBean> excelUtil = new ExcelUtil<>(CollectionLettersExportBean.class);
        excelUtil.exportExcel(response, HyperBeanUtils.copyListProperties(list, CollectionLettersExportBean::new),"催款函");
    }



    @PostMapping("/saveOrUpdate")
    @Operation(summary = "修改或保存处理意见")
    @PreFlowPermission
    public JsonObject<Boolean> saveOrUpdate(@RequestBody CollectionLettersHandleDTO dto) {
        if (dto.getId() == null) {
            // 新增的时候判断流程id是否一致
            PreFlowPermissionAspect.checkProcessInstanceId(dto.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        } else {
            // 修改的时候 根据id查出来 再判断流程id是否一致
            String id = dto.getId();
            ContractCollectionLettersHandle handle = handleService.getById(id);
            if (handle == null) {
                throw new CrmException("参数有误");
            } else {
                // 权限点一样的 就不让他改这个id 先不抽取update vo
                dto.setProcessInstanceId(null);
                PreFlowPermissionAspect.checkProcessInstanceId(handle.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            }
        }
        return new JsonObject<>(handleService.saveOrUpdate(dto));
    }

    @GetMapping("/getContractInfoByProcessInstanceId")
    @Operation(summary = "根据流程id查询合同信息")
    @PreFlowPermission
    public JsonObject<CrmContractExecuteVO> getContractInfoByContractNumber(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        // 其他线程可能影响这个接口
        PageHelper.clearPage();
        ContractCollectionLetterMain letterMain = collectionLetterMainService.getByProcessInstanceId(processInstanceId);
        String contractNumber = letterMain.getContractNumber();
        return contractExecuteService.getByContractNumber(contractNumber);
    }

    @GetMapping("/getLetterHandleByProcessInstanceId")
    @Operation(summary = "根据流程实例id查询催款函处理意见")
    @PreFlowPermission
    public JsonObject<CollectionLettersHandleDTO> getLetterHandleByProcessInstanceId(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        // 其他线程可能影响这个接口
        PageHelper.clearPage();
        CollectionLettersHandleDTO result = new CollectionLettersHandleDTO();
        CollectionLettersHandleDTO handleDTO = handleService.getByProcessInstanceId(processInstanceId);
        if (handleDTO != null) {
            HyperBeanUtils.copyProperties(handleDTO, result);
        }
        ContractCollectionLetterMain letterMain = collectionLetterMainService.getByProcessInstanceId(processInstanceId);
        result.setProcessNumber(letterMain.getProcessNumber());
        return new JsonObject<>(result);
    }

    @GetMapping("/getNoticeArriveByProcessInstanceId")
    @Operation(summary = "根据流程实例id查询通知和送达信息")
    @PreFlowPermission
    public JsonObject<List<ContractReviewNoticeArriveDTO>> getNoticeArriveByProcessInstanceId(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ContractCollectionLetterMain letterMain = collectionLetterMainService.getByProcessInstanceId(processInstanceId);
        String contractNumber = letterMain.getContractNumber();
        ContractReviewMain main = mainService.getOne(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getContractNumber, contractNumber)
                .eq(ContractReviewMain::getDelFlag, 0).last("limit 1"));

        return new JsonObject<>(HyperBeanUtils.copyListProperties(noticeArriveService.list(new LambdaQueryWrapper<ContractReviewNoticeArrive>()
                .eq(ContractReviewNoticeArrive::getContractReviewMainId, main.getId())
                .eq(ContractReviewNoticeArrive::getDelFlag, 0)), ContractReviewNoticeArriveDTO::new));
    }



}
