package com.topsec.crm.flow.core.controller.returnExchange;

import com.topsec.crm.flow.api.RemoteFlowContractInvoiceService;
import com.topsec.crm.flow.api.dto.contractInvoice.ContractInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.RevenueRecognitionDTO;
import com.topsec.crm.flow.api.vo.ProcessFileInfoVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.ContractReviewReturnExchange;
import com.topsec.crm.flow.core.entity.ReturnExchangeProduct;
import com.topsec.crm.flow.core.entity.ReturnExchangeProductThird;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/business/contractReturnExchange")
@Tag(name = "合同评审退换货business", description = "合同评审退换货business")
@RequiredArgsConstructor
public class ContractReturnExchangeBusinessController extends BaseController {

    private final ContractReviewReturnExchangeService contractReviewReturnExchangeService;
    private final ReturnExchangeProductService returnExchangeProductService;
    private final ReturnExchangeProductThirdService returnExchangeProductThirdService;
    private final ContractReviewPaymentProvisionService contractReviewPaymentProvisionService;
    private final ContractReviewRevenueRecognitionService contractReviewRevenueRecognitionService;
    private final ProcessFileInfoService processFileInfoService;
    private final RemoteFlowContractInvoiceService remoteFlowContractInvoiceService;
    private final ContractInvoiceExtendService contractInvoiceExtendService;

    @GetMapping("/detail")
    @Operation(summary = "退换货流程基础信息")
    @PreAuthorize(hasPermission = "crm_refund", dataScope = "crm_refund")
    public JsonObject<ContractReviewReturnExchange> returnExchangeContractInfo(@RequestParam String processInstanceId) {
        checkReturnExchangeByProcessInstanceId(processInstanceId);
        ContractReviewReturnExchange returnExchangeBaseVO = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        return new JsonObject<>(returnExchangeBaseVO);
    }

    @GetMapping("/returnExchangeReProduct")
    @Operation(summary = "查询退货换货旧产品详情")
    @PreAuthorize(hasPermission = "crm_refund", dataScope = "crm_refund")
    public JsonObject<List<ReturnExchangeProduct>> returnExchangeDetail(@RequestParam String processInstanceId) {
        checkReturnExchangeByProcessInstanceId(processInstanceId);
        return new JsonObject<>(returnExchangeProductService.listOldProductByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/returnExchangeNewProduct")
    @Operation(summary = "查询退货换货新产品详情")
    @PreAuthorize(hasPermission = "crm_refund", dataScope = "crm_refund")
    public JsonObject<List<ReturnExchangeProduct>> returnExchangeNewProduct(@RequestParam String processInstanceId) {
        checkReturnExchangeByProcessInstanceId(processInstanceId);
        return new JsonObject<>(returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/returnExchangeReProductThird")
    @Operation(summary = "查询退货换货旧第三方产品详情")
    @PreAuthorize(hasPermission = "crm_refund", dataScope = "crm_refund")
    public JsonObject<List<ReturnExchangeProductThird>> returnExchangeReProductThird(@RequestParam String processInstanceId) {
        checkReturnExchangeByProcessInstanceId(processInstanceId);
        return new JsonObject<>(returnExchangeProductThirdService.listOldProductByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/returnExchangeNewProductThird")
    @Operation(summary = "查询退货换货新第三方产品详情")
    @PreAuthorize(hasPermission = "crm_refund", dataScope = "crm_refund")
    public JsonObject<List<ReturnExchangeProductThird>> returnExchangeNewProductThird(@RequestParam String processInstanceId) {
        checkReturnExchangeByProcessInstanceId(processInstanceId);
        return new JsonObject<>(returnExchangeProductThirdService.listNewProductByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/paymentProvisions")
    @Operation(summary = "查询合同付款条款")
    @PreAuthorize(hasPermission = "crm_refund", dataScope = "crm_refund")
    public JsonObject<List<PaymentProvisionDTO>> payment(@RequestParam String processInstanceId) {
        checkReturnExchangeByProcessInstanceId(processInstanceId);
        return new JsonObject<>(HyperBeanUtils.copyListProperties(contractReviewPaymentProvisionService.getByReturnExchangeProcessInstanceId(processInstanceId), PaymentProvisionDTO::new));
    }

    @GetMapping("/revenueRecognitions")
    @Operation(summary = "查询新产品的收入确认条款")
    @PreAuthorize(hasPermission = "crm_refund", dataScope = "crm_refund")
    public JsonObject<List<RevenueRecognitionDTO>> revenueRecognition(@RequestParam String processInstanceId) {
        checkReturnExchangeByProcessInstanceId(processInstanceId);
        List<RevenueRecognitionDTO> revenueRecognitionDTOS = HyperBeanUtils.copyListProperties(contractReviewRevenueRecognitionService.getByReturnExchangeProcessInstanceId(processInstanceId), RevenueRecognitionDTO::new);
        // 要过滤出是新产品的
        List<ReturnExchangeProduct> returnExchangeProducts = returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId);
        List<ReturnExchangeProductThird> returnExchangeProductThirds = returnExchangeProductThirdService.listNewProductByProcessInstanceId(processInstanceId);
        Set<String> returnExchangeProductIds = new HashSet<>();
        ListUtils.emptyIfNull(returnExchangeProducts).forEach(returnExchangeProduct -> returnExchangeProductIds.add(returnExchangeProduct.getId()));
        ListUtils.emptyIfNull(returnExchangeProductThirds).forEach(returnExchangeProductThird -> returnExchangeProductIds.add(returnExchangeProductThird.getId()));
        List<RevenueRecognitionDTO> list = revenueRecognitionDTOS.stream().filter(revenueRecognitionDTO -> returnExchangeProductIds.contains(revenueRecognitionDTO.getProductOwnId()) || returnExchangeProductIds.contains(revenueRecognitionDTO.getProductThirdId())).toList();
        return new JsonObject<>(list);
    }

    // todo 合同发货

    @GetMapping("/files")
    @Operation(summary = "退换货附件")
    @PreAuthorize(hasPermission = "crm_refund", dataScope = "crm_refund")
    public JsonObject<List<ProcessFileInfoVO>> files(@RequestParam String processInstanceId) {
        checkReturnExchangeByProcessInstanceId(processInstanceId);
        return new JsonObject<>(processFileInfoService.queryByProcessInstanceId(processInstanceId, null, null));
    }

    @GetMapping("/getInvoiceProcessPage")
    @Operation(summary = "发票")
    @PreAuthorize(hasPermission = "crm_refund", dataScope = "crm_refund")
    public JsonObject<TableDataInfo> getInvoiceProcessPage(@RequestParam String processInstanceId, @RequestParam Integer pageSize, @RequestParam Integer pageNum) {
        checkReturnExchangeByProcessInstanceId(processInstanceId);
        ContractReviewReturnExchange returnExchangeBaseVO = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        return remoteFlowContractInvoiceService.getInvoiceProcessPage(returnExchangeBaseVO.getContractNumber(), pageSize, pageNum);
    }

    @GetMapping("/getInvoiceAmount")
    @Operation(summary = "根据退换货流程ID获取开票金额", description = "开票金额")
    @PreAuthorize(hasPermission = "crm_refund", dataScope = "crm_refund")
    public JsonObject<ContractInfoDTO> getInvoiceAmount(@RequestParam String processInstanceId) {
        checkReturnExchangeByProcessInstanceId(processInstanceId);
        ContractReviewReturnExchange returnExchangeBaseVO = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        ContractInfoDTO result = new ContractInfoDTO();
        CompletableFuture.allOf(CompletableFuture.supplyAsync(() ->
                        contractInvoiceExtendService.getMakeInvoiceAmountTotal(returnExchangeBaseVO.getContractNumber())).thenAccept(result::setMakeInvoiceAmount),
                CompletableFuture.supplyAsync(() ->
                        contractInvoiceExtendService.getReturnInvoiceAmountTotal(returnExchangeBaseVO.getContractNumber())).thenAccept(result::setReturnInvoiceAmount)
        );
        return new JsonObject<>(result);
    }

    private void checkReturnExchangeByProcessInstanceId(String processInstanceId) {
        ContractReviewReturnExchange contractReviewReturnExchange = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        if (contractReviewReturnExchange == null) {
            throw new CrmException("参数信息有误");
        }
        String createUser = contractReviewReturnExchange.getCreateUser();
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        if (personIdList == null) {
            return;
        }
        if (!personIdList.contains(createUser)) {
            // 不包含退换货的创建人id 代表没权限
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

    }

}
