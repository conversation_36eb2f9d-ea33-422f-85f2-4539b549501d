package com.topsec.crm.flow.core.controller.projectReport;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.topsec.crm.account.api.client.RemoteAccountService;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.dto.projectRadio.ProjectRadioMainVo;
import com.topsec.crm.flow.api.dto.projectReport.ProjectReportFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.projectReport.ProjectReportMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ProjectRadioMain;
import com.topsec.crm.flow.core.entity.ProjectReportMain;
import com.topsec.crm.flow.core.process.impl.ProjectReportAgentProcessService;
import com.topsec.crm.flow.core.process.impl.ProjectReportCompanyProcessService;
import com.topsec.crm.flow.core.service.IProjectRadioMainService;
import com.topsec.crm.flow.core.service.IProjectReportMainService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ProjectMemberEnum;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.project.api.client.*;
import com.topsec.crm.project.api.dto.ProjectMemberQuery;
import com.topsec.crm.project.api.entity.*;
import com.topsec.enums.ApprovalStatusEnum;

import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbsapi.client.TbsCrmClient;
import com.topsec.tbsapi.client.TbsPersonClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.AccountAgentRelVO;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.tos.common.vo.process.DetailBaseVO;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/projectReport")
@Tag(name = "项目报备", description = "/projectReport")
@RequiredArgsConstructor
public class ProjectReportMainController extends BaseController {

    @Autowired
    private IProjectReportMainService projectReportMainService;
    @Autowired
    private ProjectReportCompanyProcessService projectReportCompanyProcessService;
    @Autowired
    private ProjectReportAgentProcessService projectReportAgentProcessService;
    @Autowired
    private IProjectRadioMainService projectRadioMainService;
    @Autowired
    private RemoteProjectProductOwnClient remoteProjectProductOwnClient;
    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    @Autowired
    private RemoteProjectAgentClient remoteProjectAgentClient;
    @Autowired
    private TosEmployeeClient tosEmployeeClient;
    @Autowired
    private RemoteCustomerService remoteCustomerService;
    @Autowired
    private RemoteProjectMemberClient remoteProjectMemberClient;
    @Autowired
    private RemoteAgentService remoteAgentService;
    @Autowired
    private TfsNodeClient tfsNodeClient;
    @Autowired
    private TbsCrmClient tbsCrmClient;
    @Autowired
    private TbsPersonClient tbsPersonClient;
    @Autowired
    private RemoteProjectSignAgentClient remoteProjectSignAgentClient;
    @Autowired
    private RemoteAccountService remoteAccountService;

    @PostMapping("/page")
    @Operation(summary = "项目报备分页列表")
    @PreAuthorize(hasPermission = "crm_project_report",dataScope = "crm_project_report")
    public JsonObject<PageUtils<ProjectReportMainVo>> selectPage(@RequestBody ProjectReportMainVo projectReportMainVo){
        List<ProjectReportMainVo> listVo = new ArrayList<ProjectReportMainVo>();

        List<String> personIds = new ArrayList<String>();
        Set<String> projectIds = new HashSet<String>();
        if(StringUtils.isNotBlank(projectReportMainVo.getKeyword())) {
            if (projectReportMainVo.getSearchType() == 4) {
                //查询部门下所有的人员ID
                // 获取部门所有人员ID
                personIds = remoteAccountService
                        .findEmployeeByDeptId(projectReportMainVo.getKeyword()).getObjEntity()
                        .stream().map(DetailBaseVO::getUuid).toList();
                if(CollectionUtils.isEmpty(personIds)) {
                    return new JsonObject<>(new PageUtils<ProjectReportMainVo>());
                }
            } else if (projectReportMainVo.getSearchType() == 7) {
                //查询代理商下所有的人员ID
                personIds = tbsCrmClient.listPersonAccountByAgentId(projectReportMainVo.getKeyword())
                        .getObjEntity().stream().map(PersonVO::getUuid).toList().stream().toList();
                if(CollectionUtils.isEmpty(personIds)) {
                    return new JsonObject<>(new PageUtils<ProjectReportMainVo>());
                }
            } else if (projectReportMainVo.getSearchType() == 3 || projectReportMainVo.getSearchType() == 6) {
                Set<String> directlyProjectIds = remoteProjectDirectlyClient.queryProjectIdsByParam(new ProjectIdQueryParam(projectReportMainVo.getSearchType(), projectReportMainVo.getKeyword())).getObjEntity();
                Set<String> agentProjectIds =  remoteProjectAgentClient.queryProjectIdsByParam(new ProjectIdQueryParam(projectReportMainVo.getSearchType(), projectReportMainVo.getKeyword())).getObjEntity();

                projectIds.addAll(directlyProjectIds);
                projectIds.addAll(agentProjectIds);
                if(CollectionUtils.isEmpty(projectIds)) {
                    return new JsonObject<>(new PageUtils<ProjectReportMainVo>());
                }
            }
        }


        List<ProjectReportMain> list = new ArrayList<ProjectReportMain>();
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        startPage();
        if(StringUtils.isNotBlank(projectReportMainVo.getKeyword())) {
            list = projectReportMainService.query()
                    .eq(StringUtils.isNotNull(projectReportMainVo.getProcessState()), "process_state", projectReportMainVo.getProcessState())
                    .eq("del_flag", false)
                    .lt(projectReportMainVo.getIsHistory() != null && projectReportMainVo.getIsHistory() == 1, "protect_end_date", LocalDate.now())
                    .and(projectReportMainVo.getIsHistory() == null || projectReportMainVo.getIsHistory() == 0,
                            wrapper -> wrapper.lt("protect_start_date", LocalDate.now())
                                    .gt("protect_end_date", LocalDate.now())
                                    .or().eq("process_state", 1)
                    )
                    .in(CollectionUtil.isNotEmpty(dataScopeParam.getPersonIdList()), "create_user", dataScopeParam.getPersonIdList())
                    .eq(projectReportMainVo.getSearchType() == 1, "process_number", projectReportMainVo.getKeyword())
                    .like(projectReportMainVo.getSearchType() == 2, "project_name", projectReportMainVo.getKeyword())
                    .in(projectReportMainVo.getSearchType() == 4, "create_user", personIds)
                    .eq(projectReportMainVo.getSearchType() == 5, "create_user", projectReportMainVo.getKeyword())
                    .in(projectReportMainVo.getSearchType() == 3 || projectReportMainVo.getSearchType() == 6, "project_id", projectIds)
                    .in(projectReportMainVo.getSearchType() == 7, "create_user", personIds)
                    .orderByDesc("create_time")
                    .list();
        }else{
            list = projectReportMainService.query()
                    .eq(StringUtils.isNotNull(projectReportMainVo.getProcessState()),"process_state", projectReportMainVo.getProcessState())
                    .eq("del_flag", false)
                    .lt(projectReportMainVo.getIsHistory() != null && projectReportMainVo.getIsHistory() == 1, "protect_end_date", LocalDate.now())
                    .and(projectReportMainVo.getIsHistory() == null || projectReportMainVo.getIsHistory() == 0,
                            wrapper -> wrapper.lt("protect_start_date", LocalDate.now())
                                    .gt("protect_end_date", LocalDate.now())
                            .or().eq("process_state", 1)
                    )
                    .in(CollectionUtil.isNotEmpty(dataScopeParam.getPersonIdList()), "create_user", dataScopeParam.getPersonIdList())
                    .orderByDesc("create_time")
                    .list();
        }

        //项目ID归类
        List<String> directlyIds = new ArrayList<String>();
        List<String> agentIds = new ArrayList<String>();
        List<String> createUserIds = new ArrayList<String>();
        for (ProjectReportMain projectReportMain : list) {
            if(projectReportMain.getProjectType() == 1){
                directlyIds.add(projectReportMain.getProjectId());
            }else if(projectReportMain.getProjectType() == 2){
                agentIds.add(projectReportMain.getProjectId());
            }
            createUserIds.add(projectReportMain.getCreateUser());
        }

        List<PersonVO> personVOSVOS = new ArrayList<PersonVO>();
        List<CrmProjectAgentVo> projectAgents = new ArrayList<CrmProjectAgentVo>();
        List<CrmProjectDirectlyVo> projectDirectlys = new ArrayList<CrmProjectDirectlyVo>();
        //1.批量查询用户信息
        JsonObject<List<PersonVO>> byIds = tbsPersonClient.listByIds(createUserIds.toArray(new String[]{}));
        if(byIds.isSuccess()){
            personVOSVOS = byIds.getObjEntity();
        }

        //2.批量查询直签项目信息
        if(CollectionUtil.isNotEmpty(directlyIds)) {
            JsonObject<List<CrmProjectDirectlyVo>> listJsonObject = remoteProjectDirectlyClient.listByIds(directlyIds);
            if(listJsonObject.isSuccess()){
                projectDirectlys = listJsonObject.getObjEntity();
            }
        }

        //3.批量查询渠道项目信息
        if(CollectionUtil.isNotEmpty(agentIds)) {
            JsonObject<List<CrmProjectAgentVo>> crmProjectAgentVoJsonObject = remoteProjectAgentClient.batchGetInfo(agentIds);
            if(crmProjectAgentVoJsonObject.isSuccess()){
                projectAgents = crmProjectAgentVoJsonObject.getObjEntity();
            }
        }

        List<CrmProjectAgentVo> finalProjectAgents = projectAgents;
        List<CrmProjectDirectlyVo> finalProjectDirectlys = projectDirectlys;
        List<PersonVO> finalEmployeeVOS = personVOSVOS;
        //4.1补充项目信息
        list.stream().forEach(reportMain -> {
            ProjectReportMainVo prmv = HyperBeanUtils.copyPropertiesByJackson(reportMain, ProjectReportMainVo.class);
            //4.1查询是否是最新的-审批通过或者驳回的项目报备，如果是才会展示重新报备按钮
            ProjectReportMain one = projectReportMainService.getOne(
                    new QueryWrapper<ProjectReportMain>()
                        .eq("project_id", prmv.getProjectId())
                        .eq("del_flag", false)
                        .orderByDesc("create_time")
                        .last("limit 1")
            );
            if(one.getId().equals(prmv.getId())){
                prmv.setIsShowReReport(one.getProcessState() == ApprovalStatusEnum.SPZ.getCode() ? false : true);
            }else{
                prmv.setIsShowReReport(false);
            }

            //4.2补全信息
            if(prmv.getProjectType() == 1){
                //查询项目详细信息
                CrmProjectDirectlyVo directlyInfo = finalProjectDirectlys.stream().filter(ra -> ra.getId().equals(prmv.getProjectId())).findFirst().orElse(null);
                if(directlyInfo != null) {
//                    prmv.setProjectName(directlyInfo.getProjectName());--25.04需求变更
                    prmv.setProjectNo(directlyInfo.getProjectNo());
                    prmv.setProjectBudget(directlyInfo.getProjectBudget());
                    prmv.setPreSigningDate(directlyInfo.getCrmProjectSigningInfo() != null ? directlyInfo.getCrmProjectSigningInfo().getPreSigningDate() : null);
                    prmv.setFinalCustomName(directlyInfo.getCrmProjectSigningInfo() != null ? directlyInfo.getCrmProjectSigningInfo().getCustomerName() : "");
                }

                //查询报备人信息
                List<PersonVO> collect = finalEmployeeVOS.stream().filter(e -> e.getUuid().equals(prmv.getCreateUser())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(collect)){
                    PersonVO personVO = collect.get(0);
                    prmv.setCreateUserName(NameUtils.getNameByPersonVO(personVO));
                    prmv.setDeptOrAgentName(personVO.getDepartmentVO() != null ? personVO.getDepartmentVO().getDeptName() : "");
                }

            }else if(prmv.getProjectType() == 2){
                CrmProjectAgentVo agentInfo = finalProjectAgents.stream().filter(ra -> ra.getId().equals(prmv.getProjectId())).findFirst().orElse(null);
                if(agentInfo != null) {
                    prmv.setProjectNo(agentInfo.getProjectNo());
//                    prmv.setProjectName(agentInfo.getProjectName());--25.04需求变更
                    prmv.setProjectBudget(agentInfo.getProjectBudget());
                    prmv.setPreSigningDate(agentInfo.getPreSigningDate());
                    prmv.setFinalCustomName(agentInfo.getFinalCustomName());
                    prmv.setDeptOrAgentName(agentInfo.getAgentInfo() != null ? agentInfo.getAgentInfo().getAgentName() : "");
                }

                //查询报备人信息
                List<PersonVO> collect = finalEmployeeVOS.stream().filter(e -> e.getUuid().equals(prmv.getCreateUser())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(collect)){
                    PersonVO personVO = collect.get(0);
                    prmv.setCreateUserName(NameUtils.getNameByPersonVO(personVO));
                }
            }

            //4.3查询项目具体审批节点
            JsonObject<Set<ApproveNode>> setJsonObject = tfsNodeClient.queryNodeByProcessInstanceId(reportMain.getProcessInstanceId());
            if(setJsonObject.isSuccess()){
                prmv.setApprovalNode(setJsonObject.getObjEntity());
            }
            listVo.add(prmv);
        });

        //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
        PageUtils dataTable = getDataTable(list,listVo);
        return new JsonObject<>(dataTable);
    }

    @GetMapping("/info")
    @Operation(summary = "项目报备详情信息")
    @PreFlowPermission
    public JsonObject<ProjectReportMainVo> info(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        ProjectReportMain projectReportMain = projectReportMainService.query().eq("process_instance_id", processInstanceId).one();
        ProjectReportMainVo prmv = HyperBeanUtils.copyPropertiesByJackson(projectReportMain, ProjectReportMainVo.class);

        //补全信息
        //1.客户信息
        String customerId = "";
        if(prmv.getProjectType() == 1){
            //查询项目详细信息
            JsonObject<CrmProjectDirectlyVo> jObject = remoteProjectDirectlyClient.getProjectInfo(prmv.getProjectId());
            if(jObject.isSuccess()){
                CrmProjectDirectlyVo objEntity = jObject.getObjEntity();
                CrmProjectSigningInfoVo signingInfo = objEntity.getCrmProjectSigningInfo();
                customerId = signingInfo.getCustomerId();
            }
        }else if(prmv.getProjectType() == 2) {
            JsonObject<CrmProjectAgentVo> jObject = remoteProjectAgentClient.getInfo(prmv.getProjectId());
            if (jObject.isSuccess()) {
                customerId = jObject.getObjEntity().getFinalCustomId();
            }

            //查询销售人员名称
            JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(prmv.getSalemanId());
            if(byId.isSuccess()){
                prmv.setSalemanName(byId.getObjEntity() != null ? NameUtils.getNameByEmployeeVO(byId.getObjEntity()) : "");
            }
        }
        Assert.isFalse(StringUtils.isEmpty(customerId),"项目最终客户不存在，请联系管理员处理。");
        JsonObject<CrmCustomerVo> customerInfo = remoteCustomerService.getCustomerInfo(customerId, UserInfoHolder.getCurrentPersonId(),false);
        if(customerInfo.isSuccess()){
            CrmCustomerVo crmCustomerVo = customerInfo.getObjEntity();
            prmv.setCustomerProvinceCode(crmCustomerVo.getProvinceCode());
            prmv.setCustomerCityCode(crmCustomerVo.getCityCode());
            prmv.setCustomerCountyCode(crmCustomerVo.getCountyCode());
            prmv.setIsNa(crmCustomerVo.getIsNa());
            prmv.setNaSalemans(crmCustomerVo.getNaSalemans());
        }

        //2.查询项目报备次数
        List<ProjectReportMain> reports = projectReportMainService.query()
                .eq("project_id", prmv.getProjectId())
                .eq("del_flag", false)
                .orderByDesc("create_time")
                .list();

        //3.查询项目广播次数
        List<ProjectRadioMain> redios = projectRadioMainService.query()
                .eq("project_id", prmv.getProjectId())
                .eq("del_flag", false)
                .orderByDesc("create_time")
                .list();

        //4.查询用户信息
        List<String> createUserIDs = reports.stream().map(ProjectReportMain::getCreateUser).collect(Collectors.toList());
        createUserIDs.addAll(redios.stream().map(ProjectRadioMain::getCreateUser).collect(Collectors.toList()));
        createUserIDs.add(prmv.getCreateUser());

        JsonObject<List<PersonVO>> listJsonObject = tbsPersonClient.listByIds(createUserIDs.toArray(new String[]{}));
        if(listJsonObject.isSuccess()){
            List<PersonVO> byIds = listJsonObject.getObjEntity();

            for (ProjectReportMain report : reports) {
                //查询报备人信息
                List<PersonVO> collect = byIds.stream().filter(e -> e.getUuid().equals(report.getCreateUser())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(collect)){
                    PersonVO personVO = collect.get(0);
                    report.setCreateUserName(NameUtils.getNameByPersonVO(personVO));
                }
            }
            for (ProjectRadioMain redio : redios) {
                List<PersonVO> collect = byIds.stream().filter(e -> e.getUuid().equals(redio.getCreateUser())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(collect)) {
                    PersonVO personVO = collect.get(0);
                    redio.setCreateUserName(NameUtils.getNameByPersonVO(personVO));
                }
            }

            List<PersonVO> collect = byIds.stream().filter(e -> e.getUuid().equals(prmv.getCreateUser())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(collect)) {
                PersonVO personVO = collect.get(0);
                prmv.setCreateUserName(NameUtils.getNameByPersonVO(personVO));
                prmv.setDeptOrAgentName(personVO.getDepartmentVO() != null ? personVO.getDepartmentVO().getDeptName() : "");

                //如果是公司项目，需要查询创建人所在工作地
                if(projectReportMain.getProjectType() == 1) {
                    JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(personVO.getUuid());
                    if (byId.isSuccess()) {
                        EmployeeVO employeeVO = byId.getObjEntity();
                        prmv.setAddress(employeeVO.getWorkPlace());
                    }
                }
            }
        }

        //渠道名称
        String accountId = AccountAccquireUtils.getAccountIdByPersonId(prmv.getCreateUser());
        JsonObject<AccountAgentRelVO> accountAgentRelVOByAccountId = tbsCrmClient.getAccountAgentRelVOByAccountId(accountId);
        if(accountAgentRelVOByAccountId.isSuccess() && accountAgentRelVOByAccountId.getObjEntity() != null){
            String agentId = accountAgentRelVOByAccountId.getObjEntity().getAgentId();
            JsonObject<CrmAgentVo> agentInfo = remoteAgentService.getAgentInfo(agentId);
            if(agentInfo.isSuccess() && agentInfo.getObjEntity() != null) {
                prmv.setDeptOrAgentName(agentInfo.getObjEntity().getAgentName());
            }
        }
        prmv.setReports(HyperBeanUtils.copyListPropertiesByJackson(reports, ProjectReportMainVo.class));
        prmv.setRadios(HyperBeanUtils.copyListPropertiesByJackson(redios, ProjectRadioMainVo.class));

        return new JsonObject<>(prmv);
    }

    @PostMapping("/companyLaunch")
    @Operation(summary = "发起公司项目报备流程")
    @PreAuthorize(hasAnyPermission = {"crm_flow_project_report","crm_project_report","crm_flow_project_report_relaunch"})
    public JsonObject<Boolean> companyLaunch(@Valid @RequestBody ProjectReportFlowLaunchDTO launchDTO){
        checkProjectAuth(launchDTO.getProjectId());

        JsonObject<List<CrmProjectSigningAgentVo>> listJsonObject = remoteProjectSignAgentClient.listAgent(launchDTO.getProjectId());
        if(listJsonObject.isSuccess() && listJsonObject.getObjEntity() != null){
            Set<String> agentIds = new HashSet<String>();
            for (CrmProjectSigningAgentVo signingAgentVo : listJsonObject.getObjEntity()) {
                //1.查询签约渠道是否允许项目报备
                if(signingAgentVo.getAgentVo() != null && signingAgentVo.getAgentVo().getId() != null){
                    Assert.isFalse(signingAgentVo.getAgentVo().getIsAllowReport() == null || signingAgentVo.getAgentVo().getIsAllowReport() == 0,
                            "系统检测到未完成技术人员认证，暂停项目报备，待完成技术人员认证后将自动恢复权限");
                }
                agentIds.add(signingAgentVo.getAgentId());
            }

            //2.查询新增的渠道是否再黑名单中
            JsonObject<List<CrmAgentVo>> ret = remoteAgentService.filterBlacklistAgent(agentIds);
            Assert.isFalse(ret.isSuccess() && CollectionUtil.isNotEmpty(ret.getObjEntity()),"项目签约渠道中存在黑名单渠道，无法发起项目报备。");
        }

        //1.判断是否存在审批中的项目报备流程，如果存在，则不让发起
        ProjectReportMain one = projectReportMainService.getOne(
                new QueryWrapper<ProjectReportMain>()
                        .eq("project_id", launchDTO.getProjectId())
                        .eq("del_flag", false)
                        .orderByDesc("create_time")
                        .last("limit 1")
        );
        Assert.isFalse(one != null && one.getProcessState() == ApprovalStatusEnum.SPZ.getCode(),"当前项目已存在审批中的项目报备流程。");

        //查询项目是否添加过产品
        JsonObject<Boolean> booleanJsonObject = remoteProjectProductOwnClient.hasOwnProduct(launchDTO.getBaseInfo().getProjectId());
        if(booleanJsonObject.isSuccess()){
            Assert.isFalse(!booleanJsonObject.getObjEntity(),"项目报备需要产品信息不为空!");
        }else{
            Assert.isFalse(true,"项目服务不可用，请稍候重试！");
        }

        return new JsonObject<>(projectReportCompanyProcessService.launch(launchDTO));
    }

    @PostMapping("/agentLaunch")
    @Operation(summary = "发起渠道项目报备流程")
    @PreAuthorize(hasAnyPermission = {"crm_agent_flow_project_report","crm_project_report","crm_flow_project_report_relaunch"})
    public JsonObject<Boolean> agentLaunch(@Valid @RequestBody ProjectReportFlowLaunchDTO launchDTO){
        checkProjectAuth(launchDTO.getProjectId());

        //1.判断是否存在审批中的项目报备流程，如果存在，则不让发起
        ProjectReportMain one = projectReportMainService.getOne(
                new QueryWrapper<ProjectReportMain>()
                        .eq("project_id", launchDTO.getProjectId())
                        .eq("del_flag", false)
                        .orderByDesc("create_time")
                        .last("limit 1")
        );
        Assert.isFalse(one != null && one.getProcessState() == ApprovalStatusEnum.SPZ.getCode(),"当前项目已存在审批中的项目报备流程。");

        //2.查询项目是否添加过产品
        JsonObject<Boolean> booleanJsonObject = remoteProjectProductOwnClient.hasOwnProduct(launchDTO.getBaseInfo().getProjectId());
        if(booleanJsonObject.isSuccess()){
            Assert.isFalse(!booleanJsonObject.getObjEntity(),"项目报备需要产品信息不为空!");
        }else{
            Assert.isFalse(true,"项目服务不可用，请稍候重试！");
        }

        //3.查询销售存在和在职状态
        JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(launchDTO.getSalemanId());
        if(byId.isSuccess() && byId.getObjEntity() != null){
            EmployeeVO entity = byId.getObjEntity();
            Assert.isFalse(!entity.getOnTheJob(), "您输入的销售人员信息有误，请重新输入！");
        }else{
            Assert.isFalse(true, "您输入的销售人员信息有误，请重新输入！");
        }

        return new JsonObject<>(projectReportAgentProcessService.launch(launchDTO));
    }

    @GetMapping("/findLastReport")
    @Operation(summary = "查询最近一次审批通过的项目报备流程信息")
    @PreAuthorize(hasPermission = "crm_project_report")
    public JsonObject<ProjectReportMainVo> findLastReport(@RequestParam String projectId){
        checkProjectAuth(projectId);

        ProjectReportMain one = projectReportMainService.getOne(new QueryWrapper<ProjectReportMain>()
                .eq("project_id", projectId)
                .eq("process_state", ApprovalStatusEnum.YSP.getCode())
                .orderByDesc("create_time")
                .last("limit 1")
        );

        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(one, ProjectReportMainVo.class));
    }

    /**
     * 渠道模块需要使用，限制当前时间再项目报备有效期内
     */
    @GetMapping("/findInProtectLastReport")
    @Operation(summary = "查询最近一次审批通过,且在保护期的项目报备流程信息")
    @PreAuthorize(hasAnyPermission = {"crm_project_directly","crm_project_agent"})
    public JsonObject<ProjectReportMainVo> findInProtectLastReport(@RequestParam String projectId){
        checkProjectAuth(projectId);

        ProjectReportMain one = projectReportMainService.getOne(new QueryWrapper<ProjectReportMain>()
                .eq("project_id", projectId)
                .eq("process_state", ApprovalStatusEnum.YSP.getCode())
                .gt("protect_end_date", LocalDateTime.now())
                .orderByDesc("create_time")
                .last("limit 1")
        );
        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(one, ProjectReportMainVo.class));
    }

    @GetMapping("/selectLastAndNoFlow")
    @Operation(summary = "查询没有流程中的项目报备的最近一次审批通过的项目报备")
    @PreAuthorize(hasPermission = "crm_project_report")
    public JsonObject<Map<String,Object>> selectLastAndNoFlow(@RequestParam String projectId){
        checkProjectAuth(projectId);

        Map<String,Object> result = new HashMap<String,Object>();

        ProjectReportMain flow = projectReportMainService.getOne(new QueryWrapper<ProjectReportMain>()
                .eq("project_id", projectId)
                .eq("process_state", ApprovalStatusEnum.SPZ.getCode())
                .last("limit 1")
        );
        if(flow != null){
            result.put("flag", false);
            result.put("message", "存在审批中的项目报备。");
            return new JsonObject<>(result);
        }

        ProjectReportMain last = projectReportMainService.getOne(new QueryWrapper<ProjectReportMain>()
                .eq("project_id", projectId)
                .eq("process_state", ApprovalStatusEnum.YSP.getCode())
                .last("limit 1")
        );
        if(last == null){
            result.put("flag", false);
            result.put("message", "暂不存在审批通过的项目报备。");
            return new JsonObject<>(result);
        }

        result.put("flag", true);
        result.put("entity",HyperBeanUtils.copyPropertiesByJackson(last, ProjectReportMainVo.class));
        return new JsonObject<>(result);
    }

    /**
     * 查询当前项目项目负责人-所属渠道信息
     */
    @GetMapping("/findProjectLeaderAgent")
    @Operation(summary = "查询当前项目项目负责人-所属渠道信息")
    @PreAuthorize(hasAnyPermission = {"crm_project_report","crm_project_agent"})
    public JsonObject<CrmAgentVo> findProjectLeaderAgent(@RequestParam String projectId){
        checkProjectAuth(projectId);

        ProjectMemberQuery memberQuery = new ProjectMemberQuery();
        memberQuery.setProjectIds(Collections.singleton(projectId));
        memberQuery.setRole(ProjectMemberEnum.ProjectMemberRoleEnum.PROJECT_LEADER.getCode());
        JsonObject<List<CrmProjectMemberVO>> projectLeaderByQueryParam = remoteProjectMemberClient.getProjectLeaderByQueryParam(memberQuery);
        Assert.isFalse(!projectLeaderByQueryParam.isSuccess() || projectLeaderByQueryParam.getObjEntity() == null
                || CollectionUtil.isEmpty(projectLeaderByQueryParam.getObjEntity()), "暂未查询到项目负责人。");

        //渠道项目负责人
        CrmProjectMemberVO crmProjectMemberVO = projectLeaderByQueryParam.getObjEntity().get(0);
        JsonObject<Map<String, CrmAgentVo>> mapJsonObject = remoteAgentService.batchGetAgentInfoByPersonIds(Collections.singletonList(crmProjectMemberVO.getPersonId()));
        CrmAgentVo agentVo = null;
        if(mapJsonObject.isSuccess()){
            Map<String, CrmAgentVo> objEntity = mapJsonObject.getObjEntity();
            agentVo = objEntity.get(crmProjectMemberVO.getPersonId());
        }
        Assert.isFalse(agentVo == null,"暂未查询到该项目负责人所属渠道，无法发起项目报备");
        return new JsonObject<>(agentVo);
    }

    /**
     * 查询项目负责人的AccountId
     */
    @GetMapping("/findProjectLeaderAccountID")
    @Operation(summary = "查询项目负责人的AccountId")
    @PreAuthorize(hasPermission = "crm_project_report")
    public JsonObject<CrmProjectMemberVO> findProjectLeaderAccountID(@RequestParam String projectId){
        checkProjectAuth(projectId);

        ProjectMemberQuery memberQuery = new ProjectMemberQuery();
        memberQuery.setProjectIds(Collections.singleton(projectId));
        memberQuery.setRole(ProjectMemberEnum.ProjectMemberRoleEnum.PROJECT_LEADER.getCode());
        JsonObject<List<CrmProjectMemberVO>> projectLeaderByQueryParam = remoteProjectMemberClient.getProjectLeaderByQueryParam(memberQuery);
        Assert.isFalse(!projectLeaderByQueryParam.isSuccess() || projectLeaderByQueryParam.getObjEntity() == null
            || CollectionUtil.isEmpty(projectLeaderByQueryParam.getObjEntity()), "暂未查询到项目负责人。");
        //渠道项目负责人
        CrmProjectMemberVO crmProjectMemberVO = projectLeaderByQueryParam.getObjEntity().get(0);
        crmProjectMemberVO.setAccountId(AccountAccquireUtils.getAccountIdByPersonId(crmProjectMemberVO.getPersonId()));
        return new JsonObject<>(crmProjectMemberVO);
    }

    /**
     * 废弃
     * 查询所属渠道是否是战略渠道
     */
    @GetMapping("/findZLQD")
    @Operation(summary = "查询所属渠道是否是战略渠道")
    @PreFlowPermission
    public JsonObject<Boolean> findZLQD(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        ProjectReportMain projectReportMain = projectReportMainService.getOne(new QueryWrapper<ProjectReportMain>()
                .eq("process_instance_id", processInstanceId)
        );
        //签约渠道中，是否有战略渠道
        Boolean isHaveZLQD = projectReportMainService.isHaveZLQD(projectReportMain);
        return new JsonObject<>(isHaveZLQD);
    }

    private void checkProjectAuth(String projectId){
        // 加一个项目的权限，根据当前登录人看看有没有项目的权限，发起之前的话 要根据当前人判断
        if (!remoteProjectDirectlyClient.hasRight(projectId, UserInfoHolder.getCurrentPersonId()).getObjEntity()) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        };
    }
}
