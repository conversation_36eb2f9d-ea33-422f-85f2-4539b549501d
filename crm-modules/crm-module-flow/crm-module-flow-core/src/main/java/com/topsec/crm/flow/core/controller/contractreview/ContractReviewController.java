package com.topsec.crm.flow.core.controller.contractreview;

import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractreview.baseinfo.ContractBasicInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnSntVO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractReviewSpecialCodeDTO;
import com.topsec.crm.flow.api.dto.contractreview.request.SpecialCodePageQuery;
import com.topsec.crm.flow.api.dto.contractreview.salemaninfo.SalesSelectDTO;
import com.topsec.crm.flow.api.dto.contractreview.statistics.ContractExecuteStatisticsVO;
import com.topsec.crm.flow.api.dto.contractreview.statusinfo.ContractTermAndDeliveryStatusDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.HomeNameValue;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.product.api.RemoteProductService;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 合同评审
 *
 * <AUTHOR>
 * @date 2024/7/9 16:56
 */
@RestController
@RequestMapping("/contractReview")
@Tag(name = "合同评审", description = "/contractReview")
@RequiredArgsConstructor
@Validated
public class ContractReviewController extends BaseController {

    private final ContractReviewMainService contractReviewMainService;

    private final ContractReviewProductOwnService contractReviewProductOwnService;

    private final ContractReviewProductThirdService contractReviewProductThirdService;

    private final ContractReviewSpecialCodeService contractReviewSpecialCodeService;

    private final ContractReviewFlowService contractReviewFlowService;

    private final RemoteProductService remoteProductService;


    @PostMapping("/getProductLineByProductIdBatch")
    @Operation(summary = "根据产品id批量获取产品线")
    @PreAuthorize
    public JsonObject<List<CrmProductVo>> getProductLineByProductIdBatch(@RequestBody List<String> productIds) {
        return remoteProductService.batchGetInfo(productIds);
    }

    @PostMapping("/pageSpecialCode")
    @PreFlowPermission
    @Operation(summary = "分页查询03J的特殊代码确认")
    public JsonObject<TableDataInfo> pageAfterSecurityCode(@RequestBody SpecialCodePageQuery query) {
        return new JsonObject<>(contractReviewFlowService.pageAfterSpecialCode(query));
    }

    @PostMapping("/updateSaleCode")
    @PreFlowPermission
    @Operation(summary = "03J代码确认")
    public JsonObject<Boolean> updateSaleCode(@RequestBody List<ContractReviewSpecialCodeDTO> contractReviewSpecialCodeDTO) {
        return new JsonObject<>(contractReviewSpecialCodeService.updateSaleCode(contractReviewSpecialCodeDTO));
    }


    @GetMapping("/checkPriceApproval")
    @Operation(summary = "检查价格审批是否办结")
    public JsonObject<Boolean> checkPriceApproval(@RequestParam String projectId) {
        return new JsonObject<>(contractReviewFlowService.checkPriceApproval(projectId));
    }


    @GetMapping("/checkTerm")
    @Operation(summary = "检查合同条款条件")
    public JsonObject<Boolean> checkTerm(@RequestParam String contractId) {
        return new JsonObject<>(contractReviewFlowService.checkTerm(contractId));
    }


}
