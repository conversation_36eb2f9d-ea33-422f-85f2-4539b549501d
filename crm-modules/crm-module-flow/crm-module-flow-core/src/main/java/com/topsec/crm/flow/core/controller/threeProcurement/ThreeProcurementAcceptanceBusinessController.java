package com.topsec.crm.flow.core.controller.threeProcurement;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.topsec.crm.flow.api.vo.ThreeProcurementAcceptanceVo;
import com.topsec.crm.flow.api.vo.ThreeProcurementReviewMainVo;
import com.topsec.crm.flow.core.entity.ThreeProcurementAcceptance;
import com.topsec.crm.flow.core.entity.ThreeProcurementRelease;
import com.topsec.crm.flow.core.service.ThreeProcurementAcceptanceService;
import com.topsec.crm.flow.core.service.ThreeProcurementReleaseService;
import com.topsec.crm.flow.core.service.ThreeProcurementReviewMainService;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 第三方采购验收结论
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-01 17:26:59
 */
@RestController
@RequestMapping("/business/threeprocurementacceptance")
@Tag(name = "【第三方采购验收结论】", description = "threeprocurementacceptance")
@RequiredArgsConstructor
@Validated
public class ThreeProcurementAcceptanceBusinessController {

    private final ThreeProcurementAcceptanceService threeProcurementAcceptanceService;

    private final ThreeProcurementReviewMainService threeProcurementReviewMainService;

    private final ThreeProcurementReleaseService threeProcurementReleaseService;


    @GetMapping("/queryThreeProcurementAcceptanceList")
    @Operation(summary = "查询验收结论")
    @PreAuthorize(hasPermission = "crm_third_purchase", dataScope = "crm_third_purchase")
    public JsonObject<List<ThreeProcurementAcceptanceVo>> queryThreeProcurementAcceptanceList(@RequestParam String purchaseNumber){
        if (StringUtils.isEmpty(purchaseNumber)) {
            throw  new CrmException("采购编号不能为空");
        }
        ThreeProcurementReviewMainVo threeProcurementReviewMainVo = threeProcurementReviewMainService.selectThreeProcurementReviewMainByPurchaseNumber(purchaseNumber);
        if (threeProcurementReviewMainVo == null){
            throw  new CrmException("采购信息不存在");
        }
        List<ThreeProcurementRelease> threeProcurementReleaseList = threeProcurementReleaseService.queryThreeProcurementReleaseByPersonId(UserInfoHolder.getCurrentPersonId());
        if (CollectionUtils.isNotEmpty(threeProcurementReleaseList)){
            List<String> strings = threeProcurementReleaseList.stream().map(ThreeProcurementRelease::getProcessNumber).toList();
            if (!strings.contains(threeProcurementReviewMainVo.getProcessNumber())){
                PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementReviewMainVo.getCreateUser());
            }
        }else {
            PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementReviewMainVo.getCreateUser());
        }
        List<ThreeProcurementAcceptance> threeProcurementAcceptances = threeProcurementAcceptanceService.queryThreeProcurementAcceptanceList(purchaseNumber);
        List<ThreeProcurementAcceptanceVo> threeProcurementAcceptanceVoList = HyperBeanUtils.copyListPropertiesByJackson(threeProcurementAcceptances, ThreeProcurementAcceptanceVo.class);
        NameUtils.setName(threeProcurementAcceptanceVoList);
        return new JsonObject<>(threeProcurementAcceptanceVoList);
    }



}
