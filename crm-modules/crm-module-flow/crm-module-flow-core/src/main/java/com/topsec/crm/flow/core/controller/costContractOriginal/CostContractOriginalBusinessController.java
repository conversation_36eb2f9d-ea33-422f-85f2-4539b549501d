package com.topsec.crm.flow.core.controller.costContractOriginal;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.api.dto.costContarct.CostContractAttachmentInfoDTO;
import com.topsec.crm.flow.api.dto.costContractOriginal.CostContractOriginalSignatureInfoDTO;
import com.topsec.crm.flow.api.dto.costContractOriginal.VO.CostContractOriginalInfoVO;
import com.topsec.crm.flow.api.dto.costContractOriginal.VO.CostContractOriginalVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CostContract;
import com.topsec.crm.flow.core.entity.CostContractOriginal;
import com.topsec.crm.flow.core.process.impl.CostContractOriginalProcessService;
import com.topsec.crm.flow.core.service.CostContractAttachmentService;
import com.topsec.crm.flow.core.service.CostContractOriginalService;
import com.topsec.crm.flow.core.service.CostContractOriginalSignatureService;
import com.topsec.crm.flow.core.service.CostContractService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;

import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@RestController
@RequestMapping("/business/costContractOriginal")
@Tag(name = "【费用合同原件-业务接口】", description = "costContractOriginal")
@RequiredArgsConstructor
@Validated
public class CostContractOriginalBusinessController extends BaseController {

    private final CostContractOriginalService costContractOriginalService;

    private final CostContractOriginalSignatureService costContractOriginalSignatureService;

    private final CostContractAttachmentService costContractAttachmentService;

    private final CostContractService costContractService;

    @PreAuthorize(hasPermission="crm_cost_contract_original",dataScope="crm_cost_contract_original")
    @PostMapping("/page")
    @Operation(summary = "费用合同原件分页查询")
    public JsonObject<TableDataInfo> page(@RequestBody CostContractOriginalVO originalVOQuery){
        startPage();
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdList = dataScopeParam !=null ? dataScopeParam.getPersonIdList(): Collections.EMPTY_SET;
        return new JsonObject<>(costContractOriginalService.page(originalVOQuery,personIdList));
    }

    @PreAuthorize(hasPermission="crm_cost_contract_original")
    @GetMapping("/pageRe")
    @Operation(summary = "根据费用合同ID分页查询原件信息")
    public JsonObject<TableDataInfo> pageRe(@RequestParam String costContractId){
        startPage();
        return new JsonObject<>(costContractOriginalService.pageRe(costContractId));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @GetMapping("/getByOriginalId")
    @Operation(summary = "根据主键ID查询原件信息")
    public JsonObject<CostContractOriginalInfoVO> getByOriginalId(@RequestParam String originalId){
        return new JsonObject<>(costContractOriginalService.getByOriginalId(originalId));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @GetMapping("/getByProcessInstanceId")
    @Operation(summary = "根据流程ID查询原件信息")
    public JsonObject<CostContractOriginalInfoVO> getByProcessInstanceId(@RequestParam String processInstanceId){
        return new JsonObject<>(costContractOriginalService.getByProcessInstanceId(processInstanceId));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @GetMapping("/getSignatureByOriginalId")
    @Operation(summary = "根据原件表ID查询签名集合")
    public JsonObject<List<CostContractOriginalSignatureInfoDTO>> getSignatureByOriginalId(@RequestParam String originalId){
        return new JsonObject<>(costContractOriginalSignatureService.getSignatureByOriginalId(originalId));
    }

    @PreAuthorize(hasPermission = "crm_cost_contract_add")
    @PostMapping("/saveSignature")
    @Operation(summary = "新增用户签名")
    public JsonObject<Boolean> saveSignature(@RequestBody CostContractOriginalSignatureInfoDTO originalSignatureInfoDTO){
        return new JsonObject<>(costContractOriginalSignatureService.saveSignature(originalSignatureInfoDTO));
    }

    @PreAuthorize(hasPermission = "crm_cost_contract_add")
    @GetMapping("/deleteSignature")
    @Operation(summary = "删除用户签名")
    public JsonObject<Boolean> deleteSignature(@RequestParam String id){
        return new JsonObject<>(costContractOriginalSignatureService.deleteSignature(id));
    }

    @PreAuthorize(hasPermission = "crm_cost_contract_add")
    @PostMapping("/saveAttachment")
    @Operation(summary = "新增合同原件附件")
    public JsonObject<Boolean> saveAttachment(@RequestBody CostContractAttachmentInfoDTO attachmentInfoDTO){
        CrmAssert.hasText(attachmentInfoDTO.getCostId(),"主表ID不能为空");
        attachmentInfoDTO.setCostType("费用备案");
        return new JsonObject<>(costContractAttachmentService.saveAttachment(attachmentInfoDTO));
    }

    @PreAuthorize(hasPermission = "crm_cost_contract_add")
    @GetMapping("/deleteAttachment")
    @Operation(summary = "删除合同原件附件信息")
    public JsonObject<Boolean> deleteAttachment(@RequestParam String id){
        return new JsonObject<>(costContractAttachmentService.deleteAttachment(id));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @GetMapping("/getByCostContractId")
    @Operation(summary = "查询合同原件附件信息")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getByCostContractId(@RequestParam String costId,@RequestParam(required = false) String isFinalQuery){
        return new JsonObject<>(costContractAttachmentService.getByCostContractId(costId,isFinalQuery));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @GetMapping("/getAttachmentByProcessInstanceId")
    @Operation(summary = "查询费用合同附件信息")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getAttachmentByProcessInstanceId(@RequestParam String processInstanceId,@RequestParam(required = false) String isFinalQuery){
        Optional<CostContractOriginal> optional = Optional.ofNullable(costContractOriginalService.getOne(new QueryWrapper<CostContractOriginal>().lambda()
                .eq(CostContractOriginal::getDelFlag,0).eq(CostContractOriginal::getProcessInstanceId,processInstanceId)));
        if (optional.isPresent()){
            CostContractOriginal costContractOriginal = optional.get();
            CostContract costContract = costContractService.getById(costContractOriginal.getCostContractId());
            return new JsonObject<>(costContractAttachmentService.getByCostContractId(costContract.getId(),isFinalQuery));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }



}
