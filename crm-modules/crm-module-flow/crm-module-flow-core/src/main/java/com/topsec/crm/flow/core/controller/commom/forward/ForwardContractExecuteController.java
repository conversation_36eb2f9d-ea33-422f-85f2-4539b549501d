package com.topsec.crm.flow.core.controller.commom.forward;

import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.CrmContractProductOwnVO;
import com.topsec.crm.contract.api.entity.CrmContractProductThirdVO;
import com.topsec.crm.contract.api.entity.CrmRevenueRecognitionVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractProcessInfoVO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.RevenueRecognitionDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/forward")
@Tag(name = "转发签验收单项目Controller", description = "/forward")
@RequiredArgsConstructor
@Validated
public class ForwardContractExecuteController extends BaseController {

    private final RemoteContractExecuteService remoteContractExecuteService;

    @PreAuthorize
    @PreFlowPermission
    @GetMapping("/contractExecuteFeign/getProcessListByContractNumber")
    @Operation(summary = "根据合同号查合同明细信息")
    JsonObject<List<CrmContractProcessInfoVO>> getProcessListByContractNumber(@RequestParam String contractNumber) {
        return remoteContractExecuteService.getProcessListByContractNumber(contractNumber);
    }

    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"reminderReceipt_03"})
    @GetMapping("/contractExecuteFeign/pageRevenueRecognitionByContractNumber")
    @Operation(summary = "根据合同号查收入确认条款")
    JsonObject<PageUtils<CrmRevenueRecognitionVO>> pageRevenueRecognitionByContractNumber(@RequestParam String contractNumber){
        return remoteContractExecuteService.pageRevenueRecognitionByContractNumber(contractNumber);
    }

    /**
     * 更新收入确认条款 根据id更新
     * @param revenueRecognition 收入确认
     * @return
     */
    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"reminderReceipt_03"})
    @PostMapping("/contractExecuteFeign/saveOrUpdateRevenueRecognitionBatch")
    @Operation(summary = "更新收入确认条款")
    JsonObject<Boolean> saveOrUpdateRevenueRecognitionBatch(@RequestBody List<RevenueRecognitionDTO> revenueRecognition){
        return remoteContractExecuteService.saveOrUpdateRevenueRecognitionBatch(revenueRecognition);
    }

    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"reminderReceipt_03"})
    @GetMapping("/contractExecuteFeign/getProductOwnByContractNumber")
    @Operation(summary = "根据合同号查自有产品")
    JsonObject<List<CrmContractProductOwnVO>> getProductOwnByContractNumber(@RequestParam String contractNumber){
        return remoteContractExecuteService.getProductOwnByContractNumber(contractNumber);
    }

    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"reminderReceipt_03"})
    @GetMapping("/contractExecuteFeign/getProductThirdByContractNumber")
    @Operation(summary = "根据合同号查第三方产品")
    JsonObject<List<CrmContractProductThirdVO>> getProductThirdByContractNumber(@RequestParam String contractNumber){
        return remoteContractExecuteService.getProductThirdByContractNumber(contractNumber);
    }


}
