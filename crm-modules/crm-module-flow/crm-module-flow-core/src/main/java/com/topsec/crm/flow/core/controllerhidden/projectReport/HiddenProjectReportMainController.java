package com.topsec.crm.flow.core.controllerhidden.projectReport;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.flow.api.dto.projectReport.ProjectReportMainVo;
import com.topsec.crm.flow.api.enums.AgentapprovalStatusEnum;
import com.topsec.crm.flow.core.entity.ProjectReportMain;
import com.topsec.crm.flow.core.service.IProjectReportMainService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.util.date.DateUtil;
import com.topsec.crm.framework.common.util.date.DateUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/hidden/projectReport")
@Tag(name = "项目报备-不对外开放", description = "/hiddenProjectReport")
@RequiredArgsConstructor
public class HiddenProjectReportMainController extends BaseController {

    @Autowired
    private IProjectReportMainService projectReportMainService;

    @GetMapping("/findSevenDays")
    @Operation(summary = "项目报备列表")
    public JsonObject<List<ProjectReportMainVo>> findSevenDays(){
        List<ProjectReportMain> list = projectReportMainService.query()
                .eq("process_state", AgentapprovalStatusEnum.YSP.getCode())
                .eq("del_flag", false)
                .le("protect_end_date", DateUtil.dateToLocalDate(DateUtils.addDays(new Date(),7)))
                .orderByDesc("create_time")
                .list();

        return new JsonObject<>(HyperBeanUtils.copyListPropertiesByJackson(list,ProjectReportMainVo.class));
    }

    /**
     * 修改项目报备发送状态
     */
    @GetMapping("/updateSendExpirationFlag")
    public JsonObject<Boolean> updateSendExpirationFlag(@RequestParam String processInstanceId){
        projectReportMainService.update(new UpdateWrapper<ProjectReportMain>().eq("process_instance_id",processInstanceId)
                .set("send_expiration_flag", 1));

        return new JsonObject<Boolean>(ResultEnum.SUCCESS.getResult(), ResultEnum.SUCCESS.getMessage(),true);
    }

    @GetMapping("/findExistReport")
    public JsonObject<ProjectReportMainVo> findExistReport(@RequestParam String projectId){
        ProjectReportMain one = projectReportMainService.getOne(new QueryWrapper<ProjectReportMain>()
                .eq("project_id", projectId)
                .orderByDesc("create_time")
                .last("limit 1")
        );

        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(one, ProjectReportMainVo.class));
    }

    @GetMapping("/findLastReport")
    public JsonObject<ProjectReportMainVo> findLastReport(@RequestParam String projectId){
        ProjectReportMain one = projectReportMainService.getOne(new QueryWrapper<ProjectReportMain>()
                .eq("project_id", projectId)
                .eq("process_state", ApprovalStatusEnum.YSP.getCode())
                .orderByDesc("create_time")
                .last("limit 1")
        );

        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(one, ProjectReportMainVo.class));
    }
}
