package com.topsec.crm.flow.core.controller.borrowForSell;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.flow.api.RemoteFlowPriceReviewService;
import com.topsec.crm.flow.core.entity.BorrowForSell;
import com.topsec.crm.flow.core.entity.BorrowForSellDelivery;
import com.topsec.crm.flow.core.service.IBorrowForSellDeliveryService;
import com.topsec.crm.flow.core.service.IBorrowForSellProductSnService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.flow.core.service.ITargetedInventoryPreparationMainService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.project.api.client.*;
import com.topsec.crm.project.api.entity.*;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 借转销支撑接口 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/business/borrowForSellSupport")
@Tag(name = "借转销业务支撑接口", description = "/business/borrowForSellSupport")
public class BorrowForSellSupportBusinessController extends BaseController {

    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    @Autowired
    private RemoteProjectMemberClient remoteProjectMembersClient;

    @Autowired
    private RemotePerformanceNegotiationClient remotePerformanceNegotiationClient;

    @Autowired
    private RemoteProjectSignAgentClient remoteProjectSignAgentClient;

    @Autowired
    private RemoteProjectProductOwnClient remoteProjectProductOwnClient;

    @Autowired
    private IBorrowForSellProductSnService borrowForSellProductSnService;

    @Autowired
    private IBorrowForSellDeliveryService borrowForSellDeliveryService;

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @Autowired
    private ITargetedInventoryPreparationMainService targetedInventoryPreparationMainService;

    @Autowired
    private RemoteFlowPriceReviewService remoteFlowPriceReviewService;

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell")
    @GetMapping("/getProjectDirectlyDetail")
    @Operation(summary = "获取公司项目详情信息")
    public JsonObject<CrmProjectDirectlyVo> getProjectDirectlyDetail(@RequestParam String projectId) {
        JsonObject<Boolean> booleanJsonObject = remoteProjectDirectlyClient.hasRight(projectId, getCurrentPersonId());
        if(booleanJsonObject.isSuccess() && booleanJsonObject.getObjEntity()){
            return remoteProjectDirectlyClient.getProjectInfo(projectId);
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell")
    @GetMapping("/salesmanList")
    @Operation(summary = "借转销销售人员下拉选择",description = "项目有业绩协商，销售人员只能是业绩协商的发起人；项目没有业绩协商，销售人员在项目成员中进行选择")
    public JsonObject<List<FlowPerson>> salesmanList(@RequestParam String projectId) {
        JsonObject<Boolean> booleanJsonObject = remoteProjectDirectlyClient.hasRight(projectId, getCurrentPersonId());
        if(booleanJsonObject.isSuccess() && booleanJsonObject.getObjEntity()){
            JsonObject<FlowPerson> negotiatorByProjectId = remotePerformanceNegotiationClient.getNegotiatorByProjectId(projectId);
            if(negotiatorByProjectId.isSuccess() && negotiatorByProjectId.getObjEntity() != null){
                return new JsonObject<List<FlowPerson>>(Collections.singletonList(negotiatorByProjectId.getObjEntity()));
            }else{
                return remoteProjectMembersClient.getFlowPersonListByProjectId(projectId);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_details",dataScope = "crm_flow_borrow_for_sell_details")
    @GetMapping("/queryProjectSignInfoTree")
    @Operation(summary = "查询项目签约关系")
    public JsonObject<List<AgentTreeSelect>> queryProjectSignInfoTree(@RequestParam String projectId,@RequestParam String processInstanceId) {
        BorrowForSell one = borrowForSellService.getOne(new LambdaQueryWrapper<BorrowForSell>().eq(BorrowForSell::getProcessInstanceId, processInstanceId));
        if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList())
                || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(one.getSalesmanPersonId())
                || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(one.getCreateUser())){
            return remoteProjectSignAgentClient.tree(projectId);
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell")
    @GetMapping("/queryProjectProduct")
    @Operation(summary = "查询项目自有产品信息")
    public JsonObject<List<CrmProjectProductOwnVO>> queryProjectProduct(@RequestParam String projectId) {
        JsonObject<Boolean> booleanJsonObject = remoteProjectDirectlyClient.hasRight(projectId, getCurrentPersonId());
        if(booleanJsonObject.isSuccess() && booleanJsonObject.getObjEntity()){
            JsonObject<List<CrmProjectProductOwnVO>> listJsonObject = remoteProjectProductOwnClient.queryProjectProductOwnTiled(projectId,new ArrayList<>());
            if(listJsonObject.isSuccess() && CollectionUtils.isNotEmpty(listJsonObject.getObjEntity())){
                List<CrmProjectProductOwnVO> result = new ArrayList<>();
                Map<String, List<CrmProjectProductOwnVO>> collect = listJsonObject.getObjEntity().stream().collect(Collectors.groupingBy(m -> m.getStuffCode() + m.getDealPrice()));
                collect.forEach((k, v) -> {
                    long sum = v.stream().mapToLong(CrmProjectProductOwnVO::getProductNum).sum();
                    CrmProjectProductOwnVO crmProjectProductOwnVO = v.get(0);
                    crmProjectProductOwnVO.setProductNum(sum);
                    result.add(crmProjectProductOwnVO);
                });
                return new JsonObject<>(result);
            }else{
                return listJsonObject;
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_gd_details",dataScope = "crm_flow_borrow_for_sell_gd_details")
    @PostMapping("/queryProductSnOfNotSend")
    @Operation(summary = "查询未发货的借转销产品序列号")
    public JsonObject<List<String>> queryProductSnOfNotSend(@RequestParam String deliveryId) {
        BorrowForSellDelivery byId = borrowForSellDeliveryService.getById(deliveryId);
        if(null != byId){
            BorrowForSell borrowForSell = borrowForSellService.getById(byId.getBorrowId());
            if (null != borrowForSell){
                if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList())
                        || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(borrowForSell.getSalesmanPersonId())
                        || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(borrowForSell.getCreateUser())){
                    List<String> result =  borrowForSellProductSnService.queryProductSnOfNotSend(deliveryId);
                    return new JsonObject<List<String>>(result);
                }
            }
        }
        throw new CrmException(ResultEnum.AUTH_ERROR_500006);
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_details",dataScope = "crm_flow_borrow_for_sell_details")
    @PostMapping("/selectSigningAgentDetail")
    @Operation(summary = "查询签约渠道详情")
    public JsonObject<CrmAgentVo> selectSigningAgentDetail(@RequestBody CrmProjectSigningAgentVo crmProjectSigningAgentVo,@RequestParam String processInstanceId) {
        BorrowForSell one = borrowForSellService.getOne(new LambdaQueryWrapper<BorrowForSell>().eq(BorrowForSell::getProcessInstanceId, processInstanceId));
        if(crmProjectSigningAgentVo.getProjectId().equals(one.getProjectId())){
            if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList())
                    || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(one.getSalesmanPersonId())
                    || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(one.getCreateUser())){
                return remoteProjectSignAgentClient.selectSigningAgentDetail(crmProjectSigningAgentVo);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreAuthorize(hasAnyPermission = {"crm_flow_borrow_for_sell_details"})
    @PostMapping("/queryStockInfo")
    @Operation(summary = "根据专项备货stockId列表批量查询专项备货单号、销售人员、销售单位信息")
    public JsonObject<List<CrmTtargetedInventoryPreparationMainVO>> queryStockInfo(@RequestBody List<String> stockIds) {
        List<CrmTtargetedInventoryPreparationMainVO> crmTtargetedInventoryPreparationMainVOS = targetedInventoryPreparationMainService.batchStockInfoByNumbers(stockIds);
        return new JsonObject<List<CrmTtargetedInventoryPreparationMainVO>>(crmTtargetedInventoryPreparationMainVOS);
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell")
    @PostMapping("/isTheLatestPassedPriceReviewValid")
    @Operation(summary = "查询项目中的价审是否有效")
    public JsonObject<Boolean> isTheLatestPassedPriceReviewValid(@RequestParam String projectId) {
        JsonObject<Boolean> booleanJsonObject = remoteProjectDirectlyClient.hasRight(projectId, getCurrentPersonId());
        if(booleanJsonObject.isSuccess() && booleanJsonObject.getObjEntity()){
            return remoteFlowPriceReviewService.isTheLatestPassedPriceReviewValid(projectId);
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }
}

