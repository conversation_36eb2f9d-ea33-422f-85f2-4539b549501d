package com.topsec.crm.flow.core.controller.agentExamineSd;

import com.topsec.crm.flow.api.dto.agentExamineSd.AgentExamineSdFlowDetailDTO;
import com.topsec.crm.flow.core.service.AgentExamineSdService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/business/agentExamineSd")
@Tag(name = "省级总经销商MBO考核", description = "/agentExamineSd")
@RequiredArgsConstructor
@Validated
public class AgentExamineSdBussinessController extends BaseController {

    private final AgentExamineSdService agentExamineSdService;



    @GetMapping("/getAgentExamineSdDetailByProcessInstanceId")
    @Operation(summary = "根据MBO考核实例ID查询流程详细信息")
    @PreAuthorize(hasPermission = "crm_agent_examine_sd",dataScope="crm_agent_examine_sd")
    public JsonObject<AgentExamineSdFlowDetailDTO> getAgentExamineSdDetailByProcessInstanceId(@RequestParam String processInstanceId){
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        agentExamineSdService.selectCrmAgentExamineSdById(processInstanceId, dataScopeParam);
        return new JsonObject<>(agentExamineSdService.selectAgentExamineSdDetailByProcessInstanceId(processInstanceId));
    }
}
