package com.topsec.crm.flow.core.controller.flow;

import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsQuickReplyClient;
import com.topsec.vo.reply.TfsQuickReplyVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/quickReply")
@Tag(name = "流程快捷回复相关Controller", description = "/quickReply")
@RequiredArgsConstructor
@Validated
public class FlowQuickReplyController extends BaseController {

    private final TfsQuickReplyClient tfsQuickReplyClient;

    /**
     * 查询所有【快捷回复】
     */
    //   @PreAuthorize(hasPermi = "core:reply:list")
//    @UserAudit(eventName = "listAll" ,eventDesc = "查询快捷回复列表", eventType = "快捷回复",getMethodData = true)
    @GetMapping("/listAll")
    @Operation(summary = "查询所有快捷回复")
    @PreAuthorize
    public JsonObject<List<TfsQuickReplyVo>> listAll(@RequestParam(required = false) String type) {
        return  tfsQuickReplyClient.listAll(type,getCurrentPersonId());
    }

    /**
     * 新增【快捷回复】
     */
//    @PreAuthorize(hasPermi = "core:reply:add")
//    @UserAudit(eventName = "add" ,eventDesc = "新增快捷回复", eventType = "快捷回复",getMethodData = true)
    @PostMapping("/add")
    @Operation(summary = "新增【快捷回复】")
    @PreAuthorize
    public JsonObject<Boolean> add(@RequestBody TfsQuickReplyVo tfsQuickReplyVo) {
        CrmAssert.hasText(tfsQuickReplyVo.getContent(), "content不能为空！");
        tfsQuickReplyVo.setCreateUser(getCurrentPersonId());
        return new JsonObject(tfsQuickReplyClient.add(tfsQuickReplyVo));
    }

    /**
     * 修改【快捷回复】
     */
//    @PreAuthorize(hasPermi = "core:reply:edit")
//    @UserAudit(eventName = "edit" ,eventDesc = "修改快捷回复", eventType = "快捷回复",getMethodData = true)
    @PostMapping("/edit")
    @Operation(summary = "修改【快捷回复】")
    @PreAuthorize
    public JsonObject<Boolean> edit(@RequestBody TfsQuickReplyVo tfsQuickReplyVo) {
        CrmAssert.hasText(tfsQuickReplyVo.getId(), "ID不能为空！");
        checkPermission(getCurrentPersonId(), new String[]{tfsQuickReplyVo.getId()});
        tfsQuickReplyVo.setUpdateTime(LocalDateTime.now());
        return new JsonObject(tfsQuickReplyClient.edit(tfsQuickReplyVo));
    }

    /**
     * 批量修改【快捷回复】
     */
//    @PreAuthorize(hasPermi = "core:reply:edit")
//    @UserAudit(eventName = "edit" ,eventDesc = "批量修改快捷回复", eventType = "快捷回复",getMethodData = true)
    @PostMapping("/editBatch")
    @Operation(summary = "批量修改【快捷回复】")
    @PreAuthorize
    public JsonObject<Boolean> editBatch(@RequestBody List<TfsQuickReplyVo> tfsQuickReplyVoList) {
        CrmAssert.notEmpty(tfsQuickReplyVoList, "集合不能为空！");
        String[] ids = tfsQuickReplyVoList.stream().map(TfsQuickReplyVo::getId).toArray(String[]::new);
        checkPermission(getCurrentPersonId(), ids);
        return new JsonObject(tfsQuickReplyClient.editBatch(tfsQuickReplyVoList));
    }

    /**
     * 删除【快捷回复】
     */
//    @PreAuthorize(hasPermi = "core:reply:remove")
//    @UserAudit(eventName = "remove" ,eventDesc = "删除快捷回复", eventType = "快捷回复",getMethodData = true)
    @DeleteMapping("/{ids}")
    @Operation(summary = "删除【快捷回复】")
    public JsonObject<Boolean> remove(@PathVariable String[] ids) {
        CrmAssert.notEmpty(ids, "集合不能为空！");
        checkPermission(getCurrentPersonId(), ids);
        return new JsonObject(tfsQuickReplyClient.remove(ids));
    }

    /**
     * 判断是否有权限修改、删除
     *
     * @param createUser
     * @param ids
     */
    private void checkPermission(String createUser, String[] ids) {
        if (ids == null || ids.length == 0) {
            return;
        }
        JsonObject<List<TfsQuickReplyVo>> listByIds = tfsQuickReplyClient.findListByIds(new ArrayList<>(Arrays.asList(ids)));
        List<TfsQuickReplyVo> tfsQuickReplyList = Optional.ofNullable(listByIds.getObjEntity()).orElse(new ArrayList<>());
        Set<String> idSet = tfsQuickReplyList.stream().map(TfsQuickReplyVo::getCreateUser).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(idSet)) {
            throw new CrmException("id不存在！");
        }
        if (idSet.size() != 1 || !idSet.contains(createUser)) {
            throw new CrmException("无权操作！");
        }
    }
}
