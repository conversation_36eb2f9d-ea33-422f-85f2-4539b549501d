package com.topsec.crm.flow.core.controller.agentRegistration;

import com.alibaba.fastjson.JSONObject;
import com.topsec.crm.agent.api.RemoteAccountVpnService;
import com.topsec.crm.flow.api.dto.agentRegistration.AgentBusinessInfoDTO;
import com.topsec.crm.flow.api.dto.agentRegistration.AgentRegistrationFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.AgentRegistration;
import com.topsec.crm.flow.core.process.impl.AgentRegistrationProcessService;
import com.topsec.crm.flow.core.service.AgentRegistrationService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.tyc.api.RemoteTycSelectService;
import com.topsec.crm.tyc.api.vo.CrmCustomerBussinessInfoDTO;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/agentRegistration")
@Tag(name = "渠道注册", description = "/agentRegistration")
@RequiredArgsConstructor
@Slf4j
public class AgentRegistrationController extends BaseController {

    private final AgentRegistrationService registrationService;

    private final AgentRegistrationProcessService registrationProcessService;

    @Value("${tyc.certifiedToken}")
    private String certifiedToken;

    private final RemoteTycSelectService remoteTycSelectService;

    private final RemoteAccountVpnService remoteAccountVpnService;

    @PostMapping("/launch")
    @Operation(summary = "发起渠道注册流程")
    @PreAuthorize(hasPermission = "crm_agent_registration_add", dataScope = "crm_agent_registration_add")
    public JsonObject<Boolean> launch(@Valid @RequestBody AgentRegistrationFlowLaunchDTO launchDTO) {
        return new JsonObject<>(registrationProcessService.launch(launchDTO));
    }

    @GetMapping("/getLaunchInfo")
    @Operation(summary = "渠道注册基础信息")
    @PreFlowPermission
    public JsonObject<AgentRegistrationFlowLaunchDTO> getLaunchInfo(@RequestParam String processId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(registrationService.getLaunchInfo(processId));
    }

    @GetMapping("/getValidateBusinessInfo")
    @Operation(summary = "验证注册资本，实缴资本是否为空")
    @PreFlowPermission
    public JsonObject<Boolean> validateBusinessInfo(@RequestParam String processId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        AgentRegistrationFlowLaunchDTO launchInfo = registrationService.getLaunchInfo(processId);
        // 工商信息
        JSONObject crmAgentBussinessInfoDTO = launchInfo.getCrmAgentBussinessInfoDTO();
        // 工商信息（修改后）
        JSONObject agentBusinessInfoUpdateDTO = launchInfo.getAgentBusinessInfoDTO();

        // 检查至少有一个不为 null
        if (crmAgentBussinessInfoDTO == null && agentBusinessInfoUpdateDTO == null) {
            throw new CrmException("工商信息为空，请完善注册信息！");
        }
        String actualCapital = null;
        String regCapital = null;
        if (crmAgentBussinessInfoDTO != null) {
            if (crmAgentBussinessInfoDTO.containsKey("actualCapital")) {
                actualCapital = crmAgentBussinessInfoDTO.getString("actualCapital");
            }
            if (crmAgentBussinessInfoDTO.containsKey("regCapital")) {
                regCapital = crmAgentBussinessInfoDTO.getString("regCapital");
            }
        }

        String actualCapitalUpdate = agentBusinessInfoUpdateDTO != null ? agentBusinessInfoUpdateDTO.getString("actualCapital") : null;
        String regCapitalUpdate = agentBusinessInfoUpdateDTO != null ? agentBusinessInfoUpdateDTO.getString("regCapital") : null;

        // 验证注册资本和实缴资本是否为空
        if ((actualCapital == null || actualCapital.isEmpty()) && (actualCapitalUpdate == null || actualCapitalUpdate.isEmpty())) {
            throw new CrmException("实缴资本为空，请完善注册信息！");
        }
        if ((regCapital == null || regCapital.isEmpty()) && (regCapitalUpdate == null || regCapitalUpdate.isEmpty())) {
            throw new CrmException("注册资本为空，请完善注册信息！");
        }
        return new JsonObject<>(true);
    }

    //todo 添加00节点修改权限
    @PostMapping("/update")
    @Operation(summary = "信息修改")
    @PreFlowPermission
    public JsonObject<Boolean> update(@Valid @RequestBody AgentRegistrationFlowLaunchDTO agentRegistrationDTO) {
        if (!agentRegistrationDTO.getAgentName().equals(agentRegistrationDTO.getAgentBusinessInfoDTO().getString("name"))) {
            throw new CrmException("渠道名称与公司名称不一致！");
        }
         return new JsonObject<>(registrationService.update(agentRegistrationDTO));
    }

    @PostMapping("/updateEnterpriseInfo")
    @Operation(summary = "更新企业信息")
    @PreFlowPermission
    public JsonObject<CrmCustomerBussinessInfoDTO> updateEnterpriseInfo(@RequestBody AgentRegistration agentRegistration) {
        return new JsonObject<>(registrationService.updateCustomerBussinessInfo(agentRegistration));
    }

    @GetMapping("/checkMobileAndEmail")
    @Operation(summary = "校验手机号和邮箱是否重复", description = "校验手机号和邮箱是否重复")
    @PreFlowPermission
    public JsonObject<Boolean> checkMobileAndEmail(@RequestParam String mobile, @RequestParam String email) {
        return new JsonObject<>(registrationService.checkMobileAndEmail(mobile, email));
    }

    @GetMapping("/availableAccountCounts")
    @Operation(summary = "获取可用账号数量")
    @PreFlowPermission
    public JsonObject<Long> availableAccountCounts(){
        return remoteAccountVpnService.availableAccountCounts();
    }
}
