package com.topsec.crm.flow.core.controller.customer;

import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.dto.customer.CustomerNaSalemanVo;
import com.topsec.crm.flow.api.dto.customer.CustomerVo;
import com.topsec.crm.flow.api.dto.customer.CustomerNaApproveFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CustomerNaConflictSnapshot;
import com.topsec.crm.flow.core.entity.CustomerNaSaleman;
import com.topsec.crm.flow.core.service.ICustomerNaConflictSnapshotService;
import com.topsec.crm.flow.core.service.ICustomerNaMainService;
import com.topsec.crm.flow.core.service.ICustomerNaSalemanService;
import com.topsec.crm.flow.core.service.ICustomerNaService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/customerNa")
@Tag(name = "NA客户审批流程相关Controller", description = "/customerNa/flow")
@RequiredArgsConstructor
@Validated
public class CustomerNaController extends BaseController {
    @Autowired
    private ICustomerNaSalemanService customerNaSalemanService;
    @Autowired
    private ICustomerNaMainService customerNaMainService;
    @Autowired
    private ICustomerNaService customerNaService;
    @Autowired
    private ICustomerNaConflictSnapshotService customerNaConflictSnapshotService;
    @Autowired
    private RemoteCustomerService remoteCustomerService;

    @PostMapping("/selecConflictList")
    @Operation(summary = "F5. 02 03 冲突客户列表(前端分页)")
    @PreFlowPermission
    public JsonObject<List<CustomerVo>> selecConflictList(@RequestBody CustomerNaApproveFlowLaunchDTO launchDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(launchDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        return new JsonObject<>(customerNaMainService.selecConflictList(launchDTO));
    }

    @PostMapping("/selecConflictSnapshot")
    @Operation(summary = "流程审批通过后查询冲突快照")
    @PreFlowPermission
    public JsonObject<List<CustomerVo>> selecConflictSnapshot(@RequestBody CustomerNaApproveFlowLaunchDTO launchDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(launchDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        List<CustomerVo> result = new ArrayList<CustomerVo>();

        List<CustomerNaConflictSnapshot> snapshots = customerNaConflictSnapshotService.query().eq("process_instance_id", launchDTO.getProcessInstanceId()).list();
        if(CollectionUtils.isNotEmpty(snapshots)) {
            List<String> customerIds = snapshots.stream().map(CustomerNaConflictSnapshot::getCustomerId).toList();
            JsonObject<Map<String, CrmCustomerVo>> mapJsonObject = remoteCustomerService.batchGetCustomerInfo(customerIds, false, false);

            if(mapJsonObject.isSuccess()) {
                Map<String, CrmCustomerVo> cusInfo = mapJsonObject.getObjEntity();
                for (CustomerNaConflictSnapshot snapshot : snapshots) {
                    CustomerVo customerVo = HyperBeanUtils.copyProperties(snapshot, CustomerVo::new);
                    customerVo.setName(snapshot.getCustomerName());
                    customerVo.setId(snapshot.getCustomerId());
                    customerVo.setArea(cusInfo.get(snapshot.getCustomerId()).getArea());
                    result.add(customerVo);
                }
            }
        }

        return new JsonObject<>(result);
    }

    @PostMapping("/addCustomer")
    @Operation(summary = "F2. 01节点：销售添加指名客户后保存")
    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"sid-0B8B6726-16A9-4176-8FE6-C46CDCCA0B80"})
    public JsonObject<Boolean> addCustomer(@Valid @RequestBody CustomerNaApproveFlowLaunchDTO launchDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(launchDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //验证添加客户
        JsonObject<Boolean> jsonObject = remoteCustomerService.checkNaRule(launchDTO.getCustomerId(), launchDTO.getProcessInstanceId());
        if(jsonObject.getObjEntity() == false){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        //验证添加的客户数据范围（是否有客户的联络人）
        JsonObject<Boolean> checkCustomer = remoteCustomerService.checkCreateLinks(launchDTO.getCustomerId(), UserInfoHolder.getCurrentPersonId());
        if(checkCustomer.getObjEntity() == false){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        customerNaService.addCustomer(launchDTO);
        return new JsonObject<>(true);
    }

    @PostMapping("/updateCustomer")
    @Operation(summary = "F6. 01节点和03节点：销售或销管 编辑指名客户")
    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"sid-0B8B6726-16A9-4176-8FE6-C46CDCCA0B80"})
    public JsonObject<Boolean> updateCustomer(@RequestBody CustomerNaApproveFlowLaunchDTO launchDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(launchDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //验证添加客户
        JsonObject<Boolean> jsonObject = remoteCustomerService.checkNaRule(launchDTO.getCustomerId(), launchDTO.getProcessInstanceId());
        if(jsonObject.getObjEntity() == false){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        //验证添加的客户数据范围（是否有客户的联络人）
        JsonObject<Boolean> checkCustomer = remoteCustomerService.checkCreateLinks(launchDTO.getCustomerId(), UserInfoHolder.getCurrentPersonId());
        if(checkCustomer.getObjEntity() == false){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        customerNaService.updateCustomer(launchDTO);
        return new JsonObject<>(true);
    }

    @PostMapping("/deleteCustomer")
    @Operation(summary = "F6-2. 01节点和03节点：销售或销管 删除指名客户")
    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"sid-0B8B6726-16A9-4176-8FE6-C46CDCCA0B80","sid-A36FBAC3-4652-4109-9C59-3D9C1B0D3A1D"})
    public JsonObject<Boolean> deleteCustomer(@RequestBody CustomerNaApproveFlowLaunchDTO launchDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(launchDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        customerNaService.deleteCustomer(launchDTO);
        return new JsonObject<>(true);
    }

    @Operation(summary = "F4. 01 02 03 -NA客户申请列表")
    @PostMapping(value = "/naCustomerApplyPage")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<PageUtils<CustomerNaSalemanVo>> naCustomerApplyPage(@RequestBody CustomerNaSalemanVo customerNaSalemanVo)
    {
        PreFlowPermissionAspect.checkProcessInstanceId(customerNaSalemanVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        startPage();
        List<CustomerNaSaleman> list = customerNaSalemanService.naCustomerApplyPage(HyperBeanUtils.copyProperties(customerNaSalemanVo, CustomerNaSaleman::new));
        List<CustomerNaSalemanVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, CustomerNaSalemanVo.class);
        if(CollectionUtils.isNotEmpty(listVo)) {
            //前端渲染表头
            CustomerNaSalemanVo cnsv = listVo.get(0) != null ? listVo.get(0) : new CustomerNaSalemanVo();
            cnsv.setMountYears(Arrays.asList(LocalDateTime.now().plusYears(-2).getYear(), LocalDateTime.now().plusYears(-1).getYear(), LocalDateTime.now().getYear()));
        }
        //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
        return new JsonObject<>(getDataTable(list,listVo));
    }

    @Operation(summary = "销管批量删除指名信息")
    @PostMapping(value = "/deleteNa")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<Boolean> deleteNa(@RequestBody List<String> ids)
    {
        //权限,销管是有删除指名的全部权限，无需验证

        customerNaService.removeByIds(ids);
        return new JsonObject<>(true);
    }

//    @Operation(summary = "销售查询指名统计信息")
//    @PostMapping(value = "/naNumCal")
//    @PreAuthorize
//    @PreFlowPermission
//    public JsonObject<HashMap<String,Integer>> naNumCal(@RequestBody List<String> ids)
//    {
//
//        return new JsonObject<>(true);
//    }

}
