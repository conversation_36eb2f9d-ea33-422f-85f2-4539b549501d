package com.topsec.crm.flow.core.controller.contractSignVerify;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.entity.CrmRevenueRecognitionVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractProcessInfoVO;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractBadDebtDetailVo;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractSignVerifyAttachmentDTO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractSignVerifyFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractSignVerifyMainVo;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractSignVerifyUserSignVo;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewCustomerDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ContractSignVerifyAttachment;
import com.topsec.crm.flow.core.entity.ContractSignVerifyMain;
import com.topsec.crm.flow.core.entity.ContractSignVerifyUserSign;
import com.topsec.crm.flow.core.entity.ContractUnconfirmed;
import com.topsec.crm.flow.core.process.impl.ContractSignVerifyProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.contract.ContractProcessInfoVO;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contractSignVerifyMain")
@Tag(name = "签验收单相关Controller", description = "/contractSignVerifyMain/flow")
@RequiredArgsConstructor
@Validated
public class ContractSignVerifyMainController extends BaseController {
    @Autowired
    private ContractSignVerifyProcessService contractSignVerifyProcessService;
    @Autowired
    private IContractSignVerifyMainService contractSignVerifyMainService;
    @Autowired
    private IContractSignVerifyAttachmentService contractSignVerifyAttachmentService;
    @Autowired
    private IContractSignVerifyUserSignService contractSignVerifyUserSignService;
    @Autowired
    private IContractUnconfirmedService contractUnconfirmedService;
    @Autowired
    private IContractUnconfirmedDetailService contractUnconfirmedDetailService;
    @Autowired
    private RemoteContractExecuteService remoteContractExecuteService;
    @Autowired
    private TfsNodeClient tfsNodeClient;
    @Autowired
    private RemoteContractReviewService remoteContractReviewService;

    @PostMapping("/singleLaunch")
    @Operation(summary = "单个发起签验收单审批流程")
    @PreAuthorize(hasPermission = "crm_contract_unconfirmed_sign_verify",dataScope = "crm_contract_unconfirmed_sign_verify")
    public JsonObject<Boolean> singleLaunch(@Valid @RequestBody ContractSignVerifyFlowLaunchDTO launchDTO) {
        //权限校验
        JsonObject<CrmContractExecuteVO> booleanJsonObject = remoteContractExecuteService.getByContractNumber(launchDTO.getContractNumber());
        if(!booleanJsonObject.isSuccess() || booleanJsonObject.getObjEntity() != null){
            DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
            Set<String> personIdList = dataScopeParam.getPersonIdList();
            if(personIdList != null && !personIdList.contains(booleanJsonObject.getObjEntity().getContractOwnerId())) {
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        //1.1 如果上传的合同号在合同执行中找不到对应时，整单上传失败，整单报错。
        JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(Collections.singleton(launchDTO.getContractNumber()));
        if(byContractNumberBatch.isSuccess() && byContractNumberBatch.getObjEntity() != null){
            List<CrmContractExecuteVO> executes = byContractNumberBatch.getObjEntity();
            Assert.isFalse(CollectionUtil.isEmpty(executes), "当前合同号在【合同执行】中找不到对应记录，无法发起签验收单");
        }
        //1.2、如果上传的合同号在【未确认明细】中找不到对应时，整单上传失败，整单报错
        List<ContractUnconfirmed> unconfirmeds = contractUnconfirmedService.query().eq("contract_number", launchDTO.getContractNumber()).list();
        Assert.isFalse(CollectionUtil.isEmpty(unconfirmeds), "当前合同号在【未确认明细】中找不到对应记录，无法发起签验收单");

        //3.发起流程
        contractSignVerifyProcessService.launch(launchDTO);
        return new JsonObject<>(true);
    }

    @PostMapping(value="/batchLaunch")
    @Operation(summary = "批量发起签验收单审批流程")
    @PreAuthorize(hasPermission = "crm_contract_unconfirmed_sign_verify_save",dataScope = "crm_contract_unconfirmed_sign_verify_save")
    public JsonObject<Boolean> batchLaunch(@RequestBody List<ContractSignVerifyAttachmentDTO> attachmentDTOS){
        List<ContractSignVerifyFlowLaunchDTO> result = checkFileNames(attachmentDTOS);

        //权限校验
        Set<String> collect = result.stream().map(ContractSignVerifyFlowLaunchDTO::getContractNumber).collect(Collectors.toSet());
        JsonObject<List<CrmContractExecuteVO>> booleanJsonObject = remoteContractExecuteService.getByContractNumberBatchNotEffective(collect);
        if(!booleanJsonObject.isSuccess() || booleanJsonObject.getObjEntity() != null){
            DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
            Set<String> personIdList = dataScopeParam.getPersonIdList();
            for (CrmContractExecuteVO crmContractExecuteVO : booleanJsonObject.getObjEntity()) {
                if(personIdList != null && !personIdList.contains(crmContractExecuteVO.getContractOwnerId())) {
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        for (ContractSignVerifyFlowLaunchDTO launchDTO : result) {
            //3.发起流程
            contractSignVerifyProcessService.launch(launchDTO);
        }
        return new JsonObject<>(true);
    }

    public List<ContractSignVerifyFlowLaunchDTO> checkFileNames(List<ContractSignVerifyAttachmentDTO> atts) {
        List<ContractSignVerifyFlowLaunchDTO> result = new ArrayList<ContractSignVerifyFlowLaunchDTO>();

        Set<String> contractNumbers = new HashSet<String>();
        //1.文件名称格式校验
        for (ContractSignVerifyAttachmentDTO att : atts) {
            //合同号-资料类型.后缀名（jpg&pdf&rar&zip）
            String fileName = att.getFileName();
            String[] split = fileName.split("-");
            Assert.isFalse(split.length < 2, "文件格式不正确，请编辑后重新上传。");
            String[] split2 = split[1].split("\\.");
            Assert.isFalse(split2.length < 2, "文件格式不正确，请编辑后重新上传。");
            String fileType = split2[0];
            if(!fileType.equals("签收单") && !fileType.equals("验收单") && !fileType.equals("初验报告") && !fileType.equals("中验报告")
                    && !fileType.equals("终验报告") && !fileType.equals("开票确认") && !fileType.equals("其他") ){
                Assert.isFalse(true, "文件格式不正确，请编辑后重新上传。");
            }
            contractNumbers.add(split[0]);

            //4.1组装数据-对合同号相同的附件合并
            ContractSignVerifyFlowLaunchDTO exist = result.stream().filter(e -> e.getContractNumber().equals(split[0])).findFirst().orElse(null);
            if(exist == null) {
                ContractSignVerifyFlowLaunchDTO one = new ContractSignVerifyFlowLaunchDTO();
                one.setContractNumber(split[0]);
                one.setConfirmedType(1);
                att.setFileType(fileType);//设置附件类型
                one.setAttachmentDTOS(Collections.singletonList(att));
                result.add(one);
            }else{
                List<ContractSignVerifyAttachmentDTO> old = new ArrayList<>(exist.getAttachmentDTOS());
                att.setFileType(fileType);//设置附件类型
                old.add(att);
                exist.setAttachmentDTOS(old);
            }
        }
        //2 如果上传的合同号在合同执行中找不到对应时，整单上传失败，整单报错。
        JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(contractNumbers);
        if(byContractNumberBatch.isSuccess() && byContractNumberBatch.getObjEntity() != null){
            List<CrmContractExecuteVO> executes = byContractNumberBatch.getObjEntity();

            StringBuilder errorMsg = new StringBuilder();
            for (String contractNumber : contractNumbers) {
                CrmContractExecuteVO exist = executes.stream().filter(e -> e.getContractNumber().equals(contractNumber)).findFirst().orElse(null);
                if(exist == null){
                    errorMsg.append(contractNumber + "、");
                }
            }
            if(errorMsg.length() > 0) {
                Assert.isFalse(true, "存在合同号 " + errorMsg.substring(0, errorMsg.length() - 1) + " 在【合同执行】中找不到对应记录，无法发起签验收单");
            }
        }
        //3、如果上传的合同号在【未确认明细】中找不到对应时，整单上传失败，整单报错
        List<ContractUnconfirmed> unconfirmeds = contractUnconfirmedService.query().in(CollectionUtil.isNotEmpty(contractNumbers),"contract_number",contractNumbers ).list();
        StringBuilder errorMsg = new StringBuilder();
        for (String contractNumber : contractNumbers) {
            if(CollectionUtil.isNotEmpty(unconfirmeds)) {
                ContractUnconfirmed exist = unconfirmeds.stream().filter(e -> e.getContractNumber().equals(contractNumber)).findFirst().orElse(null);
                if (exist == null) {
                    errorMsg.append(contractNumber + "、");
                }
            }else{
                errorMsg.append(contractNumber + "、");
            }
        }
        if(errorMsg.length() > 0) {
            Assert.isFalse(true, "存在合同号 " + errorMsg.substring(0, errorMsg.length() - 1) + " 在【未确认明细】中找不到对应记录，无法发起签验收单");
        }

        //4.文件校验
        for(int i=0;i<atts.size();++i){
            String fileName = atts.get(i).getFileName();
            for(int j=i+1;j<atts.size();++j){
                if(fileName.equals(atts.get(j).getFileName())){
                    //2.相同文件名校验
                    Assert.isFalse(true,"同一合同存在相同类型的文件，无法发起签验收单流程。");
                }
            }
        }

        return result;
    }

    @GetMapping("/info")
    @Operation(summary = "签验收单详情信息")
    @PreFlowPermission
    public JsonObject<ContractSignVerifyMainVo> info(@RequestParam String processInstanceId,@Schema(hidden = true) HttpServletRequest httpServletRequest) {
        //ProcessInstanceId校验
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询流程基本信息
        ContractSignVerifyMain contractSignVerifyMain = contractSignVerifyMainService.query().eq("process_instance_id", processInstanceId).one();
        ContractSignVerifyMainVo csvmv = HyperBeanUtils.copyPropertiesByJackson(contractSignVerifyMain, ContractSignVerifyMainVo.class);
        if(csvmv != null) {
            //2.查询合同执行信息
            JsonObject<CrmContractExecuteVO> obj = remoteContractExecuteService.getByContractNumber(contractSignVerifyMain.getContractNumber());
            if (obj.isSuccess() && obj.getObjEntity() != null) {
                CrmContractExecuteVO executeVO = obj.getObjEntity();
                csvmv.setContractExecuteVO(HyperBeanUtils.copyPropertiesByJackson(executeVO, ContractExecuteVO.class));

                //查询预付款金额
                JsonObject<List<ContractReviewCustomerDTO>> cib = remoteContractReviewService.getCustomerInfoByContractIdBatch(Collections.singleton(executeVO.getContractId()));
                if (cib.isSuccess() && CollectionUtil.isNotEmpty(cib.getObjEntity())) {
                    List<ContractReviewCustomerDTO> reviews = cib.getObjEntity();
                    ContractReviewCustomerDTO reviewCustomerDTO = reviews.stream().filter(c -> c.getContractReviewMainId().equals(executeVO.getContractId())).findFirst().orElse(null);
                    csvmv.setPrepayment(reviewCustomerDTO != null ? reviewCustomerDTO.getPrepayment() : null);
                }
            }

            //3.查询附件列表
            List<ContractSignVerifyAttachment> attachments = contractSignVerifyAttachmentService.query().eq("process_instance_id", processInstanceId).list();
            csvmv.setAttachmentDTOS(HyperBeanUtils.copyListPropertiesByJackson(attachments, ContractSignVerifyAttachmentDTO.class));
            //4.查询用户签名列表
            List<ContractSignVerifyUserSign> userSigns = contractSignVerifyUserSignService.query().eq("process_instance_id", processInstanceId).list();
            csvmv.setUserSignVos(HyperBeanUtils.copyListPropertiesByJackson(userSigns, ContractSignVerifyUserSignVo.class));

            //5.查询合同明细
            JsonObject<List<CrmContractProcessInfoVO>> obj1 = remoteContractExecuteService.getProcessListByContractNumber(csvmv.getContractNumber());
            if (obj1.isSuccess() && obj1.getObjEntity() != null) {
                csvmv.setContractProcessInfoVOS(HyperBeanUtils.copyListPropertiesByJackson(obj1.getObjEntity(), com.topsec.crm.flow.api.dto.contractreview.CrmContractProcessInfoVO.class));
            }

            //6.查询收入确认条款
            httpServletRequest.setAttribute("pageNum",1);
            httpServletRequest.setAttribute("pageSize",1000);
            JsonObject<PageUtils<CrmRevenueRecognitionVO>> obj2 = remoteContractExecuteService.pageRevenueRecognitionByContractNumber(csvmv.getContractNumber());
            if (obj2.isSuccess() && obj2.getObjEntity() != null) {
                List<CrmRevenueRecognitionVO> list = obj2.getObjEntity().getList();
                csvmv.setRevenueRecognitionVOS(HyperBeanUtils.copyListPropertiesByJackson(list, com.topsec.crm.flow.api.dto.contractreview.CrmRevenueRecognitionVO.class));
            }
        }

        return new JsonObject<>(csvmv);
    }

    @PostMapping("/update")
    @Operation(summary = "修改签验收申请")
    @PreFlowPermission
    public JsonObject<Boolean> update(@Valid @RequestBody ContractSignVerifyFlowLaunchDTO launchable) {
        String processInstanceId = launchable.getProcessInstanceId();
        //ProcessInstanceId校验
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.1删除用户签名
        contractSignVerifyUserSignService.remove(new QueryWrapper<ContractSignVerifyUserSign>().eq("process_instance_id", processInstanceId));
        //1.2新增用户签名
        if(CollectionUtil.isNotEmpty(launchable.getUserSignVos())) {
            List<ContractSignVerifyUserSign> userSigns = HyperBeanUtils.copyListPropertiesByJackson(launchable.getUserSignVos(), ContractSignVerifyUserSign.class);
            for (ContractSignVerifyUserSign userSign : userSigns) {
                userSign.setId(UUID.randomUUID().toString());
                userSign.setProcessInstanceId(processInstanceId);
            }
            contractSignVerifyUserSignService.saveBatch(userSigns);
        }

        //2.修改附件信息
        List<ContractSignVerifyAttachment> attachments = HyperBeanUtils.copyListPropertiesByJackson(launchable.getAttachmentDTOS(), ContractSignVerifyAttachment.class);
        List<String> updateIds = new ArrayList<String>();
        for (ContractSignVerifyAttachment attachment : attachments) {
            if(StringUtils.isNotBlank(attachment.getId())){
                updateIds.add(attachment.getId());
            }
        }
        //2.1删除不在提交的表单中的ID
        if(CollectionUtil.isNotEmpty(updateIds)){
            contractSignVerifyAttachmentService.remove(
                    new QueryWrapper<ContractSignVerifyAttachment>().eq("process_instance_id", processInstanceId)
                            .notIn("id",updateIds));
        }else{
            contractSignVerifyAttachmentService.remove(
                    new QueryWrapper<ContractSignVerifyAttachment>().eq("process_instance_id", processInstanceId));
        }

        //2.2新增或者编辑现在的
        if(CollectionUtil.isNotEmpty(attachments)){
            for (ContractSignVerifyAttachment attachment : attachments) {
                if(StringUtils.isBlank(attachment.getId())) {
                    attachment.setId(UUID.randomUUID().toString());
                }
                attachment.setProcessInstanceId(processInstanceId);
                attachment.setCreateUserName(NameUtils.getName(UserInfoHolder.getCurrentPersonId()));
            }
            contractSignVerifyAttachmentService.saveOrUpdateBatch(attachments);
        }

        //3修改信息
        contractSignVerifyMainService.update(new UpdateWrapper<ContractSignVerifyMain>().eq("process_instance_id",processInstanceId)
                .set("confirmed_type", launchable.getConfirmedType())
                .set("remark", launchable.getRemark())
        );

        return new JsonObject<>(true);
    }

    @PostMapping("/updateAttach")
    @Operation(summary = "修改签验收单据")
    @PreFlowPermission(hasAnyNodes = {"reminderReceipt_03","sid-2C998412-9097-4870-BF53-0EA728173F8F","reminderReceipt_05"})
    public JsonObject<Boolean> updateAttach(@Valid @RequestBody ContractSignVerifyFlowLaunchDTO launchable) {
        //ProcessInstanceId校验
        PreFlowPermissionAspect.checkProcessInstanceId(launchable.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        String processInstanceId = launchable.getProcessInstanceId();
        //2.修改附件信息
        List<ContractSignVerifyAttachment> attachments = HyperBeanUtils.copyListPropertiesByJackson(launchable.getAttachmentDTOS(), ContractSignVerifyAttachment.class);
        List<String> updateIds = new ArrayList<String>();
        for (ContractSignVerifyAttachment attachment : attachments) {
            if(StringUtils.isNotBlank(attachment.getId())){
                updateIds.add(attachment.getId());
            }
        }
        //2.1删除不在提交的表单中的ID
        if(CollectionUtil.isNotEmpty(updateIds)){
            contractSignVerifyAttachmentService.remove(
                    new QueryWrapper<ContractSignVerifyAttachment>().eq("process_instance_id", processInstanceId)
                            .notIn("id",updateIds));
        }else{
            contractSignVerifyAttachmentService.remove(
                    new QueryWrapper<ContractSignVerifyAttachment>().eq("process_instance_id", processInstanceId));
        }

        //2.2新增或者编辑现在的
        if(CollectionUtil.isNotEmpty(attachments)){
            for (ContractSignVerifyAttachment attachment : attachments) {
                if(StringUtils.isBlank(attachment.getId())) {
                    attachment.setId(UUID.randomUUID().toString());
                }
                attachment.setProcessInstanceId(processInstanceId);
                attachment.setCreateUserName(NameUtils.getName(UserInfoHolder.getCurrentPersonId()));
            }
            contractSignVerifyAttachmentService.saveOrUpdateBatch(attachments);
        }
        return new JsonObject<>(true);
    }

    @PostMapping("/updateUserSign")
    @Operation(summary = "修改用户签名")
    @PreFlowPermission(hasAnyNodes = {"reminderReceipt_03","sid-2C998412-9097-4870-BF53-0EA728173F8F","reminderReceipt_05"})
    public JsonObject<Boolean> updateUserSign(@Valid @RequestBody ContractSignVerifyFlowLaunchDTO launchable) {
        PreFlowPermissionAspect.checkProcessInstanceId(launchable.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        String processInstanceId = launchable.getProcessInstanceId();
        //1.修改用户签名
        List<ContractSignVerifyUserSign> userSigns = HyperBeanUtils.copyListPropertiesByJackson(launchable.getUserSignVos(), ContractSignVerifyUserSign.class);
        List<String> updateIds = new ArrayList<String>();
        for (ContractSignVerifyUserSign userSign : userSigns) {
            if(StringUtils.isNotBlank(userSign.getId())){
                updateIds.add(userSign.getId());
            }
        }
        //1.1删除不在提交的表单中的ID
        if(CollectionUtil.isNotEmpty(updateIds)){
            contractSignVerifyUserSignService.remove(
                    new QueryWrapper<ContractSignVerifyUserSign>().eq("process_instance_id", processInstanceId)
                            .notIn("id",updateIds));
        }else{
            contractSignVerifyUserSignService.remove(
                    new QueryWrapper<ContractSignVerifyUserSign>().eq("process_instance_id", processInstanceId));
        }

        //1.2新增或者编辑现在的
        if(CollectionUtil.isNotEmpty(userSigns)){
            for (ContractSignVerifyUserSign userSign : userSigns) {
                if(StringUtils.isBlank(userSign.getId())) {
                    userSign.setId(UUID.randomUUID().toString());
                }
                userSign.setProcessInstanceId(processInstanceId);
            }
            contractSignVerifyUserSignService.saveOrUpdateBatch(userSigns);
        }

        return new JsonObject<>(true);
    }
}
