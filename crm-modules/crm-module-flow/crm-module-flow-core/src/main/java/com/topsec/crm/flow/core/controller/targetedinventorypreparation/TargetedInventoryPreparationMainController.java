package com.topsec.crm.flow.core.controller.targetedinventorypreparation;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.TargetedInventoryPreparationBaseInfoDTO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.TargetedInventoryPreparationDocDTO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.TargetedInventoryPreparationFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.TargetedInventoryPreparationProductOwnDTO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.*;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.TargetedInventoryPreparationMain;
import com.topsec.crm.flow.core.entity.TargetedInventoryPreparationProductOwn;
import com.topsec.crm.flow.core.entity.TargetedInventoryPreparationStock;
import com.topsec.crm.flow.core.process.impl.TargetedInventoryPreparationProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.entity.CrmTtargetedInventoryPreparationMainVO;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * 专项备货流程主表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-07-26 18:21
 */
@RestController
@RequestMapping("/targetedInventoryPreparationMain")
@RequiredArgsConstructor
@Validated
@Tag(name = "专项备货-流程接口", description = "/targetedInventoryPreparationMain")
public class TargetedInventoryPreparationMainController extends BaseController {
    @Autowired
    private TargetedInventoryPreparationProcessService targetedInventoryPreparationProcessService;
    @Autowired
    private ITargetedInventoryPreparationMainService iTargetedInventoryPreparationMainService;

    private final ITargetedInventoryPreparationProductOwnService iTargetedInventoryPreparationProductOwnService;

    private final TargetedInventoryPreparationOwnChangeReService targetedInventoryPreparationOwnChangeReService;

    private final TargetedInventoryPreparationDocService targetedInventoryPreparationDocService;

    private final ITargetedInventoryPreparationFlowService iTargetedInventoryPreparationFlowService;

    private final ITargetedInventoryPreparationStockService stockService;

    private final ITargetedInventoryPreparationStockReturnService stockReturnService;

    @PreFlowPermission(hasAnyNodes = {"sid-87B15B10-B97F-4F6F-B464-CA009E40FFBD","specialStocking_03"})
    @PostMapping("/updateTargetedInventoryPreparationInfo")
    @Operation(summary = "01、03修改专项备货信息")
    public JsonObject<Boolean> updateTargetedInventoryPreparationInfo(@RequestBody TargetedInventoryPreparationBaseInfoVO targetedInventoryPreparationBaseInfoVO){
        CrmAssert.hasText(targetedInventoryPreparationBaseInfoVO.getId(), "专项备货Id不能为空");
        Optional<TargetedInventoryPreparationMain> optionalById = Optional.ofNullable(iTargetedInventoryPreparationMainService.getById(targetedInventoryPreparationBaseInfoVO.getId()));
        if (optionalById.isPresent()){
            TargetedInventoryPreparationMain main = optionalById.get();
            PreFlowPermissionAspect.checkProcessInstanceId(main.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(iTargetedInventoryPreparationMainService.updateTargetedInventoryPreparationInfo(targetedInventoryPreparationBaseInfoVO));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

//    @PreFlowPermission
//    @GetMapping("/QueryTargetedInventoryPreparationInfo")
//    @Operation(summary = "根据专项备货ID查询专项备货信息")
//    public JsonObject<TargetedInventoryPreparationBaseInfoVO> queryTargetedInventoryPreparationInfo(@RequestParam String targetedInventoryPreparationId){
//        CrmAssert.hasText(targetedInventoryPreparationId, "专项备货Id不能为空");
//        return new JsonObject<>(iTargetedInventoryPreparationMainService.queryTargetedInventoryPreparationInfo(targetedInventoryPreparationId));
//    }

    @PreFlowPermission
    @GetMapping("/deleteTargetedInventoryPreparationInfo")
    @Operation(summary = "专项备货删除")
    public JsonObject<Boolean> delete(@RequestParam String targetedInventoryPreparationId) {
        CrmAssert.hasText(targetedInventoryPreparationId, "专项备货Id不能为空");
        Optional<TargetedInventoryPreparationMain> optionalById = Optional.ofNullable(iTargetedInventoryPreparationMainService.getById(targetedInventoryPreparationId));
        if (optionalById.isPresent()){
            TargetedInventoryPreparationMain main = optionalById.get();
            PreFlowPermissionAspect.checkProcessInstanceId(main.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(iTargetedInventoryPreparationMainService.delete(targetedInventoryPreparationId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    @PostMapping("/launch")
    @Operation(summary = "发起专项备货流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody TargetedInventoryPreparationFlowLaunchDTO launchDTO) {
        return new JsonObject<>(targetedInventoryPreparationProcessService.launch(launchDTO));
    }

    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    @GetMapping("/getIsExistByProjectId")
    @Operation(summary = "根据项目ID查询是否存在专项备货")
    public JsonObject<Boolean> getIsExistByProjectId(@RequestParam String projectId) {
        return new JsonObject<>(iTargetedInventoryPreparationMainService.getIsExistByProjectId(projectId));
    }

    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    @GetMapping("/isInitiateVerification")
    @Operation(summary = "项目新增专项备货时校验")
    public JsonObject<Boolean> isInitiateVerification(@RequestParam String projectId){
        return new JsonObject<>(iTargetedInventoryPreparationMainService.isInitiateVerification(getCurrentPersonId(),projectId));
    }

//    @GetMapping("/getInventorySalePassage")
//    @Operation(summary = "项目可选择销售通路 1-合同评审 2-业绩上报")
//    public JsonObject<List<Integer>> getInventorySalePassage(@RequestParam String projectId){
//        return new JsonObject<>(iTargetedInventoryPreparationMainService.getInventorySalePassage(projectId));
//    }

    @PreFlowPermission
    @GetMapping("/getTargetedInventoryPreparationByProcessInstanceId")
    @Operation(summary = "根据流程ID查询专项备货信息")
    public JsonObject<TargetedInventoryPreparationBaseInfoVO> getTargetedInventoryPreparationByProcessInstanceId(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId, "流程Id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationMainService.getTargetedInventoryPreparationByProcessInstanceId(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/getOwnPageByTargetedInventoryPreparationMainId")
    @Operation(summary = "根据主表ID分页查询明细")
    public JsonObject<List<TargetedInventoryPreparationProductOwnVO>> getOwnPageByTargetedInventoryPreparationMainId(@RequestParam String targetedInventoryPreparationMainId){
//        startPage();
        CrmAssert.hasText(targetedInventoryPreparationMainId, "专项备货Id不能为空");
        Optional<TargetedInventoryPreparationMain> optionalById = Optional.ofNullable(iTargetedInventoryPreparationMainService.getById(targetedInventoryPreparationMainId));
        if (optionalById.isPresent()){
            TargetedInventoryPreparationMain main = optionalById.get();
            PreFlowPermissionAspect.checkProcessInstanceId(main.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(iTargetedInventoryPreparationProductOwnService.getOwnPageByTargetedInventoryPreparationMainId(targetedInventoryPreparationMainId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    @PostMapping("/getAllNoCheckProductOwnByProjectId")
    @Operation(summary = "根据项目ID查询自有产品信息")
    public JsonObject<List<TargetedInventoryPreparationProductOwnVO>> getAllNoCheckProductOwnByProjectId(@RequestBody TargetedInventoryPreparationProductOwnQuery targetedInventoryPreparationProductOwnQuery){
        CrmAssert.hasText(targetedInventoryPreparationProductOwnQuery.getProjectId(), "项目ID不能为空");
        CrmAssert.hasText(targetedInventoryPreparationProductOwnQuery.getProjectNo(),"项目编号不能为空");
        return new JsonObject<>(iTargetedInventoryPreparationProductOwnService.getAllNoCheckProductOwnByProjectId(targetedInventoryPreparationProductOwnQuery));
    }

    @PreFlowPermission()
    @PostMapping("/getAllNoCheckProductOwnFlowByProjectId")
    @Operation(summary = "根据项目ID查询自有产品信息（主表ID必传值）")
    public JsonObject<List<TargetedInventoryPreparationProductOwnVO>> getAllNoCheckProductOwnFlowByProjectId(@RequestBody TargetedInventoryPreparationProductOwnQuery targetedInventoryPreparationProductOwnQuery){
        CrmAssert.hasText(targetedInventoryPreparationProductOwnQuery.getProjectId(), "项目ID不能为空");
        CrmAssert.hasText(targetedInventoryPreparationProductOwnQuery.getProjectNo(),"项目编号不能为空");
        CrmAssert.hasText(targetedInventoryPreparationProductOwnQuery.getTargetedInventoryPreparationMainId(),"主表ID不能为空");
        Optional<TargetedInventoryPreparationMain> optionalById = Optional.ofNullable(iTargetedInventoryPreparationMainService.getById(targetedInventoryPreparationProductOwnQuery.getTargetedInventoryPreparationMainId()));
        if (optionalById.isPresent()){
            TargetedInventoryPreparationMain main = optionalById.get();
            PreFlowPermissionAspect.checkProcessInstanceId(main.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(iTargetedInventoryPreparationProductOwnService.getAllNoCheckProductOwnByProjectId(targetedInventoryPreparationProductOwnQuery));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission(hasAnyNodes = {"specialStocking_06","sid-90D63A9F-2182-4ACE-A96A-F24AA662A9B0","specialStocking_11"})
    @PostMapping("/updateOwnNumByList")
    @Operation(summary = "06、10步修改专项备货数量")
    public JsonObject<Boolean> updateOwnNumByList( @RequestBody List<TargetedInventoryPreparationProductOwnDTO> ownDTOS) {
        for (TargetedInventoryPreparationProductOwnDTO ownDTO : ownDTOS) {
            iTargetedInventoryPreparationProductOwnService.getById(ownDTO.getId());
            Optional<TargetedInventoryPreparationProductOwn> optional = Optional.ofNullable(iTargetedInventoryPreparationProductOwnService.getById(ownDTO.getId()));
            if (optional.isPresent()){
                TargetedInventoryPreparationMain main = iTargetedInventoryPreparationMainService.getById(optional.get().getTargetedInventoryPreparationMainId());
                PreFlowPermissionAspect.checkProcessInstanceId(main.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            }else{
                throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
            }
        }
        return new JsonObject<>(iTargetedInventoryPreparationProductOwnService.updateOwnNumByList(ownDTOS));
    }

    @PreFlowPermission
    @GetMapping("/getChangeReByOwnId")
    @Operation(summary = "根据备货行ID查询变更记录")
    public JsonObject<List<TargetedInventoryPreparationOwnChangeReVO>> getChangeReByOwnId(@RequestParam String targetedInventoryPreparationProductOwnId){
        CrmAssert.hasText(targetedInventoryPreparationProductOwnId, "明细ID不能为空");
        Optional<TargetedInventoryPreparationProductOwn> optional = Optional.ofNullable(iTargetedInventoryPreparationProductOwnService.getById(targetedInventoryPreparationProductOwnId));
        if (optional.isPresent()){
            TargetedInventoryPreparationMain main = iTargetedInventoryPreparationMainService.getById(optional.get().getTargetedInventoryPreparationMainId());
            PreFlowPermissionAspect.checkProcessInstanceId(main.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(targetedInventoryPreparationOwnChangeReService.getChangeReByOwnId(targetedInventoryPreparationProductOwnId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission(hasAnyNodes = {"sid-E2D675BE-B22E-4CCA-9128-6722E0D548CB","sid-47276AC6-DBBB-4D2D-8980-2EE1D3C958AE","specialStocking_11"})
    @PostMapping("/updateTargetedInventoryPrearationByDTO")
    @Operation(summary = "07、08步修改专项备货延期等信息")
    public JsonObject<Boolean> updateTargetedInventoryPrearationByDTO( @RequestBody TargetedInventoryPreparationBaseInfoDTO targetedInventoryPreparationBaseInfoDTO) {
        CrmAssert.hasText(targetedInventoryPreparationBaseInfoDTO.getId(),"主表ID不能为空");
        Optional<TargetedInventoryPreparationMain> optionalById = Optional.ofNullable(iTargetedInventoryPreparationMainService.getById(targetedInventoryPreparationBaseInfoDTO.getId()));
        if (optionalById.isPresent()){
            TargetedInventoryPreparationMain main = optionalById.get();
            PreFlowPermissionAspect.checkProcessInstanceId(main.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(iTargetedInventoryPreparationMainService.updateTargetedInventoryPrearationByDTO(targetedInventoryPreparationBaseInfoDTO));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }

    }

    @PreFlowPermission
    @GetMapping("/getTargetedInventoryPreparationDocsByMainId")
    @Operation(summary = "根据主表ID查询附件信息")
    public JsonObject<List<TargetedInventoryPreparationDocDTO>> getTargetedInventoryPreparationDocsByMainId(@RequestParam String targetedInventoryPreparationId){
        CrmAssert.hasText(targetedInventoryPreparationId,"主表ID不能为空");
        Optional<TargetedInventoryPreparationMain> optionalById = Optional.ofNullable(iTargetedInventoryPreparationMainService.getById(targetedInventoryPreparationId));
        if (optionalById.isPresent()){
            TargetedInventoryPreparationMain main = optionalById.get();
            PreFlowPermissionAspect.checkProcessInstanceId(main.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(targetedInventoryPreparationDocService.getTargetedInventoryPreparationDocsByMainId(targetedInventoryPreparationId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }

    }

    @PreFlowPermission
    @GetMapping("/isDirectlySign")
    @Operation(summary = "获取专项备货项目是否直签")
    public JsonObject<Boolean> isDirectlySign(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationFlowService.isDirectlySign(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/isDelayStocking")
    @Operation(summary = "获取专项备货是否延期")
    public JsonObject<Boolean> isDelayStocking(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationFlowService.isDelayStocking(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/isAcceptDelay")
    @Operation(summary = "获取专项备货是否接受延期")
    public JsonObject<Boolean> isAcceptDelay(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationFlowService.isAcceptDelay(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/isZeroFine")
    @Operation(summary = "获取专项备货罚款金额是否为0")
    public JsonObject<Boolean> isZeroFine(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationFlowService.isZeroFine(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/isNeedPayManagementFee")
    @Operation(summary = "获取专项备货是否需要缴纳管理费")
    public JsonObject<Boolean> isNeedPayManagementFee(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationFlowService.isNeedPayManagementFee(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/isInStock")
    @Operation(summary = "获取专项备货是否在库")
    public JsonObject<Boolean> isInStock(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationFlowService.isInStock(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/isStorageRecord")
    @Operation(summary = "获取专项备货是否有入库记录")
    public JsonObject<Boolean> isStorageRecord(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationFlowService.isStorageRecord(processInstanceId));
    }

    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    @GetMapping("/getOwnWithdrawPageByMainId")
    @Operation(summary = "根据主表ID分页查询明细（撤销使用）")
    public JsonObject<TableDataInfo> getOwnWithdrawPageByMainId(@RequestParam String targetedInventoryPreparationId){
        startPage();
        CrmAssert.hasText(targetedInventoryPreparationId, "流程Id不能为空");
        return new JsonObject<>(iTargetedInventoryPreparationProductOwnService.getOwnWithdrawPageByMainId(targetedInventoryPreparationId));
    }

    @GetMapping("/getTtargetedInventoryPreparationInfoByProjectIdStuffCode")
    @Operation(summary = "根据项目ID、物料代码查询专项备货信息")
    public JsonObject<List<CrmTtargetedInventoryPreparationMainVO>> getTtargetedInventoryPreparationInfoByProjectIdStuffCode(@RequestParam String projectId,@RequestParam String stuffCode){
        return new JsonObject<>(iTargetedInventoryPreparationMainService.getTtargetedInventoryPreparationInfoByProjectIdStuffCode(projectId, stuffCode));
    }

//    @PreFlowPermission
//    @GetMapping("/autoUpdateApprovalNodeShort")
//    @Operation(summary = "06/10 暂挂自动办结")
//    public void autoUpdateApprovalNodeShort(){
//        iTargetedInventoryPreparationMainService.autoUpdateApprovalNodeShort();
//    }
//
////    @PreFlowPermission
//    @GetMapping("/autoUpdateApprovalNodePayment")
//    @Operation(summary = "09/12 财务自动办结")
//    public void autoUpdateApprovalNodePayment(){
//        iTargetedInventoryPreparationMainService.autoUpdateApprovalNodePayment();
//    }

    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    @GetMapping("/downloadTargetedInventoryPreparationFile")
    @Operation(summary = "专项备货销售保证书")
    public void downloadTargetedInventoryPreparationFile(HttpServletResponse response) throws IOException {
        iTargetedInventoryPreparationMainService.downloadTargetedInventoryPreparationFile(response,"专项备货销售保证书.docx");
    }

//    @PostMapping("/saveTargetedInventoryPreparationStock")
//    @Operation(summary = "测试阶段模拟同步数据(库存)")
//    public JsonObject<Boolean> saveTargetedInventoryPreparationStock(@RequestBody TargetedInventoryPreparationStock targetedInventoryPreparationStock){
//        return new JsonObject<>(stockService.saveTargetedInventoryPreparationStock(targetedInventoryPreparationStock));
//    }
//
//    @PostMapping("/saveTargetedInventoryPreparationStockReturn")
//    @Operation(summary = "测试阶段模拟同步数据(出库)")
//    public JsonObject<Boolean> saveTargetedInventoryPreparationStockReturn(@RequestBody TargetedInventoryPreparationStockReturn targetedInventoryPreparationStockReturn){
//        return new JsonObject<>(stockReturnService.saveTargetedInventoryPreparationStockReturn(targetedInventoryPreparationStockReturn));
//    }

    @PreFlowPermission(hasAnyNodes = {"specialStocking_03","specialStocking_06"})
    @GetMapping("/updateTargetedInventoryPreparationNodeState")
    @Operation(summary = "03步发06步更改状态")
    public JsonObject<Boolean> updateTargetedInventoryPreparationNodeState(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationMainService.updateTargetedInventoryPreparationNodeState(processInstanceId));
    }

    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    @GetMapping("/getMainInfo")
    @Operation(summary = "根据项目ID获取专项备货信息")
    public JsonObject<List<TargetedInventoryPreparationBaseInfoVO>> getMainInfo(@RequestParam String projectId){
        return new JsonObject<>(iTargetedInventoryPreparationMainService.getMainInfo(projectId));
    }

    @PreFlowPermission
    @GetMapping("/getStockVOByProcessInstanceId")
    @Operation(summary = "调配与拆分数据")
    public JsonObject<List<TargetedInventoryPreparationStockVO>> getStockVOByProcessInstanceId(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationMainService.getStockVOByProcessInstanceId(processInstanceId));
    }

    @PreFlowPermission(hasAnyNodes = {"specialStocking_11","sid-EF18582F-CAF3-4CDD-905D-DECA0E958346"})
    @GetMapping("/sendingWeChatNotifications")
    @Operation(summary = "11步发往12步时发送企微通知")
    public JsonObject<Boolean> sendingWeChatNotifications(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationMainService.sendingWeChatNotifications(processInstanceId));
    }

    @PreFlowPermission(hasAnyNodes = {"specialStocking_11"})
    @PostMapping("/updateTargetedInventoryPreparationStock")
    @Operation(summary = "11步填写处理结果")
    public JsonObject<Boolean> updateTargetedInventoryPreparationStock(@RequestBody List<TargetedInventoryPreparationStockVO> stockVOS){
        for (TargetedInventoryPreparationStockVO stockVO : stockVOS) {
            CrmAssert.hasText(stockVO.getSn(),"序列号不能为空");
            Optional<TargetedInventoryPreparationStock> optionalById = Optional.ofNullable(stockService.getOne(new QueryWrapper<TargetedInventoryPreparationStock>().lambda()
                    .eq(TargetedInventoryPreparationStock::getSn,stockVO.getSn()).eq(TargetedInventoryPreparationStock::getProcessNumber,stockVO.getProcessNumber())));
            if (optionalById.isPresent()){
                TargetedInventoryPreparationStock stock = optionalById.get();
                TargetedInventoryPreparationMain main = iTargetedInventoryPreparationMainService.getOne(new QueryWrapper<TargetedInventoryPreparationMain>().lambda().eq(TargetedInventoryPreparationMain::getDelFlag,0)
                        .eq(TargetedInventoryPreparationMain::getProcessNumber,stock.getProcessNumber()));
                PreFlowPermissionAspect.checkProcessInstanceId(main.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            }else{
                throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
            }
        }
        return new JsonObject<>(stockService.updateTargetedInventoryPreparationStock(stockVOS));

    }


}
