package com.topsec.crm.flow.core.controller.threeProcurement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.topsec.crm.flow.api.dto.contractreview.baseinfo.ContractBasicInfoDTO;
import com.topsec.crm.flow.api.vo.*;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ThreeProcurementProductLineReviewMain;
import com.topsec.crm.flow.core.service.ContractReviewFlowService;
import com.topsec.crm.flow.core.service.ContractReviewMainService;
import com.topsec.crm.flow.core.service.ThreeProcurementProductLineReviewMainService;
import com.topsec.crm.flow.core.service.ThreeProcurementReviewMainService;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.operation.api.RemoteSupplierContactService;
import com.topsec.crm.operation.api.RemoteSupplierProductService;
import com.topsec.crm.operation.api.dto.SupplierContactsQuery;
import com.topsec.crm.operation.api.entity.SupplierContactsVO;
import com.topsec.crm.operation.api.entity.SupplierVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 【产品线第三方采购】Controller
 *
 * @date 2024-09-02
 */
@RestController
@RequestMapping("/business/threeProcurementProductLine")
@Tag(name = "【【业务查询-产品线第三方采购】】", description = "threeProcurementProductLine")
@RequiredArgsConstructor
@Validated
public class ThreeProcurementProductLineReviewMainController extends BaseController
{
    private final ThreeProcurementProductLineReviewMainService threeProcurementProductLineReviewMainService;


//    private final ThreeProcurementProductLineReviewProcessService threeProcurementProductLineReviewProcessService;


    private final ThreeProcurementReviewMainService threeProcurementReviewMainService;

    private final ContractReviewMainService contractReviewMainService;

    private final ContractReviewFlowService contractReviewFlowService;

    private final RemoteSupplierContactService remoteSupplierContactService;

    private final RemoteSupplierProductService remoteSupplierProductService;


    private final TfsNodeClient tfsNodeClient;





    @GetMapping("/queryOwnProductList")
    @Operation(summary = "根据合同id查询自有产品(发起时查询产品数据)")
    @PreAuthorize(hasPermission = "crm_third_purchase_product_line_todo",dataScope = "crm_third_purchase_product_line_todo")
    public JsonObject<List<ThreeProcurementProductVo>> queryOwnProductList(@RequestParam String contractId,@RequestParam String processInstanceId){
        CrmAssert.hasText(contractId, "合同id不能为空");
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        ThreeProcurementProductLineReviewMain threeProcurementProductLineReviewMain = threeProcurementProductLineReviewMainService.queryThreeProcurementProductLineReviewMain(processInstanceId);
        if (threeProcurementProductLineReviewMain == null){
            throw new CrmException("未查询到流程信息");
        }
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementProductLineReviewMain.getAssigneeId());
        String personId = threeProcurementProductLineReviewMain.getAssigneeId();
        if (StringUtils.isBlank(personId)){
            throw new CrmException("未查询到办理人");
        }
        List<ThreeProcurementProductVo> productOwnDTOList =threeProcurementReviewMainService.queryOwnProductList(contractId, personId,threeProcurementProductLineReviewMain.isReturnAndExchange());
        if (CollectionUtils.isNotEmpty(productOwnDTOList)){
            threeProcurementReviewMainService.findThreeProcurementProductStatus(processInstanceId, productOwnDTOList);
        }
        return new JsonObject<>(productOwnDTOList);
    }



    /**
     * 分页查询【【产品线采购提醒列表】】
     */

    @PostMapping("/page")
    @Operation(summary = "分页查询产品线采购提醒列表")
    @PreAuthorize(hasPermission = "crm_third_purchase_product_line_todo",dataScope = "crm_third_purchase_product_line_todo")
    public JsonObject<PageUtils<ThreeProcurementProductLineReviewMainVo>> page(@RequestBody ThreeProcurementReviewQuery threeProcurementReviewQuery)
    {
        Page<ThreeProcurementProductLineReviewMain> threeProcurementProductLineReviewMainPage = threeProcurementProductLineReviewMainService.pageThreeProcurementProductLineReviewMainList(threeProcurementReviewQuery);
        IPage<ThreeProcurementProductLineReviewMainVo> convert = threeProcurementProductLineReviewMainPage.convert(threeProcurementProductLineReview -> {
            return HyperBeanUtils.copyPropertiesByJackson(threeProcurementProductLineReview, ThreeProcurementProductLineReviewMainVo.class);
        });
        List<ThreeProcurementProductLineReviewMainVo> convertRecords = convert.getRecords();
        List<String> stringList = convertRecords.stream().map(ThreeProcurementProductLineReviewMainVo::getProcessInstanceId).toList();
        if (CollectionUtils.isNotEmpty(stringList)){
            Map<String, Set<ApproveNode>> stringSetMap = Optional.ofNullable(tfsNodeClient.queryNodeByProcessInstanceIdList(ListUtils.emptyIfNull(stringList)))
                    .map(JsonObject::getObjEntity).orElseThrow(() -> new RuntimeException("审批节点查询失败"));
            List<String> ids = convertRecords.stream().map(ThreeProcurementProductLineReviewMainVo::getCreateUser).toList();
            Map<String, String> nameMap = NameUtils.getNameMap(ids);
            convertRecords.forEach(threeProcurementProductLineReviewMainVo -> {
                threeProcurementProductLineReviewMainVo.setCreateUserName(nameMap.get(threeProcurementProductLineReviewMainVo.getCreateUser()));
                Set<ApproveNode> approveNodeSet = stringSetMap.get(threeProcurementProductLineReviewMainVo.getProcessInstanceId());
                threeProcurementProductLineReviewMainVo.setApprovalNode(approveNodeSet);
            });
        }

        return new JsonObject<>(new PageUtils<>(convert));
    }


    @GetMapping("/queryThreeProcurementProductLineContract")
    @Operation(summary = "根据流程实例id查询【产品线第三方采购提醒】合同信息")
    @PreAuthorize(hasPermission = "crm_third_purchase_product_line_todo",dataScope = "crm_third_purchase_product_line_todo")
    public JsonObject<ContractBasicInfoDTO> queryThreeProcurementProductLineContract(@RequestParam String processInstanceId){
        ThreeProcurementProductLineReviewMain threeProcurementProductLineReviewMain = threeProcurementProductLineReviewMainService.queryThreeProcurementProductLineReviewMain(processInstanceId);
        if (threeProcurementProductLineReviewMain == null){
            throw new CrmException("流程实例id不存在");
        }
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementProductLineReviewMain.getAssigneeId());
        return new JsonObject<>(contractReviewMainService.contractBasicInfo(threeProcurementProductLineReviewMain.getContractId(), true));

    }


    @GetMapping("/contractAmount")
    @Operation(summary = "合同总金额")
    @PreAuthorize(hasPermission = "crm_third_purchase_product_line_todo",dataScope = "crm_third_purchase_product_line_todo")
    public JsonObject<BigDecimal> contractAmount(@RequestParam String processInstanceId) {
        ThreeProcurementProductLineReviewMain threeProcurementProductLineReviewMain = threeProcurementProductLineReviewMainService.queryThreeProcurementProductLineReviewMain(processInstanceId);
        if (threeProcurementProductLineReviewMain == null){
            throw new CrmException("流程实例id不存在");
        }
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementProductLineReviewMain.getAssigneeId());
        return new JsonObject<>(contractReviewFlowService.contractAmountTotal(threeProcurementProductLineReviewMain.getContractId()));
    }




    @PostMapping("/pageThreeProcurement")
    @Operation(summary = "产品线采购提醒详情分页查询第三方采购审批列表")
    @PreAuthorize(hasPermission = "crm_third_purchase_product_line_todo",dataScope = "crm_third_purchase_product_line_todo")
    public JsonObject<PageUtils<ThreeProcurementReviewMainVo>> pageThreeProcurement(@RequestBody ThreeProcurementReviewQuery threeProcurementReviewQuery)
    {
        threeProcurementReviewQuery.setCreateUser(UserInfoHolder.getCurrentPersonId());
        ThreeProcurementProductLineReviewMain threeProcurementProductLineReviewMain = threeProcurementProductLineReviewMainService.queryThreeProcurementProductLineReviewMain(threeProcurementReviewQuery.getProcessInstanceId());
        if (threeProcurementProductLineReviewMain == null){
            throw new CrmException("流程实例id不存在");
        }
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementProductLineReviewMain.getAssigneeId());
        IPage<ThreeProcurementReviewMainVo> convert = threeProcurementReviewMainService.getThreeProcurementReviewMainVoIPage(threeProcurementReviewQuery);

        return new JsonObject<>(new PageUtils<>(convert));
    }



    @GetMapping("/findSupplierById")
    @Operation(summary = "根据供应商id查询供应商基本信息-自有产品")
    @PreAuthorize(hasPermission = "crm_third_purchase_product_line_todo",dataScope = "crm_third_purchase_product_line_todo")
    public JsonObject<ThreeProcurementSupplierVo> findSupplierById(@RequestParam String processInstanceId, @RequestParam String supplierId){
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        CrmAssert.hasText(supplierId, "供应商id不能为空");
        ThreeProcurementProductLineReviewMain threeProcurementProductLineReviewMain = threeProcurementProductLineReviewMainService.queryThreeProcurementProductLineReviewMain(processInstanceId);
        if (threeProcurementProductLineReviewMain == null){
            throw new CrmException("产品线采购提醒流程不存在");
        }
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), threeProcurementProductLineReviewMain.getAssigneeId());
        SupplierVO supplierVO = Optional.ofNullable(remoteSupplierProductService.selectCrmSupplierById(supplierId)).map(JsonObject::getObjEntity)
                .orElseThrow(() -> new RuntimeException("供应商信息查询失败"));
        // 供应商联系人构建查询对象
        SupplierContactsQuery query = new SupplierContactsQuery();
        query.setSupplierIds(List.of(supplierId));
        query.setCreateUser(UserInfoHolder.getCurrentPersonId());
        List<SupplierContactsVO> contactsVOS = Optional.ofNullable(remoteSupplierContactService.getSupplierContacts(query))
                .map(JsonObject::getObjEntity).orElseThrow(() -> new CrmException("供应商联系人查询失败"));
        ThreeProcurementSupplierVo threeProcurementSupplierVo = ThreeProcurementSupplierVo.builder().supplierVO(supplierVO).supplierContacts(contactsVOS).build();
        return new JsonObject<>(threeProcurementSupplierVo);
    }








}
