package com.topsec.crm.flow.core.controller.costFiling;


import com.topsec.crm.flow.api.dto.costContarct.CostContractAttachmentInfoDTO;
import com.topsec.crm.flow.api.dto.costFiling.CostFilingInfo;
import com.topsec.crm.flow.api.dto.costFiling.VO.CostFilingVO;
import com.topsec.crm.flow.api.dto.costFiling.VO.SupplierInfoVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.service.CostContractAttachmentService;
import com.topsec.crm.flow.core.service.ICostFilingDetailService;
import com.topsec.crm.flow.core.service.ICostFilingService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;

import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/business/costFiling")
@Tag(name = "【费用备案-业务接口】", description = "costFiling")
@RequiredArgsConstructor
@Validated
public class CostFilingBusinessController extends BaseController {

    private final ICostFilingService iCostFilingService;

    private final ICostFilingDetailService iCostFilingDetailService;

    private final CostContractAttachmentService costContractAttachmentService;

    @PreAuthorize(hasPermission="crm_cost_filing",dataScope="crm_cost_filing")
    @PostMapping("/selectCostFilingByProcessNumber")
    @Operation(summary = "分页查询查询费用备案")
    public JsonObject<TableDataInfo> selectCostFilingByProcessNumber(@RequestBody CostFilingVO costFilingVO){
        startPage();
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdsOfPermission = dataScopeParam != null ? dataScopeParam.getPersonIdList() : Collections.EMPTY_SET;
        return new JsonObject<>(iCostFilingService.selectCostFilingByProcessNumber(costFilingVO,personIdsOfPermission));
    }

    @PreAuthorize(hasPermission="crm_cost_filing",dataScope="crm_cost_filing")
    @GetMapping("/selectCostFilingByProcessInstanceId")
    @Operation(summary = "根据流程ID查询费用备案")
    public JsonObject<CostFilingInfo> selectCostFilingByProcessInstanceId(String processInstanceId){
        return new JsonObject<>(iCostFilingService.selectCostFilingByProcessInstanceId(processInstanceId));
    }

    @PreAuthorize(hasPermission = "crm_cost_filing_add")
    @GetMapping("/deleteCostFilingDetailById")
    @Operation(summary = "删除费用备案明细")
    public JsonObject<Boolean> deleteCostFilingDetailById(String id){
        return new JsonObject<>(iCostFilingDetailService.deleteCostFilingDetailById(id));
    }

    @PreAuthorize(hasPermission="crm_cost_filing",dataScope="crm_cost_filing",dataMasking = "crm_cost_filing")
    @GetMapping("/selectDetailByCostFilingId")
    @Operation(summary = "根据主表ID分页查询费用备案明细")
    public JsonObject<TableDataInfo> selectDetailByCostFilingId(String costFilingId){
        return new JsonObject<>(iCostFilingDetailService.selectDetailByCostFilingId(costFilingId));
    }


    @PreAuthorize(hasPermission="crm_cost_filing")
    @GetMapping("/getRatioByCostFilingId")
    @Operation(summary = "根据费用备案ID查询是否需要提前支付")
    public JsonObject<? extends Object> getRatioByCostFilingId(@RequestParam String costFilingId){
        return iCostFilingDetailService.getRatioByCostFilingId(costFilingId);
    }

    @PreAuthorize(hasPermission = "crm_cost_filing_add")
    @PostMapping("/saveAttachment")
    @Operation(summary = "新增费用备案附件")
    public JsonObject<Boolean> saveAttachment(@RequestBody CostContractAttachmentInfoDTO attachmentInfoDTO){
        CrmAssert.hasText(attachmentInfoDTO.getCostId(),"主表ID不能为空");
        attachmentInfoDTO.setCostType("费用备案");
        return new JsonObject<>(costContractAttachmentService.saveAttachment(attachmentInfoDTO));
    }

    @PreAuthorize(hasPermission = "crm_cost_filing_add")
    @GetMapping("/deleteAttachment")
    @Operation(summary = "删除费用备案附件信息")
    public JsonObject<Boolean> deleteAttachment(@RequestParam String id){
        return new JsonObject<>(costContractAttachmentService.deleteAttachment(id));
    }

    @PreAuthorize(hasPermission="crm_cost_filing")
    @GetMapping("/getByCostContractId")
    @Operation(summary = "查询费用备案附件信息")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getByCostContractId(@RequestParam String costId,@RequestParam(required = false) String isFinalQuery){
        return new JsonObject<>(costContractAttachmentService.getByCostContractId(costId,isFinalQuery));
    }
}
