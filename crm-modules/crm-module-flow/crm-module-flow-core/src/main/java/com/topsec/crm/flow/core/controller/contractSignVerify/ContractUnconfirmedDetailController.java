package com.topsec.crm.flow.core.controller.contractSignVerify;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.CrmRevenueRecognitionVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedDetailVo;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.product.api.RemoteProductService;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contractUnconfirmedDetail")
@Tag(name = "收入确认核定对象（平铺） 相关Controller", description = "/contractUnconfirmedDetail")
@RequiredArgsConstructor
@Validated
public class ContractUnconfirmedDetailController extends BaseController {
    @Autowired
    private IContractUnconfirmedDetailService contractUnconfirmedDetailService;
    @Autowired
    private IContractUnconfirmedDetailSnapshotService contractUnconfirmedDetailSnapshotService;
    @Autowired
    private IContractUnconfirmedService contractUnconfirmedService;
    @Autowired
    private IContractUnconfirmedSnapshotService contractUnconfirmedSnapshotService;
    @Autowired
    private IContractSignVerifyMainService contractSignVerifyMainService;
    @Autowired
    private RemoteProductService remoteProductService;

    /**
     * 分页列表
     *
     * 合同纬度，只传contractNumber
     * 未确认明细纬度，传入contractNumber和unconfirmedId
     */
    @PostMapping("/selectPage")
    @Operation(summary = "收入确认核定列表记录")
    @PreFlowPermission
    public JsonObject<Map<String,List<ContractUnconfirmedDetailVo>>> selectPage(@RequestBody ContractUnconfirmedDetailVo contractUnconfirmedDetailVo) {
        Map<String,List<ContractUnconfirmedDetailVo>> result = new HashMap<String,List<ContractUnconfirmedDetailVo>>();
        //查询流程状态
        ContractSignVerifyMain contractSignVerifyMain = contractSignVerifyMainService.query().eq("process_instance_id", contractUnconfirmedDetailVo.getProcessInstanceId()).one();
        if(contractSignVerifyMain.getProcessState() != ApprovalStatusEnum.YSP.getCode()) {
            //流程审批中，实时查询
            if (StringUtils.isNotBlank(contractUnconfirmedDetailVo.getUnconfirmedId())) {
                List<ContractUnconfirmedDetail> details = contractUnconfirmedDetailService.query()
                        .eq(StringUtils.isNotBlank(contractUnconfirmedDetailVo.getUnconfirmedId()), "unconfirmed_id", contractUnconfirmedDetailVo.getUnconfirmedId())
//                        .eq("is_return", 0)
                        .eq("del_flag", 0)
                        .orderByDesc("sign_date")
                        .list();

//                evavReturn(details, result, contractUnconfirmedDetailVo.getUnconfirmedId());
            }
            if (StringUtils.isBlank(contractUnconfirmedDetailVo.getUnconfirmedId()) && StringUtils.isNotBlank(contractUnconfirmedDetailVo.getContractNumber())) {
                //查询未确认明细
                List<ContractUnconfirmed> uns = contractUnconfirmedService.query().eq("contract_number", contractUnconfirmedDetailVo.getContractNumber()).list();

                for (ContractUnconfirmed un : uns) {
                    List<ContractUnconfirmedDetail> details = contractUnconfirmedDetailService.query()
                            .eq(StringUtils.isNotBlank(un.getId()), "unconfirmed_id", un.getId())
//                            .eq("is_return", 0)
                            .eq("del_flag", 0)
                            .orderByDesc("sign_date")
                            .list();

                    //对收入确认明细做汇总，减去退换货的明细
//                    evavReturn(details, result, un.getId());
                }
            }
        }else{
            //流程审批结束，查询快照数据
            List<ContractUnconfirmedDetailSnapshot> details = contractUnconfirmedDetailSnapshotService.query()
                    .eq("process_instance_id", contractUnconfirmedDetailVo.getProcessInstanceId())
                    .orderByDesc("sign_date")
                    .list();
            //对快照数据按unconfirmedId分组
            Map<String, List<ContractUnconfirmedDetailSnapshot>> map = details.stream().collect(Collectors.groupingBy(ContractUnconfirmedDetailSnapshot::getUnconfirmedId));
            for (String key : map.keySet()) {
                //对收入确认明细做汇总，减去退换货的明细
                int sum = map.get(key).stream().mapToInt(ContractUnconfirmedDetailSnapshot::getSignNumber).sum();
                List<ContractUnconfirmedDetailSnapshot> list = map.get(key).stream().filter(e -> e.getSignNumber() == 1).limit(sum).toList();
                result.put(key, HyperBeanUtils.copyListPropertiesByJackson(list, ContractUnconfirmedDetailVo.class));
            }
        }

        //补充人员名称和电子发货标识
        Set<String> personIds = new HashSet<>();
        Set<String> materialCods = new HashSet<>();
        for (String key : result.keySet()) {
            personIds.addAll(result.get(key).stream().map(ContractUnconfirmedDetailVo::getCreateUser).collect(Collectors.toSet()));
            materialCods.addAll(result.get(key).stream().map(ContractUnconfirmedDetailVo::getMaterialCode).collect(Collectors.toSet()));
        }
        JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(personIds.stream().toList());
        if(byIds.isSuccess() && byIds.getObjEntity() != null){
            List<EmployeeVO> employ = byIds.getObjEntity();
            for (String key : result.keySet()) {
                for (ContractUnconfirmedDetailVo unconfirmedDetailVo : result.get(key)) {
                    EmployeeVO employeeVO = employ.stream().filter(e -> e.getUuid().equals(unconfirmedDetailVo.getCreateUser())).findFirst().orElse(null);
                    unconfirmedDetailVo.setCreateUserName(employeeVO != null ? NameUtils.getNameByEmployeeVO(employeeVO) : "");
                }
            }
        }

        if(CollectionUtil.isNotEmpty(materialCods)) {
            JsonObject<List<CrmProductVo>> proObj = remoteProductService.batchGetInfoByMaterialCode(materialCods.stream().toList());
            if(proObj.isSuccess() && proObj.getObjEntity() != null){
                List<CrmProductVo> productVos = proObj.getObjEntity();
                for (String key : result.keySet()) {
                    for (ContractUnconfirmedDetailVo unconfirmedDetailVo : result.get(key)) {
                        //设置生产类型
                        CrmProductVo productVo = productVos.stream().filter(c -> c.getMaterialCode().equals(unconfirmedDetailVo.getMaterialCode())).findFirst().orElse(null);
                        if(productVo != null && productVo.getProductionType() != null) {
                            unconfirmedDetailVo.setIsDZFH(productVo.getProductionType().equals("DZFH") ? true : false);
                        }else{
                            unconfirmedDetailVo.setIsDZFH(false);
                        }
                    }
                }
            }
        }

        return new JsonObject<>(result);
    }

    private void evavReturn(List<ContractUnconfirmedDetail> details, Map<String, List<ContractUnconfirmedDetailVo>> result, String contractUnconfirmedDetailVo) {
        //对收入确认明细做汇总，减去退换货的明细
        int sum = details.stream().mapToInt(ContractUnconfirmedDetail::getSignNumber).sum();
        List<ContractUnconfirmedDetail> list = details.stream().filter(e -> e.getSignNumber() == 1).limit(sum).toList();
        result.put(contractUnconfirmedDetailVo, HyperBeanUtils.copyListPropertiesByJackson(list, ContractUnconfirmedDetailVo.class));
    }

    @PostMapping("/editSignDate")
    @Operation(summary = "填写收入确认核定")
    @PreFlowPermission(hasAnyNodes = {"reminderReceipt_03"})
    public JsonObject<Boolean> editSignDate(@RequestBody List<ContractUnconfirmedDetailVo> unconfirmedDetailVos) {
        //权限校验


        //1.删除signDate日期为空的数据
        Set<String> notRemoveIds = new HashSet<String>();
        for (ContractUnconfirmedDetailVo unconfirmedDetailVo : unconfirmedDetailVos) {
            if(StringUtils.isNotNull(unconfirmedDetailVo.getSignDate())){
                notRemoveIds.add(unconfirmedDetailVo.getId());
            }
        }
        Map<String, List<ContractUnconfirmedDetailVo>> collect = unconfirmedDetailVos.stream().collect(Collectors.groupingBy(ContractUnconfirmedDetailVo::getUnconfirmedId));
        for (String key : collect.keySet()) {
            ContractUnconfirmedDetailVo delVo = collect.get(key).get(0);
            contractUnconfirmedDetailService.remove(new QueryWrapper<ContractUnconfirmedDetail>()
                    .eq("unconfirmed_id", delVo.getUnconfirmedId())
                    .notIn(CollectionUtil.isNotEmpty(notRemoveIds),"id", notRemoveIds));
        }

        //2.填写签验收时间
        List<ContractUnconfirmedDetail> details = new ArrayList<ContractUnconfirmedDetail>();
        for (ContractUnconfirmedDetailVo unconfirmedDetailVo : unconfirmedDetailVos) {
            if(StringUtils.isNotNull(unconfirmedDetailVo.getSignDate())){
                ContractUnconfirmedDetail detail = HyperBeanUtils.copyPropertiesByJackson(unconfirmedDetailVo, ContractUnconfirmedDetail.class);
                detail.setId(StringUtils.isNotBlank(detail.getId()) ? detail.getId() : UUID.randomUUID().toString());
                detail.setSignNumber(1);
                detail.setIsReturn(0);
                details.add(detail);
            }
        }
        contractUnconfirmedDetailService.saveOrUpdateBatch(details);

        return new JsonObject<>(true);
    }
}
