package com.topsec.crm.flow.core.controllerhidden.contractSuspensionOfReimbursement;

import com.topsec.crm.flow.core.service.ContractSuspensionOfReimbursementService;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/hiddenContractSuspensionOfReimbursement")
@Tag(name = "暂停报销", description = "暂停报销")
@RequiredArgsConstructor
@Validated
public class HiddenContractSuspensionOfReimbursementController {

    private final ContractSuspensionOfReimbursementService contractSuspensionOfReimbursementService;

    @GetMapping("/sync")
    public JsonObject<Void> updateContractSuspensionOfReimbursement() {
        contractSuspensionOfReimbursementService.sync();
        return new JsonObject<>();
    }

}
