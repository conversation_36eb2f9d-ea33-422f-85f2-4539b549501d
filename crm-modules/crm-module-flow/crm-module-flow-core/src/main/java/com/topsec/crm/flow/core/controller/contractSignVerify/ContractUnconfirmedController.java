package com.topsec.crm.flow.core.controller.contractSignVerify;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.UnconfirmedExportVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedVo;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewCustomerDTO;
import com.topsec.crm.flow.core.entity.ContractBadDebtMain;
import com.topsec.crm.flow.core.entity.ContractSignVerifyMain;
import com.topsec.crm.flow.core.entity.ContractUnconfirmed;
import com.topsec.crm.flow.core.entity.ContractUnconfirmedDetail;
import com.topsec.crm.flow.core.service.IContractSignVerifyMainService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedDetailService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedService;
import com.topsec.crm.framework.common.enums.ContractEnum;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contractUnconfirmed")
@Tag(name = "未确认（确认条款）明细列表 相关Controller", description = "/contractUnconfirmed")
@RequiredArgsConstructor
@Validated
public class ContractUnconfirmedController extends BaseController {
    @Autowired
    private IContractUnconfirmedService contractUnconfirmedService;
    @Autowired
    private IContractUnconfirmedDetailService contractUnconfirmedDetailService;
    @Autowired
    private RemoteContractExecuteService remoteContractExecuteService;
    @Autowired
    private RemoteContractReviewService remoteContractReviewService;
    @Autowired
    private IContractSignVerifyMainService contractSignVerifyMainService;

    /**
     * 分页列表
     */
    @PostMapping("/selectPage")
    @Operation(summary = "查询未确认明细列表记录")
    @PreAuthorize(hasPermission = "crm_contract_unconfirmed_sign_verify",dataScope = "crm_contract_unconfirmed_sign_verify")
    public JsonObject<PageUtils<ContractUnconfirmedVo>> selectPage(@RequestBody ContractUnconfirmedVo contractUnconfirmedVo) {
        //查询符合条件的合同执行ID
        List<String> filterContractNumbers = new ArrayList<String>();
        //组合页面负责人参数和权限范围内的用户ID
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();

        Set<String> personIdList = dataScopeParam.getPersonIdList();
        Set<String> queryPIds = new HashSet<String>();

        if(StringUtils.isNotBlank(contractUnconfirmedVo.getContractOwnerDeptId())
                || CollectionUtil.isNotEmpty(contractUnconfirmedVo.getContractOwnerIds())
                || CollectionUtil.isNotEmpty(personIdList)) {
            CrmContractAfterQuery query = new CrmContractAfterQuery();
            CrmContractAfterQuery.BaseQuery baseQuery = new CrmContractAfterQuery.BaseQuery();
            baseQuery.setContractOwnerDeptId(StringUtils.isNotBlank(contractUnconfirmedVo.getContractOwnerDeptId()) ? contractUnconfirmedVo.getContractOwnerDeptId() : null);
            if (personIdList != null) {
                if (CollectionUtil.isNotEmpty(contractUnconfirmedVo.getContractOwnerIds())) {
                    //找出两者的并集
                    for (String s : personIdList) {
                        for (String contractOwnerId : contractUnconfirmedVo.getContractOwnerIds()) {
                            if (s.equals(contractOwnerId)) {
                                queryPIds.add(contractOwnerId);
                            }
                        }
                    }
                    if (CollectionUtil.isEmpty(queryPIds)) {
                        return new JsonObject<>(new PageUtils<ContractUnconfirmedVo>());
                    }
                    baseQuery.setContractOwnerIds(queryPIds.stream().toList());
                } else {
                    baseQuery.setContractOwnerIds(personIdList.stream().toList());
                }
            } else {
                //如果personIdList == null，全量数据权限
                if (CollectionUtil.isNotEmpty(contractUnconfirmedVo.getContractOwnerIds())) {
                    baseQuery.setContractOwnerIds(contractUnconfirmedVo.getContractOwnerIds());
                } else {
                    //设置为null，查询全量数据
                    baseQuery.setContractOwnerIds(null);
                }
            }
            query.setBaseQuery(baseQuery);
            query.setNeedContractType(true);
            JsonObject<PageUtils<CrmContractExecuteVO>> pageUtilsJsonObject = remoteContractExecuteService.pageByCondition(query);
            if (pageUtilsJsonObject.isSuccess() && pageUtilsJsonObject.getObjEntity() != null && CollectionUtil.isNotEmpty(pageUtilsJsonObject.getObjEntity().getList())) {
                if (CollectionUtil.isNotEmpty(pageUtilsJsonObject.getObjEntity().getList())) {
                    filterContractNumbers = pageUtilsJsonObject.getObjEntity().getList().stream().map(CrmContractExecuteVO::getContractNumber).toList();
                }
            } else {
                return new JsonObject<>(new PageUtils<ContractUnconfirmedVo>());
            }
        }

        startPage();
        List<ContractUnconfirmed> list = contractUnconfirmedService.query()
                .in(CollectionUtil.isNotEmpty(filterContractNumbers), "contract_number", filterContractNumbers)
                .eq(StringUtils.isNotBlank(contractUnconfirmedVo.getMaterialCode()),"material_code", contractUnconfirmedVo.getMaterialCode())
                .eq(StringUtils.isNotBlank(contractUnconfirmedVo.getContractNumber()), "contract_number", contractUnconfirmedVo.getContractNumber())
                .eq(StringUtils.isNotNull(contractUnconfirmedVo.getConfirmType()),"confirm_type", contractUnconfirmedVo.getConfirmType())
                //如果是阶段性验收（2），需要查询阶段性验收报告
                .eq(StringUtils.isNotNull(contractUnconfirmedVo.getConfirmType()) && contractUnconfirmedVo.getConfirmType() == 2 && StringUtils.isNotNull(contractUnconfirmedVo.getStage()),"stage", contractUnconfirmedVo.getStage())
                .orderByDesc("create_time")
                .list();

        if(CollectionUtil.isNotEmpty(list)){
            Set<String> contractNumbers = list.stream().map(ContractUnconfirmed::getContractNumber).collect(Collectors.toSet());
            //查询合同执行信息
            JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(contractNumbers);
            if(byContractNumberBatch.isSuccess()){
                List<CrmContractExecuteVO> contractInfos = byContractNumberBatch.getObjEntity();
                Set<String> contractIds = contractInfos.stream().map(CrmContractExecuteVO::getContractId).collect(Collectors.toSet());
                //查询预付款金额
                JsonObject<List<ContractReviewCustomerDTO>> cib = remoteContractReviewService.getCustomerInfoByContractIdBatch(contractIds);
                if(cib.isSuccess()) {
                    List<ContractReviewCustomerDTO> reviews = cib.getObjEntity();
                    for (ContractUnconfirmed contractUnconfirmed : list) {
                        CrmContractExecuteVO crmContractExecuteVO = contractInfos.stream().filter(c -> c.getContractNumber().equals(contractUnconfirmed.getContractNumber())).findFirst().orElse(null);
                        contractUnconfirmed.setContractExecuteVO(crmContractExecuteVO);

                        if (crmContractExecuteVO != null) {
                            ContractReviewCustomerDTO reviewCustomerDTO = reviews.stream().filter(c -> c.getContractReviewMainId().equals(crmContractExecuteVO.getContractId())).findFirst().orElse(null);
                            contractUnconfirmed.setPrepayment(reviewCustomerDTO != null ? reviewCustomerDTO.getPrepayment() : null);
                        }
                    }
                }

                //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
                List<ContractUnconfirmedVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, ContractUnconfirmedVo.class);
                //补充合同原销售单位字段-坏账时间字段
                for (ContractUnconfirmedVo detail : listVo) {
                    ContractExecuteVO contractExecuteVO = detail.getContractExecuteVO();

                    JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(contractExecuteVO != null ? contractExecuteVO.getSaleId() : "");
                    if(byId.isSuccess() && byId.getObjEntity() != null){
                        contractExecuteVO.setSaleDeptName(byId.getObjEntity().getDept() != null ? byId.getObjEntity().getDept().getName() : "");
                    }
                }

                PageUtils dataTable = getDataTable(list,listVo);
                return new JsonObject<>(dataTable);
            }
        }
        return new JsonObject<>(new PageUtils<ContractUnconfirmedVo>());
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    @Operation(summary = "导出", description = "示例")
    @PreAuthorize(hasPermission = "crm_contract_urge_sign_verify_export",dataScope = "crm_contract_unconfirmed_sign_verify_export")
    public void export(@RequestBody ContractUnconfirmedVo contractUnconfirmedVo) throws Exception {
        List<UnconfirmedExportVO> result = new ArrayList<UnconfirmedExportVO>();
        //查询符合条件的合同执行ID
        List<String> filterContractNumbers = new ArrayList<String>();
        //组合页面负责人参数和权限范围内的用户ID
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();

        Set<String> personIdList = dataScopeParam.getPersonIdList();
        Set<String> queryPIds = new HashSet<String>();

        CrmContractAfterQuery query = new CrmContractAfterQuery();
        CrmContractAfterQuery.BaseQuery baseQuery = new CrmContractAfterQuery.BaseQuery();
        baseQuery.setContractOwnerDeptId(StringUtils.isNotBlank(contractUnconfirmedVo.getContractOwnerDeptId()) ? contractUnconfirmedVo.getContractOwnerDeptId() : null);
        if(personIdList != null) {
            if(CollectionUtil.isNotEmpty(contractUnconfirmedVo.getContractOwnerIds())){
                //找出两者的并集
                for (String s : personIdList) {
                    for (String contractOwnerId : contractUnconfirmedVo.getContractOwnerIds()) {
                        if(s.equals(contractOwnerId)){
                            queryPIds.add(contractOwnerId);
                        }
                    }
                }
                if(CollectionUtil.isEmpty(queryPIds)){
                    ExcelUtil<UnconfirmedExportVO> excelUtil = new ExcelUtil<>(UnconfirmedExportVO.class);
                    excelUtil.exportExcel(response, result, "未确认明细列表信息");
                }
                baseQuery.setContractOwnerIds(queryPIds.stream().toList());
            }else{
                baseQuery.setContractOwnerIds(personIdList.stream().toList());
            }
        }else{
            //如果personIdList == null，全量数据权限
            if (CollectionUtil.isNotEmpty(contractUnconfirmedVo.getContractOwnerIds())) {
                baseQuery.setContractOwnerIds(contractUnconfirmedVo.getContractOwnerIds());
            }else{
                //设置为null，查询全量数据
                baseQuery.setContractOwnerIds(null);
            }
        }
        query.setBaseQuery(baseQuery);
        query.setNeedContractType(true);
        JsonObject<PageUtils<CrmContractExecuteVO>> pageUtilsJsonObject = remoteContractExecuteService.pageByCondition(query);
        if(pageUtilsJsonObject.isSuccess() && pageUtilsJsonObject.getObjEntity() != null && CollectionUtil.isNotEmpty(pageUtilsJsonObject.getObjEntity().getList())){
            if(CollectionUtil.isNotEmpty(pageUtilsJsonObject.getObjEntity().getList())){
                filterContractNumbers = pageUtilsJsonObject.getObjEntity().getList().stream().map(CrmContractExecuteVO::getContractNumber).toList();
            }
        }else{
            ExcelUtil<UnconfirmedExportVO> excelUtil = new ExcelUtil<>(UnconfirmedExportVO.class);
            excelUtil.exportExcel(response, result, "未确认明细列表信息");
        }

        List<ContractUnconfirmed> list = contractUnconfirmedService.query()
                .in(CollectionUtil.isNotEmpty(filterContractNumbers), "contract_number", filterContractNumbers)
                .eq(StringUtils.isNotBlank(contractUnconfirmedVo.getMaterialCode()),"material_code", contractUnconfirmedVo.getMaterialCode())
                .eq(StringUtils.isNotBlank(contractUnconfirmedVo.getContractNumber()), "contract_number", contractUnconfirmedVo.getContractNumber())
                .eq(StringUtils.isNotNull(contractUnconfirmedVo.getConfirmType()),"confirm_type", contractUnconfirmedVo.getConfirmType())
                //如果是阶段性验收（2），需要查询阶段性验收报告
                .eq(StringUtils.isNotNull(contractUnconfirmedVo.getConfirmType()) && contractUnconfirmedVo.getConfirmType() == 2 && StringUtils.isNotNull(contractUnconfirmedVo.getStage()),"stage", contractUnconfirmedVo.getStage())
                .orderByDesc("create_time")
                .list();

        if(CollectionUtil.isNotEmpty(list)){
            //查询合同执行信息
            JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(list.stream().map(ContractUnconfirmed::getContractNumber).collect(Collectors.toSet()));
            if(byContractNumberBatch.isSuccess()){
                List<CrmContractExecuteVO> contractInfos = byContractNumberBatch.getObjEntity();

                for (ContractUnconfirmed contractUnconfirmed : list) {
                    CrmContractExecuteVO crmContractExecuteVO = contractInfos.stream().filter(c -> c.getContractNumber().equals(contractUnconfirmed.getContractNumber())).findFirst().orElse(null);

                    UnconfirmedExportVO exportVO = HyperBeanUtils.copyPropertiesByJackson(contractUnconfirmed, UnconfirmedExportVO.class);
                    if(crmContractExecuteVO != null){
                        exportVO.setContractOwnerDeptName(crmContractExecuteVO.getContractOwnerDeptName());
                        exportVO.setContractOwnerName(crmContractExecuteVO.getContractOwnerName());
                        JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(crmContractExecuteVO.getSaleId());
                        if(byId.isSuccess() && byId.getObjEntity() != null){
                            exportVO.setDeptName(byId.getObjEntity().getDept() != null ? byId.getObjEntity().getDept().getName() : "");
                        }
                        exportVO.setSaleName(crmContractExecuteVO.getSaleName());
                        exportVO.setContractNumber(crmContractExecuteVO.getContractNumber());
                        exportVO.setContractTime(crmContractExecuteVO.getContractTime());
                        exportVO.setEstimatedTime(crmContractExecuteVO.getEstimatedTime());
                    }

                    //设置收入确认条款
                    String s = contractUnconfirmed.getConfirmType() == ContractEnum.CONFIRMTYPE.STOP.getCode() ? ContractEnum.CONFIRMTYPE.STOP.getValue()+"-"+ContractEnum.STAGE.getStage(contractUnconfirmed.getStage()) : ContractEnum.CONFIRMTYPE.getValue(contractUnconfirmed.getConfirmType());
                    exportVO.setConfirmTypeAndStage(s);

                    result.add(exportVO);
                }
            }
        }

        ExcelUtil<UnconfirmedExportVO> excelUtil = new ExcelUtil<>(UnconfirmedExportVO.class);
        excelUtil.exportExcel(response, result, "未确认明细列表信息");
    }
}
