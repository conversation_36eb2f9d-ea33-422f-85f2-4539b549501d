package com.topsec.crm.flow.core.controllerhidden.paymentverification;

import com.topsec.crm.flow.core.service.VerificationRefundService;
import com.topsec.crm.framework.common.bean.ProcessExtensionInfoVO;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/hidden/verificationRefund")
@RequiredArgsConstructor
@Tag(name = "退款")
public class HiddenVerificationRefundController {

    private final VerificationRefundService refundService;

    @GetMapping("/getRefundLaunchInfoList")
    @Operation(hidden = true)
    public JsonObject<List<ProcessExtensionInfoVO>> getRefundLaunchInfoList(@RequestParam List<String> verificationIds) {
        return new JsonObject<>(refundService.getLaunchInfoList(verificationIds));
    }

    @GetMapping("/isHasOngoingRefundProcessMap")
    @Operation(hidden = true)
    public JsonObject<Map<String,Boolean>> isHasOngoingRefundProcessMap(@RequestParam List<String> verificationIds) {
        return new JsonObject<>(refundService.isHasOngoingRefundProcessMap(verificationIds));
    }

    @GetMapping("/isHasRefundProcessMap")
    @Operation(hidden = true)
    public JsonObject<Map<String,Boolean>> isHasRefundProcessMap(@RequestParam List<String> verificationIds) {
        return new JsonObject<>(refundService.isHasRefundProcessMap(verificationIds));
    }

}
