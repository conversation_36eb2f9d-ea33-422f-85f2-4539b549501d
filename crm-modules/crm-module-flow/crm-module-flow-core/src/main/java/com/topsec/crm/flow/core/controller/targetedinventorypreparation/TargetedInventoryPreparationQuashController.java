package com.topsec.crm.flow.core.controller.targetedinventorypreparation;

import com.topsec.crm.flow.api.dto.targetedinventorypreparation.TargetedInventoryPreparationProductOwnQuashDTO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.TargetedInventoryPreparationQuashFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.TargetedInventoryPreparationProductOwnQuashVO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.TargetedInventoryPreparationQuashBaseInfoVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.process.impl.TargetedInventoryPreparationQuashProcessService;
import com.topsec.crm.flow.core.service.ITargetedInventoryPreparationProductQuashService;
import com.topsec.crm.flow.core.service.ITargetedInventoryPreparationQuashService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 专项备货撤销流程主表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-12-16 15:42
 */
@RestController
@RequestMapping("/targeted-inventory-preparation-quash")
@RequiredArgsConstructor
@Validated
@Tag(name = "专项备货撤销", description = "/targetedInventoryPreparationQuash")
public class TargetedInventoryPreparationQuashController extends BaseController {
    private final TargetedInventoryPreparationQuashProcessService targetedInventoryPreparationQuashProcessService;
    private final ITargetedInventoryPreparationQuashService iTargetedInventoryPreparationQuashService;
    private final ITargetedInventoryPreparationProductQuashService iTargetedInventoryPreparationProductQuashService;

    @PostMapping("/launch")
    @Operation(summary = "发起专项备货撤销流程")
    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    public JsonObject<Boolean> launch(@Valid @RequestBody TargetedInventoryPreparationQuashFlowLaunchDTO launchDTO) {
        return new JsonObject<>(targetedInventoryPreparationQuashProcessService.launch(launchDTO));
    }
    @GetMapping("/QueryTIPInfoByTIPQuashMainId")
    @Operation(summary = "根据专项备货撤销流程主表ID查询专项备货撤销基础信息")
    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    public JsonObject<TargetedInventoryPreparationQuashBaseInfoVO> queryTIPQuashInfoByTIPQuashMainId(@RequestParam String targetedInventoryPreparationQuashId){
        CrmAssert.hasText(targetedInventoryPreparationQuashId, "专项备货撤销流程主表Id不能为空");
        return new JsonObject<>(iTargetedInventoryPreparationQuashService.queryTIPQuashInfoByTIPQuashMainId(targetedInventoryPreparationQuashId));
    }

    @PreFlowPermission
    @GetMapping("/QueryTIPInfoByProcessInstanceId")
    @Operation(summary = "(流程)根据专项备货撤销流程ID查询专项备货撤销基础信息")
    public JsonObject<TargetedInventoryPreparationQuashBaseInfoVO> queryTIPInfoByProcessInstanceId(@RequestParam String targetedInventoryPreparationQuashProcessInstanceId){
        CrmAssert.hasText(targetedInventoryPreparationQuashProcessInstanceId, "专项备货撤销流程Id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(targetedInventoryPreparationQuashProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationQuashService.queryTIPQuashInfoByProcessInstanceId(targetedInventoryPreparationQuashProcessInstanceId));
    }

    @GetMapping("/QueryTIPInfoBusinessByProcessInstanceId")
    @Operation(summary = "(业务)根据专项备货撤销流程ID查询专项备货撤销基础信息")
    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    public JsonObject<TargetedInventoryPreparationQuashBaseInfoVO> queryTIPInfoBusinessByProcessInstanceId(@RequestParam String targetedInventoryPreparationQuashProcessInstanceId){
        CrmAssert.hasText(targetedInventoryPreparationQuashProcessInstanceId, "专项备货撤销流程Id不能为空");
        return new JsonObject<>(iTargetedInventoryPreparationQuashService.queryTIPQuashInfoByProcessInstanceId(targetedInventoryPreparationQuashProcessInstanceId));
    }

    @GetMapping("/getTIPQuashProductByTIPQuashMainId")
    @Operation(summary = "根据撤销流程主表ID查询产品明细")
    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    public JsonObject<List<TargetedInventoryPreparationProductOwnQuashVO>> getTIPQuashProductPageByTIPQuashMainId(@RequestParam String targetedInventoryPreparationQuashMainId,@RequestParam String targetedInventoryPreparationProductNameORCode){
        CrmAssert.hasText(targetedInventoryPreparationQuashMainId, "专项备货撤销流程主表Id不能为空");
        return new JsonObject<>(iTargetedInventoryPreparationProductQuashService.QueryProductByTIPQuashMainId(targetedInventoryPreparationQuashMainId,targetedInventoryPreparationProductNameORCode));
    }
    @PreFlowPermission
    @GetMapping("/getTIPQuashProductByTIPQuashProcessInstanceId")
    @Operation(summary = "(流程)根据撤销流程ID查询产品明细")
    public JsonObject<List<TargetedInventoryPreparationProductOwnQuashVO>> getTIPQuashProductByTIPQuashProcessInstanceId(@RequestParam String targetedInventoryPreparationQuashProcessInstanceId,@RequestParam(required = false) String targetedInventoryPreparationProductNameORCode){
        CrmAssert.hasText(targetedInventoryPreparationQuashProcessInstanceId, "专项备货撤销流程Id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(targetedInventoryPreparationQuashProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationProductQuashService.queryProductByTIPQuashProcessInstanceId(targetedInventoryPreparationQuashProcessInstanceId,targetedInventoryPreparationProductNameORCode));
    }

    @PreFlowPermission
    @GetMapping("/getTIPQuashProductByTIPProcessInstanceId")
    @Operation(summary = "(流程)根据专项备货流程ID查询撤销产品明细")
    public JsonObject<List<TargetedInventoryPreparationProductOwnQuashVO>> getTIPQuashProductByTIPProcessInstanceId(@RequestParam String targetedInventoryPreparationProcessInstanceId){
        CrmAssert.hasText(targetedInventoryPreparationProcessInstanceId, "专项备货流程Id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(targetedInventoryPreparationProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationProductQuashService.queryQuashProductByTIPProcessInstanceId(targetedInventoryPreparationProcessInstanceId));
    }

    @GetMapping("/getTIPQuashProductBusinessByProcessInstanceId")
    @Operation(summary = "(业务)根据撤销流程ID查询产品明细")
    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    public JsonObject<List<TargetedInventoryPreparationProductOwnQuashVO>> getTIPQuashProductBusinessByTIPQuashId(@RequestParam String targetedInventoryPreparationQuashProcessInstanceId,@RequestParam(required = false) String targetedInventoryPreparationProductNameORCode){
        CrmAssert.hasText(targetedInventoryPreparationQuashProcessInstanceId, "专项备货撤销流程Id不能为空");
        return new JsonObject<>(iTargetedInventoryPreparationProductQuashService.queryProductByTIPQuashProcessInstanceId(targetedInventoryPreparationQuashProcessInstanceId,targetedInventoryPreparationProductNameORCode));
    }

    @PreFlowPermission
    @PostMapping("/updateTIPQuashProductInfo")
    @Operation(summary = "(流程)01修改专项备货撤销产品信息")
    public JsonObject<Boolean> updateTIPQuashProductInfo(@RequestBody List<TargetedInventoryPreparationProductOwnQuashDTO> targetedInventoryPreparationProductOwnQuashDTOList,String targetedInventoryPreparationQuashProcessInstanceId){
        CrmAssert.hasText(targetedInventoryPreparationQuashProcessInstanceId, "专项备货撤销流程Id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(targetedInventoryPreparationQuashProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iTargetedInventoryPreparationProductQuashService.updateProductInfoByTIPQuashProcessInstanceId(targetedInventoryPreparationProductOwnQuashDTOList,targetedInventoryPreparationQuashProcessInstanceId));
    }

    @PostMapping("/updateTIPQuashProductBusinessInfo")
    @Operation(summary = "(业务)01修改专项备货撤销产品信息")
    @PreAuthorize(hasPermission = "crm_flow_special_stock")
    public JsonObject<Boolean> updateTIPQuashProductBusinessInfo(@RequestBody List<TargetedInventoryPreparationProductOwnQuashDTO> targetedInventoryPreparationProductOwnQuashDTOList,String targetedInventoryPreparationQuashId){
        CrmAssert.hasText(targetedInventoryPreparationQuashId, "专项备货撤销流程主表Id不能为空");
        return new JsonObject<>(iTargetedInventoryPreparationProductQuashService.updateProductInfo(targetedInventoryPreparationProductOwnQuashDTOList,targetedInventoryPreparationQuashId));
    }
}
