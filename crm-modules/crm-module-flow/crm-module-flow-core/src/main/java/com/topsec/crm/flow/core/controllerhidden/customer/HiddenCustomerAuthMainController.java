package com.topsec.crm.flow.core.controllerhidden.customer;

import com.topsec.crm.flow.core.service.ICustomerAuthMainService;
import com.topsec.crm.framework.common.bean.StatsTimeSearchVO;
import com.topsec.crm.framework.common.enums.StatsTimeEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/hidden/customer/auth")
@RequiredArgsConstructor
@Validated
public class HiddenCustomerAuthMainController extends BaseController {
    @Autowired
    private ICustomerAuthMainService customerAuthMainService;

    /**
     * 查询客户新增认证通过数量
     */
    @GetMapping("/selectPassNum")
    public JsonObject<Long> selectPassNum(@RequestBody StatsTimeSearchVO searchVO) {
        Long count = customerAuthMainService.query()
                .eq("process_state", 2)
                .between("update_time", searchVO.getStart(), searchVO.getEnd())
                .count();

        return new JsonObject<>(count);
    }


}
