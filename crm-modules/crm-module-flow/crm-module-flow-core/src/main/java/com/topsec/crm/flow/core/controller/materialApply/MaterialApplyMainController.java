package com.topsec.crm.flow.core.controller.materialApply;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.api.RemoteSalesAgreementProductService;
import com.topsec.crm.flow.api.RemoteSalesAgreementService;
import com.topsec.crm.flow.api.dto.materialApply.ExistDisableProductVo;
import com.topsec.crm.flow.api.dto.materialApply.MaterialApplyFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.materialApply.MaterialApplyMainVo;
import com.topsec.crm.flow.api.dto.materialApply.MaterialApplyProductDTO;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementProductVo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementReviewMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.MaterialApplyMain;
import com.topsec.crm.flow.core.entity.MaterialApplyProduct;
import com.topsec.crm.flow.core.process.impl.MaterialApplyProcessService;
import com.topsec.crm.flow.core.service.IMaterialApplyMainService;
import com.topsec.crm.flow.core.service.IMaterialApplyProductService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.product.api.*;
import com.topsec.crm.product.api.entity.*;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectProductOwnClient;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.jwt.UserInfoHolder;

import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("materialApplyMain")
@Tag(name = "代码权限申请", description = "/materialApplyMain")
@RequiredArgsConstructor
public class MaterialApplyMainController extends BaseController {

    @Autowired
    private MaterialApplyProcessService materialApplyProcessService;
    @Autowired
    private IMaterialApplyMainService materialApplyMainService;
    @Autowired
    private IMaterialApplyProductService materialApplyProductService;
    @Autowired
    private TosEmployeeClient tosEmployeeClient;
    @Autowired
    private RemoteProductService remoteProductService;
    @Autowired
    private RemoteQuotationPhaseService remoteQuotationPhaseService;
    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    @Autowired
    private RemoteProductDistributionRelService remoteProductDistributionRelService;
    @Autowired
    private RemoteMaterialDisableService remoteMaterialDisableService;
    @Autowired
    private RemoteMaterialControlService remoteMaterialControlService;
    @Autowired
    private RemoteProjectProductOwnClient remoteProjectProductOwnClient;
    @Autowired
    private RemoteSalesAgreementProductService remoteSalesAgreementProductService;
    @Autowired
    private RemoteSalesAgreementService remoteSalesAgreementService;
    @Autowired
    private RemoteTqmProductService remoteTqmProductService;
    @Autowired
    private RemoteStockDisableCodeConfigService remoteStockDisableCodeConfigService;

    @PostMapping("/launch")
    @Operation(summary = "发起代码权限申请流程")
    @PreAuthorize(hasPermission = "crm_flow_product_authority")
    public JsonObject<Boolean> launch(@Valid @RequestBody MaterialApplyFlowLaunchDTO launchDTO){
        if(launchDTO.getSourceType().equals("1") || launchDTO.getSourceType().equals("3") || launchDTO.getSourceType().equals("4")){
            if (!remoteProjectDirectlyClient.hasRight(launchDTO.getProOrAgreementId(), UserInfoHolder.getCurrentPersonId()).getObjEntity()) {
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else if(launchDTO.getSourceType().equals("2")){
            //查询协议信息
            JsonObject<SalesAgreementReviewMainVo> jobj = remoteSalesAgreementService.querySalesAgreementInfo(launchDTO.getProOrAgreementId());
            if(jobj.isSuccess() && jobj.getObjEntity() != null){
                if(!jobj.getObjEntity().getAgreementManagerPersonId().equals(UserInfoHolder.getCurrentPersonId())){
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }

        //查询是否存在流程中的代码申请流程
//        MaterialApplyMain materialApplyMain = materialApplyMainService.getOne(new QueryWrapper<MaterialApplyMain>()
//                .eq("pro_or_agreement_id", launchDTO.getProOrAgreementId())
//                .orderByAsc("create_time")
//                .last("limit 1")
//        );
//        Assert.isFalse(materialApplyMain != null && materialApplyMain.getProcessState() == ApprovalStatusEnum.SPZ.getCode(),"当前项目/协议存在审批中的代码权限申请流程。");

        return new JsonObject<>(materialApplyProcessService.launch(launchDTO));
    }

    @PostMapping("/selectInfo")
    @Operation(summary = "查询代码权限申请详情")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<MaterialApplyMainVo> selectInfo(@RequestBody MaterialApplyMainVo materialApplyMainVo){
        PreFlowPermissionAspect.checkProcessInstanceId(materialApplyMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询流程信息
        MaterialApplyMain materialApplyMain = materialApplyMainService.getOne(new QueryWrapper<MaterialApplyMain>().eq("process_instance_id", materialApplyMainVo.getProcessInstanceId()));

        if(materialApplyMain != null){
            List<MaterialApplyProduct> products = materialApplyProductService.query()
                    .eq("material_apply_main_id",materialApplyMain.getId()).list();

            //2.查询产品信息
            List<String> productIds = products.stream().map(MaterialApplyProduct::getProductId).toList();
            JsonObject<List<CrmProductVo>> listJsonObject = remoteProductService.batchGetInfo(productIds);

            //3.查询报价单信息
            JsonObject<List<CrmQuotationPhaseVo>> phaseObj = remoteQuotationPhaseService.batchGetQuotation(productIds);

            //4.查询创建人信息
            JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(materialApplyMain.getCreateUser());
            if(byId.isSuccess()){
                EmployeeVO employeeVO = byId.getObjEntity();
                materialApplyMain.setApplyName(NameUtils.getNameByEmployeeVO(employeeVO));
                materialApplyMain.setDeptName(employeeVO.getDept() != null ? employeeVO.getDept().getName() : "");
                materialApplyMain.setMobile(employeeVO.getMobile());
                materialApplyMain.setEmail(employeeVO.getEmail());
            }

            if(listJsonObject.isSuccess() && listJsonObject.getObjEntity() != null){
                List<CrmProductVo> objEntity = listJsonObject.getObjEntity();

                for (MaterialApplyProduct product : products) {
                    CrmProductVo crmProductVo = objEntity.stream().filter(p -> product.getProductId().equals(p.getId())).findFirst().orElse(null);
                    if(crmProductVo != null){
                        product.setProductName(crmProductVo.getName());
                        product.setPN(crmProductVo.getPn());
                        product.setProfile(crmProductVo.getProfile());

                        //补充报价单信息
                        if (phaseObj != null) {
                            List<CrmQuotationPhaseVo> phases = phaseObj.getObjEntity();
                            List<CrmQuotationPhaseVo> qpvs = phases.stream().filter(pha -> product.getProductId().equals(pha.getProductId())).toList();
                            if(CollectionUtil.isNotEmpty(qpvs)){
                                List<String> list = qpvs.stream().map(q -> q.getPhaseName() + "(" + q.getPrice() + ")").toList();
                                product.setQpvs(list);
                            }
                        }

                        //补充 分销对应非分销代码 信息
                        JsonObject<CrmProductDistributionRelVo> distributionRel = remoteProductDistributionRelService.getDistributionRel(crmProductVo.getId());
                        if(distributionRel.isSuccess()){
                            product.setHasDistributionRel(distributionRel.getObjEntity() != null ? true : false);
                        }
                    }
                }
            }
            materialApplyMain.setProducts(products);

            //补充projectNo/协议编号展示字段
            if(materialApplyMain.getSourceType() == 1 || materialApplyMain.getSourceType() == 3 || materialApplyMain.getSourceType() == 4){
                //查询项目信息
                JsonObject<CrmProjectDirectlyVo> projectInfo = remoteProjectDirectlyClient.getProjectInfo(materialApplyMain.getProOrAgreementId());
                if(projectInfo.isSuccess() && projectInfo.getObjEntity() != null){
                    materialApplyMain.setProOrAgreementIdShow(projectInfo.getObjEntity().getProjectNo());
                }
            }else{
                //查询协议信息
                JsonObject<SalesAgreementReviewMainVo> salesAgreementReviewMainVoJsonObject = remoteSalesAgreementService.querySalesAgreementInfo(materialApplyMain.getProOrAgreementId());
                if(salesAgreementReviewMainVoJsonObject.isSuccess() && salesAgreementReviewMainVoJsonObject.getObjEntity() != null){
                    materialApplyMain.setProOrAgreementIdShow(salesAgreementReviewMainVoJsonObject.getObjEntity().getProcessNumber());
                }
            }
        }

        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(materialApplyMain, MaterialApplyMainVo.class));
    }

    //查询物料代码所在的流程单
    @PostMapping("/findInFlow")
    @Operation(summary = "查询物料代码所在的流程单")
    @PreAuthorize(hasAnyPermission = {"crm_flow_product_authority","crm_product_disabled_config","crm_product_control_config"})
    public JsonObject<PageUtils<MaterialApplyMainVo>> findInFlow(@RequestBody MaterialApplyMainVo materialApplyMainVo){
        startPage();
        //查询项目报备流程信息
        String sql = "select material_apply_main_id from material_apply_product where product_id = \"" + materialApplyMainVo.getProductId() + "\"";
        List<MaterialApplyMain> materialApplyMains = materialApplyMainService.query()
                .eq("apply_type", materialApplyMainVo.getApplyType())
                .inSql("id", sql)
                .list();

        for (MaterialApplyMain materialApplyMain : materialApplyMains) {
            JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(materialApplyMain.getCreateUser());
            if(byId.isSuccess()){
                EmployeeVO employeeVO = byId.getObjEntity();
                materialApplyMain.setApplyName(NameUtils.getNameByEmployeeVO(employeeVO));
                materialApplyMain.setDeptName(employeeVO.getDept() != null ? employeeVO.getDept().getName() : "");
            }

            //补充projectNo/协议编号展示字段
            if(materialApplyMain.getSourceType() == 1 || materialApplyMain.getSourceType() == 3 || materialApplyMain.getSourceType() == 4){
                //查询项目信息
                JsonObject<CrmProjectDirectlyVo> projectInfo = remoteProjectDirectlyClient.getProjectInfo(materialApplyMain.getProOrAgreementId());
                if(projectInfo.isSuccess() && projectInfo.getObjEntity() != null){
                    materialApplyMain.setProOrAgreementIdShow(projectInfo.getObjEntity().getProjectNo());
                }
            }else{
                //查询协议信息
                JsonObject<SalesAgreementReviewMainVo> salesAgreementReviewMainVoJsonObject = remoteSalesAgreementService.querySalesAgreementInfo(materialApplyMain.getProOrAgreementId());
                if(salesAgreementReviewMainVoJsonObject.isSuccess() && salesAgreementReviewMainVoJsonObject.getObjEntity() != null){
                    materialApplyMain.setProOrAgreementIdShow(salesAgreementReviewMainVoJsonObject.getObjEntity().getProcessNumber());
                }
            }
        }

        List<MaterialApplyMainVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(materialApplyMains, MaterialApplyMainVo.class);

        return new JsonObject<>(getDataTable(materialApplyMains,listVo));
    }

    //查询项目/协议中 是否存在 产品禁用代码 和 产品控制代码（排除掉流程审批通过且在生效时间之内的代码）
    @PostMapping("/existDisableProduct")
    @Operation(summary = "查询项目/协议中 是否存在 产品禁用代码 和 产品控制代码（排除掉流程审批通过且在生效时间之内的代码）")
    @PreAuthorize(hasAnyPermission = {"crm_flow_product_authority","crm_product_disabled_config","crm_product_control_config"})
    public JsonObject<ExistDisableProductVo> existDisableProduct(@RequestBody MaterialApplyMainVo materialApplyMainVo){
        ExistDisableProductVo result = new ExistDisableProductVo();

        List<String> proProductIds = new ArrayList<String>();
        //如果有自定义传入的产品ID列表，则优先按照自定义产品列表查询规则来
        if(CollectionUtil.isNotEmpty(materialApplyMainVo.getProductIds())){
            proProductIds = materialApplyMainVo.getProductIds();
        }else{
            if(materialApplyMainVo.getSourceType() == 1 || materialApplyMainVo.getSourceType() == 3 ||materialApplyMainVo.getSourceType() == 4){
                //来源于项目
                JsonObject<List<CrmProjectProductOwnVO>> listJsonObject = remoteProjectDirectlyClient.tiledProductOwnOfDirectlyProject(materialApplyMainVo.getProOrAgreementId());
                if(listJsonObject.isSuccess()){
                    List<CrmProjectProductOwnVO> products = listJsonObject.getObjEntity();
                    //1.项目中产品Id列表
                    proProductIds = products.stream().map(CrmProjectProductOwnVO::getProductId).toList();
                }
            }else if(materialApplyMainVo.getSourceType() == 2){
                JsonObject<List<SalesAgreementProductVo>> jObj = remoteSalesAgreementProductService.querySalesAgreementProductByAgreementId(materialApplyMainVo.getProOrAgreementId());
                if(jObj.isSuccess() && CollectionUtil.isNotEmpty(jObj.getObjEntity())){
                    List<SalesAgreementProductVo> products = jObj.getObjEntity();
                    proProductIds = products.stream().map(SalesAgreementProductVo::getProductId).toList();
                }
            }
        }

        if(CollectionUtil.isEmpty(proProductIds)){
            result.setDisable(Collections.EMPTY_LIST);
            result.setControl(Collections.EMPTY_LIST);
            return new JsonObject<>(result);
        }

        //2.查询已经申请解禁，并且在生效时间之内的产品ID列表
        List<String> okProductIds = materialApplyMainService.selectUnDisableProducts(HyperBeanUtils.copyProperties(materialApplyMainVo, MaterialApplyMain::new));

        if(materialApplyMainVo.getSourceType() != 4) {//不是专项备货场景
            //3.查询禁用代码列表中，除了okProductIds，存不存在其他生效时间内的禁用产品
            CrmMaterialDisableVo disableParams = new CrmMaterialDisableVo();
            disableParams.setProductIds(proProductIds);
            disableParams.setOkProductIds(okProductIds);
            disableParams.setPersonId(materialApplyMainVo.getPersonId());//流程发起人ID
            disableParams.setSourceType(materialApplyMainVo.getSourceType());
            disableParams.setProOrAgreementId(materialApplyMainVo.getProOrAgreementId());
            JsonObject<List<CrmMaterialDisableVo>> existDisableProducts = remoteMaterialDisableService.findExistDisableProducts(disableParams);
            result.setDisable(HyperBeanUtils.copyListPropertiesByJackson(existDisableProducts.getObjEntity(), com.topsec.crm.flow.api.vo.CrmMaterialDisableVo.class));

            //4.查询禁用代码列表中，除了okProductIds，存不存在其他生效时间内的禁用产品
            CrmMaterialControlVo controlParams = new CrmMaterialControlVo();
            controlParams.setProductIds(proProductIds);
            controlParams.setOkProductIds(okProductIds);
            controlParams.setPersonId(materialApplyMainVo.getPersonId());//流程发起人ID
            controlParams.setSourceType(materialApplyMainVo.getSourceType());
            controlParams.setProOrAgreementId(materialApplyMainVo.getProOrAgreementId());
            JsonObject<List<CrmMaterialDisableVo>> existControlProducts = remoteMaterialControlService.findExistControlProducts(controlParams);
            result.setControl(HyperBeanUtils.copyListPropertiesByJackson(existControlProducts.getObjEntity(), com.topsec.crm.flow.api.vo.CrmMaterialDisableVo.class));

            //5.查询产品最近一次审批通过的申请记录，不区分产品禁用和产品控制。
            if (CollectionUtil.isEmpty(existDisableProducts.getObjEntity()) && CollectionUtil.isEmpty(existControlProducts.getObjEntity())) {
                if (CollectionUtil.isNotEmpty(okProductIds)) {
                    List<MaterialApplyProduct> lastProductFlow = new ArrayList<MaterialApplyProduct>();
                    for (String okProductId : okProductIds) {
                        //查询已经申请解禁，的产品列表，所在的流程-所对应的产品申请信息
                        MaterialApplyProduct materialApplyProduct = materialApplyMainService.findProductInFlow(okProductId,materialApplyMainVo.getProOrAgreementId());
                        lastProductFlow.add(materialApplyProduct);
                    }
                    result.setLastProductFlow(HyperBeanUtils.copyListPropertiesByJackson(lastProductFlow, MaterialApplyProductDTO.class));
                }
            }
        }else{
            //TODO minggang 6.如果是专项备货，需要查询专项备货禁用的产品
            JsonObject<List<String>> productIdsByParams = remoteStockDisableCodeConfigService.getProductIdsByParams(proProductIds, okProductIds);
            if(productIdsByParams.isSuccess() && CollectionUtil.isNotEmpty(productIdsByParams.getObjEntity())){
                List<String> productIds = productIdsByParams.getObjEntity();

                //查询产品基本信息
                JsonObject<List<CrmProductVo>> listJsonObject = remoteProductService.batchGetInfo(productIds);
                if(listJsonObject.isSuccess()) {
                    List<CrmProductVo> productVos = listJsonObject.getObjEntity();

                    List<CrmMaterialDisableVo> disableVos = new ArrayList<CrmMaterialDisableVo>();
                    for (String productId : productIds) {
                        CrmProductVo productVo = productVos.stream().filter(e -> e.getId().equals(productId)).findFirst().orElse(null);

                        CrmMaterialDisableVo e = new CrmMaterialDisableVo();
                        e.setProductId(productId);
                        e.setMaterialCode(productVo != null ? productVo.getMaterialCode() : null);
                        e.setPn(productVo != null ? productVo.getPn() : null);
                        disableVos.add(e);
                    }
                    result.setDisable(HyperBeanUtils.copyListPropertiesByJackson(disableVos, com.topsec.crm.flow.api.vo.CrmMaterialDisableVo.class));
                }
            }
        }

        return new JsonObject<>(result);
    }

    //查询物料代码所在的流程单
    @GetMapping("/findTqmExist")
    @Operation(summary = "查询物料代码所在的流程单")
    @PreFlowPermission
    public JsonObject<Boolean> findTqmExist(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询流程信息
        MaterialApplyMain materialApplyMain = materialApplyMainService.getOne(new QueryWrapper<MaterialApplyMain>().eq("process_instance_id", processInstanceId));
        List<MaterialApplyProduct> products = materialApplyProductService.query()
                .eq("material_apply_main_id",materialApplyMain.getId()).list();

        if(CollectionUtil.isNotEmpty(products)){
            List<String> list = products.stream().map(MaterialApplyProduct::getMaterialCode).toList();
            JsonObject<Boolean> noExist = remoteTqmProductService.findTqmExist(list);
            return noExist;
        }
        return new JsonObject<>(false);
    }

    //查询项目/协议中 禁用或者控制的物料代码，返回给前端展示
    @PostMapping("/selectAllDisableOrContractPro")
    @Operation(summary = "查询项目/协议中 禁用或者控制的物料代码，返回给前端展示")
    @PreAuthorize(hasPermission = "crm_flow_product_authority")
    public JsonObject<List<CrmProjectProductOwnVO>> selectAllDisableOrContractPro(@RequestBody MaterialApplyMainVo materialApplyMainVo){
        //权限控制
        if(materialApplyMainVo.getSourceType()==1 || materialApplyMainVo.getSourceType()==3 || materialApplyMainVo.getSourceType()==4){
            if (!remoteProjectDirectlyClient.hasRight(materialApplyMainVo.getProOrAgreementId(), UserInfoHolder.getCurrentPersonId()).getObjEntity()) {
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else if(materialApplyMainVo.getSourceType()==2){
            //查询协议信息
            JsonObject<SalesAgreementReviewMainVo> jobj = remoteSalesAgreementService.querySalesAgreementInfo(materialApplyMainVo.getProOrAgreementId());
            if(jobj.isSuccess() && jobj.getObjEntity() != null){
                if(!jobj.getObjEntity().getAgreementManagerPersonId().equals(UserInfoHolder.getCurrentPersonId())){
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }

        List<CrmProjectProductOwnVO> products = new ArrayList<CrmProjectProductOwnVO>();
        if(materialApplyMainVo.getSourceType() == 1 || materialApplyMainVo.getSourceType() == 3 ||materialApplyMainVo.getSourceType() == 4){
            //来源于项目
            JsonObject<List<CrmProjectProductOwnVO>> listJsonObject = remoteProjectDirectlyClient.tiledProductOwnOfDirectlyProject(materialApplyMainVo.getProOrAgreementId());
            if(listJsonObject.isSuccess()){
                products = listJsonObject.getObjEntity();
            }
        }else if(materialApplyMainVo.getSourceType() == 2){
            JsonObject<List<SalesAgreementProductVo>> jObj = remoteSalesAgreementProductService.querySalesAgreementProductByAgreementId(materialApplyMainVo.getProOrAgreementId());
            if(jObj.isSuccess() && CollectionUtil.isNotEmpty(jObj.getObjEntity())){
                products = HyperBeanUtils.copyListPropertiesByJackson(jObj.getObjEntity(),CrmProjectProductOwnVO.class);
            }
        }

        List<CrmProjectProductOwnVO> result = new ArrayList<CrmProjectProductOwnVO>();
        if(CollectionUtil.isNotEmpty(products)){
            //产品ID列表
            List<String> proProductIds = products.stream().map(CrmProjectProductOwnVO::getProductId).toList();
            //2.查询已经申请解禁，并且在生效时间之内的产品ID列表（区分了sourceType）
            List<String> okProductIds = materialApplyMainService.selectUnDisableProducts(HyperBeanUtils.copyProperties(materialApplyMainVo, MaterialApplyMain::new));
            if(materialApplyMainVo.getSourceType() == 1 || materialApplyMainVo.getSourceType() == 3){
                //项目下单和产品借试用
                if(materialApplyMainVo.getApplyType() == 1){
                    //代码禁用
                    //3.查询禁用代码列表中，除了okProductIds，存不存在其他生效时间内的禁用产品
                    CrmMaterialDisableVo disableParams = new CrmMaterialDisableVo();
                    disableParams.setProductIds(proProductIds);
                    disableParams.setOkProductIds(okProductIds);
                    disableParams.setPersonId(UserInfoHolder.getCurrentPersonId());//流程发起人ID
                    disableParams.setSourceType(materialApplyMainVo.getSourceType());
                    disableParams.setProOrAgreementId(materialApplyMainVo.getProOrAgreementId());
                    JsonObject<List<CrmMaterialDisableVo>> existDisableProducts = remoteMaterialDisableService.findExistDisableProducts(disableParams);
                    if (existDisableProducts.isSuccess()) {
                        List<CrmMaterialDisableVo> list = existDisableProducts.getObjEntity().stream().filter(e -> e.getFlowControl() == 1).toList();
                        for (CrmMaterialDisableVo disableVo : list) {
                            CrmProjectProductOwnVO crmProjectProductOwnVO = products.stream().filter(e -> e.getProductId().equals(disableVo.getProductId())).findFirst().orElse(null);
                            result.add(crmProjectProductOwnVO);
                        }
                    }
                }else if(materialApplyMainVo.getApplyType() == 2){
                    //代码控制
                    //4.查询禁用代码列表中，除了okProductIds，存不存在其他生效时间内的禁用产品
                    CrmMaterialControlVo controlParams = new CrmMaterialControlVo();
                    controlParams.setProductIds(proProductIds);
                    controlParams.setOkProductIds(okProductIds);
                    controlParams.setPersonId(UserInfoHolder.getCurrentPersonId());//流程发起人ID
                    controlParams.setSourceType(materialApplyMainVo.getSourceType());
                    controlParams.setProOrAgreementId(materialApplyMainVo.getProOrAgreementId());
                    JsonObject<List<CrmMaterialDisableVo>> existControlProducts = remoteMaterialControlService.findExistControlProducts(controlParams);
                    if (existControlProducts.isSuccess()) {
                        List<CrmMaterialDisableVo> list = existControlProducts.getObjEntity().stream().filter(e -> e.getFlowControl() == 1).toList();
                        for (CrmMaterialDisableVo disableVo : list) {
                            CrmProjectProductOwnVO crmProjectProductOwnVO = products.stream().filter(e -> e.getProductId().equals(disableVo.getProductId())).findFirst().orElse(null);
                            result.add(crmProjectProductOwnVO);
                        }
                    }
                }
            }else if(materialApplyMainVo.getSourceType() == 2){
                //销售协议
                //3.查询禁用代码列表中，除了okProductIds，存不存在其他生效时间内的禁用产品
                CrmMaterialDisableVo disableParams = new CrmMaterialDisableVo();
                disableParams.setProductIds(proProductIds);
                disableParams.setOkProductIds(okProductIds);
                disableParams.setPersonId(UserInfoHolder.getCurrentPersonId());//流程发起人ID
                disableParams.setSourceType(materialApplyMainVo.getSourceType());
                disableParams.setProOrAgreementId(materialApplyMainVo.getProOrAgreementId());
                disableParams.setIndustryOne(materialApplyMainVo.getIndustryOne());
                disableParams.setIndustryTwo(materialApplyMainVo.getIndustryTwo());
                JsonObject<List<CrmMaterialDisableVo>> existDisableProducts = remoteMaterialDisableService.findExistDisableProducts(disableParams);
                if (existDisableProducts.isSuccess()) {
                    List<CrmMaterialDisableVo> list = existDisableProducts.getObjEntity().stream().filter(e -> e.getFlowControl() == 1).toList();
                    for (CrmMaterialDisableVo disableVo : list) {
                        CrmProjectProductOwnVO crmProjectProductOwnVO = products.stream().filter(e -> e.getProductId().equals(disableVo.getProductId())).findFirst().orElse(null);
                        result.add(crmProjectProductOwnVO);
                    }
                }
            }else if(materialApplyMainVo.getSourceType() == 4) {
                //专项备货
                JsonObject<List<String>> productIdsByParams = remoteStockDisableCodeConfigService.getProductIdsByParams(proProductIds, okProductIds);
                if (productIdsByParams.isSuccess() && CollectionUtil.isNotEmpty(productIdsByParams.getObjEntity())) {
                    List<String> productIds = productIdsByParams.getObjEntity();

                    //查询产品基本信息
                    JsonObject<List<CrmProductVo>> listJsonObject = remoteProductService.batchGetInfo(productIds);
                    if (listJsonObject.isSuccess()) {
                        List<CrmProductVo> productVos = listJsonObject.getObjEntity();

                        for (String productId : productIds) {
                            CrmProductVo productVo = productVos.stream().filter(e -> e.getId().equals(productId)).findFirst().orElse(null);

                            CrmProjectProductOwnVO e = new CrmProjectProductOwnVO();
                            e.setProductId(productId);
                            e.setStuffCode(productVo != null ? productVo.getMaterialCode() : null);
                            e.setProductName(productVo != null ? productVo.getName() : null);
                            e.setPnCode(productVo != null ? productVo.getPn() : null);
                            e.setProductCategory(productVo != null ? productVo.getSpecificationId() : null);
                            result.add(e);
                        }
                    }
                }
            }
        }

        //相同记录去重
        result = result.stream().distinct().collect(Collectors.toList());
        List<String> productIds = result.stream().map(CrmProjectProductOwnVO::getProductId).toList();
        if(CollectionUtil.isNotEmpty(productIds)){
            JsonObject<List<CrmQuotationPhaseVo>> phases = remoteQuotationPhaseService.batchGetQuotation(productIds);
            JsonObject<List<CrmProductVo>> listJsonObject = remoteProductService.batchGetInfo(productIds);

            if(phases.isSuccess() && listJsonObject.isSuccess()){
                for (CrmProjectProductOwnVO crmProjectProductOwnVO : result) {
                    //1.补充报价单信息
                    List<String> list = phases.getObjEntity().stream().map(q -> q.getPhaseName() + "(" + q.getPrice() + ")").toList();
                    crmProjectProductOwnVO.setQpvs(list);

                    //2.补充分销对应非分销代码信息
                    JsonObject<CrmProductDistributionRelVo> distributionRel = remoteProductDistributionRelService.getDistributionRel(crmProjectProductOwnVO.getProductId());
                    if(distributionRel.isSuccess() && distributionRel.getObjEntity() != null){
                        crmProjectProductOwnVO.setHasDistributionRel(distributionRel.getObjEntity() != null ? true : false);
                    }

                    //3.补充产品描述
                    CrmProductVo productVo = listJsonObject.getObjEntity().stream().filter(e -> e.getId().equals(crmProjectProductOwnVO.getProductId())).findFirst().orElse(null);
                    crmProjectProductOwnVO.setProfile(productVo != null ? productVo.getProfile() : null);
                }
            }
        }

        return new JsonObject<>(result);
    }
}
