package com.topsec.crm.flow.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewMainInfo;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductThirdDTO;
import com.topsec.crm.flow.api.dto.pricereview.ProductPriceState;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.PriceReviewMainMapper;
import com.topsec.crm.flow.core.mapper.PriceReviewSaleAgreementRelMapper;
import com.topsec.crm.flow.core.mapper.ProcessExtensionInfoMapper;
import com.topsec.crm.flow.core.mapstruct.PriceReviewMainConvertor;
import com.topsec.crm.flow.core.service.PriceReviewFlowService;
import com.topsec.crm.flow.core.service.PriceReviewMainService;
import com.topsec.crm.flow.core.validator.CheckCondition;
import com.topsec.crm.flow.core.validator.HyperValidator;
import com.topsec.crm.flow.core.validator.pricereview.PriceReviewCheckContext;
import com.topsec.crm.flow.core.validator.pricereview.condition.*;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.enums.ProcessDefinitionKeyEnum;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.enums.RelationTypeEnum;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.tos.common.vo.TosDepartmentVO;
import com.topsec.tos.common.vo.process.DetailBaseVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 */
@Service
@RequiredArgsConstructor
public class PriceReviewMainServiceImpl extends ServiceImpl<PriceReviewMainMapper, PriceReviewMain>
implements PriceReviewMainService {

    private final PriceReviewFlowService priceReviewFlowService;
    private final ProcessExtensionInfoMapper processExtensionInfoMapper;
    private final TosEmployeeClient tosEmployeeClient;
    private final PriceReviewSaleAgreementRelMapper priceReviewSaleAgreementRelMapper;
    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final PriceReviewMainMapper priceReviewMainMapper;
    // 价格评审失效条件
    private final List<CheckCondition<PriceReviewCheckContext>> priceReviewCheckStrategyList= Lists.newArrayList(
            new OwnQuantityChangeCondition(),
            new ProjectProcureTypeChangeCondition(),
            new OwnDiscountChangeCondition(),
            new ThirdTaxChangeCondition(),
            new OutsourcingServiceFeeChangeCondition(),
            new SigningAgentChangeCondition(),
            new OwnPriceChangeCondition(),
            new OwnLicNumChangeCondition(),
            new SigningTypeChangeCondition(),
            new OwnWarrantyPeriodChangeCondition(),
            new OwnNonStandardDiscountExpireCondition());

    @Override
    public CrmProjectDirectlyVo queryProjectInfo(String processInstanceId){
        ProcessExtensionInfo processExtensionInfo = processExtensionInfoMapper.selectOne(new LambdaQueryWrapper<ProcessExtensionInfo>().eq(ProcessExtensionInfo::getProcessInstanceId, processInstanceId));
        Integer processState = processExtensionInfo.getProcessState();
        if (ApprovalStatusEnum.YSP.getCode().equals(processState)){
            PriceReviewMain priceReviewMain = priceReviewMainMapper.selectOne(new LambdaQueryWrapper<PriceReviewMain>().eq(PriceReviewMain::getProcessInstanceId, processInstanceId));
            return priceReviewMain.getProjectInfo();
        }else {
            return Optional.ofNullable(remoteProjectDirectlyClient.getProjectInfo(processInstanceId))
                    .map(JsonObject::getObjEntity)
                    .orElse(null);
        }
    }

    @Override
    public PriceReviewMain queryLatestPriceReviewMainByProjectId(String projectId) {
        return getOne(new QueryWrapper<PriceReviewMain>().eq("project_id", projectId).orderByDesc("create_time").last("limit 1"));
    }
    @Override
    public PriceReviewMainInfo queryLatestPriceReviewMainInfoByProjectId(String projectId) {

        ProcessExtensionInfo processExtensionInfo = processExtensionInfoMapper.selectLastProjectPriceReviewProcessInfo(projectId);
        if (processExtensionInfo==null) return null;
        String processInstanceId = processExtensionInfo.getProcessInstanceId();

        PriceReviewMain priceReviewMain = baseMapper.queryByProcessInstanceId(processInstanceId);

        PriceReviewMainInfo priceReviewMainInfo = queryPriceReviewMainInfo(priceReviewMain, processExtensionInfo);
        if (priceReviewMainInfo!=null){
            Optional.of(priceReviewMainInfo).map(PriceReviewMainInfo::getCreateUser).flatMap(createUser -> Optional.ofNullable(tosEmployeeClient.findVisibleInfoById(createUser))
                    .map(JsonObject::getObjEntity)).ifPresent(priceReviewMainInfo::setCreateEmployee);
        }
        return priceReviewMainInfo;

    }

    @Override
    public PriceReviewMainInfo baseInfo(String processInstanceId) {
        PriceReviewMain priceReviewMain = baseMapper.queryByProcessInstanceId(processInstanceId);
        if (priceReviewMain==null) return null;
        ProcessExtensionInfo lastPassedProjectPriceReviewProcessInfo = processExtensionInfoMapper.
                selectLastPassedProjectPriceReviewProcessInfo(priceReviewMain.getProjectId());

        return queryPriceReviewMainInfo(priceReviewMain,lastPassedProjectPriceReviewProcessInfo);
    }


    @Override
    public PriceReviewMainInfo queryPriceReviewMainInfo(PriceReviewMain priceReviewMain,
                                                        ProcessExtensionInfo lastPassedProjectPriceReviewProcessInfo) {
        if (priceReviewMain==null) return null;
        String projectId = priceReviewMain.getProjectId();
        PriceReviewMainInfo priceReviewMainInfo = PriceReviewMainConvertor.INSTANCE.toInfo(priceReviewMain);

        String processInstanceId = priceReviewMain.getProcessInstanceId();
        Integer processState = priceReviewMain.getProcessState();
        //若流程没有通过 ，则为未生效
        if (!ApprovalStatusEnum.YSP.getCode().equals(processState)){
            priceReviewMainInfo.setState(0);
            priceReviewMainInfo.setPriceEffectiveDate(null);
            return priceReviewMainInfo;
        }

        if (Objects.equals(processInstanceId,lastPassedProjectPriceReviewProcessInfo.getProcessInstanceId())){
            priceReviewMainInfo.setState(1);
            priceReviewMainInfo.setPriceEffectiveDate(lastPassedProjectPriceReviewProcessInfo.getStateTime().toLocalDate());
            Integer priceExpiryDays = Optional.ofNullable(priceReviewMain.getPriceExpiryDaysOfApproval()).orElse(priceReviewMain.getPriceExpiryDays());
            if (priceExpiryDays!=null){
                priceReviewMainInfo.setPriceExpiryDate(lastPassedProjectPriceReviewProcessInfo.getStateTime()
                        .plusDays(priceExpiryDays).toLocalDate());
            }

            //该流程通过 且 与最近一条通过流程实例id 相同,然后判断
            boolean theLatestPassedPriceReviewValid = isTheLatestPassedPriceReviewValid(projectId);
            priceReviewMainInfo.setState(theLatestPassedPriceReviewValid?1:2);
        } else {
            //该流程通过 且 与最近一条通过流程实例id 不相同,则为已失效
            priceReviewMainInfo.setState(2);
            priceReviewMainInfo.setPriceEffectiveDate(null);
        }
        return priceReviewMainInfo;
    }

    @Override
    public Map<String, ProductPriceState> queryPriceState( String projectId,  Set<String> productRecordIdSet) {

        ProjectPriceReviewDetail projectSnapshot = priceReviewFlowService.queryLatestPassedSnapshotByProjectId(projectId);
        if (projectSnapshot == null) {
            return productRecordIdSet.stream().map(id -> {
                ProductPriceState priceStateInfo = new ProductPriceState();
                priceStateInfo.setState(0);
                priceStateInfo.setRecordId(id);
                return priceStateInfo;
            }).collect(Collectors.toMap(ProductPriceState::getRecordId, v -> v));
        }

        ProjectDetail projectDetail = priceReviewFlowService.queryProjectDetail(projectId);

        PriceReviewCheckContext priceReviewCheckContext = new PriceReviewCheckContext(projectDetail, projectSnapshot);

        HyperValidator<PriceReviewCheckContext> invalidator = HyperValidator.check(priceReviewCheckContext, priceReviewCheckStrategyList);



        Set<String> ownIdSet = ListUtils.emptyIfNull(priceReviewCheckContext.getProjectSnapshot().getProductOwnList())
                .stream().map(PriceReviewProductOwnDTO::getRecordId).collect(Collectors.toSet());
        Set<String> thirdIdSet = ListUtils.emptyIfNull(priceReviewCheckContext.getProjectSnapshot().getProductThirdList())
                .stream().map(PriceReviewProductThirdDTO::getRecordId).collect(Collectors.toSet());
        Set<String> outIdSet = ListUtils.emptyIfNull(priceReviewCheckContext.getProjectSnapshot().getProductServiceOutsourcingList())
                .stream().map(PriceReviewProductServiceOutsourcing::getRecordId).collect(Collectors.toSet());
        Set<String> inThisProcess = Stream.of(ownIdSet, thirdIdSet, outIdSet).flatMap(Collection::stream).collect(Collectors.toSet());



        Map<String, ProductPriceState> failMap = invalidator.failPostProcess((context) -> {
            return productRecordIdSet.stream().map(recordId -> {
                ProductPriceState priceStateInfo = new ProductPriceState();
                priceStateInfo.setRecordId(recordId);
                priceStateInfo.setFailureReason(invalidator.getFailMessage());
                priceStateInfo.setProcessInstanceId(context.getProjectSnapshot().getPriceReviewMain().getProcessInstanceId());
                // 在此流程中的则为失效，否则为未生效
                if (inThisProcess.contains(recordId)) {
                    priceStateInfo.setState(2);
                } else {
                    priceStateInfo.setState(0);
                }
                return priceStateInfo;
            }).collect(Collectors.toMap(ProductPriceState::getRecordId, v -> v));

        });


        //若为生效 则传入的productRecordIdSet全部为生效
        Map<String, ProductPriceState> successMap = invalidator.successPostProcess((context) -> {
            return productRecordIdSet.stream().map(recordId -> {
                ProductPriceState priceStateInfo = new ProductPriceState();
                priceStateInfo.setRecordId(recordId);
                priceStateInfo.setFailureReason(invalidator.getFailMessage());
                priceStateInfo.setState(1);
                priceStateInfo.setProcessInstanceId(context.getProjectSnapshot().getPriceReviewMain().getProcessInstanceId());
                return priceStateInfo;
            }).collect(Collectors.toMap(ProductPriceState::getRecordId, v -> v));
        });
        Map<String, ProductPriceState> resultMap = ObjectUtils.firstNonNull(failMap, successMap);


        //给失效时间赋值
        PriceReviewMain priceReviewMain = projectSnapshot.getPriceReviewMain();
        Integer priceExpiryDays = Optional.ofNullable(priceReviewMain.getPriceExpiryDaysOfApproval()).orElse(priceReviewMain.getPriceExpiryDays());
        LocalDateTime priceExpiryTime=null;
        if (priceExpiryDays!=null){
            LocalDateTime approvalPassTime = projectSnapshot.getProcessExtensionInfo().getStateTime();
            priceExpiryTime = approvalPassTime.plusDays(priceExpiryDays);
        }
        //判断是否所有产品都是标准折扣，全标准折扣不展示失效时间
        if (priceExpiryTime!=null){
            boolean allStandardDiscountProduct = priceReviewFlowService
                    .allStandardDiscountProduct(priceReviewMain.getProcessInstanceId());
            if (allStandardDiscountProduct){
                priceExpiryTime=null;
            }
        }
        Collection<ProductPriceState> priceStates = CollectionUtils.emptyIfNull(resultMap.values()) ;
        for (ProductPriceState priceState : priceStates) {
            String recordId = priceState.getRecordId();
            boolean result = invalidator.getResult();
            if (result){
                priceState.setPriceExpiryTime(priceExpiryTime);
            }else {
                if (inThisProcess.contains(recordId)) {
                    priceState.setPriceExpiryTime(priceExpiryTime);
                }
            }
        }

        return resultMap;

    }


    @Override
    public Set<String> querySaleAgreementOfLatestPassedPriceReview(String projectId) {
        ProcessExtensionInfo processExtensionInfo = processExtensionInfoMapper.selectLastPassedProjectPriceReviewProcessInfo(projectId);
        if (processExtensionInfo==null) return Collections.emptySet();
        String processInstanceId = processExtensionInfo.getProcessInstanceId();
        return priceReviewSaleAgreementRelMapper.selectList(new LambdaQueryWrapper<PriceReviewSaleAgreementRel>()
                        .eq(PriceReviewSaleAgreementRel::getPriceReviewProcessInstanceId, processInstanceId))
                .stream().map(PriceReviewSaleAgreementRel::getSaleAgreementId).collect(Collectors.toSet());
    }

    @Override
    public PriceReviewMainInfo queryLatestPassedPriceReviewMainByProjectId(String projectId) {
        ProcessExtensionInfo processExtensionInfo = processExtensionInfoMapper.selectLastPassedProjectPriceReviewProcessInfo(projectId);
        if (processExtensionInfo==null) return null;
        String processInstanceId = processExtensionInfo.getProcessInstanceId();

        PriceReviewMain priceReviewMain = baseMapper.queryByProcessInstanceId(processInstanceId);

        PriceReviewMainInfo priceReviewMainInfo = queryPriceReviewMainInfo(priceReviewMain, processExtensionInfo);
        if (priceReviewMainInfo!=null){
            Optional.of(priceReviewMainInfo).map(PriceReviewMainInfo::getCreateUser).flatMap(createUser -> Optional.ofNullable(tosEmployeeClient.findVisibleInfoById(createUser))
                    .map(JsonObject::getObjEntity)).ifPresent(priceReviewMainInfo::setCreateEmployee);
        }
        return priceReviewMainInfo;
    }

    @Override
    public boolean isTheLatestPassedPriceReviewValid(String projectId) {
        ProjectPriceReviewDetail projectSnapshot = priceReviewFlowService.queryLatestPassedSnapshotByProjectId(projectId);
        if (projectSnapshot==null)
            return false;

        ProjectDetail projectDetail = priceReviewFlowService.queryProjectDetail(projectId);
        PriceReviewCheckContext priceReviewCheckContext = new PriceReviewCheckContext(projectDetail, projectSnapshot);
        HyperValidator<PriceReviewCheckContext> invalidator = HyperValidator.check(priceReviewCheckContext, priceReviewCheckStrategyList);
        return invalidator.getResult();
    }


    @Override
    public boolean hasLaunchedPriceReview(String projectId) {
        return processExtensionInfoMapper.exists(new QueryWrapper<ProcessExtensionInfo>()
                .eq("process_definition_key", ProcessDefinitionKeyEnum.PRICE_APPROVAL_KEY.getValue())
                .eq("project_id", projectId)
        );
    }


    @Override
    public List<PriceReviewMainInfo> queryHistoryPassedPriceReview(String processInstanceId) {
        PriceReviewMain priceReviewMain = getOne(new LambdaQueryWrapper<PriceReviewMain>()
                .eq(PriceReviewMain::getProcessInstanceId, processInstanceId));
        if (priceReviewMain==null) return null;
        String finalCustomerId = priceReviewMain.getFinalCustomerId();
        List<PriceReviewMain> list = list(new LambdaQueryWrapper<PriceReviewMain>()
                .eq(PriceReviewMain::getFinalCustomerId, finalCustomerId)
                // .eq(PriceReviewMain::getProjectId, projectId)
                // .in(FlowBaseEntity::getProcessState, ApprovalStatusEnum.YSP.getCode(),ApprovalStatusEnum.SPZ.getCode())
                .orderByDesc(BaseEntity::getCreateTime)
                .last("limit 5")
        );
        Map<String, String> deptNameMap = Optional.ofNullable(tosEmployeeClient.findByIds(list.stream().map(BaseEntity::getCreateUser).distinct().collect(Collectors.toList()),
                        List.of(RelationTypeEnum.MEMBER)))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(EmployeeVO::getUuid, employeeVO -> {
                    return Optional.of(employeeVO).map(DetailBaseVO::getDept).map(TosDepartmentVO::getName).orElse("");
                }));
        Map<String, List<PriceReviewMain>> mapByProject = list.stream().collect(Collectors.groupingBy(PriceReviewMain::getProjectId));
        List<PriceReviewMainInfo> resultList = mapByProject.entrySet().stream().flatMap(stringListEntry -> {
            List<PriceReviewMain> reviewMainList = stringListEntry.getValue();
            String projectId = stringListEntry.getKey();
            ProcessExtensionInfo lastPassedProjectPriceReviewProcessInfo = processExtensionInfoMapper.
                    selectLastPassedProjectPriceReviewProcessInfo(projectId);
            List<PriceReviewMainInfo> infos = reviewMainList.stream().map(pm -> {
                PriceReviewMainInfo info = queryPriceReviewMainInfo(pm, lastPassedProjectPriceReviewProcessInfo);
                String createUser = info.getCreateUser();
                String deptName = deptNameMap.get(createUser);
                info.setCreateDept(deptName);
                return info;
            }).toList();
            return infos.stream();
        }).collect(Collectors.toList());
        NameUtils.setName(resultList);
        return resultList.stream().sorted(Comparator.comparing(PriceReviewMainInfo::getCreateTime)).toList();
    }

    @Override
    public List<CrmProjectDirectlyVo> querySaleAgreementRelatedProjects(String saleAgreementId) {
        if (StringUtils.isBlank(saleAgreementId)) return Collections.emptyList();
        Set<String> projectIds = priceReviewSaleAgreementRelMapper
                                         .querySaleAgreementRelatedProjectId(saleAgreementId);
        List<String> validProjectIds = projectIds.stream()
                .parallel()
                .filter(this::isTheLatestPassedPriceReviewValid)
                .toList();
        if(!validProjectIds.isEmpty()){
            return Optional.ofNullable(remoteProjectDirectlyClient.listByIds(validProjectIds))
                    .map(JsonObject::getObjEntity)
                    .orElse(Collections.emptyList());
        }
        return Collections.emptyList();
    }
}
