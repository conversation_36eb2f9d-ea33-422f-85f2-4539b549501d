package com.topsec.crm.flow.core.controller.agentPrepaymentUsage;

import com.topsec.crm.flow.api.dto.agentPrepaymentUsage.AgentPrepaymentUsageDTO;
import com.topsec.crm.flow.api.dto.agentPrepaymentUsage.AgentPrepaymentUsageFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.process.impl.AgentPrepaymentUsageProcesservice;
import com.topsec.crm.flow.core.service.AgentPrepaymentUsageService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbsapi.client.TbsCrmClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.AccountAgentRelVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

@RestController
@RequestMapping("/agentPrepaymentUsage")
@Tag(name = "预付款使用")
@Validated
public class AgentPrepaymentUsageController {
    @Resource
    private TbsCrmClient tbsCrmClient;
    @Autowired
    private AgentPrepaymentUsageProcesservice prepaymentUsageProcesservice;
    @Autowired
    private AgentPrepaymentUsageService agentPrepaymentUsageService;

    @PostMapping("/launch")
    @Operation(summary = "预付款使用-发起")
    @PreAuthorize(hasPermission = "crm_agent_repayment_use_add",dataScope = "crm_agent_repayment_use_add")
    public JsonObject<Boolean> launch(@Valid @RequestBody AgentPrepaymentUsageFlowLaunchDTO launchDTO){
        String recipientMainAccountId = getRecipientMainAccountId(launchDTO.getRecipientCompanyId());
        launchDTO.setAssigneeList(Collections.singleton(recipientMainAccountId));
        return new JsonObject<>(prepaymentUsageProcesservice.launch(launchDTO));
    }

    @GetMapping("/paymentDetail")
    @Operation(summary = "预付款使用-详情")
    @PreFlowPermission
    public JsonObject<AgentPrepaymentUsageDTO> paymentDetail() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(agentPrepaymentUsageService.prepaymentUsageDetail(processInstanceId));
    }

    private String getRecipientMainAccountId(String recipientCompanyId) {
        return Optional.ofNullable(tbsCrmClient.listAccountAgentRelVOByAgentId(recipientCompanyId))
                .map(JsonObject::getObjEntity)
                .orElse(Collections.emptyList())
                .stream()

                .filter(agentRelVO -> {
                    return Objects.equals(agentRelVO.getAccountType(),0);
                })
                .findFirst()
                .map(AccountAgentRelVO::getAccountId)
                .orElseThrow(() -> new RuntimeException("收款方不存在主账号"));
    }
}
