package com.topsec.crm.flow.core.controller.biddingMonitoring;

import com.alibaba.fastjson.JSON;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.dto.biddingMonitoring.BiddingMonitoringFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.biddingMonitoring.VO.BiddingMonitoringFlowDetailVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BiddingMonitoringFlow;
import com.topsec.crm.flow.core.entity.BiddingMonitoringInfo;
import com.topsec.crm.flow.core.process.impl.BiddingMonitoringProcessService;
import com.topsec.crm.flow.core.service.BiddingMonitoringFlowService;
import com.topsec.crm.flow.core.service.BiddingMonitoringInfoService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.date.DateUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.operation.api.RemoteCompetitorService;
import com.topsec.crm.operation.api.entity.CompetitorVO;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-12-23  11:41
 * @Description:
 */

@RestController
@RequestMapping("/biddingMonitoringFlow")
@Tag(name = "商机审批-招投标信息监测", description = "/biddingMonitoringFlow")
@RequiredArgsConstructor
@Validated
public class BiddingMonitoringFlowController extends BaseController {

    private final BiddingMonitoringFlowService biddingMonitoringFlowService;

    private final BiddingMonitoringProcessService biddingMonitoringProcessService;

    private final BiddingMonitoringInfoService biddingMonitoringInfoService;

    private final RemoteCustomerService remoteCustomerService;


    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    private final RemoteCompetitorService remoteCompetitorService;


    @PreAuthorize(hasPermission = "crm_bidding_monitoring_update",dataScope = "crm_bidding_monitoring_update")
    @PostMapping("/launch")
    @Operation(summary = "商机审批-招投标信息监测")
    public JsonObject<Boolean> launch(@Valid @RequestBody BiddingMonitoringFlowLaunchDTO launchDTO) {
        return new JsonObject<>(biddingMonitoringProcessService.launch(launchDTO));
    }



    @PreAuthorize(hasPermission = "crm_bidding_monitoring_update",dataScope = "crm_bidding_monitoring_update")
    //@PreFlowPermission
    @PostMapping("/launchBatch")
    @Operation(summary = "商机审批-招投标信息监测批量发起")
    public JsonObject<Boolean> launchBatch(@Valid @RequestBody List<BiddingMonitoringFlowLaunchDTO> launchDTOS) {
       try {
           launchDTOS.forEach(launchDTO -> {
               biddingMonitoringProcessService.launch(launchDTO);
           });
           return new JsonObject<>(true);
       }catch (Exception e){
           return new JsonObject<>(false);
       }
    }

    @PreAuthorize(hasPermission = "crm_bidding_monitoring_update",dataScope = "crm_bidding_monitoring_update")
    //@PreFlowPermission
    @PostMapping("/launchAndReturnProcessInstanceId")
    @Operation(summary = "商机审批-招投标信息监测返回流程id")
    public JsonObject<String> launchAndReturnProcessInstanceId(@Valid @RequestBody BiddingMonitoringFlowLaunchDTO launchDTO) {
        return new JsonObject<>(biddingMonitoringProcessService.launchAndReturnProcessInstanceId(launchDTO));
    }


    @PreFlowPermission
    @PostMapping("/competitorPage")
    @Operation(summary = "竞争对手分页查询")
    public JsonObject<TableDataInfo> CompetitorPage(@RequestBody CompetitorVO competitorVO){
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.notNull("流程实例id不能为null",headerValue);
        return remoteCompetitorService.page(competitorVO);
    }




    @PreFlowPermission
    @GetMapping("/detail")
    @Operation(summary = "商机审批-招投标详情(流程)")
    public JsonObject<BiddingMonitoringFlowDetailVO> detail(@RequestParam(required = false) String id,@RequestParam(required = false) String processInstanceId) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        BiddingMonitoringFlowDetailVO biddingMonitoringFlowDetailVO = biddingMonitoringFlowService.detail(id,processInstanceId);
        return new JsonObject<BiddingMonitoringFlowDetailVO>(biddingMonitoringFlowDetailVO);
    }



    @PreFlowPermission(hasAnyNodes = {"sid-72075B57-2AB1-4E7E-A2BE-8051D3DC2A37"})
    @PutMapping("/updateBiddingMonitoringFlow")
    @Operation(summary = "03修改招投标信息")
    public JsonObject<Boolean> updateBiddingMonitoringFlow(@RequestBody BiddingMonitoringFlowLaunchDTO launchDTO) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        launchDTO.setProcessInstanceId(processInstanceId);
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        //修改招投标信息
        boolean update = biddingMonitoringInfoService.lambdaUpdate()
                .eq(BiddingMonitoringInfo::getProcessInstanceId, launchDTO.getProcessInstanceId())
                .set(StringUtils.isNotBlank(launchDTO.getCustomerName()), BiddingMonitoringInfo::getBusinessUltimate, launchDTO.getCustomerName())
                .set(StringUtils.isNotBlank(launchDTO.getCustomerId()), BiddingMonitoringInfo::getCustomerId, launchDTO.getCustomerId())
                .set(StringUtils.isNotBlank(launchDTO.getProvince()), BiddingMonitoringInfo::getProvince, launchDTO.getProvince())
                .set(StringUtils.isNotBlank(launchDTO.getCity()), BiddingMonitoringInfo::getCity, launchDTO.getCity())
                .set(StringUtils.isNotBlank(launchDTO.getRegion()), BiddingMonitoringInfo::getRegion, launchDTO.getRegion())
                .set(StringUtils.isNotBlank(launchDTO.getFirstLevelIndustryAffiliation()), BiddingMonitoringInfo::getFirstLevelIndustryAffiliation, launchDTO.getFirstLevelIndustryAffiliation())
                .set(StringUtils.isNotBlank(launchDTO.getSecondaryLevelIndustryAffiliation()), BiddingMonitoringInfo::getSecondaryLevelIndustryAffiliation, launchDTO.getSecondaryLevelIndustryAffiliation())
                .set(StringUtils.isNotBlank(launchDTO.getReleaseTime()), BiddingMonitoringInfo::getReleaseTime, launchDTO.getReleaseTime())
                .set(StringUtils.isNotBlank(launchDTO.getBiddingProjectName()), BiddingMonitoringInfo::getBiddingProjectName, launchDTO.getBiddingProjectName())
                .set(StringUtils.isNotBlank(launchDTO.getInvolvingProducts()), BiddingMonitoringInfo::getInvolvingProducts, launchDTO.getInvolvingProducts())
                .set(StringUtils.isNotBlank(launchDTO.getProjectBudget()), BiddingMonitoringInfo::getProjectBudget, launchDTO.getProjectBudget())
                .set(StringUtils.isNotBlank(launchDTO.getBiddingDeadline()), BiddingMonitoringInfo::getBiddingDeadline, launchDTO.getBiddingDeadline())
                .set(StringUtils.isNotBlank(launchDTO.getBiddingWebsite()), BiddingMonitoringInfo::getBiddingWebsite, launchDTO.getBiddingWebsite())
                .update();
        BiddingMonitoringFlow biddingMonitoringFlow = HyperBeanUtils.copyPropertiesByJackson(launchDTO, BiddingMonitoringFlow.class);
        return new JsonObject<Boolean>(biddingMonitoringFlowService.updateBiddingMonitoringFlow(biddingMonitoringFlow));
    }



    @PreFlowPermission(hasAnyNodes = {"sid-AB5BAE87-EC35-467B-AB52-6270258D4174"})
    @PutMapping("/updateFollowUser")
    @Operation(summary = "修改项目跟进人员信息")
    public JsonObject<Boolean> updateFollowUser(@RequestBody BiddingMonitoringFlowLaunchDTO launchDTO) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        boolean update = biddingMonitoringFlowService.lambdaUpdate()
                .eq(BiddingMonitoringFlow::getProcessInstanceId,launchDTO.getProcessInstanceId())
                .set(StringUtils.isNotBlank(launchDTO.getFollowUpPerson()),BiddingMonitoringFlow::getFollowUpPerson, launchDTO.getFollowUpPerson())
                .update();
        return new JsonObject<Boolean>(update);
    }



    @PreFlowPermission(hasAnyNodes = {"sid-9952C46B-2715-487C-BB3C-86AE01A0E722"})
    @PutMapping("/updateFollowUpInfo")
    @Operation(summary = "02填写跟进信息")
    public JsonObject<Boolean> updateFollowUpInfo(@RequestBody BiddingMonitoringFlowLaunchDTO launchDTO) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        boolean update = biddingMonitoringFlowService.lambdaUpdate()
                .eq(BiddingMonitoringFlow::getProcessInstanceId,launchDTO.getProcessInstanceId())
                .set(BiddingMonitoringFlow::getFollowUp, launchDTO.getFollowUp())
                .set(BiddingMonitoringFlow::getProjectId,launchDTO.getProjectId())
                .set(BiddingMonitoringFlow::getProjectNo,launchDTO.getProjectNo())
                .set(BiddingMonitoringFlow::getBidderResult,launchDTO.getBidderResult())
                .set(BiddingMonitoringFlow::getBiddingType,launchDTO.getBiddingType())
                .set(BiddingMonitoringFlow::getBidderUnit,launchDTO.getBidderUnit())
                .set(BiddingMonitoringFlow::getBidderPublicationTime,launchDTO.getBidderPublicationTime())
                .set(BiddingMonitoringFlow::getNotFollowReason,launchDTO.getNotFollowReason())
                .set(BiddingMonitoringFlow::getBidderProvider, JSON.toJSONString(launchDTO.getBidderProvider()))
                .set(BiddingMonitoringFlow::getBidderWebsite,launchDTO.getBidderWebsite())
                .update();
        return new JsonObject<Boolean>(update);
    }



    @PreFlowPermission
    @PutMapping("/updateFollowUpType")
    @Operation(summary = "02修改项目跟进状态(02办理完毕调用)")
    public JsonObject<Boolean> updateFollowUpType(@RequestBody BiddingMonitoringFlowLaunchDTO launchDTO) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        String followUpType=null;
        LocalDateTime followUpTime = LocalDateTime.now();
        if (launchDTO.getFollowUp()==1){
            LocalDate biddingDeadline = Optional.ofNullable(launchDTO.getBiddingDeadline())
                    .map(DateUtil::strToDate)
                    .map(DateUtil::dateToLocalDate)
                    .orElseThrow(() -> new IllegalArgumentException("投标截止时间不能为空"));
            long daysBetween = ChronoUnit.DAYS.between(biddingDeadline, followUpTime);
            if (daysBetween <= 7) {
                followUpType =  "正常跟进";
            } else if (daysBetween > 7 && daysBetween <= 30) {
                followUpType =  "消极跟进";
            } else if (daysBetween > 30) {
                followUpType =  "消极办理";
            } else {
            }
        }
        boolean update = biddingMonitoringFlowService.lambdaUpdate()
                .eq(BiddingMonitoringFlow::getProcessInstanceId,processInstanceId)
                .set(BiddingMonitoringFlow::getFollowUpTime,followUpTime)
                .set(BiddingMonitoringFlow::getFollowUp, launchDTO.getFollowUp())
                .set(BiddingMonitoringFlow::getFollowUpType, followUpType)
                .update();
        return new JsonObject<Boolean>(update);
    }

    @PostMapping("/saleMangerSelectCustomer")
    @Operation(summary = "销管选择客户")
    @PreFlowPermission(hasAnyNodes = {"sid-72075B57-2AB1-4E7E-A2BE-8051D3DC2A37"})
    public JsonObject<List<CrmCustomerVo>> saleMangerSelectCustomer(@RequestBody CrmCustomerVo crmCustomerVo){
        return  remoteCustomerService.saleMangerSelectCustomer(crmCustomerVo);
    }

}