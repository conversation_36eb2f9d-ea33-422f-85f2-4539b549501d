package com.topsec.crm.flow.core.controller.contractreceivable;

import com.topsec.crm.flow.api.dto.performancereport.*;
import com.topsec.crm.flow.core.service.ContractReceivableService;
import com.topsec.crm.flow.core.service.PerformanceReportPaymentTermsService;
import com.topsec.crm.flow.core.service.PerformanceReportReceivableAmountService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/performanceReportReceivableAmount")
@Tag(name = "业绩上报应收催款列表", description = "/performanceReportReceivableAmount")
@RequiredArgsConstructor
@Slf4j
public class PerformanceReportReceivableAmountController extends BaseController {
    private final PerformanceReportReceivableAmountService performanceReportReceivableAmountService;

    private final PerformanceReportPaymentTermsService performanceReportPaymentInfoService;

    private final ContractReceivableService receivableService;
    @PostMapping("/pagePerformanceReportReceivableAmount")
    @Operation(summary = "业绩上报应收催款列表（分页）", method = "POST")
    @PreAuthorize(hasPermission = "crm_performance_report_receivable", dataScope = "crm_performance_report_receivable")
    public JsonObject<PageUtils<PerformanceReportReceivableAccountVO>> pagePerformanceReportReceivableAmount(@RequestBody PerformanceReportReceivableQuery query) {
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        if (CollectionUtils.isNotEmpty(personIdList)) {
            query.setContractOwnerIds(personIdList);
        }
        PageUtils<PerformanceReportReceivableAccountVO> performanceReportReceivableAccountVOPageUtils = performanceReportReceivableAmountService.pagePerformanceReportReceivableAccount(query);
        return new JsonObject<>(performanceReportReceivableAccountVOPageUtils);
    }

    @PostMapping("/exportPerformanceReportReceivableAmount")
    @Operation(summary = "导出业绩上报应收催款信息", method = "POST")
    @PreAuthorize(hasPermission = "crm_performance_report_receivable_export", dataScope = "crm_performance_report_receivable_export")
    public void exportPerformanceReportReceivableAmount(@RequestBody PerformanceReportReceivableQuery query) throws Exception {
        query.setPageNum(1);
        query.setPageSize(Integer.MAX_VALUE);
        PageUtils<PerformanceReportReceivableAccountVO> performanceReportReceivableAccountVOPageUtils = performanceReportReceivableAmountService.pagePerformanceReportReceivableAccount(query);
        List<PerformanceReportReceivableAccountVO> list = performanceReportReceivableAccountVOPageUtils.getList();
        ExcelUtil<PerformanceReportReceivableAccountVO> excelUtil = new ExcelUtil<>(PerformanceReportReceivableAccountVO.class);
        excelUtil.exportExcel(response, list,"业绩上报应收催款信息");
    }
    @GetMapping("/getPerformanceReportReceivableAccountDetail")
    @Operation(summary = "业绩上报应收催款详情", method = "POST")
    @PreAuthorize(hasPermission = "crm_performance_report_receivable", dataScope = "crm_performance_report_receivable")
    public JsonObject<PerformanceReportReceivableAccountVO> getPerformanceReportReceivableAccountDetail(@RequestParam String processInstanceId) {
        return new JsonObject<>(performanceReportReceivableAmountService.getPerformanceReportReceivableAccount(processInstanceId));
    }

    @PostMapping("/pagePerformanceExecuteByCondition")
    @Operation(summary = "分页查询业绩执行（批量发起应收催款的查询接口）", method = "POST")
    @PreAuthorize(hasPermission = "crm_performance_report_receivable", dataScope = "crm_performance_report_receivable")
    public JsonObject<PageUtils<PerformanceExecuteVO>> pagePerformanceExecuteByCondition(@RequestBody PerformanceReportReceivableQuery query) {
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        if (CollectionUtils.isNotEmpty(personIdList)) {
            query.setContractOwnerIds(personIdList);
        }
        return new JsonObject<>(performanceReportReceivableAmountService.pageByCondition(query));
    }

    @PostMapping("/selectPerformanceReportPaymentTermsByProcessInstanceId")
    @Operation(summary = "付款条款列表（分页）", method = "POST")
    @PreAuthorize(hasPermission = "crm_performance_report_receivable_payment_provision_update", dataScope = "crm_performance_report_receivable_payment_provision_update")
    public JsonObject<PageUtils<PerformanceReportPaymentVO>> selectPerformanceReportPaymentTermsByProcessInstanceId(@RequestBody PerformanceReportPaymentTermsQuery query) {
        List<PerformanceReportPaymentVO> performanceReportPaymentVOS = performanceReportReceivableAmountService.selectPerformanceReportPaymentTermsByProcessInstanceId(query.getProcessInstanceId());
        return new JsonObject<>(PageUtils.paginate(performanceReportPaymentVOS, query.getPageSize(), query.getPageNum()));
    }

    @PostMapping("/insertPerformanceReportPaymentTerms")
    @Operation(summary = "付款条款新增", method = "POST")
    @PreAuthorize(hasPermission = "crm_performance_report_receivable_payment_provision_update", dataScope = "crm_performance_report_receivable_payment_provision_update")
    public JsonObject<Boolean> insertPerformanceReportPaymentTerms(@RequestBody PerformanceReportPaymentTermsDTO dto){
        return new JsonObject<>(performanceReportPaymentInfoService.insertPerformanceReportPaymentTerms(dto));
    }

    @PostMapping("/updatePerformanceReportPaymentTerms")
    @Operation(summary = "付款条款修改", method = "POST")
    @PreAuthorize(hasPermission = "crm_performance_report_receivable_payment_provision_update", dataScope = "crm_performance_report_receivable_payment_provision_update")
    public JsonObject<Boolean> updatePerformanceReportPaymentTerms(@RequestBody PerformanceReportPaymentTermsDTO dto) {
        return new JsonObject<>(performanceReportPaymentInfoService.updatePerformanceReportPaymentTerms(dto));
    }

    @GetMapping("/delete")
    @Operation(summary = "付款条款删除", method = "POST")
    @PreAuthorize(hasPermission = "crm_performance_report_receivable_payment_provision_update", dataScope = "crm_performance_report_receivable_payment_provision_update")
    public JsonObject<Boolean> deletePerformanceReportPaymentTerms(@RequestParam String id) {
        return new JsonObject<>(performanceReportPaymentInfoService.deletePerformanceReportPaymentTerms(id));
    }

    @GetMapping("/launchReceivable")
    @Operation(summary = "发起应收催款流程")
    @PreAuthorize(hasPermission = "crm_performance_report_receivable_apply", dataScope = "crm_performance_report_receivable_apply")
    public JsonObject<Boolean> launchReceivable(@RequestParam String contractNumber, @RequestParam Integer ignoreCheck) {
        // 这个是合管发起 没有数据权限 可以发起所有的
        try {
            return new JsonObject<>(receivableService.launchPerformanceReportReceivable(contractNumber, ignoreCheck));
        } catch (Exception e) {
            log.error("发起应收催款流程失败", e);
            if (e.getMessage().contains("是否需要再次发起")) {
                return new JsonObject<>(3020, e.getMessage());
            }
            return new JsonObject<>(3010, e.getMessage());
        }
    }

    @PostMapping("/launchReceivableBatch")
    @Operation(summary = "发起应收催款流程（批量）")
    @PreAuthorize(hasPermission = "crm_performance_report_receivable_apply", dataScope = "crm_performance_report_receivable_apply")
    JsonObject<Boolean> launchReceivableBatch(@RequestBody Set<String> contractNumber, @RequestParam Integer ignoreCheck) {
        // 这个是合管发起 没有数据权限 可以发起所有的
        try {
            return new JsonObject<>(receivableService.launchPerformanceReportReceivableBatch(contractNumber, ignoreCheck));
        } catch (Exception e) {
            log.error("发起应收催款流程失败（批量）", e);
            return new JsonObject<>(3010, e.getMessage());
        }
    };
}
