package com.topsec.crm.flow.core.controller.collectionletters.businessauth;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.dto.collectionhandle.CollectionLettersHandleDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewNoticeArriveDTO;
import com.topsec.crm.flow.core.entity.ContractCollectionLetterMain;
import com.topsec.crm.flow.core.entity.ContractReviewMain;
import com.topsec.crm.flow.core.entity.ContractReviewNoticeArrive;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.constants.TosConstants;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@RestController
@AllArgsConstructor
@RequestMapping("/business/collectionLetters")
@Tag(name = "催款函业务接口",  description = "/collectionLetters")
public class ContractCollectionLettersBusinessController extends BaseController {

    private final ContractCollectionLetterService letterService;
    private final ContractCollectionLettersHandleService handleService;
    private final RemoteContractExecuteService contractExecuteService;
    private final ContractCollectionLetterMainService collectionLetterMainService;
    private final ContractReviewMainService mainService;
    private final ContractReviewNoticeArriveService noticeArriveService;

    @GetMapping("/getContractInfoByProcessInstanceId")
    @Operation(summary = "根据流程id查询合同信息")
    @PreAuthorize(hasPermission = "crm_contract_receivable_collection_letter", dataScope = "crm_contract_receivable_collection_letter")
    public JsonObject<CrmContractExecuteVO> getContractInfoByContractNumber(@RequestParam String processInstanceId) {
        // 查看这个流程的合同负责人和dataScope的关系
        checkCollectionLetterByProcessInstanceId(processInstanceId);
        // 其他线程可能影响这个接口
        PageHelper.clearPage();
        ContractCollectionLetterMain letterMain = collectionLetterMainService.getByProcessInstanceId(processInstanceId);
        String contractNumber = letterMain.getContractNumber();
        return contractExecuteService.getByContractNumber(contractNumber);
    }

    @GetMapping("/getLetterHandleByProcessInstanceId")
    @Operation(summary = "根据流程实例id查询催款函处理意见")
    @PreAuthorize(hasPermission = "crm_contract_receivable_collection_letter", dataScope = "crm_contract_receivable_collection_letter")
    public JsonObject<CollectionLettersHandleDTO> getLetterHandleByProcessInstanceId(@RequestParam String processInstanceId) {
        // 查看这个流程的合同负责人和dataScope的关系
        checkCollectionLetterByProcessInstanceId(processInstanceId);
        // 其他线程可能影响这个接口
        PageHelper.clearPage();
        CollectionLettersHandleDTO result = new CollectionLettersHandleDTO();
        CollectionLettersHandleDTO handleDTO = handleService.getByProcessInstanceId(processInstanceId);
        if (handleDTO != null) {
            HyperBeanUtils.copyProperties(handleDTO, result);
        }
        ContractCollectionLetterMain letterMain = collectionLetterMainService.getByProcessInstanceId(processInstanceId);
        result.setProcessNumber(letterMain.getProcessNumber());
        return new JsonObject<>(result);
    }

    @GetMapping("/getNoticeArriveByProcessInstanceId")
    @Operation(summary = "根据流程实例id查询通知和送达信息")
    @PreAuthorize(hasPermission = "crm_contract_receivable_collection_letter", dataScope = "crm_contract_receivable_collection_letter")
    public JsonObject<List<ContractReviewNoticeArriveDTO>> getNoticeArriveByProcessInstanceId(@RequestParam String processInstanceId) {
        // 查看这个流程的合同负责人和dataScope的关系
        checkCollectionLetterByProcessInstanceId(processInstanceId);
        ContractCollectionLetterMain letterMain = collectionLetterMainService.getByProcessInstanceId(processInstanceId);
        String contractNumber = letterMain.getContractNumber();
        ContractReviewMain main = mainService.getOne(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getContractNumber, contractNumber)
                .eq(ContractReviewMain::getDelFlag, 0).last("limit 1"));

        return new JsonObject<>(HyperBeanUtils.copyListProperties(noticeArriveService.list(new LambdaQueryWrapper<ContractReviewNoticeArrive>()
                .eq(ContractReviewNoticeArrive::getContractReviewMainId, main.getId())
                .eq(ContractReviewNoticeArrive::getDelFlag, 0)), ContractReviewNoticeArriveDTO::new));
    }

    private void checkCollectionLetterByProcessInstanceId(String processInstanceId){
        ContractCollectionLetterMain letterMain = collectionLetterMainService.getByProcessInstanceId(processInstanceId);
        if (letterMain == null) {
            throw new CrmException("参数信息有误");
        }
        String contractOwnerId = letterMain.getContractOwnerId();
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        if (personIdList == null) {
            return;
        }
        if (!personIdList.contains(contractOwnerId)) {
            // 不包含合同负责人的id 代表没权限
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }
}
