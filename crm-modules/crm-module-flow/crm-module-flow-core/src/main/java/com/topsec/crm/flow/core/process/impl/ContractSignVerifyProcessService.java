package com.topsec.crm.flow.core.process.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractProductOwnService;
import com.topsec.crm.contract.api.RemoteContractProductThirdService;
import com.topsec.crm.contract.api.entity.CrmContractProductOwnUpdateVO;
import com.topsec.crm.contract.api.entity.CrmContractProductThirdUpdateVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.RemoteContractUnconfirmedDetailService;
import com.topsec.crm.flow.api.dto.contractSignVerify.*;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.process.AbstractProcessService;
import com.topsec.crm.flow.core.process.ProcessTypeEnum;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.enums.ProcessDefinitionKeyEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.FlowStateInfoVo;
import com.topsec.vo.node.TfsNodeVo;
import com.topsec.vo.task.TfsTaskVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 签验收单流程服务
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ContractSignVerifyProcessService extends AbstractProcessService<ContractSignVerifyFlowLaunchDTO> {
    @Autowired
    private IContractSignVerifyMainService contractSignVerifyMainService;
    @Autowired
    private IContractSignVerifyAttachmentService contractSignVerifyAttachmentService;
    @Autowired
    private IContractSignVerifyUserSignService contractSignVerifyUserSignService;
    @Autowired
    private RemoteContractExecuteService contractExecuteService;
    @Autowired
    private IContractUnconfirmedDetailService contractUnconfirmedDetailService;
    @Autowired
    private RemoteContractProductThirdService remoteContractProductThirdService;
    @Autowired
    private RemoteContractProductOwnService remoteContractProductOwnService;
    @Autowired
    private IContractUnconfirmedService contractUnconfirmedService;
    @Autowired
    private IContractUnconfirmedDetailSnapshotService contractUnconfirmedDetailSnapshotService;
    @Autowired
    private RemoteContractUnconfirmedDetailService remoteContractUnconfirmedDetailService;
    @Autowired
    private IContractUnconfirmedSnapshotService contractUnconfirmedSnapshotService;

    @Override
    public ProcessTypeEnum processType() {
        return ProcessTypeEnum.CONTRACT_SIGN_VERIFY;
    }

    @Override
    protected void preProcess(ContractSignVerifyFlowLaunchDTO launchable) {
        Map<String,Object> variableMap= new HashMap<>();

        if(launchable.getConfirmedType() == 2){
            //先行确认-合同负责人审批
            variableMap.put("confirmClassification",true);

            //查询合同执行信息
            JsonObject<CrmContractExecuteVO> byContractNumber = contractExecuteService.getByContractNumber(launchable.getContractNumber());
            if(byContractNumber.isSuccess() && byContractNumber.getObjEntity() != null) {
                CrmContractExecuteVO exeVo = byContractNumber.getObjEntity();
                List<FlowPerson> flowPeople = AccountAccquireUtils.convertGetAccountByPersonId(Collections.singletonList(exeVo.getContractOwnerId()));
                //下一级节点办理人为所传入的销售列表(需要传入accountId)
                variableMap.put("assigneeList", flowPeople.stream().map(FlowPerson::getAccountId).collect(Collectors.toList()));
            }

        }else if(launchable.getConfirmedType() == 1){
            //正常确认-03节点（王洋3920）审批
            variableMap.put("confirmClassification",false);

            TfsNodeVo tfsNodeVo = new TfsNodeVo();
            tfsNodeVo.setVariables(variableMap);
            tfsNodeVo.setProcessDefinitionKey(ProcessDefinitionKeyEnum.REMINDER_RECEIPT_KEY);
            JsonObject<Map<String, List<String>>> mapJsonObject = tfsNodeClient.queryNodeAssigneeList(tfsNodeVo);
            if(mapJsonObject.isSuccess()){
                Map<String, List<String>> objEntity = mapJsonObject.getObjEntity();
                List<String> assigneeList = objEntity.get("assigneeList");
                launchable.setAssigneeList(CollectionUtil.toTreeSet(assigneeList, Comparator.naturalOrder()));
            }
        }

        launchable.setVariables(variableMap);
        launchable.setMatter("签验收单审批");
    }

    @Override
    protected String afterEngineLaunch(ProcessExtensionInfo processInfo, ContractSignVerifyFlowLaunchDTO launchable) {
        //1.保存合同坏账流程
        ContractSignVerifyMain contractSignVerifyMain = HyperBeanUtils.copyPropertiesByJackson(launchable, ContractSignVerifyMain.class);
        contractSignVerifyMainService.save(contractSignVerifyMain);

        //2.保存附件信息
        if(CollectionUtil.isNotEmpty(launchable.getAttachmentDTOS())) {
            List<ContractSignVerifyAttachment> attachments = HyperBeanUtils.copyListPropertiesByJackson(launchable.getAttachmentDTOS(), ContractSignVerifyAttachment.class);
            for (ContractSignVerifyAttachment attachment : attachments) {
                attachment.setId(UUID.randomUUID().toString());
                attachment.setProcessInstanceId(processInfo.getProcessInstanceId());
                attachment.setCreateUserName(NameUtils.getName(UserInfoHolder.getCurrentPersonId()));
            }
            contractSignVerifyAttachmentService.saveBatch(attachments);
        }

        //3.保存用户签名
        if(CollectionUtil.isNotEmpty(launchable.getUserSignVos())) {
            List<ContractSignVerifyUserSign> userSigns = HyperBeanUtils.copyListPropertiesByJackson(launchable.getUserSignVos(), ContractSignVerifyUserSign.class);
            for (ContractSignVerifyUserSign userSign : userSigns) {
                userSign.setId(UUID.randomUUID().toString());
                userSign.setProcessInstanceId(processInfo.getProcessInstanceId());
            }
            contractSignVerifyUserSignService.saveBatch(userSigns);
        }

        //没有业务ID
        return contractSignVerifyMain.getId();
    }

    //审批中处理
    @Override
    public void handleProcessing(FlowStateInfoVo flowInfo) {
        log.info("handleProcessing");
    }

    //已通过处理
    @Override
    public void handlePass(FlowStateInfoVo flowInfo) {
        log.info("handlePass");
        //签验收单通过，同步确认数量
        //1:自有产品，2：第三方产品
        //查询流程详情
        //1.查询流程基本信息
        ContractSignVerifyMain vmain = contractSignVerifyMainService.query().eq("process_instance_id", flowInfo.getProcessInstanceId()).one();
        List<ContractUnconfirmedDetail> list = contractUnconfirmedDetailService.query()
                .eq(StringUtils.isNotBlank(vmain.getContractNumber()), "contract_number", vmain.getContractNumber())
                .eq(StringUtils.isNotBlank(vmain.getUnconfirmedId()), "unconfirmed_id", vmain.getUnconfirmedId())
                .eq( "is_return", 0)
                .eq("del_flag", 0)
                .list();

        //2.数据汇总
        List<CrmContractProductOwnUpdateVO> ownUpdateVOS = new ArrayList<CrmContractProductOwnUpdateVO>();
        List<CrmContractProductThirdUpdateVO> thirdUpdateVOS = new ArrayList<CrmContractProductThirdUpdateVO>();
        for (ContractUnconfirmedDetail detail : list) {
            if(detail.getIsReturn() == 0) {
                String unconfirmedId = detail.getUnconfirmedId();
                if(detail.getProductSource() != null) {
                    if (detail.getProductSource() == 1) {
                        CrmContractProductOwnUpdateVO exist = ownUpdateVOS.stream().filter(e -> e.getProductOwnId().equals(unconfirmedId)).findFirst().orElse(null);
                        if (exist != null) {
                            exist.setConfirmNum(exist.getConfirmNum() + 1);
                        } else {
                            exist = new CrmContractProductOwnUpdateVO();
                            exist.setProductOwnId(unconfirmedId);
                            exist.setConfirmNum(1);
                            ownUpdateVOS.add(exist);
                        }
                    } else if (detail.getProductSource() == 2) {
                        CrmContractProductThirdUpdateVO exist = thirdUpdateVOS.stream().filter(e -> e.getProductThirdId().equals(unconfirmedId)).findFirst().orElse(null);
                        if (exist != null) {
                            exist.setConfirmNum(exist.getConfirmNum() + 1);
                        } else {
                            exist = new CrmContractProductThirdUpdateVO();
                            exist.setProductThirdId(unconfirmedId);
                            exist.setConfirmNum(1);
                            thirdUpdateVOS.add(exist);
                        }
                    }
                }
            }
        }
        //2.1修改同步产品数量
        remoteContractProductOwnService.writeContractProductOwn(ownUpdateVOS);
        remoteContractProductThirdService.writeContractProductThird(thirdUpdateVOS);

        //3.保存收入确认核定快照数据
        List<ContractUnconfirmedDetailSnapshot> snapshots = new ArrayList<ContractUnconfirmedDetailSnapshot>();
        if(StringUtils.isNotBlank(vmain.getUnconfirmedId())) {
            List<ContractUnconfirmedDetail> details = contractUnconfirmedDetailService.query()
                    .eq(StringUtils.isNotBlank(vmain.getUnconfirmedId()), "unconfirmed_id", vmain.getUnconfirmedId())
                    .eq("del_flag", 0)
                    .orderByDesc("sign_date")
                    .list();
            snapshots.addAll(HyperBeanUtils.copyListPropertiesByJackson(details, ContractUnconfirmedDetailSnapshot.class));
        }
        if(StringUtils.isBlank(vmain.getUnconfirmedId()) && StringUtils.isNotBlank(vmain.getContractNumber())){
            //查询未确认明细
            List<ContractUnconfirmed> uns = contractUnconfirmedService.query().eq("contract_number", vmain.getContractNumber()).list();

            for (ContractUnconfirmed un : uns) {
                List<ContractUnconfirmedDetail> details = contractUnconfirmedDetailService.query()
                        .eq(StringUtils.isNotBlank(un.getId()), "unconfirmed_id", un.getId())
                        .eq("del_flag", 0)
                        .orderByDesc("sign_date")
                        .list();
                snapshots.addAll(HyperBeanUtils.copyListPropertiesByJackson(details, ContractUnconfirmedDetailSnapshot.class));
            }
        }

        for (ContractUnconfirmedDetailSnapshot snapshot : snapshots) {
            snapshot.setId(UUID.randomUUID().toString());
            snapshot.setProcessInstanceId(flowInfo.getProcessInstanceId());
        }
        contractUnconfirmedDetailSnapshotService.saveBatch(snapshots);

        //4.保存未确认明细快照数据
        //4.1查询未确认明细列表
        ContractUnconfirmedVo params = new ContractUnconfirmedVo();
        params.setContractNumber(vmain.getContractNumber());
        params.setId(vmain.getUnconfirmedId());
        JsonObject<List<ContractUnconfirmedVo>> listJsonObject = remoteContractUnconfirmedDetailService.selectList(params);
        if(listJsonObject.isSuccess() && listJsonObject.getObjEntity() != null){
            List<ContractUnconfirmedSnapshot> snaps = HyperBeanUtils.copyListPropertiesByJackson(listJsonObject.getObjEntity(), ContractUnconfirmedSnapshot.class);
            for (ContractUnconfirmedSnapshot snap : snaps) {
                snap.setUnconfirmedId(snap.getId());
                snap.setId(UUID.randomUUID().toString());
                snap.setProcessInstanceId(flowInfo.getProcessInstanceId());
            }
            contractUnconfirmedSnapshotService.saveBatch(snaps);
        }
    }



    @Override
    public void handlePassBack(FlowStateInfoVo flowInfo) {
        //完结后退回
    }

    @Override
    public void handleProcessingBack(FlowStateInfoVo flowInfo) {
        //流程中退回
    }

    @Override
        public Pair<Boolean, String> checkFilling(TfsTaskVo taskVo) {
        return Pair.of(true, null);
    }
}
