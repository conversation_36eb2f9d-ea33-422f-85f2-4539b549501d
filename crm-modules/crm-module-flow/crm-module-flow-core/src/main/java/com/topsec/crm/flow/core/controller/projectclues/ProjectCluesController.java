package com.topsec.crm.flow.core.controller.projectclues;

import com.topsec.crm.flow.api.dto.projectclues.ProjectCluesFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ProjectClues;
import com.topsec.crm.flow.core.process.impl.ProjectCluesProcessService;
import com.topsec.crm.flow.core.service.IProjectCluesService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.enums.ProcessDefinitionKeyEnum;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.*;

/**
 * <p>
 * 项目线索信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@RestController
@RequestMapping("/projectClues")
@Tag(name = "项目线索商机流程", description = "/projectClues")
public class ProjectCluesController extends BaseController {

    @Autowired
    private ProjectCluesProcessService projectCluesProcessService;

    @Autowired
    private IProjectCluesService projectCluesService;

    @Autowired
    private TfsNodeClient tfsNodeClient;

    // JD节点ID
    private static final String JD_PATH = "sid-9BD6B803-9758-4FD4-B509-7C14C4EF6235";

    // 非JD节点ID
    private static final String UN_JD_PATH = "sid-370CE77A-D774-4B23-8B91-E48F20F4FA00";

    @PreAuthorize(hasPermission = "crm_project_clues_add")
    @PostMapping("/launch")
    @Operation(summary = "发起项目线索商机流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody ProjectCluesFlowLaunchDTO launchDTO) {
        launchDTO.setMatter("商机咨询-项目线索");
        String consultType = launchDTO.getConsultType();
        if(StringUtils.isNotBlank(consultType)){
            String activityId = "";
            if("JD".equals(consultType)){
                activityId = JD_PATH;
            }else{
                activityId = UN_JD_PATH;
            }
            JsonObject<List<String>> listJsonObject = tfsNodeClient.selectSpecifyNodeAssigneeList(ProcessDefinitionKeyEnum.PROJECT_CLUES_KEY.getValue(), activityId);
            if(listJsonObject.isSuccess()){
                List<String> assigneeList = listJsonObject.getObjEntity();
                if(CollectionUtils.isNotEmpty(assigneeList)){
                    launchDTO.setAssigneeList(new HashSet<>(assigneeList));
                }
            }
        }
        return new JsonObject<>(projectCluesProcessService.launch(launchDTO));
    }

    @PreFlowPermission
    @GetMapping("/detail/{processInstanceId}")
    @Operation(summary = "查看项目线索流程详情")
    public JsonObject<ProjectCluesFlowLaunchDTO> projectCluesDetail(@PathVariable String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ProjectClues projectClues = projectCluesService.projectCluesDetail(processInstanceId);
        return new JsonObject<ProjectCluesFlowLaunchDTO>(HyperBeanUtils.copyPropertiesByJackson(projectClues, ProjectCluesFlowLaunchDTO.class));
    }

    @PreFlowPermission(hasAnyNodes = {"sid-9BD6B803-9758-4FD4-B509-7C14C4EF6235","sid-370CE77A-D774-4B23-8B91-E48F20F4FA00"})
    @PutMapping("/updateProjectClues")
    @Operation(summary = "修改项目线索数据")
    public JsonObject<Boolean> updateProjectClues(@RequestBody ProjectCluesFlowLaunchDTO projectClues) {
        PreFlowPermissionAspect.checkProcessInstanceId(projectClues.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ProjectClues projectCluesUpdate = HyperBeanUtils.copyPropertiesByJackson(projectClues, ProjectClues.class);
        return new JsonObject<Boolean>(projectCluesService.updateProjectClues(projectCluesUpdate));
    }

    @PreFlowPermission(hasAnyNodes = {"sid-160DD0C7-DCCC-45D2-A6FB-353DC874F4E0"})
    @PutMapping("/updateFollowUser")
    @Operation(summary = "修改项目跟进人员信息")
    public JsonObject<Boolean> updateFollowUser(@RequestBody ProjectCluesFlowLaunchDTO projectClues) {
        PreFlowPermissionAspect.checkProcessInstanceId(projectClues.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        boolean update = projectCluesService.lambdaUpdate()
                .set(ProjectClues::getFollowUserId, projectClues.getFollowUserId())
                .set(ProjectClues::getFollowUserName, projectClues.getFollowUserName())
                .eq(ProjectClues::getProcessInstanceId, projectClues.getProcessInstanceId())
                .update();
        return new JsonObject<Boolean>(update);
    }

    @PreFlowPermission(hasAnyNodes = {"sid-B80DB6E6-C7FA-4A5B-8338-C909FB2F4A7D"})
    @PutMapping("/updateFollowResult")
    @Operation(summary = "修改项目跟进结果信息")
    public JsonObject<Boolean> updateFollowResult(@RequestBody ProjectCluesFlowLaunchDTO projectClues) {
        PreFlowPermissionAspect.checkProcessInstanceId(projectClues.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        boolean update = projectCluesService.lambdaUpdate()
                .set(ProjectClues::getClueEffective, projectClues.getClueEffective())
                .set(ProjectClues::getProjectId, projectClues.getProjectId())
                .set(ProjectClues::getProjectNo, projectClues.getProjectNo())
                .set(ProjectClues::getClueStatus, projectClues.getClueStatus())
                .eq(ProjectClues::getProcessInstanceId, projectClues.getProcessInstanceId())
                .update();
        return new JsonObject<Boolean>(update);
    }
}
