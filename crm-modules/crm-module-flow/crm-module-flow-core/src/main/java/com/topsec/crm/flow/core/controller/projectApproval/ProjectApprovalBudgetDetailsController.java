package com.topsec.crm.flow.core.controller.projectApproval;

import com.topsec.crm.flow.api.dto.projectApproval.ProjectApprovalBudgetDetailsVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ProjectApproval;
import com.topsec.crm.flow.core.entity.ProjectApprovalBudgetDetails;
import com.topsec.crm.flow.core.service.ProjectApprovalBudgetDetailsService;
import com.topsec.crm.flow.core.service.ProjectApprovalService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-07-18  15:20
 * @Description: 立项审批预算明细
 */
@RestController
@RequestMapping("/projectApprovalBudgetDetails")
@Tag(name = "立项审批预算明细", description = "/projectApprovalBudgetDetails")
@RequiredArgsConstructor
@Validated
public class ProjectApprovalBudgetDetailsController extends BaseController {

    private final ProjectApprovalBudgetDetailsService projectApprovalBudgetDetailsService;


    private final ProjectApprovalService projectApprovalService;


    @GetMapping("/deleteById")
    @Operation(summary = "根据id删除")
    @PreFlowPermission
    public JsonObject<Boolean> deleteById(@RequestParam String id) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);

        Optional<ProjectApprovalBudgetDetails> optionalById =Optional.ofNullable(projectApprovalBudgetDetailsService.getById(id));
        if (optionalById.isPresent()) {
            ProjectApprovalBudgetDetails byId = optionalById.get();
            String processInstanceId = projectApprovalService.getById(byId.getProjectApprovalId()).getProcessInstanceId();
            PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
            return new JsonObject<>(projectApprovalBudgetDetailsService.deleteById(id));
        } else {
            // 处理 id 不存在的情况，例如抛出异常或返回错误信息
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "新增或修改")
    @PreFlowPermission
    public JsonObject<Boolean> saveOrUpdate(@RequestBody ProjectApprovalBudgetDetailsVO projectApprovalBudgetDetailsVO) {
        if (StringUtils.isNotBlank(projectApprovalBudgetDetailsVO.getProjectApprovalId())){
            String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
            ProjectApproval projectApproval = Optional.ofNullable(projectApprovalService.getById(projectApprovalBudgetDetailsVO.getProjectApprovalId())).orElseThrow(() -> new CrmException(ResultEnum.NULL_OBJ_ENTITY));
            PreFlowPermissionAspect.checkProcessInstanceId(headerValue, projectApproval.getProcessInstanceId());
        }
        return new JsonObject<>(projectApprovalBudgetDetailsService.saveOrUpdate(projectApprovalBudgetDetailsVO)) ;
    }

    @GetMapping("/selectByProjectApprovalId")
    @Operation(summary = "根据立项审批id查询")
    @PreFlowPermission
    public JsonObject<List<ProjectApprovalBudgetDetailsVO>> selectByProjectApprovalId(@RequestParam String projectApprovalId) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ProjectApproval projectApproval = Optional.ofNullable(projectApprovalService.getById(projectApprovalId)).orElseThrow(() -> new CrmException(ResultEnum.NULL_OBJ_ENTITY));
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, projectApproval.getProcessInstanceId());
        return new JsonObject<>(projectApprovalBudgetDetailsService.selectByProjectApprovalId(projectApprovalId));
    }


}
