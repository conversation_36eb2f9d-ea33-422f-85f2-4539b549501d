package com.topsec.crm.flow.core.controller.contractreview.businessauth;


import cn.hutool.core.collection.CollectionUtil;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.contractreview.delivery.ContractDeliveryDetailDTO;
import com.topsec.crm.flow.api.dto.contractreview.fileinfo.ContractReviewAttachmentDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnSntVO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.dto.contractreview.response.ContractAttachmentStatus;
import com.topsec.crm.flow.api.dto.contractreview.salemaninfo.SalesSelectDTO;
import com.topsec.crm.flow.api.dto.contractreview.statistics.ContractExecuteStatisticsVO;
import com.topsec.crm.flow.api.dto.contractreview.statistics.ContractReviewTabulateDataVO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.ReviewRetentionMoneyDTO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteStatisticsVO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewMainInfo;
import com.topsec.crm.flow.api.vo.sealApplication.SealApplicationInfoVO;
import com.topsec.crm.flow.core.entity.ContractReviewAttachment;
import com.topsec.crm.flow.core.entity.ContractReviewDelivery;
import com.topsec.crm.flow.core.entity.ContractReviewMain;
import com.topsec.crm.flow.core.entity.SalesAgreementReviewMain;
import com.topsec.crm.flow.core.process.impl.TfsProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.bean.HomeNameValue;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.utils.AuthorizeUtil;
import com.topsec.crm.project.api.client.RemoteProjectSignAgentClient;
import com.topsec.crm.project.api.entity.AgentTreeSelect;
import com.topsec.crm.project.api.entity.CrmProjectSigningAgentVo;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.query.FormListQuery;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsCcFormClient;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.tfs.api.client.TfsTaskClient;
import com.topsec.vo.TfsFormContentVo;
import com.topsec.vo.task.Approved;
import com.topsec.vo.task.TaskHistoryVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Tag(name = "合同评审业务接口")
@RequiredArgsConstructor
@RequestMapping("/business")
public class ContractBusinessController extends BaseController {

    private final ContractReviewMainService contractReviewMainService;
    private final AuthorizeUtil authorizeUtil;
    private final RemoteContractExecuteService remoteContractExecuteService;
    private final ContractReviewAttachmentService contractReviewAttachmentService;
    private final ContractReviewDeliveryService contractReviewDeliveryService;
    private final ContractReviewPaymentProvisionService contractReviewPaymentProvisionService;
    private final ContractReviewRevenueRecognitionService reviewRevenueRecognitionService;
    private final ContractReviewRetentionMoneyService contractReviewRetentionMoneyService;
    private final ContractReviewProductOwnService contractReviewProductOwnService;
    private final ContractReviewProductThirdService contractReviewProductThirdService;
    private final TfsProcessService tfsProcessService;
    private final TfsFormContentClient tfsFormContentClient;
    private final TfsTaskClient tfsTaskClient;
    private final PriceReviewMainService priceReviewMainService;
    private final RemoteProjectSignAgentClient remoteProjectSignAgentClient;
    private final TfsCcFormClient tfsCcFormClient;
    private final ContractReviewFlowService flowService;
    private final SalesAgreementReviewMainService salesAgreementReviewMainService;
    private final ISealApplicationContractReviewService iSealApplicationContractReviewService;


    @GetMapping("/contractReview/contractInfo")
    @Operation(summary = "合同评审信息")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<ContractReviewMainFlowLaunchDTO> contractInfo(@RequestParam String contractId) {
        checkBusinessAuthByContractId(contractId);
        ContractReviewMainFlowLaunchDTO launchDTO = contractReviewMainService.contractInfo(contractId, false, true);
        List<ContractProductThirdDTO> contractProductThirdDTOS = launchDTO.getContractProductThirdDTOS();
        ListUtils.emptyIfNull(contractProductThirdDTOS).forEach(contractReviewProductThirdService::mosaicProductPrice);
        return new JsonObject<>(launchDTO);
    }

    @GetMapping("/contractReview/contractInfoBasic")
    @Operation(summary = "合同评审信息")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<ContractReviewMainFlowLaunchDTO> contractInfoBasic(@RequestParam String contractId) {
        checkBusinessAuthByContractId(contractId);
        ContractReviewMainFlowLaunchDTO launchDTO = contractReviewMainService.contractInfo(contractId, false, false);
        return new JsonObject<>(launchDTO);
    }

    @GetMapping("/contractReview/contractInfoByProcessInstanceId")
    @Operation(summary = "根据流程id获取合同评审信息")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<ContractReviewMainFlowLaunchDTO> contractInfoByProcessInstanceId(@RequestParam String processInstanceId) {
        ContractReviewMain contractReviewMain = contractReviewMainService.getByProcessInstanceId(processInstanceId);
        if (contractReviewMain == null) {
            throw new CrmException("该合同评审不存在");
        }
        checkBusinessAuthByContractId(contractReviewMain.getId());

        ContractReviewMainFlowLaunchDTO launchDTO = contractReviewMainService.contractInfoByProcessInstanceId(processInstanceId);
        List<ContractProductThirdDTO> contractProductThirdDTOS = launchDTO.getContractProductThirdDTOS();
        ListUtils.emptyIfNull(contractProductThirdDTOS).forEach(contractReviewProductThirdService::mosaicProductPrice);
        return new JsonObject<>(launchDTO);
    }

    @GetMapping("/contractReview/contractInfoBasicByProcessInstanceId")
    @Operation(summary = "根据流程id获取合同评审信息")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<ContractReviewMainFlowLaunchDTO> contractInfoBasicByProcessInstanceId(@RequestParam String processInstanceId) {
        ContractReviewMain contractReviewMain = contractReviewMainService.getByProcessInstanceId(processInstanceId);
        if (contractReviewMain == null) {
            throw new CrmException("该合同评审不存在");
        }
        checkBusinessAuthByContractId(contractReviewMain.getId());

        return new JsonObject<>(contractReviewMainService.contractInfoBasicByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/contractReview/productOwnInfoByContractId")
    @Operation(summary = "根据合同id取合同中的自有产品信息（树结构）")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<List<ContractProductOwnDTO>> productOwnInfoByContractId(@RequestParam String contractId) {
        checkBusinessAuthByContractId(contractId);
        return new JsonObject<>(contractReviewProductOwnService.productInfoByContractId(contractId, true));
    }

    @GetMapping("/contractReview/productThirdInfoByContractId")
    @Operation(summary = "根据合同id取合同中的自有产品信息（树结构）")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<List<ContractProductThirdDTO>> productThirdInfoByContractId(@RequestParam String contractId) {
        checkBusinessAuthByContractId(contractId);
        return new JsonObject<>(contractReviewProductThirdService.productThirdInfoByContractId(contractId, true));
    }

    @GetMapping("/contractReview/getExecuteStatistics")
    @Operation(summary = "合同评审统计信息")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<ContractExecuteStatisticsVO> getExecuteStatistics(@RequestParam String contractId) {
        // 草稿接口，待发起的时候，自己看自己的
        checkBusinessAuthByContractId(contractId);
        return new JsonObject<>(contractReviewMainService.getContractExecuteStatistics(contractId));
    }

    @GetMapping("/contractReview/getPerformanceExecuteStatistics")
    @Operation(summary = "合同评审统计信息")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<PerformanceExecuteStatisticsVO> getPerformanceExecuteStatistics(@RequestParam String contractId) {
        // 草稿接口，待发起的时候，自己看自己的
        checkBusinessAuthByContractId(contractId);
        return new JsonObject<>(contractReviewMainService.getPerformanceExecuteStatistics(contractId));
    }

    @GetMapping("/contractAttachment/getAttachmentByContractId")
    @Operation(summary = "根据合同id获取附件")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<List<ContractReviewAttachmentDTO>> getByContractId(@RequestParam String contractId) {
        checkBusinessAuthByContractId(contractId);
        return new JsonObject<>(contractReviewAttachmentService.getByContractId(contractId));
    }

    @GetMapping("/contractDelivery/getDeliveryInfoList")
    @Operation(summary = "根据合同ID获取合同发货信息", description = "合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<List<ContractDeliveryDTO>> getDeliveryInfoList(@RequestParam String contractId){
        checkBusinessAuthByContractId(contractId);
        return new JsonObject<>(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage(),contractReviewDeliveryService.getContractDeliveryInfoList(contractId));
    }

    @GetMapping("/contractTerm/pagePaymentProvisionByContractId")
    @Operation(summary = "根据合同id查询付款条款")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<TableDataInfo> pagePaymentProvisionByContractId(@RequestParam String contractId) {
        checkBusinessAuthByContractId(contractId);
        startPage();
        return new JsonObject<>(contractReviewPaymentProvisionService.pageByContractId(contractId));
    }

    @GetMapping("/contractTerm/pageRevenueRecognitionByContractId")
    @Operation(summary = "根据合同id查询收入确认条款")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<TableDataInfo> pageByContractId(@RequestParam String contractId) {
        checkBusinessAuthByContractId(contractId);
        startPage();
        return new JsonObject<>(reviewRevenueRecognitionService.pageByContractId(contractId));
    }

    @GetMapping("/contractTerm/pageRetentionMoneyByContractId")
    @Operation(summary = "根据合同id查询质保金条款")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<ReviewRetentionMoneyDTO> pageRetentionMoneyByContractId(@RequestParam String contractId) {
        checkBusinessAuthByContractId(contractId);
        return new JsonObject<>(contractReviewRetentionMoneyService.getByContractId(contractId));
    }

    @PostMapping("/contractReview/productOwnSnByContractIds")
    @Operation(summary = "根据合同id查询出货产品详情")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<TableDataInfo> productOwnSnByContractId(@RequestBody ContractProductOwnSntVO contractProductOwnSntVO) {
        assert contractProductOwnSntVO != null;
        checkBusinessAuthByContractId(contractProductOwnSntVO.getContractId());
        return new JsonObject<>(contractReviewProductOwnService.productOwnSnByContractId(contractProductOwnSntVO));
    }

    @GetMapping("/contractDelivery/getDeliveredProductPage")
    @Operation(summary = "根据发货ID获取发货分页列表", description = "合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<TableDataInfo> getDeliveredProductPage(@RequestParam String deliveryId){
        // 判断这个id 是不是这个人加的
        ContractReviewDelivery delivery = contractReviewDeliveryService.getById(deliveryId);
        if (delivery == null || delivery.getDelFlag()) {
            throw new CrmException("发货信息不存在");
        }
        String contractId = delivery.getContractId();
        checkBusinessAuthByContractId(contractId);
        startPage();
        return new JsonObject<>(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage(),contractReviewDeliveryService.getContractDeliveryProductPage(deliveryId));
    }

    @GetMapping("/process/findByProcessInstanceId")
    @Operation(summary = "根据流程实例id查询流程信息")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<TfsFormContentVo> findByProcessInstanceId(@RequestParam String processInstanceId) {
        ContractReviewMain contractReviewMain = contractReviewMainService.getByProcessInstanceId(processInstanceId);
        if (contractReviewMain == null) {
            throw new CrmException("该合同评审不存在");
        }
        checkBusinessAuthByContractId(contractReviewMain.getId());
        return tfsProcessService.updateCompanyName(tfsFormContentClient.findByProcessInstanceId(processInstanceId));
    }

    @GetMapping("/task/queryContractAuthCommentList")
    @Operation(summary = "根据流程实例id查询合同评审历史审批进度以及评论回复(有节点权限验证)")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<List<TaskHistoryVo>> queryContractAuthCommentList(@RequestParam String processInstanceId) {
        ContractReviewMain contractReviewMain = contractReviewMainService.getByProcessInstanceId(processInstanceId);
        if (contractReviewMain == null) {
            throw new CrmException("该合同评审不存在");
        }
        checkBusinessAuthByContractId(contractReviewMain.getId());
        String currentAccountId = getCurrentAccountId();
        return Optional.ofNullable(tfsTaskClient.getTaskHistoryList(processInstanceId)).map(
                        taskHistoryVo->{
                            tfsProcessService.authCommentList(taskHistoryVo.getObjEntity(),currentAccountId);
                            return taskHistoryVo;
                        })
                .orElseThrow(() -> new CrmException("查询审批意见失败"));
    }

    @Operation(summary = "查询项目最后一个通过的价审信息")
    @GetMapping("/contractReview/queryLatestPassedPriceReviewMainByContractId")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<PriceReviewMainInfo> queryLatestPassedPriceReviewMainByProjectId(@RequestParam String contractId) {
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        checkBusinessAuthByContractInfo(contractInfo);
        String projectId = contractInfo.getProjectId();
        return new JsonObject<>(priceReviewMainService.queryLatestPassedPriceReviewMainByProjectId(projectId));
    }

    @GetMapping("/agent/tree/{contractId}")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<List<AgentTreeSelect>> tree(@PathVariable String contractId) {
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        checkBusinessAuthByContractInfo(contractInfo);
        String projectId = contractInfo.getProjectId();
        return remoteProjectSignAgentClient.tree(projectId);
    }

    @GetMapping("/agent/selectSigningAgentDetail")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<CrmAgentVo> selectSigningAgentDetail(@RequestParam String contractId, @RequestParam String agentId) {
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        checkBusinessAuthByContractInfo(contractInfo);
        String projectId = contractInfo.getProjectId();
        //查询项目
        JsonObject<List<CrmProjectSigningAgentVo>> listJsonObject = remoteProjectSignAgentClient.listAgent(projectId);
        if(listJsonObject.isSuccess()){
            List<CrmProjectSigningAgentVo> signs = listJsonObject.getObjEntity();
            if(CollectionUtil.isEmpty(signs)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
            Set<String> collect = signs.stream().map(CrmProjectSigningAgentVo::getAgentId).collect(Collectors.toSet());
            if(!collect.contains(agentId)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }
        CrmProjectSigningAgentVo query = new CrmProjectSigningAgentVo();
        query.setAgentId(agentId);
        query.setProjectId(projectId);
        return remoteProjectSignAgentClient.selectSigningAgentDetail(query);
    }

    @GetMapping("/contractAttachment/checkAttachment")
    @Operation(summary = "查当前合同是否有电子合同和普通附件")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<ContractAttachmentStatus> checkAttachment(@RequestParam String contractId) {
        checkBusinessAuthByContractId(contractId);
        return new JsonObject<>(contractReviewAttachmentService.checkAttachment(contractId));
    }

    @PostMapping("/contractAttachment/upload")
    @Operation(summary = "上传合同附件")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<Boolean> upload(@RequestBody ContractReviewAttachmentDTO contractReviewAttachmentDTO) {
        assert contractReviewAttachmentDTO != null;
        checkBusinessAuthByContractId(contractReviewAttachmentDTO.getContractReviewMainId());
        return new JsonObject<>(contractReviewAttachmentService.saveContractAttachment(contractReviewAttachmentDTO));
    }

    @PostMapping("/contractDelivery/saveDeliveryInfo")
    @Operation(summary = "发货信息保存", description = "合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<Boolean> saveDeliveryInfo(@Valid @RequestBody ContractDeliveryDTO deliveryDTO){
        assert deliveryDTO != null;
        String contractId = deliveryDTO.getContractId();
        checkBusinessAuthByContractId(contractId);
        return new JsonObject<>(contractReviewDeliveryService.saveDeliveryInfo(deliveryDTO));
    }

    @GetMapping("/contractDelivery/getProductPageByContractId")
    @Operation(summary = "根据合同ID获取合同产品列表", description = "合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<PageUtils<ContractDeliveryDetailDTO>> getProductPageByContractId(@RequestParam String contractId, String deliveryId, Integer pageNum, Integer pageSize){
        checkBusinessAuthByContractId(contractId);
        return new JsonObject<>(ResultEnum.SUCCESS.getResult(),ResultEnum.SUCCESS.getMessage(),contractReviewDeliveryService.getProductPageByContractId(contractId, deliveryId,pageNum, pageSize));
    }

    @GetMapping("/contractDelivery/deleteById")
    @Operation(summary = "删除发货", description = "合同发货")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<Boolean> deleteById(@RequestParam String id){
        // 判断这个id 是不是这个人加的
        ContractReviewDelivery delivery = contractReviewDeliveryService.getById(id);
        if (delivery == null || delivery.getDelFlag()) {
            throw new CrmException("发货信息不存在");
        }
        checkBusinessAuthByContractId(delivery.getContractId());
        return new JsonObject<>(contractReviewDeliveryService.deleteById(id));
    }

    @GetMapping("/contractAttachment/delete")
    @Operation(summary = "删除附件")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<Boolean> delete(@RequestParam String id) {
        ContractReviewAttachment attachment = contractReviewAttachmentService.getById(id);
        if (attachment == null || attachment.getDelFlag() == 1) {
            throw new CrmException("附件不存在");
        }
        checkBusinessAuthByContractId(attachment.getContractReviewMainId());
        return new JsonObject<>(contractReviewAttachmentService.delete(id));
    }

    @PostMapping("/cclist")
    @Operation(summary = "根基流程实例id查询抄送人")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<List<Approved>> cclist(@RequestBody FormListQuery formListQuery){
        if(Objects.isNull(formListQuery)){
            throw new CrmException("参数不能为空");
        }
        String processInstanceId = formListQuery.getProcessInstanceId();
        ContractReviewMain contractReviewMain = contractReviewMainService.getByProcessInstanceId(processInstanceId);
        if (contractReviewMain == null) {
            throw new CrmException("该合同评审不存在");
        }
        checkBusinessAuthByContractId(contractReviewMain.getId());
        if(Objects.isNull(processInstanceId)){
            throw new CrmException("参数不能为空");
        }
        return  tfsCcFormClient.cclist(formListQuery);
    }

    @PreAuthorize
    @PostMapping("/exportContractReviewTabulateData")
    @Operation(summary = "导出试运行期间合同评审统计数据")
    public void exportContractReviewTabulateData(@RequestBody(required = false) List<String> processInstanceIds) throws IOException {
        List<ContractReviewTabulateDataVO> list = flowService.exportContractReviewTabulateData(new HashSet<>(processInstanceIds));
        ExcelUtil<ContractReviewTabulateDataVO> excelUtil = new ExcelUtil<>(ContractReviewTabulateDataVO.class);
        excelUtil.exportExcel(response,list,"合同评审统计数据");
    }
    @GetMapping("/contractReview/getSalesAgreementsById")
    @Operation(description = "根据合同id查询销售协议")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<List<SalesAgreementReviewMain>> getSalesAgreementsById(@RequestParam String contractId) {
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        checkBusinessAuthByContractInfo(contractInfo);
        List<String> salesAgreements = contractInfo.getSalesAgreements();
        if (CollectionUtil.isEmpty(salesAgreements)) {
            return new JsonObject<>(Collections.emptyList());
        }
        return new JsonObject<>(salesAgreementReviewMainService.findSalesAgreementByIds(salesAgreements));
    }


    @GetMapping("/querySealApplicationByContractId")
    @Operation(description = "合同评审印鉴")
    @PreAuthorize(hasAnyPermission = {"crm_contract_execute_company", "crm_contract_execute_agent"})
    public JsonObject<List<SealApplicationInfoVO>> querySealApplicationByContractNumber(@RequestParam String contractId){
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        checkBusinessAuthByContractInfo(contractInfo);
        if (StringUtils.isEmpty(contractInfo.getContractNumber())) {
            return new JsonObject<>(Collections.emptyList());
        }
        return new JsonObject<>(iSealApplicationContractReviewService.listSealInfoByContractNumber(contractInfo.getContractNumber()));
    }



    // 下面接口都是假接口 前端不好处理前缀 但是又用不到 直接啥也不干就行
    @GetMapping("/contractReview/linkmanSelect")
    public JsonObject<List<HomeNameValue<String, String>>> linkmanSelect(){return new JsonObject<>(new ArrayList<>());}
    @GetMapping("/contractReview/customerAndSignContractByProject")
    public JsonObject<ContractReviewMainFlowLaunchDTO> customerAndSignContractByProject(){return new JsonObject<>(new ContractReviewMainFlowLaunchDTO());}
    @GetMapping("/contractReview/deemedDirectCheck")
    public JsonObject<Boolean> deemedDirectCheck(){return new JsonObject<>(true);}
    @GetMapping("/contractReview/salesSelect")
    public JsonObject<SalesSelectDTO> salesSelect(){return new JsonObject<>(new SalesSelectDTO());}
    @GetMapping("/contractReview/productOwnInfoByProjectId")
    public JsonObject<TableDataInfo> productOwnInfoByProjectId(){return new JsonObject<>(new TableDataInfo());}
    @GetMapping("/contractReview/productThirdInfoByProjectId")
    public JsonObject<TableDataInfo> productThirdInfoByProjectId(){return new JsonObject<>(new TableDataInfo());}

    private void checkBusinessAuthByContractId(String contractId){
        ContractReviewMainBaseInfoDTO contractInfo = contractReviewMainService.getByContractId(contractId);
        checkBusinessAuthByContractInfo(contractInfo);
    }

    private void checkBusinessAuthByContractInfo(ContractReviewMainBaseInfoDTO contractInfo){
        if (contractInfo == null) {
            throw new CrmException("该合同信息不存在");
        }
        Integer projectSource = contractInfo.getProjectSource();
        DataScopeParam dataScopeParam = new DataScopeParam();
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        if (projectSource == 1) {
            authorizeUtil.setDataScopeParam(currentPersonId, "crm_contract_execute_company", dataScopeParam);
        } else if (projectSource == 3) {
            authorizeUtil.setDataScopeParam(currentPersonId, "crm_contract_execute_agent", dataScopeParam);
        } else {
            throw new CrmException("项目来源异常");
        }
        // 判断dataScope中的personIds
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        if (personIdList == null) {
            // 有所有人的权限
            return;
        }
        String contractNumber = contractInfo.getContractNumber();
        // 先取合同执行里面 看看是否合同评审已经生效
        JsonObject<CrmContractExecuteVO> result = remoteContractExecuteService.getByContractNumber(contractNumber);
        String contractOwnerId;
        if (result.isSuccess() && result.getObjEntity() != null) {
            // 有合同执行 要判断当前登录人和合同负责人
            CrmContractExecuteVO crmContractExecuteVO = result.getObjEntity();
            contractOwnerId = crmContractExecuteVO.getContractOwnerId();
        } else {
            // 无合同执行，判断合同评审的销售人，作为合同负责人
            contractOwnerId = contractInfo.getSaleId();
        }
        if (!personIdList.contains(contractOwnerId)) {
            // 不包含合同负责人的id 代表没权限
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }


}
