package com.topsec.crm.flow.core.controller.threeProcurement;

import com.topsec.crm.flow.api.vo.ThreeProcurementAcceptanceVo;
import com.topsec.crm.flow.api.vo.ThreeProcurementReviewMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ThreeProcurementAcceptance;
import com.topsec.crm.flow.core.entity.ThreeProcurementPaymentReviewMain;
import com.topsec.crm.flow.core.entity.ThreeProcurementRelease;
import com.topsec.crm.flow.core.service.ThreeProcurementAcceptanceService;
import com.topsec.crm.flow.core.service.ThreeProcurementPaymentReviewMainService;
import com.topsec.crm.flow.core.service.ThreeProcurementReleaseService;
import com.topsec.crm.flow.core.service.ThreeProcurementReviewMainService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;


/**
 * 第三方采购验收结论
 *
 * <AUTHOR>
 * @email 
 * @date 2024-11-01 17:26:59
 */
@RestController
@RequestMapping("/threeprocurementacceptance")
@Tag(name = "【第三方采购验收结论】", description = "threeprocurementacceptance")
@RequiredArgsConstructor
@Validated
public class ThreeProcurementAcceptanceController {

    private final ThreeProcurementAcceptanceService threeProcurementAcceptanceService;

    private final ThreeProcurementReviewMainService threeProcurementReviewMainService;
    private final ThreeProcurementPaymentReviewMainService threeProcurementPaymentReviewMainService;


    @GetMapping("/queryThreeProcurementAcceptanceList")
    @Operation(summary = "查询验收结论")
    @PreFlowPermission
    public JsonObject<List<ThreeProcurementAcceptanceVo>> queryThreeProcurementAcceptanceList(@RequestParam String purchaseNumber){
        if (StringUtils.isEmpty(purchaseNumber)) {
            throw  new CrmException("采购编号不能为空");
        }
        ThreeProcurementReviewMainVo threeProcurementReviewMainVo = threeProcurementReviewMainService.selectThreeProcurementReviewMainByPurchaseNumber(purchaseNumber);
        if (threeProcurementReviewMainVo == null){
            throw  new CrmException("采购信息不存在");
        }
        if (!Objects.equals(threeProcurementReviewMainVo.getProcessInstanceId(),HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID))){
            List<ThreeProcurementPaymentReviewMain> threeProcurementPaymentList = threeProcurementPaymentReviewMainService.getThreeProcurementPaymentList(purchaseNumber, null);
            if (threeProcurementPaymentList.isEmpty()){
                throw  new CrmException("流程实例id不匹配");
            }
            List<String> strings = threeProcurementPaymentList.stream().map(ThreeProcurementPaymentReviewMain::getPurchaseNumber).toList();
            if (!strings.contains(purchaseNumber)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }

        List<ThreeProcurementAcceptance> threeProcurementAcceptances = threeProcurementAcceptanceService.queryThreeProcurementAcceptanceList(purchaseNumber);
        List<ThreeProcurementAcceptanceVo> threeProcurementAcceptanceVoList = HyperBeanUtils.copyListPropertiesByJackson(threeProcurementAcceptances, ThreeProcurementAcceptanceVo.class);
        NameUtils.setName(threeProcurementAcceptanceVoList);
        return new JsonObject<>(threeProcurementAcceptanceVoList);
    }




    @PostMapping("/saveThreeProcurementAcceptance")
    @Operation(summary = "新增验收结论")
    @PreFlowPermission
    public JsonObject<Boolean> saveThreeProcurementAcceptance(@RequestBody ThreeProcurementAcceptanceVo threeProcurementAcceptanceVo){
        if (threeProcurementAcceptanceVo == null) {
            throw  new CrmException("采购编号不能为空");
        }
        if (StringUtils.isEmpty(threeProcurementAcceptanceVo.getProcessInstanceId())) {
            throw  new CrmException("流程实例id不能为空不能为空");
        }
        PreFlowPermissionAspect.checkProcessInstanceId(threeProcurementAcceptanceVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementAcceptance threeProcurementAcceptance = HyperBeanUtils.copyPropertiesByJackson(threeProcurementAcceptanceVo, ThreeProcurementAcceptance.class);
        boolean result = threeProcurementAcceptanceService.saveThreeProcurementAcceptance(threeProcurementAcceptance);
        return new JsonObject<>(result);
    }



}
