package com.topsec.crm.flow.core.controller.flow;

import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsAssignRuleClient;
import com.topsec.vo.node.TfsAssignRuleVo;
import com.topsec.vo.node.TfsNextNodeVo;
import com.topsec.vo.node.TfsNodeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/assignRule")
@Tag(name = "流程节点配置规则相关Controller", description = "/assignRule")
@RequiredArgsConstructor
@Validated
public class FlowAssignRuleController extends BaseController {

    private final TfsAssignRuleClient tfsAssignRuleClient;


    @PostMapping("/queryRuleInfo")
    @Operation(summary = "查询节点配置规则")
    @PreFlowPermission
    public JsonObject<TfsAssignRuleVo> queryRuleInfo(@RequestBody TfsNextNodeVo tfsNextNodeVo){
        PreFlowPermissionAspect.checkProcessInstanceId(tfsNextNodeVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return tfsAssignRuleClient.findByRuleInfo(tfsNextNodeVo);
    }


}
