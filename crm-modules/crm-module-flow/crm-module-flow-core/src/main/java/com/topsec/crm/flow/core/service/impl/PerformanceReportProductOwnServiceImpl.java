package com.topsec.crm.flow.core.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.flow.api.dto.performancereport.*;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.*;
import com.topsec.crm.flow.core.service.PerformanceReportContractDeliveryService;
import com.topsec.crm.flow.core.service.PerformanceReportProductOwnService;
import com.topsec.crm.flow.core.service.ReturnExchangeProductService;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnVO;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.product.api.RemoteProductAgentInventoryService;
import com.topsec.crm.product.api.RemoteProductSeparationRelService;
import com.topsec.crm.product.api.RemoteProductService;
import com.topsec.crm.product.api.RemoteProductWholesalePriceService;
import com.topsec.crm.product.api.dto.CrmProductSeparationRelQuery;
import com.topsec.crm.product.api.entity.CrmProductAgentInventoryVO;
import com.topsec.crm.product.api.entity.CrmProductSeparationRelVo;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.product.api.entity.CrmProductWholesalePriceVO;
import com.topsec.crm.project.api.client.RemoteProjectProductSnClient;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PerformanceReportProductOwnServiceImpl extends ServiceImpl<PerformanceReportProductOwnMapper, PerformanceReportProductOwn> implements PerformanceReportProductOwnService {

    private final PerformanceReportProductOwnMapper performanceReportProductOwnMapper;

    private final PerformanceReportProductOwnSnMapper performanceReportProductOwnSnMapper;

    private final PerformanceReportProductSeparationSnMapper performanceReportProductSeparationSnMapper;

    private final PerformanceReportProductSeparationMapper performanceReportProductSeparationMapper;

    private final RemoteProductSeparationRelService remoteProductSeparationRelService;

    private final RemoteProductService remoteProductService;

    private final RemoteProductAgentInventoryService remoteProductAgentInventoryService;

    private final RemoteProjectProductSnClient remoteProjectProductSnClient;

    private final PerformanceReportMapper performanceReportMapper;

    private final PerformanceReportContractDeliveryService performanceReportContractDeliveryService;

    private final PerformanceReportContractDeliveryDetailMapper performanceReportContractDeliveryDetailMapper;

    private final RemoteProductWholesalePriceService remoteProductWholesalePriceService;

    private final ReturnExchangeProductService returnExchangeProductService;
    /**
     * 重新匹配所有软硬分离产品
     * @param performanceReportProductQuery
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertPerformanceReportProductSeparationAll(PerformanceReportProductQuery performanceReportProductQuery) {
        List<PerformanceReportProductOwn> performanceReportProductOwnList = list(new QueryWrapper<PerformanceReportProductOwn>()
                .eq("process_instance_id", performanceReportProductQuery.getProcessInstanceId())
                .eq("del_flag", 0)
                .eq(StringUtils.isNotEmpty(performanceReportProductQuery.getReturnExchangeProcessInstanceId()), "return_exchange_process_instance_id", performanceReportProductQuery.getReturnExchangeProcessInstanceId())
                .gt("product_num", 0));
        for (PerformanceReportProductOwn performanceReportProductOwn : performanceReportProductOwnList){
            performanceReportProductQuery.setPerformanceReportProductId(performanceReportProductOwn.getId());
            insertPerformanceReportProductSeparation(performanceReportProductQuery);
        }
        return true;
    }

    /**
     * 匹配软硬分离产品
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertPerformanceReportProductSeparation(PerformanceReportProductQuery performanceReportProductQuery) {
        String performanceReportProductId = performanceReportProductQuery.getPerformanceReportProductId();
        String supplierName = performanceReportProductQuery.getSupplierName();
        PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductId);
        PerformanceReport performanceReport = performanceReportMapper.selectOne(new QueryWrapper<PerformanceReport>().eq("process_instance_id", performanceReportProductOwn.getProcessInstanceId()));
        // 查询软硬分离关系条件
        CrmProductSeparationRelQuery crmProductSeparationRelQuery = new CrmProductSeparationRelQuery();
        crmProductSeparationRelQuery.setContractCompanyName(supplierName);
        crmProductSeparationRelQuery.setStuffCode(performanceReportProductOwn.getStuffCode());
        // 查询软硬分离关系
        List<CrmProductSeparationRelVo> crmProductSeparationRelVos = getCrmProductSeparationInfo(performanceReportProductOwn, crmProductSeparationRelQuery);
        if (crmProductSeparationRelVos == null){
            throw new CrmException("无软硬分离匹配关系");
        }
        // 软硬分离产品数量
        Integer productNum = performanceReportProductOwn.getProductNum();
        // 判断是否 借转销 专项备货
        if ((performanceReportProductOwn.getIsBorrowForForward() != null && performanceReportProductOwn.getIsBorrowForForward() == 1) || (performanceReportProductOwn.getIsSpecialItem() != null && performanceReportProductOwn.getIsSpecialItem() == 1)){
            int borrowForForwardOrSpecialItemNum = performanceReportProductSeparationMapper.selectBorrowForForwardOrSpecialItemNum(performanceReportProductOwn.getId());
            productNum = productNum - borrowForForwardOrSpecialItemNum;
        }

        List<CrmProductSeparationRelVo> collect = crmProductSeparationRelVos.stream().sorted(Comparator.comparing(CrmProductSeparationRelVo::getSerialNumber)).collect(Collectors.toList());
        // 写入硬件产品和序列号
        Integer productHardNum = insertPerformanceReportProductSeparationHardwareInfo(performanceReportProductOwn.getId(), supplierName, collect, productNum, performanceReportProductQuery.getApprovalNode(), performanceReport.getProcessNumber(), performanceReportProductQuery.getReturnExchangeProcessInstanceId());
        // 补默认硬件
        insertPerformanceReportProductSeparationDefaultHardware(productHardNum, performanceReportProductId, performanceReportProductOwn.getStuffCode());
        // 写入软件(不包括借转销专项备货)
        List<PerformanceReportProductSeparation> performanceReportProductSeparationList = performanceReportProductSeparationMapper.selectList(new QueryWrapper<PerformanceReportProductSeparation>()
                .eq("performance_report_product_id", performanceReportProductId)
                .eq("product_classification", 1)
                .eq("del_flag", 0)
                .and(performanceReportProductSeparationQueryWrapper -> performanceReportProductSeparationQueryWrapper.isNull("borrow_for_forward").or().eq("borrow_for_forward", 0))
                .and(performanceReportProductSeparationQueryWrapper -> performanceReportProductSeparationQueryWrapper.isNull("special_item").or().eq("special_item", 0)));
        for (PerformanceReportProductSeparation performanceReportProductSeparation : performanceReportProductSeparationList){
            insertPerformanceReportProductSeparationSoft(performanceReportProductId, performanceReportProductSeparation, crmProductSeparationRelQuery);
        }
        if (StringUtils.isNotEmpty(performanceReportProductOwn.getReturnExchangeProcessInstanceId())){
            insertReturnExchangeNewProductSeparationHardwareSn(performanceReportProductOwn, 1);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertBorrowForForwardOrSpecialItemSeparation(PerformanceReportProductQuery performanceReportProductQuery) {
        List<PerformanceReportProductOwn> performanceReportProductOwnList = list(new QueryWrapper<PerformanceReportProductOwn>().eq("process_instance_id", performanceReportProductQuery.getProcessInstanceId())
                                                                                .and(performanceReportProductOwnQueryWrapper -> performanceReportProductOwnQueryWrapper.eq("is_borrow_for_forward", 1).or().eq("is_special_item", 1))
                .eq(StringUtils.isNotEmpty(performanceReportProductQuery.getReturnExchangeProcessInstanceId()), "return_exchange_process_instance_id", performanceReportProductQuery.getReturnExchangeProcessInstanceId()));
        if (performanceReportProductOwnList == null || performanceReportProductOwnList.isEmpty()){
            return false;
        }
        List<PerformanceReportProductSeparation> performanceReportProductSeparationList = performanceReportProductSeparationMapper.selectList(new QueryWrapper<PerformanceReportProductSeparation>().in("performance_report_product_id", performanceReportProductOwnList.stream().map(PerformanceReportProductOwn::getId).collect(Collectors.toList())));
        if (performanceReportProductSeparationList != null && !performanceReportProductSeparationList.isEmpty()){
            return false;
        }
        CrmProductSeparationRelQuery crmProductSeparationRelQuery = new CrmProductSeparationRelQuery();
        crmProductSeparationRelQuery.setContractCompanyName(performanceReportProductQuery.getSupplierName());
        for (PerformanceReportProductOwn performanceReportProductOwn : performanceReportProductOwnList){
            crmProductSeparationRelQuery.setStuffCode(performanceReportProductOwn.getStuffCode());
            insertPerformanceReportBorrowForForward(performanceReportProductOwn, performanceReportProductOwn.getProductNum(), crmProductSeparationRelQuery);
            if (StringUtils.isNotEmpty(performanceReportProductOwn.getReturnExchangeProcessInstanceId())){
                insertReturnExchangeNewProductSeparationHardwareSn(performanceReportProductOwn, 1);
            }
        }
        return true;
    }

    private Integer insertPerformanceReportBorrowForForward(PerformanceReportProductOwn performanceReportProductOwn, Integer productNum, CrmProductSeparationRelQuery crmProductSeparationRelQuery ) {
        List<CrmProjectProductSnVO> projectProductSnVOList = remoteProjectProductSnClient.getProductSn(performanceReportProductOwn.getProjectRecordId()).getObjEntity();
        if (projectProductSnVOList != null) {
            // 序列号对应产品信息
            String deviceStuffCode = projectProductSnVOList.get(0).getDeviceStuffCode();
            CrmProductVo crmProductVo = remoteProductService.getProductByMaterialCode(deviceStuffCode).getObjEntity();
            // 硬件
            PerformanceReportProductSeparation performanceReportProductSeparation = getProductDetail(crmProductVo.getId());
            CrmProductSeparationRelVo productSeparationRelVo = remoteProductSeparationRelService.getCrmProductSeparationRelHardwareInfo(performanceReportProductOwn.getStuffCode(), deviceStuffCode).getObjEntity();

            performanceReportProductSeparation.setIsDefault(productSeparationRelVo.getIsDefault());
            if (performanceReportProductOwn.getIsBorrowForForward() != null && performanceReportProductOwn.getIsBorrowForForward() == 1) {
                performanceReportProductSeparation.setBorrowForForward(1);
            } else if (performanceReportProductOwn.getIsSpecialItem() != null && performanceReportProductOwn.getIsSpecialItem() == 1) {
                performanceReportProductSeparation.setSpecialItem(1);
            }
            performanceReportProductSeparation.setProductClassification(1);
            performanceReportProductSeparation.setPerformanceReportProductId(performanceReportProductOwn.getId());
            performanceReportProductSeparation.setProductNum(projectProductSnVOList.size());
            BigDecimal sellinPrice = productSeparationRelVo.getSellinPrice();
            if (performanceReportProductOwn.getProductLic() > 0){
                sellinPrice = sellinPrice.multiply(new BigDecimal(performanceReportProductOwn.getProductLic()));
            }
            performanceReportProductSeparation.setSellInPrice(sellinPrice);
            // 成交单价
            BigDecimal quotedPrice = performanceReportProductSeparationMapper.selectQuotedPrice(performanceReportProductOwn.getId());
            if (quotedPrice.compareTo(sellinPrice) > 0){
                performanceReportProductSeparation.setQuotedPrice(sellinPrice);
            }else {
                performanceReportProductSeparation.setQuotedPrice(quotedPrice);
            }
            // 价差
            BigDecimal subtract = performanceReportProductSeparation.getSellInPrice().subtract(performanceReportProductSeparation.getQuotedPrice());
            BigDecimal num = BigDecimal.valueOf(performanceReportProductSeparation.getProductNum());
            performanceReportProductSeparation.setPriceDifference(subtract.multiply(num));
            performanceReportProductSeparationMapper.insert(performanceReportProductSeparation);
            // 序列号
            for (CrmProjectProductSnVO crmProjectProductSnVO : projectProductSnVOList) {
                PerformanceReportProductSeparationSn performanceReportProductSeparationSn = new PerformanceReportProductSeparationSn();
                performanceReportProductSeparationSn.setPerformanceReportProductSeparationId(performanceReportProductSeparation.getId());
                performanceReportProductSeparationSn.setPerformanceReportProductId(performanceReportProductOwn.getId());
                performanceReportProductSeparationSn.setPsn(crmProjectProductSnVO.getPsn());
                performanceReportProductSeparationSnMapper.insert(performanceReportProductSeparationSn);
            }
            productNum = productNum - performanceReportProductSeparation.getProductNum();
            // 软件
            insertPerformanceReportProductSeparationSoft(performanceReportProductOwn.getId(), performanceReportProductSeparation, crmProductSeparationRelQuery);

        }
        return productNum;
    }


    /**
     * 修改硬件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePerformanceReportProductSeparationHardware(PerformanceReportProductQuery performanceReportProductQuery) {
        List<PerformanceReportProductSeparationDTO> performanceReportProductSeparationDTOList = performanceReportProductQuery.getPerformanceReportProductSeparationDTOList();
        String supplierName = performanceReportProductQuery.getSupplierName();
        // 自有产品信息
        String performanceReportProductId = performanceReportProductSeparationDTOList.get(0).getPerformanceReportProductId();
        PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductId);
        PerformanceReport performanceReport = performanceReportMapper.selectOne(new QueryWrapper<PerformanceReport>().eq("process_instance_id", performanceReportProductOwn.getProcessInstanceId()));
        // 删除匹配的软硬产品
        deletePerformanceReportProductSn(performanceReportProductOwn.getId());
        performanceReportProductSeparationMapper.deletePerformanceReportProductSeparation(performanceReportProductOwn.getId());

        Integer productNum = performanceReportProductOwn.getProductNum();

        CrmProductSeparationRelQuery crmProductSeparationRelQuery = new CrmProductSeparationRelQuery();
        crmProductSeparationRelQuery.setContractCompanyName(supplierName);
        crmProductSeparationRelQuery.setStuffCode(performanceReportProductOwn.getStuffCode());
        // 判断是否 借转销 专项备货
        if ((performanceReportProductOwn.getIsBorrowForForward() != null && performanceReportProductOwn.getIsBorrowForForward() == 1) || (performanceReportProductOwn.getIsSpecialItem() != null && performanceReportProductOwn.getIsSpecialItem() == 1)){
            int borrowForForwardOrSpecialItemNum = performanceReportProductSeparationMapper.selectBorrowForForwardOrSpecialItemNum(performanceReportProductOwn.getId());
            productNum = productNum - borrowForForwardOrSpecialItemNum;
        }

        List<CrmProductSeparationRelVo> crmProductSeparationRelVos1 = new ArrayList<>();
        for (PerformanceReportProductSeparationDTO performanceReportProductSeparationDTO : performanceReportProductSeparationDTOList){
            CrmProductSeparationRelVo crmProductSeparationRelVo = new CrmProductSeparationRelVo();
            crmProductSeparationRelVo.setIsDefault(performanceReportProductSeparationDTO.getIsDefault());
            crmProductSeparationRelVo.setSeparationProductId(performanceReportProductSeparationDTO.getProductId());
            crmProductSeparationRelVo.setAvailableInventoryNum(performanceReportProductSeparationDTO.getProductNum());
            crmProductSeparationRelVo.setQuotedPrice(performanceReportProductSeparationDTO.getQuotedPrice());
            crmProductSeparationRelVo.setSellinPrice(performanceReportProductSeparationDTO.getSellInPrice());
            crmProductSeparationRelVos1.add(crmProductSeparationRelVo);
        }
        // 写入硬件产品和序列号
        Integer productHardNum = insertPerformanceReportProductSeparationHardwareInfo(performanceReportProductOwn.getId(), supplierName, crmProductSeparationRelVos1, productNum, performanceReportProductQuery.getApprovalNode(), performanceReport.getProcessNumber(), performanceReportProductQuery.getReturnExchangeProcessInstanceId());
        // 补默认硬件产品
        insertPerformanceReportProductSeparationDefaultHardware(productHardNum, performanceReportProductId, performanceReportProductOwn.getStuffCode());
        // 写入软件产品(不包括借转销专项备货)
        List<PerformanceReportProductSeparation> performanceReportProductSeparationList = performanceReportProductSeparationMapper.selectList(new QueryWrapper<PerformanceReportProductSeparation>()
                .eq("performance_report_product_id", performanceReportProductId)
                .eq("product_classification", 1)
                .eq("del_flag", 0)
                .and(performanceReportProductSeparationQueryWrapper -> performanceReportProductSeparationQueryWrapper.isNull("borrow_for_forward").or().eq("borrow_for_forward", 0))
                .and(performanceReportProductSeparationQueryWrapper -> performanceReportProductSeparationQueryWrapper.isNull("special_item").or().eq("special_item", 0)));
        // 查询软件
        for (PerformanceReportProductSeparation performanceReportProductSeparation : performanceReportProductSeparationList){
            insertPerformanceReportProductSeparationSoft(performanceReportProductId, performanceReportProductSeparation, crmProductSeparationRelQuery);
        }
        if (StringUtils.isNotEmpty(performanceReportProductOwn.getReturnExchangeProcessInstanceId())){
            insertReturnExchangeNewProductSeparationHardwareSn(performanceReportProductOwn, 1);
        }
        return true;
    }


    /**
     * 国代更改序列号
     *
     * @param performanceReportProductSeparationDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePerformanceReportProductSeparationSn(PerformanceReportProductSeparationDTO performanceReportProductSeparationDTO) {
        String performanceReportProductId = performanceReportProductSeparationDTO.getPerformanceReportProductId();
        PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductId);
        PerformanceReport performanceReport = performanceReportMapper.selectOne(new LambdaQueryWrapper<PerformanceReport>()
                .eq(PerformanceReport::getProcessInstanceId, performanceReportProductOwn.getProcessInstanceId())
                .eq(PerformanceReport::getDelFlag, 0)
                .last("limit 1"));
        String supplierName = performanceReport.getSupplierName();
        String processNumber = performanceReport.getProcessNumber();
        // 当前硬件
        PerformanceReportProductSeparation performanceReportProductSeparation = performanceReportProductSeparationMapper.selectById(performanceReportProductSeparationDTO.getId());
        // 删除当前硬件已关联序列号
        deletePerformanceReportProductSeparationSn(performanceReportProductSeparationDTO.getId(), supplierName, processNumber, performanceReportProductOwn.getReturnExchangeProcessInstanceId());
        // 删除发货序列号信息
        performanceReportContractDeliveryDetailMapper.deleteContractDeliveryDetail(performanceReportProductOwn.getProcessInstanceId());
        // 修改前的产品数量
        Integer productNumOld = performanceReportProductSeparation.getProductNum();
        List<PerformanceReportProductSeparationSnDTO> performanceReportProductSeparationSnDTOS = performanceReportProductSeparationDTO.getPerformanceReportProductSeparationSnDTOS();
        // 非默认修改产品数量，默认则不需要
        if (performanceReportProductSeparation.getIsDefault() == 0){
            // 修改当前硬件信息
            performanceReportProductSeparation.setProductNum(performanceReportProductSeparationSnDTOS.size());
            // 价差
            BigDecimal subtract = performanceReportProductSeparation.getSellInPrice().subtract(performanceReportProductSeparation.getQuotedPrice());
            BigDecimal num = BigDecimal.valueOf(performanceReportProductSeparation.getProductNum());
            performanceReportProductSeparation.setPriceDifference(subtract.multiply(num));
            performanceReportProductSeparationMapper.updateById(performanceReportProductSeparation);
        }
        // 写入硬件序列号
        ListUtils.emptyIfNull(performanceReportProductSeparationSnDTOS).forEach(performanceReportProductSeparationSnDTO -> {
            PerformanceReportProductSeparationSn performanceReportProductSeparationSn = new PerformanceReportProductSeparationSn();
            performanceReportProductSeparationSn.setPerformanceReportProductSeparationId(performanceReportProductSeparationDTO.getId());
            performanceReportProductSeparationSn.setPerformanceReportProductId(performanceReportProductId);
            performanceReportProductSeparationSn.setPsn(performanceReportProductSeparationSnDTO.getPsn());
            performanceReportProductSeparationSn.setProductDeliveryId(performanceReportProductSeparationSnDTO.getProductDeliveryId());
            performanceReportProductSeparationSn.setStorageTime(performanceReportProductSeparationSnDTO.getStorageTime());
            performanceReportProductSeparationSnMapper.insert(performanceReportProductSeparationSn);
        });
        // 锁定库存
        List<String> list1 = performanceReportProductSeparationSnDTOS.stream().map(PerformanceReportProductSeparationSnDTO::getPsn).toList();
        if (StringUtils.isEmpty(performanceReportProductOwn.getReturnExchangeProcessInstanceId())){
            remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(1, supplierName, processNumber, list1);
        }else {
            remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(4, supplierName, processNumber, list1);
        }
        // 删除软件
        PerformanceReportProductSeparation performanceReportProductSeparationSoft = performanceReportProductSeparationMapper.selectOne(new QueryWrapper<PerformanceReportProductSeparation>().eq("hardware_separation_id", performanceReportProductSeparation.getId()).eq("del_flag", 0));
        performanceReportProductSeparationMapper.deleteById(performanceReportProductSeparationSoft.getId(), true);
        // 重新写入软件
        CrmProductSeparationRelQuery crmProductSeparationRelQuery = new CrmProductSeparationRelQuery();
        crmProductSeparationRelQuery.setContractCompanyName(supplierName);
        crmProductSeparationRelQuery.setStuffCode(performanceReportProductOwn.getStuffCode());
        insertPerformanceReportProductSeparationSoft(performanceReportProductId, performanceReportProductSeparation, crmProductSeparationRelQuery);

        // 剩余硬件产品数量
        Integer productHardNum = productNumOld - performanceReportProductSeparation.getProductNum();
        // 补默认硬件
        PerformanceReportProductSeparation performanceReportProductSeparationDefault = insertPerformanceReportProductSeparationDefaultHardware(productHardNum, performanceReportProductOwn.getId(), performanceReportProductOwn.getStuffCode());
        if (performanceReportProductSeparationDefault != null){
            if (performanceReportProductSeparation.getIsDefault() == 0){
                // 删除默认硬件已关联序列号
                deletePerformanceReportProductSeparationSn(performanceReportProductSeparationDefault.getId(), supplierName, processNumber, performanceReportProductOwn.getReturnExchangeProcessInstanceId());
                // 重新关联
                List<CrmProductAgentInventoryVO> crmProductAgentInventoryVOS = Optional.ofNullable(remoteProductAgentInventoryService.getCrmProductAgentInventoryInfo(performanceReportProductSeparationDefault.getStuffCode(), supplierName, performanceReportProductSeparationDefault.getProductNum(), performanceReportProductSeparationDefault.getSellInPrice())).map(JsonObject::getObjEntity).orElse(null);
                ListUtils.emptyIfNull(crmProductAgentInventoryVOS).forEach(crmProductAgentInventoryVO -> {
                    // 关联硬件序列号
                    insertPerformanceReportProductSeparationHardwareSn(performanceReportProductId, performanceReportProductSeparationDefault, crmProductAgentInventoryVO);
                });
                // 锁定库存
                if (crmProductAgentInventoryVOS != null){
                    List<String> list = crmProductAgentInventoryVOS.stream().map(CrmProductAgentInventoryVO::getInSerialNumber).toList();
                    if (StringUtils.isEmpty(performanceReportProductOwn.getReturnExchangeProcessInstanceId())){
                        remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(1, supplierName, processNumber, list);
                    }else {
                        remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(4, supplierName, processNumber, list);
                    }
                }
            }
            insertPerformanceReportProductSeparationSoft(performanceReportProductOwn.getId(), performanceReportProductSeparationDefault, crmProductSeparationRelQuery);
        }
        if (StringUtils.isNotEmpty(performanceReportProductOwn.getReturnExchangeProcessInstanceId())){
            insertReturnExchangeNewProductSeparationHardwareSn(performanceReportProductOwn, 1);
        }
        return true;
    }

    public boolean deletePerformanceReportProductSeparationSn(String id, String supplierName, String processNumber, String returnExchangeProcessInstanceId){
        // 删除硬件已关联序列号
        List<PerformanceReportProductSeparationSn> performanceReportProductSeparationSns1 = performanceReportProductSeparationSnMapper.selectList(new QueryWrapper<PerformanceReportProductSeparationSn>().eq("performance_report_product_separation_id", id).eq("del_flag", 0));
        performanceReportProductSeparationSnMapper.updateByPerformanceReportProductSeparationId(id);
        List<String> list2 = performanceReportProductSeparationSns1.stream().map(PerformanceReportProductSeparationSn::getPsn).toList();
        // 解除锁定库存
        if (StringUtils.isEmpty(returnExchangeProcessInstanceId)){
            remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(1, supplierName, processNumber, list2);
        }else {
            remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(4, supplierName, processNumber, list2);
        }
        return true;
    }

    @Override
    public boolean updatePerformanceReportProductSn(PerformanceReportProductOwnDTO performanceReportProductOwnDTO) {
        PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductOwnDTO.getId());
        PerformanceReport performanceReport = performanceReportMapper.selectOne(new LambdaQueryWrapper<PerformanceReport>()
                .eq(PerformanceReport::getProcessInstanceId, performanceReportProductOwn.getProcessInstanceId())
                .eq(PerformanceReport::getDelFlag, 0)
                .last("limit 1"));
        String supplierName = performanceReport.getSupplierName();
        String processNumber = performanceReport.getProcessNumber();
        // 删除已关联序列号，解除锁定
        deletePerformanceReportProductSn(performanceReportProductOwnDTO.getId());

        // 锁定库存
        List<PerformanceReportProductOwnSnDTO> performanceReportProductOwnSnDTOS = performanceReportProductOwnDTO.getPerformanceReportProductOwnSnDTOS();
        for (PerformanceReportProductOwnSnDTO performanceReportProductOwnSnDTO : performanceReportProductOwnSnDTOS){
            PerformanceReportProductOwnSn performanceReportProductOwnSn = new PerformanceReportProductOwnSn();
            performanceReportProductOwnSn.setPerformanceReportProductOwnId(performanceReportProductOwnDTO.getId());
            performanceReportProductOwnSn.setPsn(performanceReportProductOwnSnDTO.getPsn());
            performanceReportProductOwnSn.setProductDeliveryId(performanceReportProductOwnSnDTO.getProductDeliveryId());
            performanceReportProductOwnSn.setStorageTime(performanceReportProductOwnSnDTO.getStorageTime());
            performanceReportProductOwnSnMapper.insert(performanceReportProductOwnSn);
        }
        if (StringUtils.isNotEmpty(performanceReportProductOwn.getReturnExchangeProcessInstanceId())){
            insertReturnExchangeNewProductSeparationHardwareSn(performanceReportProductOwn, 2);
        }
        List<String> list1 = performanceReportProductOwnSnDTOS.stream().map(PerformanceReportProductOwnSnDTO::getPsn).toList();
        if (StringUtils.isEmpty(performanceReportProductOwn.getReturnExchangeProcessInstanceId())){
            remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(1, supplierName, processNumber, list1);
        }else {
            remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(4, supplierName, processNumber, list1);
        }
        return true;
    }

    /**
     * 修改软件
     * @param performanceReportProductSeparationDTO
     * @return
     */
    @Override
    public boolean updatePerformanceReportProductSeparationSoft(PerformanceReportProductSeparationDTO performanceReportProductSeparationDTO) {
        PerformanceReportProductSeparation productSeparationSoft = getProductDetail(performanceReportProductSeparationDTO.getProductId());
        PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductSeparationDTO.getPerformanceReportProductId());
        BigDecimal consumePrice = performanceReportProductOwnMapper.selectPerformanceReportProductSeparationConsumePrice(performanceReportProductSeparationDTO.getPerformanceReportProductId(), performanceReportProductSeparationDTO.getHardwareSeparationId());
        if (consumePrice == null){
            throw new CrmException("无价格信息");
        }
        float osSystemNum = consumePrice.multiply(BigDecimal.valueOf(performanceReportProductSeparationDTO.getProductNum())).divide(performanceReportProductSeparationDTO.getSellInPrice(), 1, RoundingMode.HALF_UP).floatValue();
        float osRealityNum = consumePrice.multiply(BigDecimal.valueOf(performanceReportProductSeparationDTO.getProductNum())).divide(performanceReportProductSeparationDTO.getSellInPrice(), 0,RoundingMode.HALF_UP).floatValue();

        productSeparationSoft.setProductClassification(2);
        productSeparationSoft.setId(performanceReportProductSeparationDTO.getId());
        productSeparationSoft.setPerformanceReportProductId(performanceReportProductSeparationDTO.getPerformanceReportProductId());
        productSeparationSoft.setIsDefault(performanceReportProductSeparationDTO.getIsDefault());
        productSeparationSoft.setHardwareSeparationId(performanceReportProductSeparationDTO.getHardwareSeparationId());
        productSeparationSoft.setConsumePrice(consumePrice);
        productSeparationSoft.setOsRealityNum(osRealityNum);
        productSeparationSoft.setOsSystemNum(osSystemNum);
        productSeparationSoft.setProductNum(performanceReportProductSeparationDTO.getProductNum());
        BigDecimal sellInPrice = performanceReportProductSeparationDTO.getSellInPrice();
        if (performanceReportProductOwn.getProductLic() > 0){
            sellInPrice = sellInPrice.multiply(new BigDecimal(performanceReportProductOwn.getProductLic()));
        }
        productSeparationSoft.setSellInPrice(sellInPrice);
        BigDecimal num = BigDecimal.valueOf(osRealityNum - osSystemNum);
        productSeparationSoft.setOsDifferencePrice(sellInPrice.multiply(num));
        performanceReportProductSeparationMapper.updateById(productSeparationSoft);

        return true;
    }

    /**
     * 查询产品详细信息
     * @param productId 软硬件产品id
     * @return
     */
    public PerformanceReportProductSeparation getProductDetail(String productId){
        if (productId == null || Objects.equals(productId, "")){
            throw new CrmException("无产品信息");
        }
        CrmProductVo crmProductVo = Optional.ofNullable(remoteProductService.getProduct(productId)).map(JsonObject::getObjEntity).orElse(null);

        PerformanceReportProductSeparation performanceReportProductSeparation = new PerformanceReportProductSeparation();
        performanceReportProductSeparation.setProductId(productId);
        if (crmProductVo != null) {
            //performanceReportProductSeparation.setProductClassification(crmProductVo.getForm());
            performanceReportProductSeparation.setStuffCode(crmProductVo.getMaterialCode());
            performanceReportProductSeparation.setProductSpecification(crmProductVo.getSpecificationId());
            performanceReportProductSeparation.setPnCode(crmProductVo.getPn());
            performanceReportProductSeparation.setQuotedPrice(crmProductVo.getPrice());
            //performanceReportProductSeparation.setSellInPrice(crmProductVo.getSellinPrice());
            performanceReportProductSeparation.setProductName(crmProductVo.getName());
        }else{
            throw new CrmException("无产品信息");
        }
        return performanceReportProductSeparation;
    }

    /**
     * 删除匹配信息 并返回匹配软硬分离关系
     * @param performanceReportProductOwn
     * @param crmProductSeparationRelQuery
     * @return
     */
    public List<CrmProductSeparationRelVo> getCrmProductSeparationInfo(PerformanceReportProductOwn performanceReportProductOwn, CrmProductSeparationRelQuery crmProductSeparationRelQuery){
        // 删除匹配的软硬产品
        deletePerformanceReportProductSn(performanceReportProductOwn.getId());
        performanceReportProductSeparationMapper.deletePerformanceReportProductSeparation(performanceReportProductOwn.getId());

        // 查询软硬分离关系条件
        crmProductSeparationRelQuery.setProductNum(performanceReportProductOwn.getProductNum());
        crmProductSeparationRelQuery.setSeparationType(1);
        // 查询所有硬件匹配关系
        List<CrmProductSeparationRelVo> crmProductSeparationRelVos = remoteProductSeparationRelService.getCrmProductSeparationInfo(crmProductSeparationRelQuery).getObjEntity();
        return crmProductSeparationRelVos;
    }

    /**
     * 写入硬件产品和序列号
     *
     * @param performanceReportProductId
     * @param supplierName
     * @param collect
     * @param productHardNum
     * @param approvalNode
     * @param processNumber
     * @param returnExchangeProcessInstanceId
     * @return 剩余硬件产品数量
     */
    public Integer insertPerformanceReportProductSeparationHardwareInfo(String performanceReportProductId, String supplierName, List<CrmProductSeparationRelVo> collect, Integer productHardNum, String approvalNode, String processNumber, String returnExchangeProcessInstanceId){
        // 是否可拆分
        //int separationSplit = performanceReportProductOwnMapper.selectPerformanceReportProductSeparationSplit(performanceReportProductId);
        PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductId);
        // 剩余硬件产品数量
        //Integer productHardNum = performanceReportProductOwn.getProductNum();

        for (CrmProductSeparationRelVo crmProductSeparationRelVo : collect){
            if (productHardNum > 0) {
                // 硬件产品信息
                BigDecimal sellinPrice = crmProductSeparationRelVo.getSellinPrice();
                if (performanceReportProductOwn.getProductLic() > 0){
                    sellinPrice = sellinPrice.multiply(new BigDecimal(performanceReportProductOwn.getProductLic()));
                }
                PerformanceReportProductSeparation performanceReportProductSeparation = getProductDetail(crmProductSeparationRelVo.getSeparationProductId());
                performanceReportProductSeparation.setProductClassification(1);
                performanceReportProductSeparation.setPerformanceReportProductId(performanceReportProductId);
                performanceReportProductSeparation.setIsDefault(crmProductSeparationRelVo.getIsDefault());
                // 成交单价
                BigDecimal quotedPrice = performanceReportProductSeparationMapper.selectQuotedPrice(performanceReportProductId);
                if (quotedPrice.compareTo(sellinPrice) > 0){
                    performanceReportProductSeparation.setQuotedPrice(sellinPrice);
                }else {
                    performanceReportProductSeparation.setQuotedPrice(quotedPrice);
                }
                performanceReportProductSeparation.setSellInPrice(sellinPrice);
                // 价差
                BigDecimal subtract = performanceReportProductSeparation.getSellInPrice().subtract(performanceReportProductSeparation.getQuotedPrice());

                Integer useNum;
                if (crmProductSeparationRelVo.getAvailableInventoryNum() >= productHardNum){
                    useNum = productHardNum;
                }else {
                    useNum = crmProductSeparationRelVo.getAvailableInventoryNum();
                }
                List<CrmProductAgentInventoryVO> crmProductAgentInventoryVOS = Optional.ofNullable(remoteProductAgentInventoryService.getCrmProductAgentInventoryInfo(performanceReportProductSeparation.getStuffCode(), supplierName, useNum, crmProductSeparationRelVo.getSellinPrice())).map(JsonObject::getObjEntity).orElse(null);
                // 是否可关联硬件序列号
                if (crmProductAgentInventoryVOS != null && !crmProductAgentInventoryVOS.isEmpty()) {
                    // 写入硬件产品
                    performanceReportProductSeparation.setProductNum(crmProductAgentInventoryVOS.size());
                    // 价差
                    BigDecimal num = BigDecimal.valueOf(crmProductAgentInventoryVOS.size());
                    performanceReportProductSeparation.setPriceDifference(subtract.multiply(num));
                    performanceReportProductSeparationMapper.insert(performanceReportProductSeparation);
                    // 关联硬件序列号
                    if (returnExchangeProcessInstanceId != null || Objects.equals(approvalNode, "06")) {
                        for (CrmProductAgentInventoryVO crmProductAgentInventoryVO : crmProductAgentInventoryVOS) {
                            insertPerformanceReportProductSeparationHardwareSn(performanceReportProductId, performanceReportProductSeparation, crmProductAgentInventoryVO);
                        }
                        // 锁定库存
                        List<String> list = crmProductAgentInventoryVOS.stream().map(CrmProductAgentInventoryVO::getInSerialNumber).toList();
                        if (StringUtils.isEmpty(performanceReportProductOwn.getReturnExchangeProcessInstanceId())){
                            remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(1, supplierName, processNumber, list);
                        }else {
                            remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(4, supplierName, processNumber, list);
                        }
                    }
                } else {
                    // 写入硬件产品
                    performanceReportProductSeparation.setProductNum(productHardNum);
                    // 价差
                    BigDecimal num = BigDecimal.valueOf(productHardNum);
                    performanceReportProductSeparation.setPriceDifference(subtract.multiply(num));
                    performanceReportProductSeparationMapper.insert(performanceReportProductSeparation);
                }
                productHardNum = productHardNum - performanceReportProductSeparation.getProductNum();
            }else {
                break;
            }
        }
        return productHardNum;
    }

    /**
     * 硬件产品序列号
     * @param performanceReportProductId
     * @param performanceReportProductSeparation
     * @param crmProductAgentInventoryVO
     */
    public void insertPerformanceReportProductSeparationHardwareSn(String performanceReportProductId, PerformanceReportProductSeparation performanceReportProductSeparation, CrmProductAgentInventoryVO crmProductAgentInventoryVO) {
        PerformanceReportProductSeparationSn performanceReportProductSeparationSn = new PerformanceReportProductSeparationSn();
        performanceReportProductSeparationSn.setPerformanceReportProductSeparationId(performanceReportProductSeparation.getId());
        performanceReportProductSeparationSn.setPerformanceReportProductId(performanceReportProductId);
        performanceReportProductSeparationSn.setPsn(crmProductAgentInventoryVO.getInSerialNumber());
        performanceReportProductSeparationSn.setProductDeliveryId(crmProductAgentInventoryVO.getProductDeliveryId());
        performanceReportProductSeparationSn.setStorageTime(crmProductAgentInventoryVO.getAcceptanceTime());
        performanceReportProductSeparationSnMapper.insert(performanceReportProductSeparationSn);
    }

    /**
     * 写入业绩上报退换货 硬件产品序列号
     */
    public void insertReturnExchangeNewProductSeparationHardwareSn(PerformanceReportProductOwn performanceReportProductOwn, Integer supplierType) {
        List<ReturnExchangeProduct> returnExchangeProductList = new ArrayList<>();
        ReturnExchangeProduct one = returnExchangeProductService.getOne(new LambdaUpdateWrapper<ReturnExchangeProduct>().eq(ReturnExchangeProduct::getRecordId, performanceReportProductOwn.getProjectRecordId())
                .eq(ReturnExchangeProduct::getProcessInstanceId, performanceReportProductOwn.getReturnExchangeProcessInstanceId()));
        List<CrmProjectProductSnVO> projectProductSnVOList = new ArrayList<>();
        if (supplierType == 1){
            List<PerformanceReportProductSeparationSn> performanceReportProductSeparationSns = performanceReportProductSeparationSnMapper.selectList(new LambdaQueryWrapper<PerformanceReportProductSeparationSn>()
                    .eq(PerformanceReportProductSeparationSn::getPerformanceReportProductId, performanceReportProductOwn.getId()));
            for (PerformanceReportProductSeparationSn performanceReportProductSeparationSn : ListUtils.emptyIfNull(performanceReportProductSeparationSns)) {
                CrmProjectProductSnVO crmProjectProductSnVO = new CrmProjectProductSnVO();
                crmProjectProductSnVO.setPsn(performanceReportProductSeparationSn.getPsn());
                crmProjectProductSnVO.setType("业绩上报");
                projectProductSnVOList.add(crmProjectProductSnVO);
            }
        }else if (supplierType == 2){
            List<PerformanceReportProductOwnSn> performanceReportProductSeparationSns = performanceReportProductOwnSnMapper.selectList(new LambdaQueryWrapper<PerformanceReportProductOwnSn>()
                    .eq(PerformanceReportProductOwnSn::getPerformanceReportProductOwnId, performanceReportProductOwn.getId()));
            for (PerformanceReportProductOwnSn performanceReportProductSeparationSn : ListUtils.emptyIfNull(performanceReportProductSeparationSns)) {
                CrmProjectProductSnVO crmProjectProductSnVO = new CrmProjectProductSnVO();
                crmProjectProductSnVO.setPsn(performanceReportProductSeparationSn.getPsn());
                crmProjectProductSnVO.setType("业绩上报");
                projectProductSnVOList.add(crmProjectProductSnVO);
            }
        }
        one.setPsn(projectProductSnVOList);
        returnExchangeProductList.add(one);
        returnExchangeProductService.updateProcessProductSn(returnExchangeProductList);
    }

    /**
     * 补默认硬件产品
     * @param productHardNum
     * @param performanceReportProductId
     * @param stuffCode
     * @return
     */
    public PerformanceReportProductSeparation insertPerformanceReportProductSeparationDefaultHardware(Integer productHardNum, String performanceReportProductId, String stuffCode){
        // 补默认硬件产品(不包括借转销专项备货硬件)
        if (productHardNum != 0){
            PerformanceReportProductSeparation performanceReportProductSeparation = performanceReportProductSeparationMapper.selectOne(new QueryWrapper<PerformanceReportProductSeparation>()
                    .eq("performance_report_product_id", performanceReportProductId)
                    .eq("product_classification", 1).eq("is_default", 1).eq("del_flag", 0)
                    .and(performanceReportProductSeparationQueryWrapper -> performanceReportProductSeparationQueryWrapper.isNull("borrow_for_forward").or().eq("borrow_for_forward", 0))
                    .and(performanceReportProductSeparationQueryWrapper -> performanceReportProductSeparationQueryWrapper.isNull("special_item").or().eq("special_item", 0)));;
            // 查询默认硬件
            CrmProductSeparationRelVo crmProductSeparationRelVoDefault = remoteProductSeparationRelService.getCrmProductSeparationRelDefaultHardware(stuffCode).getObjEntity();

            if ((performanceReportProductSeparation == null && productHardNum < 0) || (performanceReportProductSeparation != null && performanceReportProductSeparation.getProductNum() + productHardNum < 0)){
                throw new CrmException("序列号数量不符");
            }
            if (performanceReportProductSeparation != null && performanceReportProductSeparation.getSellInPrice().compareTo(crmProductSeparationRelVoDefault.getSellinPrice()) == 0){
                // 删除软件
                PerformanceReportProductSeparation performanceReportProductSeparationSoft = performanceReportProductSeparationMapper.selectOne(new QueryWrapper<PerformanceReportProductSeparation>().eq("hardware_separation_id", performanceReportProductSeparation.getId()).eq("del_flag", 0));
                if (performanceReportProductSeparationSoft != null){
                    performanceReportProductSeparationMapper.deleteById(performanceReportProductSeparationSoft.getId(),true);
                }
                if (performanceReportProductSeparation.getProductNum() + productHardNum == 0){
                    performanceReportProductSeparationMapper.deleteById(performanceReportProductSeparation.getId(),true);
                    return null;
                }
                performanceReportProductSeparation.setProductNum(performanceReportProductSeparation.getProductNum() + productHardNum);
                // 价差
                BigDecimal subtract = performanceReportProductSeparation.getSellInPrice().subtract(performanceReportProductSeparation.getQuotedPrice());
                BigDecimal num = BigDecimal.valueOf(performanceReportProductSeparation.getProductNum());
                performanceReportProductSeparation.setPriceDifference(subtract.multiply(num));
                performanceReportProductSeparationMapper.updateById(performanceReportProductSeparation);

                return performanceReportProductSeparation;
            }else {
                if (crmProductSeparationRelVoDefault == null){
                    throw new CrmException("无软硬分离匹配关系");
                }
                PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductId);
                PerformanceReportProductSeparation performanceReportProductSeparationDefault = getProductDetail(crmProductSeparationRelVoDefault.getSeparationProductId());
                performanceReportProductSeparationDefault.setProductClassification(1);
                performanceReportProductSeparationDefault.setPerformanceReportProductId(performanceReportProductId);
                performanceReportProductSeparationDefault.setIsDefault(1);
                performanceReportProductSeparationDefault.setPriceDifference(BigDecimal.ZERO);
                performanceReportProductSeparationDefault.setProductNum(productHardNum);
                // 成交单价
                BigDecimal quotedPrice = performanceReportProductSeparationMapper.selectQuotedPrice(performanceReportProductId);
                BigDecimal sellinPrice = crmProductSeparationRelVoDefault.getSellinPrice();
                if (performanceReportProductOwn.getProductLic() > 0){
                    sellinPrice = sellinPrice.multiply(new BigDecimal(performanceReportProductOwn.getProductLic()));
                }
                if (quotedPrice.compareTo(sellinPrice) > 0){
                    performanceReportProductSeparation.setQuotedPrice(sellinPrice);
                }else {
                    performanceReportProductSeparation.setQuotedPrice(quotedPrice);
                }
                performanceReportProductSeparationDefault.setSellInPrice(sellinPrice);
                // 价差
                BigDecimal subtract = performanceReportProductSeparationDefault.getSellInPrice().subtract(performanceReportProductSeparationDefault.getQuotedPrice());
                BigDecimal num = BigDecimal.valueOf(productHardNum);
                performanceReportProductSeparation.setPriceDifference(subtract.multiply(num));
                performanceReportProductSeparationMapper.insert(performanceReportProductSeparationDefault);
                return performanceReportProductSeparationDefault;
            }

        }
        return null;
    }

    /**
     * 写入软件产品
     * @param performanceReportProductId 自有产品id
     * @param performanceReportProductSeparation 硬件产品对象
     * @param crmProductSeparationRelQuery 查询软件库存信息
     */
    public void insertPerformanceReportProductSeparationSoft(String performanceReportProductId, PerformanceReportProductSeparation performanceReportProductSeparation, CrmProductSeparationRelQuery crmProductSeparationRelQuery){
        BigDecimal consumePrice = performanceReportProductOwnMapper.selectPerformanceReportProductSeparationConsumePrice(performanceReportProductId, performanceReportProductSeparation.getId());
        PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductId);
        crmProductSeparationRelQuery.setSeparationType(2);
        crmProductSeparationRelQuery.setConsumePrice(consumePrice);
        crmProductSeparationRelQuery.setProductNum(performanceReportProductSeparation.getProductNum());
        CrmProductSeparationRelVo crmProductSeparationRelSoft = remoteProductSeparationRelService.getCrmProductSeparationInfo(crmProductSeparationRelQuery).getObjEntity().stream().filter(crmProductSeparationRelVo -> crmProductSeparationRelVo.getSerialNumber() == 1).findFirst().orElse(null);

        if (crmProductSeparationRelSoft == null){
            throw new CrmException("无软硬分离匹配关系");
        }
        PerformanceReportProductSeparation productSoftDetail = getProductDetail(crmProductSeparationRelSoft.getSeparationProductId());
        productSoftDetail.setProductClassification(2);
        productSoftDetail.setPerformanceReportProductId(performanceReportProductId);
        productSoftDetail.setIsDefault(crmProductSeparationRelSoft.getIsDefault());
        productSoftDetail.setHardwareSeparationId(performanceReportProductSeparation.getId());
        productSoftDetail.setConsumePrice(consumePrice);
        productSoftDetail.setOsSystemNum(crmProductSeparationRelSoft.getOsSystemNum());
        productSoftDetail.setOsRealityNum(crmProductSeparationRelSoft.getOsRealityNum());
        productSoftDetail.setProductNum(performanceReportProductSeparation.getProductNum());
        BigDecimal sellinPrice = crmProductSeparationRelSoft.getSellinPrice();
        if (performanceReportProductOwn.getProductLic() > 0){
            sellinPrice = sellinPrice.multiply(new BigDecimal(performanceReportProductOwn.getProductLic()));
        }
        productSoftDetail.setSellInPrice(sellinPrice);

        BigDecimal num = BigDecimal.valueOf(crmProductSeparationRelSoft.getOsRealityNum() - crmProductSeparationRelSoft.getOsSystemNum());
        productSoftDetail.setOsDifferencePrice(sellinPrice.multiply(num));
        performanceReportProductSeparationMapper.insert(productSoftDetail);
    }

    @Override
    public List<CrmProductAgentInventoryVO> getPerformanceReportProductSeparationSn(PerformanceReportProductQuery performanceReportProductQuery) {
        PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductQuery.getPerformanceReportProductId());
        PerformanceReport performanceReport = performanceReportMapper.selectOne(new LambdaQueryWrapper<PerformanceReport>()
                .eq(PerformanceReport::getProcessInstanceId, performanceReportProductQuery.getProcessInstanceId())
                .eq(PerformanceReport::getDelFlag, 0)
                .last("limit 1"));
        Integer supplierType = performanceReport.getSupplierType();
        CrmProductSeparationRelQuery crmProductSeparationRelQuery = new CrmProductSeparationRelQuery();
        crmProductSeparationRelQuery.setStuffCode(performanceReportProductQuery.getStuffCode());
        crmProductSeparationRelQuery.setContractCompanyName(performanceReportProductQuery.getSupplierName());
        crmProductSeparationRelQuery.setPerformanceReportProductId(performanceReportProductQuery.getPerformanceReportProductId());
        crmProductSeparationRelQuery.setSupplierType(supplierType);
        crmProductSeparationRelQuery.setProcessNumber(performanceReport.getProcessNumber());
        if (supplierType == 1){
            PerformanceReportProductSeparation performanceReportProductSeparation = performanceReportProductSeparationMapper.selectById(performanceReportProductQuery.getPerformanceReportProductSeparationId());
            if (performanceReportProductSeparation == null){
                throw new CrmException("无序列号信息");
            }
            crmProductSeparationRelQuery.setConsumePrice(performanceReportProductSeparation.getSellInPrice());
            crmProductSeparationRelQuery.setProductLic(performanceReportProductOwn.getProductLic());
        }else if (supplierType == 2){
        }
        List<CrmProductAgentInventoryVO> crmProductAgentInventoryVOS = Optional.ofNullable(remoteProductAgentInventoryService.getPerformanceReportProductSeparationSn(crmProductSeparationRelQuery).getObjEntity()).orElse(null);
        if (crmProductAgentInventoryVOS == null){
            throw new CrmException("无序列号信息");
        }
        return crmProductAgentInventoryVOS;
    }

    @Override
    public List<CrmProductSeparationRelVo> selectPerformanceReportProductSeparationHardware(CrmProductSeparationRelQuery crmProductSeparationRelQuery) {
        List<CrmProductSeparationRelVo> productSeparationRelVoList = remoteProductSeparationRelService.getCrmProductSeparationRelHardware(crmProductSeparationRelQuery).getObjEntity();
        if (productSeparationRelVoList == null){
            throw new CrmException("无硬件信息");
        }
        return productSeparationRelVoList;
    }

    @Override
    public List<CrmProductSeparationRelVo> selectPerformanceReportProductSeparationSoft(String supplierName) {
        List<CrmProductSeparationRelVo> productSeparationRelVoList = remoteProductSeparationRelService.getCrmProductSeparationRelSoft(supplierName).getObjEntity();
        if (productSeparationRelVoList == null){
            throw new CrmException("无软件信息");
        }
        return productSeparationRelVoList;
    }

    @Override
    public boolean deletePerformanceReportProductSn(String performanceReportProductId) {
        PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductId);
        PerformanceReport performanceReport = performanceReportMapper.selectOne(new QueryWrapper<PerformanceReport>().eq("process_instance_id", performanceReportProductOwn.getProcessInstanceId()));
        List<PerformanceReportProductOwnSn> performanceReportProductOwnSnList = performanceReportProductOwnSnMapper.selectList(new QueryWrapper<PerformanceReportProductOwnSn>().eq("performance_report_product_own_id", performanceReportProductId).eq("del_flag", 0));
        List<String> list = ListUtils.emptyIfNull(performanceReportProductOwnSnList).stream().map(PerformanceReportProductOwnSn::getPsn).toList();
        // 删除序列号，解除锁定
        performanceReportProductOwnSnMapper.deletePerformanceReportProductSn(performanceReportProductId);
        if (!list.isEmpty()){
            if (StringUtils.isEmpty(performanceReportProductOwn.getReturnExchangeProcessInstanceId())){
                remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(1, performanceReport.getSupplierName(), performanceReport.getProcessNumber(), list);
            }else {
                remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(4, performanceReport.getSupplierName(), performanceReport.getProcessNumber(), list);
            }
        }

        List<PerformanceReportProductSeparationSn> performanceReportProductSeparationSns = performanceReportProductSeparationSnMapper.selectList(new QueryWrapper<PerformanceReportProductSeparationSn>().eq("performance_report_product_id", performanceReportProductId).eq("del_flag", 0));
        List<String> list1 = ListUtils.emptyIfNull(performanceReportProductSeparationSns).stream().map(PerformanceReportProductSeparationSn::getPsn).toList();
        performanceReportProductSeparationSnMapper.deletePerformanceReportProductSeparationSn(performanceReportProductId);
        if (!list1.isEmpty()){
            if (StringUtils.isEmpty(performanceReportProductOwn.getReturnExchangeProcessInstanceId())){
                remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(1, performanceReport.getSupplierName(), performanceReport.getProcessNumber(), list1);
            }else {
                remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(4, performanceReport.getSupplierName(), performanceReport.getProcessNumber(), list1);
            }
        }
        performanceReportContractDeliveryDetailMapper.deleteContractDeliveryDetail(performanceReportProductOwn.getProcessInstanceId());
        return true;
    }

    @Override
    public Integer selectBorrowForForwardOrSpecialItemNum(String performanceReportProductId) {
        PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductId);
        List<CrmProjectProductSnVO> projectProductSnVOList = remoteProjectProductSnClient.getProductSn(performanceReportProductOwn.getProjectRecordId()).getObjEntity();
        return projectProductSnVOList.size();
    }

    @Override
    public List<PerformanceReportProductListVO> exportPerformanceReportProductList(String processInstanceId, String returnExchangeProcessInstanceId) {
        List<PerformanceReportProductOwn> performanceReportProductOwnList = list(new QueryWrapper<PerformanceReportProductOwn>()
                .eq("process_instance_id", processInstanceId)
                .eq("del_flag", 0)
                .eq(StringUtils.isNotEmpty(returnExchangeProcessInstanceId), "return_exchange_process_instance_id", returnExchangeProcessInstanceId)
                .orderByAsc("parent_id","create_time"));
        List<PerformanceReportProductListVO> performanceReportProductListVOS = new ArrayList<>();
        // 自有产品
        List<CrmProductWholesalePriceVO> crmProductWholesalePriceVOS = remoteProductWholesalePriceService.batchGetInfo(performanceReportProductOwnList.stream().map(PerformanceReportProductOwn::getStuffCode).collect(Collectors.toList())).getObjEntity();
        int groupId = 1;
        for (PerformanceReportProductOwn performanceReportProductOwn : performanceReportProductOwnList){
            // 硬件产品
            List<PerformanceReportProductSeparation> performanceReportProductSeparationList = performanceReportProductSeparationMapper.selectList(new QueryWrapper<PerformanceReportProductSeparation>()
                    .eq("performance_report_product_id", performanceReportProductOwn.getId())
                    .eq("product_classification", 1)
                    .eq("del_flag", 0));
            for (PerformanceReportProductSeparation performanceReportProductSeparation : performanceReportProductSeparationList){
                PerformanceReportProductListVO performanceReportProductListVO = new PerformanceReportProductListVO();
                performanceReportProductListVO.setStuffCode(performanceReportProductOwn.getStuffCode());
                performanceReportProductListVO.setProductName(performanceReportProductOwn.getProductName());
                performanceReportProductListVO.setHostSerialNumber(performanceReportProductOwn.getHostSerialNumber());
                if (performanceReportProductSeparation.getIsDefault() == 0){
                    performanceReportProductListVO.setType("备货物料代码");
                }else {
                    performanceReportProductListVO.setType("默认物料代码");
                }
                performanceReportProductListVO.setStuffCodeSeparation(performanceReportProductSeparation.getStuffCode());
                performanceReportProductListVO.setProductNameSeparation(performanceReportProductSeparation.getProductName());
                performanceReportProductListVO.setProductSpecification(performanceReportProductSeparation.getProductSpecification());
                performanceReportProductListVO.setPnCode(performanceReportProductSeparation.getPnCode());
                performanceReportProductListVO.setProductNum(performanceReportProductSeparation.getProductNum());
                performanceReportProductListVO.setQuotedPrice(performanceReportProductSeparation.getQuotedPrice());
                // 序列号
                List<PerformanceReportProductSeparationSn> performanceReportProductSeparationSns = performanceReportProductSeparationSnMapper.selectList(new QueryWrapper<PerformanceReportProductSeparationSn>()
                        .eq("performance_report_product_separation_id", performanceReportProductSeparation.getId())
                        .eq("del_flag", 0));
                List<String> stringList = ListUtils.emptyIfNull(performanceReportProductSeparationSns).stream().map(PerformanceReportProductSeparationSn::getPsn).collect(Collectors.toList());
                String join = StringUtils.join(stringList, ",");
                performanceReportProductListVO.setSerialNumber(join);
                performanceReportProductListVO.setId(performanceReportProductSeparation.getId());

                CrmProductWholesalePriceVO crmProductWholesalePriceVO1 = crmProductWholesalePriceVOS.stream().filter(crmProductWholesalePriceVO -> Objects.equals(crmProductWholesalePriceVO.getStuffCode(), performanceReportProductOwn.getStuffCode())).findFirst().orElse(null);
                if (crmProductWholesalePriceVO1 != null){
                    boolean b = performanceReportProductOwn.getDealPrice().add(Optional.ofNullable(performanceReportProductOwn.getRebatePrice()).orElse(BigDecimal.ZERO)).compareTo(crmProductWholesalePriceVO1.getSellinPrice()) < 0;
                    performanceReportProductListVO.setSpecialPrice(b ? "是" : "否");
                }
                if (StringUtils.isEmpty(performanceReportProductOwn.getParentId()) || Objects.equals(performanceReportProductOwn.getParentId(), "0")){
                    performanceReportProductListVO.setGroupId(groupId);
                    groupId++;
                }else {
                    List<Integer> list = performanceReportProductListVOS.stream().filter(i -> Objects.equals(i.getPerformanceReportProductId(), performanceReportProductOwn.getParentId())).map(PerformanceReportProductListVO::getGroupId).toList();
                    performanceReportProductListVO.setGroupId(list.get(0));
                }
                performanceReportProductListVOS.add(performanceReportProductListVO);
            }
        }
        performanceReportProductListVOS.sort(new Comparator<PerformanceReportProductListVO>() {
            @Override
            public int compare(PerformanceReportProductListVO p1, PerformanceReportProductListVO p2) {
                return Integer.compare(p1.getGroupId(), p2.getGroupId()); // 按年龄升序排序
            }
        });
        return performanceReportProductListVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean importPerformanceReportProductList(MultipartFile file) throws Exception {
        String filename = file.getOriginalFilename();
        //异常信息集合
        List<String> errorMessageList = new ArrayList<>();
        if (filename == null || !filename.endsWith(".xls") && !filename.endsWith(".xlsx")) {
            throw new CrmException("请上传后缀为.xls或.xlsx的excel文件");
        }
        InputStream inputStream = file.getInputStream();
        ExcelUtil<PerformanceReportProductListVO> excelUtil = new ExcelUtil<>(PerformanceReportProductListVO.class);
        List<PerformanceReportProductListVO> performanceReportProductListVOS = excelUtil.importExcel("产品清单", inputStream);
        List<PerformanceReportProductListVO> performanceReportProductListVOList = performanceReportProductListVOS.stream().filter(performanceReportProductListVO -> performanceReportProductListVO.getId() != null && !performanceReportProductListVO.getId().isEmpty()).toList();
        List<PerformanceReportProductListVO> productListVOList = new ArrayList<>();
        if (!performanceReportProductListVOList.isEmpty()) {
            for (PerformanceReportProductListVO productListVO : performanceReportProductListVOList) {
                PerformanceReportProductSeparation performanceReportProductSeparation = performanceReportProductSeparationMapper.selectOne(new QueryWrapper<PerformanceReportProductSeparation>().eq("id", productListVO.getId()).eq("stuff_code", productListVO.getStuffCodeSeparation()));
                if (performanceReportProductSeparation != null && performanceReportProductSeparation.getBorrowForForward() == null && performanceReportProductSeparation.getSpecialItem() == null){
                    if (org.apache.commons.lang3.StringUtils.isBlank(productListVO.getSerialNumber())){
                        throw new CrmException(productListVO.getStuffCodeSeparation() + "序列号为必填项");
                    }else {
                        PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductSeparation.getPerformanceReportProductId());
                        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductOwn.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
                        String[] split = productListVO.getSerialNumber().split(",");
                        List<String> list = Arrays.asList(split);
                        if (list.size() != performanceReportProductSeparation.getProductNum()){
                            throw new CrmException(performanceReportProductOwn.getStuffCode() + "的硬件物料代码" + productListVO.getStuffCodeSeparation() + " 序列号数量和硬件产品数量不相等");
                        }else {
                            List<String> stringList = list.stream().distinct().toList();
                            if (stringList.size() != performanceReportProductSeparation.getProductNum()){
                                throw new CrmException(performanceReportProductOwn.getStuffCode() + "的硬件物料代码" + productListVO.getStuffCodeSeparation() + "序列号存在重复");
                            }else {
                                PerformanceReport performanceReport = performanceReportMapper.selectOne(new QueryWrapper<PerformanceReport>().eq("process_instance_id", performanceReportProductOwn.getProcessInstanceId())
                                        .eq("del_flag", 0));
                                for (String string : stringList) {
                                    CrmProductAgentInventoryVO crmProductAgentInventoryVO = remoteProductAgentInventoryService.getProductSnInventoryInfo(performanceReportProductSeparation.getStuffCode(), performanceReport.getSupplierName(), string.trim(), performanceReportProductSeparation.getSellInPrice(), performanceReport.getSupplierType(), performanceReportProductSeparation.getPerformanceReportProductId(), performanceReport.getProcessNumber()).getObjEntity();
                                    if (crmProductAgentInventoryVO == null){
                                        throw new CrmException(performanceReportProductOwn.getStuffCode() + "的硬件物料代码" + productListVO.getStuffCodeSeparation() + "的序列号" + string + "无效;");
                                    }
                                }
                                productListVOList.add(productListVO);
                            }

                        }

                    }
                }else if (performanceReportProductSeparation == null){
                    throw new CrmException(productListVO.getId() +"该ID不存在，请重新导出");
                }
            }

            for (PerformanceReportProductListVO performanceReportProductListVO : ListUtils.emptyIfNull(productListVOList)) {
                String[] split = performanceReportProductListVO.getSerialNumber().split(",");
                List<String> list = Arrays.asList(split);
                PerformanceReportProductSeparation performanceReportProductSeparation = performanceReportProductSeparationMapper.selectOne(new QueryWrapper<PerformanceReportProductSeparation>().eq("id", performanceReportProductListVO.getId()).eq("stuff_code", performanceReportProductListVO.getStuffCodeSeparation()));
                PerformanceReportProductOwn performanceReportProductOwn = getById(performanceReportProductSeparation.getPerformanceReportProductId());
                PerformanceReport performanceReport = performanceReportMapper.selectOne(new QueryWrapper<PerformanceReport>().eq("process_instance_id", performanceReportProductOwn.getProcessInstanceId())
                        .eq("del_flag", 0));
                // 删除已关联序列号
                deletePerformanceReportProductSeparationSn(performanceReportProductListVO.getId(), performanceReport.getSupplierName(), performanceReport.getProcessNumber(), performanceReportProductOwn.getReturnExchangeProcessInstanceId());
                // 删除发货序列号信息
                performanceReportContractDeliveryDetailMapper.deleteContractDeliveryDetail(performanceReportProductOwn.getProcessInstanceId());
                for (String string : list) {
                    CrmProductAgentInventoryVO crmProductAgentInventoryVO = remoteProductAgentInventoryService.getProductSnInventoryInfo(performanceReportProductSeparation.getStuffCode(), performanceReport.getSupplierName(), string.trim(), performanceReportProductSeparation.getSellInPrice(), performanceReport.getSupplierType(), performanceReportProductSeparation.getPerformanceReportProductId(), performanceReport.getProcessNumber()).getObjEntity();
                    insertPerformanceReportProductSeparationHardwareSn(performanceReportProductSeparation.getPerformanceReportProductId(), performanceReportProductSeparation, crmProductAgentInventoryVO);
                }
                // 锁定库存
                if (StringUtils.isEmpty(performanceReportProductOwn.getReturnExchangeProcessInstanceId())){
                    remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(1, performanceReport.getSupplierName(), performanceReport.getProcessNumber(), list);
                }else {
                    remoteProductAgentInventoryService.lockProductAgentInventoryPerformanceReport(4, performanceReport.getSupplierName(), performanceReport.getProcessNumber(), list);
                }
            }
        }else {
            throw new CrmException("硬件物料代码为空");
        }
        return true;
    }

    @Override
    public boolean insertDeliverySnInfo(PerformanceReportContractDeliveryDetailDTO performanceReportContractDeliveryDetailDTO) {
        PerformanceReportContractDelivery performanceReportContractDelivery = performanceReportContractDeliveryService.getById(performanceReportContractDeliveryDetailDTO.getContractDeliveryId());
        List<ContractDeliveryProductVO> deliveryProducts = performanceReportContractDelivery.getDeliveryProducts();
        List<ContractDeliveryProductSnVO> contractDeliveryProductSnVOS = performanceReportContractDeliveryDetailDTO.getDeliveryProductSns();
        for (ContractDeliveryProductVO contractDeliveryProductVO : deliveryProducts){
            PerformanceReportProductOwn performanceReportProductOwn = getOne(new QueryWrapper<PerformanceReportProductOwn>().eq("project_record_id", contractDeliveryProductVO.getProjectProductRecordId()).eq("del_flag", 0));
            // 产品发货数量
            Integer productVONum = contractDeliveryProductVO.getNum();
            // 关联序列号数量
            List<ContractDeliveryProductSnVO> contractDeliveryProductSnVOList = contractDeliveryProductSnVOS.stream().filter(contractDeliveryProductSnVO -> Objects.equals(contractDeliveryProductSnVO.getPerformanceReportProductId(), performanceReportProductOwn.getId())).collect(Collectors.toList());
            if (productVONum < ListUtils.emptyIfNull(contractDeliveryProductSnVOList).size()){
                throw new CrmException(performanceReportProductOwn.getStuffCode() +"关联的序列号数量不能超过产品发货数量");
            }
        }
        PerformanceReportContractDeliveryDetail performanceReportContractDeliveryDetail = HyperBeanUtils.copyProperties(performanceReportContractDeliveryDetailDTO, PerformanceReportContractDeliveryDetail::new);
        performanceReportContractDeliveryDetailMapper.insert(performanceReportContractDeliveryDetail);
        return true;
    }

    @Override
    public List<DeliveryProductSnDTO> selectDeliverySnInfo(String contractDeliveryId) {
        // 发货序列号信息
        List<PerformanceReportContractDeliveryDetail> performanceReportContractDeliveryDetailList = performanceReportContractDeliveryDetailMapper.selectList(new QueryWrapper<PerformanceReportContractDeliveryDetail>().eq("contract_delivery_id", contractDeliveryId));
        List<DeliveryProductSnDTO> deliveryProductSnDTOS = new ArrayList<>();
        ListUtils.emptyIfNull(performanceReportContractDeliveryDetailList).forEach(performanceReportContractDeliveryDetail -> {
            DeliveryProductSnDTO deliveryProductSnDTO = new DeliveryProductSnDTO();
            deliveryProductSnDTO.setId(performanceReportContractDeliveryDetail.getId());
            deliveryProductSnDTO.setContractDeliveryId(performanceReportContractDeliveryDetail.getContractDeliveryId());
            deliveryProductSnDTO.setDeliveryNo(performanceReportContractDeliveryDetail.getDeliveryNo());
            deliveryProductSnDTO.setCompanyName(performanceReportContractDeliveryDetail.getCompanyName());
            deliveryProductSnDTO.setReceivingCity(performanceReportContractDeliveryDetail.getReceivingCity());
            deliveryProductSnDTO.setShippingCity(performanceReportContractDeliveryDetail.getShippingCity());
            List<DeliveryProductSnDTO.ProductInfo> productInfos = new ArrayList<>();
            // 序列号
            List<ContractDeliveryProductSnVO> deliveryProductSns = performanceReportContractDeliveryDetail.getDeliveryProductSns();
            for (ContractDeliveryProductSnVO contractDeliveryProductSnVO : deliveryProductSns){
                PerformanceReportProductOwn performanceReportProductOwn = getById(contractDeliveryProductSnVO.getPerformanceReportProductId());
                DeliveryProductSnDTO.ProductInfo productInfo = new DeliveryProductSnDTO.ProductInfo();
                productInfo.setPsn(contractDeliveryProductSnVO.getPsn());
                productInfo.setProductName(performanceReportProductOwn.getProductName());
                productInfo.setPnCode(performanceReportProductOwn.getPnCode());
                productInfos.add(productInfo);
            }
            deliveryProductSnDTO.setProductInfoList(productInfos);
            deliveryProductSnDTOS.add(deliveryProductSnDTO);
        });
        return deliveryProductSnDTOS;
    }

    @Override
    public List<ContractDeliveryProductSnVO> selectProductDeliverySnInfo(String contractDeliveryId) {
        // 发货信息
        PerformanceReportContractDelivery performanceReportContractDelivery = performanceReportContractDeliveryService.getById(contractDeliveryId);
        if (performanceReportContractDelivery == null) {
            throw new CrmException("合同发货不存在");
        }
        // 发货产品
        List<ContractDeliveryProductVO> deliveryProducts = performanceReportContractDelivery.getDeliveryProducts();
        List<String> performanceReportProductIds = new ArrayList<>();
        ListUtils.emptyIfNull(deliveryProducts).forEach(contractDeliveryProductVO -> {
            PerformanceReportProductOwn performanceReportProductOwn = getOne(new QueryWrapper<PerformanceReportProductOwn>().eq("project_record_id", contractDeliveryProductVO.getProjectProductRecordId()).eq("del_flag", 0));
            performanceReportProductIds.add(performanceReportProductOwn.getId());
        });
        // 已经关联的序列号
        List<String> psnList = selectContractDeliveryDetail(performanceReportContractDelivery.getProcessInstanceId());
        //
        if (psnList.isEmpty()){
            psnList = null;
        }
        PerformanceReport performanceReport = performanceReportMapper.selectOne(new QueryWrapper<PerformanceReport>().eq("process_instance_id", performanceReportContractDelivery.getProcessInstanceId()));
        List<ContractDeliveryProductSnVO> contractDeliveryProductSnVOList;
        if (performanceReport.getSupplierType() == 1){
            contractDeliveryProductSnVOList = performanceReportProductSeparationSnMapper.selectProductDeliverySnInfo(performanceReportProductIds, psnList);
        }else {
            contractDeliveryProductSnVOList = performanceReportProductOwnSnMapper.selectProductDeliverySnInfo(performanceReportProductIds, psnList);
        }
        return contractDeliveryProductSnVOList;
    }

    /**
     * 查询已关联合同发货序列号
     */
    private List<String> selectContractDeliveryDetail(String processInstanceId) {
        List<String> psnList = new ArrayList<>();
        List<PerformanceReportContractDelivery> performanceReportContractDeliveryList = performanceReportContractDeliveryService.list(new QueryWrapper<PerformanceReportContractDelivery>().eq("process_instance_id", processInstanceId));
        for (PerformanceReportContractDelivery performanceReportContractDelivery1 : performanceReportContractDeliveryList){
            List<PerformanceReportContractDeliveryDetail> performanceReportContractDeliveryDetail = performanceReportContractDeliveryDetailMapper.selectList(new QueryWrapper<PerformanceReportContractDeliveryDetail>().eq("contract_delivery_id", performanceReportContractDelivery1.getId()));
            for (PerformanceReportContractDeliveryDetail performanceReportContractDeliveryDetail1 : ListUtils.emptyIfNull(performanceReportContractDeliveryDetail)) {
                if (performanceReportContractDeliveryDetail1 != null) {
                    List<ContractDeliveryProductSnVO> deliveryProductSns = performanceReportContractDeliveryDetail1.getDeliveryProductSns();
                    psnList.addAll(deliveryProductSns.stream().map(ContractDeliveryProductSnVO::getPsn).collect(Collectors.toList()));
                }
            }
        }
        return psnList;
    }

    @Override
    public boolean deleteDeliverySnInfo(String contractDeliveryDetailId) {
        performanceReportContractDeliveryDetailMapper.deleteById(contractDeliveryDetailId);
        return true;
    }

    @Override
    public List<ContractDeliveryProductSnVO> importProductDeliverySn(MultipartFile file, String contractDeliveryId) throws Exception {
        String filename = file.getOriginalFilename();

        if (filename == null || !filename.endsWith(".xls") && !filename.endsWith(".xlsx")) {
            throw new CrmException("请上传后缀为.xls或.xlsx的excel文件");
        }
        InputStream inputStream = file.getInputStream();
        ExcelUtil<ProductDeliverySn> excelUtil = new ExcelUtil<>(ProductDeliverySn.class);
        List<ProductDeliverySn> psnVOList = excelUtil.importExcel("发货序列号", inputStream);
        if (psnVOList.isEmpty() || psnVOList.get(0) == null){
            throw new CrmException("序列号为空");
        }
        List<String> psnList = psnVOList.stream().map(ProductDeliverySn::getPsn).toList();
        Set<String> set = new HashSet<>();
        for (String string : psnList) {
            string = string.trim();
            if (!set.add(string)){
                throw new CrmException(string + "重复");
            }
        }
        List<ContractDeliveryProductSnVO> contractDeliveryProductSnVOS = selectProductDeliverySnInfo(contractDeliveryId);
        // 已经关联的序列号
        PerformanceReportContractDelivery performanceReportContractDelivery = performanceReportContractDeliveryService.getById(contractDeliveryId);
        List<String> stringList = selectContractDeliveryDetail(performanceReportContractDelivery.getProcessInstanceId());

        for (String psn : set){
            if (stringList.contains(psn)){
                throw new CrmException(psn + "导入重复");
            }
            List<ContractDeliveryProductSnVO> contractDeliveryProductSnVOList = contractDeliveryProductSnVOS.stream().filter(contractDeliveryProductSnVO -> Objects.equals(contractDeliveryProductSnVO.getPsn(), psn)).collect(Collectors.toList());
            if (contractDeliveryProductSnVOList.isEmpty()){
                throw new CrmException(psn + "无效");
            }
        }
        return contractDeliveryProductSnVOS.stream().filter(contractDeliveryProductSnVO -> set.contains(contractDeliveryProductSnVO.getPsn())).collect(Collectors.toList());
    }

    /**
     * 根据业绩上报单号查询使用了返点的自有产品list
     */
    @Override
    public List<PerformanceReportProductOwnDTO> selectProductOwnByProcessNumber(String processNumber) {
        List<PerformanceReportProductOwnDTO> performanceReportProductOwnDTOS = performanceReportProductOwnMapper.selectProductOwnByProcessNumber(processNumber);
        ListUtils.emptyIfNull(performanceReportProductOwnDTOS).forEach(performanceReportProductOwnDTO -> {
            // 价差
            BigDecimal bigDecimal = performanceReportProductOwnMapper.selectPriceDifference(performanceReportProductOwnDTO.getId());
            performanceReportProductOwnDTO.setPriceDifference(bigDecimal);
            // os使用返点
            BigDecimal bigDecimal1 = performanceReportProductOwnMapper.selectOSConsumePrice(performanceReportProductOwnDTO.getId());
            performanceReportProductOwnDTO.setReplaceSoftware(bigDecimal1);
        });
        return performanceReportProductOwnDTOS;
    }

    @Override
    public List<PerformanceReportProductOwnDTO> selectProductOwnByProjectIds(List<String> projectIds) {
        if (CollectionUtils.isEmpty(projectIds)) {
            return Collections.emptyList();
        }
        List<PerformanceReportProductOwnDTO> performanceReportProductOwnDTOS = new ArrayList<>();
        List<PerformanceReport> performanceReports = performanceReportMapper.selectList(new QueryWrapper<PerformanceReport>().in("project_id", projectIds).eq("del_flag", 0).eq("report_status", 1));
        ListUtils.emptyIfNull(performanceReports).forEach(performanceReport -> {
            List<PerformanceReportProductOwn> performanceReportProductOwnList = list(new QueryWrapper<PerformanceReportProductOwn>().eq("process_instance_id", performanceReport.getProcessInstanceId()).eq("del_flag", 0));
            List<PerformanceReportProductOwnDTO> performanceReportProductOwnDTOS1 = HyperBeanUtils.copyListPropertiesByJackson(performanceReportProductOwnList, PerformanceReportProductOwnDTO.class);
            for (PerformanceReportProductOwnDTO performanceReportProductOwnDTO : performanceReportProductOwnDTOS1){
                performanceReportProductOwnDTO.setProjectId(performanceReport.getProjectId());
            }
            performanceReportProductOwnDTOS.addAll(performanceReportProductOwnDTOS1);
        });
        return performanceReportProductOwnDTOS;
    }

}
