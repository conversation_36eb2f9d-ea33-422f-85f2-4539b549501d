package com.topsec.crm.flow.core.controller.salesAgreement;

import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementMembersRelVo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementMembersReviewMainLaunchVo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementReviewMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.SalesAgreementMembersReviewMain;
import com.topsec.crm.flow.core.entity.SalesAgreementReviewMain;
import com.topsec.crm.flow.core.process.impl.SalesAgreementMembersProcessService;
import com.topsec.crm.flow.core.service.SalesAgreementMembersRelService;
import com.topsec.crm.flow.core.service.SalesAgreementMembersReviewMainService;
import com.topsec.crm.flow.core.service.SalesAgreementReviewMainService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 协议成员表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-31 10:52:58
 */
@RestController
@RequestMapping("/salesAgreementMembers")
@Tag(name = "【销售协议-协议成员】", description = "salesAgreementMembers")
@RequiredArgsConstructor
@Validated
public class SalesAgreementMembersReviewMainController {
    private final SalesAgreementMembersProcessService salesAgreementMembersProcessService;
    private final SalesAgreementMembersRelService salesAgreementMembersRelService;
    private final SalesAgreementReviewMainService salesAgreementReviewMainService;
    private final SalesAgreementMembersReviewMainService salesAgreementMembersReviewMainService;




    @PostMapping("/launch")
    @Operation(summary = "发起协议成员变更流程")
    @PreFlowPermission
    public JsonObject<Boolean> launch(@Valid @RequestBody SalesAgreementMembersReviewMainLaunchVo salesAgreementMembersReviewMainLaunchVo) {
        return new JsonObject<>(salesAgreementMembersProcessService.launch(salesAgreementMembersReviewMainLaunchVo));
    }


    @GetMapping("/querySalesAgreementMembersBaseInfo")
    @Operation(summary = "协议成员变更基本信息查询")
    @PreFlowPermission
    public JsonObject<SalesAgreementReviewMainVo> querySalesAgreementMembersBaseInfo(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        SalesAgreementMembersReviewMain salesAgreementMembersReviewMain = salesAgreementMembersReviewMainService.querySalesAgreementMembersBaseInfo(processInstanceId);
        if (salesAgreementMembersReviewMain == null){
            throw  new CrmException("流程实例不存在");
        }
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(salesAgreementMembersReviewMain.getParentProcessInstanceId());
        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(salesAgreement, SalesAgreementReviewMainVo.class));
    }


    @GetMapping("/querySubSalesAgreementMembersList")
    @Operation(summary = "查询协议成员变更流程协议成员列表")
    @PreFlowPermission
    public JsonObject<List<SalesAgreementMembersRelVo>> querySubSalesAgreementMembersList(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<SalesAgreementMembersRelVo> salesAgreementMembersRelVos = salesAgreementMembersRelService.convertSubSalesAgreementMembersList(processInstanceId);
        return new JsonObject<>(salesAgreementMembersRelVos);
    }


    @GetMapping("/checkUnfinishedSalesAgreementMembersProcesses")
    @Operation(summary = "校验协议成员变更流程是否存在未办结的流程")
    public JsonObject<Boolean> checkUnfinishedSalesAgreementMembersProcesses(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        boolean flag = false;
        List<SalesAgreementMembersReviewMain> salesAgreementMembersReviewMains = salesAgreementMembersReviewMainService.querySalesAgreementMembersList(processInstanceId, 1);
        if (!salesAgreementMembersReviewMains.isEmpty()){
            flag = true;
        }
        return new JsonObject<>(flag);
    }








}
