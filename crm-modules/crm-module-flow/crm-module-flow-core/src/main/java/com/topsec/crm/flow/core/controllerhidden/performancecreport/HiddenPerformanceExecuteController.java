package com.topsec.crm.flow.core.controllerhidden.performancecreport;

import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteStatisticsVO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteVO;
import com.topsec.crm.flow.core.service.PerformanceExecuteService;
import com.topsec.crm.stats.api.entity.StatsAgentSaleVO;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/hidden/performanceExecute")
@Tag(name = "业绩执行", description = "/performanceExecute")
@RequiredArgsConstructor
public class HiddenPerformanceExecuteController {
    private final PerformanceExecuteService performanceReportService;

    @PostMapping("/saveOrUpdatePerformanceExecute")
    @Operation(summary = "业绩执行-新增或修改业绩执行信息")
    public JsonObject<Boolean> saveOrUpdatePerformanceExecute(){
        return new JsonObject<>(performanceReportService.saveOrUpdatePerformanceExecute());
    }
    @GetMapping("/getPerformanceExecuteStatisticsBySaleDept")
    @Operation(summary = "根据部门查询业绩执行的统计信息", method = "GET")
    public JsonObject<PerformanceExecuteStatisticsVO> getPerformanceExecuteStatisticsBySaleDept(@RequestParam String saleDeptId) {
        return new JsonObject<>(performanceReportService.getPerformanceExecuteStatisticsBySaleDept(saleDeptId));
    };

    @GetMapping("/getAgentContractTotalAmount")
    @Operation(summary ="统计业绩执行签约单位合同金额")
    public JsonObject<List<StatsAgentSaleVO>> getAgentContractTotalAmount(@RequestParam(required = false) String month){
        return new JsonObject<>(performanceReportService.getAgentContractTotalAmount(month));
    }


    @PostMapping("/updateWriteOff")
    @Operation(summary = "业绩执行-修改业绩执行回款相关信息")
    public JsonObject<Boolean> updateWriteOff(@RequestBody List<PerformanceExecuteVO> voList){
        return new JsonObject<>(performanceReportService.updateWriteOff(voList));
    }

    @GetMapping("/getCompanyNameDebtAmount")
    @Operation(summary = "获取签订公司欠款金额", method = "GET")
    public JsonObject<Map<String, BigDecimal>> getCompanyNameDebtAmount(){
        return new JsonObject<>(performanceReportService.getCompanyNameDebtAmount());
    }

}
