package com.topsec.crm.flow.core.controller.projectReport.business;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ProjectReportMain;
import com.topsec.crm.flow.core.service.IProjectReportMainService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.project.api.client.RemoteProjectAgentClient;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.entity.CrmProjectAgentVo;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/business")
@Tag(name = "转发渠道项目Controller", description = "/forward")
@RequiredArgsConstructor
@Validated
public class BusinessAgentController extends BaseController {

    private final RemoteProjectAgentClient remoteProjectAgentClient;
    private final IProjectReportMainService projectReportMainService;
    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    //查询项目报备的渠道项目详情
    @GetMapping("/agent/{processInstanceId}")
    @PreAuthorize(hasPermission = "crm_project_report",dataScope = "crm_project_report")
    JsonObject<CrmProjectAgentVo> getAgentProjectInfo(@PathVariable String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //查询当前流程的项目ID
        ProjectReportMain projectReportMain = projectReportMainService.query().eq("process_instance_id", processInstanceId).one();

        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        if(CollectionUtil.isNotEmpty(dataScopeParam.getPersonIdList()) && !dataScopeParam.getPersonIdList().contains(projectReportMain.getCreateUser())){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        String projectId = projectReportMain.getProjectId();
        return remoteProjectAgentClient.getInfo(projectId);
    }

    //查询项目报备的渠道项目详情
    @GetMapping("/directly/{processInstanceId}")
    @PreAuthorize(hasPermission = "crm_project_report",dataScope = "crm_project_report")
    JsonObject<CrmProjectDirectlyVo> getDirectlyProjectInfo(@PathVariable String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //查询当前流程的项目ID
        ProjectReportMain projectReportMain = projectReportMainService.query().eq("process_instance_id", processInstanceId).one();
        //权限校验
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        if(CollectionUtil.isNotEmpty(dataScopeParam.getPersonIdList()) && !dataScopeParam.getPersonIdList().contains(projectReportMain.getCreateUser())){
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        String projectId = projectReportMain.getProjectId();
        return remoteProjectDirectlyClient.getProjectInfo(projectId);
    }

}
