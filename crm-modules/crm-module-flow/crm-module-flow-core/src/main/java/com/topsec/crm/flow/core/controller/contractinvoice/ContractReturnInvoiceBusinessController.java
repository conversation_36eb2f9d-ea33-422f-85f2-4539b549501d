package com.topsec.crm.flow.core.controller.contractinvoice;

import com.github.pagehelper.PageHelper;
import com.topsec.crm.contract.api.ContractProductDetailQuery;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.CrmContractProductAllDetailVO;
import com.topsec.crm.contract.api.entity.CrmRevenueRecognitionVO;
import com.topsec.crm.flow.api.RemoteContractSignVerifyMainService;
import com.topsec.crm.flow.api.RemoteContractUnconfirmedDetailService;
import com.topsec.crm.flow.api.dto.contractInvoice.ContractInfoDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.ContractProductDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.InvoiceDocDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.returnInvoice.ContractReturnInvoiceDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.returnInvoice.MakeInvoiceInfoVO;
import com.topsec.crm.flow.api.dto.contractInvoice.returnInvoice.ReturnInvoicePageVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractSignVerifyAttachmentDTO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedDetailVo;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.core.process.impl.TfsProcessService;
import com.topsec.crm.flow.core.service.ContractInvoiceExtendService;
import com.topsec.crm.flow.core.service.ContractReturnInvoiceService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsTaskClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.task.TaskHistoryVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("business/contractReturnInvoice")
@Tag(name = "【合同退票】-业务",description = "business/contractReturnInvoice")
public class ContractReturnInvoiceBusinessController extends BaseController {

    private final ContractInvoiceExtendService extendService;
    private final ContractReturnInvoiceService returnInvoiceService;
    private final RemoteContractExecuteService remoteContractExecuteService;
    private final RemoteContractUnconfirmedDetailService remoteContractUnconfirmedDetailService;
    private final RemoteContractSignVerifyMainService remoteContractSignVerifyMainService;
    private final TfsTaskClient tfsTaskClient;
    private final TfsProcessService tfsProcessService;


    @PostMapping("/getReturnInvoicePage")
    @Operation(summary = "合同退票流程分页列表-列表")
    @PreAuthorize(hasPermission = "crm_contract_invoice_refund",dataScope = "crm_contract_invoice_refund")
    public JsonObject<TableDataInfo> getReturnInvoicePage(@RequestBody ReturnInvoicePageVO query){
        startPage();
        return new JsonObject<>(returnInvoiceService.getReturnInvoicePage(query));
    }

    @PostMapping("/exportReturnInvoicePage")
    @Operation(summary = "合同退票流程导出-列表")
    @PreAuthorize(hasPermission = "crm_contract_invoice_refund",dataScope = "crm_contract_invoice_refund")
    public void exportMakeInvoicePage(@RequestBody ReturnInvoicePageVO query) throws IOException {
        ExcelUtil<ReturnInvoicePageVO> excelUtil = new ExcelUtil<>(ReturnInvoicePageVO.class);
        List<ReturnInvoicePageVO> processList = returnInvoiceService.exportMakeInvoicePage(query);
        excelUtil.exportExcel(response, processList,"合同退票");
    }

    @GetMapping("/getContractInfo")
    @Operation(summary = "获取合同信息-详情",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_invoice_refund",dataScope = "crm_contract_invoice_refund")
    public JsonObject<ContractInfoDTO> getContractInfo(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(extendService.getContractInfo(contractNumber));
    }

    @GetMapping("/getMakeInvoiceInfoAfterLaunch")
    @Operation(summary = "获取开票信息-发起之后-详情",parameters = {@Parameter(name = "processInstanceId",description = "退票流程实例ID")})
    @PreAuthorize(hasPermission = "crm_contract_invoice_refund",dataScope = "crm_contract_invoice_refund")
    public JsonObject<MakeInvoiceInfoVO> getMakeInvoiceInfoAfterLaunch(@RequestParam String processInstanceId){
        return new JsonObject<>(extendService.getLastMakeInvoiceInfo(processInstanceId));
    }

    @GetMapping("/getProcessInfo")
    @Operation(summary = "获取合同退票流程信息",parameters = {@Parameter(name = "processInstanceId",description = "退票流程实例ID")})
    @PreAuthorize(hasPermission = "crm_contract_invoice_refund",dataScope = "crm_contract_invoice_refund")
    public JsonObject<ContractReturnInvoiceDTO> getProcessInfo(@RequestParam String processInstanceId){
        return new JsonObject<>(returnInvoiceService.getProcessInfo(processInstanceId));
    }

    @GetMapping("/getRevenueRecognition")
    @Operation(summary = "获取收入确认列表-详情",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_invoice_refund",dataScope = "crm_contract_invoice_refund")
    public JsonObject<PageUtils<CrmRevenueRecognitionVO>> getRevenueRecognition(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        PageUtils<CrmRevenueRecognitionVO> objEntity = remoteContractExecuteService.pageRevenueRecognitionByContractNumber(contractNumber).getObjEntity();
        return new JsonObject<>(objEntity);
    }

    @GetMapping("/getPaymentTerms")
    @Operation(summary = "获取付款条款-详情",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_invoice_refund",dataScope = "crm_contract_invoice_refund")
    public JsonObject<List<PaymentProvisionDTO>> getPaymentTerms(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        List<PaymentProvisionDTO> list = remoteContractExecuteService.pagePaymentProvisionByContractNumber(contractNumber).getObjEntity().getList();
        return new JsonObject<>(Objects.requireNonNullElseGet(list, ArrayList::new));
    }

    @GetMapping("/getMakeInvoiceDoc")
    @Operation(summary = "获取电汇底单及其他单据",description ="附件",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_invoice_refund",dataScope = "crm_contract_invoice_refund")
    public JsonObject<List<InvoiceDocDTO>> getMakeInvoiceDoc(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(extendService.getMakeInvoiceDoc(contractNumber));
    }

    @GetMapping("/getAttach")
    @Operation(summary = "获取签验收单据-详情",description ="附件",parameters = {@Parameter(name = "contractNumber",description = "生效合同编号",example = "12320240900001")})
    @PreAuthorize(hasPermission = "crm_contract_invoice_refund",dataScope = "crm_contract_invoice_refund")
    public JsonObject<List<ContractSignVerifyAttachmentDTO>> getAttach(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        List<ContractSignVerifyAttachmentDTO> list = remoteContractSignVerifyMainService.getAttachAfter01(List.of(contractNumber)).getObjEntity().get(contractNumber);
        return new JsonObject<>(Objects.requireNonNullElseGet(list, ArrayList::new));
    }

    @GetMapping("/getContractProcessPage")
    @Operation(summary = "开票/退票流程-分页列表-详情")
    @PreAuthorize(hasPermission = "crm_contract_invoice_refund",dataScope = "crm_contract_invoice_refund")
    public JsonObject<TableDataInfo> getContractProcessPage(@RequestParam String contractNumber){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(contractNumber, UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        startPage();
        return new JsonObject<>(extendService.getInvoiceProcessPage(contractNumber, PageHelper.getLocalPage().getPageSize(), PageHelper.getLocalPage().getPageNum()));
    }

    @PostMapping("/getContractProductList")
    @Operation(summary = "合同产品-详情",description = "合同中的所有产品行签验收情况")
    @PreAuthorize(hasPermission = "crm_contract_invoice_refund",dataScope = "crm_contract_invoice_refund")
    public JsonObject<List<ContractProductDTO>> getContractProductList(@RequestBody ContractProductDetailQuery query){
        JsonObject<Boolean> hasRight = remoteContractExecuteService.hasRight(query.getContractNumber(), UserInfoHolder.getCurrentPersonId());
        if(!hasRight.isSuccess() || !hasRight.getObjEntity()){
            throw  new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        query.setNeedGrossMargin(true);
        query.setNeedSpecialCode(true);
        query.setNeedSn(true);
        List<CrmContractProductAllDetailVO> entity = remoteContractExecuteService.queryContractProductDetailByCondition(query).getObjEntity();
        List<ContractProductDTO> contractProductDTOS = HyperBeanUtils.copyListProperties(entity, ContractProductDTO::new);
        return new JsonObject<>(contractProductDTOS);
    }

    @GetMapping("/queryInvoiceAuthCommentList")
    @Operation(summary = "根据流程实例id查询审批进度以及评论回复",parameters = {@Parameter(example = "52e30c06-3227-11f0-a9ac-0242ac12000d")})
    @PreAuthorize
    public JsonObject<List<TaskHistoryVo>> queryInvoiceAuthCommentList(@RequestParam String processInstanceId) {
        String currentAccountId = getCurrentAccountId();
        return Optional.ofNullable(tfsTaskClient.getTaskHistoryList(processInstanceId)).map(
                        taskHistoryVo->{
                            tfsProcessService.authCommentList(taskHistoryVo.getObjEntity(),currentAccountId);
                            return taskHistoryVo;
                        })
                .orElseThrow(() -> new CrmException("查询审批意见失败"));
    }

}
