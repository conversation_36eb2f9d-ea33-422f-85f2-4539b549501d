package com.topsec.crm.flow.core.controller.contractreview;

import com.topsec.crm.flow.api.dto.contractreview.sninfo.ChangeSaleSnExportBean;
import com.topsec.crm.flow.api.dto.contractreview.sninfo.ChangeSaleSnQuery;
import com.topsec.crm.flow.api.dto.contractreview.sninfo.ChangeSaleSnVO;
import com.topsec.crm.flow.core.service.ContractProductSnService;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
* 合同中产品序列号表
*
* <AUTHOR>
* @since 1.0.0 2024-07-25
*/
@RestController
@RequestMapping("/contractSn")
@Tag(name = "合同关联序列号", description = "/contractSn")
@RequiredArgsConstructor
@Validated
public class ContractProductSnController extends BaseController {

    private final ContractProductSnService contractProductSnService;

    @PostMapping("/pageChangeSalesSn")
    @Operation(summary = "分页查询转销序列号", description = "分页查询转销序列号")
    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_sn", dataScope = "crm_flow_borrow_for_sell_sn")
    public JsonObject<PageUtils<ChangeSaleSnVO>> pageChangeSalesSn(@RequestBody ChangeSaleSnQuery query){
        return new JsonObject<>(contractProductSnService.pageChangeSalesSn(query));
    }

    @PostMapping("/exportChangeSalesSn")
    @Operation(summary = "导出转销序列号", description = "导出转销序列号")
    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_sn_export", dataScope = "crm_flow_borrow_for_sell_sn_export")
    public void exportChangeSalesSn(@RequestBody ChangeSaleSnQuery query) throws IOException {
        query.setPageNo(null);
        query.setPageSize(null);
        PageUtils<ChangeSaleSnVO> pageUtils = contractProductSnService.pageChangeSalesSn(query);
        List<ChangeSaleSnExportBean> exportBeans = new ArrayList<>();
        ListUtils.emptyIfNull(pageUtils.getList()).forEach(item -> {
            ChangeSaleSnExportBean changeSaleSnExportBean = HyperBeanUtils.copyProperties(item, ChangeSaleSnExportBean::new);
            Set<ApproveNode> approveNodes = item.getApproveNodeSet();
            if (CollectionUtils.isNotEmpty(approveNodes)) {
                changeSaleSnExportBean.setApproveNodeSet(approveNodes.stream()
                        .map(ApproveNode::getNodeName)
                        .distinct()
                        .collect(Collectors.joining(",")));
            } else {
                //处理 approveNodes 为 null 的情况
                changeSaleSnExportBean.setApproveNodeSet("流程已办结");
            }
            exportBeans.add(changeSaleSnExportBean);
        });

        ExcelUtil<ChangeSaleSnExportBean> excelUtil = new ExcelUtil<>(ChangeSaleSnExportBean.class);
        excelUtil.exportExcel(response, exportBeans,"转销序列号");
    }

}
