package com.topsec.crm.flow.core.controller.threeProcurement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.topsec.crm.flow.api.dto.contractreview.baseinfo.ContractBasicInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.vo.ThreeProcurementProductVo;
import com.topsec.crm.flow.api.vo.ThreeProcurementReviewMainVo;
import com.topsec.crm.flow.api.vo.ThreeProcurementReviewQuery;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ReturnExchangeProductThird;
import com.topsec.crm.flow.core.entity.ThreeProcurementReviewMain;
import com.topsec.crm.flow.core.entity.ThreeProcurementSmReviewMain;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.operation.api.entity.SupplierVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 【SM第三方采购】Controller
 *
 * @date 2024-09-02
 */
@RestController
@RequestMapping("/threeProcurementSm")
@Tag(name = "【【流程查询-SM第三方采购】】", description = "threeProcurementSm")
@RequiredArgsConstructor
@Validated
public class ThreeProcurementSmProcessController extends BaseController
{

    private final ThreeProcurementSmReviewMainService threeProcurementSmReviewMainService;



    private final ContractReviewProductThirdService contractReviewProductThirdService;


    private final ThreeProcurementReviewMainService threeProcurementReviewMainService;

    private final ContractReviewMainService contractReviewMainService;

    private final ContractReviewFlowService contractReviewFlowService;

    private final ThreeProcurementProductService threeProcurementProductService;

    private final ReturnExchangeProductThirdService returnExchangeProductThirdService;






    @GetMapping("/queryThirdProductByContractId")
    @Operation(summary = "根据合同id查询合同中的第三方产品信息")
    @PreFlowPermission
    public JsonObject<List<ThreeProcurementProductVo>> queryThirdProductByContractId(@RequestParam String contractId,@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementSmReviewMain threeProcurementSmReviewMain = threeProcurementSmReviewMainService.queryThreeProcurementSmReviewMain(processInstanceId);
        if (threeProcurementSmReviewMain == null){
            throw new RuntimeException("未查询到流程信息");
        }
        List<ThreeProcurementProductVo> procurementProductVoList = null;
        if (threeProcurementSmReviewMain.isReturnAndExchange()) {
            List<ReturnExchangeProductThird> returnExchangeProductThirds = returnExchangeProductThirdService.listNewProductByProcessInstanceId(threeProcurementSmReviewMain.getReturnAndExchangeProcessId());
            procurementProductVoList = HyperBeanUtils.copyListPropertiesByJackson(returnExchangeProductThirds, ThreeProcurementProductVo.class);
        }else {
            List<ContractProductThirdDTO> productThirdDTOList = contractReviewProductThirdService.productThirdInfoByContractId(contractId, true);
            procurementProductVoList = HyperBeanUtils.copyListPropertiesByJackson(productThirdDTOList, ThreeProcurementProductVo.class);
        }
        if (CollectionUtils.isNotEmpty(procurementProductVoList)){
            Map<String, ThreeProcurementProductVo> productVoMap = procurementProductVoList.stream()
                    .collect(Collectors.toMap(
                            ThreeProcurementProductVo::getProductId,
                            product -> product
                    ));
            threeProcurementReviewMainService.findThreeProcurementProductStatus(processInstanceId, procurementProductVoList);
            procurementProductVoList.forEach(procurementProductVo -> {
                if(procurementProductVo.getProcurementStatus().equals(0)){
                    procurementProductVo.setProcurementPrice(productVoMap.get(procurementProductVo.getProductId()).getPurchasePrice());
                    procurementProductVo.setProcurementTaxRate(productVoMap.get(procurementProductVo.getProductId()).getTaxRate());
                }
            });
        }
        return new JsonObject<>(procurementProductVoList);
    }






    @GetMapping("/queryThreeProcurementSmContract")
    @Operation(summary = "根据流程实例id查询【SM第三方采购提醒】合同id")
    @PreFlowPermission
    public JsonObject<ContractBasicInfoDTO> queryThreeProcurementSmContract(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementSmReviewMain threeProcurementSmReviewMain = threeProcurementSmReviewMainService.queryThreeProcurementSmReviewMain(processInstanceId);
        if (threeProcurementSmReviewMain == null){
            throw new RuntimeException("未查询到流程信息");
        }
        return new JsonObject<>(contractReviewMainService.contractBasicInfo(threeProcurementSmReviewMain.getContractId(), true));

    }


    @GetMapping("/contractAmount")
    @Operation(summary = "合同总金额")
    @PreFlowPermission
    public JsonObject<BigDecimal> contractAmount(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementSmReviewMain threeProcurementSmReviewMain = threeProcurementSmReviewMainService.queryThreeProcurementSmReviewMain(processInstanceId);
        if (threeProcurementSmReviewMain == null){
            throw new RuntimeException("未查询到流程信息");
        }
        return new JsonObject<>(contractReviewFlowService.contractAmountTotal(threeProcurementSmReviewMain.getContractId()));
    }


    @PostMapping("/pageThreeProcurement")
    @Operation(summary = "SM采购提醒详情分页查询第三方采购审批列表")
    @PreFlowPermission
    public JsonObject<PageUtils<ThreeProcurementReviewMainVo>> pageThreeProcurement(@RequestBody ThreeProcurementReviewQuery threeProcurementReviewQuery)
    {
        if (Objects.isNull(threeProcurementReviewQuery)){
            throw new CrmException("参数不能为空");
        }
        PreFlowPermissionAspect.checkProcessInstanceId(threeProcurementReviewQuery.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        threeProcurementReviewQuery.setCreateUser(UserInfoHolder.getCurrentPersonId());
        IPage<ThreeProcurementReviewMainVo> convert = threeProcurementReviewMainService.getThreeProcurementReviewMainVoIPage(threeProcurementReviewQuery);

        return new JsonObject<>(new PageUtils<>(convert));
    }



    @GetMapping("/findSupplierByProcessInstanceId")
    @Operation(summary = "根据合同id查询第三方产品的供应商-涉密待办入口发起时调用 ")
    @PreFlowPermission
    public JsonObject<List<SupplierVO>> findSupplierByProcessInstanceId(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程实例ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementSmReviewMain threeProcurementSmReviewMain = threeProcurementSmReviewMainService.queryThreeProcurementSmReviewMain(processInstanceId);
        if (threeProcurementSmReviewMain == null){
            throw new RuntimeException("未查询到流程信息");
        }
        boolean returnAndExchange = threeProcurementSmReviewMain.isReturnAndExchange();
        //退换货
        if (returnAndExchange) {
            return new JsonObject<>(returnExchangeProductThirdService.selectSupplierByReturnExchangeId(threeProcurementSmReviewMain.getReturnAndExchangeProcessId()));
        }
        return new JsonObject<>(threeProcurementProductService.selectSupplierByContractId(threeProcurementSmReviewMain.getContractId()));
    }











}
