package com.topsec.crm.flow.core.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 合同预测人员项目信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
@TableName("contract_forecast_user_project")
public class ContractForecastUserProject extends ContractForecastStatistics {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 合同预测用户表ID
     */
    private String forecastUserId;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 最终客户ID
     */
    private String finalCustomerId;

    /**
     * 最终客户名称
     */
    private String finalCustomerName;

    /**
     * 是否指名
     */
    private Boolean specify;

    /**
     * 预测新增合同金额
     */
    private BigDecimal forecastNewContractAmount;

    /**
     * 删除标识 0未删除,1已删除
     */
    @TableLogic
    private Boolean delFlag;

    @TableField(exist = false)
    private List<ContractForecastUserProjectProduct> userProjectProducts;

    /**
     * 重写setter方法，在设置产品列表时自动计算统计数据
     */
    public void setUserProjectProducts(List<ContractForecastUserProjectProduct> userProjectProducts) {
        this.userProjectProducts = userProjectProducts;
        // 自动计算统计数据
        ContractForecastStatistics stats = ContractForecastUserProjectProduct.fromRawData(this.userProjectProducts, "signDate");
        this.accumulate(stats);
    }
}
