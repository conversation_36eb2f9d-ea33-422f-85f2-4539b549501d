package com.topsec.crm.flow.core.controller.contractBadDebt;

import cn.hutool.core.collection.CollectionUtil;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractBadDebtDetailVo;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedVo;
import com.topsec.crm.flow.core.entity.ContractBadDebtExecuteSnapshot;
import com.topsec.crm.flow.core.entity.ContractBadDebtMain;
import com.topsec.crm.flow.core.service.IContractBadDebtDetailService;
import com.topsec.crm.flow.core.service.IContractBadDebtExecuteSnapshotService;
import com.topsec.crm.flow.core.service.IContractBadDebtMainService;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.flow.core.entity.ContractBadDebtDetail;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contractBadDebtDetail")
@Tag(name = "合同坏账相关Controller", description = "/contractBadDebtDetail/flow")
@RequiredArgsConstructor
@Validated
public class ContractBadDebtDetailController extends BaseController {
    @Autowired
    private IContractBadDebtDetailService contractBadDebtDetailService;
    @Autowired
    private RemoteContractExecuteService remoteContractExecuteService;
    @Autowired
    private IContractBadDebtMainService contractBadDebtMainService;
    @Autowired
    private IContractBadDebtExecuteSnapshotService contractBadDebtExecuteSnapshotService;

    /**
     * 分页列表
     *
     * 普通销售-查看自己的
     * 部门负责人-查看管辖部门
     * 管理员-查看所有
     */
    @PostMapping("/selectPage")
    @Operation(summary = "查询合同坏账列表记录")
    @PreAuthorize(hasPermission = "crm_contract_bad_debt",dataScope = "crm_contract_bad_debt")
    public JsonObject<PageUtils<ContractBadDebtDetailVo>> selectPage(@RequestBody ContractBadDebtDetailVo contractBadDebtDetailVo) {
        //查询符合条件的合同执行ID
        List<String> filterContractNumbers = new ArrayList<String>();
        //组合页面负责人参数和权限范围内的用户ID
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        Set<String> queryPIds = new HashSet<String>();

        if(StringUtils.isNotBlank(contractBadDebtDetailVo.getContractCompanyName()) || StringUtils.isNotBlank(contractBadDebtDetailVo.getFinalCustomerName())
                || StringUtils.isNotBlank(contractBadDebtDetailVo.getContractOwnerDeptId()) || CollectionUtil.isNotEmpty(contractBadDebtDetailVo.getContractOwnerIds())
                || CollectionUtil.isNotEmpty(personIdList)) {
            CrmContractAfterQuery query = new CrmContractAfterQuery();
            CrmContractAfterQuery.BaseQuery baseQuery = new CrmContractAfterQuery.BaseQuery();
            baseQuery.setContractCompanyName(StringUtils.isNotBlank(contractBadDebtDetailVo.getContractCompanyName()) ? contractBadDebtDetailVo.getContractCompanyName() : null);
            baseQuery.setFinalCustomerName(StringUtils.isNotBlank(contractBadDebtDetailVo.getFinalCustomerName()) ? contractBadDebtDetailVo.getFinalCustomerName() : null);
            baseQuery.setContractOwnerDeptId(StringUtils.isNotBlank(contractBadDebtDetailVo.getContractOwnerDeptId()) ? contractBadDebtDetailVo.getContractOwnerDeptId() : null);
            if (personIdList != null) {
                if (CollectionUtil.isNotEmpty(contractBadDebtDetailVo.getContractOwnerIds())) {
                    //找出两者的并集
                    for (String s : personIdList) {
                        for (String contractOwnerId : contractBadDebtDetailVo.getContractOwnerIds()) {
                            if (s.equals(contractOwnerId)) {
                                queryPIds.add(contractOwnerId);
                            }
                        }
                    }
                    if (CollectionUtil.isEmpty(queryPIds)) {
                        return new JsonObject<>(new PageUtils<ContractBadDebtDetailVo>());
                    }
                    baseQuery.setContractOwnerIds(queryPIds.stream().toList());
                } else {
                    baseQuery.setContractOwnerIds(personIdList.stream().toList());
                }
            } else {
                //如果personIdList == null，全量数据权限
                if (CollectionUtil.isNotEmpty(contractBadDebtDetailVo.getContractOwnerIds())) {
                    baseQuery.setContractOwnerIds(contractBadDebtDetailVo.getContractOwnerIds());
                } else {
                    //设置为null，查询全量数据
                    baseQuery.setContractOwnerIds(null);
                }
            }
            query.setBaseQuery(baseQuery);
            query.setNeedContractType(true);
            JsonObject<PageUtils<CrmContractExecuteVO>> pageUtilsJsonObject = remoteContractExecuteService.pageByCondition(query);
            if (pageUtilsJsonObject.isSuccess() && pageUtilsJsonObject.getObjEntity() != null && CollectionUtil.isNotEmpty(pageUtilsJsonObject.getObjEntity().getList())) {
                if (CollectionUtil.isNotEmpty(pageUtilsJsonObject.getObjEntity().getList())) {
                    filterContractNumbers = pageUtilsJsonObject.getObjEntity().getList().stream().map(CrmContractExecuteVO::getContractNumber).toList();
                }
            } else {
                return new JsonObject<>(new PageUtils<ContractBadDebtDetailVo>());
            }
        }

        startPage();
        List<ContractBadDebtDetail> list = contractBadDebtDetailService.query()
                .in(CollectionUtil.isNotEmpty(filterContractNumbers), "contract_number", filterContractNumbers)
                .eq(StringUtils.isNotBlank(contractBadDebtDetailVo.getProcessNumber()),"process_number", contractBadDebtDetailVo.getProcessNumber())
                .eq(StringUtils.isNotNull(contractBadDebtDetailVo.getStatus()),"status", contractBadDebtDetailVo.getStatus())
                .orderByDesc("create_time")
                .list();

        if(CollectionUtil.isNotEmpty(list)){
            //查询合同执行信息
            Set<String> collect = list.stream().map(ContractBadDebtDetail::getContractNumber).collect(Collectors.toSet());
            JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(collect);
            List<ContractBadDebtExecuteSnapshot> snaps = contractBadDebtExecuteSnapshotService.getByContractNumberBatch(collect);
            if(byContractNumberBatch.isSuccess()){
                List<CrmContractExecuteVO> contractInfos = byContractNumberBatch.getObjEntity();

                //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
                List<ContractBadDebtDetailVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, ContractBadDebtDetailVo.class);
                //补充合同原销售单位字段-坏账时间字段
                for (ContractBadDebtDetailVo detail : listVo) {
                    ContractBadDebtMain one = contractBadDebtMainService.query().eq("process_instance_id", detail.getProcessInstanceId()).one();
                    //设置合同执行信息
                    if(one != null && one.getProcessState() == ApprovalStatusEnum.SPZ.getCode()){
                        //审批中实时查询
                        CrmContractExecuteVO crmContractExecuteVO = contractInfos.stream().filter(c -> c.getContractNumber().equals(detail.getContractNumber())).findFirst().orElse(null);
                        detail.setContractExecuteVO(HyperBeanUtils.copyPropertiesByJackson(crmContractExecuteVO,ContractExecuteVO.class));
                    }else{
                        //审批结束，查询快照
                        //审批通过查询
                        ContractBadDebtExecuteSnapshot snapshot = snaps.stream().filter(c -> c.getContractNumber().equals(detail.getContractNumber())).findFirst().orElse(null);
                        if(snapshot != null){//老系统合同执行不存在判断
                            snapshot.setContractExecuteStatus(detail.getStatus() == 1 ? 2 : 3);
                            detail.setContractExecuteVO(HyperBeanUtils.copyPropertiesByJackson(snapshot,ContractExecuteVO.class));
                        }
                    }

                    //补充原销售单位字段
                    ContractExecuteVO contractExecuteVO = detail.getContractExecuteVO();
                    if(contractExecuteVO != null) {//老系统合同执行不存在判断
                        contractExecuteVO.setPassTime(one != null ? one.getPassTime() : null);
                        JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(contractExecuteVO.getSaleId());
                        if (byId.isSuccess() && byId.getObjEntity() != null) {
                            contractExecuteVO.setSaleDeptName(byId.getObjEntity().getDept() != null ? byId.getObjEntity().getDept().getName() : "");
                        }
                    }
                }
                
                PageUtils dataTable = getDataTable(list,listVo);
                return new JsonObject<>(dataTable);
            }
        }
        return new JsonObject<>(new PageUtils<ContractBadDebtDetailVo>());
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    @Operation(summary = "导出", description = "示例")
    @PreAuthorize(hasPermission = "crm_contract_bad_debt_export",dataScope = "crm_contract_bad_debt_export")
    public void export(@RequestBody ContractBadDebtDetailVo contractBadDebtDetailVo) throws Exception {
        List<ContractExecuteVO> result = new ArrayList<ContractExecuteVO>();
        //查询符合条件的合同执行ID
        List<String> filterContractNumbers = new ArrayList<String>();
        //组合页面负责人参数和权限范围内的用户ID
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        Set<String> queryPIds = new HashSet<String>();

        CrmContractAfterQuery query = new CrmContractAfterQuery();
        CrmContractAfterQuery.BaseQuery baseQuery = new CrmContractAfterQuery.BaseQuery();
        baseQuery.setContractCompanyName(StringUtils.isNotBlank(contractBadDebtDetailVo.getContractCompanyName()) ? contractBadDebtDetailVo.getContractCompanyName() : null);
        baseQuery.setFinalCustomerName(StringUtils.isNotBlank(contractBadDebtDetailVo.getFinalCustomerName()) ? contractBadDebtDetailVo.getFinalCustomerName() : null);
        baseQuery.setContractOwnerDeptId(StringUtils.isNotBlank(contractBadDebtDetailVo.getContractOwnerDeptId()) ? contractBadDebtDetailVo.getContractOwnerDeptId() : null);

        if(personIdList != null) {
            if (CollectionUtil.isNotEmpty(contractBadDebtDetailVo.getContractOwnerIds())) {
                //找出两者的并集
                for (String s : personIdList) {
                    for (String contractOwnerId : contractBadDebtDetailVo.getContractOwnerIds()) {
                        if (s.equals(contractOwnerId)) {
                            queryPIds.add(contractOwnerId);
                        }
                    }
                }
                if (CollectionUtil.isEmpty(queryPIds)) {
                    ExcelUtil<ContractExecuteVO> excelUtil = new ExcelUtil<>(ContractExecuteVO.class);
                    excelUtil.exportExcel(response, result, "合同坏账信息");
                }
                baseQuery.setContractOwnerIds(queryPIds.stream().toList());
            } else {
                baseQuery.setContractOwnerIds(personIdList.stream().toList());
            }
        }else{
            //如果personIdList == null，全量数据权限
            if (CollectionUtil.isNotEmpty(contractBadDebtDetailVo.getContractOwnerIds())) {
                baseQuery.setContractOwnerIds(contractBadDebtDetailVo.getContractOwnerIds());
            }else{
                //设置为null，查询全量数据
                baseQuery.setContractOwnerIds(null);
            }
        }
        query.setBaseQuery(baseQuery);
        query.setNeedContractType(true);
        JsonObject<PageUtils<CrmContractExecuteVO>> pageUtilsJsonObject = remoteContractExecuteService.pageByCondition(query);
        if(pageUtilsJsonObject.isSuccess() && pageUtilsJsonObject.getObjEntity() != null && CollectionUtil.isNotEmpty(pageUtilsJsonObject.getObjEntity().getList())){
            if(CollectionUtil.isNotEmpty(pageUtilsJsonObject.getObjEntity().getList())){
                filterContractNumbers = pageUtilsJsonObject.getObjEntity().getList().stream().map(CrmContractExecuteVO::getContractNumber).toList();
            }
        }else{
            ExcelUtil<ContractExecuteVO> excelUtil = new ExcelUtil<>(ContractExecuteVO.class);
            excelUtil.exportExcel(response, result, "合同坏账信息");
        }

        List<ContractBadDebtDetail> list = contractBadDebtDetailService.query()
                .in(CollectionUtil.isNotEmpty(filterContractNumbers), "contract_number", filterContractNumbers)
                .eq(StringUtils.isNotBlank(contractBadDebtDetailVo.getProcessNumber()),"process_number", contractBadDebtDetailVo.getProcessNumber())
                .eq(StringUtils.isNotNull(contractBadDebtDetailVo.getStatus()),"status", contractBadDebtDetailVo.getStatus())
                .orderByDesc("create_time")
                .list();

        if(CollectionUtil.isNotEmpty(list)){
            //查询合同执行信息
            JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(list.stream().map(ContractBadDebtDetail::getContractNumber).collect(Collectors.toSet()));
            if(byContractNumberBatch.isSuccess()){
                List<CrmContractExecuteVO> contractInfos = byContractNumberBatch.getObjEntity();

                for (ContractBadDebtDetail contractBadDebtDetail : list) {
                    CrmContractExecuteVO crmContractExecuteVO = contractInfos.stream().filter(c -> c.getContractNumber().equals(contractBadDebtDetail.getContractNumber())).findFirst().orElse(null);
                    contractBadDebtDetail.setContractExecuteVO(crmContractExecuteVO);
                }

                //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
                List<ContractBadDebtDetailVo> listVo = HyperBeanUtils.copyListPropertiesByJackson(list, ContractBadDebtDetailVo.class);
                //补充合同原销售单位字段
                for (ContractBadDebtDetailVo badDebtDetailVo : listVo) {
                    ContractExecuteVO contractExecuteVO = badDebtDetailVo.getContractExecuteVO();
                    JsonObject<EmployeeVO> byId = tosEmployeeClient.findById(contractExecuteVO.getSaleId());
                    if(byId.isSuccess() && byId.getObjEntity() != null){
                        contractExecuteVO.setSaleDeptName(byId.getObjEntity().getDept() != null ? byId.getObjEntity().getDept().getName() : "");
                    }
                }

                for (ContractBadDebtDetailVo detail : listVo) {
                    //查询流程信息
                    ContractBadDebtMain one = contractBadDebtMainService.query().eq("process_instance_id", detail.getProcessInstanceId()).one();

                    ContractExecuteVO contractExecuteVO = detail.getContractExecuteVO();
                    contractExecuteVO.setProcessNumber(detail.getProcessNumber());
                    contractExecuteVO.setStatus(detail.getStatus());
                    contractExecuteVO.setReason(detail.getReason());
                    contractExecuteVO.setPassTime(one != null ? one.getPassTime() : null);
                    result.add(contractExecuteVO);
                }
            }
        }

        ExcelUtil<ContractExecuteVO> excelUtil = new ExcelUtil<>(ContractExecuteVO.class);
        excelUtil.exportExcel(response, result, "合同坏账信息");
    }
}
