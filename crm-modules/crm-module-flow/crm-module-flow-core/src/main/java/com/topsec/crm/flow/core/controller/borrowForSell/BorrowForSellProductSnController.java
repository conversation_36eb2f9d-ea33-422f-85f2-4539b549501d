package com.topsec.crm.flow.core.controller.borrowForSell;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellAgentPsnInputExcelVO;
import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellDeviceDTO;
import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellProductDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BorrowForSell;
import com.topsec.crm.flow.core.service.IBorrowForSellProductSnService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * <p>
 * 借转销国代出货产品序列号 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/borrowForSellProductSn")
@Tag(name = "借转销国代出货产品序列号", description = "/borrowForSellProductSn")
public class BorrowForSellProductSnController extends BaseController {

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @Autowired
    private IBorrowForSellProductSnService borrowForSellProductSnService;

    @PreFlowPermission
    @PostMapping("/saveBorrowForSellProductSnAgent")
    @Operation(summary = "保存借转销国代出货序列号信息")
    public JsonObject<Boolean> saveBorrowForSellProductSn(@RequestParam String borrowId, @RequestBody List<BorrowForSellProductDTO> productList) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            Boolean result =  borrowForSellProductSnService.saveBorrowForSellProductSnAgent(borrowId,productList);
            return new JsonObject<Boolean>(result);
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @PostMapping("/queryBorrowForSellDeviceList")
    @Operation(summary = "查询借转销产品以及序列号信息")
    public JsonObject<List<BorrowForSellDeviceDTO>> queryBorrowForSellDeviceList(@RequestParam String borrowId,@RequestParam(required = false) Integer shipmentType) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            List<BorrowForSellDeviceDTO> result =  borrowForSellProductSnService.queryBorrowForSellDeviceList(borrowId, shipmentType);
            return new JsonObject<List<BorrowForSellDeviceDTO>>(result);
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @PostMapping("/importBorrowForSellAgentPsn")
    @Operation(summary = "导入借转销国代出货序列号信息")
    public JsonObject<Boolean> importBorrowForSellAgentPsn(@RequestParam String borrowId,MultipartFile file) throws Exception {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        String filename = file.getOriginalFilename();
        //异常信息集合
        if (filename == null || !filename.endsWith(".xls") && !filename.endsWith(".xlsx")) {
            throw new CrmException("请上传后缀为.xls或.xlsx的excel文件");
        }
        InputStream inputStream = file.getInputStream();
        ImportParams importParams = new ImportParams();
        importParams.setSheetName("借转销国代出货序列号信息");
        List<BorrowForSellAgentPsnInputExcelVO> borrowForSellAgentPsnInputExcelVOS = ExcelImportUtil.importExcel(inputStream, BorrowForSellAgentPsnInputExcelVO.class, importParams);
        List<BorrowForSellAgentPsnInputExcelVO> list = borrowForSellAgentPsnInputExcelVOS.stream().filter(item -> StringUtils.isNotBlank(item.getSns())).toList();
        boolean result = false;
        if(CollectionUtils.isNotEmpty(list)){
            result = borrowForSellProductSnService.importBorrowForSellAgentPsn(borrowId, list);
        }
        return new JsonObject<>(result);
    }
}

