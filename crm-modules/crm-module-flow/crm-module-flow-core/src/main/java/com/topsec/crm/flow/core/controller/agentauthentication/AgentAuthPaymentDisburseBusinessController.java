package com.topsec.crm.flow.core.controller.agentauthentication;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.RemoteCrmAgentAuthenticationService;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.file.api.RemoteFsmDocService;
import com.topsec.crm.flow.api.RemotePerformanceExecuteService;
import com.topsec.crm.flow.api.dto.agentauthentication.AgentAuthPaymentDisburseDetailInfo;
import com.topsec.crm.flow.api.dto.agentauthentication.AgentAuthPaymentDisburseQuery;
import com.topsec.crm.flow.api.dto.agentauthentication.AgentAuthPaymentDisburseVO;
import com.topsec.crm.flow.api.dto.agentauthentication.AgentAuthPaymentQuery;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteQuery;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteVO;
import com.topsec.crm.flow.core.entity.AgentAuthPaymentDisburse;
import com.topsec.crm.flow.core.entity.FlowBaseEntity;
import com.topsec.crm.flow.core.mapstruct.AgentAuthPaymentDisburseConvertor;
import com.topsec.crm.flow.core.service.AgentAuthPaymentDisburseService;
import com.topsec.crm.flow.core.service.PerformanceExecuteService;
import com.topsec.crm.framework.common.bean.CrmFsmDoc;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.AgentEnum;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.enums.FormTypeEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.query.CommonProcessQuery;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.vo.TfsFormContentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/agentAuthPaymentDisburse")
@Tag(name = "付款缴纳-业务")
@RequiredArgsConstructor
@Validated
public class AgentAuthPaymentDisburseBusinessController {

    private final AgentAuthPaymentDisburseService agentAuthPaymentDisburseService;
    private final RemoteAgentService remoteAgentService;
    private final RemoteFsmDocService remoteFsmDocService;
    private final TfsFormContentClient tfsFormContentClient;
    private final RemoteCrmAgentAuthenticationService remoteCrmAgentAuthenticationService;
    private final RemotePerformanceExecuteService remotePerformanceExecuteService;
    private final PerformanceExecuteService  performanceExecuteService;

    @GetMapping("/detail")
    @Operation(summary = "渠道认证付款缴纳-详情")
    @PreAuthorize(hasPermission = "crm_agent_authentication",dataScope="crm_agent_authentication")
    public JsonObject<AgentAuthPaymentDisburseDetailInfo> detail(@RequestParam String processInstanceId){
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        AgentAuthPaymentDisburse one = agentAuthPaymentDisburseService
                .getOne(new QueryWrapper<AgentAuthPaymentDisburse>().eq("process_instance_id", processInstanceId));
        String payoutCompanyId = one.getPayoutCompanyId();
        Boolean allScope = dataScopeParam.getAllScope();
        if (!allScope){
            Set<String> agentIds = Optional.ofNullable(remoteCrmAgentAuthenticationService.getCrmAgentsByDataScopeParam(dataScopeParam))
                    .map(JsonObject::getObjEntity).orElse(Collections.emptySet());
            if (!agentIds.contains(payoutCompanyId)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }
        AgentAuthPaymentDisburseDetailInfo detailInfo = AgentAuthPaymentDisburseConvertor.INSTANCE.toDetailInfo(one);
        List<String> attachmentIds = one.getAttachmentIds();
        if (CollectionUtils.isNotEmpty(attachmentIds)){
            List<CrmFsmDoc> crmFsmDocs = Optional.ofNullable(remoteFsmDocService.batchSelectByDocId(attachmentIds))
                    .map(JsonObject::getObjEntity)
                    .orElse(Collections.emptyList());
            detailInfo.setAttachments(crmFsmDocs);
        }

        Optional.ofNullable(remoteAgentService.getAgentInfo(one.getRecipientCompanyId())).map(JsonObject::getObjEntity).ifPresent(
                agent -> {
                    detailInfo.setAddress(agent.getDetailAddress());
                    detailInfo.setBankName(agent.getDepositBank());
                    detailInfo.setBankAccount(agent.getBankAccount());
                });


        return new JsonObject<>(detailInfo);
    }

    @PostMapping({"/queryProcessPage"})
    @Operation(summary = "渠道认证付款缴纳-列表")
    @PreAuthorize(hasPermission = "crm_agent_authentication",dataScope="crm_agent_authentication")
    public JsonObject<PageUtils<TfsFormContentVo>> queryProcessPage(@RequestBody AgentAuthPaymentQuery query){
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        String parentProcessInstanceId = query.getParentProcessInstanceId();
        List<AgentAuthPaymentDisburse> disburseList = agentAuthPaymentDisburseService
                .list(new QueryWrapper<AgentAuthPaymentDisburse>()
                        .eq("parent_process_instance_id", parentProcessInstanceId)
                        .select("process_instance_id","payout_company_id")
                ).stream().toList();
        if (CollectionUtils.isEmpty(disburseList)){
            return new JsonObject<>(PageUtils.empty());
        }
        Boolean allScope = dataScopeParam.getAllScope();
        if (!allScope){
            String payoutCompanyId = disburseList.get(0).getPayoutCompanyId();
            Set<String> agentIds = Optional.ofNullable(remoteCrmAgentAuthenticationService.getCrmAgentsByDataScopeParam(dataScopeParam))
                    .map(JsonObject::getObjEntity).orElse(Collections.emptySet());
            if (!agentIds.contains(payoutCompanyId)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }
        List<String> instanceIdList = disburseList.stream().map(FlowBaseEntity::getProcessInstanceId).toList();
        CommonProcessQuery commonProcessQuery=new CommonProcessQuery();
        commonProcessQuery.setPlatformCode(UserInfoHolder.getCurrentAudience());
        commonProcessQuery.setType(FormTypeEnum.PAYMENT_DISBURSE.getType());
        commonProcessQuery.setPageNum(query.getPageNum());
        commonProcessQuery.setPageSize(query.getPageSize());
        commonProcessQuery.setStatus(query.getStatus());
        commonProcessQuery.setProcessInstanceIds(instanceIdList);

        PageUtils<TfsFormContentVo> pageUtils = Optional.ofNullable(tfsFormContentClient.queryProcessPage(commonProcessQuery))
                .map(JsonObject::getObjEntity).map(PageUtils::new)
                .orElse(new PageUtils<>());

        return new JsonObject<>(pageUtils);
    }

    @PostMapping("/paymentPage")
    @Operation(summary = "付款申请列表")
    @PreAuthorize(hasPermission = "crm_agent_payment",dataScope = "crm_agent_payment")
    public JsonObject<PageUtils<AgentAuthPaymentDisburseVO>> paymentPage(@RequestBody AgentAuthPaymentDisburseQuery query) {
        return new JsonObject<>(agentAuthPaymentDisburseService.paymentPage(query));
    }

    @GetMapping("/paymentDetail")
    @Operation(summary = "付款申请详情")
    @PreAuthorize(hasPermission = "crm_agent_payment",dataScope = "crm_agent_payment")
    public JsonObject<AgentAuthPaymentDisburseVO> paymentDetail(@RequestParam String processInstanceId) {
        AgentAuthPaymentDisburse disburse = agentAuthPaymentDisburseService.getEntityByProcessInstanceId(processInstanceId);
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        if(StringUtils.isBlank(UserInfoHolder.getCurrentAgentId())){
            PreAuthorizeAspect.checkPersonIdDataScope(dataScopeParam,disburse.getCreateUser());
        }else {
            if(disburse.getPayoutCompanyId().equals(UserInfoHolder.getCurrentAgentId())
                    || disburse.getRecipientCompanyId().equals(UserInfoHolder.getCurrentAgentId())){
            }else {
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }
        return new JsonObject<>(agentAuthPaymentDisburseService.paymentDetail(processInstanceId));
    }

    @GetMapping("/selectZDInfo")
    @Operation(summary = "查询国代和省代列表")
    @PreAuthorize(hasAnyPermission = {"crm_agent_payment","crm_performance_execute_execute","crm_performance_execute_execute_agent"})
    public JsonObject<List<CrmAgentVo>> selectZDInfo(@RequestParam Integer paymentNature) {
        CrmAgentVo crmAgentVo = new CrmAgentVo();
        crmAgentVo.setLevel(AgentEnum.AgentLevelEnum.NATIONAL_DISTRIBUTOR.getLevel());
        JsonObject<List<CrmAgentVo>> listJsonObject = remoteAgentService.selectGDandSDSelect(crmAgentVo);
        if(listJsonObject.isSuccess()){
            if(paymentNature == 1){
                return listJsonObject;
            }
            if(paymentNature == 2){
                List<String> ids = listJsonObject.getObjEntity().stream().map(CrmAgentVo::getId).toList();
                Map<String, String> objEntity = Collections.emptyMap();
                if(StringUtils.isBlank(UserInfoHolder.getCurrentAgentId())){
                    objEntity = remotePerformanceExecuteService.querySupplierList().getObjEntity();
                }else {
                    objEntity = performanceExecuteService.quertSupplierList2();
                }
                List<CrmAgentVo> crmAgentVos = objEntity.entrySet().stream().map(entry -> {
                    CrmAgentVo crmAgentVo1 = new CrmAgentVo();
                    crmAgentVo1.setId(entry.getKey());
                    crmAgentVo1.setAgentName(entry.getValue());
                    if (ids.contains(entry.getKey()))
                        crmAgentVo1.setLevel(AgentEnum.AgentLevelEnum.NATIONAL_DISTRIBUTOR.getLevel());
                    else crmAgentVo1.setLevel(AgentEnum.AgentLevelEnum.PROVINCIAL_DISTRIBUTOR.getLevel());
                    return crmAgentVo1;
                }).toList();
                return new JsonObject<>(crmAgentVos);
            }
        }
        return new JsonObject<>(null);
    }

    @PostMapping("/pagePerformanceReport")
    @Operation(summary = "查询有欠款的生效业绩上报分页列表")
    @PreAuthorize(hasAnyPermission = {"crm_performance_execute_execute","crm_performance_execute_execute_agent"})
    public JsonObject<PageUtils<PerformanceExecuteVO>> pagePerformanceReport(@RequestBody PerformanceExecuteQuery query) {
        query.setReportStatus(1);
        query.setDebtAmountGt(BigDecimal.ZERO);
        if(StringUtils.isBlank(UserInfoHolder.getCurrentAgentId())){
            return remotePerformanceExecuteService.pageNternal(query);
        }else {
            return remotePerformanceExecuteService.pageAgent(query);
        }
    }

}
