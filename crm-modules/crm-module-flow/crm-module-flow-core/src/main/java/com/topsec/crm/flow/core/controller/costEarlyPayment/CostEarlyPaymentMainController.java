package com.topsec.crm.flow.core.controller.costEarlyPayment;

import com.topsec.crm.flow.api.dto.costContarct.CostContractAttachmentInfoDTO;
import com.topsec.crm.flow.api.dto.costEarlyPayment.CostEarlyPaymentFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.costEarlyPayment.CostEarlyPaymentInfoDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CostEarlyPaymentMain;
import com.topsec.crm.flow.core.entity.CostFiling;
import com.topsec.crm.flow.core.entity.CostPayment;
import com.topsec.crm.flow.core.process.impl.CostEarlyPaymentProcessService;
import com.topsec.crm.flow.core.service.CostContractAttachmentService;
import com.topsec.crm.flow.core.service.ICostEarlyPaymentMainService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.List;
import java.util.Optional;

/**
 * 费用提前支付主表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-08-16 14:45
 */
@Tag(name = "费用提前支付主表前端控制器")
@RestController
@PreAuthorize(hasPermission = "cost-early-payment-main")
@RequestMapping("/cost-early-payment-main")
@RequiredArgsConstructor
@Validated
public class CostEarlyPaymentMainController {
    @Autowired
    ICostEarlyPaymentMainService iCostEarlyPaymentMainService;
    @Autowired
    CostEarlyPaymentProcessService costEarlyPaymentProcessService;

    private final CostContractAttachmentService costContractAttachmentService;

//    @PreAuthorize(hasPermission="crm_cost_filing")
//    @PostMapping("/saveCostEarlyPaymentInfo")
//    @Operation(summary = "保存或修改业务数据,根据dto是否传id判断")
//    public JsonObject<String> saveOrUpdateCostEarlyPaymentInfo(@RequestBody CostEarlyPaymentFlowLaunchDTO costEarlyPaymentFlowLaunchDTO){
//        return new JsonObject<>(iCostEarlyPaymentMainService.saveOrUpdateCostEarlyPaymentInfo(costEarlyPaymentFlowLaunchDTO));
//    }

    @PreFlowPermission
    @GetMapping("/QueryCostEarlyPaymentInfo")
    @Operation(summary = "根据费用提前支付流程ID查询费用提前支付信息")
    public JsonObject<CostEarlyPaymentInfoDTO> queryCostEarlyPaymentInfo(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId, "流程ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(iCostEarlyPaymentMainService.queryCostEarlyPaymentInfo(processInstanceId));
    }

//    @PreAuthorize(hasPermission="crm_cost_filing")
//    @GetMapping("/QueryCostEarlyPaymentStateByCostFilingId")
//    @Operation(summary = "根据费用备案ID查询费用提前支付信息")
//    public JsonObject<CostEarlyPaymentMain> queryCostEarlyPaymentStateByCostFilingId(@RequestParam String CostFilingId){
//        CrmAssert.hasText(CostFilingId, "费用备案ID不能为空");
//        return new JsonObject<>(iCostEarlyPaymentMainService.queryCostEarlyPaymentStateByCostFilingId(CostFilingId));
//    }

    @PreAuthorize(hasPermission="crm_cost_filing")
    @GetMapping("/deleCostEarlyPaymentInfo")
    @Operation(summary = "费用提前支付删除")
    public JsonObject<Boolean> delete(@RequestParam String costEarlyPaymentId) {
        CrmAssert.hasText(costEarlyPaymentId, "费用提前支付Id不能为空");
        Optional<CostEarlyPaymentMain> optional = Optional.ofNullable(iCostEarlyPaymentMainService.getById(costEarlyPaymentId));
        if (optional.isPresent()){
            CostEarlyPaymentMain costEarlyPaymentMain = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(costEarlyPaymentMain.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(iCostEarlyPaymentMainService.delete(costEarlyPaymentId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }

    }

    @PreAuthorize(hasPermission="crm_flow_cost_filing")
    @PostMapping("/launch")
    @Operation(summary = "发起费用提前支付流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody CostEarlyPaymentFlowLaunchDTO launchDTO) {
        return new JsonObject<>(costEarlyPaymentProcessService.launch(launchDTO));
    }

    @PreFlowPermission
    @GetMapping("/getByCostContractId")
    @Operation(summary = "查询费用提前支付附件信息")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getByCostContractId(@RequestParam String costId, @RequestParam(required = false) String isFinalQuery){
        CrmAssert.hasText(costId,"主表ID不能为空！");
        Optional<CostEarlyPaymentMain> optionalById = Optional.ofNullable(iCostEarlyPaymentMainService.getById(costId));
        if (optionalById.isPresent()){
            CostEarlyPaymentMain earlyPaymentMain = optionalById.get();
            PreFlowPermissionAspect.checkProcessInstanceId(earlyPaymentMain.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractAttachmentService.getByCostContractId(costId,isFinalQuery));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }
}
