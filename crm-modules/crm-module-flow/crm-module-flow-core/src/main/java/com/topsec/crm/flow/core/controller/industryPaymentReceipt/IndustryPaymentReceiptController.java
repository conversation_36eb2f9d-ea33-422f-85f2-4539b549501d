package com.topsec.crm.flow.core.controller.industryPaymentReceipt;

import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.flow.api.dto.industryPaymentReceipt.IndustryPaymentReceiptLaunchable;
import com.topsec.crm.flow.api.dto.industryPaymentReceipt.IndustryPaymentReceiptQuery;
import com.topsec.crm.flow.api.dto.industryPaymentReceipt.IndustryPaymentReceiptVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.process.impl.IndustryPaymentReceiptProcessService;
import com.topsec.crm.flow.core.service.IndustryPaymentReceiptService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.crm.framework.security.utils.AuthorizeUtil;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/industryPaymentReceipt")
@Tag(name = "行业回款", description = "/industryPaymentReceipt")
@RequiredArgsConstructor
public class IndustryPaymentReceiptController {

    private final IndustryPaymentReceiptProcessService paymentReceiptProcessService;
    private final IndustryPaymentReceiptService industryPaymentReceiptService;
    private final RemoteContractExecuteService remoteContractExecuteService;

    @PostMapping("/launch")
    @Operation(summary = "发起行业回款流程")
    @PreAuthorize(hasPermission = "crm_industry_repayment_add")
    public JsonObject<Boolean> launch(@Valid @RequestBody IndustryPaymentReceiptLaunchable launchDTO) {
        boolean launch = paymentReceiptProcessService.launch(launchDTO);
        return new JsonObject<>(launch);
    }

    // 数据来源于收款单位下办结了且欠款金额大于0的重点行业出货合同，仅单选；
    // 销售只能选择自己名下重点行业产品对应的合同号，
    // 部门商务可以选择管辖部门下所有销售名下重点行业产品对应的合同号
    @PostMapping("/listContractList")
    @Operation(summary = "核销明细选择合同列表")
    @PreAuthorize(hasPermission = "crm_industry_repayment_add",dataScope = "crm_contract_execute_company")
    // @PreAuthorize(hasPermission = "crm_industry_repayment_add",dataScope = "crm_industry_repayment_add")
    public JsonObject<List<CrmContractExecuteVO>> listContractList(@RequestParam String signingCompanyId) {
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        Set<String> personIdList = dataScopeParam.getPersonIdList();
        Set<String> deptIdList = dataScopeParam.getDeptIdList();

        CrmContractAfterQuery crmContractAfterQuery = CrmContractAfterQuery.buildBaseQuery();
        CrmContractAfterQuery.IndustryPaymentQuery industryPaymentQuery=new CrmContractAfterQuery.IndustryPaymentQuery();
        CrmContractAfterQuery.BaseQuery baseQuery = crmContractAfterQuery.getBaseQuery();
        crmContractAfterQuery.setPageNum(1);
        crmContractAfterQuery.setPageSize(Integer.MAX_VALUE);
        //优先使用部门过滤
        if (CollectionUtils.isNotEmpty(deptIdList)){
            baseQuery.setContractOwnerDeptIds(deptIdList);
        }else if (CollectionUtils.isNotEmpty(personIdList)){
            baseQuery.setContractOwnerIds(new ArrayList<>(personIdList));
        }

        industryPaymentQuery.setSigningCompany(Set.of(signingCompanyId));
        crmContractAfterQuery.setIndustryPaymentQuery(industryPaymentQuery);
        crmContractAfterQuery.setBaseQuery(baseQuery);
        crmContractAfterQuery.setOrderByColumn("contract_time");
        crmContractAfterQuery.setIsAsc("desc");
        List<CrmContractExecuteVO> crmContractExecuteVOS = Optional.ofNullable(remoteContractExecuteService.pageByCondition(crmContractAfterQuery))
                .map(JsonObject::getObjEntity).map(PageUtils::getList).orElse(Collections.emptyList());
        return new JsonObject<>(crmContractExecuteVOS);
    }

    @GetMapping("/detail")
    @Operation(summary = "行业回款详情")
    @PreFlowPermission
    public JsonObject<IndustryPaymentReceiptVO> detail() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        IndustryPaymentReceiptVO industryPaymentReceiptVO = industryPaymentReceiptService.queryDetailByProcessInstanceId(processInstanceId);
        NameUtils.setName(industryPaymentReceiptVO);
        return new JsonObject<>(industryPaymentReceiptVO);
    }

    @PostMapping("/page")
    @Operation(summary = "行业回款列表")
    @PreAuthorize(hasPermission = "crm_industry_repayment",dataScope = "crm_industry_repayment")
    public JsonObject<PageUtils<IndustryPaymentReceiptVO>> page(@RequestBody IndustryPaymentReceiptQuery query) {
        query.setPersonIds(AuthorizeUtil.mergePersonIds(Set.of(query.getPersonId())));

        PageUtils<IndustryPaymentReceiptVO> page = industryPaymentReceiptService.page(query);
        List<IndustryPaymentReceiptVO> list = ListUtils.emptyIfNull(page.getList()).stream().filter(Objects::nonNull).toList();
        NameUtils.setName(list);
        return new JsonObject<>(page);
    }


}
