package com.topsec.crm.flow.core.controllerhidden.salesAgreement;

import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementProductVo;
import com.topsec.crm.flow.core.entity.SalesAgreementProduct;
import com.topsec.crm.flow.core.entity.SalesAgreementReviewMain;
import com.topsec.crm.flow.core.service.SalesAgreementProductService;
import com.topsec.crm.flow.core.service.SalesAgreementReviewMainService;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/hidden/salesAgreementProduct")
@Tag(name = "销售协议产品-不对外开放", description = "/hidden/salesAgreementProduct")
@RequiredArgsConstructor
@Validated
public class HiddenSalesAgreementProductController extends BaseController {

    private final SalesAgreementReviewMainService salesAgreementReviewMainService;

    private final SalesAgreementProductService salesAgreementProductService;

    @GetMapping("/querySalesAgreementProductByAgreementId")
    @Operation(summary = "根据销售协议id查询产品信息")
    public JsonObject<List<SalesAgreementProductVo>> querySalesAgreementProductByAgreementId(@RequestParam  String agreementId) {
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.getById(agreementId);
        if (salesAgreement == null){
            throw new CrmException("销售协议不存在");
        }
        String processInstanceId = salesAgreement.getProcessInstanceId();
        List<SalesAgreementProduct> salesAgreementProductList = salesAgreementProductService.findSalesAgreementProductList(processInstanceId);
        List<SalesAgreementProductVo> salesAgreementProductVos = HyperBeanUtils.copyListPropertiesByJackson(salesAgreementProductList, SalesAgreementProductVo.class);
        return new JsonObject<>(salesAgreementProductVos);
    }

}
