package com.topsec.crm.flow.core.controller.adborrowforprobation;


import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.AdBorrowForProbation;
import com.topsec.crm.flow.core.process.impl.AdBorrowForProbationProcessService;
import com.topsec.crm.flow.core.service.AdBorrowForProbationService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 样机借试用信息
 */
@RestController
@RequestMapping("/adBorrowForProbation")
@Tag(description = "/adBorrowForProbation", name = "样机借试用流程")
public class AdBorrowForProbationController extends BaseController {

    @Resource
    private AdBorrowForProbationProcessService adBorrowedForProbationProcessService;

    @Resource
    private AdBorrowForProbationService adBorrowForProbationService;

    @PostMapping("/launch")
    @Operation(summary = "发起样机借试用流程")
    @PreAuthorize(hasPermission = "crm_sample_probation_add")
    public JsonObject<Boolean> launch(@Valid @RequestBody AdBorrowForProbationFlowLaunchDTO launchDTO) {
        return new JsonObject<>(adBorrowedForProbationProcessService.launch(launchDTO));
    }

    @GetMapping("/flow/detail/{processInstanceId}")
    @Operation(summary = "查看样机借试用流程详情")
    @PreFlowPermission
    public JsonObject<AdBorrowForProbationVO> selectAdBorrowForProbationDetail(@PathVariable String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(adBorrowForProbationService.selectAdBorrowForProbationDetail(processInstanceId));
    }

    @GetMapping("/flow/IsCompleteInfo")
    @Operation(summary = "判断总代是否完整填写序列号的发货信息")
    @PreFlowPermission
    public JsonObject<Boolean> IsCompleteInfo(@RequestParam String adBorrowId) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        AdBorrowForProbation adEntity = adBorrowForProbationService.getAdEntityByProcessInstanceId(processInstanceId);
        if (adEntity == null || !adBorrowId.equals(adEntity.getId())) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(adBorrowForProbationService.IsCompleteInfo(adBorrowId));
    }

    @PostMapping("/flow/flowSave")
    @Operation(summary = "流程基本信息修改后保存")
    @PreFlowPermission
    public JsonObject<Boolean> flowSave(@RequestBody AdBorrowForProbationFlowLaunchDTO launchDTO) throws Exception {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        launchDTO.setProcessInstanceId(processInstanceId);
        AdBorrowForProbation adEntity = adBorrowForProbationService.getAdEntityByProcessInstanceId(processInstanceId);
        if(adEntity == null) throw new Exception("流程不存在");
        launchDTO.setId(adEntity.getId());
        return new JsonObject<>(adBorrowForProbationService.flowSave(launchDTO));
    }

    @GetMapping("/flow/getDirectors")
    @Operation(summary = "获取渠道总监")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> getDirectors() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(adBorrowForProbationService.getDirectors(processInstanceId));
    }

}
