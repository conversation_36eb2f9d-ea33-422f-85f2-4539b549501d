package com.topsec.crm.flow.core.controller.contractreview.statistic;

import com.topsec.crm.flow.api.dto.contractreview.statistics.ContractStatisticsQuery;
import com.topsec.crm.flow.api.dto.contractreview.statistics.ContractStatisticsVO;
import com.topsec.crm.flow.core.service.ContractReviewFlowService;
import com.topsec.crm.framework.common.bean.StatsTimeSearchVO;
import com.topsec.crm.framework.common.enums.StatsTimeEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@Tag(name = "合同评审统计接口")
@RequiredArgsConstructor
@RequestMapping("/statistic")
public class ContractReviewStatisticController {

    private final ContractReviewFlowService flowService;

    @PostMapping("/getContractStatistics")
    @PreAuthorize
    public JsonObject<ContractStatisticsVO> queryContractStatistics(@RequestBody StatsTimeSearchVO query) {
        ContractStatisticsQuery contractQuery = new ContractStatisticsQuery();
        contractQuery.setPersonId(UserInfoHolder.getCurrentPersonId());
        contractQuery.setStartTime(query.getStart());
        contractQuery.setEndTime(query.getEnd());
        return new JsonObject<>(flowService.queryContractStatistics(contractQuery));
    }

}
