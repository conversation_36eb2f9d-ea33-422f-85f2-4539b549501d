package com.topsec.crm.flow.core.controller.adborrowforprobation;

import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationBackFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.adborrowforprobation.AdBorrowForProbationBackVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.AdBorrowForProbationBack;
import com.topsec.crm.flow.core.process.impl.AdBorrowForProbationBackProcessService;
import com.topsec.crm.flow.core.service.AdBorrowForProbationBackService;
import com.topsec.crm.flow.core.service.AdBorrowForProbationService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 样机归还信息
 */
@RestController
@RequestMapping("/adBorrowForProbationBack")
@Tag(description = "/adBorrowForProbationBack", name = "样机归还流程")
@RequiredArgsConstructor
@Validated
public class AdBorrowForProbationBackController extends BaseController {

    @Resource
    private AdBorrowForProbationBackProcessService adBorrowForProbationBackProcessService;

    @Resource
    private AdBorrowForProbationBackService adBorrowForProbationBackService;

    @Resource
    private AdBorrowForProbationService adBorrowForProbationService;

    @PostMapping("/launch")
    @Operation(summary = "发起样机归还流程")
    @PreAuthorize(hasPermission = "crm_sample_back_add")
    public JsonObject<Boolean> launch(@Valid @RequestBody AdBorrowForProbationBackFlowLaunchDTO launchDTO) {
        return new JsonObject<>(adBorrowForProbationBackProcessService.launch(launchDTO));
    }

    @GetMapping("/flow/detail/{processInstanceId}")
    @Operation(summary = "查看样机归还流程详情")
    @PreFlowPermission
    public JsonObject<AdBorrowForProbationBackVO> selectAdBorrowForProbationDetail(@PathVariable String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(adBorrowForProbationBackService.selectAdBorrowForProbationBackDetail(processInstanceId));
    }

    @PostMapping("/flow/flowSave")
    @Operation(summary = "流程基本信息修改后保存")
    @PreFlowPermission
    public JsonObject<Boolean> flowSave(@RequestBody AdBorrowForProbationBackFlowLaunchDTO launchDTO) throws Exception {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        launchDTO.setProcessInstanceId(processInstanceId);
        AdBorrowForProbationBack adEntity = adBorrowForProbationBackService.getAdBackEntityByProcessInstanceId(processInstanceId);
        if(adEntity == null) throw new Exception("流程不存在");
        launchDTO.setId(adEntity.getId());
        return new JsonObject<>(adBorrowForProbationBackService.flowSave(launchDTO));
    }

    @GetMapping("/flow/getDirectors")
    @Operation(summary = "获取渠道总监")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> getDirectors() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        AdBorrowForProbationBack adEntity = adBorrowForProbationBackService.getAdBackEntityByProcessInstanceId(processInstanceId);
        if(adEntity == null) throw new RuntimeException("流程不存在");
        return new JsonObject<>(adBorrowForProbationService.getDirectors(adEntity.getAdBorrowProcessId()));
    }

}