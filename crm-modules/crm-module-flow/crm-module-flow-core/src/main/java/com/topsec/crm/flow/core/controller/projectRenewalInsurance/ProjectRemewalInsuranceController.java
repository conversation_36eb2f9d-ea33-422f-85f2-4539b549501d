package com.topsec.crm.flow.core.controller.projectRenewalInsurance;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.topsec.crm.flow.api.dto.projectRemewalInsurance.ProjectRemewalInsuranceFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.projectRemewalInsurance.ProjectRemewalInsuranceVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ProjectRemewalInsurance;
import com.topsec.crm.flow.core.process.impl.ProjectRemewalInsuranceProcessService;
import com.topsec.crm.flow.core.service.IProjectRemewalInsuranceService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectProductOwnClient;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.TfsFormContentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.*;

@RestController
@RequestMapping("/projectRemewalInsurance")
@Tag(name = "零金额续保", description = "/projectRemewalInsurance")
@RequiredArgsConstructor
public class ProjectRemewalInsuranceController extends BaseController {

    @Autowired
    private IProjectRemewalInsuranceService projectRemewalInsuranceService;
    @Autowired
    private ProjectRemewalInsuranceProcessService projectRemewalInsuranceProcessService;
    @Autowired
    private RemoteProjectProductOwnClient remoteProjectProductOwnClient;
    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    @GetMapping("/info")
    @Operation(summary = "零金额续保详情信息")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<ProjectRemewalInsuranceVo> info(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        ProjectRemewalInsurance projectRemewalInsurance = projectRemewalInsuranceService.query().eq("process_instance_id", processInstanceId).one();
        ProjectRemewalInsuranceVo prmv = HyperBeanUtils.copyPropertiesByJackson(projectRemewalInsurance, ProjectRemewalInsuranceVo.class);

        return new JsonObject<>(prmv);
    }

    @PostMapping("/launch")
    @Operation(summary = "发起零金额续保流程")
    @PreAuthorize(hasPermission = "crm_flow_zero_amount_renewal")
    public JsonObject<Boolean> launch(@Valid @RequestBody ProjectRemewalInsuranceFlowLaunchDTO launchDTO){
        checkProjectAuth(launchDTO.getProjectId());

        //1.判断是否存在审批中的零金额续保流程，如果存在，则不让发起
        ProjectRemewalInsurance one = projectRemewalInsuranceService.getOne(
                new QueryWrapper<ProjectRemewalInsurance>()
                        .eq("project_id", launchDTO.getProjectId())
                        .eq("del_flag", false)
                        .orderByDesc("create_time")
                        .last("limit 1")
        );
        Assert.isFalse(one != null && one.getProcessState() == ApprovalStatusEnum.SPZ.getCode(),"当前项目已存在审批中的零金额续保流程。");

        //2.查询项目产品是否包含分销保修服务
        JsonObject<List<CrmProjectProductOwnVO>> listJsonObject = remoteProjectDirectlyClient.tiledProductOwnOfDirectlyProject(launchDTO.getProjectId());
        if(listJsonObject.isSuccess()){
            List<CrmProjectProductOwnVO> products = listJsonObject.getObjEntity();
            List<String> list = products.stream().map(CrmProjectProductOwnVO::getStuffCode).toList();
            Assert.isFalse(!list.contains("6001002003002"),"当前项目不包含分销保修服务[6001002003002],无法发起零金额续保。");
        }

        JsonObject<CrmProjectDirectlyVo> projectInfo = remoteProjectDirectlyClient.getProjectInfo(launchDTO.getProjectId());
        if(projectInfo.isSuccess() && projectInfo.getObjEntity() != null){
            CrmProjectDirectlyVo info = projectInfo.getObjEntity();
            Assert.isFalse(info.getSigningType() == 0 && info.getZdType() == 0,"公司项目为非直签，且ZD也为非直签，不允许发起零金额续保。");
        }

        return new JsonObject<>(projectRemewalInsuranceProcessService.launch(launchDTO));
    }

    //查询是否有流程中的零金额续保
    @GetMapping("/findSPZ")
    @Operation(summary = "查询是否有流程中的零金额续保")
    @PreAuthorize(hasPermission = "crm_flow_zero_amount_renewal")
    public JsonObject<Boolean> findSPZ(@RequestParam String projectId){
        checkProjectAuth(projectId);

        //1.判断是否存在审批中的零金额续保流程，如果存在，则不让发起
        ProjectRemewalInsurance one = projectRemewalInsuranceService.getOne(
                new QueryWrapper<ProjectRemewalInsurance>()
                        .eq("project_id", projectId)
                        .eq("del_flag", false)
                        .eq("process_state", ApprovalStatusEnum.SPZ.getCode())
                        .orderByDesc("create_time")
                        .last("limit 1")
        );
        if (one != null) {
            return new JsonObject<>(true);
        }else{
            return new JsonObject<>(false);
        }
    }

    //查询产品信息是否有变动
    @GetMapping("/findProductChange")
    @Operation(summary = "查询产品信息是否有变动")
    @PreAuthorize(hasPermission = "crm_flow_zero_amount_renewal")
    public JsonObject<Map<String,Object>> findProductChange(@RequestParam String projectId){
        checkProjectAuth(projectId);

        Map<String,Object> result = new HashMap<String,Object>();
        //1.判断是否存在审批中的零金额续保流程
        ProjectRemewalInsurance one = projectRemewalInsuranceService.getOne(
                new QueryWrapper<ProjectRemewalInsurance>()
                        .eq("project_id", projectId)
                        .eq("del_flag", false)
                        .orderByDesc("create_time")
                        .last("limit 1")
        );
        if (one != null && one.getProcessState() == ApprovalStatusEnum.SPZ.getCode()) {
            result.put("flag",false);
            result.put("message","零金额续保流程未办结不可发起价格审批！");
            return new JsonObject<>(result);
        }else{
            JsonObject<CrmProjectDirectlyVo> projectInfoObj = remoteProjectDirectlyClient.getProjectInfo(projectId);
            /**
             *  2.验证发起场景，当创建项目时【是否直签】选择【直签】且【是否ZD直签】选择【是】时
             *  发起价审流程之前，必须存在零金额续保流程
             */
            if(projectInfoObj.isSuccess()){
                CrmProjectDirectlyVo info = projectInfoObj.getObjEntity();
                //确实总代直签字段
                if(info.getSigningType() == 1){
                    if(one == null) {
                        result.put("flag", false);
                        result.put("message", "当前场景下发起价审流程，缺少零金额审批流程！");
                        return new JsonObject<>(result);
                    }
                }else{
                    if(one != null) {
                        //3.判断产品明细是否有变动
                        //查询项目产品列表信息
                        JsonObject<List<CrmProjectProductOwnVO>> listJsonObject = remoteProjectProductOwnClient.queryProjectProductOwnStore(projectId);
                        if (listJsonObject.isSuccess()) {
                            List<CrmProjectProductOwnVO> details = listJsonObject.getObjEntity().stream().filter(e -> e.getParentId().equals("0")).toList();
                            String json = JSONObject.toJSONString(details);
                            if(!one.getProductDetail().equals(json)){
                                result.put("flag", false);
                                result.put("message", "检测到项目中的产品信息有变更，需要重新审批零金额续保流程后才能发起价格评审！");
                                return new JsonObject<>(result);
                            }
                        }
                    }
                }
            }
        }

        result.put("flag",true);
        return new JsonObject<>(result);
    }

    @Operation(summary = "零金额续保发起校验")
    @GetMapping("/checkBeforeFilling")
    @PreAuthorize(hasPermission = "crm_flow_zero_amount_renewal")
    public JsonObject<Void> checkBeforeFilling(String projectId) {
        checkProjectAuth(projectId);

        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        return projectRemewalInsuranceProcessService.checkBeforeFillingJsonObj(Pair.of(projectId,currentPersonId));
    }

    private void checkProjectAuth(String projectId){
        // 加一个项目的权限，根据当前登录人看看有没有项目的权限，发起之前的话 要根据当前人判断
        if (!remoteProjectDirectlyClient.hasRight(projectId, UserInfoHolder.getCurrentPersonId()).getObjEntity()) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        };
    }

    @PreAuthorize(hasPermission = "crm_flow_zero_amount_renewal")
    @GetMapping("/queryProjectProductOwnStore")
    @Operation(summary = "零金额续保查询产品列表-发起时查看")
    public JsonObject<List<CrmProjectProductOwnVO>> queryProjectProductOwnStore(@RequestParam String projectId) {
        checkProjectAuth(projectId);
        return remoteProjectProductOwnClient.queryProjectProductOwnStore(projectId);
    }
}
