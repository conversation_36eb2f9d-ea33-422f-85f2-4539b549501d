package com.topsec.crm.flow.core.controller.sealManagement;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.api.dto.sealApplication.SealApplicationFlowLaunchDTO;
import com.topsec.crm.flow.api.vo.sealApplication.ApplySealTypeInfoVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.SealApplication;
import com.topsec.crm.flow.core.process.impl.ProjectCubBidSealApplicationBranchProcessService;
import com.topsec.crm.flow.core.service.SealApplicationService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.operation.api.RemoteSealTypeService;
import com.topsec.crm.operation.api.entity.Seal.CrmSealAdminQuery;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2025-05-19  17:43
 * @Description:
 */
@RestController
@RequestMapping("/projectCubBidSealApplicationBranch")
@Tag(name = "项目、客户、印鉴类申请(分支机构)", description = "/projectCubBidSealApplicationBranch")
@RequiredArgsConstructor
@Validated
public class ProjectCubBidSealApplicationBranchController extends BaseController {

    private final ProjectCubBidSealApplicationBranchProcessService projectCubBidSealApplicationBranchProcessService;

    private final RemoteSealTypeService remoteSealTypeService;

    private final SealApplicationService sealApplicationService;

    @PostMapping("/launch")
    @Operation(summary = "发起项目、客户、印鉴类申请(分支机构)流程")
    @PreAuthorize(hasPermission = "crm_seal_apply_add",dataScope = "crm_seal_apply_add")
    public JsonObject<Boolean> launch(@Valid @RequestBody SealApplicationFlowLaunchDTO launchDTO) {
        return new JsonObject<>(projectCubBidSealApplicationBranchProcessService.launch(launchDTO));
    }


    @PostMapping("/getByDeptIdAndSealNames")
    @Operation(summary = "根据发起人所在部门ID,印鉴类型匹配出审批人")
    @PreFlowPermission
    public JsonObject<Map<String, Map<String, String>>> getByDeptIdAndSealName(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        SealApplication one = sealApplicationService.getOne(new LambdaQueryWrapper<SealApplication>().eq(SealApplication::getProcessInstanceId, processInstanceId).eq(SealApplication::getDelFlag, false));
        if (one != null) {
            String saleDeptId = one.getSaleDeptId();
            List<String> result = one.getApplySealType().stream()
                    .flatMap(infoVO -> infoVO.getSealTypeVOS().stream())
                    .map(ApplySealTypeInfoVO.SealTypeVO::getSealType)
                    .collect(Collectors.toList());
            CrmSealAdminQuery crmSealAdminQuery = new CrmSealAdminQuery();
            crmSealAdminQuery.setDeptId(saleDeptId);
            crmSealAdminQuery.setSealNames(result);

            return remoteSealTypeService.getByDeptIdAndSealName(crmSealAdminQuery);
        }
        return new JsonObject<>(null);
    }
}