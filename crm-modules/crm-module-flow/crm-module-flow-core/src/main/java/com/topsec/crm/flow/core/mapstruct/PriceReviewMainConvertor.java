package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.pricereview.PriceReviewMainInfo;
import com.topsec.crm.flow.core.entity.PriceReviewMain;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface PriceReviewMainConvertor {

    PriceReviewMainConvertor INSTANCE = Mappers.getMapper(PriceReviewMainConvertor.class);



    @Mapping(target = "priceExpiryDate", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createDept", ignore = true)
    @Mapping(target = "createEmployee", ignore = true)
    @Mapping(target = "state", ignore = true)
    @Mapping(target = "priceEffectiveDate", ignore = true)
    PriceReviewMainInfo toInfo(PriceReviewMain priceReviewMain);
}
