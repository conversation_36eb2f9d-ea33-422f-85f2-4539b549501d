package com.topsec.crm.flow.core.controller.salesAgreement;

import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementMembersLogVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.SalesAgreementMembersLog;
import com.topsec.crm.flow.core.entity.SalesAgreementMembersReviewMain;
import com.topsec.crm.flow.core.service.SalesAgreementMembersLogService;
import com.topsec.crm.flow.core.service.SalesAgreementMembersReviewMainService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 协议成员日志表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-12-31 10:52:58
 */
@RestController
@RequestMapping("/salesAgreementMemberslog")
@Tag(name = "【销售协议-协议成员日志】", description = "salesAgreementMembersLog")
@RequiredArgsConstructor
@Validated
public class SalesAgreementMembersLogController {
    private final SalesAgreementMembersLogService salesAgreementMembersLogService;
    private final SalesAgreementMembersReviewMainService salesAgreementMembersReviewMainService;



    @GetMapping("/querySalesAgreementMembersLogList")
    @Operation(summary = "销售协议主流程查询协议成员变更列表")
    @PreFlowPermission
    public JsonObject<List<SalesAgreementMembersLogVo>> querySalesAgreementMembersLogList(@RequestParam String processInstanceId, @RequestParam String recordId) {
        CrmAssert.hasText("流程实例id不能为null", processInstanceId);
        CrmAssert.hasText("协议成员记录id不能为null", recordId);
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<SalesAgreementMembersLog> salesAgreementMembersLogList = salesAgreementMembersLogService.findSalesAgreementMembersLogList(processInstanceId, recordId);
        List<SalesAgreementMembersLogVo> salesAgreementMembersLogVos = HyperBeanUtils.copyListPropertiesByJackson(salesAgreementMembersLogList, SalesAgreementMembersLogVo.class);
        NameUtils.setName(salesAgreementMembersLogVos);
        return new JsonObject<>(salesAgreementMembersLogVos);
    }


    @GetMapping("/querySubSalesAgreementMembersLogList")
    @Operation(summary = "协议成员变更流程查询协议成员变更列表")
    @PreFlowPermission
    public JsonObject<List<SalesAgreementMembersLogVo>> querySubSalesAgreementMembersLogList(@RequestParam String processInstanceId, @RequestParam String recordId) {
        CrmAssert.hasText("流程实例id不能为null", processInstanceId);
        CrmAssert.hasText("协议成员记录id不能为null", recordId);
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        SalesAgreementMembersReviewMain salesAgreementMembersReviewMain = salesAgreementMembersReviewMainService.querySalesAgreementMembersBaseInfo(processInstanceId);
        if (salesAgreementMembersReviewMain == null){
            throw  new CrmException("记录不存在");
        }
        List<SalesAgreementMembersLog> salesAgreementMembersLogList = salesAgreementMembersLogService.findSalesAgreementMembersLogList(salesAgreementMembersReviewMain.getParentProcessInstanceId(), recordId);
        List<SalesAgreementMembersLogVo> salesAgreementMembersLogVos = HyperBeanUtils.copyListPropertiesByJackson(salesAgreementMembersLogList, SalesAgreementMembersLogVo.class);
        NameUtils.setName(salesAgreementMembersLogVos);
        return new JsonObject<>(salesAgreementMembersLogVos);
    }



}
