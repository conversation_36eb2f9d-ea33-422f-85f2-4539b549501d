package com.topsec.crm.flow.core.controller.returnExchange;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.performancereport.*;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.api.dto.returnexchange.*;
import com.topsec.crm.flow.api.dto.returnexchange.contract.ContractReturnExchangeSelectQuery;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.controller.performancereport.PerformanceReportController;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.handler.right.ProjectRightHandler;
import com.topsec.crm.flow.core.handler.right.Step00RemoveEditProcessRightHandler;
import com.topsec.crm.flow.core.mapper.PerformanceReportContractDeliveryDetailMapper;
import com.topsec.crm.flow.core.mapper.ProcessExtensionInfoMapper;
import com.topsec.crm.flow.core.mapstruct.ReturnExchangeConvertor;
import com.topsec.crm.flow.core.process.impl.PerformanceReportReturnExchangeProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.flow.core.service.impl.ReturnExchangeProductServiceImpl;
import com.topsec.crm.flow.core.service.impl.ReturnExchangeServiceImpl;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.config.AuthorizeContextHolder;
import com.topsec.crm.product.api.dto.CrmProductSeparationRelQuery;
import com.topsec.crm.product.api.entity.CrmProductAgentInventoryVO;
import com.topsec.crm.product.api.entity.CrmProductSeparationRelVo;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.project.api.RemoteProjectAgentService;
import com.topsec.crm.project.api.RemoteProjectDirectlyService;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.logistics.api.domain.BatchSubParam;
import com.topsec.logistics.api.domain.LogisticsInfo;
import com.topsec.logistics.api.domain.LogisticsInfoDetail;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.common.utils.WebFilenameUtils;
import com.topsec.tos.common.vo.BriefInfoVO;
import com.topsec.tos.common.vo.TosDepartmentVO;
import com.topsec.tos.common.vo.process.DetailBaseVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 业绩上报退换货
 * <AUTHOR>
 */
@RestController
@RequestMapping("/performanceReturnExchange")
@Tag(name = "业绩上报退换货", description = "业绩上报退换货")
@RequiredArgsConstructor
public class PerformanceReturnExchangeController extends BaseController {
    private final PerformanceReportReturnExchangeProcessService performanceReportReturnExchangeProcessService;
    private final PerformanceReportReturnExchangeService performanceReportReturnExchangeService;
    private final ReturnExchangeService returnExchangeService;
    private final TosDepartmentClient tosDepartmentClient;
    private final RemoteProjectDirectlyService remoteProjectDirectlyService;
    private final RemoteProjectAgentService remoteProjectAgentService;
    private final PerformanceReportProductOwnService performanceReportProductOwnService;
    private final PerformanceReportContractDeliveryService performanceReportContractDeliveryService;
    private final PerformanceReportContractDeliveryDetailMapper performanceReportContractDeliveryDetailMapper;
    private final PerformanceExecuteService performanceExecuteService;
    private final RedissonClient redissonClient;
    private final ProcessExtensionInfoMapper processExtensionInfoMapper;
    private final PerformanceReportService performanceReportService;
    private final ContractReviewFlowService contractReviewFlowService;
    private final PriceReviewMainService priceReviewMainService;

    private static final String REDIS_KEY_PREFIX = "performanceReportReturnExchange:";

    @PostMapping("/launch")
    @Operation(summary = "发起")
    @PreAuthorize(hasPermission = "crm_refund_apply")
    public JsonObject<Boolean> launch(@Valid @RequestBody PerformanceReportReturnExchangeLaunchDTO launchDTO) {
        String contractNumber = launchDTO.getContractNumber();

        String redisKey=REDIS_KEY_PREFIX+contractNumber;
        RLock rlock = redissonClient.getLock(redisKey);
        try {
            rlock.lock();
            boolean exists = performanceReportReturnExchangeService.exists(new LambdaQueryWrapper<PerformanceReportReturnExchange>()
                    .eq(ReturnExchange::getContractNumber, contractNumber)
                    .eq(FlowBaseEntity::getProcessState, ApprovalStatusEnum.SPZ.getCode()));
            if (exists){
                return JsonObject.errorT("当前合同[%s]存在审批中的退换货流程，请勿重复发起".formatted(contractNumber));
            }
            boolean launch = performanceReportReturnExchangeProcessService.launch(launchDTO);
            return new JsonObject<>(launch);
        }finally {
            rlock.unlock();
        }
    }
    @PostMapping("/writeDraft")
    @Operation(summary = "写草稿并校验")
    @PreAuthorize(hasPermission = "crm_refund_apply")
    public JsonObject<Boolean> writeDraft(@RequestBody PerformanceReportReturnExchangeLaunchDTO launchDTO) {
        Integer step = launchDTO.getStep();
        if (step == null){
            throw new CrmException("step不能为空");
        }
        Integer subStep = launchDTO.getSubStep();
        //选择退换货产品的校验
        if (step == 2 && subStep ==null){
            performanceReportReturnExchangeProcessService.validateStep2(launchDTO);

        }else if (step == 2 && subStep==3){
            performanceReportReturnExchangeProcessService.validateStep23(launchDTO);
        }else if (step == 2 && subStep==5){
            performanceReportReturnExchangeProcessService.validateStep25(launchDTO);
        }
        returnExchangeService.writeDraft(launchDTO,UserInfoHolder.getCurrentPersonId());
        return new JsonObject<>(true);
    }

    @PostMapping("/writeDraftWithoutValidate")
    @Operation(summary = "写草稿-不校验")
    @PreAuthorize(hasPermission = "crm_refund_apply")
    public JsonObject<Boolean> writeDraftWithoutValidate(@RequestBody PerformanceReportReturnExchangeLaunchDTO launchDTO) {
        Integer step = launchDTO.getStep();
        if (step == null){
            throw new CrmException("step不能为空");
        }
        returnExchangeService.writeDraft(launchDTO,UserInfoHolder.getCurrentPersonId());
        return new JsonObject<>(true);
    }

    @GetMapping("/readDraft")
    @Operation(summary = "读草稿")
    @PreAuthorize(hasPermission = "crm_refund_apply")
    public JsonObject<PerformanceReportReturnExchangeLaunchDTO> readDraft(@RequestParam @Schema(description = "合同编号")
                                                                              String inputKey) {
        boolean exists = performanceReportReturnExchangeService.exists(new LambdaQueryWrapper<PerformanceReportReturnExchange>()
                .eq(ReturnExchange::getContractNumber, inputKey)
                .eq(FlowBaseEntity::getProcessState, ApprovalStatusEnum.SPZ.getCode()));
        if (exists){
            return JsonObject.errorT("当前合同[%s]存在审批中的退换货流程，请勿重复发起".formatted(inputKey));
        }
        String draftKey = returnExchangeService.generateDraftKey(inputKey, UserInfoHolder.getCurrentPersonId());
        return new JsonObject<>(returnExchangeService.readDraft(draftKey, PerformanceReportReturnExchangeLaunchDTO.class));
    }

    @GetMapping("/existDraft")
    @Operation(summary = "是否存在草稿")
    @PreAuthorize(hasPermission = "crm_refund_apply")
    public JsonObject<Boolean> existDraft(@RequestParam @Schema(description = "合同编号") String inputKey) {
        String draftKey = returnExchangeService.generateDraftKey(inputKey, UserInfoHolder.getCurrentPersonId());
        return new JsonObject<>(returnExchangeService.existDraft(draftKey));
    }

    @PostMapping("/validateNewOldProductMaterialCodeAndNumber")
    @Operation(summary = "提交时需要校验新产品物料代码及数量是否与旧产品一致，如果不一致给用户确认提示，交互详见产品原型")
    @PreAuthorize(hasPermission = "crm_refund_apply")
    public JsonObject<Boolean> validateNewOldProductMaterialCodeAndNumber(@RequestBody PerformanceReportReturnExchangeLaunchDTO launchDTO) {
        try {
            performanceReportReturnExchangeProcessService.validateNewOldProductMaterialCodeAndNumber(launchDTO);
        }catch (Exception e){
            return JsonObject.errorT(e.getMessage());
        }
        return JsonObject.successT();
    }



    @GetMapping("/queryAssignListLaunch")
    @Operation(summary = "发起查询审批人")
    @PreAuthorize(hasPermission = "crm_refund_apply")
    public JsonObject<List<FlowPerson>> queryAssignList(@RequestParam @Schema(description = "是否开箱即损") Boolean damageOutOfBox){
        List<FlowPerson> chargePerson = performanceReportReturnExchangeProcessService.queryChargePerson( UserInfoHolder.getCurrentPersonId());
        List<String> deptIds = chargePerson.stream().map(FlowPerson::getDept)
                .map(BriefInfoVO::getUuid).filter(Objects::nonNull).toList();
        List<FlowPerson> flowPeople ;
        if(damageOutOfBox){
            List<String> assList = deptIds.stream().flatMap(id -> {
                return Optional.ofNullable(tosDepartmentClient.findById(id))
                        .map(JsonObject::getObjEntity)
                        .map(TosDepartmentVO::getAssistants)
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(DetailBaseVO::getUuid)
                        .distinct();
            }).distinct().toList();
            flowPeople= AccountAccquireUtils.convertGetAccountByPersonId(assList);
        }else {
            flowPeople= chargePerson;
        }

        return new JsonObject<>(flowPeople);
    }


    @GetMapping("/queryChargePerson01")
    @Operation(summary = "01负责人查询-汇报上级或渠道对应的负责人")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> queryChargePerson01(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PerformanceReportReturnExchange one = performanceReportReturnExchangeService
                .getOne(new LambdaQueryWrapper<PerformanceReportReturnExchange>().eq(PerformanceReportReturnExchange::getProcessInstanceId, processInstanceId));
        String createUser = one.getCreateUser();
        List<FlowPerson> chargePerson = performanceReportReturnExchangeProcessService.queryChargePerson(createUser);
        return new JsonObject<>(chargePerson);
    }


    @PutMapping("/fillInServiceRequestNumber")
    @Operation(summary = "填写服务请求号")
    @PreFlowPermission
    public JsonObject<Boolean> fillInServiceRequestNumber(@RequestBody @NotEmpty List<String> serviceRequestNumber){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        boolean b = performanceReportReturnExchangeService.fillInServiceRequestNumber(serviceRequestNumber,processInstanceId );
        return new JsonObject<>(b);
    }


    @PutMapping("/fillInProductLineRequired")
    @Operation(summary = "是否需要产品线分析")
    @PreFlowPermission
    public JsonObject<Boolean> fillInProductLineRequired(@RequestParam Boolean productLineRequired){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        boolean b = returnExchangeService.fillInProductLineRequired(processInstanceId, productLineRequired, currentPersonId);
        return new JsonObject<>(b);
    }

    @GetMapping("/feeAccountingList")
    @Operation(summary = "退换货产品费用核算列表")
    @PreFlowPermission
    public JsonObject<List<ReturnExchangeProductVO>> feeAccountingList(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<ReturnExchangeProductVO> returnExchangeProductVOS = returnExchangeService
                .feeAccountingList(processInstanceId);
        return new JsonObject<>(returnExchangeProductVOS);
    }

    @PostMapping("/fillInFee")
    @Operation(summary = "填写费用核算")
    @PreFlowPermission
    public JsonObject<Boolean> fillInFee(@RequestBody @NotEmpty List<@Valid ReturnExchangeFeeVO> returnExchangeFeeVOList){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        for (ReturnExchangeFeeVO returnExchangeFeeVO : returnExchangeFeeVOList) {
            returnExchangeFeeVO.setProcessInstanceId(processInstanceId);
        }
        return new JsonObject<>(returnExchangeService.fillInFee(returnExchangeFeeVOList));
    }

    @PostMapping("/confirmReceiving")
    @Operation(summary = "总代确认收货")
    @PreFlowPermission
    public JsonObject<Boolean> confirmReceiving(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(performanceReportReturnExchangeService.confirmReceiving(processInstanceId));
    }

    //
    // 1、业绩上报不可选择第三方产品，当为业绩上报时，隐藏第三方产品页签，合同评审和业绩上报选择新产品的逻辑不同，能选择的产品信息参考项目中的约束条件（公司项目和QD项目也存在不同，需要根据当前关联的业绩上报来自公司项目和QD项目判断）；新产品选择逻辑比较复杂，可考虑将业绩上报和合同评审的产品选择分两个页面开发，减少共用页面带来了逻辑耦合；
    //         2、产品禁用与限制规则和项目保持一致，如果项目中不可选择禁用或限制的产品，退换货也不能选择，如果通过代码权限申请后项目中能选择，这里也能选择；
    //         3、仅可添加自有产品和第三方产品（不可选择外包服务费），可选择产品类型：硬件（包括主设备和配件）、软件（授权）、服务（除以下不可选择的服务代码外的其它服务代码）；
    // 不可选择的服务产品：
    //
    // 租赁服务：租赁方案熊总还没确定，暂不处理（代码：6005003000002、6001002000002）、
    //
    // 服务代码：【安服-重保服务：603005003002、6003005005002】、
    // 【安服-HW服务：6003005002002、6003030000002】、
    // 【安服-驻场服务：6001017000002、6003006001002、6003031000002、6003032000002】、
    // 【安服-安全运营服务：6003003006002】、
    // 【安服-通用安全服务：6003005001002、6003018000002、6004001011002、6005002001002】、
    // 【安服-安全咨询服务：6002004001002、6003001001002】、
    // 【售后-驻场服务：6001004002002】、
    // 【云服务-云WAF服务：5998010000201】
    //
    //         4、选择的产品如果存在选配或子服务，底层数据存级联关系用于办结后推生产，退换货详情中【退货产品列表】以平铺的方式展示，无需展示级联关系；
    //
    //         5、提交时需要校验一下该用户名称是否存在关联的借试用、借转销产品，如果存在，转跳到【关联借试用、借转销、重点行业序列号页面】，交互详见产品原型；
    //
    //         6、提交时需要校验新产品物料代码及数量是否与旧产品一致，如果不一致给用户确认提示，交互详见产品原型；
    @PostMapping("/pageDirectOwnProducts")
    @Operation(summary = "新产品选择分页-公司项目")
    @PreAuthorize(hasPermission = "crm_refund_apply")
    public JsonObject<PageUtils<CrmProductVo>> pageDirectOwnProducts(@RequestParam Integer pageNum, @RequestParam Integer pageSize,
                                                                     @RequestBody CrmProductVo crmProductVo) {
        String currentAgentId = UserInfoHolder.getCurrentAgentId();
        boolean launchByAgent= StringUtils.isNotBlank(currentAgentId);
        JsonObject<PageUtils<CrmProductVo>> pageUtilsJsonObject;
        if (launchByAgent){
            pageUtilsJsonObject = remoteProjectAgentService.pageAgentProducts(pageNum, pageSize, crmProductVo);
        }else {
            pageUtilsJsonObject = remoteProjectDirectlyService.pageOwnProducts(pageNum, pageSize, crmProductVo);
        }

        Optional.ofNullable(pageUtilsJsonObject)
                .map(JsonObject::getObjEntity)
                .ifPresent(pageUtils -> {
                    List<CrmProductVo> crmProductVos = ListUtils.emptyIfNull(pageUtils.getList());
                    for (CrmProductVo productVo : crmProductVos) {
                        String materialCode = productVo.getMaterialCode();
                        if (StringUtils.isNotBlank(materialCode) && ReturnExchangeProductServiceImpl.disableCode.contains(materialCode)){
                            productVo.setCodeDisable(true);
                        }
                    }
                });
        return pageUtilsJsonObject;
    }


    @GetMapping("/oldProductSelectionForLaunchPage")
    @Operation(summary = "退换货产品选择（发起页面使用）")
    @PreAuthorize(hasPermission = "crm_refund_apply")
    public JsonObject<List<ReturnExchangeProductVO>> oldProductSelectionForLaunchPage(@RequestParam String contractNumber){
        return new JsonObject<>(performanceReportReturnExchangeService.queryPerformanceReportProductList(contractNumber));
    }



    @PostMapping("/filterProductHasSn")
    @Operation(summary = "选择出货序列号产品列表" ,description = "与PageProductChooseShipmentSn返回保持一致，对应total>0表示需要跳转【关联借试用、借转销、重点行业序列号页面】")
    @PreAuthorize(rightHandler = ProjectRightHandler.class,rightHandlerExtractArgsEL = {"#query.projectId"})
    public JsonObject<PageUtils<PriceReviewProductOwnDTO>> filterProductHasSn(@RequestBody @Valid ReturnExchangeSnPageQuery query) {
        query.setPersonId(UserInfoHolder.getCurrentPersonId());
        PageUtils<PriceReviewProductOwnDTO> paginate = PageUtils
                .paginate(returnExchangeService.filterProductHasSn(query), query.getPageSize(), query.getPageNum());
        return new JsonObject<>(paginate);
    }


    @GetMapping("/detail")
    @Operation(summary = "退换货流程详情")
    @PreFlowPermission
    public JsonObject<ReturnExchangeDetailVO> detail(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(performanceReportReturnExchangeService.detail(processInstanceId, UserInfoHolder.getCurrentPersonId()));
    }


    @PostMapping("/pagePerformanceExecuteByCondition")
    @Operation(summary = "分页查询业绩上报(发起退换货查询使用)", method = "POST")
    @PreAuthorize(hasPermission = "crm_refund_apply",dataScope = "crm_performance_execute_execute")
    public JsonObject<PageUtils<ReturnExchangeBaseVO>> pagePerformanceExecuteByCondition(@RequestBody ContractReturnExchangeSelectQuery query) {
        DataScopeParam dataScopeParam = AuthorizeContextHolder.getDataScopeParam();
        String currentAgentId = UserInfoHolder.getCurrentAgentId();

        //查询正在审批中的业绩上报退换货流程对应的业绩上报id
        List<String> excludeContractNumber = performanceReportReturnExchangeService.list(new LambdaQueryWrapper<PerformanceReportReturnExchange>()
                        .eq(FlowBaseEntity::getProcessState, ApprovalStatusEnum.SPZ.getCode())
                        .select(ReturnExchange::getContractNumber,ReturnExchange::getId))
                .stream().map(ReturnExchange::getContractNumber)
                .toList();

        PerformanceExecuteQuery executeQuery=new PerformanceExecuteQuery();
        executeQuery.setKeyWord(query.getKeyWord());
        executeQuery.setDataScopeParam(dataScopeParam);
        executeQuery.setExcludeContractNumber(excludeContractNumber);
        executeQuery.setPageNum(query.getPageNum());
        executeQuery.setPageSize(query.getPageSize());

        boolean innerUser=  StringUtils.isBlank(currentAgentId);
        // 列表数据来源：
        // 登录人为公司员工时：根据登录人查询流程的数据范围进行限制（人个数据、管辖部门数据等）；
        // 登录人为渠道商时：根据登录人的身份查询流程中签约单位为当前渠道商的数据；详见权限方案；
        PageUtils<PerformanceExecuteVO> performanceExecuteVOPageUtils;

        if (innerUser) {
            performanceExecuteVOPageUtils = performanceExecuteService.pageInternal(executeQuery);
        } else {
            dataScopeParam.setAgentId(currentAgentId);
            dataScopeParam.setPersonIdList(Collections.emptySet());
            performanceExecuteVOPageUtils = performanceExecuteService.pageAgent(executeQuery);
        }
        List<PerformanceExecuteVO> performanceExecuteVOS = ListUtils.emptyIfNull(performanceExecuteVOPageUtils.getList());
        Set<String> processInstanceId = performanceExecuteVOS.stream()
                .map(PerformanceExecuteVO::getPerformanceProcessInstanceId).collect(Collectors.toSet());
        Map<String, String> projectIdMap ;
        if (CollectionUtils.isNotEmpty(processInstanceId)){
             projectIdMap = processExtensionInfoMapper.selectList(new LambdaQueryWrapper<ProcessExtensionInfo>()
                            .in(ProcessExtensionInfo::getProcessInstanceId,processInstanceId)).stream()
                    .collect(Collectors.toMap(ProcessExtensionInfo::getProcessInstanceId, ProcessExtensionInfo::getProjectId));
        }else {
             projectIdMap = Collections.emptyMap();
        }

        PageUtils<ReturnExchangeBaseVO> convert = performanceExecuteVOPageUtils.convert(
                performanceExecuteVO -> {
                    ReturnExchangeBaseVO returnExchangeBaseVO = ReturnExchangeConvertor.INSTANCE.toReturnExchangeBaseVO(performanceExecuteVO);
                    returnExchangeBaseVO.setProjectId(projectIdMap.get(performanceExecuteVO.getPerformanceProcessInstanceId()));
                    return returnExchangeBaseVO;
                });

        return new JsonObject<>(convert);
    }

    @PostMapping("/addPerformanceReportProductSeparation")
    @Operation(summary = "匹配软硬分离产品")
    @PreFlowPermission(hasAnyNodes = {"reportReturnOrExchangePerformance_03","sid-11B9048E-6B7A-429A-9B21-061D65245438"})
    public JsonObject<Boolean> addPerformanceReportProductSeparation(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getById(performanceReportProductQuery.getPerformanceReportProductId());
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductOwn.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        return new JsonObject<>(performanceReportProductOwnService.insertPerformanceReportProductSeparation(performanceReportProductQuery));
    }

    @PostMapping("/addPerformanceReportProductSeparationAll")
    @Operation(summary = "重新匹配所有软硬分离产品")
    @PreFlowPermission(hasAnyNodes = {"reportReturnOrExchangePerformance_03","sid-11B9048E-6B7A-429A-9B21-061D65245438"})
    public JsonObject<Boolean> addPerformanceReportProductSeparationAll(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductQuery.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        PerformanceReportReturnExchange one = performanceReportReturnExchangeService.getOne(new LambdaQueryWrapper<PerformanceReportReturnExchange>().eq(PerformanceReportReturnExchange::getProcessInstanceId, performanceReportProductQuery.getReturnExchangeProcessInstanceId()));
        performanceReportProductQuery.setProcessInstanceId(one.getParentProcessInstanceId());
        return new JsonObject<>(performanceReportProductOwnService.insertPerformanceReportProductSeparationAll(performanceReportProductQuery));
    }

    @PostMapping("/addBorrowForForwardOrSpecialItemSeparation")
    @Operation(summary = "匹配借转销专项备货软硬分离产品")
    @PreFlowPermission(hasAnyNodes = {"reportReturnOrExchangePerformance_03","sid-11B9048E-6B7A-429A-9B21-061D65245438"})
    public JsonObject<Boolean> addBorrowForForwardOrSpecialItemSeparation(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductQuery.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        PerformanceReportReturnExchange one = performanceReportReturnExchangeService.getOne(new LambdaQueryWrapper<PerformanceReportReturnExchange>().eq(PerformanceReportReturnExchange::getProcessInstanceId, performanceReportProductQuery.getReturnExchangeProcessInstanceId()));
        performanceReportProductQuery.setProcessInstanceId(one.getParentProcessInstanceId());
        return new JsonObject<>(performanceReportProductOwnService.insertBorrowForForwardOrSpecialItemSeparation(performanceReportProductQuery));
    }

    @GetMapping("/getBorrowForForwardOrSpecialItemNum")
    @Operation(summary = "查询产品关联借试用或专项备货序列号数量")
    @PreFlowPermission
    public JsonObject<Integer> getBorrowForForwardOrSpecialItemNum(@RequestParam String performanceReportProductId) {
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getById(performanceReportProductId);
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductOwn.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportProductOwnService.selectBorrowForForwardOrSpecialItemNum(performanceReportProductId));
    }

    @GetMapping("/getReturnExchangeProductByProcessInstanceId")
    @Operation(summary = "04步查询产品详细信息")
    @PreFlowPermission
    public JsonObject<List<PerformanceReportProductOwnDTO>> getReturnExchangeProductByProcessInstanceId(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<PerformanceReportProductOwnDTO> performanceReportProductOwnDTOList = performanceReportReturnExchangeService.selectReturnExchangeProductByProcessInstanceId(processInstanceId);
        return new JsonObject<>(performanceReportProductOwnDTOList);
    }

    @GetMapping("/getReturnExchangeProductStatistics")
    @Operation(summary = "根据业绩上报退换货流程实例ID查询产品统计信息")
    @PreFlowPermission
    public JsonObject<PerformanceReportProductStatisticsDTO> getReturnExchangeProductStatistics(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject(performanceReportReturnExchangeService.getPerformanceReportProductStatistics(processInstanceId));
    }

    @PostMapping("/getPerformanceReportProductSeparationHardware")
    @Operation(summary = "查询 选择硬件物料代码")
    @PreFlowPermission(hasAnyNodes = {"reportReturnOrExchangePerformance_03","sid-11B9048E-6B7A-429A-9B21-061D65245438"})
    public JsonObject<List<CrmProductSeparationRelVo>> getPerformanceReportProductSeparationHardware(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getById(performanceReportProductQuery.getPerformanceReportProductId());
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductOwn.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        CrmProductSeparationRelQuery crmProductSeparationRelQuery = new CrmProductSeparationRelQuery();
        crmProductSeparationRelQuery.setStuffCode(performanceReportProductOwn.getStuffCode());
        crmProductSeparationRelQuery.setContractCompanyName(performanceReportProductQuery.getSupplierName());

        List<CrmProductSeparationRelVo> performanceReportProductSeparationDTOS = performanceReportProductOwnService.selectPerformanceReportProductSeparationHardware(crmProductSeparationRelQuery);
        return new JsonObject<>(performanceReportProductSeparationDTOS);
    }

    @PostMapping("/editPerformanceReportProductSeparationSoft")
    @Operation(summary = "修改 OS物料代码")
    @PreFlowPermission(hasAnyNodes = {"reportReturnOrExchangePerformance_03","sid-11B9048E-6B7A-429A-9B21-061D65245438"})
    public JsonObject<Boolean> editPerformanceReportProductSeparationSoft(@RequestBody PerformanceReportProductSeparationDTO performanceReportProductSeparationDTO){
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getById(performanceReportProductSeparationDTO.getPerformanceReportProductId());
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductOwn.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportProductOwnService.updatePerformanceReportProductSeparationSoft(performanceReportProductSeparationDTO));
    }

    @GetMapping("/getPerformanceReportProductSeparationSoft")
    @Operation(summary = "查询 选择OS物料代码")
    @PreFlowPermission(hasAnyNodes = {"reportReturnOrExchangePerformance_03","sid-11B9048E-6B7A-429A-9B21-061D65245438"})
    public JsonObject<List<CrmProductSeparationRelVo>> getPerformanceReportProductSeparationSoft(@RequestParam String supplierName){
        List<CrmProductSeparationRelVo> productSeparationRelVos = performanceReportProductOwnService.selectPerformanceReportProductSeparationSoft(supplierName);
        return new JsonObject<>(productSeparationRelVos);
    }

    @PostMapping("/editPerformanceReportProductSeparationHardware")
    @Operation(summary = "修改 硬件物料代码")
    @PreFlowPermission(hasAnyNodes = {"reportReturnOrExchangePerformance_03","sid-11B9048E-6B7A-429A-9B21-061D65245438"})
    public JsonObject<Boolean> editPerformanceReportProductSeparationHardware(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductQuery.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        PerformanceReportReturnExchange one = performanceReportReturnExchangeService.getOne(new LambdaQueryWrapper<PerformanceReportReturnExchange>().eq(PerformanceReportReturnExchange::getProcessInstanceId, performanceReportProductQuery.getReturnExchangeProcessInstanceId()));
        performanceReportProductQuery.setProcessInstanceId(one.getParentProcessInstanceId());
        return new JsonObject<>(performanceReportProductOwnService.updatePerformanceReportProductSeparationHardware(performanceReportProductQuery));
    }

    @PostMapping("/getPerformanceReportProductSeparationSn")
    @Operation(summary = "查询库存序列号")
    @PreFlowPermission(hasAnyNodes = {"reportReturnOrExchangePerformance_03","sid-11B9048E-6B7A-429A-9B21-061D65245438"})
    public JsonObject<List<CrmProductAgentInventoryVO>> getPerformanceReportProductSeparationSn(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportProductQuery.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        PerformanceReportReturnExchange one = performanceReportReturnExchangeService.getOne(new LambdaQueryWrapper<PerformanceReportReturnExchange>().eq(PerformanceReportReturnExchange::getProcessInstanceId, performanceReportProductQuery.getReturnExchangeProcessInstanceId()));
        performanceReportProductQuery.setProcessInstanceId(one.getParentProcessInstanceId());
        return new JsonObject<>(performanceReportProductOwnService.getPerformanceReportProductSeparationSn(performanceReportProductQuery));
    }

    @PostMapping("/editPerformanceReportProductSeparationSn")
    @Operation(summary = "国代修改序列号")
    @PreFlowPermission(hasAnyNodes = {"reportReturnOrExchangePerformance_03","sid-11B9048E-6B7A-429A-9B21-061D65245438"})
    public JsonObject<Boolean> editPerformanceReportProductSeparationSn(@RequestBody PerformanceReportProductSeparationDTO performanceReportProductSeparationDTO){
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getOne(new LambdaQueryWrapper<PerformanceReportProductOwn>()
                .eq(PerformanceReportProductOwn::getReturnExchangeProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID))
                .eq(PerformanceReportProductOwn::getDelFlag, 0)
                .eq(PerformanceReportProductOwn::getId, performanceReportProductSeparationDTO.getPerformanceReportProductId()));
        if (performanceReportProductOwn == null) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(performanceReportProductOwnService.updatePerformanceReportProductSeparationSn(performanceReportProductSeparationDTO));
    }

    @PostMapping("/editPerformanceReportProductSn")
    @Operation(summary = "省代修改序列号")
    @PreFlowPermission(hasAnyNodes = {"reportReturnOrExchangePerformance_03","sid-11B9048E-6B7A-429A-9B21-061D65245438"})
    public JsonObject<Boolean> editPerformanceReportProductSn(@RequestBody PerformanceReportProductOwnDTO performanceReportProductOwnDTO){
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getOne(new LambdaQueryWrapper<PerformanceReportProductOwn>()
                .eq(PerformanceReportProductOwn::getReturnExchangeProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID))
                .eq(PerformanceReportProductOwn::getDelFlag, 0)
                .eq(PerformanceReportProductOwn::getId, performanceReportProductOwnDTO.getId()));
        if (performanceReportProductOwn == null) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(performanceReportProductOwnService.updatePerformanceReportProductSn(performanceReportProductOwnDTO));
    }

    @DeleteMapping("/deletePerformanceReportProductSeparationSn")
    @Operation(summary = "清空序列号")
    @PreFlowPermission(hasAnyNodes = {"reportReturnOrExchangePerformance_03","sid-11B9048E-6B7A-429A-9B21-061D65245438"})
    public JsonObject<Boolean> deletePerformanceReportProductSeparationSn(@RequestParam String performanceReportProductId){
        PerformanceReportProductOwn performanceReportProductOwn = performanceReportProductOwnService.getOne(new LambdaQueryWrapper<PerformanceReportProductOwn>()
                .eq(PerformanceReportProductOwn::getReturnExchangeProcessInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID))
                .eq(PerformanceReportProductOwn::getDelFlag, 0)
                .eq(PerformanceReportProductOwn::getId, performanceReportProductId));
        if (performanceReportProductOwn == null) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(performanceReportProductOwnService.deletePerformanceReportProductSn(performanceReportProductId));
    }

    @GetMapping(value="/export")
    @Operation(summary = "导出序列号")
    @PreFlowPermission
    public void exportPerformanceReportProductSn() throws Exception{
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        List<PsnVO> psnVOS = performanceReportReturnExchangeService.exportPerformanceReportProductSn(processInstanceId);
        ExcelUtil<PsnVO> excelUtil = new ExcelUtil<>(PsnVO.class);
        excelUtil.exportExcel(response, psnVOS,"关联序列号");
    }

    @GetMapping("/getDynastyApprover")
    @Operation(summary = "查询国代审批人")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> getDynastyApprover() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(performanceReportReturnExchangeService.selectDynastyApprover(processInstanceId));
    }

    @PostMapping("/savePerformanceReportProductInfo")
    @Operation(summary = "04是否使用返点代替OS消耗")
    @PreFlowPermission(hasAnyNodes = {"reportReturnOrExchangePerformance_03","sid-11B9048E-6B7A-429A-9B21-061D65245438"})
    public JsonObject<Boolean> savePerformanceReportProductInfo(@RequestParam Integer rebateReplaceOs){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);

        return new JsonObject<>(performanceReportReturnExchangeService.savePerformanceReportProductInfo(processInstanceId, rebateReplaceOs));

    }

    @PostMapping("/completionConditions")
    @Operation(summary = "办理完毕条件")
    @PreFlowPermission
    public JsonObject<Boolean> completionConditions(@RequestBody PerformanceReportProductQuery performanceReportProductQuery){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        performanceReportProductQuery.setProcessInstanceId(processInstanceId);
        return new JsonObject<>(performanceReportReturnExchangeService.selectCompletionConditions(performanceReportProductQuery));
    }

    @GetMapping(value="/exportPerformanceReportProductList")
    @Operation(summary = "导出产品清单")
    @PreFlowPermission
    public void exportPerformanceReportProductList() throws Exception {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PerformanceReportReturnExchange reportReturnExchange = performanceReportReturnExchangeService.getOne(new LambdaQueryWrapper<PerformanceReportReturnExchange>().eq(PerformanceReportReturnExchange::getProcessInstanceId, processInstanceId).last("limit 1"));

        List<PerformanceReportProductListVO> performanceReportProductListVOS = performanceReportProductOwnService.exportPerformanceReportProductList(reportReturnExchange.getParentProcessInstanceId(),processInstanceId);
        ExcelUtil<PerformanceReportProductListVO> excelUtil = new ExcelUtil<>(PerformanceReportProductListVO.class);
        excelUtil.exportExcel(response, performanceReportProductListVOS,"产品清单");
    }

    @PostMapping("/importPerformanceReportProductList")
    @Operation(summary = "导入【产品清单】")
    @PreFlowPermission(hasAnyNodes = "sid-11B9048E-6B7A-429A-9B21-061D65245438")
    public JsonObject<Boolean> importPerformanceReportProductList(MultipartFile file) throws Exception {
        if(file == null || file.isEmpty()){
            throw new CrmException("参数异常", ResultEnum.FAIL.getResult());
        }
        return new JsonObject<>(performanceReportProductOwnService.importPerformanceReportProductList(file));
    }

    @GetMapping("/getDeliverySnInfo")
    @Operation(summary = "查询发货详情信息")
    @PreFlowPermission
    public JsonObject<List<PerformanceReportController.LogisticsInfoVO>> getDeliverySnInfo(@RequestParam String contractDeliveryId){
        PerformanceReportContractDelivery performanceReportContractDelivery1 = performanceReportContractDeliveryService.getById(contractDeliveryId);
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportContractDelivery1.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        List<PerformanceReportController.LogisticsInfoVO> logisticsInfoVOList = new ArrayList<>();
        List<DeliveryProductSnDTO> deliveryProductSnDTOS = performanceReportProductOwnService.selectDeliverySnInfo(contractDeliveryId);
        // 快递信息
        ListUtils.emptyIfNull(deliveryProductSnDTOS).forEach(deliveryProductSnDTO -> {
            BatchSubParam batchSubParam=new BatchSubParam();
            batchSubParam.setCompany(deliveryProductSnDTO.getCompanyName());
            batchSubParam.setKdybCom(deliveryProductSnDTO.getCompanyCode());
            batchSubParam.setNumber(deliveryProductSnDTO.getDeliveryNo());
            batchSubParam.setPhone("1111");
            List<LogisticsInfoDetail> logisticsInfoDetails =new ArrayList<>();
            logisticsInfoDetails.add(new LogisticsInfoDetail("3", deliveryProductSnDTO.getDeliveryNo(), "[洛阳市]您的快件已签收，如有疑问请电联快递员【徐好，电话：18336768057】，感谢您使用顺丰，期待再次为您服务。（主单总件数：1件）", "2024-08-17", "河南,洛阳市", "已签收", "洛阳市", "112.45404,34.619682", "luo yang shi", "3"));
            logisticsInfoDetails.add(new LogisticsInfoDetail("2", deliveryProductSnDTO.getDeliveryNo(), "[洛阳市]快件交给徐好，正在派送途中（联系电话：18336768057，顺丰已开启“安全呼叫”保护您的电话隐私,请放心接听！）（主单总件数：1件）", "2024-07-17", "河南,洛阳市", "派件中", "洛阳市", "112.45404,34.619682", "luo yang shi", "5"));
            logisticsInfoDetails.add(new LogisticsInfoDetail("1", deliveryProductSnDTO.getDeliveryNo(), "[洛阳市]快件已发车", "2024-06-17", "河南,洛阳市", "揽收", "洛阳市", "112.45404,34.619682", "luo yang shi", "103"));
            JsonObject<LogisticsInfo> info = new JsonObject<>();
            info.setResult(0);
            info.setObjEntity(new LogisticsInfo("1", "shutdown", "ZTO", "中通快递", "3", "1", "2023-11-11","北京市","洛阳市", logisticsInfoDetails, "1"));

            PerformanceReportController.LogisticsInfoVO logisticsInfoVO = new PerformanceReportController.LogisticsInfoVO();
            if (info.isSuccess()){
                logisticsInfoVO.setLogisticsInfo(info.getObjEntity());
            }
            logisticsInfoVO.setDeliveryProductSnDTO(deliveryProductSnDTO);
            logisticsInfoVOList.add(logisticsInfoVO);
        });
        return new JsonObject<>(logisticsInfoVOList);
    }

    @PostMapping("/saveDeliveryInfo")
    @Operation(summary = "添加发货详情信息")
    @PreFlowPermission(hasAnyNodes = "sid-11B9048E-6B7A-429A-9B21-061D65245438")
    public JsonObject<Boolean> saveDeliverySnInfo(@RequestBody PerformanceReportContractDeliveryDetailDTO performanceReportContractDeliveryDetailDTO){
        PerformanceReportContractDelivery performanceReportContractDelivery1 = performanceReportContractDeliveryService.getById(performanceReportContractDeliveryDetailDTO.getContractDeliveryId());
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportContractDelivery1.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(performanceReportProductOwnService.insertDeliverySnInfo(performanceReportContractDeliveryDetailDTO));
    }

    @GetMapping("/queryProductDeliverySnInfo")
    @Operation(summary = "查询选中的产品发货序列号")
    @PreFlowPermission
    public JsonObject<List<ContractDeliveryProductSnVO>> queryProductDeliverySnInfo(@RequestParam String contractDeliveryId){
        PerformanceReportContractDelivery performanceReportContractDelivery1 = performanceReportContractDeliveryService.getById(contractDeliveryId);
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportContractDelivery1.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        return new JsonObject<>(performanceReportReturnExchangeService.selectProductDeliverySnInfo(contractDeliveryId));
    }

    @GetMapping("/deleteDeliverySnInfo")
    @Operation(summary = "删除发货信息")
    @PreFlowPermission(hasAnyNodes = "sid-11B9048E-6B7A-429A-9B21-061D65245438")
    public JsonObject<Boolean> deleteDeliverySnInfo(@RequestParam String contractDeliveryDetailId){
        PerformanceReportContractDeliveryDetail performanceReportContractDeliveryDetail = performanceReportContractDeliveryDetailMapper.selectById(contractDeliveryDetailId);
        PerformanceReportContractDelivery performanceReportContractDelivery1 = performanceReportContractDeliveryService.getById(performanceReportContractDeliveryDetail.getContractDeliveryId());
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportContractDelivery1.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        return new JsonObject<>(performanceReportProductOwnService.deleteDeliverySnInfo(contractDeliveryDetailId));
    }

    @PostMapping("/importProductDeliverySn")
    @Operation(summary = "导入发货信息序列号")
    @PreFlowPermission(hasAnyNodes = "sid-11B9048E-6B7A-429A-9B21-061D65245438")
    public JsonObject<List<ContractDeliveryProductSnVO>> importProductDeliverySn(String contractDeliveryId, MultipartFile file) throws Exception {
        PerformanceReportContractDelivery performanceReportContractDelivery1 = performanceReportContractDeliveryService.getById(contractDeliveryId);
        PreFlowPermissionAspect.checkProcessInstanceId(performanceReportContractDelivery1.getReturnExchangeProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        if(file == null || file.isEmpty()){
            throw new CrmException("参数异常", ResultEnum.FAIL.getResult());
        }
        return new JsonObject<>(performanceReportReturnExchangeService.importProductDeliverySn(file, contractDeliveryId));
    }

    @PostMapping("/00edit")
    @Operation(summary = "00步编辑流程")
    @PreAuthorize(rightHandler = Step00RemoveEditProcessRightHandler.class)
    public JsonObject<Boolean> editProcess00(@RequestBody PerformanceReportReturnExchangeLaunchDTO launchDTO){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        launchDTO.setProcessInstanceId(processInstanceId);
        String projectId = processExtensionInfoMapper.selectOne(new LambdaQueryWrapper<ProcessExtensionInfo>()
                        .eq(ProcessExtensionInfo::getProcessInstanceId, processInstanceId).select(ProcessExtensionInfo::getProjectId))
                .getProjectId();
        PriceReviewMain priceReviewMain = priceReviewMainService.queryLatestPriceReviewMainByProjectId(projectId);
        if (priceReviewMain!=null&&ApprovalStatusEnum.SPZ.getCode().equals(priceReviewMain.getProcessState())){
            return JsonObject.errorT("存在审批中的价格评审，不可编辑退换货流程");
        }

        performanceReportReturnExchangeProcessService.edit00(launchDTO);
        return new JsonObject<>(true);
    }

    @GetMapping("/tabFinal")
    @Operation(summary = "标记终稿")
    @PreFlowPermission
    public JsonObject<Boolean> tabFinal(@RequestParam String docId) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PerformanceReportReturnExchange one = performanceReportReturnExchangeService.getOne(new LambdaQueryWrapper<PerformanceReportReturnExchange>()
                .eq(PerformanceReportReturnExchange::getProcessInstanceId, processInstanceId)
                .select(ReturnExchange::getId, ReturnExchange::getFinalDocId)
        );
        Set<String> finalDocId = SetUtils.emptyIfNull(one.getFinalDocId());
        Set<String> input = Stream.of(docId).collect(Collectors.toSet());
        input.addAll(finalDocId);
        one.setFinalDocId(input);
        return new JsonObject<>(performanceReportReturnExchangeService.updateById(one));
    }

    @GetMapping("/cancelFinal")
    @Operation(summary = "取消标记")
    @PreFlowPermission
    public JsonObject<Boolean> cancelFinal(@RequestParam String docId) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PerformanceReportReturnExchange one = performanceReportReturnExchangeService.getOne(new LambdaQueryWrapper<PerformanceReportReturnExchange>()
                .eq(PerformanceReportReturnExchange::getProcessInstanceId, processInstanceId)
                .select(ReturnExchange::getId, ReturnExchange::getFinalDocId)
        );
        Set<String> finalDocId = one.getFinalDocId();
        if (CollectionUtils.isEmpty(finalDocId)) {
            return new JsonObject<>(true);
        }else {
            finalDocId.remove(docId);
        }
        one.setFinalDocId(finalDocId);
        return new JsonObject<>(performanceReportReturnExchangeService.updateById(one));
    }

    // 登录人为公司员工时：根据登录人查询流程的数据范围进行限制（人个数据、管辖部门数据等）；
    // 登录人为渠道商时：根据登录人的身份查询流程中签约单位为当前渠道商的数据；详见权限方案；
    // 退换货	合同评审退换货列表	【TAB】页面访问	crm_refund	Y
    // 业绩上报退换货列表	【TAB】页面访问	crm_refund_performance_report	Y
    // 发起退换货	【功能】发起退换货	crm_refund_apply	N
    // 导出	【功能】导出列表	crm_refund_export	Y

    // 登录人为公司员工时：根据登录人查询流程的数据范围进行限制（人个数据、管辖部门数据等）；
    // 登录人为渠道商时：根据登录人的身份查询流程中签约单位为当前渠道商的数据；详见权限方案；
    // 业绩上报退换货列表
    // crm_refund_performance_report
    @PostMapping("/page")
    @PreAuthorize(hasPermission = "crm_refund_performance_report",dataScope = "crm_refund_performance_report")
    @Operation(summary = "业绩上报退换货流程列表")
    public JsonObject<PageUtils<ReturnExchangeItem>> page(@RequestBody ReturnExchangePageQuery query) {
        DataScopeParam dataScopeParam = query.getDataScopeParam();
        String agentId = dataScopeParam.getAgentId();

        if (!dataScopeParam.getAllScope()){
            boolean agentUser= StringUtils.isNotBlank(agentId);

            if (!agentUser){
                Set<String> personIds = dataScopeParam.getPersonIdList();
                query.setPersonIds(personIds);
            }else {
                query.setContractCompanyId(agentId);
            }
        }
        return new JsonObject<>(performanceReportReturnExchangeService.page(query));
    }


    @PostMapping("/export")
    @PreAuthorize(hasAllPermission ={"crm_refund_export","crm_refund_performance_report"} ,dataScope = "crm_refund_performance_report")
    @Operation(summary = "业绩上报退换货流程列表导出")
    @SneakyThrows
    public void export(@RequestBody ReturnExchangePageQuery query, HttpServletResponse response) {
        DataScopeParam dataScopeParam = query.getDataScopeParam();
        String agentId = dataScopeParam.getAgentId();
        if (!dataScopeParam.getAllScope()){
            boolean agentUser= StringUtils.isNotBlank(agentId);
            if (!agentUser){
                Set<String> personIds = dataScopeParam.getPersonIdList();
                query.setPersonIds(personIds);
            }else {
                query.setContractCompanyId(agentId);
            }
        }
        query.setPageSize(Integer.MAX_VALUE);
        PageUtils<ReturnExchangeItem> page = performanceReportReturnExchangeService.page(query);
        List<ReturnExchangeItem> list = page.getList();
        List<ReturnExchangeServiceImpl.ReturnExchangeExcelItem> returnExchangeExcelItems = ReturnExchangeConvertor.INSTANCE.toReturnExchangeExcelItem(list);

        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), ReturnExchangeServiceImpl.ReturnExchangeExcelItem.class).build()){
            response.addHeader(HttpHeaders.CONTENT_DISPOSITION, WebFilenameUtils.disposition("业绩上报退换货列表.xlsx",false));
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            excelWriter.write(returnExchangeExcelItems, EasyExcel.writerSheet("业绩上报退换货").build());
        }
    }

    @PostMapping("/updateContractDelivery")
    @Operation(summary = "01销售单位负责人修改合同发货")
    @PreFlowPermission
    public JsonObject<Boolean> updateContractDelivery(@RequestBody List<PerformanceReportContractDeliveryDTO> contractDeliveryList){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        performanceReportReturnExchangeProcessService.saveUpdateContractDelivery(contractDeliveryList,processInstanceId);
        return new JsonObject<>(true);
    }

    @GetMapping("/returnExchangeProductByProcessInstanceId")
    @Operation(summary = "业绩上报退换货产品列表【缺货下单】")
    @PreFlowPermission
    public JsonObject<PageUtils<PerformanceReportProductOwnDTO>> getproductInfoByProcessInstanceId(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ReturnExchangePageQuery query = new ReturnExchangePageQuery();
        query.setProcessInstanceId(processInstanceId);
        return new JsonObject<>(performanceReportReturnExchangeService.selectReturnExchangeNewProductByProcessInstanceId(query));
    }

    @GetMapping("/querySelectedProductDeliveryInfo")
    @Operation(summary = "查询选中的产品发货信息")
    @PreFlowPermission
    public JsonObject<List<PerformanceReportContractDeliveryDTO>> querySelectedProductDeliveryInfo(@RequestParam Set<String> recordIdSet){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(performanceReportReturnExchangeService.querySelectedProductDeliveryInfo(processInstanceId,recordIdSet));
    }

    @GetMapping("/oneClickReturnExchange")
    @Operation(summary = "查询是否显示一键退换货")
    @PreFlowPermission
    public JsonObject<String> oneClickReturnExchange(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(performanceReportReturnExchangeService.oneClickReturnExchange(processInstanceId));
    }

    @GetMapping("/getProcessInfoByPerformanceReportId")
    @Operation(summary = "缺货订单列表")
    @PreFlowPermission
    public JsonObject<List<ContractReviewMainBaseInfoDTO>> getProcessInfoByPerformanceReportId(@RequestParam Integer processSource, @RequestParam String performanceReportId,
                                                                                               @RequestParam(required = false) String contractProcessNumber){
        PerformanceReport byId = performanceReportService.getById(performanceReportId);
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PerformanceReportReturnExchange one = performanceReportReturnExchangeService.getOne(new LambdaQueryWrapper<PerformanceReportReturnExchange>().eq(PerformanceReportReturnExchange::getProcessInstanceId, headerValue));
        if (processSource == 1){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), one.getParentProcessInstanceId());
        }else if(processSource == 2) {
            List<PerformanceReportProductOwnDTO> performanceReportProductByProcessInstanceId = performanceReportService.getPerformanceReportProductByProcessInstanceId(byId.getProcessInstanceId());
            if (Optional.ofNullable(performanceReportProductByProcessInstanceId)
                    .orElse(Collections.emptyList())
                    .stream()
                    .noneMatch(dto -> headerValue.equals(dto.getReturnExchangeProcessInstanceId()))) {
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }
        String currentAgentId = UserInfoHolder.getCurrentAgentId();
        if (StringUtils.isNotEmpty(currentAgentId)){
            if(byId.getSupplierId().equals(currentAgentId)) {
                return new JsonObject<>(contractReviewFlowService.getProcessInfoByPerformanceReportId(performanceReportId, contractProcessNumber));
            }
        }
        return new JsonObject<>();
    }
}
