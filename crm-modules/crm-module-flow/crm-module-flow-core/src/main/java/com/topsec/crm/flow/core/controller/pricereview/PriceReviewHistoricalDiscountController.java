package com.topsec.crm.flow.core.controller.pricereview;

import com.topsec.crm.flow.api.dto.pricereview.PriceReviewHistoricalDiscountDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.service.PriceReviewHistoricalDiscountService;
import com.topsec.crm.project.api.RemoteProjectDirectlyService;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 历史折扣
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("priceReviewHistoricalDiscount")
@Tag(name = "价格评审历史折扣", description = "/priceReviewHistoricalDiscount")
@RequiredArgsConstructor
@Validated
public class PriceReviewHistoricalDiscountController {

    private final PriceReviewHistoricalDiscountService priceReviewHistoricalDiscountService;

    private final RemoteProjectDirectlyService remoteProjectDirectlyService;

    @GetMapping("/HistoricalDiscounts")
    @Operation(summary = "历史折扣")
    @PreFlowPermission
    public JsonObject<List<PriceReviewHistoricalDiscountDTO>> HistoricalDiscounts(@RequestParam List<String> agentCompanyIds) {
        return new JsonObject<>(priceReviewHistoricalDiscountService.countHistoricalDiscount(agentCompanyIds));
    }
}
