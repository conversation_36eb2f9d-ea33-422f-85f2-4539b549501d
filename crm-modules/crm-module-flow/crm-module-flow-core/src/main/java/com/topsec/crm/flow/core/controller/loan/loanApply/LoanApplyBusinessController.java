package com.topsec.crm.flow.core.controller.loan.loanApply;

import com.topsec.crm.flow.api.dto.loan.loanApply.LoanApplyDTO;
import com.topsec.crm.flow.api.dto.loan.loanApply.LoanApplyQuery;
import com.topsec.crm.flow.core.process.impl.TfsProcessService;
import com.topsec.crm.flow.core.service.LoanApplyService;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsTaskClient;
import com.topsec.vo.task.TaskHistoryVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/business/loanApply")
@Tag(name = "【借款流程-业务】", description = "loanApply")
@RequiredArgsConstructor
@Validated
public class LoanApplyBusinessController extends BaseController {

    private final LoanApplyService loanApplyService;

    private final TfsTaskClient tfsTaskClient;

    private final TfsProcessService tfsProcessService;

    @PreAuthorize(hasPermission = "crm_loan",dataScope = "crm_loan")
    @PostMapping("/loanApplyPage")
    @Operation(summary = "借款列表")
    public JsonObject<PageUtils<LoanApplyDTO>> loanApplyPage(@RequestBody LoanApplyQuery query) {
        startPage();
        return new JsonObject<>(loanApplyService.loanApplyPage(query));
    }

    @PreAuthorize(hasPermission = "crm_loan_export",dataScope = "crm_loan_export")
    @PostMapping("/loanApplyExport")
    @Operation(summary = "借款列表导出")
    public void loanApplyExport(@RequestBody LoanApplyQuery query) throws IOException {
        List<LoanApplyDTO> loanApplyDTOS = loanApplyService.loanApplyExport(query);
        ExcelUtil<LoanApplyDTO> excelUtil = new ExcelUtil<>(LoanApplyDTO.class);
        excelUtil.exportExcel(response, loanApplyDTOS,"借款申请");
    }

    @PreAuthorize(hasPermission = "crm_loan",dataScope = "crm_loan")
    @GetMapping("/queryById")
    @Operation(summary = "查询借款详情")
    public JsonObject<LoanApplyDTO> queryById(@RequestParam String id) {
        return new JsonObject<>(loanApplyService.queryById(id));
    }

    @PreAuthorize(hasPermission = "crm_loan",dataScope = "crm_loan")
    @GetMapping("/isCanToCost")
    @Operation(summary = "是否可借款转费用",parameters = {@Parameter(name = "processInstanceId",description = "借款流程实例ID")})
    public JsonObject<Boolean> isCanToCost(@RequestParam String processInstanceId){
        return new JsonObject<>(loanApplyService.isCanToCost(processInstanceId));
    }

    @GetMapping("/queryLoanAuthCommentList")
    @Operation(summary = "根据流程实例id查询审批进度以及评论回复")
    @PreAuthorize
    public JsonObject<List<TaskHistoryVo>> queryInvoiceAuthCommentList(@RequestParam String processInstanceId) {
        String currentAccountId = getCurrentAccountId();
        return Optional.ofNullable(tfsTaskClient.getTaskHistoryList(processInstanceId)).map(
                        taskHistoryVo->{
                            tfsProcessService.authCommentList(taskHistoryVo.getObjEntity(),currentAccountId);
                            return taskHistoryVo;
                        })
                .orElseThrow(() -> new CrmException("查询审批意见失败"));
    }

}
