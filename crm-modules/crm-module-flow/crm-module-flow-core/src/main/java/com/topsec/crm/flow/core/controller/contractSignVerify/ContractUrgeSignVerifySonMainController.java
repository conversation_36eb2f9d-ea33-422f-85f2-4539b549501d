package com.topsec.crm.flow.core.controller.contractSignVerify;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.*;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewCustomerDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.process.impl.ContractSignVerifyProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contractUrgeSignVerifySonMain")
@Tag(name = "子催交签验收单相关Controller", description = "/contractUrgeSignVerifySonMain/flow")
@RequiredArgsConstructor
@Validated
public class ContractUrgeSignVerifySonMainController extends BaseController {
    @Autowired
    private IContractUrgeSignVerifySonMainService contractUrgeSignVerifySonMainService;

    @GetMapping("/info")
    @Operation(summary = "签验收单详情信息")
    @PreFlowPermission
    public JsonObject<ContractUrgeSignVerifySonMainVo> info(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询流程基本信息
        ContractUrgeSignVerifySonMain urgeSignVerifySonMain = contractUrgeSignVerifySonMainService.query().eq("process_instance_id", processInstanceId).one();
        ContractUrgeSignVerifySonMainVo csvmv = HyperBeanUtils.copyPropertiesByJackson(urgeSignVerifySonMain, ContractUrgeSignVerifySonMainVo.class);

        //2.查询部门负责人信息
        JsonObject<List<EmployeeVO>> byDeptLeader = tosDepartmentClient.findByDeptLeader(csvmv.getDeptId());
        if(byDeptLeader.isSuccess()){
            List<EmployeeVO> objEntity = byDeptLeader.getObjEntity();
            csvmv.setDeptLeaders(objEntity);
        }

        return new JsonObject<>(csvmv);
    }

}
