package com.topsec.crm.flow.core.controllerhidden.teambuilding;

import com.topsec.crm.flow.api.dto.teamBuilding.TeamBuildingProcessVO;
import com.topsec.crm.flow.core.service.TeamBuildingService;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-11-06  15:44
 * @Description:
 */

@RestController
@RequestMapping("/hidden/teamBuilding")
@Tag(name = "团队组建-不对外开放", description = "/hidden/teamBuilding")
@RequiredArgsConstructor
@Validated
public class HiddenTeamBuildingController extends BaseController {

    private final TeamBuildingService teamBuildingService;

    @GetMapping("/teamBuildingProcessIsLaunch")
    @Operation(summary = "团队组建流程是否发起")
    public JsonObject<TeamBuildingProcessVO> teamBuildingProcessIsLaunch(@RequestParam String projectId){
        CrmAssert.notNull("项目id不能为null",projectId);
        return new JsonObject<>(teamBuildingService.teamBuildingProcessIsLaunch(projectId));
    }
}