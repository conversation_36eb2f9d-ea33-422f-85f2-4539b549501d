package com.topsec.crm.flow.core.controllerhidden.contractSignVerify;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedVo;
import com.topsec.crm.flow.api.dto.contractSignVerify.UnconfirmedExportVO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewCustomerDTO;
import com.topsec.crm.flow.core.entity.ContractSignVerifyAttachment;
import com.topsec.crm.flow.core.entity.ContractSignVerifyMain;
import com.topsec.crm.flow.core.entity.ContractUnconfirmed;
import com.topsec.crm.flow.core.entity.ContractUnconfirmedUrge;
import com.topsec.crm.flow.core.service.IContractSignVerifyMainService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.enums.ContractEnum;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/hidden/contractUnconfirmed")
@RequiredArgsConstructor
@Validated
public class HiddenContractUnconfirmedController extends BaseController {
    @Autowired
    private RemoteContractExecuteService remoteContractExecuteService;

    @Autowired
    private IContractUnconfirmedService contractUnconfirmedService;
    @Autowired
    private RemoteContractReviewService remoteContractReviewService;


    /**
     * 每日同步未确认明细信息
     */
    @PostMapping("/syncUnconfirmed")
    public JsonObject<Boolean> syncUnconfirmed() {
        JsonObject<List<ContractUnconfirmedVo>> unConfirmDetail = remoteContractExecuteService.getUnConfirmDetail();
        if(unConfirmDetail.isSuccess()){
            List<ContractUnconfirmedVo> unconfirmedVos = unConfirmDetail.getObjEntity();
            if(CollectionUtil.isNotEmpty(unconfirmedVos)){
                // 1.清除历史数据
                contractUnconfirmedService.remove(new QueryWrapper<ContractUnconfirmed>());

                //2.插入-未确认（确认条款）明细列表
                contractUnconfirmedService.saveOrUpdateBatch(HyperBeanUtils.copyListPropertiesByJackson(unconfirmedVos,ContractUnconfirmed.class));
            }

        }

        return new JsonObject<>(true);
    }

    /**
     * 查询未确认明细列表记录-用来做审批通过后的快照查询
     */
    @PostMapping("/selectList")
    public JsonObject<List<ContractUnconfirmedVo>> selectList(@RequestBody ContractUnconfirmedVo contractUnconfirmedVo) {
        startPage();
        List<ContractUnconfirmed> list = contractUnconfirmedService.query()
                .eq(StringUtils.isNotBlank(contractUnconfirmedVo.getContractNumber()), "contract_number", contractUnconfirmedVo.getContractNumber())
                .eq(StringUtils.isNotBlank(contractUnconfirmedVo.getId()),"id", contractUnconfirmedVo.getId())
                .list();

        return new JsonObject<>(HyperBeanUtils.copyListPropertiesByJackson(list,ContractUnconfirmedVo.class));
    }

}
