package com.topsec.crm.flow.core.controller.borrowForSell;

import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellAttachmentDTO;
import com.topsec.crm.flow.core.entity.BorrowForSell;
import com.topsec.crm.flow.core.service.IBorrowForSellAttachmentService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 借转销附件信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/borrowForSellAttachmentBusiness")
@Tag(name = "借转销附件业务接口", description = "/borrowForSellAttachmentBusiness")
public class BorrowForSellAttachmentBusinessController {

    @Autowired
    private IBorrowForSellAttachmentService borrowForSellAttachmentService;

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_details", dataScope = "crm_flow_borrow_for_sell_details")
    @PostMapping("/saveBorrowForSellAttachment")
    @Operation(summary = "保存借转销流程附件信息")
    public JsonObject<Boolean> saveBorrowForSellAttachment(@RequestParam String borrowId,@RequestParam Integer attachmentGroup, @RequestBody List<BorrowForSellAttachmentDTO> attachments) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList()) || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(byId.getSalesmanPersonId()) || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(byId.getCreateUser())){
            return new JsonObject<>(borrowForSellAttachmentService.saveBorrowForSellAttachment(borrowId,attachmentGroup,attachments));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }
}

