package com.topsec.crm.flow.core.controller.backingmaterial;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.api.dto.backingmaterial.BackingMaterialCompanyInfo;
import com.topsec.crm.flow.api.dto.backingmaterial.BackingMaterialFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.backingmaterial.SigningAgentDetailDTO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewHistoricalDiscountDTO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewMainInfo;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductQuery;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.handler.right.ProjectRightHandler;
import com.topsec.crm.flow.core.mapstruct.PriceReviewProductConvertor;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyPriceStatisticsVO;
import com.topsec.crm.project.api.entity.CrmProjectOutsourcingServiceVo;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.crm.project.api.entity.CrmProjectProductThirdVo;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@RestController
@RequestMapping("/backingMaterial")
@Tag(name = "支撑材料", description = "/backingMaterial")
@RequiredArgsConstructor
@Validated
public class BackingMaterialController extends BaseController {

    private final BackingMaterialService backingMaterialService;
    private final PriceReviewStatisticsService priceReviewStatisticsService;
    private final PriceReviewSpecialCodeService priceReviewSpecialCodeService;
    private final PriceReviewProductOwnService priceReviewProductOwnService;
    private final PriceReviewMainService priceReviewMainService;
    private final PriceReviewHistoricalDiscountService priceReviewHistoricalDiscountService;
    private final PriceReviewProductThirdService priceReviewProductThirdService;
    private final PriceReviewProductServiceOutsourcingService priceReviewProductServiceOutsourcingService;
    private final PriceReviewFlowService priceReviewFlowService;
    private final PriceReviewSaleAgreementRelService priceReviewSaleAgreementRelService;
    private final SalesAgreementReviewMainService salesAgreementReviewMainService;


    /**
     * 分页查询
     */
    public JsonObject<PageUtils<BackingMaterialFlowLaunchDTO>> page(@RequestBody BackingMaterialFlowLaunchDTO backingMaterialFlowLaunchDTO){

        ArrayList<BackingMaterialFlowLaunchDTO> list = new ArrayList<>();
        startPage();
        List<BackingMaterialFlowLaunchDTO> backingMaterialFlowLaunchDTOS = backingMaterialService.selectBackingMaterialList(backingMaterialFlowLaunchDTO);
        backingMaterialFlowLaunchDTOS.stream().forEach(backingMaterialFlowLaunchDTO1 -> list.add(HyperBeanUtils.copyProperties(backingMaterialFlowLaunchDTO1, BackingMaterialFlowLaunchDTO::new)));

        PageUtils pageUtils = getDataTable(backingMaterialFlowLaunchDTOS, list);
        return new JsonObject<>(pageUtils);
    }

    /**
     * 根据流程ID查询支撑材料详细信息
     */
    @GetMapping("/getInfo")
    @Operation(summary = "根据延交流程ID查询支撑材料详细信息")
    @PreFlowPermission
    public JsonObject<BackingMaterialFlowLaunchDTO> getBackingMaterialInfo(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject(backingMaterialService.getInfo(processInstanceId));
    }

    /**
     * 查询支撑材料列表
     */
    @PostMapping("/list")
    @Operation(summary = "查询 支撑材料列表")
    @PreFlowPermission
    public JsonObject<BackingMaterialFlowLaunchDTO> list(@RequestBody BackingMaterialFlowLaunchDTO backingMaterialFlowLaunchDTO){

        return new JsonObject(backingMaterialService.selectBackingMaterialList(backingMaterialFlowLaunchDTO));
    }

    /**
     * 根据价审ID查询 支撑材料详细信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据价审流程ID查询 支撑材料详细信息")
    @PreFlowPermission
    public JsonObject<BackingMaterialFlowLaunchDTO> getInfo(@PathVariable("id") String priceReviewProcessId){
        return  new JsonObject(backingMaterialService.selectBackingMaterialByPriceReviewProcessId(priceReviewProcessId));
    }

    /**
     * 根据价审ID查询 可提供的支撑材料信息
     */
    @GetMapping("/getProvideCommitmentInfo/{id}")
    @Operation(summary = "查询 可提供的支撑材料信息")
    @PreFlowPermission
    public JsonObject<BackingMaterialFlowLaunchDTO> getProvideCommitmentInfo(@PathVariable("id") String priceReviewProcessId){
        return  new JsonObject(backingMaterialService.selectProvideCommitmentInfoById(priceReviewProcessId));
    }

    /**
     * 根据价审ID查询 可提供并且不延交的支撑材料信息
     */
    @GetMapping("/getProvideAndNotDeferCommitmentInfo/{id}")
    @Operation(summary = "查询 可提供并且不延交的支撑材料信息")
    @PreFlowPermission
    public JsonObject<BackingMaterialFlowLaunchDTO> getProvideAndNotDeferCommitmentInfo(@PathVariable("id") String priceReviewProcessId){
        return  new JsonObject(backingMaterialService.selectProvideAndNotDeferCommitmentInfoById(priceReviewProcessId));
    }

    /**
     * 根据价审ID查询 可提供的支撑材料信息
     */
    @GetMapping("/getNotProvideCommitmentInfo/{id}")
    @Operation(summary = "查询 不可提供的支撑材料信息")
    @PreFlowPermission
    public JsonObject<BackingMaterialFlowLaunchDTO> getNotProvideCommitmentInfo(@PathVariable("id") String priceReviewProcessId){
        return  new JsonObject(backingMaterialService.selectNotProvideCommitmentInfoById(priceReviewProcessId));
    }

    @GetMapping("/getFirstDeferCommitmentInfo/{id}")
    @Operation(summary = "查询 首次延交的支撑材料信息")
    @PreFlowPermission
    public JsonObject<BackingMaterialFlowLaunchDTO> getFirstDeferCommitmentInfo(@PathVariable("id") String priceReviewProcessId){
        return  new JsonObject(backingMaterialService.selectFirstDeferCommitmentInfoById(priceReviewProcessId));
    }

    /**
     * 根据价审ID查询 可提供并且延交的支撑材料信息
     */
    @GetMapping("/getProvideAndDeferCommitmentInfo/{id}")
    @Operation(summary = "查询 二次延交延交的支撑材料信息")
    @PreFlowPermission
    public JsonObject<BackingMaterialFlowLaunchDTO> getProvideAndDeferCommitmentInfo(@PathVariable("id") String priceReviewProcessId){
        return  new JsonObject(backingMaterialService.selectProvideAndDeferCommitmentInfoById(priceReviewProcessId));
    }

    /**
     * 新增【支撑材料】
     */
    /*@PostMapping("/add")
    @Operation(summary = "新增 支撑材料")
    @PreFlowPermission
    public JsonObject<Boolean> add(@RequestBody List<BackingMaterialFlowLaunchDTO> backingMaterialFlowLaunchDTOList)
    {
        return new JsonObject(backingMaterialService.insertBackingMaterial(backingMaterialFlowLaunchDTOList));
    }*/

    /**
     * 修改 支撑材料
     */
    @PostMapping("/edit")
    @Operation(summary = "价审-修改 支撑材料")
    @PreFlowPermission
    public JsonObject<Boolean> edit(@RequestBody List<BackingMaterialFlowLaunchDTO> backingMaterialFlowLaunchDTOList){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        for (BackingMaterialFlowLaunchDTO backingMaterialFlowLaunchDTO : backingMaterialFlowLaunchDTOList) {
            backingMaterialFlowLaunchDTO.setPriceReviewProcessInstanceId(processInstanceId);
        }
        return new JsonObject(backingMaterialService.updateBackingMaterial(backingMaterialFlowLaunchDTOList));
    }

    @PostMapping("/editYJ")
    @Operation(summary = "延交-修改 支撑材料")
    @PreFlowPermission
    public JsonObject<Boolean> editYJ(@RequestBody List<BackingMaterialFlowLaunchDTO> backingMaterialFlowLaunchDTOList){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        for (BackingMaterialFlowLaunchDTO backingMaterialFlowLaunchDTO : backingMaterialFlowLaunchDTOList) {
            backingMaterialFlowLaunchDTO.setProcessInstanceId(processInstanceId);
        }
        return new JsonObject(backingMaterialService.updateBackingMaterial(backingMaterialFlowLaunchDTOList));
    }



    /**
     * 删除 支撑材料
     */
    /*@DeleteMapping("/{id}")
    @Operation(summary = "删除 支撑材料")
    @PreFlowPermission
    public JsonObject<Boolean> deleteBackingMaterial(@PathVariable String id){
        return new JsonObject(backingMaterialService.deleteBackingMaterial(id));
    }*/


    /**
     * 承诺项渠道名称
     */
    @GetMapping("/getBackingMaterialCompanyInfo")
    @Operation(summary = "承诺项渠道名称")
    @PreAuthorize(rightHandler = ProjectRightHandler.class, rightHandlerExtractArgsEL = {"#projectId"})
    public JsonObject<BackingMaterialCompanyInfo> getBackingMaterialCompanyInfo(@RequestParam String projectId){

        return  new JsonObject(backingMaterialService.selectBackingMaterialCompanyInfo(projectId));
    }


    @GetMapping("/getSigningAgentDetail")
    @Operation(summary = "签约渠道详情")
    @PreFlowPermission
    public JsonObject<SigningAgentDetailDTO> getSigningAgentDetail(@RequestParam String agentCompanyId){

        return new JsonObject(backingMaterialService.selectSigningAgentDetail(agentCompanyId));
    }

    @GetMapping("/getDisplayCommitmentInfo")
    @Operation(summary = "判断是否显示承诺项")
    @PreAuthorize(rightHandler = ProjectRightHandler.class, rightHandlerExtractArgsEL = {"#projectId"})
    public JsonObject<Boolean> getDisplayCommitmentInfo(@RequestParam String projectId){
        return  new JsonObject(backingMaterialService.selectDisplayCommitmentInfo(projectId));
    }

    @GetMapping("/getDelayedProvidedInfo")
    @Operation(summary = "是否存在未如期提供支撑材料")
    @PreFlowPermission
    public JsonObject<Boolean> getDelayedProvidedInfo(@RequestParam String agentCompanyId){

        return new JsonObject(backingMaterialService.selectDelayedProvidedInfo(agentCompanyId));
    }

    @GetMapping("/queryProjectPriceStatistics")
    @Operation(summary = "产品报价汇总")
    @PreFlowPermission
    public JsonObject<CrmProjectDirectlyPriceStatisticsVO> queryProjectPriceStatistics(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String priceReviewProcessInstanceId = queryPriceReviewProcessInstanceId(processInstanceId);
        return new JsonObject<>(priceReviewStatisticsService.queryProjectDirectlyPriceStatisticsByProcessInstanceId(priceReviewProcessInstanceId, UserInfoHolder.getCurrentPersonId()));
    }

    @PostMapping("/queryOwnProductPriceDetails")
    @Operation(summary = "产品报价明细-自有产品")
    @PreFlowPermission
    public JsonObject<PageUtils<CrmProjectProductOwnVO>> queryOwnProductPriceDetails(@RequestBody @Valid PriceReviewProductQuery query) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String priceReviewProcessInstanceId = queryPriceReviewProcessInstanceId(processInstanceId);
        query.setProcessInstanceId(priceReviewProcessInstanceId);
        return new JsonObject<>(priceReviewProductOwnService.queryOwnProductPriceDetails(query));
    }


    @GetMapping("/baseInfo")
    @Operation(summary = "价格评审基础信息")
    @PreFlowPermission
    public JsonObject<PriceReviewMainInfo> baseInfo() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String priceReviewProcessInstanceId = queryPriceReviewProcessInstanceId(processInstanceId);
        return new JsonObject<>(priceReviewMainService.baseInfo(priceReviewProcessInstanceId));
    }

    @Operation(summary = "历史价格审批-展示该项目的近三次办结的价格评审流")
    @GetMapping("/queryHistoryPassedPriceReview")
    @PreFlowPermission
    public JsonObject<List<PriceReviewMainInfo>> queryHistoryPassedPriceReview(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String priceReviewProcessInstanceId = queryPriceReviewProcessInstanceId(processInstanceId);
        return new JsonObject<>(priceReviewMainService.queryHistoryPassedPriceReview(priceReviewProcessInstanceId));
    }

    @Operation(summary = "当前登录人是否是特殊代码审批人")
    @GetMapping("/isSpecialCodeApprovePerson")
    @PreFlowPermission
    public JsonObject<Boolean> isSpecialCodeApprovePerson() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String priceReviewProcessInstanceId = queryPriceReviewProcessInstanceId(processInstanceId);
        String currentPersonId = UserInfoHolder.getCurrentPersonId();
        List<PriceReviewSpecialCode> specialCodeList = priceReviewSpecialCodeService.list(new QueryWrapper<PriceReviewSpecialCode>()
                .eq("process_instance_id", priceReviewProcessInstanceId));
        return new JsonObject<>(priceReviewStatisticsService.isSpecialCodeApprovePerson(specialCodeList, currentPersonId));
    }

    @GetMapping("/HistoricalDiscounts")
    @Operation(summary = "历史折扣")
    @PreFlowPermission
    public JsonObject<List<PriceReviewHistoricalDiscountDTO>> HistoricalDiscounts(@RequestParam List<String> agentCompanyIds) {
        return new JsonObject<>(priceReviewHistoricalDiscountService.countHistoricalDiscount(agentCompanyIds));
    }

    @PostMapping("/queryThirdProductPriceDetails")
    @Operation(summary = "产品报价明细-第三方产品")
    @PreFlowPermission
    public JsonObject<PageUtils<CrmProjectProductThirdVo>> queryThirdProductPriceDetails(@RequestBody @Valid PriceReviewProductQuery query) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String priceReviewProcessInstanceId = queryPriceReviewProcessInstanceId(processInstanceId);
        query.setProcessInstanceId(priceReviewProcessInstanceId);
        return new JsonObject<>(priceReviewProductThirdService.queryThirdProductPriceDetails(query));
    }

    @PostMapping("/queryOutsourcingProductPriceDetails")
    @Operation(summary = "产品报价明细-外包服务费")
    @PreFlowPermission
    public JsonObject<PageUtils<CrmProjectOutsourcingServiceVo>> queryOutsourcingProductPriceDetails(@RequestBody @Valid PriceReviewProductQuery query) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String priceReviewProcessInstanceId = queryPriceReviewProcessInstanceId(processInstanceId);
        query.setProcessInstanceId(priceReviewProcessInstanceId);
        List<PriceReviewProductServiceOutsourcing> outsourcing = priceReviewProductServiceOutsourcingService
                .queryProjectOutsourcingByProcessInstanceId(priceReviewProcessInstanceId);
        PageUtils<CrmProjectOutsourcingServiceVo> converted = PageUtils.paginate(outsourcing, query.getPageSize(), query.getPageNum())
                .convert(PriceReviewProductConvertor.INSTANCE::toOutsourcingCrm);
        return new JsonObject<>(converted);
    }

    @Operation(summary = "逐级上级到能包下折扣和价差的或到1级，第一次查传personId，personId为该流程发起人；之后无需传入")
    @GetMapping("/querySuperiorContainApproverList")
    @PreFlowPermission
    public JsonObject<List<FlowPerson>> querySuperiorContainApproverList(@RequestParam(required = false) String personId) {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String priceReviewProcessInstanceId = queryPriceReviewProcessInstanceId(processInstanceId);
        //是否逐级之前
        boolean before = StringUtils.isNotBlank(personId);
        //当前登录人
        String currentPeronId = UserInfoHolder.getCurrentPersonId();
        personId = !before ? currentPeronId : personId;
        return new JsonObject<>(priceReviewFlowService.querySuperiorContainApproverList(priceReviewProcessInstanceId, personId, before));
    }

    @Operation(summary = "检查折扣是否为1.1或更低")
    @GetMapping("/isOnePointOneDiscountOrLess")
    @PreFlowPermission
    public JsonObject<Boolean> isOnePointOneDiscountOrLess() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String priceReviewProcessInstanceId = queryPriceReviewProcessInstanceId(processInstanceId);
        return new JsonObject<>(priceReviewFlowService.isOnePointOneDiscountOrLess(priceReviewProcessInstanceId));
    }

    @Operation(summary = "查询销售协议信息--流程进行中用")
    @GetMapping("/querySalesAgreement")
    @PreFlowPermission
    public JsonObject<List<SalesAgreementReviewMain>> querySalesAgreement(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String priceReviewProcessInstanceId = queryPriceReviewProcessInstanceId(processInstanceId);
        List<String> saleAgreement = priceReviewSaleAgreementRelService
                .list(new LambdaQueryWrapper<PriceReviewSaleAgreementRel>()
                        .eq(PriceReviewSaleAgreementRel::getPriceReviewProcessInstanceId, priceReviewProcessInstanceId)).stream()
                .map(PriceReviewSaleAgreementRel::getSaleAgreementId)
                .collect(Collectors.toList());
        List<SalesAgreementReviewMain> fromSalesAgreement = salesAgreementReviewMainService.findSalesAgreementByIds(saleAgreement);
        return new JsonObject<>(fromSalesAgreement);
    }


    private String queryPriceReviewProcessInstanceId(String processInstanceId) {
        return backingMaterialService
                .list(new LambdaQueryWrapper<BackingMaterial>()
                        .eq(BackingMaterial::getProcessInstanceId, processInstanceId)
                        .select(BackingMaterial::getPriceReviewProcessInstanceId))
                .stream().map(BackingMaterial::getPriceReviewProcessInstanceId)
                .findFirst().orElse(null);
    }

}
