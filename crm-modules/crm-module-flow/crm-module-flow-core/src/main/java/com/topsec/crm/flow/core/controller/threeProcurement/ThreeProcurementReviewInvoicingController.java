package com.topsec.crm.flow.core.controller.threeProcurement;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.topsec.crm.flow.api.vo.ThreeProcurementReviewInvoicingVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ThreeProcurementReviewInvoicing;
import com.topsec.crm.flow.core.entity.ThreeProcurementReviewMain;
import com.topsec.crm.flow.core.service.ThreeProcurementReviewInvoicingService;
import com.topsec.crm.flow.core.service.ThreeProcurementReviewMainService;
import com.topsec.crm.flow.core.service.ThreeProcurementReviewPaymentProvisionService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购开票Controller
 *
 * @date 2024-09-02
 */
@RestController
@RequestMapping("/threeProcurementReviewInvoicing")
@Tag(name = "【采购开票】", description = "threeProcurementReviewInvoicing")
@RequiredArgsConstructor
@Validated
public class ThreeProcurementReviewInvoicingController extends BaseController
{

    private final  ThreeProcurementReviewInvoicingService threeProcurementReviewInvoicingService;
    private final  ThreeProcurementReviewPaymentProvisionService threeProcurementReviewPaymentProvisionService;
    private final ThreeProcurementReviewMainService threeProcurementReviewMainService;

    /**
     * 分页查询【采购开票】
     */
    @GetMapping("/selectThreeProcurementReviewInvoicingListByProcessInstanceId")
    @Operation(summary = "查询采购开票")
    @PreFlowPermission
    public JsonObject<List<ThreeProcurementReviewInvoicingVo>> selectThreeProcurementReviewInvoicingListByProcessInstanceId(@RequestParam String processInstanceId)
    {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<ThreeProcurementReviewInvoicing> threeProcurementReviewInvoicingList = threeProcurementReviewInvoicingService.selectThreeProcurementReviewInvoicingByProcessInstanceId(processInstanceId);
        List<ThreeProcurementReviewInvoicingVo> threeProcurementReviewInvoicingVos = HyperBeanUtils.copyListPropertiesByJackson(threeProcurementReviewInvoicingList, ThreeProcurementReviewInvoicingVo.class);
        NameUtils.setName(threeProcurementReviewInvoicingVos);
        return new JsonObject<>(threeProcurementReviewInvoicingVos);
    }



    /**
     * 新增【采购开票】
     */
    @PostMapping("/add")
    @Operation(summary = "新增【采购开票】")
    @PreFlowPermission
    public JsonObject<Boolean> add(@RequestBody ThreeProcurementReviewInvoicingVo threeProcurementReviewInvoicingVo)
    {
        PreFlowPermissionAspect.checkProcessInstanceId(threeProcurementReviewInvoicingVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        BigDecimal invoicingPrice = threeProcurementReviewInvoicingVo.getInvoicingPrice();
        if (invoicingPrice == null){
            throw new CrmException("开票金额不能为空");
        }
        ThreeProcurementReviewMain threeProcurementReviewMain = threeProcurementReviewMainService.selectThreeProcurementReviewMainByProcessInstanceId(threeProcurementReviewInvoicingVo.getProcessInstanceId());
        if (threeProcurementReviewMain == null){
            throw new CrmException("采购审批流程不存在");
        }
        BigDecimal payableAmount = threeProcurementReviewPaymentProvisionService.selectPayableAmount(threeProcurementReviewMain.getProcessInstanceId(), threeProcurementReviewMain.getProcessNumber(), true);
        int result = invoicingPrice.compareTo(payableAmount);
        if (result > 0){
            throw new CrmException("开票金额不能大于应付金额");
        }
        List<ThreeProcurementReviewInvoicing> threeProcurementReviewInvoicingList = threeProcurementReviewInvoicingService.selectThreeProcurementReviewInvoicingByProcessInstanceId(threeProcurementReviewMain.getParentProcessInstanceId());
        if (CollectionUtils.isNotEmpty(threeProcurementReviewInvoicingList)){
            BigDecimal reduce = threeProcurementReviewInvoicingList.stream().map(ThreeProcurementReviewInvoicing::getInvoicingPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal add = reduce.add(invoicingPrice);
            if (add.compareTo(payableAmount) > 0){
                throw new CrmException("累计开票金额不能大于应付金额");
            }
        }
        ThreeProcurementReviewInvoicing threeProcurementReviewInvoicing = HyperBeanUtils.copyProperties(threeProcurementReviewInvoicingVo, ThreeProcurementReviewInvoicing::new);
        return new JsonObject<>(threeProcurementReviewInvoicingService.insertThreeProcurementReviewInvoicing(threeProcurementReviewInvoicing));
    }



    /**
     * 删除【采购开票】
     */
	@PostMapping("/remove")
    @Operation(summary = "删除【采购开票】")
    @PreFlowPermission
    public JsonObject<Boolean> remove(@RequestBody String[] ids)
    {
        return new JsonObject<>(threeProcurementReviewInvoicingService.deleteThreeProcurementReviewInvoicingByIds(ids));
    }


    @GetMapping("/queryPayableAmount")
    @Operation(summary = "查询应付金额:第三方采购流程实例id、采购编号")
    @PreFlowPermission
    public JsonObject<BigDecimal> queryPayableAmount(@RequestParam String processInstanceId,@RequestParam String purchaseNumber){
        return new JsonObject<>(threeProcurementReviewPaymentProvisionService.selectPayableAmount(processInstanceId,purchaseNumber,true));
    }
}
