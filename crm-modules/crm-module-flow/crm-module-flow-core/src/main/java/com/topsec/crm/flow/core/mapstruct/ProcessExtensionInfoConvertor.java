package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.core.entity.ProcessExtensionInfo;
import com.topsec.crm.framework.common.bean.ProcessExtensionInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ProcessExtensionInfoConvertor {
    ProcessExtensionInfoConvertor INSTANCE= Mappers.getMapper(ProcessExtensionInfoConvertor.class);

    ProcessExtensionInfoVO toVO(ProcessExtensionInfo processExtensionInfo);
    List<ProcessExtensionInfoVO> toVOList(List<ProcessExtensionInfo> processExtensionInfo);

}
