package com.topsec.crm.flow.core.controller.loan.loanToCost;

import com.topsec.crm.flow.api.dto.loan.loanApply.LoanApplyDTO;
import com.topsec.crm.flow.api.dto.loan.loanApply.LoanApplyQuery;
import com.topsec.crm.flow.core.service.LoanApplyService;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/loanToCost")
@Tag(name = "【借款转费用-业务】", description = "loanToCost")
@RequiredArgsConstructor
@Validated
public class LoanToCostBusinessController extends BaseController {

    @Resource
    private LoanApplyService loanApplyService;

    @GetMapping("/getLoanToCostInfo")
    @Operation(summary = "查询借款转费用流程")
    @PreAuthorize(hasPermission = "crm_loan_to_cost")
    public JsonObject<LoanApplyDTO> getLoanToCostInfo(@RequestParam String processInstanceId){
        return new JsonObject<>(loanApplyService.queryByProcessInstanceId(processInstanceId));
    }

    @PreAuthorize(hasPermission = "crm_loan",dataScope = "crm_loan")
    @PostMapping("/loanToCostPage")
    @Operation(summary = "借款转费用列表")
    public JsonObject<PageUtils<LoanApplyDTO>> loanApplyPage(@RequestBody LoanApplyQuery query) {
        startPage();
        return new JsonObject<>(loanApplyService.loanApplyPage(query));
    }


}
