package com.topsec.crm.flow.core.controller.contractexportrecord;


import cn.afterturn.easypoi.util.WebFilenameUtils;
import com.alibaba.excel.EasyExcel;
import com.topsec.crm.account.api.client.RemoteResourceService;
import com.topsec.crm.contract.api.RemoteExportRecordDocumentFeignService;
import com.topsec.crm.contract.api.entity.exportrecord.ContractExportRecordQuery;
import com.topsec.crm.contract.api.entity.exportrecord.CrmContractDocumentExportRecordVO;
import com.topsec.crm.contract.api.entity.exportrecord.ExportRecordDetailVO;
import com.topsec.crm.contract.api.entity.exportrecord.ExportRecordVO;
import com.topsec.crm.flow.api.dto.contractoriginal.BatchAddOriginalDTO;
import com.topsec.crm.flow.api.dto.contractoriginal.TreeSelect;
import com.topsec.crm.flow.api.dto.contractoriginal.vo.BacthRetrunInfoVO;
import com.topsec.crm.flow.core.entity.handler.TemplateExcelBeanTwo;
import com.topsec.crm.flow.core.entity.handler.TemplateHandler;
import com.topsec.crm.flow.core.service.ContractExportRecordDocumentService;
import com.topsec.crm.framework.common.bean.CrmFsmDoc;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * <p>
 * 合同导出文件表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/contractExportRecord")
@Tag(name = "批量导出附件", description = "/contractExportRecord")
@RequiredArgsConstructor
@Validated
public class ContractExportRecordDocumentController extends BaseController {

    @Resource
    private ContractExportRecordDocumentService contractExportRecordDocumentService;

    @Resource
    private RemoteExportRecordDocumentFeignService remoteExportRecordDocumentFeignService;

    @Resource
    private RemoteResourceService remoteResourceService;

    @PreAuthorize(hasPermission="crm_contract_original_batch_export",dataScope = "crm_contract_execute_company")
    @GetMapping("/template")
    @Operation(summary  = "导入合同号--合同号模版下载")
    public void licTemplate() {
        response.setContentType("application/vnd.ms-excel");
        try {
            response.setHeader("content-disposition", WebFilenameUtils.disposition("合同号模版.xlsx"));
            EasyExcel.write(response.getOutputStream(), TemplateExcelBeanTwo.class)
                    .registerWriteHandler(new TemplateHandler())
                    .sheet("合同号模版")
                    .doWrite(new ArrayList<TemplateExcelBeanTwo>());
        } catch (Exception e) {
            throw new CrmException("导出合同号模版失败");
        }
    }
    @PreAuthorize(hasPermission="crm_contract_original_batch_export",dataScope = "crm_contract_execute_company")
    @PostMapping(value = "/addContractNo")
    @Operation(summary  = "批量导出——添加")
    public JsonObject<BacthRetrunInfoVO> addContractNo(@RequestBody BatchAddOriginalDTO batchAddOriginalDTO) throws Exception {
        return new JsonObject<>(contractExportRecordDocumentService.addContractNo(batchAddOriginalDTO));
    }
    @PreAuthorize(hasPermission="crm_contract_original_batch_export",dataScope = "crm_contract_execute_company")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary  = "批量导出--导入合同号")
    public JsonObject<BacthRetrunInfoVO> upload(@RequestBody MultipartFile file) throws Exception {
        return new JsonObject<>(contractExportRecordDocumentService.upload(file,getCurrentPersonId()));
    }

    @PreAuthorize(hasPermission="crm_contract_original_batch_export",dataScope = "crm_contract_execute_company")
    @PostMapping(value = "/startExporting")
    @Operation(summary  = " 批量导出--开始导出")
    public JsonObject<CrmFsmDoc> startExporting(@RequestBody List<BacthRetrunInfoVO.ExportVO> data) {
       return new JsonObject<>(contractExportRecordDocumentService.startExporting(data));
    }

    @PreAuthorize(hasPermission="crm_contract_original_batch_export",dataScope = "crm_contract_original_batch_export")
    @PostMapping("/page")
    @Operation(summary = "批量导出附件- 批量导出记录(分页)", method = "POST")
    public JsonObject<PageUtils<ExportRecordVO>> pages(@RequestBody ContractExportRecordQuery query) {
        //水平权限校验
        query.setOwnerIds(PreAuthorizeAspect.getPermissionPersonIds());
        TableDataInfo tableDataInfo = remoteExportRecordDocumentFeignService.pages(query).getObjEntity();
        return  new JsonObject<>(new PageUtils<ExportRecordVO>(HyperBeanUtils.copyListPropertiesByJackson(tableDataInfo.getList(),ExportRecordVO.class),(int)tableDataInfo.getTotalCount(),query.getPageNum(),query.getPageSize()));
    }

    @PreAuthorize(hasPermission="crm_contract_original_batch_export",dataScope = "crm_contract_original_batch_export")
    @GetMapping("/queryDetailById")
    @Operation(summary = "查看-- 导出记录详情", method = "POST")
    public JsonObject<ExportRecordDetailVO> queryDetailById(@RequestParam String id) {
        CrmContractDocumentExportRecordVO crmContractDocumentExportRecordVO = Optional.ofNullable(remoteExportRecordDocumentFeignService.queryById(id).getObjEntity()).orElse(null);
        CrmAssert.notNull(crmContractDocumentExportRecordVO, "导出信息不存在");
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), crmContractDocumentExportRecordVO.getOwnerId());
        return remoteExportRecordDocumentFeignService.queryDetailById(id);
    }
    @PreAuthorize(hasPermission="crm_contract_original_batch_export")
    @GetMapping("/querySelect")
    @Operation(summary = " 批量导出-- 文件类型下拉框", method = "POST")
    public JsonObject<List<TreeSelect>> querySelect() {
        return new JsonObject<>(contractExportRecordDocumentService.querySelect());
    }

    @PreAuthorize(hasPermission="crm_contract_original_batch_export",dataScope = "crm_contract_original_batch_export")
    @GetMapping(value = "/deleteById")
    @Operation(summary  = " 批量导出--删除")
    public JsonObject<Boolean> deleteById(@RequestParam String id) {
        CrmContractDocumentExportRecordVO crmContractDocumentExportRecordVO = Optional.ofNullable(remoteExportRecordDocumentFeignService.queryById(id).getObjEntity()).orElse(null);
        CrmAssert.notNull(crmContractDocumentExportRecordVO, "导出信息不存在");
        PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), crmContractDocumentExportRecordVO.getOwnerId());
        return remoteExportRecordDocumentFeignService.deleteById(id);
    }
}

