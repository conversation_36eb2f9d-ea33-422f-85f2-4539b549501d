package com.topsec.crm.flow.core.controllerhidden.salesAgreement;

import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementReviewMainVo;
import com.topsec.crm.flow.core.entity.SalesAgreementReviewMain;
import com.topsec.crm.flow.core.service.SalesAgreementReviewMainService;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/hidden/salesAgreement")
@Tag(name = "销售协议产品-不对外开放", description = "/hidden/salesAgreement")
@RequiredArgsConstructor
@Validated
public class HiddenSalesAgreementController extends BaseController {
    private final SalesAgreementReviewMainService salesAgreementReviewMainService;

    @GetMapping("/querySalesAgreementInfo")
    @Operation(summary = "根据销售协议id查询产品信息")
    public JsonObject<SalesAgreementReviewMainVo> querySalesAgreementInfo(@RequestParam String agreementId) {
        CrmAssert.hasText(agreementId, "销售协议id不能为空");
        SalesAgreementReviewMain SalesAgreement = salesAgreementReviewMainService.getById(agreementId);
        if (SalesAgreement == null){
            throw new CrmException("销售协议不存在");
        }
        SalesAgreementReviewMainVo salesAgreementReviewMainVo = HyperBeanUtils.copyPropertiesByJackson(SalesAgreement, SalesAgreementReviewMainVo.class);
        return new JsonObject<>(salesAgreementReviewMainVo);

    }
}
