package com.topsec.crm.flow.core.controllerhidden.pricereview;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.esotericsoftware.minlog.Log;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewHistoricalDiscountDTO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewMainInfo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementReviewMainVo;
import com.topsec.crm.flow.core.entity.PriceReviewSaleAgreementRel;
import com.topsec.crm.flow.core.entity.ProcessExtensionInfo;
import com.topsec.crm.flow.core.entity.SalesAgreementReviewMain;
import com.topsec.crm.flow.core.mapper.ProcessExtensionInfoMapper;
import com.topsec.crm.flow.core.process.impl.PriceReviewProcessService;
import com.topsec.crm.flow.core.service.PriceReviewHistoricalDiscountService;
import com.topsec.crm.flow.core.service.PriceReviewSaleAgreementRelService;
import com.topsec.crm.flow.core.service.SalesAgreementReviewMainService;
import com.topsec.crm.flow.core.service.impl.PriceReviewMainServiceImpl;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/priceReview")
@Tag(name = "价格评审", description = "/priceReview")
@RequiredArgsConstructor
public class PriceReviewInnerController {

    private final PriceReviewMainServiceImpl priceReviewMainServiceImpl;
    private final PriceReviewProcessService priceReviewProcessService;
    private final ProcessExtensionInfoMapper processExtensionInfoMapper;
    private final PriceReviewHistoricalDiscountService priceReviewHistoricalDiscountService;
    private final PriceReviewSaleAgreementRelService priceReviewSaleAgreementRelService;
    private final SalesAgreementReviewMainService salesAgreementReviewMainService;


    @Operation(summary = "项目是否发起过价审")
    @GetMapping("/hasLaunchedPriceReview")
    public JsonObject<Boolean>  hasLaunchedPriceReview(String projectId){
        return new JsonObject<>(priceReviewMainServiceImpl.hasLaunchedPriceReview(projectId));
    }

    @GetMapping("/HistoricalDiscounts")
    @Operation(summary = "历史折扣-公司项目")
    public JsonObject<List<PriceReviewHistoricalDiscountDTO>> HistoricalDiscounts(@RequestParam List<String> agentCompanyIds){
        return new JsonObject<>(priceReviewHistoricalDiscountService.countHistoricalDiscount(agentCompanyIds));
    }

    @GetMapping("/queryLatestPassedPriceReviewMainByProjectId")
    @Operation(summary = "查询项目最新一条通过的价审信息")
    public JsonObject<PriceReviewMainInfo> queryLatestPassedPriceReviewMainByProjectId(@RequestParam String projectId) {
        return new JsonObject<>(priceReviewMainServiceImpl.queryLatestPassedPriceReviewMainByProjectId(projectId));
    }

    @GetMapping("/queryLatestPriceReviewMainInfoByProjectId")
    @Operation(summary = "查询项目最新一条价审信息")
    JsonObject<PriceReviewMainInfo> queryLatestPriceReviewMainInfoByProjectId(@RequestParam String projectId){
        return new JsonObject<>(priceReviewMainServiceImpl.queryLatestPriceReviewMainInfoByProjectId(projectId));
    }


    @Operation(summary = "查询项目有效价审的销售协议信息")
    @GetMapping("/hiddenQuerySalesAgreement")
    public JsonObject<List<SalesAgreementReviewMainVo>> querySalesAgreement(@RequestParam String projectId){
        boolean isValidated = priceReviewMainServiceImpl.isTheLatestPassedPriceReviewValid(projectId);
        if (!isValidated){
            return new JsonObject<>(Collections.emptyList());
        }
        PriceReviewMainInfo priceReviewMainInfo = priceReviewMainServiceImpl.queryLatestPassedPriceReviewMainByProjectId(projectId);
        String processInstanceId = priceReviewMainInfo.getProcessInstanceId();
        List<String> saleAgreement = priceReviewSaleAgreementRelService
                .list(new LambdaQueryWrapper<PriceReviewSaleAgreementRel>()
                        .eq(PriceReviewSaleAgreementRel::getPriceReviewProcessInstanceId, processInstanceId)).stream()
                .map(PriceReviewSaleAgreementRel::getSaleAgreementId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saleAgreement)) return new JsonObject<>(Collections.emptyList());
        List<SalesAgreementReviewMain> fromSalesAgreement = salesAgreementReviewMainService.findSalesAgreementByIds(saleAgreement);
        return new JsonObject<>(HyperBeanUtils.copyListPropertiesByJackson(fromSalesAgreement, SalesAgreementReviewMainVo.class));
    }

    @Operation(summary = "最新已通过价审是否有效，若从未发过价审算失效")
    @GetMapping("/hiddenIsTheLatestPassedPriceReviewIsValid")
    public JsonObject<Boolean> isTheLatestPassedPriceReviewValid(@RequestParam String projectId){
        return new JsonObject<>(priceReviewMainServiceImpl.isTheLatestPassedPriceReviewValid(projectId));
    }

    @Operation(summary = "老CRM价格评审部分处理")
    @GetMapping("/hiddenProcessOldCrm")
    public JsonObject<Boolean> hiddenProcessOldCrm(){
        priceReviewProcessService.processOldCrm();
        return new JsonObject<>(Boolean.TRUE);
    }
    @Operation(summary = "老CRM同步项目进度")
    @GetMapping("/hiddenUpdateProjectProcess")
    public JsonObject<Boolean> hiddenUpdateProjectProcess(){
        List<String> projectIds = processExtensionInfoMapper
                .selectList(new QueryWrapper<ProcessExtensionInfo>()
                .eq("process_state", 2)
                .likeRight("process_instance_id", "history")
                        .select("distinct project_id")
        ).stream().filter(processExtensionInfo -> {
                    return processExtensionInfo!=null&&StringUtils.isNotBlank(processExtensionInfo.getProjectId());
                }).map(ProcessExtensionInfo::getProjectId).toList();
        projectIds.stream().parallel().forEach(projectId -> {
            try {
                priceReviewProcessService.updateProjectProgress(projectId);
            }catch (Exception e){
                Log.error("更新项目进度失败", e);
            }
        });
        return new JsonObject<>(Boolean.TRUE);
    }

}
