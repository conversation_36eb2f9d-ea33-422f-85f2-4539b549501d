package com.topsec.crm.flow.core.controller.salesAgreementDocment;


import com.topsec.crm.flow.api.dto.saleagreementdocument.SalesAgreementBaseInfoLaunchDTO;
import com.topsec.crm.flow.api.dto.saleagreementdocument.SalesAgreementDocumentLaunchDTO;
import com.topsec.crm.flow.api.vo.ProcessAttachmentVo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementMembersLogVo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementMembersVo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementOwnProductVo;
import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementReviewMainVo;
import com.topsec.crm.flow.api.vo.salesAgreementDocment.*;
import com.topsec.crm.flow.core.entity.SalesAgreementDocument;
import com.topsec.crm.flow.core.entity.SalesAgreementMembers;
import com.topsec.crm.flow.core.entity.SalesAgreementMembersLog;
import com.topsec.crm.flow.core.entity.SalesAgreementReviewMain;
import com.topsec.crm.flow.core.process.impl.SalesAgreementDocumentProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <p>
 * 销售协议原件表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@RestController
@RequestMapping("/business/salesAgreementDocument")
@Tag(name = "协议原件-业务接口", description = "/business/salesAgreementDocument")
@RequiredArgsConstructor
@Validated
public class SalesAgreementDocumentBusinessController extends BaseController {
    @Resource
    private SalesAgreementDocumentService salesAgreementDocumentService;

    @Resource
    private SalesAgreementReviewMainService salesAgreementReviewMainService;

    @Resource
    private SalesAgreementMembersService salesAgreementMembersService;

    @Resource
    private SalesAgreementProductService salesAgreementProductService;
    @Resource
    private ThreeProcurementPaymentReviewMainService threeProcurementPaymentReviewMainService;
    @Resource
    private SalesAgreementDocumentAttachmentService salesAgreementDocumentAttachmentService;
    @Resource
    private SalesAgreementMembersLogService salesAgreementMembersLogService;

    @Resource
    private SalesAgreementDocumentProcessService processService;

    @PreAuthorize(hasPermission = "crm_contract_agreements_original", dataScope = "crm_contract_agreements_original")
    @PostMapping("/page")
    @Operation(summary = "协议原件列表（分页）", method = "POST")
    public JsonObject<PageUtils<SalesAgreemenDocVO>> pages(@RequestBody SalesAgreementDocQuery query) {
        List<SalesAgreemenDocVO> list = salesAgreementDocumentService.pages(query,getCurrentPersonId(),true);
        PageUtils dataTable = listToPage(list);
        return new JsonObject<>(dataTable);
    }

    @PreAuthorize(hasPermission = "crm_contract_agreements_export", dataScope = "crm_contract_agreements_export")
    @PostMapping("/export")
    @Operation(summary = "协议原件列表（分页）-导出", method = "POST")
    public void export(@RequestBody SalesAgreementDocQuery query) throws Exception {
        List<SalesAgreemenDocVO> list = salesAgreementDocumentService.pages(query,getCurrentPersonId(),false);
        ExcelUtil<SalesAgreemenDocVO> excelUtil = new ExcelUtil<>(SalesAgreemenDocVO.class);
        excelUtil.exportExcel(response, list, "协议原件");
    }

    @PreAuthorize(hasPermission = "crm_contract_agreements_original", dataScope = "crm_contract_agreements_original")
    @GetMapping("/selectAgreementInfoById")
    @Operation(summary = "协议原件详情-基本信息", method = "GET")
    public JsonObject<SalesAgreementReviewMainVo> selectContractInfoById(@RequestParam String agreementProcessInstanceId) {
        CrmAssert.hasText(agreementProcessInstanceId, "销售协议的流程实例化id不能为空");
        horizontalPermissionVerification(null,agreementProcessInstanceId);
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(agreementProcessInstanceId);
        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(salesAgreement, SalesAgreementReviewMainVo.class));
    }

    @PreAuthorize(hasPermission = "crm_contract_agreements_original", dataScope = "crm_contract_agreements_original")
    @GetMapping("/querySalesAgreementMembersList")
    @Operation(summary = "协议原件详情-协议成员")
    public JsonObject<List<SalesAgreementMembersVo>> querySalesAgreementMembersList(@RequestParam String agreementProcessInstanceId) {
        CrmAssert.hasText(agreementProcessInstanceId, "销售协议的流程实例化id不能为空");
        horizontalPermissionVerification(null,agreementProcessInstanceId);
        List<SalesAgreementMembersVo> salesAgreementMembersVoList = salesAgreementMembersService.convertSalesAgreementMembersList(agreementProcessInstanceId);
        return new JsonObject<>(salesAgreementMembersVoList);
    }

    @PreAuthorize(hasPermission = "crm_contract_agreements_original", dataScope = "crm_contract_agreements_original")
    @GetMapping("/querySalesAgreementPriceProductList")
    @Operation(summary = "协议原件详情-查询协议价格审批产品信息")
    public JsonObject<SalesAgreementOwnProductVo> querySalesAgreementPriceProductList(@RequestParam String agreementProcessInstanceId) throws ExecutionException {
        CrmAssert.hasText(agreementProcessInstanceId, "销售协议的流程实例化id不能为空");
        horizontalPermissionVerification(null,agreementProcessInstanceId);
        SalesAgreementOwnProductVo salesAgreementOwnProductVo = salesAgreementProductService.convertToSalesAgreementOwnProductVo(agreementProcessInstanceId, false);
        return new JsonObject<>(salesAgreementOwnProductVo);
    }

    @PreAuthorize(hasPermission = "crm_contract_agreements_original", dataScope = "crm_contract_agreements_original")
    @GetMapping("/querySalesAgreementProductList")
    @Operation(summary = "协议原件详情-自有产品")
    public JsonObject<SalesAgreementOwnProductVo> querySalesAgreementProductList(@RequestParam String agreementProcessInstanceId) throws ExecutionException {
        CrmAssert.hasText(agreementProcessInstanceId, "销售协议的流程实例化id不能为空");
        horizontalPermissionVerification(null,agreementProcessInstanceId);
        SalesAgreementOwnProductVo salesAgreementOwnProductVo = salesAgreementProductService.convertToSalesAgreementOwnProductVo(agreementProcessInstanceId, true);
        return new JsonObject<>(salesAgreementOwnProductVo);
    }

    //12
    @PreAuthorize(hasPermission = "crm_contract_agreements_original", dataScope = "crm_contract_agreements_original")
    @GetMapping("/selectSalesAgreementAttachmentList")
    @Operation(summary = "协议原件详情-协议附件列表")
    public JsonObject<List<ProcessAttachmentVo>> selectSalesAgreementAttachmentList(@RequestParam String agreementProcessInstanceId) {
        CrmAssert.hasText(agreementProcessInstanceId, "销售协议的流程实例化id不能为空");
        horizontalPermissionVerification(null,agreementProcessInstanceId);
        SalesAgreementReviewMain salesAgreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(agreementProcessInstanceId);
        List<ProcessAttachmentVo> listThreeProcurementAttachment = threeProcurementPaymentReviewMainService.getListThreeProcurementAttachment(salesAgreement.getAttachmentIds());
        return new JsonObject<>(listThreeProcurementAttachment);
    }

    @PreAuthorize(hasPermission = "crm_contract_agreements_original", dataScope = "crm_contract_agreements_original")
    @GetMapping("/querySalesAgreementDocProductList")
    @Operation(summary = "协议原件详情-协议原件上交审批")
    public JsonObject<List<SalesAgreementDocumentVO>> querySalesAgreementDocProductList(@RequestParam String agreementProcessInstanceId) {
        CrmAssert.hasText(agreementProcessInstanceId, "销售协议的流程实例化id不能为空");
        horizontalPermissionVerification(null,agreementProcessInstanceId);
        return new JsonObject<>(salesAgreementDocumentService.querySalesAgreementDocProductList(agreementProcessInstanceId));
    }

    //12
    @PreAuthorize(hasPermission = "crm_contract_agreements_original", dataScope = "crm_contract_agreements_original")
    @GetMapping("/queryAttachments")
    @Operation(summary = "协议原件详情-协议原件")
    public JsonObject<List<SalesAgreementDocumentAttachmentVO>> queryAttachments(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程id不能为空");
        horizontalPermissionVerification(processInstanceId,null);
        return new JsonObject<>(salesAgreementDocumentAttachmentService.queryAttachmentList(processInstanceId));
    }

    @PreAuthorize(hasPermission = "crm_contract_agreements_apply")
    @PostMapping("/launch")
    @Operation(summary = "协议原件详情页——协议原件上交")
    public JsonObject<Boolean> launch(@RequestBody SalesAgreementBaseInfoLaunchDTO salesAgreementBaseInfoLaunchDTO) {
        SalesAgreementDocumentLaunchDTO salesAgreementDocumentLaunchDTO = new SalesAgreementDocumentLaunchDTO();
        salesAgreementBaseInfoLaunchDTO.setApplyUserName(getCurrentPersonName());
        salesAgreementDocumentLaunchDTO.setBaseInfo(salesAgreementBaseInfoLaunchDTO);
        salesAgreementDocumentService.checkPermission(salesAgreementBaseInfoLaunchDTO.getAgreementProcessInstanceId(),getCurrentPersonId(),getCurrentRoles());
        return new JsonObject<>(processService.launch(salesAgreementDocumentLaunchDTO));
    }

    @PreAuthorize(hasPermission = "crm_contract_agreements_original", dataScope = "crm_contract_agreements_original")
    @GetMapping("/querySalesAgreementMembersLogList")
    @Operation(summary = "协议原件详情——协议成员——变更次数")
    public JsonObject<List<SalesAgreementMembersLogVo>> querySalesAgreementMembersLogList(@RequestParam String agreementProcessInstanceId, @RequestParam String recordId) {
        CrmAssert.hasText("流程实例id不能为null", agreementProcessInstanceId);
        CrmAssert.hasText("协议成员记录id不能为null", recordId);
        horizontalPermissionVerification(null,agreementProcessInstanceId);
        List<SalesAgreementMembersLog> salesAgreementMembersLogList = salesAgreementMembersLogService.findSalesAgreementMembersLogList(agreementProcessInstanceId, recordId);
        List<SalesAgreementMembersLogVo> salesAgreementMembersLogVos = HyperBeanUtils.copyListPropertiesByJackson(salesAgreementMembersLogList, SalesAgreementMembersLogVo.class);
        NameUtils.setName(salesAgreementMembersLogVos);
        return new JsonObject<>(salesAgreementMembersLogVos);
    }

    @PreAuthorize(hasPermission = "crm_contract_agreements_original", dataScope = "crm_contract_agreements_original")
    @GetMapping("/selectAgreementDocInfoById")
    @Operation(summary = "协议原件上交审批详情页-基本信息（包含协议基本信息）")
    public JsonObject<SalesAgreementBaseInfoVO> selectAgreementDocInfoById(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId, "流程id不能为空");
        horizontalPermissionVerification(processInstanceId,null);
        return new JsonObject<>(salesAgreementDocumentService.selectAgreementDocInfoById(processInstanceId));
    }

    private void horizontalPermissionVerification(String processInstanceId, String agreementProcessInstanceId) {
        Set<String> personList = PreAuthorizeAspect.getPermissionPersonIds();
        if (CollectionUtils.isEmpty(personList)) {
            return;
        }
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        //查询所有用户的部门
        Set<String> depts =dataScopeParam != null ? Optional.ofNullable(dataScopeParam.getDeptIdList()).orElse(new HashSet<>()).stream().filter(s ->{
            return !"-1".equals(s);
        }).collect(Collectors.toSet()) : Collections.EMPTY_SET;
        //判断根据用户和等于用户，来排查这批数据中存在
        Set<String> processInstanceIds = new HashSet<>();
        List<SalesAgreementMembers> list2 = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(personList)){
            list2.addAll(salesAgreementMembersService.findSalesAgreementMembersListByPersonIds(personList));
        }
        if(CollectionUtils.isNotEmpty(depts)){
            list2.addAll(salesAgreementMembersService.findSalesAgreementMembersListByDeptIds(depts));
        }
        processInstanceIds = list2.stream().map(SalesAgreementMembers::getProcessInstanceId).collect(Collectors.toSet());
        SalesAgreementReviewMain salesAgreement = null;
        if (StringUtils.isNotEmpty(processInstanceId)) {
            SalesAgreementDocument salesAgreementDocument = salesAgreementDocumentService.getByProcessInstanceId(processInstanceId);
            agreementProcessInstanceId = salesAgreementDocument!=null?salesAgreementDocument.getAgreementProcessInstanceId():null;
        }

        if (StringUtils.isNotEmpty(agreementProcessInstanceId)) {
            salesAgreement = salesAgreementReviewMainService.findSalesAgreementByProcessInstanceId(agreementProcessInstanceId);
            if(salesAgreement != null&&!processInstanceIds.contains(salesAgreement.getProcessInstanceId())) {
                 PreAuthorizeAspect.checkPersonIdDataScope(PreAuthorizeAspect.getDataScopeParam(), salesAgreement.getAgreementManagerPersonId());
            }else if (salesAgreement==null){
                throw new CrmException("协议数据不存在");
            }
        }else {
            throw new CrmException("协议数据不存在");
        }
    }
}

