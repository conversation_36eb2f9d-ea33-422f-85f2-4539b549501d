package com.topsec.crm.flow.core.controller.projectReport.forward;

import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.PageDomain;
import com.topsec.crm.framework.common.web.page.TableSupport;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.RemoteProjectNameChangeRecordService;
import com.topsec.crm.project.api.entity.CrmProjectNameChangeRecordVo;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.vo.TfsFormContentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 项目名称变更记录Controller
 *
 * @date 2024-06-11
 */
@RestController
@RequestMapping("/forward/record")
@Tag(name = "【项目名称变更记录】", description = "crmProjectNameChangeRecord")
public class CrmProjectNameChangeRecordController extends BaseController
{
    @Autowired
    private RemoteProjectNameChangeRecordService remoteProjectNameChangeRecordService;
    @Autowired
    private TfsFormContentClient tfsformContentClient;

    /**
     * 分页查询【项目名称变更记录】
     */
    @PostMapping("/list")
    public JsonObject<List<CrmProjectNameChangeRecordVo>> page(@RequestBody CrmProjectNameChangeRecordVo crmProjectNameChangeRecordVo)
    {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(crmProjectNameChangeRecordVo.getProjectId().equals(tfsFormContentVo.getProjectId())){
                return remoteProjectNameChangeRecordService.list(crmProjectNameChangeRecordVo);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

}
