package com.topsec.crm.flow.core.controllerhidden.projectclues;

import com.topsec.crm.flow.core.service.IProjectCluesService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.bean.StatsPersonTimeSearchVO;
import com.topsec.crm.framework.common.bean.StatsResultVO;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 项目线索信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@RestController
@RequestMapping("/hidden/projectClues")
@Tag(name = "项目线索商机业务接口", description = "/business/projectClues")
public class HiddenProjectCluesBusinessController extends BaseController {

    @Autowired
    private IProjectCluesService projectCluesService;

    // JD管理员工号
    private static final String JD_MANGER_PERSON_ID = "df267417189546beaf5e63e1ca7166fd";

    // 非JD管理员工号
    private static final String UN_JD_MANGER_PERSON_ID = "572eae57356a49e6be5ce497349cac47";

    @PostMapping("/statisticProjectClues")
    public JsonObject<List<StatsResultVO>> statisticProjectClues(@RequestBody @Validated StatsPersonTimeSearchVO statsTimeSearchVO) {
        String currentPersonId = statsTimeSearchVO.getCurrentPersonId();
        boolean jdManager = false;
        boolean unJdManager = false;
        if (JD_MANGER_PERSON_ID.equals(currentPersonId)) {
            jdManager = true;
        } else if (UN_JD_MANGER_PERSON_ID.equals(currentPersonId)) {
            unJdManager = true;
        }
        List<StatsResultVO> list = projectCluesService.statisticProjectClues(statsTimeSearchVO, statsTimeSearchVO.getPersonIds(), jdManager, unJdManager);
        return new JsonObject<>(list);
    }

    @PostMapping("/countProjectClues")
    public JsonObject<Integer> countProjectClues(@RequestBody @Validated StatsPersonTimeSearchVO statsTimeSearchVO) {
        String currentPersonId = statsTimeSearchVO.getCurrentPersonId();
        boolean jdManager = false;
        boolean unJdManager = false;
        if (JD_MANGER_PERSON_ID.equals(currentPersonId)) {
            jdManager = true;
        } else if (UN_JD_MANGER_PERSON_ID.equals(currentPersonId)) {
            unJdManager = true;
        }
        Integer count = projectCluesService.countProjectClues(statsTimeSearchVO, statsTimeSearchVO.getPersonIds(), jdManager, unJdManager);
        return new JsonObject<>(count);
    }

}
