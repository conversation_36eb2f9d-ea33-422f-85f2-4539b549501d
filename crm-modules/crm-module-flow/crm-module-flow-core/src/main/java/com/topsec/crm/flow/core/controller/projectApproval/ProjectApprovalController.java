package com.topsec.crm.flow.core.controller.projectApproval;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.api.dto.projectApproval.ProjectApprovalFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.projectApproval.ProjectApprovalInfoVO;
import com.topsec.crm.flow.api.dto.projectApproval.ProjectApprovalProcessVO;
import com.topsec.crm.flow.api.dto.projectApproval.ProjectApprovalVO;
import com.topsec.crm.flow.api.dto.teamBuilding.TeamBuildingDetailVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ProjectApproval;
import com.topsec.crm.flow.core.mapstruct.TeamBuildingMainConvert;
import com.topsec.crm.flow.core.process.impl.ProjectApprovalProcessService;
import com.topsec.crm.flow.core.service.ProjectApprovalService;
import com.topsec.crm.flow.core.service.TeamBuildingService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-07-18  15:17
 * @Description: 立项审批
 */
@RestController
@RequestMapping("/projectApproval")
@Tag(name = "立项审批", description = "/projectApproval")
@RequiredArgsConstructor
@Validated
public class ProjectApprovalController extends BaseController {

    private final ProjectApprovalService projectApprovalService;

    private final TeamBuildingService teamBuildingService;

    private final ProjectApprovalProcessService projectApprovalProcessService;



    @PostMapping("/launch")
    @PreAuthorize(hasPermission = "crm_flow_project_approval",dataScope = "crm_flow_project_approval")
    @Operation(summary = "发起立项审批流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody ProjectApprovalFlowLaunchDTO launchDTO) {
        return new JsonObject<>(projectApprovalProcessService.launch(launchDTO));
    }

    @PostMapping("/launchAndReturnProcessInstanceId")
    @PreAuthorize(hasPermission = "crm_flow_project_approval",dataScope = "crm_flow_project_approval")
    @Operation(summary = "发起立项审批流程并返回流程id")
    public JsonObject<String> launchAndReturnProcessInstanceId(@Valid @RequestBody ProjectApprovalFlowLaunchDTO launchDTO) {
        return new JsonObject<>(projectApprovalProcessService.launchAndReturnProcessInstanceId(launchDTO));
    }





    @GetMapping ("/getProjectApprovalProcessInfo")
    @Operation(summary = "获取流程信息")
    @PreFlowPermission
    public JsonObject<ProjectApprovalProcessVO> getProjectApprovalProcessInfo(@RequestParam String processInstanceId) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(projectApprovalService.getProjectApprovalProcessInfo(processInstanceId));
    }


    @GetMapping("/teamBuildingBaseInfo")
    @PreFlowPermission
    @Operation(summary = "团队组建资料")
    public JsonObject<TeamBuildingDetailVO> teamBuildingBaseInfo(@RequestParam String teamBuildingId){
        CrmAssert.notNull("团队组建id不能为null",teamBuildingId);
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String teamBuildingProcessInstanceId = teamBuildingService.getById(teamBuildingId).getProcessInstanceId();
        ProjectApproval one = Optional.ofNullable(projectApprovalService.getOne(new LambdaQueryWrapper<ProjectApproval>().eq(ProjectApproval::getTeamBuildingProcessInstanceId, teamBuildingProcessInstanceId))).orElseThrow(()->new CrmException(ResultEnum.NULL_OBJ_ENTITY));
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, one.getProcessInstanceId());
        return new JsonObject<>(teamBuildingService.teamBuildingBaseInfo(teamBuildingId));
    }

    @GetMapping("/applicantStepOfFourth")
    @PreFlowPermission
    @Operation(summary = "02主责部门")
    public JsonObject<List<FlowPerson>> applicantStepOfFourth(@RequestParam String teamBuildingId){
        CrmAssert.notNull("团队组建id不能为null",teamBuildingId);
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String teamBuildingProcessInstanceId = teamBuildingService.getById(teamBuildingId).getProcessInstanceId();
        ProjectApproval one = Optional.ofNullable(projectApprovalService.getOne(new LambdaQueryWrapper<ProjectApproval>().eq(ProjectApproval::getTeamBuildingProcessInstanceId, teamBuildingProcessInstanceId))).orElseThrow(()->new CrmException(ResultEnum.NULL_OBJ_ENTITY));
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, one.getProcessInstanceId());
        return new JsonObject<>(TeamBuildingMainConvert.INSTANCE.toFlowPersonList(teamBuildingService.applicantStepOfFourth(teamBuildingId)));
    }


    @GetMapping("/applicantStepOfEighth")
    @PreFlowPermission
    @Operation(summary = "08QA")
    public JsonObject<FlowPerson> applicantStepOfEighth(@RequestParam String processInstanceId){
        CrmAssert.notNull("流程id不能为null",processInstanceId);
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ProjectApproval one = Optional.ofNullable(projectApprovalService.getOne(new LambdaQueryWrapper<ProjectApproval>().eq(ProjectApproval::getProcessInstanceId, headerValue))).orElseThrow(()->new CrmException(ResultEnum.NULL_OBJ_ENTITY));
        return new JsonObject<>(TeamBuildingMainConvert.INSTANCE.toFlowPerson(teamBuildingService.applicantStepOfEighth(one.getTeamBuildingProcessInstanceId())));
    }



    @GetMapping("/deleteById")
    @Operation(summary = "根据id删除")
    @PreFlowPermission
    public JsonObject<Boolean> deleteById(@RequestParam String id) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = projectApprovalService.getById(id).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(projectApprovalService.deleteById(id)) ;
    }

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "新增或修改")
    @PreFlowPermission
    public JsonObject<Boolean> saveOrUpdate(@RequestBody ProjectApprovalVO projectApprovalVO) {
        if (StringUtils.isNotBlank(projectApprovalVO.getId())){
            String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
            String processInstanceId = projectApprovalService.getById(projectApprovalVO.getId()).getProcessInstanceId();
            PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        }

        return new JsonObject<>(projectApprovalService.saveOrUpdate(projectApprovalVO)) ;
    }

    @GetMapping("/selectByProjectApprovalId")
    @Operation(summary = "根据立项审批id查询")
    @PreFlowPermission
    public JsonObject<List<ProjectApprovalVO>> selectByProjectApprovalId(@RequestParam String projectApprovalId) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = projectApprovalService.getById(projectApprovalId).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        CrmAssert.notNull(projectApprovalId,"立项审批id不能为空");
        return new JsonObject<>(projectApprovalService.selectByProjectApprovalId(projectApprovalId)) ;
    }

    @PostMapping("/saveProjectInfo")
    @Operation(summary = "03QA修改项目信息保存")
    @PreFlowPermission(hasAnyNodes = {"projectApproval_03"})
    public JsonObject<Boolean> saveProjectInfo(@RequestBody ProjectApprovalInfoVO projectApprovalInfoVO) {
        if (StringUtils.isNotBlank(projectApprovalInfoVO.getId())){
            String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
            String processInstanceId = projectApprovalService.getById(projectApprovalInfoVO.getId()).getProcessInstanceId();
            PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        }
        return new JsonObject<>(projectApprovalService.saveProjectInfo(projectApprovalInfoVO)) ;
    }

    @PostMapping("/updateProjectDescription")
    @Operation(summary = "05QA修改项目描述")
    @PreFlowPermission(hasAnyNodes = {"sid-32D06211-4F10-420D-9E74-C602F4BE7FD4"})
    public JsonObject<Boolean> updateProjectDescription(@RequestBody ProjectApprovalVO projectApprovalVO) {
        CrmAssert.notNull(projectApprovalVO.getId(),"立项审批id不能为空");
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = projectApprovalService.getById(projectApprovalVO.getId()).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(projectApprovalService.updateProjectDescription(projectApprovalVO)) ;
    }


}
