package com.topsec.crm.flow.core.controllerhidden.contractBadDebt;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractBadDebt.ContractBadDebtDetailVo;
import com.topsec.crm.flow.api.enums.AgentapprovalStatusEnum;
import com.topsec.crm.flow.core.entity.ContractBadDebtDetail;
import com.topsec.crm.flow.core.entity.ContractBadDebtExecuteSnapshot;
import com.topsec.crm.flow.core.entity.ContractBadDebtMain;
import com.topsec.crm.flow.core.service.IContractBadDebtDetailService;
import com.topsec.crm.flow.core.service.IContractBadDebtExecuteSnapshotService;
import com.topsec.crm.flow.core.service.IContractBadDebtMainService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/hidden/contractBadDebtMain")
@RequiredArgsConstructor
@Validated
public class HiddenContractBadDebtMainController extends BaseController {
    @Autowired
    private IContractBadDebtDetailService contractBadDebtDetailService;
    @Autowired
    private IContractBadDebtMainService contractBadDebtMainService;
    @Autowired
    private RemoteContractExecuteService remoteContractExecuteService;
    @Autowired
    private IContractBadDebtExecuteSnapshotService contractBadDebtInfoSnapshotService;

    //查询所有的合同坏账信息
    @GetMapping("/findAllBadDebt")
    public JsonObject<List<ContractBadDebtDetailVo>> findAllBadDebt() {
        List<ContractBadDebtDetail> list = contractBadDebtDetailService.list();
        return new JsonObject<>(HyperBeanUtils.copyListPropertiesByJackson(list, ContractBadDebtDetailVo.class));
    }

    //合同是否存在坏账-包括流程中和审批通过的
    @PostMapping("/contractHasBadDebt")
    public JsonObject<Map<String,ContractBadDebtDetailVo>> contractHasBadDebt(@RequestBody List<String> contractNumbers) {
        Assert.isFalse(CollectionUtil.isEmpty(contractNumbers), "合同编号不为空。");

        Map<String,ContractBadDebtDetailVo> reuslt = new HashMap<String,ContractBadDebtDetailVo>();
        List<ContractBadDebtDetail> lists = contractBadDebtDetailService.query().in("contract_number", contractNumbers).list();
        Map<String, List<ContractBadDebtDetail>> collect = lists.stream().collect(Collectors.groupingBy(ContractBadDebtDetail::getContractNumber));

        List<String> processInstanceIds = lists.stream().map(ContractBadDebtDetail::getProcessInstanceId).toList();

        List<ContractBadDebtMain> processInstanceId = CollectionUtils.isEmpty(processInstanceIds) ? Collections.emptyList() : contractBadDebtMainService.query().in("process_instance_id", processInstanceIds).list();
        Map<String, ContractBadDebtMain> processInstanceIdMap = processInstanceId.stream().collect(Collectors.toMap(ContractBadDebtMain::getProcessInstanceId, v -> v));
        for (String contractNumber : contractNumbers) {
//            List<ContractBadDebtDetail> list = contractBadDebtDetailService.query().eq("contract_number", contractNumber).list();
            List<ContractBadDebtDetail> list = collect.get(contractNumber);
            if(CollectionUtil.isNotEmpty(list) && list.size() == 1){
                //同一个合同只会存在一个合同坏账
                ContractBadDebtDetail contractBadDebtDetail = list.get(0);
                //校验流程是否已经审批通过
//                ContractBadDebtMain cbdm = contractBadDebtMainService.query().eq("process_instance_id", contractBadDebtDetail.getProcessInstanceId()).one();
                ContractBadDebtMain cbdm = processInstanceIdMap.get(contractBadDebtDetail.getProcessInstanceId());
                if(cbdm.getProcessState() == AgentapprovalStatusEnum.YSP.getCode()){
                    reuslt.put(contractNumber,HyperBeanUtils.copyPropertiesByJackson(contractBadDebtDetail, ContractBadDebtDetailVo.class));
                }
            }
        }
        return new JsonObject<>(reuslt);
    }

    //数据同步-清洗快照数据
    @GetMapping("/cleanContractBadMainSnapshot")
    public JsonObject<Boolean> cleanContractBadMainSnapshot() {
        List<ContractBadDebtMain> mains = contractBadDebtMainService.list();

        for (ContractBadDebtMain main : mains) {
            //查询当前流程所有的合同执行信息
            List<ContractBadDebtDetail> list = contractBadDebtDetailService.query().eq("process_instance_id", main.getProcessInstanceId()).list();
            JsonObject<List<CrmContractExecuteVO>> byContractNumberBatch = remoteContractExecuteService.getByContractNumberBatchNotEffective(list.stream().map(ContractBadDebtDetail::getContractNumber).collect(Collectors.toSet()));

            //1.保存审批通过后合同当前快照信息
            List<CrmContractExecuteVO> executeVos = byContractNumberBatch.getObjEntity();
            List<ContractBadDebtExecuteSnapshot> snapshots = new ArrayList<ContractBadDebtExecuteSnapshot>();
            for (CrmContractExecuteVO crmContractExecuteVO : executeVos) {
                ContractBadDebtExecuteSnapshot snapshot = HyperBeanUtils.copyPropertiesByJackson(crmContractExecuteVO,ContractBadDebtExecuteSnapshot.class);
                snapshot.setProcessInstanceId(main.getProcessInstanceId());
                snapshots.add(snapshot);
            }
            contractBadDebtInfoSnapshotService.saveBatch(snapshots);
        }

        return new JsonObject<>(true);
    }

}
