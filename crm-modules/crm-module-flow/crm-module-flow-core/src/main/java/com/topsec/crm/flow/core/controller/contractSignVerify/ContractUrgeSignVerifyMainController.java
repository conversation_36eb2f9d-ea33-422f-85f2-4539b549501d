package com.topsec.crm.flow.core.controller.contractSignVerify;

import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUnconfirmedUrgeSnapshotVo;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUrgeSignVerifyMainVo;
import com.topsec.crm.flow.api.dto.contractSignVerify.ContractUrgeSignVerifySonMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ContractUnconfirmedUrgeSnapshot;
import com.topsec.crm.flow.core.entity.ContractUrgeSignVerifyMain;
import com.topsec.crm.flow.core.entity.ContractUrgeSignVerifySonMain;
import com.topsec.crm.flow.core.process.ProcessTypeEnum;
import com.topsec.crm.flow.core.service.IContractUnconfirmedUrgeService;
import com.topsec.crm.flow.core.service.IContractUnconfirmedUrgeSnapshotService;
import com.topsec.crm.flow.core.service.IContractUrgeSignVerifyMainService;
import com.topsec.crm.flow.core.service.IContractUrgeSignVerifySonMainService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contractUrgeSignVerifyMain")
@Tag(name = "催交签验收单相关Controller", description = "/contractUrgeSignVerifyMain/flow")
@RequiredArgsConstructor
@Validated
public class ContractUrgeSignVerifyMainController extends BaseController {

    @Autowired
    private IContractUrgeSignVerifyMainService contractUrgeSignVerifyMainService;
    @Autowired
    private IContractUrgeSignVerifySonMainService contractUrgeSignVerifySonMainService;
    @Autowired
    private IContractUnconfirmedUrgeSnapshotService contractUnconfirmedUrgeSnapshotService;
    @Autowired
    private TosDepartmentClient tosDepartmentClient;

    @GetMapping("/info")
    @Operation(summary = "签验收单详情信息")
    @PreFlowPermission
    public JsonObject<ContractUrgeSignVerifyMainVo> info(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        ContractUrgeSignVerifyMain verifyMain = contractUrgeSignVerifyMainService.query().eq("process_instance_id", processInstanceId).one();
        ContractUrgeSignVerifyMainVo csvmv = HyperBeanUtils.copyPropertiesByJackson(verifyMain, ContractUrgeSignVerifyMainVo.class);
        
        //1.查询子催交验收单列表
        List<ContractUrgeSignVerifySonMain> sonMains = contractUrgeSignVerifySonMainService.query().eq("parent_process_instance_id", processInstanceId).list();
        List<ContractUrgeSignVerifySonMainVo> sonMainVos = HyperBeanUtils.copyListPropertiesByJackson(sonMains, ContractUrgeSignVerifySonMainVo.class);
        for (ContractUrgeSignVerifySonMainVo sonMainVo : sonMainVos) {
            sonMainVo.setApplyUserName("topsec");
            sonMainVo.setIsConfirm(sonMainVo.getProcessState().equals(ApprovalStatusEnum.YSP.getCode().toString()) ? true : false);

            //如果子审批通过，查询催交明细
            if(sonMainVo.getProcessState().equals(ApprovalStatusEnum.YSP.getCode().toString())){
                List<ContractUnconfirmedUrgeSnapshot> list = contractUnconfirmedUrgeSnapshotService.query()
                        .in("process_instance_id", sonMainVo.getProcessInstanceId())
                        .list();
                sonMainVo.setUrgeSnapshotVos(HyperBeanUtils.copyListPropertiesByJackson(list, ContractUnconfirmedUrgeSnapshotVo.class));
            }
        }
        csvmv.setSonMainVos(sonMainVos);

        //2.查询部门负责人信息
        JsonObject<List<EmployeeVO>> byDeptLeader = tosDepartmentClient.findByDeptLeader(csvmv.getDeptId());
        if(byDeptLeader.isSuccess()){
            List<EmployeeVO> objEntity = byDeptLeader.getObjEntity();
            csvmv.setDeptLeaders(objEntity);
        }

        return new JsonObject<>(csvmv);
    }
    
}
