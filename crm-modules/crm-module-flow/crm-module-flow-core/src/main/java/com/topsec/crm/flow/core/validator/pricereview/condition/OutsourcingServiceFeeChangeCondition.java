package com.topsec.crm.flow.core.validator.pricereview.condition;

import com.topsec.crm.flow.core.entity.PriceReviewProductServiceOutsourcing;
import com.topsec.crm.flow.core.validator.CheckCondition;

import com.topsec.crm.flow.core.validator.pricereview.PriceReviewCheckContext;
import org.apache.commons.collections4.ListUtils;

import java.math.BigDecimal;

/**
 * 外包服务费增加，价审失效
 *
 * <AUTHOR>
 */
public class OutsourcingServiceFeeChangeCondition implements CheckCondition<PriceReviewCheckContext> {
    @Override
    public boolean check(PriceReviewCheckContext context)  {
        BigDecimal currentFee = ListUtils.emptyIfNull(context.getProjectDetail().getProductServiceOutsourcingList()).stream()
                .map(PriceReviewProductServiceOutsourcing::getPurchaseTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal snapshotFee = ListUtils.emptyIfNull(context.getProjectSnapshot().getProductServiceOutsourcingList()).stream()
                .map(PriceReviewProductServiceOutsourcing::getPurchaseTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (currentFee.compareTo(snapshotFee) > 0) {
            return false;
        }

        return true;
    }

    @Override
    public String defaultFailureReason() {
        return "外包服务费增加";
    }
}
