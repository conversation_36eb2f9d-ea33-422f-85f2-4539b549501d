package com.topsec.crm.flow.core.controller.proofOfDebtCollection;

import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.dto.contractreceivable.ContractReceivableDetailDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.api.dto.proofOfDebtCollection.ProofOfDebtCollectionLaunchable;
import com.topsec.crm.flow.api.dto.proofOfDebtCollection.ProofOfDebtCollectionQuery;
import com.topsec.crm.flow.api.dto.proofOfDebtCollection.ProofOfDebtCollectionVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.process.impl.ProofOfDebtCollectionProcessService;
import com.topsec.crm.flow.core.service.ContractReceivableService;
import com.topsec.crm.flow.core.service.ProofOfDebtCollectionService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/proofOfDebtCollection")
@Tag(name = "催款证据", description = "/proofOfDebtCollection")
@RequiredArgsConstructor
public class ProofOfDebtCollectionController {

    private final ProofOfDebtCollectionProcessService proofOfDebtCollectionProcessService;
    private final ProofOfDebtCollectionService proofOfDebtCollectionService;
    private final RemoteContractExecuteService remoteContractExecuteService;

    private final ContractReceivableService contractReceivableService;

    @PostMapping("/launch")
    @Operation(summary = "发起催款证据流程")
    @PreAuthorize(hasPermission = "crm_contract_receivable_collection_proof")
    public JsonObject<Boolean> launch(@Valid @RequestBody ProofOfDebtCollectionLaunchable launchDTO) {
        boolean launch = proofOfDebtCollectionProcessService.launch(launchDTO);
        return new JsonObject<>(launch);
    }
    @PostMapping("/page")
    @Operation(summary = "催款证据流程分页-合同催款证据详情页使用")
    @PreAuthorize(hasPermission = "crm_contract_receivable_collection_proof")
    public JsonObject<PageUtils<ProofOfDebtCollectionVO>> page(@Valid @RequestBody ProofOfDebtCollectionQuery query) {
        query.setPlatformCode(UserInfoHolder.getCurrentAudience());
        PageUtils<ProofOfDebtCollectionVO> page = proofOfDebtCollectionService.page(query);
        List<ProofOfDebtCollectionVO> list = page.getList();
        NameUtils.setName(list);
        return new JsonObject<>(page);
    }

    @PostMapping("/detail")
    @Operation(summary = "催款证据流程详情")
    @PreFlowPermission
    public JsonObject<ProofOfDebtCollectionVO> detail() {
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        ProofOfDebtCollectionVO detail = proofOfDebtCollectionService.detail(processInstanceId);
        NameUtils.setName(detail);
        return new JsonObject<>(detail);
    }

    @GetMapping("/queryExecute")
    @Operation(summary = "根据合同编号查询合同执行信息")
    @PreAuthorize(hasPermission = "crm_contract_receivable_collection_proof")
    public JsonObject<CrmContractExecuteVO> queryExecute(@RequestParam String contractNumber){
        return remoteContractExecuteService.getByContractNumber(contractNumber);
    }

    @PostMapping("/queryPaymentProvision")
    @Operation(summary = "根据合同编号查询合同付款条款")
    @PreAuthorize(hasPermission = "crm_contract_receivable_collection_proof")
    public JsonObject<PageUtils<PaymentProvisionDTO>> queryPaymentProvision(@Valid @RequestBody ProofOfDebtCollectionQuery query,
                                                                     @Schema(hidden = true) HttpServletRequest httpServletRequest){

        String contractNumber = query.getContractNumber();
        httpServletRequest.setAttribute("pageNum",query.getPageNum());
        httpServletRequest.setAttribute("pageSize",query.getPageSize());
        return remoteContractExecuteService.pagePaymentProvisionByContractNumber(contractNumber);
    }

    @PostMapping("/queryReceivable")
    @Operation(summary = "根据合同编号查询应收催款信息")
    @PreAuthorize(hasPermission = "crm_contract_receivable_collection_proof")
    public JsonObject<PageUtils<ContractReceivableDetailDTO>> queryReceivable(@Valid @RequestBody ProofOfDebtCollectionQuery query,
                                                                       @Schema(hidden = true) HttpServletRequest httpServletRequest){
        httpServletRequest.setAttribute("pageNum",query.getPageNum());
        httpServletRequest.setAttribute("pageSize",query.getPageSize());
        return new JsonObject<>(contractReceivableService.pageReceivableDetailByContractNumber(query.getContractNumber()));
    }

    @PutMapping("/fillInfo")
    @Operation(summary = "02法务部填信息")
    @PreFlowPermission
    public JsonObject<Boolean> fillInfo(@RequestBody ProofOfDebtCollectionVO collectionVO){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        collectionVO.setProcessInstanceId(processInstanceId);
        Assert.hasText(collectionVO.getProcessInstanceId(),"流程实例id不能为空");
        Assert.hasText(collectionVO.getArchiveNumber(),"档案编号不能为空");
        Assert.notNull(collectionVO.getPagesNumber(),"合同页数不能为空");
        Assert.notNull(collectionVO.getCopiesNumber(),"合同份数不能为空");
        Assert.notNull(collectionVO.getProcurementMethod(),"采购方式不能为空");
        return new JsonObject<>(proofOfDebtCollectionService.fillInfo(collectionVO));
    }

    @GetMapping("/valid02")
    @Operation(summary = "02法务部填信息校验")
    @PreFlowPermission
    public JsonObject<Boolean> valid02(){
        String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(proofOfDebtCollectionService.valid02(processInstanceId));
    }


}
