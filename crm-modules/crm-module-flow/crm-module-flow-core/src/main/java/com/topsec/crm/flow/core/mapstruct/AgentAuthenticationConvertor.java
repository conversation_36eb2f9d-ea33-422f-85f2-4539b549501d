package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.agentauthentication.AgentAuthenticationDocDTO;
import com.topsec.crm.flow.core.entity.AgentAuthenticationDoc;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AgentAuthenticationConvertor {
    AgentAuthenticationConvertor INSTANCE = Mappers.getMapper(AgentAuthenticationConvertor.class);


    AgentAuthenticationDocDTO toDocDTO(AgentAuthenticationDoc doc);
    List<AgentAuthenticationDocDTO> toDocDTOList(List<AgentAuthenticationDoc> doc);

    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "delFlag", ignore = true)
    AgentAuthenticationDoc toDoc(AgentAuthenticationDocDTO doc);
    List<AgentAuthenticationDoc> toDocList(List<AgentAuthenticationDocDTO> doc);

}
