package com.topsec.crm.flow.core.validator.pricereview.condition;

import com.topsec.crm.flow.core.validator.CheckCondition;

import com.topsec.crm.flow.core.validator.pricereview.PriceReviewCheckContext;
import lombok.Data;
import org.apache.commons.collections4.ListUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 签约关系变更
 * <AUTHOR>
 */
public class SigningAgentChangeCondition implements CheckCondition<PriceReviewCheckContext> {

    /**
     * 四个属性任意一个属性不同，则属于签约关系变更
     */
    @Data
    private static class SignAgentComparable {
        private String agentId;
        private String parentAgentId;
        private Integer isContractPartyA;
        private Integer isWinningBidder;
    }

    // public static void main(String[] args) {
    //     Set<SignAgentComparable> set = new HashSet<>();
    //     SignAgentComparable signAgentComparable = new SignAgentComparable();
    //     signAgentComparable.setAgentId("1");
    //     signAgentComparable.setParentAgentId("1");
    //     signAgentComparable.setIsContractPartyA(1);
    //     signAgentComparable.setIsWinningBidder(1);
    //     set.add(signAgentComparable);
    //     signAgentComparable = new SignAgentComparable();
    //     signAgentComparable.setAgentId("3");
    //     signAgentComparable.setParentAgentId("3");
    //     signAgentComparable.setIsContractPartyA(3);
    //     signAgentComparable.setIsWinningBidder(3);
    //     set.add(signAgentComparable);
    //     signAgentComparable = new SignAgentComparable();
    //     signAgentComparable.setAgentId("4");
    //     signAgentComparable.setParentAgentId("4");
    //     signAgentComparable.setIsContractPartyA(4);
    //     signAgentComparable.setIsWinningBidder(4);
    //     set.add(signAgentComparable);
    //     signAgentComparable = new SignAgentComparable();
    //     signAgentComparable.setAgentId("5");
    //     signAgentComparable.setParentAgentId("5");
    //     signAgentComparable.setIsContractPartyA(5);
    //     signAgentComparable.setIsWinningBidder(5);
    //     set.add(signAgentComparable);
    //
    //
    //     Set<SignAgentComparable> set1 = new HashSet<>();
    //     signAgentComparable = new SignAgentComparable();
    //     signAgentComparable.setAgentId("5");
    //     signAgentComparable.setParentAgentId("5");
    //     signAgentComparable.setIsContractPartyA(5);
    //     signAgentComparable.setIsWinningBidder(5);
    //     set1.add(signAgentComparable);
    //     signAgentComparable = new SignAgentComparable();
    //     signAgentComparable.setAgentId("1");
    //     signAgentComparable.setParentAgentId("1");
    //     signAgentComparable.setIsContractPartyA(1);
    //     signAgentComparable.setIsWinningBidder(1);
    //     set1.add(signAgentComparable);
    //     signAgentComparable = new SignAgentComparable();
    //     signAgentComparable.setAgentId("3");
    //     signAgentComparable.setParentAgentId("3");
    //     signAgentComparable.setIsContractPartyA(3);
    //     signAgentComparable.setIsWinningBidder(3);
    //     set1.add(signAgentComparable);
    //     signAgentComparable = new SignAgentComparable();
    //     signAgentComparable.setAgentId("4");
    //     signAgentComparable.setParentAgentId("4");
    //     signAgentComparable.setIsContractPartyA(4);
    //     signAgentComparable.setIsWinningBidder(4);
    //     set1.add(signAgentComparable);
    //
    //     System.out.println(set1.equals(set));
    // }



    @Override
    public boolean check(PriceReviewCheckContext context)  {
        Integer currentType = context.getProjectDetail().getProjectInfo().getSigningType();
        // 当前为直签，跳过判断签约关系变更
        if ( currentType.equals(1) ) {
            return true;
        }

        Set<SignAgentComparable> current = ListUtils.emptyIfNull(context.getProjectDetail().getSigningAgentList())
                .stream().filter(agent -> {
                    return Objects.equals(agent.getDelFlag(), 0);
                })
                .map(agent -> {
                    SignAgentComparable comparable = new SignAgentComparable();
                    comparable.setAgentId(agent.getAgentId());
                    comparable.setParentAgentId(agent.getParentAgentId());
                    comparable.setIsContractPartyA(agent.getIsContractPartyA());
                    comparable.setIsWinningBidder(agent.getIsWinningBidder());
                    return comparable;
                }).collect(Collectors.toSet());

        Set<SignAgentComparable> snapshot = ListUtils.emptyIfNull(context.getProjectSnapshot().getSigningAgentList())
                .stream().filter(agent -> {
                    return Objects.equals(agent.getDelFlag(), 0);
                })
                .map(agent -> {
                    SignAgentComparable comparable = new SignAgentComparable();
                    comparable.setAgentId(agent.getAgentId());
                    comparable.setParentAgentId(agent.getParentAgentId());
                    comparable.setIsContractPartyA(agent.getIsContractPartyA());
                    comparable.setIsWinningBidder(agent.getIsWinningBidder());
                    return comparable;
                }).collect(Collectors.toSet());

        return snapshot.equals(current);
    }

    @Override
    public String defaultFailureReason() {
        return "签约关系发生变更";
    }
}
