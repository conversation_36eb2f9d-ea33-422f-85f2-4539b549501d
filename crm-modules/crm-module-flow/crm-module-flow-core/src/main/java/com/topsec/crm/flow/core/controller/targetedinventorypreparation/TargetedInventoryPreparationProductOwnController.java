package com.topsec.crm.flow.core.controller.targetedinventorypreparation;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.TargetedInventoryPreparationProductOwnVO;
import com.topsec.crm.flow.core.entity.TargetedInventoryPreparationMain;
import com.topsec.crm.flow.core.service.ITargetedInventoryPreparationProductOwnService;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.product.api.RemoteStockDisableCodeConfigService;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 专项备货流程-自有产品表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-07-26 18:21
 */
@RestController
@Slf4j
@RequestMapping("/targetedInventoryPreparationProductOwn")
@Tag(name = "专项备货-自有产品", description = "/targetedInventoryPreparationProductOwn")
public class TargetedInventoryPreparationProductOwnController extends BaseController {
//    @Autowired
//    private ITargetedInventoryPreparationProductOwnService iTargetedInventoryPreparationProductOwnService;


}
