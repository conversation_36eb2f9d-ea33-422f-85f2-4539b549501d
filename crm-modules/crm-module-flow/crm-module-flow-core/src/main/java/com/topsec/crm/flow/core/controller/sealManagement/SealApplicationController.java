package com.topsec.crm.flow.core.controller.sealManagement;

import com.topsec.crm.flow.api.vo.sealApplication.SealApplicationInfoVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.service.SealApplicationService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.operation.api.entity.Seal.CrmSealTypeProcessVO;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/sealApplication")
@Tag(name = "印鉴申请-流程接口", description = "/sealApplication")
@RequiredArgsConstructor
@Validated
public class SealApplicationController extends BaseController {

    private final SealApplicationService sealApplicationService;

    @GetMapping("/queryApproverForSpecifiedNode")
    @Operation(summary = "获取指定节点的可选审批人列表（只能查询流程图上配置的固定审核人，不支持动态指定审核人）")
    @PreAuthorize
    public JsonObject<List<FlowPerson>> page(@RequestParam String processDefinitionKey){
        return new JsonObject<>(sealApplicationService.queryApproverForSpecifiedNode(processDefinitionKey));
    }

    @GetMapping("/getSealInfoByProcessInstanceId")
    @Operation(summary = "获取印鉴详情信息")
    @PreFlowPermission
    public JsonObject<SealApplicationInfoVO> getSealInfoByProcessInstanceId(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(sealApplicationService.getSealInfoByProcessInstanceId(processInstanceId));
    }

    @PostMapping("/updateSealApplication")
    @Operation(summary = "修改印鉴申请")
    @PreFlowPermission
    //todo 需补充不同流程审批节点ID
    public JsonObject<Boolean> updateSealApplication(@RequestBody SealApplicationInfoVO sealApplicationInfoVO){
        CrmAssert.hasText(sealApplicationInfoVO.getProcessInstanceId(),"流程ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(sealApplicationInfoVO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(sealApplicationService.updateSealApplication(sealApplicationInfoVO));
    }


    @GetMapping("/querySealListByProcessInstanceId")
    @Operation(summary = "主流程详情查询印鉴列表")
    @PreFlowPermission
    public JsonObject<List<SealApplicationInfoVO>> querySealListByProcessInstanceId(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(sealApplicationService.listSealInfoByParentProcessInstanceId(processInstanceId));
    }

    @GetMapping("/getSealTypeByPII")
    @Operation(summary = "根据流程获取可申请印鉴类型")
    @PreFlowPermission
    public JsonObject<List<CrmSealTypeProcessVO>> getSealTypeByPII(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(sealApplicationService.getSealTypeByPII(processInstanceId));
    }

}
