
package com.topsec.crm.flow.core.service.impl.handoverProcess;

import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.flow.api.vo.handoverProcess.*;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.HandoverProcessReviewMainMapper;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.flow.core.util.ListConvertUtil;
import com.topsec.crm.framework.common.bean.*;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.operation.api.RemoteExpertService;
import com.topsec.crm.operation.api.RemoteSupplierService;
import com.topsec.crm.operation.api.RemoteTenderingCompanyService;
import com.topsec.crm.project.api.RemoteProjectDynastyService;
import com.topsec.crm.project.api.client.RemoteBorrowForProbationClient;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectPerformanceNegotiationClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.BriefInfoVO;
import com.topsec.tos.common.vo.EmployeeVO;
import io.jsonwebtoken.lang.Arrays;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


@Service("handoverProcessReviewMainService")
@RequiredArgsConstructor
public class HandoverProcessReviewMainServiceImpl extends ServiceImpl<HandoverProcessReviewMainMapper, HandoverProcessReviewMain> implements HandoverProcessReviewMainService {
    private final HandoverProjectService handoverProjectService;

    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final HandoverTrialBorrowService handoverTrialBorrowService;
    private final HandoverBiddingCompanyService handoverBiddingCompanyService;
    private final HandoverLoanService handoverLoanService;
    private final HandoverBorrowToSaleService handoverBorrowToSaleService;
    private final HandoverCustomerService handoverCustomerService;
    private final HandoverContractService handoverContractService;
    private final HandoverExpertService handoverExpertService;
    private final HandoverPerformanceService handoverPerformanceService;
    private final HandoverPerformanceNegotiationService handoverPerformanceNegotiationService;
    private final RemoteCustomerService remoteCustomerService;
    private final IBorrowForSellService borrowForSellService;
    private final RemoteBorrowForProbationClient remoteBorrowForProbationClient;
    private final RemoteExpertService remoteExpertService;
    private final RemoteProjectDynastyService remoteProjectDynastyService;
    private final RemoteContractExecuteService remoteContractExecuteService;
    private final RemoteTenderingCompanyService remoteTenderingCompanyService;
    private final RemoteSupplierService remoteSupplierService;
    private final RemoteProjectPerformanceNegotiationClient remoteProjectPerformanceNegotiationClient;
    private final LoanApplyService loanApplyService;
    private final SalesAgreementReviewMainService salesAgreementReviewMainService;

    private final TosDepartmentClient tosDepartmentClient;


    private final HandoverSalesAgreementService handoverSalesAgreementService;

    private final HandoverSupplierService handoverSupplierService;
    @Override
    public Page<HandoverProcessReviewMain> page(HandoverProcessCommonQuery query) {
        Page<HandoverProcessReviewMain> page = new Page<>(query.getPageNum(), query.getPageSize());
        return baseMapper.selectPage(page, new LambdaQueryWrapper<HandoverProcessReviewMain>()
                        .like(StringUtils.isNotBlank(query.getProcessNumber()), HandoverProcessReviewMain::getProcessNumber, query.getProcessNumber())
                .eq(StringUtils.isNotBlank(query.getPersonId()), HandoverProcessReviewMain::getPersonId, query.getPersonId())
                        .eq(StringUtils.isNotBlank(query.getHandoverType()), HandoverProcessReviewMain::getHandoverType, query.getHandoverType())
                .in(CollectionUtils.isNotEmpty(query.getDeptIdList()), HandoverProcessReviewMain::getDeptId, query.getDeptIdList())
        );

    }

    @Override
    public HandoverProcessReviewMain getHandoverProcessInfoByProcessInstanceId(String processInstanceId) {
        return baseMapper.selectOne(new LambdaQueryWrapper<HandoverProcessReviewMain>()
                .eq(HandoverProcessReviewMain::getProcessInstanceId, processInstanceId));
    }

    @Override
    public boolean updateHandoverProcessInfo(HandoverProcessReviewMain handoverProcessReviewMain) {
        // 参数校验
        if (handoverProcessReviewMain.getId() == null && handoverProcessReviewMain.getProcessInstanceId() == null) {
            throw new CrmException("参数不能为空");
        }

        HandoverProcessReviewMain handover = baseMapper.selectById(handoverProcessReviewMain.getId());
        if (handover == null) {
            throw new CrmException("数据不存在");
        }

        // 根据交接类型处理不同逻辑
        if (Integer.valueOf(1).equals(handover.getHandoverType())) {
            // 离职类型处理
            validateAndProcessQuitDetail(handoverProcessReviewMain);
        } else {
            // 调动类型处理
            validateAndProcessTransferDetail(handoverProcessReviewMain);
        }

        // 更新数据库
        return updateHandoverDetails(handoverProcessReviewMain);
    }

    /**
     * 验证并处理离职详情
     */
    private void validateAndProcessQuitDetail(HandoverProcessReviewMain handoverProcessReviewMain) {
        QuitDetailVo quitDetail = handoverProcessReviewMain.getQuitDetail();
        if (quitDetail == null) {
            throw new CrmException("参数错误");
        }

        // 验证必填字段
        validateQuitDetailFields(quitDetail);

        // 复用原有数据并更新变更字段
        QuitDetailVo existingQuitDetail = handoverProcessReviewMain.getQuitDetail();
        if (existingQuitDetail != null) {
            existingQuitDetail.setEmergencyContact(quitDetail.getEmergencyContact());
            existingQuitDetail.setEmergencyContactPhone(quitDetail.getEmergencyContactPhone());
            existingQuitDetail.setAssetReturnNumbers(quitDetail.getAssetReturnNumbers());
            existingQuitDetail.setAssetTransferNumbers(quitDetail.getAssetTransferNumbers());
            existingQuitDetail.setApplyForResidencePermit(quitDetail.getApplyForResidencePermit());
        }
    }

    /**
     * 验证离职详情必填字段
     */
    private void validateQuitDetailFields(QuitDetailVo quitDetail) {
        if (StringUtils.isBlank(quitDetail.getEmergencyContact())) {
            throw new CrmException("紧急联系人不能为空");
        }
        if (StringUtils.isBlank(quitDetail.getEmergencyContactPhone())) {
            throw new CrmException("紧急联系人方式不能为空");
        }
        if (StringUtils.isBlank(quitDetail.getAssetReturnNumbers())) {
            throw new CrmException("资产退库单号不能为空");
        }
        if (StringUtils.isBlank(quitDetail.getAssetTransferNumbers())) {
            throw new CrmException("资产调拨单号不能为空");
        }
        if (quitDetail.getApplyForResidencePermit() == null) {
            throw new CrmException("申请居住证不能为空");
        }
    }

    /**
     * 验证并处理调动详情
     */
    private void validateAndProcessTransferDetail(HandoverProcessReviewMain handoverProcessReviewMain) {
        TransferDetail transferDetail = handoverProcessReviewMain.getTransferDetail();
        if (transferDetail == null) {
            throw new CrmException("参数错误");
        }

        // 验证必填字段
        validateTransferDetailFields(transferDetail);

        // 复用原有数据并更新变更字段
        TransferDetail existingTransferDetail = handoverProcessReviewMain.getTransferDetail();
        if (existingTransferDetail != null) {
            existingTransferDetail.setAssetReturnNumbers(transferDetail.getAssetReturnNumbers());
            existingTransferDetail.setAssetTransferNumbers(transferDetail.getAssetTransferNumbers());
            existingTransferDetail.setTransferReason(transferDetail.getTransferReason());
            existingTransferDetail.setTutor(transferDetail.getTutor());
            existingTransferDetail.setIsPostTransferSales(transferDetail.getIsPostTransferSales());
            existingTransferDetail.setTransferTime(transferDetail.getTransferTime());

            // 成本分摊信息处理
//            if (transferDetail.getCostAllocationVo() != null) {
                existingTransferDetail.setCostAllocationVo(transferDetail.getCostAllocationVo());
//            }
        }
    }

    /**
     * 验证调动详情必填字段
     */
    private void validateTransferDetailFields(TransferDetail transferDetail) {
        if (StringUtils.isBlank(transferDetail.getTransferReason())) {
            throw new CrmException("调动原因不能为空");
        }
        if (transferDetail.getTransferTime() == null) {
            throw new CrmException("调动日期不能为空");
        }
        if (StringUtils.isBlank(transferDetail.getAssetReturnNumbers())) {
            throw new CrmException("资产退库单号不能为空");
        }
        if (StringUtils.isBlank(transferDetail.getAssetTransferNumbers())) {
            throw new CrmException("资产调拨单号不能为空");
        }
        if (transferDetail.getTutor() == null) {
            throw new CrmException("导师不能为空");
        }
        if (transferDetail.getIsPostTransferSales() == null) {
            throw new CrmException("调动后是否包含销售性质不能为空");
        }
    }

    /**
     * 更新交接详情到数据库
     */
    private boolean updateHandoverDetails(HandoverProcessReviewMain handoverProcessReviewMain) {
        LambdaUpdateWrapper<HandoverProcessReviewMain> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(handoverProcessReviewMain.getTransferDetail() != null,
                        HandoverProcessReviewMain::getTransferDetail,
                        JacksonUtils.toJson(handoverProcessReviewMain.getTransferDetail()))
                .set(handoverProcessReviewMain.getQuitDetail() != null,
                        HandoverProcessReviewMain::getQuitDetail,
                        JacksonUtils.toJson(handoverProcessReviewMain.getQuitDetail()))
                .eq(HandoverProcessReviewMain::getId, handoverProcessReviewMain.getId());

        return baseMapper.update(updateWrapper) > 0;
    }



    @Override
    public JsonObject<PageUtils<?>> getListPage(HandoverProcessQuery query, String type) {
        switch (type) {
            case "project":
                JsonObject<PageUtils<HandoverProjectVo>> projectPage = remoteProjectDirectlyClient.queryUserTransferProjectPage(query);
                if (projectPage != null && projectPage.isSuccess()) {
                    PageUtils<HandoverProjectVo> objEntity = projectPage.getObjEntity();
                    if (objEntity != null) {
                        List<HandoverProjectVo> list = objEntity.getList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<String> businessIds = list.stream().map(HandoverProjectVo::getBusinessId).collect(Collectors.toList());
                            List<HandoverProject> projects = handoverProjectService.findByBusinessIds(businessIds,query.getProcessInstanceId());
                            Set<String> projectBusinessIds = projects.stream().map(HandoverProject::getBusinessId).collect(Collectors.toSet());
                            list.forEach(vo -> vo.setSelect(projectBusinessIds.contains(vo.getBusinessId())));
                        }
                    }
                    return new JsonObject<>(objEntity);
                } else {
                    return new JsonObject<>(new PageUtils<>());
                }

            case "zdProject":
                JsonObject<PageUtils<HandoverProjectVo>> zdJsonObject = remoteProjectDynastyService.queryUserTransferProjectPage(query);
                if (zdJsonObject != null && zdJsonObject.isSuccess()) {
                    PageUtils<HandoverProjectVo> objEntity = zdJsonObject.getObjEntity();
                    if (objEntity != null) {
                        List<HandoverProjectVo> list = objEntity.getList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<String> businessIds = list.stream().map(HandoverProjectVo::getBusinessId).collect(Collectors.toList());
                            List<HandoverProject> projects = handoverProjectService.findByBusinessIds(businessIds,query.getProcessInstanceId());
                            Set<String> projectBusinessIds = projects.stream().map(HandoverProject::getBusinessId).collect(Collectors.toSet());
                            list.forEach(vo -> vo.setSelect(projectBusinessIds.contains(vo.getBusinessId())));
                        }
                    }
                    return new JsonObject<>(objEntity);
                } else {
                    return new JsonObject<>(new PageUtils<>());
                }

            case "contract":
                JsonObject<PageUtils<HandoverContractVo>> contractPage = remoteContractExecuteService.pageByHandoverProcessQuery(query);
                if (contractPage != null && contractPage.isSuccess()) {
                    PageUtils<HandoverContractVo> objEntity = contractPage.getObjEntity();
                    if (objEntity != null) {
                        List<HandoverContractVo> list = objEntity.getList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<String> businessIds = list.stream().map(HandoverContractVo::getBusinessId).collect(Collectors.toList());
                            List<HandoverContract> contracts = handoverContractService.findByBusinessIds(businessIds,query.getProcessInstanceId());
                            Set<String> contractBusinessIds = contracts.stream().map(HandoverContract::getBusinessId).collect(Collectors.toSet());
                            list.forEach(vo ->
                                vo.setSelect(contractBusinessIds.contains(vo.getBusinessId())));
                        }
                    }
                    return new JsonObject<>(objEntity);
                } else {
                    return new JsonObject<>(new PageUtils<>());
                }

            case "performance":
                throw new IllegalArgumentException("还没提供接口: " + type);

            case "trialBorrow":
                JsonObject<PageUtils<HandoverTrialBorrowVo>> trialBorrowPage = remoteBorrowForProbationClient.queryUserTransferDevicePage(query);
                if (trialBorrowPage != null && trialBorrowPage.isSuccess()) {
                    PageUtils<HandoverTrialBorrowVo> objEntity = trialBorrowPage.getObjEntity();
                    if (objEntity != null) {
                        List<HandoverTrialBorrowVo> list = objEntity.getList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<String> businessIds = list.stream().map(HandoverTrialBorrowVo::getBusinessId).collect(Collectors.toList());
                            List<HandoverTrialBorrow> trialBorrows = handoverTrialBorrowService.findByBusinessIds(businessIds,query.getProcessInstanceId());
                            Set<String> trialBorrowBusinessIds = trialBorrows.stream().map(HandoverTrialBorrow::getBusinessId).collect(Collectors.toSet());
                            list.forEach(vo -> vo.setSelect(trialBorrowBusinessIds.contains(vo.getBusinessId())));
                        }
                    }
                    return new JsonObject<>(objEntity);
                } else {
                    return new JsonObject<>(new PageUtils<>());
                }

            case "borrowToSale":
                PageUtils<HandoverBorrowToSaleVo> borrowToSaleVoPageUtils = borrowForSellService.queryUserTransferBorrowForSellPage(query);
                if (borrowToSaleVoPageUtils != null) {
                    List<HandoverBorrowToSaleVo> list = borrowToSaleVoPageUtils.getList();
                    if (CollectionUtils.isNotEmpty(list)) {
                        List<String> businessIds = list.stream().map(HandoverBorrowToSaleVo::getBusinessId).collect(Collectors.toList());
                        List<HandoverBorrowToSale> borrowToSales = handoverBorrowToSaleService.findByBusinessIds(businessIds,query.getProcessInstanceId());
                        Set<String> borrowToSaleBusinessIds = borrowToSales.stream().map(HandoverBorrowToSale::getBusinessId).collect(Collectors.toSet());
                        list.forEach(vo -> vo.setSelect(borrowToSaleBusinessIds.contains(vo.getBusinessId())));
                    }
                }
                return new JsonObject<>(borrowToSaleVoPageUtils != null ? borrowToSaleVoPageUtils : new PageUtils<>());

            case "performanceNegotiation":
                JsonObject<PageUtils<HandoverPerformanceNegotiationVo>> handoverPerformanceNegotiationPage = remoteProjectPerformanceNegotiationClient.getHandoverPerformanceNegotiationPage(query);
                if (handoverPerformanceNegotiationPage != null && handoverPerformanceNegotiationPage.isSuccess()) {
                    PageUtils<HandoverPerformanceNegotiationVo> objEntity = handoverPerformanceNegotiationPage.getObjEntity();
                    if (objEntity != null) {
                        List<HandoverPerformanceNegotiationVo> list = objEntity.getList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<String> businessIds = list.stream().map(HandoverPerformanceNegotiationVo::getBusinessId).collect(Collectors.toList());
                            List<HandoverPerformanceNegotiation> performanceNegotiations = handoverPerformanceNegotiationService.findByBusinessIds(businessIds,query.getProcessInstanceId());
                            Set<String> performanceNegotiationBusinessIds = performanceNegotiations.stream().map(HandoverPerformanceNegotiation::getBusinessId).collect(Collectors.toSet());
                            list.forEach(vo -> vo.setSelect(performanceNegotiationBusinessIds.contains(vo.getBusinessId())));
                        }
                    }
                    return new JsonObject<>(objEntity);
                } else {
                    return new JsonObject<>(new PageUtils<>());
                }

            case "salesAgreement":
                PageUtils<HandoverSalesAgreementVo> handoverSalesAgreementProcessPage = salesAgreementReviewMainService.findHandoverSalesAgreementProcessPage(query);
                if (handoverSalesAgreementProcessPage != null) {
                    List<HandoverSalesAgreementVo> list = handoverSalesAgreementProcessPage.getList();
                    if (CollectionUtils.isNotEmpty(list)) {
                        List<String> businessIds = list.stream().map(HandoverSalesAgreementVo::getId).collect(Collectors.toList());
                        List<HandoverSalesAgreement> salesAgreements = handoverSalesAgreementService.findByBusinessIds(businessIds,query.getProcessInstanceId());
                        Set<String> salesAgreementBusinessIds = salesAgreements.stream().map(HandoverSalesAgreement::getBusinessId).collect(Collectors.toSet());
                        list.forEach(vo ->{
                            vo.setBusinessId(vo.getId());
                            vo.setId(null);
                            vo.setSelect(salesAgreementBusinessIds.contains(vo.getBusinessId()));
                            vo.setPersonId(query.getPersonId());
                        } );
                    }
                }
                return new JsonObject<>(handoverSalesAgreementProcessPage != null ? handoverSalesAgreementProcessPage : new PageUtils<>());

            case "loan":
                PageUtils<HandoverLoanVo> handoverLoanVoPage = loanApplyService.handoverLoanSelectPage(query);
                if (handoverLoanVoPage != null) {
                    List<HandoverLoanVo> list = handoverLoanVoPage.getList();
                    if (CollectionUtils.isNotEmpty(list)) {
                        List<String> businessIds = list.stream().map(HandoverLoanVo::getBusinessId).collect(Collectors.toList());
                        List<HandoverLoan> loans = handoverLoanService.findByBusinessIds(businessIds,query.getProcessInstanceId());
                        Set<String> loanBusinessIds = loans.stream().map(HandoverLoan::getBusinessId).collect(Collectors.toSet());
                        list.forEach(vo -> vo.setSelect(loanBusinessIds.contains(vo.getBusinessId())));
                    }
                }
                return new JsonObject<>(handoverLoanVoPage != null ? handoverLoanVoPage : new PageUtils<>());
            case "customer":
                JsonObject<PageUtils<HandoverCustomerVo>> customerHasLinkman = remoteCustomerService.selectCustomerHasLinkman(query);
                if (customerHasLinkman != null && customerHasLinkman.isSuccess()) {
                    PageUtils<HandoverCustomerVo> objEntity = customerHasLinkman.getObjEntity();
                    if (objEntity != null) {
                        List<HandoverCustomerVo> list = objEntity.getList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<String> businessIds = list.stream().map(HandoverCustomerVo::getBusinessId).collect(Collectors.toList());
                            List<HandoverCustomer> customers = handoverCustomerService.findByBusinessIds(businessIds,query.getProcessInstanceId());
                            Set<String> customerBusinessIds = customers.stream().map(HandoverCustomer::getBusinessId).collect(Collectors.toSet());
                            list.forEach(vo -> {
                                vo.setPersonId(query.getPersonId());
                                Optional.ofNullable(vo.getCompanyContact())
                                        .stream()
                                        .flatMap(List::stream)
                                        .filter(contact -> contact != null &&
                                                contact.getId() != null &&
                                                contact.getId().equals(query.getPersonId()))
                                        .findFirst()
                                        .ifPresent(contact -> vo.setCompanyContact(Collections.singletonList(contact)));
                                vo.setSelect(customerBusinessIds.contains(vo.getBusinessId()));
                            });
                        }
                    }
                    return new JsonObject<>(objEntity);
                } else {
                    return new JsonObject<>(new PageUtils<>());
                }

            case "expert":
                JsonObject<PageUtils<HandoverExpertVo>> expertPage = remoteExpertService.expertPage(query);
                if (expertPage != null && expertPage.isSuccess()) {
                    PageUtils<HandoverExpertVo> objEntity = expertPage.getObjEntity();
                    if (objEntity != null) {
                        List<HandoverExpertVo> list = objEntity.getList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<String> businessIds = list.stream().map(HandoverExpertVo::getBusinessId).collect(Collectors.toList());
                            List<HandoverExpert> experts = handoverExpertService.findByBusinessIds(businessIds,query.getProcessInstanceId());
                            Set<String> expertBusinessIds = experts.stream().map(HandoverExpert::getBusinessId).collect(Collectors.toSet());
                            list.forEach(vo -> vo.setSelect(expertBusinessIds.contains(vo.getBusinessId())));
                        }
                    }
                    return new JsonObject<>(objEntity);
                } else {
                    return new JsonObject<>(new PageUtils<>());
                }

            case "biddingCompany":
                JsonObject<PageUtils<HandoverBiddingCompanyVo>> biddingCompanyPage = remoteTenderingCompanyService.searchHandoverBiddingCompaniesPage(query);
                if (biddingCompanyPage != null && biddingCompanyPage.isSuccess()) {
                    PageUtils<HandoverBiddingCompanyVo> objEntity = biddingCompanyPage.getObjEntity();
                    if (objEntity != null) {
                        List<HandoverBiddingCompanyVo> list = objEntity.getList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<String> businessIds = list.stream().map(HandoverBiddingCompanyVo::getBusinessId).collect(Collectors.toList());
                            List<HandoverBiddingCompany> biddingCompanies = handoverBiddingCompanyService.findByBusinessIds(businessIds,query.getProcessInstanceId());
                            Set<String> biddingCompanyBusinessIds = biddingCompanies.stream().map(HandoverBiddingCompany::getBusinessId).collect(Collectors.toSet());
                            list.forEach(vo -> vo.setSelect(biddingCompanyBusinessIds.contains(vo.getBusinessId())));
                        }
                    }
                    return new JsonObject<>(objEntity);
                } else {
                    return new JsonObject<>(new PageUtils<>());
                }

            case "supplier":
                JsonObject<PageUtils<HandoverSupplierVo>> supplierPage = remoteSupplierService.pageByHandoverProcessQuery(query);
                if (supplierPage != null && supplierPage.isSuccess()) {
                    PageUtils<HandoverSupplierVo> objEntity = supplierPage.getObjEntity();
                    if (objEntity != null) {
                        List<HandoverSupplierVo> list = objEntity.getList();
                        if (CollectionUtils.isNotEmpty(list)) {
                            List<String> businessIds = list.stream().map(HandoverSupplierVo::getBusinessId).collect(Collectors.toList());
                            List<HandoverSupplier> suppliers = handoverSupplierService.findByBusinessIds(businessIds,query.getProcessInstanceId());
                            Set<String> supplierBusinessIds = suppliers.stream().map(HandoverSupplier::getBusinessId).collect(Collectors.toSet());
                            list.forEach(vo -> vo.setSelect(supplierBusinessIds.contains(vo.getBusinessId())));
                        }
                    }
                    return new JsonObject<>(objEntity);
                } else {
                    return new JsonObject<>(new PageUtils<>());
                }

            default:
                throw new IllegalArgumentException("不支持的类型: " + type);
        }
    }



    @Override
    public JsonObject<PageUtils<?>> getPage(HandoverProcessQuery query, String type) {
        switch (type) {
            case "project":
                query.setProjectType(0);
                Page<HandoverProject> projectPage = handoverProjectService.page(query);
                IPage<HandoverProjectVo> convert = projectPage.convert(handoverProjectVo ->
                        HyperBeanUtils.copyPropertiesByJackson(handoverProjectVo, HandoverProjectVo.class));
                return new JsonObject<>(new PageUtils<>(convert));

            case "zdProject":
                query.setProjectType(1);
                Page<HandoverProject> zdProjectPage = handoverProjectService.page(query);
                IPage<HandoverProjectVo> zdConvert = zdProjectPage.convert(handoverProjectVo ->
                        HyperBeanUtils.copyPropertiesByJackson(handoverProjectVo, HandoverProjectVo.class));
                return new JsonObject<>(new PageUtils<>(zdConvert));

            case "contract":
                Page<HandoverContract> contractPage = handoverContractService.page(query);
                IPage<HandoverContractVo> contractVoPage = contractPage.convert(entity ->
                        HyperBeanUtils.copyPropertiesByJackson(entity, HandoverContractVo.class));
                return new JsonObject<>(new PageUtils<>(contractVoPage));

            case "performance":
                Page<HandoverPerformance> performancePage = handoverPerformanceService.page(query);
                IPage<HandoverPerformanceVo> performanceConvert = performancePage.convert(handoverPerformance ->
                        HyperBeanUtils.copyPropertiesByJackson(handoverPerformance, HandoverPerformanceVo.class)
                );
                return new JsonObject<>(new PageUtils<>(performanceConvert));

            case "trialBorrow":
                Page<HandoverTrialBorrow> trialBorrowPage = handoverTrialBorrowService.page(query);
                IPage<HandoverTrialBorrowVo> trialBorrowConvert = trialBorrowPage.convert(handoverTrialBorrowVo ->
                        HyperBeanUtils.copyPropertiesByJackson(handoverTrialBorrowVo, HandoverTrialBorrowVo.class));
                return new JsonObject<>(new PageUtils<>(trialBorrowConvert));

            case "borrowToSale":
                Page<HandoverBorrowToSale> borrowToSalePage = handoverBorrowToSaleService.page(query);
                IPage<HandoverBorrowToSaleVo> borrowToSaleVoPage = borrowToSalePage.convert(entity ->
                        HyperBeanUtils.copyPropertiesByJackson(entity, HandoverBorrowToSaleVo.class));
                return new JsonObject<>(new PageUtils<>(borrowToSaleVoPage));


            case "performanceNegotiation":
                Page<HandoverPerformanceNegotiation> performanceNegotiationPage = handoverPerformanceNegotiationService.page(query);
                IPage<HandoverPerformanceNegotiationVo> performanceNegotiationConvert = performanceNegotiationPage.convert(handoverPerformanceNegotiationVo ->
                        HyperBeanUtils.copyPropertiesByJackson(handoverPerformanceNegotiationVo, HandoverPerformanceNegotiationVo.class));
                return new JsonObject<>(new PageUtils<>(performanceNegotiationConvert));

            case "salesAgreement":
                Page<HandoverSalesAgreement> salesAgreementPage = handoverSalesAgreementService.page(query);
                IPage<HandoverSalesAgreementVo> salesAgreementPageConvert = salesAgreementPage.convert(handoverSalesAgreement ->
                        HyperBeanUtils.copyPropertiesByJackson(handoverSalesAgreement, HandoverSalesAgreementVo.class)
                );
                return new JsonObject<>(new PageUtils<>(salesAgreementPageConvert));

            case "loan":
                Page<HandoverLoan> loanPage = handoverLoanService.page(query);
                IPage<HandoverLoanVo> loanVoPage = loanPage.convert(entity ->
                        HyperBeanUtils.copyPropertiesByJackson(entity, HandoverLoanVo.class));
                return new JsonObject<>(new PageUtils<>(loanVoPage));

            case "customer":
                Page<HandoverCustomer> customerPage = handoverCustomerService.page(query);
                IPage<HandoverCustomerVo> customerVoPage = customerPage.convert(entity ->
                        HyperBeanUtils.copyPropertiesByJackson(entity, HandoverCustomerVo.class));
                return new JsonObject<>(new PageUtils<>(customerVoPage));

            case "expert":
                Page<HandoverExpert> expertPage = handoverExpertService.page(query);
                IPage<HandoverExpertVo> expertVoPage = expertPage.convert(entity ->
                        HyperBeanUtils.copyPropertiesByJackson(entity, HandoverExpertVo.class));
                return new JsonObject<>(new PageUtils<>(expertVoPage));

            case "biddingCompany":
                Page<HandoverBiddingCompany> biddingCompanyPage = handoverBiddingCompanyService.page(query);
                IPage<HandoverBiddingCompanyVo> biddingCompanyVoPage = biddingCompanyPage.convert(entity ->
                        HyperBeanUtils.copyPropertiesByJackson(entity, HandoverBiddingCompanyVo.class));
                return new JsonObject<>(new PageUtils<>(biddingCompanyVoPage));

            case "supplier":
                Page<HandoverSupplier> supplierPage = handoverSupplierService.page(query);
                IPage<HandoverSupplierVo> supplierConvert = supplierPage.convert(handoverSupplierVo ->
                        HyperBeanUtils.copyPropertiesByJackson(handoverSupplierVo, HandoverSupplierVo.class));
                return new JsonObject<>(new PageUtils<>(supplierConvert));

            default:
                throw new IllegalArgumentException("不支持的类型: " + type);
    }
}

    @Override
    public JsonObject<Boolean> genericAddHandover(List<?> vo, String type) {
        // 参数校验
        if (vo == null) {
            throw new IllegalArgumentException("参数vo不能为空");
        }

        if (type == null || type.isEmpty()) {
            throw new IllegalArgumentException("参数type不能为空");
        }

        try {
            switch (type) {
                case "biddingCompany":
                    List<HandoverBiddingCompany> biddingCompany = ListConvertUtil.safeConvertList(vo, HandoverBiddingCompany.class);
                    return new JsonObject<>(handoverBiddingCompanyService.addHandover(biddingCompany));

                case "loan":
                    List<HandoverLoan> loan = ListConvertUtil.safeConvertList(vo, HandoverLoan.class);
                    return new JsonObject<>(handoverLoanService.addHandover(loan));

                case "borrowToSale":
                    List<HandoverBorrowToSale> borrowToSale = ListConvertUtil.safeConvertList(vo, HandoverBorrowToSale.class);
                    return new JsonObject<>(handoverBorrowToSaleService.addHandover(borrowToSale));

                case "customer":
                    List<HandoverCustomer> customer = ListConvertUtil.safeConvertList(vo, HandoverCustomer.class);
                    return new JsonObject<>(handoverCustomerService.addHandover(customer));

                case "contract":
                    List<HandoverContract> contract = ListConvertUtil.safeConvertList(vo, HandoverContract.class);
                    return new JsonObject<>(handoverContractService.addHandover(contract));

                case "expert":
                    List<HandoverExpert> expert = ListConvertUtil.safeConvertList(vo, HandoverExpert.class);
                    return new JsonObject<>(handoverExpertService.addHandover(expert));

                case "project":
                case "zdProject":
                    List<HandoverProject> project = ListConvertUtil.safeConvertList(vo, HandoverProject.class);
                    return new JsonObject<>(handoverProjectService.addHandover(project));

                case "trialBorrow":
                    List<HandoverTrialBorrow> trialBorrow = ListConvertUtil.safeConvertList(vo, HandoverTrialBorrow.class);
                    return new JsonObject<>(handoverTrialBorrowService.addHandover(trialBorrow));

                case "performance":
                    List<HandoverPerformance> performance = ListConvertUtil.safeConvertList(vo, HandoverPerformance.class);
                    return new JsonObject<>(handoverPerformanceService.addHandover(performance));

                case "performanceNegotiation":
                    List<HandoverPerformanceNegotiation> performanceNegotiation = ListConvertUtil.safeConvertList(vo, HandoverPerformanceNegotiation.class);
                    return new JsonObject<>(handoverPerformanceNegotiationService.addHandover(performanceNegotiation));

                case "salesAgreement":
                    List<HandoverSalesAgreement> salesAgreement = ListConvertUtil.safeConvertList(vo, HandoverSalesAgreement.class);
                    return new JsonObject<>(handoverSalesAgreementService.addHandover(salesAgreement));

                case "supplier":
                    List<HandoverSupplier> supplier = ListConvertUtil.safeConvertList(vo, HandoverSupplier.class);
                    return new JsonObject<>(handoverSupplierService.addHandover(supplier));

                default:
                    throw new IllegalArgumentException("不支持的类型: " + type);
            }
        } catch (Exception e) {
            throw new RuntimeException("批量添加数据失败: " + e.getMessage(), e);
        }
    }




    @Override
    public JsonObject<Boolean> genericUpdateHandover(HandoverUpdateVo vo, String type) {
        // 参数校验
        if (vo == null) {
            throw new IllegalArgumentException("参数vo不能为空");
        }

        if (type == null || type.isEmpty()) {
            throw new IllegalArgumentException("参数type不能为空");
        }
        switch (type) {
            case "biddingCompany":
                return new JsonObject<>(handoverBiddingCompanyService.updateHandover(vo));

            case "loan":
                return new JsonObject<>(handoverLoanService.updateHandover(vo));

            case "borrowToSale":
                return new JsonObject<>(handoverBorrowToSaleService.updateHandover(vo));

            case "customer":
                return new JsonObject<>(handoverCustomerService.updateHandover(vo));

            case "contract":
                return new JsonObject<>(handoverContractService.updateHandover(vo));

            case "expert":
                return new JsonObject<>(handoverExpertService.updateHandover(vo));

            case "project":
            case "zdProject":
                return new JsonObject<>(handoverProjectService.updateHandover(vo));

            case "trialBorrow":
                return new JsonObject<>(handoverTrialBorrowService.updateHandover(vo));

            case "performance":
                return new JsonObject<>(handoverPerformanceService.updateHandover(vo));

            case "performanceNegotiation":
                return new JsonObject<>(handoverPerformanceNegotiationService.updateHandover(vo));

            case "salesAgreement":
                return new JsonObject<>(handoverSalesAgreementService.updateHandover(vo));

            case "supplier":
                return new JsonObject<>(handoverSupplierService.updateHandover(vo));

            default:
                throw new IllegalArgumentException("不支持的类型: " + type);
        }
    }

    @Override
    public JsonObject<Boolean> genericDelete(String[] ids, String type) {

        List<String> idList = Arrays.asList(ids);
        // 参数校验
        if (idList == null) {
            throw new IllegalArgumentException("参数ids不能为空");
        }

        if (type == null || type.isEmpty()) {
            throw new IllegalArgumentException("参数type不能为空");
        }
        switch (type) {
            case "biddingCompany":
                return new JsonObject<>(handoverBiddingCompanyService.removeByIds(idList));
            case "loan":
                return new JsonObject<>(handoverLoanService.removeByIds(idList));
            case "borrowToSale":
                return new JsonObject<>(handoverBorrowToSaleService.removeByIds(idList));
            case "customer":
                return new JsonObject<>(handoverCustomerService.removeByIds(idList));
            case "contract":
                return new JsonObject<>(handoverContractService.removeByIds(idList));
            case "expert":
                return new JsonObject<>(handoverExpertService.removeByIds(idList));
            case "project":
            case "zdProject":
                return new JsonObject<>(handoverProjectService.removeByIds(idList));
            case "trialBorrow":
                return new JsonObject<>(handoverTrialBorrowService.removeByIds(idList));
            case "performance":
                return new JsonObject<>(handoverPerformanceService.removeByIds(idList));
            case "performanceNegotiation":
                return new JsonObject<>(handoverPerformanceNegotiationService.removeByIds(idList));
            case "salesAgreement":
                return new JsonObject<>(handoverSalesAgreementService.removeByIds(idList));
            case "supplier":
                return new JsonObject<>(handoverSupplierService.removeByIds(idList));
            default:
                throw new IllegalArgumentException("不支持的类型: " + type);
        }

    }

    @Override
    public JsonObject<List<HandoverInfoVo>> getInfoPage(HandoverProcessQuery query) {

        List<CompletableFuture<HandoverInfoVo>> futures = new ArrayList<>();

        // Project查询
        CompletableFuture<HandoverInfoVo> projectFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            query.setProjectType(0);
            Page<HandoverProject> projectPage = handoverProjectService.page(query);
            IPage<HandoverProjectVo> convert = projectPage.convert(handoverProjectVo ->
                    HyperBeanUtils.copyPropertiesByJackson(handoverProjectVo, HandoverProjectVo.class));
            handoverInfoVo.setType("project");
            handoverInfoVo.setPage(new PageUtils<>(convert));
            return handoverInfoVo;
        });
        futures.add(projectFuture);

        // ZDProject查询
        CompletableFuture<HandoverInfoVo> zdProjectFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            query.setProjectType(1);
            Page<HandoverProject> zdProjectPage = handoverProjectService.page(query);
            IPage<HandoverProjectVo> zdConvert = zdProjectPage.convert(handoverProjectVo ->
                    HyperBeanUtils.copyPropertiesByJackson(handoverProjectVo, HandoverProjectVo.class));
            handoverInfoVo.setType("zdProject");
            handoverInfoVo.setPage(new PageUtils<>(zdConvert));
            return handoverInfoVo;
        });
        futures.add(zdProjectFuture);

        // Contract查询
        CompletableFuture<HandoverInfoVo> contractFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            Page<HandoverContract> contractPage = handoverContractService.page(query);
            IPage<HandoverContractVo> contractVoPage = contractPage.convert(entity ->
                    HyperBeanUtils.copyPropertiesByJackson(entity, HandoverContractVo.class));
            handoverInfoVo.setType("contract");
            handoverInfoVo.setPage(new PageUtils<>(contractVoPage));
            return handoverInfoVo;
        });
        futures.add(contractFuture);

        // Performance查询
        CompletableFuture<HandoverInfoVo> performanceFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            Page<HandoverPerformance> performancePage = handoverPerformanceService.page(query);
            IPage<HandoverPerformanceVo> performanceConvert = performancePage.convert(handoverPerformance ->
                    HyperBeanUtils.copyPropertiesByJackson(handoverPerformance, HandoverPerformanceVo.class)
            );
            handoverInfoVo.setType("performance");
            handoverInfoVo.setPage(new PageUtils<>(performanceConvert));
            return handoverInfoVo;
        });
        futures.add(performanceFuture);

        // TrialBorrow查询
        CompletableFuture<HandoverInfoVo> trialBorrowFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            Page<HandoverTrialBorrow> trialBorrowPage = handoverTrialBorrowService.page(query);
            IPage<HandoverTrialBorrowVo> trialBorrowConvert = trialBorrowPage.convert(handoverTrialBorrowVo ->
                    HyperBeanUtils.copyPropertiesByJackson(handoverTrialBorrowVo, HandoverTrialBorrowVo.class));
            handoverInfoVo.setType("trialBorrow");
            handoverInfoVo.setPage(new PageUtils<>(trialBorrowConvert));
            return handoverInfoVo;
        });
        futures.add(trialBorrowFuture);

        // BorrowToSale查询
        CompletableFuture<HandoverInfoVo> borrowToSaleFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            Page<HandoverBorrowToSale> borrowToSalePage = handoverBorrowToSaleService.page(query);
            IPage<HandoverBorrowToSaleVo> borrowToSaleVoPage = borrowToSalePage.convert(entity ->
                    HyperBeanUtils.copyPropertiesByJackson(entity, HandoverBorrowToSaleVo.class));
            handoverInfoVo.setType("borrowToSale");
            handoverInfoVo.setPage(new PageUtils<>(borrowToSaleVoPage));
            return handoverInfoVo;
        });
        futures.add(borrowToSaleFuture);

        // PerformanceNegotiation查询
        CompletableFuture<HandoverInfoVo> performanceNegotiationFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            Page<HandoverPerformanceNegotiation> performanceNegotiationPage = handoverPerformanceNegotiationService.page(query);
            IPage<HandoverPerformanceNegotiationVo> performanceNegotiationConvert = performanceNegotiationPage.convert(handoverPerformanceNegotiationVo ->
                    HyperBeanUtils.copyPropertiesByJackson(handoverPerformanceNegotiationVo, HandoverPerformanceNegotiationVo.class));
            handoverInfoVo.setType("performanceNegotiation");
            handoverInfoVo.setPage(new PageUtils<>(performanceNegotiationConvert));
            return handoverInfoVo;
        });
        futures.add(performanceNegotiationFuture);

        // SalesAgreement查询
        CompletableFuture<HandoverInfoVo> salesAgreementFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            Page<HandoverSalesAgreement> salesAgreementPage = handoverSalesAgreementService.page(query);
            IPage<HandoverSalesAgreementVo> salesAgreementPageConvert = salesAgreementPage.convert(handoverSalesAgreement ->
                    HyperBeanUtils.copyPropertiesByJackson(handoverSalesAgreement, HandoverSalesAgreementVo.class)
            );
            handoverInfoVo.setType("salesAgreement");
            handoverInfoVo.setPage(new PageUtils<>(salesAgreementPageConvert));
            return handoverInfoVo;
        });
        futures.add(salesAgreementFuture);

        // Loan查询
        CompletableFuture<HandoverInfoVo> loanFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            Page<HandoverLoan> loanPage = handoverLoanService.page(query);
            IPage<HandoverLoanVo> loanVoPage = loanPage.convert(entity ->
                    HyperBeanUtils.copyPropertiesByJackson(entity, HandoverLoanVo.class));
            handoverInfoVo.setType("loan");
            handoverInfoVo.setPage(new PageUtils<>(loanVoPage));
            return handoverInfoVo;
        });
        futures.add(loanFuture);

        // Customer查询
        CompletableFuture<HandoverInfoVo> customerFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            Page<HandoverCustomer> customerPage = handoverCustomerService.page(query);
            IPage<HandoverCustomerVo> customerVoPage = customerPage.convert(entity ->
                    HyperBeanUtils.copyPropertiesByJackson(entity, HandoverCustomerVo.class));
            handoverInfoVo.setType("customer");
            handoverInfoVo.setPage(new PageUtils<>(customerVoPage));
            return handoverInfoVo;
        });
        futures.add(customerFuture);

        // Expert查询
        CompletableFuture<HandoverInfoVo> expertFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            Page<HandoverExpert> expertPage = handoverExpertService.page(query);
            IPage<HandoverExpertVo> expertVoPage = expertPage.convert(entity ->
                    HyperBeanUtils.copyPropertiesByJackson(entity, HandoverExpertVo.class));
            handoverInfoVo.setType("expert");
            handoverInfoVo.setPage(new PageUtils<>(expertVoPage));
            return handoverInfoVo;
        });
        futures.add(expertFuture);

        // BiddingCompany查询
        CompletableFuture<HandoverInfoVo> biddingCompanyFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            Page<HandoverBiddingCompany> biddingCompanyPage = handoverBiddingCompanyService.page(query);
            IPage<HandoverBiddingCompanyVo> biddingCompanyVoPage = biddingCompanyPage.convert(entity ->
                    HyperBeanUtils.copyPropertiesByJackson(entity, HandoverBiddingCompanyVo.class));
            handoverInfoVo.setType("biddingCompany");
            handoverInfoVo.setPage(new PageUtils<>(biddingCompanyVoPage));
            return handoverInfoVo;
        });
        futures.add(biddingCompanyFuture);

        // Supplier查询
        CompletableFuture<HandoverInfoVo> supplierFuture = CompletableFuture.supplyAsync(() -> {
            HandoverInfoVo handoverInfoVo = new HandoverInfoVo();
            Page<HandoverSupplier> supplierPage = handoverSupplierService.page(query);
            IPage<HandoverSupplierVo> supplierConvert = supplierPage.convert(handoverSupplierVo ->
                    HyperBeanUtils.copyPropertiesByJackson(handoverSupplierVo, HandoverSupplierVo.class));
            handoverInfoVo.setType("supplier");
            handoverInfoVo.setPage(new PageUtils<>(supplierConvert));
            return handoverInfoVo;
        });
        futures.add(supplierFuture);

        // 等待所有查询完成并收集结果
        List<HandoverInfoVo> results = futures.stream()
                .map(CompletableFuture::join)
                .toList();
        return new JsonObject<>(results);

    }


    @Override
    public boolean uploadHandoverProcessAttachment(HandoverProcessReviewMain handoverProcessReviewMain) {
        return baseMapper.update(null, new LambdaUpdateWrapper<HandoverProcessReviewMain>()
                .set(HandoverProcessReviewMain::getAttachmentIds, JacksonUtils.toJson(handoverProcessReviewMain.getAttachmentIds()))
                .eq(HandoverProcessReviewMain::getDelFlag, 0)
                .eq(HandoverProcessReviewMain::getProcessInstanceId, handoverProcessReviewMain.getProcessInstanceId()))>0;
    }

    @Override
    public CostAllocationVo queryCostDivision(String processInstanceId) {
        if (StringUtils.isBlank(processInstanceId)) {
            throw new CrmException("流程实例ID不能为空");
        }

        HandoverProcessReviewMain handoverProcessReviewMain = Optional.ofNullable(getHandoverProcessInfoByProcessInstanceId(processInstanceId))
                .orElseThrow(() -> new CrmException("未找到对应的交接流程信息"));

        CostAllocationVo costAllocationVo = new CostAllocationVo();
        costAllocationVo.setId(handoverProcessReviewMain.getId());
        costAllocationVo.setPersonId(handoverProcessReviewMain.getPersonId());
        costAllocationVo.setProcessInstanceId(handoverProcessReviewMain.getProcessInstanceId());
        costAllocationVo.setName(handoverProcessReviewMain.getPersonName());

        List<ResponsibleDepartmentVo> responsibleDept = new LinkedList<>();

    // 原部门信息 - 添加到列表开头
        buildResponsibleDepartment(handoverProcessReviewMain.getDeptId())
                .ifPresent(dept -> responsibleDept.add(0, dept));

    // 调动后部门信息 - 添加到列表末尾
        buildResponsibleDepartment(
                Optional.ofNullable(handoverProcessReviewMain.getTransferDetail())
                        .map(TransferDetail::getTransferDept)
                        .map(BaseInfoVO::getUuid)
                        .orElse(null))
                .ifPresent(responsibleDept::add); // 默认添加到末尾

        costAllocationVo.setResponsibleDept(responsibleDept);
        return costAllocationVo;
    }

    @Override
    public boolean batchApproveOrReject(HandoverUpdateVo vo,String type) {
        if ("contract".equals( type)){
            return handoverContractService.batchApproveOrReject(vo);
        }else if ("performance".equals( type)){
            return handoverPerformanceService.batchApproveOrReject(vo);
        }else {
            throw new CrmException("类型错误");
        }
    }

    /**
     * 构建责任部门信息
     * @param deptId 部门ID
     * @return Optional<ResponsibleDepartmentVo> 对象
     */
    private Optional<ResponsibleDepartmentVo> buildResponsibleDepartment(String deptId) {
        return Optional.ofNullable(deptId)
                .filter(StringUtils::isNotBlank)
                .map(this::createResponsibleDepartment);
    }

    private ResponsibleDepartmentVo createResponsibleDepartment(String deptId) {
        ResponsibleDepartmentVo responsibleDeptVo = new ResponsibleDepartmentVo();
        List<BriefInfoVO> leaders = getDepartmentLeaders(deptId).stream()
                .map(this::convertFlowPersonToBaseInfoVO)
                .collect(Collectors.toList());
        BaseInfoVO baseInfoVO = new BaseInfoVO();
        baseInfoVO.setUuid(deptId);
        responsibleDeptVo.setDept(baseInfoVO);
        responsibleDeptVo.setLeader(leaders);
        return responsibleDeptVo;
    }

    private BriefInfoVO convertFlowPersonToBaseInfoVO(EmployeeVO employeeVO) {
        BriefInfoVO baseInfoVO = new BriefInfoVO();
        baseInfoVO.setUuid(employeeVO.getUuid());
        baseInfoVO.setName(employeeVO.getName());
        baseInfoVO.setJobNo(employeeVO.getJobNo());
        return baseInfoVO;
    }


    // 提取公共方法
    private List<EmployeeVO> getDepartmentLeaders(String deptId) {
        return Optional.ofNullable(tosDepartmentClient.findById(deptId))
                .filter(JsonObject::isSuccess)
                .map(JsonObject::getObjEntity)
                .map(dept -> ListUtils.emptyIfNull(dept.getLeaders()))
                .orElse(Collections.emptyList());
    }





}