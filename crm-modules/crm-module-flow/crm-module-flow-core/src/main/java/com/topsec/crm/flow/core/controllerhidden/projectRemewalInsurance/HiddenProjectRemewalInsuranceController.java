package com.topsec.crm.flow.core.controllerhidden.projectRemewalInsurance;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.topsec.crm.flow.api.dto.projectRemewalInsurance.ProjectRemewalInsuranceFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.projectRemewalInsurance.ProjectRemewalInsuranceVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ProjectRemewalInsurance;
import com.topsec.crm.flow.core.process.impl.ProjectRemewalInsuranceProcessService;
import com.topsec.crm.flow.core.service.IProjectRemewalInsuranceService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectProductOwnClient;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/hidden/projectRemewalInsurance")
@RequiredArgsConstructor
public class HiddenProjectRemewalInsuranceController extends BaseController {

    @Autowired
    private IProjectRemewalInsuranceService projectRemewalInsuranceService;
    @Autowired
    private ProjectRemewalInsuranceProcessService projectRemewalInsuranceProcessService;
    @Autowired
    private RemoteProjectProductOwnClient remoteProjectProductOwnClient;
    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    @GetMapping("/findAll")
    public JsonObject<List<ProjectRemewalInsuranceVo>> findAll() {
        List<ProjectRemewalInsurance> projectRemewalInsurance = projectRemewalInsuranceService.query().list();
        List<ProjectRemewalInsuranceVo> prmv = HyperBeanUtils.copyListPropertiesByJackson(projectRemewalInsurance, ProjectRemewalInsuranceVo.class);
        return new JsonObject<>(prmv);
    }

    @GetMapping("/updateProduct")
    public JsonObject<Boolean> updateProduct(@RequestBody List<ProjectRemewalInsuranceVo> projectRemewalInsuranceVos) {
        List<ProjectRemewalInsurance> prmvs = HyperBeanUtils.copyListPropertiesByJackson(projectRemewalInsuranceVos, ProjectRemewalInsurance.class);
        projectRemewalInsuranceService.updateBatchById(prmvs);
        return new JsonObject<>(true);
    }

    @GetMapping("/findSPZ")
    public JsonObject<Boolean> findSPZ(@RequestParam String projectId){
        //1.判断是否存在审批中的零金额续保流程，如果存在，则不让发起
        ProjectRemewalInsurance one = projectRemewalInsuranceService.getOne(
                new QueryWrapper<ProjectRemewalInsurance>()
                        .eq("project_id", projectId)
                        .eq("del_flag", false)
                        .eq("process_state", ApprovalStatusEnum.SPZ.getCode())
                        .orderByDesc("create_time")
                        .last("limit 1")
        );
        if (one != null) {
            return new JsonObject<>(true);
        }else{
            return new JsonObject<>(false);
        }
    }

}
