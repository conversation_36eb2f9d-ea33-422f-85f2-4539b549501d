package com.topsec.crm.flow.core.controller.threeProcurement;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.flow.api.dto.contractreview.baseinfo.ContractBasicInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.api.vo.*;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ContractReviewPaymentProvision;
import com.topsec.crm.flow.core.entity.ThreeProcurementPaymentReviewMain;
import com.topsec.crm.flow.core.entity.ThreeProcurementRelease;
import com.topsec.crm.flow.core.entity.ThreeProcurementReviewMain;
import com.topsec.crm.flow.core.process.impl.ThreeProcurementReviewProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.*;

/**
 * 第三方采购Controller
 *
 * @date 2024-09-02
 */
@RestController
@RequestMapping("/threeProcurement")
@Tag(name = "【第三方采购】", description = "threeProcurement")
@RequiredArgsConstructor
@Validated
public class ThreeProcurementReviewMainController extends BaseController
{

    private final ThreeProcurementReviewMainService threeProcurementReviewMainService;

    private final ThreeProcurementReviewProcessService threeProcurementReviewProcessService;

    private final ThreeProcurementPaymentReviewMainService threeProcurementPaymentReviewMainService;

    private final ContractReviewMainService contractReviewMainService;

    private final ContractReviewPaymentProvisionService contractReviewPaymentProvisionService;

    private final TfsNodeClient tfsNodeClient;





    @PostMapping("/launch")
    @Operation(summary = "发起第三方采购流程-SM和产品线")
    @PreAuthorize(hasAnyPermission = {"crm_third_purchase_sm_todo","crm_third_purchase_product_line_todo"})
    public JsonObject<Boolean> launch(@Valid @RequestBody ThreeProcurementReviewLaunchVo threeProcurementReviewLaunchVo) {
        return new JsonObject<>(threeProcurementReviewProcessService.launch(threeProcurementReviewLaunchVo));
    }







    @GetMapping("/queryThirdInfoByContractIdAndSupplier")
    @Operation(summary = "根据流程实例id查询第三方产品信息-流程权限（编辑时查询固定查询某个供应商下的第三方产品） ")
    @PreFlowPermission
    public JsonObject<ThreeProcurementProductInfoVo> queryThirdInfoByContractIdAndSupplier(@RequestParam String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程实例ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementReviewMain threeProcurementReviewMain = threeProcurementReviewMainService.selectThreeProcurementReviewMainByProcessInstanceId(processInstanceId);
        if (Objects.isNull(threeProcurementReviewMain)){
            throw new CrmException("合同信息不存在");
        }
        ThreeProcurementProductInfoVo procurementProductInfoVo = threeProcurementReviewMainService.queryThirdProductInfo(threeProcurementReviewMain.getContractId(), threeProcurementReviewMain.getSupplier(),threeProcurementReviewMain.isReturnAndExchange());
        return new JsonObject<>(procurementProductInfoVo);
    }










    @GetMapping("/contractBasicInfo")
    @Operation(summary = "合同评审基础信息")
    @PreFlowPermission
    public JsonObject<ContractBasicInfoDTO> contractBasicInfo(@RequestParam String processInstanceId) {
        CrmAssert.hasText(processInstanceId,"流程实例ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementReviewMain threeProcurementReviewMain = threeProcurementReviewMainService.selectThreeProcurementReviewMainByProcessInstanceId(processInstanceId);
        if (Objects.isNull(threeProcurementReviewMain)){
            throw new CrmException("合同信息不存在");
        }
        String contractId = threeProcurementReviewMain.getContractId();
        return new JsonObject<>(contractReviewMainService.contractBasicInfo(contractId, true));
    }



    /**
     * 获取【第三方采购】详情
     */
    @GetMapping(value = "/getThreeProcurementBaseInfo")
    @Operation(summary = "获取【第三方采购】详情")
    @PreFlowPermission
    public JsonObject<ThreeProcurementReviewBaseInfoVo> getThreeProcurementBaseInfo(@RequestParam String processInstanceId)
    {
        CrmAssert.hasText(processInstanceId,"流程实例ID不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementReviewBaseInfoVo baseInfoVo = threeProcurementReviewMainService.selectThreeProcurementReviewBaseInfoVoByProcessInstanceId(processInstanceId);
        return new JsonObject<>(baseInfoVo);
    }



    /**
     * 付款审批详情中采购基本信息
     */
    @GetMapping(value = "/getThreeProcurementBasicInformation")
    @Operation(summary = "付款审批详情中采购基本信息")
    @PreFlowPermission
    public JsonObject<ThreeProcurementReviewMainVo> getThreeProcurementBasicInformation(@RequestParam String purchaseNumber)
    {
        ThreeProcurementReviewMainVo threeProcurementReviewMainVo = threeProcurementReviewMainService.selectThreeProcurementReviewMainByPurchaseNumber(purchaseNumber);
        if (!Objects.isNull(threeProcurementReviewMainVo)){
                List<ThreeProcurementPaymentReviewMain> threeProcurementPaymentList = threeProcurementPaymentReviewMainService.getThreeProcurementPaymentList(purchaseNumber, null);
                if (threeProcurementPaymentList.isEmpty()){
                    throw  new CrmException("流程实例id不匹配");
                }
                List<String> strings = threeProcurementPaymentList.stream().map(ThreeProcurementPaymentReviewMain::getPurchaseNumber).toList();
                if (!strings.contains(purchaseNumber)){
                    throw new CrmException(ResultEnum.AUTH_ERROR_500006);
                }

            Map<String, Set<ApproveNode>> stringSetMap = Optional.ofNullable(tfsNodeClient.queryNodeByProcessInstanceIdList(ListUtils.emptyIfNull(Collections.singletonList(threeProcurementReviewMainVo.getProcessInstanceId()))))
                    .map(JsonObject::getObjEntity).orElseThrow(() -> new RuntimeException("审批节点查询失败"));
            threeProcurementReviewMainVo.setApprovalNode(stringSetMap.get(threeProcurementReviewMainVo.getProcessInstanceId()));
        }
        return new JsonObject<>(threeProcurementReviewMainVo);
    }


    @PostMapping("/upload")
    @Operation(summary = "02拟定采购合同-第三方采购-上传采购合同")
    @PreFlowPermission(hasAnyNodes = {"thirdProcure_02"})
    public JsonObject<Boolean> upload(@RequestBody ThreeProcurementReviewMainVo threeProcurementReviewMainVo){
        ThreeProcurementReviewMain threeProcurementReviewMain = HyperBeanUtils.copyProperties(threeProcurementReviewMainVo, ThreeProcurementReviewMain::new);
        return new JsonObject(threeProcurementReviewMainService.uploadThreeProcurementReviewMain(threeProcurementReviewMain));
    }


    @PostMapping("/associatedPo")
    @Operation(summary = "10关联PO号-关联PO号")
    @PreFlowPermission(hasAnyNodes = {"sid-E07A6CF0-66BC-4AF1-B968-B333904927F7"})
    public JsonObject<Boolean> associatedPo(@RequestBody ThreeProcurementAssociatedPoVo associatedPoVO){
        CrmAssert.hasText(associatedPoVO.getPo(), "po号不能为空");
        CrmAssert.hasText(associatedPoVO.getProcessInstanceId(), "流程实例id不能为空");
        PreFlowPermissionAspect.checkProcessInstanceId(associatedPoVO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(threeProcurementReviewMainService.updatePo(associatedPoVO));
    }

    /**
     * 批量导入物料代码
     */
    @PostMapping("/batchUpdateStuffCode")
    @Operation(summary = "批量导入物料代码")
    @PreFlowPermission(hasAnyNodes = {"sid-2A33C3CF-D099-4A95-9DDE-5DB2904D9A65"})
    public JsonObject<Boolean> batchUpdateStuffCode(@RequestBody List<ThreeProcurementStuffCodeVo> threeProcurementStuffCodeVoList)
    {
        return new JsonObject<>(threeProcurementReviewMainService.batchUpdateMaterialCode(threeProcurementStuffCodeVoList));
    }



    /**
     * 修改第三方采购信息
     */
    @PostMapping("/updateThreeProcurement")
    @Operation(summary = "修改第三方采购信息")
    @PreFlowPermission
    public JsonObject<Boolean> updateThreeProcurement(@RequestBody ThreeProcurementReviewBaseInfoVo threeProcurementReviewBaseInfoVo)
    {
        PreFlowPermissionAspect.checkProcessInstanceId(threeProcurementReviewBaseInfoVo.getThreeProcurementReviewMainVo().getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(threeProcurementReviewMainService.updateThreeProcurement(threeProcurementReviewBaseInfoVo));
    }

    @PostMapping("/selectAttachmentList")
    @Operation(summary = "查询第三方采购附件列表")
    @PreFlowPermission
    public JsonObject<List<ProcessAttachmentVo>> selectAttachmentList(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementReviewMain threeProcurementReviewMain = threeProcurementReviewMainService.selectThreeProcurementReviewMainByProcessInstanceId(processInstanceId);
        List<ProcessAttachmentVo> listThreeProcurementAttachment = threeProcurementPaymentReviewMainService.getListThreeProcurementAttachment(threeProcurementReviewMain.getAttachmentIds());
        return new JsonObject<>(listThreeProcurementAttachment);
    }

    @GetMapping("/checkLaunchPayment")
    @PreAuthorize
    public JsonObject<Boolean> checkLaunchPayment(@RequestParam String processInstanceId){
        List<ThreeProcurementPaymentReviewMain> procurementPaymentReviewMainList = threeProcurementPaymentReviewMainService.selectThreeProcurementPaymentByProcurementProcessId(processInstanceId);
        return new JsonObject<>(CollectionUtils.isNotEmpty(procurementPaymentReviewMainList));
    }


    @GetMapping("/pagePaymentProvisionByContractId")
    @PreFlowPermission
    @Operation(summary = "根据流程实例id查询合同付款条款")
    public JsonObject<TableDataInfo> pagePaymentProvisionByContractId(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ThreeProcurementReviewMain threeProcurementReviewMain = threeProcurementReviewMainService.selectThreeProcurementReviewMainByProcessInstanceId(processInstanceId);
        if (Objects.isNull(threeProcurementReviewMain)){
            throw new CrmException("合同信息不存在");
        }
        String contractId = threeProcurementReviewMain.getContractId();
        startPage();
        if (threeProcurementReviewMain.isReturnAndExchange()){
            List<ContractReviewPaymentProvision>  reviewPaymentProvisionList = contractReviewPaymentProvisionService.getByReturnExchangeProcessInstanceId(threeProcurementReviewMain.getReturnAndExchangeProcessId());
            List<PaymentProvisionDTO> result = HyperBeanUtils.copyListProperties(reviewPaymentProvisionList, PaymentProvisionDTO::new);
            TableDataInfo tableDataInfo = new TableDataInfo();
            NameUtils.setName(result);
            tableDataInfo.setList(result);
            tableDataInfo.setTotalCount(new PageInfo<>(reviewPaymentProvisionList).getTotal());
            return new JsonObject<>(tableDataInfo);
        }
        return new JsonObject<>(contractReviewPaymentProvisionService.pageByContractId(contractId));
    }








}
