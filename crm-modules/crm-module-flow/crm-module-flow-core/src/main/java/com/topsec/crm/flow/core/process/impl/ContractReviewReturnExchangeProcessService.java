package com.topsec.crm.flow.core.process.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.topsec.crm.contract.api.*;
import com.topsec.crm.contract.api.entity.CrmContractBaseInfoVO;
import com.topsec.crm.contract.api.entity.CrmContractProductOwnVO;
import com.topsec.crm.contract.api.entity.CrmContractProductThirdVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.originaldocument.NodeSaveInfoVO;
import com.topsec.crm.file.api.RemoteFsmDocService;
import com.topsec.crm.flow.api.RemoteContractUnconfirmedDetailService;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewTermDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractFlowProductMappingDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.dto.contractreview.sninfo.ContractProductSnVO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.PaymentProvisionDTO;
import com.topsec.crm.flow.api.dto.contractreview.terminfo.RevenueRecognitionDTO;
import com.topsec.crm.flow.api.dto.costTransferMinus.CostTransferMinusFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.returnexchange.*;
import com.topsec.crm.flow.api.dto.sealApplication.SealApplicationFlowLaunchDTO;
import com.topsec.crm.flow.api.vo.ProcessFileInfoVO;
import com.topsec.crm.flow.api.vo.ProcessFileInput;
import com.topsec.crm.flow.api.vo.ThreeProcurementProductLineReviewLaunchVo;
import com.topsec.crm.flow.api.vo.ThreeProcurementSmReviewLaunchVo;
import com.topsec.crm.flow.api.vo.costTransferMinus.MinusMoneyQuery;
import com.topsec.crm.flow.api.vo.sealApplication.ApplySealTypeInfoVO;
import com.topsec.crm.flow.api.vo.sealApplication.SealFileInfoVO;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.validator.returnexchange.ReturnValidator;
import com.topsec.crm.flow.core.mapper.*;
import com.topsec.crm.flow.core.mapstruct.ReturnExchangeConvertor;
import com.topsec.crm.flow.core.process.AbstractProjectProcessService;
import com.topsec.crm.flow.core.process.ProcessTypeEnum;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.flow.core.util.FlowThreadExecutor;
import com.topsec.crm.flow.core.util.PriceUtils;
import com.topsec.crm.framework.common.bean.CrmFsmDoc;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnVO;
import com.topsec.crm.framework.common.bean.FileInput;
import com.topsec.crm.framework.common.enums.OriginalTypeEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.operation.api.RemoteContractReviewConfigService;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractSignCompanyVO;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectProductOwnClient;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.crm.project.api.entity.CrmProjectOutsourcingServiceVo;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.enums.ActionTypeEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.FlowStateInfoVo;
import com.topsec.vo.task.TfsTaskVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ContractReviewReturnExchangeProcessService extends AbstractProjectProcessService<ContractReviewReturnExchangeLaunchDTO> implements LockStateQuerier {

    private final ContractReviewMainService mainService;
    private final ContractReviewProductOwnMapper contractReviewProductOwnMapper;
    private final ContractReviewProductThirdMapper contractReviewProductThirdMapper;
    private final ReturnExchangeProductMapper returnExchangeProductMapper;
    private final ReturnExchangeProductThirdMapper returnExchangeProductThirdMapper;
    private final ReturnExchangeFeeService returnExchangeFeeService;
    private final ReturnExchangeFeeMapper returnExchangeFeeMapper;
    private final RemoteContractExecuteService contractExecuteService;
    private final ContractReviewReturnExchangeMapper contractReviewReturnExchangeMapper;
    private final ContractReviewReturnExchangeService contractReviewReturnExchangeService;
    private final ReturnExchangeProductService returnExchangeProductService;
    private final ReturnExchangeProductThirdService returnExchangeProductThirdService;
    private final ContractReviewPaymentProvisionService contractReviewPaymentProvisionService;
    private final ContractReviewRevenueRecognitionService contractReviewRevenueRecognitionService;
    private final ContractReviewProductOwnService contractReviewProductOwnService;
    private final ContractReviewProductThirdService contractReviewProductThirdService;
    private final ContractProductSnService contractProductSnService;
    private final RemoteContractProductOwnService remoteContractProductOwnService;
    private final RemoteContractProductThirdService remoteContractProductThirdService;
    private final RemoteContractReviewService remoteContractReviewService;
    private final ProcessFileInfoService processFileInfoService;
    private final ICostFilingService costFilingService;
    private final ContractReviewDeliveryService contractReviewDeliveryService;
    private final ThreeProcurementProductLineReviewMainService threeProcurementProductLineReviewMainService;
    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    private final ThreeProcurementSmReviewMainService threeProcurementSmReviewMainService;
    private final RemoteContractOriginalDocumentService remoteContractOriginalDocumentService;
    private final RemoteContractUnconfirmedDetailService remoteContractUnconfirmedDetailService;
    private final ContractReviewSpecialCodeService contractReviewSpecialCodeService;
    private final SealApplicationReturnOrExchangeProcessService sealApplicationReturnOrExchangeProcessService;
    private final RemoteContractReviewConfigService remoteContractReviewConfigService;
    private final RemoteFsmDocService remoteFsmDocService;
    private final CostTransferMinusProcessService costTransferMinusProcessService;
    private final RemoteProjectProductOwnClient projectProductOwnClient;

    public final static String node00Id="__initiator__";
    public final static String node01Id="sid-B30196D5-B68C-4024-95EB-6B22BF35B240";
    public final static String node02Id="reviewReturnAndExchange_02";
    public final static String node03Id="reviewReturnAndExchange_03";
    public final static String node03FId="reviewReturnAndExchange_03F";
    public final static String node04Id="reviewReturnAndExchange_04";

    @Override
    protected void preProcess(ContractReviewReturnExchangeLaunchDTO launchable) {
        // 合同号
        String contractNumber = launchable.getContractNumber();
        List<ContractReviewMainBaseInfoDTO> contractReviews = mainService.getByContractNumber(contractNumber);
        // 随机取一个
        if (CollectionUtils.isEmpty(contractReviews)) {
            throw new CrmException("合同号不存在");
        }
        ContractReviewMainBaseInfoDTO baseInfoDTO = contractReviews.get(0);

        String projectId = baseInfoDTO.getProjectId();
        launchable.setProjectId(StringUtils.firstNonBlank(launchable.getProjectId(),projectId));
    }

    @Override
    protected void edit00Process(ContractReviewReturnExchangeLaunchDTO launchable) {
        saveOrUpdateData(launchable.getProcessInstanceId(), launchable, true);
    }

    private String saveOrUpdateData(String processInstanceId, ContractReviewReturnExchangeLaunchDTO launchable, boolean update) {
        String contractNumber = launchable.getContractNumber();
        List<ContractReviewMainBaseInfoDTO> baseInfoDTOS = mainService.getByContractNumber(contractNumber);
        // 是否进货合同
        Boolean isImportantContractIn = baseInfoDTOS.get(0).getIsImportantContractIn();
        Integer returnReason = launchable.getReturnReason();
        Integer type = launchable.getType();
        String personId = UserInfoHolder.getCurrentPersonId();

        List<ReturnExchangeProductThird> beforeUpdateThird = Collections.emptyList();
        List<ReturnExchangeProduct> beforeUpdateOwn = Collections.emptyList();

        //1.产品----------------------
        // todo 没有校验保修期 原型上没有
        if (update) {
            // 先删除之前的
            contractReviewPaymentProvisionService.update(new LambdaUpdateWrapper<ContractReviewPaymentProvision>()
                    .eq(ContractReviewPaymentProvision::getReturnExchangeProcessInstanceId, processInstanceId)
                    .set(ContractReviewPaymentProvision::getDelFlag, 1));
            contractReviewRevenueRecognitionService.update(new LambdaUpdateWrapper<ContractReviewRevenueRecognition>()
                    .eq(ContractReviewRevenueRecognition::getReturnExchangeProcessInstanceId, processInstanceId)
                    .set(ContractReviewRevenueRecognition::getDelFlag, 1));
            // 旧产品直接全删 全新增 不然逻辑可能有问题
            returnExchangeProductService.update(new LambdaUpdateWrapper<ReturnExchangeProduct>()
                    .eq(ReturnExchangeProduct::getProcessInstanceId, processInstanceId)
                    .ne(ReturnExchangeProduct::getType, 4)
                    .set(ReturnExchangeProduct::getDelFlag, true));
            returnExchangeProductThirdService.update(new LambdaUpdateWrapper<ReturnExchangeProductThird>()
                    .eq(ReturnExchangeProductThird::getProcessInstanceId, processInstanceId)
                    .ne(ReturnExchangeProductThird::getType, 4)
                    .set(ReturnExchangeProductThird::getDelFlag, true));
            beforeUpdateThird = returnExchangeProductThirdService.listNewProductByProcessInstanceId(processInstanceId);
            beforeUpdateOwn = returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId);

            // 文件
            processFileInfoService.delete(processInstanceId, null);

            // 费用核算
            returnExchangeFeeMapper.hardDeleteByProcessInstanceId(processInstanceId);
        }

        // 1.1新自有产品
        // 新产品需要保存层级关系 父级ID 循环保存
        List<ReturnExchangeProductVO> newProduct= ListUtils.emptyIfNull(launchable.getNewProduct());
        List<ReturnExchangeProductVO> child = newProduct.stream().map(ReturnExchangeProductVO::getChild).filter(Objects::nonNull).flatMap(Collection::stream).toList();
        if (CollectionUtils.isNotEmpty(child)) {
            child.forEach(i -> {
                i.setRecordId(i.getId());
            });
            newProduct.addAll(child);
        }
        if (update) {
            // 三合一 id存在修改 id在数据库不存在新增 数据库存在前端没有的id 删除
            List<ReturnExchangeProduct> dbRows = returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId);
            Set<String> dbRowIds = ListUtils.emptyIfNull(dbRows).stream().map(ReturnExchangeProduct::getId).collect(Collectors.toSet());
            Set<String> webRowIds = ListUtils.emptyIfNull(newProduct).stream().map(ReturnExchangeProductVO::getId).collect(Collectors.toSet());

            // 1.不存在数据库的记录删除
            List<ReturnExchangeProduct> deleteRows = dbRows.stream()
                    .filter(item -> !webRowIds.contains(item.getId()))
                    .toList();
            deleteRows.forEach(deleteRow -> {
                returnExchangeProductService.update(new LambdaUpdateWrapper<ReturnExchangeProduct>()
                        .set(ReturnExchangeProduct::getDelFlag, 1)
                        .eq(ReturnExchangeProduct::getId, deleteRow.getId()));
                // todo 如果前端未删除收入确认条款 则这个地方需要删除付款条款
            });

            // 2. 新增产品
            List<ReturnExchangeProductVO> insertRows = newProduct.stream().filter(returnExchangeProductVO -> !dbRowIds.contains(returnExchangeProductVO.getId())).toList();
            saveNewProductOwn(insertRows, processInstanceId);

            // 3.修改产品
            List<ReturnExchangeProductVO> updateRows = newProduct.stream().filter(returnExchangeProductVO -> dbRowIds.contains(returnExchangeProductVO.getId())).toList();
            updateRows.forEach(returnExchangeProductVO -> {
                // 修改产品信息 根据id
                ReturnExchangeProduct returnExchangeProduct = ReturnExchangeConvertor.INSTANCE.toReturnExchangeProduct(returnExchangeProductVO);
                // 前端生成主键
                returnExchangeProduct.setType(4);
                returnExchangeProduct.setProcessInstanceId(processInstanceId);
                initPrice(returnExchangeProduct);
                returnExchangeProductMapper.updateById(returnExchangeProduct);
            });

        } else {
            // 新增 新自有产品
            saveNewProductOwn(newProduct, processInstanceId);
        }
        // 查出产品
        // 1.2 退产品要校验数量
        List<ReturnExchangeProduct> reProduct= ListUtils.emptyIfNull(ReturnExchangeConvertor
                .INSTANCE.toReturnExchangeProduct(launchable.getReProduct()));
        Set<String> recordIds = reProduct.stream().map(ReturnExchangeProduct::getRecordId).collect(Collectors.toSet());
        Map<String, List<ReturnExchangeProduct>> oldProductByRecordId;
        Map<String, ContractReviewProductOwn> contractProductByRecordId;
        if (CollectionUtils.isNotEmpty(recordIds)){
            // 这个地方是为了取已退数量 byRecordIdBatch
            List<ReturnExchangeProduct> byRecordIdBatch = returnExchangeProductService.getOldByRecordIdBatch(recordIds);
            List<ContractReviewProductOwn> contractReviewProductOwns = contractReviewProductOwnService.list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                    .in(ContractReviewProductOwn::getProjectProductOwnId, recordIds)
                    .gt(ContractReviewProductOwn::getProductNum, 0)
                    .eq(ContractReviewProductOwn::getDelFlag, 0));
            oldProductByRecordId = byRecordIdBatch.stream().collect(Collectors.groupingBy(ReturnExchangeProduct::getRecordId));
            contractProductByRecordId = contractReviewProductOwns.stream().collect(Collectors.toMap(ContractReviewProductOwn::getProjectProductOwnId, p -> p, (p1, p2) -> p1));
        } else {
            oldProductByRecordId = new HashMap<>();
            contractProductByRecordId = new HashMap<>();
        }
        reProduct.stream().peek(returnExchangeProduct -> {
            returnExchangeProduct.setId(null);
            returnExchangeProduct.setType(type);
            returnExchangeProduct.setProcessInstanceId(processInstanceId);
            initPrice(returnExchangeProduct);
        }).forEach(returnExchangeProduct -> {
            // 根据项目的产品行id 查合同 如果有退换货的 就是有负行
            // 这个地方直接去退换货中的
            List<ReturnExchangeProduct> list = oldProductByRecordId.get(returnExchangeProduct.getRecordId());
            // 要退的数量
            Integer quantity = returnExchangeProduct.getProductNum();
            // 已经退的数量
            Integer returnNum = list == null ? 0 : list.stream().mapToInt(ReturnExchangeProduct::getProductNum).sum();
            // 产品数量
            ContractReviewProductOwn own = contractProductByRecordId.get(returnExchangeProduct.getRecordId());
            long productNum = own == null ? 0 : own.getProductNum();
            if (quantity + returnNum > productNum) {
                String stuffCode = returnExchangeProduct.getStuffCode();
                throw new CrmException("[物料代码%s]退货总数量不能大于产品数量%s".formatted(stuffCode,productNum));
            }

            // 税率 直接取旧产品的
            if (own != null) {
                returnExchangeProduct.setTaxRate(own.getTaxRate());
            }

        });

        // 旧产品直接保存 修改的时候前面已经将之前的数据删了
        returnExchangeProductMapper.insert(reProduct);

        // 1.3 新第三方产品
        List<ReturnExchangeProductThirdVO> newProductThird = ListUtils.emptyIfNull(launchable.getNewThirdProduct());
        if (update) {
            // 三合一 id存在修改 id在数据库不存在新增 数据库存在前端没有的id 删除
            List<ReturnExchangeProductThird> dbRows = returnExchangeProductThirdService.listNewProductByProcessInstanceId(processInstanceId);
            Set<String> dbRowIds = ListUtils.emptyIfNull(dbRows).stream().map(ReturnExchangeProductThird::getId).collect(Collectors.toSet());
            Set<String> webRowIds = ListUtils.emptyIfNull(newProductThird).stream().map(ReturnExchangeProductThirdVO::getId).collect(Collectors.toSet());

            // 1.不存在数据库的记录删除
            List<ReturnExchangeProductThird> deleteRows = dbRows.stream()
                    .filter(item -> !webRowIds.contains(item.getId()))
                    .toList();
            deleteRows.forEach(deleteRow -> {
                returnExchangeProductThirdService.update(new LambdaUpdateWrapper<ReturnExchangeProductThird>()
                        .set(ReturnExchangeProductThird::getDelFlag, 1)
                        .eq(ReturnExchangeProductThird::getId, deleteRow.getId()));
                // todo 如果前端未删除收入确认条款 则这个地方需要删除付款条款
            });

            // 2.新增产品
            List<ReturnExchangeProductThirdVO> insertRows = newProductThird.stream().filter(returnExchangeProductThirdVO -> !dbRowIds.contains(returnExchangeProductThirdVO.getId())).toList();
            saveNewProductThird(insertRows, processInstanceId);

            // 3.修改产品
            List<ReturnExchangeProductThirdVO> updateRows = newProductThird.stream().filter(returnExchangeProductThirdVO -> dbRowIds.contains(returnExchangeProductThirdVO.getId())).toList();
            updateRows.forEach(returnExchangeProductThirdVO -> {
                // 修改产品信息 根据id
                ReturnExchangeProductThird returnExchangeProductThird = ReturnExchangeConvertor.INSTANCE.toReturnExchangeThird(returnExchangeProductThirdVO);
                // 前端生成主键
                returnExchangeProductThird.setType(4);
                returnExchangeProductThird.setProcessInstanceId(processInstanceId);
                initPriceThird(returnExchangeProductThird);
                returnExchangeProductThirdMapper.updateById(returnExchangeProductThird);
            });
        } else {
            saveNewProductThird(newProductThird, processInstanceId);
        }
        // 1.4 旧第三方产品
        List<ReturnExchangeProductThird> reProductThirds = ListUtils.emptyIfNull(ReturnExchangeConvertor.INSTANCE.toReturnExchangeThird(launchable.getReThirdProduct()));
        Set<String> recordIdThirds = reProductThirds.stream().map(ReturnExchangeProductThird::getRecordId).collect(Collectors.toSet());
        Map<String, List<ReturnExchangeProductThird>> oldProductThirdByRecordId;
        Map<String, ContractReviewProductThird> contractThirdProductByRecordId;
        if (CollectionUtils.isNotEmpty(recordIdThirds)){
            List<ReturnExchangeProductThird> byRecordIdBatch = returnExchangeProductThirdService.getOldByRecordIdBatch(recordIdThirds);
            List<ContractReviewProductThird> contractReviewProductThirds = contractReviewProductThirdService.list(new LambdaQueryWrapper<ContractReviewProductThird>()
                    .in(ContractReviewProductThird::getProjectProductThirdId, recordIdThirds)
                    .gt(ContractReviewProductThird::getProductNum, 0)
                    .eq(ContractReviewProductThird::getDelFlag, 0));
            oldProductThirdByRecordId = byRecordIdBatch.stream().collect(Collectors.groupingBy(ReturnExchangeProductThird::getRecordId));
            contractThirdProductByRecordId = contractReviewProductThirds.stream().collect(Collectors.toMap(ContractReviewProductThird::getProjectProductThirdId, p -> p, (p1, p2) -> p1));
        } else {
            oldProductThirdByRecordId = new HashMap<>();
            contractThirdProductByRecordId = new HashMap<>();
        }
        reProductThirds.stream().peek(returnExchangeProduct -> {
            returnExchangeProduct.setId(null);
            returnExchangeProduct.setType(type);
            returnExchangeProduct.setProcessInstanceId(processInstanceId);
            initPriceThird(returnExchangeProduct);
        }).forEach(returnExchangeProduct -> {
            List<ReturnExchangeProductThird> list = oldProductThirdByRecordId.get(returnExchangeProduct.getRecordId());
            // 根据项目的产品行id 查合同 如果有退换货的 就是有负行
            ContractReviewProductThird one = contractThirdProductByRecordId.get(returnExchangeProduct.getRecordId());
            // 要退的数量
            Integer quantity = returnExchangeProduct.getProductNum();
            // 还可以退的数量 这个productNum有负的 所以直接sum出来的就是可以退的
            Integer returnNum = list == null ? 0 : list.stream().mapToInt(ReturnExchangeProductThird::getProductNum).sum();
            long productNum = one == null ? 0 : one.getProductNum();
            if (quantity + returnNum > productNum) {
                String stuffCode = returnExchangeProduct.getStuffCode();
                throw new CrmException("[物料代码%s]退货总数量不能大于产品数量%s".formatted(stuffCode,productNum));
            }

            // 税率 直接取旧产品的
            if (one != null) {
                returnExchangeProduct.setTaxRate(one.getTaxRate());
            }
        });

        returnExchangeProductThirdMapper.insert(reProductThirds);

        // 产品校验
        ReturnValidator.validate(type, returnReason, launchable.getReProduct(), launchable.getNewProduct(), launchable.getReThirdProduct(), launchable.getNewThirdProduct());
        // 作废的校验要和合同里面比对
        if (type == 3) {
            // 取合同中的自有产品、第三方产品
            List<CrmContractProductOwnVO> ownVOS = contractExecuteService.getProductOwnByContractNumber(contractNumber).getObjEntity();
            if (CollectionUtils.isNotEmpty(ownVOS)) {
                Map<String, List<ContractProductSnVO>> sn;
                if (isImportantContractIn) {
                    sn = contractReviewReturnExchangeService.getImportantSnMapByContractNumber(contractNumber);

                } else {
                    // 取out表
                    sn = contractExecuteService.getSnMapByContractNumber(contractNumber).getObjEntity();
                }
                Set<String> snInContract = MapUtils.emptyIfNull(sn).values().stream().flatMap(List::stream).map(ContractProductSnVO::getSn).collect(Collectors.toSet());
                Set<String> ownInContract = ownVOS.stream().map(CrmContractProductOwnVO::getProjectProductOwnId).collect(Collectors.toSet());
                // 判断前端传过来的旧产品和序列号
                Set<String> ownInWeb = reProduct.stream().map(ReturnExchangeProduct::getRecordId).collect(Collectors.toSet());
                Set<String> snInWeb = reProduct.stream().map(ReturnExchangeProduct::getPsn).flatMap(List::stream).map(CrmProjectProductSnVO::getPsn).collect(Collectors.toSet());
                if (!snInContract.equals(snInWeb) || !ownInContract.equals(ownInWeb)) {
                    throw new CrmException("合同产品与产品序列号不匹配");
                }
            }

            List<CrmContractProductThirdVO> thirdVOS = contractExecuteService.getProductThirdByContractNumber(contractNumber).getObjEntity();
            if (CollectionUtils.isNotEmpty(thirdVOS)) {
                Set<String> thirdInContract = thirdVOS.stream().map(CrmContractProductThirdVO::getProjectProductThirdId).collect(Collectors.toSet());
                Set<String> thirdInWeb = reProductThirds.stream().map(ReturnExchangeProductThird::getRecordId).collect(Collectors.toSet());
                if (!thirdInContract.equals(thirdInWeb)) {
                    throw new CrmException("必须选择合同中全部产品");
                }
            }

        }

        // 2 付款条款
        // 把新产品查出来 这个里面有层级关系
        List<ReturnExchangeProduct> newProductList = returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId);
        List<ReturnExchangeProductThird> newProductThirdList = returnExchangeProductThirdService.listNewProductByProcessInstanceId(processInstanceId);
        // 新产品总额
        BigDecimal newProductAmount = PriceUtils.getReturnExchangeTotalPrice(newProductList, newProductThirdList);
        // 旧产品总额
        BigDecimal oldProductAmount = returnExchangeProductService.getOldProductAmount(processInstanceId);
        //1、当“换货价差”≠0时显示且必填，为0时隐藏此项； 不允许添加付款条款
        BigDecimal subtract = newProductAmount.subtract(oldProductAmount);
        if (subtract.compareTo(BigDecimal.ZERO) != 0 && CollectionUtils.isEmpty(launchable.getPaymentClause())) {
            // 添加付款条款
            throw new CrmException("退货产品与新产品总金额存在差额，请正确填写【付款条款】信息！");
        }
        //2、付款条款总额应等于换货差额；
        List<ReturnExchangePaymentClauseVO> paymentClause = launchable.getPaymentClause();
        BigDecimal paymentTotal = ListUtils.emptyIfNull(paymentClause).stream().map(ReturnExchangePaymentClauseVO::getShouldPayMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (paymentTotal.compareTo(subtract) != 0) {
            // 添加付款条款
            throw new CrmException("付款条款总额应等于换货差额");
        }
        //3、条款类型默认为有明确条款，不可修改
        List<ReturnExchangePaymentClauseVO> list = ListUtils.emptyIfNull(paymentClause).stream().filter(item -> item.getPaymentType() == null || item.getPaymentType() != 0).toList();
        if (CollectionUtils.isNotEmpty(list)){
            throw new CrmException("条款类型默认为有明确条款，不可修改");
        }
        // 写合同的表 把退换货的processInstanceId 存进去

        List<ContractReviewPaymentProvision> contractReviewPaymentProvisions = ReturnExchangeConvertor.INSTANCE.returnExchangePaymentToContractPayment(paymentClause);
        ListUtils.emptyIfNull(contractReviewPaymentProvisions).forEach(item -> {
            item.setReturnExchangeProcessInstanceId(processInstanceId);
        });
        contractReviewPaymentProvisionService.saveBatch(contractReviewPaymentProvisions);
        // 3.收入确认条款 直接保存合同的
        List<ReturnExchangeRevenueRecognitionVO> revenueRecognition = launchable.getRevenueRecognition();
        // 当存在新产品，提交时需要校验是否填写【收入确认条款】信息，以及填写的数量与新产品数量是否一致 ，否则给予报错提示：“请正确填写【收入确认条款】信息，确保填写条款的产品数量与新产品数量一致！”
        if (CollectionUtils.isNotEmpty(newProductList) || CollectionUtils.isNotEmpty(newProductThirdList)) {
            // 校验每个产品都有一个默认的收入确认条款
            if (CollectionUtils.isEmpty(revenueRecognition)) {
                throw new CrmException("请正确填写【收入确认条款】信息，确保填写条款的产品数量与新产品数量一致！");
            }
            List<String> productOwnIds = new ArrayList<>();
            List<String> productThirdIds = new ArrayList<>();
            revenueRecognition.forEach(item -> {
                if (item.getRecordOwnId() != null) {
                    productOwnIds.add(item.getRecordOwnId());
                }
                if (item.getRecordThirdId() != null) {
                    productThirdIds.add(item.getRecordThirdId());
                }
            });
            Map<String, Long> countByOwnId = CollectionUtils.isEmpty(productOwnIds) ? Collections.emptyMap() : productOwnIds.stream().collect(Collectors.groupingBy(item -> item, Collectors.counting()));
            Map<String, Long> countByThirdId = CollectionUtils.isEmpty(productThirdIds) ? Collections.emptyMap() : productThirdIds.stream().collect(Collectors.groupingBy(item -> item, Collectors.counting()));
            if (CollectionUtils.isNotEmpty(newProductList)) {
                newProductList.forEach(item -> {
                    String id = item.getId();
                    // 判断在productOwnIds 是不是有切仅有一个
                    Long l = countByOwnId.get(id);
                    if (l == null || l > 1) {
                        throw new CrmException("请正确填写【收入确认条款】信息，确保填写条款的产品数量与新产品数量一致！");
                    }
                });

                newProductThirdList.forEach(item -> {
                    String id = item.getId();
                    Long l = countByThirdId.get(id);
                    if (l == null || l > 1) {
                        throw new CrmException("请正确填写【收入确认条款】信息，确保填写条款的产品数量与新产品数量一致！");
                    }
                });
            }
        }
        // 转成合同的收入确认条款 并且把退换货的产品id存进去 方便后续生效、回滚
        List<ContractReviewRevenueRecognition> contractReviewRevenueRecognitions = ReturnExchangeConvertor.INSTANCE.returnExchangeRevenueToContractRevenue(revenueRecognition);
        ListUtils.emptyIfNull(contractReviewRevenueRecognitions).forEach(item -> {
            item.setReturnExchangeProcessInstanceId(processInstanceId);
        });
        // 旧产品的收入确认条款要和合同的保持一致
        saveOldProductOwnRecognition(reProduct, contractProductByRecordId, processInstanceId);
        saveOldProductThirdRecognition(reProductThirds, contractThirdProductByRecordId, processInstanceId);
        contractReviewRevenueRecognitionService.saveBatch(contractReviewRevenueRecognitions);

        // 5.费用核算---------------------
        // 退换货原因是【未发货换货】或【未发货退货】 没有费用核算
        if (!(returnReason == 5 || returnReason == 10)){
            List<ReturnExchangeFee> fees = returnExchangeFeeService.listByContractNumber(reProduct,contractNumber);
            returnExchangeFeeMapper.insert(fees);

        }

        //附件-----
        List<FileInput> attachments = launchable.getAttachments();
        processFileInfoService.uploadProcessFileByFileId(new ProcessFileInput(processInstanceId, attachments));

        // 6.主表----------------------
        ContractReviewReturnExchange item = contractReviewReturnExchangeMapper.selectOne(new LambdaQueryWrapper<ContractReviewReturnExchange>().eq(FlowBaseEntity::getProcessInstanceId, processInstanceId));
        List<ContractReviewMainBaseInfoDTO> byContractNumber = mainService.getByContractNumber(contractNumber);
        assert byContractNumber != null;
        CrmContractExecuteVO crmContractExecuteVO = contractExecuteService.getByContractNumber(contractNumber).getObjEntity();
        if (item == null) {

            item = ReturnExchangeConvertor.INSTANCE
                    .toContractReturnExchange(launchable,crmContractExecuteVO);
        } else {
            String id = item.getId();
            item = ReturnExchangeConvertor.INSTANCE
                    .toContractReturnExchange(launchable,crmContractExecuteVO);
            item.setId(id);
        }
        ContractReviewMainBaseInfoDTO contractReviewMain = byContractNumber.get(0);
        item.setContractId(byContractNumber.get(0).getId());
        contractReviewReturnExchangeMapper.insertOrUpdate(item);

        // 重点行业合同 判断只让选择重点行业序列号 并且签订公司要一致
        Boolean isImportantContract = contractReviewMain.getIsImportantContract();
        if (isImportantContract != null && isImportantContract && CollectionUtils.isNotEmpty(newProductList)) {
            List<CrmProjectProductSnVO> snVos = newProductList.stream().flatMap(i -> i.getPsn().stream()).toList();
            // 必须是重点行业的序列号
            boolean b = snVos.stream().allMatch(sn -> ContractProductSnVO.IMPORTANT_CONTRACT.equals(sn.getType()));
            if (!b) {
                throw new CrmException("重点行业退换货合同,新产品只允许关联重点行业序列号");
            }

        }
        // 是否有费用备案
        Boolean hasCostFiling = costFilingService.getInfoByContractNumber(contractNumber);
        // 如果有 要提前取出产品的外包服务费 避免负行导致外包服务费有问题
        BigDecimal splitOutsource = BigDecimal.ZERO;
        if (hasCostFiling != null && hasCostFiling) {
            List<CrmProjectProductOwnVO> projectProductOwnVOS = projectProductOwnClient.fillSplitOutsourcePriceByRecordIds(contractReviewMain.getProjectId(), recordIds).getObjEntity();
            splitOutsource = ListUtils.emptyIfNull(projectProductOwnVOS).stream().map(CrmProjectProductOwnVO::getSplitOutsourcePrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 4.合同发货 直接保存合同的

        List<PerformanceReportContractDeliveryDTO> contractDeliveryList = launchable.getContractDeliveryList();
        if (CollectionUtils.isNotEmpty(contractDeliveryList) && !update) {
            contractReviewDeliveryService.returnChangeLaunchSave(contractDeliveryList, item.getId());
        }

        if (update) {
            returnExchangeProductService.rollbackProjectProductOwn(beforeUpdateOwn, launchable.getProjectId());
            returnExchangeProductThirdService.rollbackProjectProductThird(beforeUpdateThird, launchable.getProjectId());
        }

        // 修改项目的产品
        returnExchangeProductService.updateProjectProductOwn(newProductList,launchable.getProjectId());
        returnExchangeProductThirdService.updateProjectProductThird(newProductThirdList, launchable.getProjectId());
        // 当合同存在外包服务费且新产品金额小于旧产品时，需校验合同未发起【费用合同】流程的外包服务费金额必须大于或等于【（旧产品金额-新产品金额）/新合同金额*项目外包服务费之和】，否则，不允许发起退换货，给予报错提示：“该合同未支付的外包服务费不足以抵扣本次退换货费用调减，不可发起退换货，如有任何疑问请联系合同管理部！”
        if (hasCostFiling != null && hasCostFiling) {
            MinusMoneyQuery moneyQuery = new MinusMoneyQuery();
            moneyQuery.setContractNumber(contractNumber);
            moneyQuery.setProjectId(contractReviewMain.getProjectId());
            // 合同金额
            BigDecimal contractAmount = crmContractExecuteVO.getContractAmount();
            // 合同金额+新产品金额-旧产品金额
            BigDecimal afterReturnAmount = contractAmount.add(newProductAmount).subtract(oldProductAmount);
            moneyQuery.setBeforeRefundMoney(contractAmount);
            moneyQuery.setAfterRefundMoney(afterReturnAmount);
            switch (type) {
                case 1:
                    moneyQuery.setContractScene(1);
                    break;
                case 2:
                    moneyQuery.setContractScene(0);
                    break;
            }
            moneyQuery.setOutsourcePrice(splitOutsource);

            if (type != 3 && !costFilingService.getIsCostCondition(moneyQuery)) {
                throw new CrmException("该合同未支付的外包服务费不足以抵扣本次退换货费用调减，不可发起退换货，如有任何疑问请联系合同管理部！");
            }
        }
        if (!update) {
            // 发起的时候发起采购
            // 发起的时候才发这两个流程 如果是00发01 不处理这个
            // 6.发起产品线采购和sm采购
            CrmProjectDirectlyVo projectDirectlyVo = Optional.ofNullable(remoteProjectDirectlyClient.getProjectInfo(contractReviewMain.getProjectId()))
                    .map(JsonObject::getObjEntity).orElse(new CrmProjectDirectlyVo());
            if (!CollectionUtils.isEmpty(newProductList)) {
                // 自有产品的产品id集合
                List<String> ownProductIds = newProductList.stream().map(ReturnExchangeProduct::getProductId).toList();
                List<String> stuffCodes = newProductList.stream().map(ReturnExchangeProduct::getStuffCode).toList();
                // 自有产品不为空 发起产品线采购
                launchProductLine(contractReviewMain.getId(), contractReviewMain.getProjectId(), ownProductIds, stuffCodes, contractReviewMain.getContractNumber(), personId, processInstanceId);
            }
            // 发起sm采购 第三方产品不为空
            if (!CollectionUtils.isEmpty(newProductThirdList) && projectDirectlyVo.getSmType() != null && projectDirectlyVo.getSmType().equals(1)) {
                launchSmReview(contractReviewMain.getId(), contractReviewMain.getProjectId(), contractReviewMain.getContractNumber(), personId, processInstanceId);
            }
            // 发起费用调减
            if (type != 3) {
                try {
                    if (costFilingService.getIsCostFiling(contractReviewMain.getContractNumber())) {
                        launchCostTransferMinus(contractReviewMain.getProjectId(), contractReviewMain.getId(), contractReviewMain.getContractNumber(), processInstanceId, type, crmContractExecuteVO, newProductAmount, oldProductAmount, splitOutsource);
                    }
                } catch (Exception e) {
                    log.error("发起费用调减失败,{}", e);
                }
            }
        }
        return item.getId();
    }

    private void saveNewProductOwn(List<ReturnExchangeProductVO> newProduct, String processInstanceId) {
        // 新增 新自有产品
        for (ReturnExchangeProductVO returnExchangeProductVO : newProduct) {
            ReturnExchangeProduct returnExchangeProduct = ReturnExchangeConvertor.INSTANCE.toReturnExchangeProduct(returnExchangeProductVO);
            // 前端生成主键
            returnExchangeProduct.setType(4);
            returnExchangeProduct.setProcessInstanceId(processInstanceId);
            initPrice(returnExchangeProduct);
            returnExchangeProductMapper.insert(returnExchangeProduct);
        }
    }

    private void saveNewProductThird(List<ReturnExchangeProductThirdVO> newProductThird, String processInstanceId){
        for (ReturnExchangeProductThirdVO returnExchangeProductThirdVO : newProductThird) {
            ReturnExchangeProductThird returnExchangeThird = ReturnExchangeConvertor.INSTANCE.toReturnExchangeThird(returnExchangeProductThirdVO);
            // 生成主键
            returnExchangeThird.setProcessInstanceId(processInstanceId);
            returnExchangeThird.setType(4);
            initPriceThird(returnExchangeThird);
            returnExchangeProductThirdMapper.insert(returnExchangeThird);
        }
    }

    private void initPrice(ReturnExchangeProduct returnExchangeProduct){
        BigDecimal dealPrice = returnExchangeProduct.getDealPrice();
        BigDecimal finalPrice = returnExchangeProduct.getFinalPrice();
        BigDecimal quotedPrice = returnExchangeProduct.getQuotedPrice();
        Integer productNum = returnExchangeProduct.getProductNum();
        returnExchangeProduct.setDealTotalPrice(dealPrice.multiply(BigDecimal.valueOf(productNum)));
        returnExchangeProduct.setFinalTotalPrice(finalPrice.multiply(BigDecimal.valueOf(productNum)));
        returnExchangeProduct.setQuotedTotalPrice(quotedPrice.multiply(BigDecimal.valueOf(productNum)));
    }

    private void initPriceThird(ReturnExchangeProductThird returnExchangeProductThird) {
        BigDecimal dealPrice = returnExchangeProductThird.getDealPrice();
        BigDecimal purchasePrice = returnExchangeProductThird.getPurchasePrice();
        BigDecimal finalPrice = returnExchangeProductThird.getFinalPrice();
        Integer productNum = returnExchangeProductThird.getProductNum();
        returnExchangeProductThird.setPurchaseTotalPrice(purchasePrice.multiply(BigDecimal.valueOf(productNum)));
        returnExchangeProductThird.setDealTotalPrice(dealPrice.multiply(BigDecimal.valueOf(productNum)));
        returnExchangeProductThird.setFinalTotalPrice(finalPrice.multiply(BigDecimal.valueOf(productNum)));
    }

    private void saveOldProductOwnRecognition(List<ReturnExchangeProduct> reProduct, Map<String, ContractReviewProductOwn> contractProductByRecordId, String processInstanceId) {
        // 旧产品的收入确认条款要和合同的保持一致
        if (CollectionUtils.isNotEmpty(reProduct)) {
            List<String> contractProductIds = new ArrayList<>();
            reProduct.forEach(item -> {
                String recordId = item.getRecordId();
                ContractReviewProductOwn contractReviewProductOwn = contractProductByRecordId.get(recordId);
                String id = contractReviewProductOwn.getId();
                contractProductIds.add(id);
            });
            List<ContractReviewRevenueRecognition> recognitions = contractReviewRevenueRecognitionService.list(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                    .in(ContractReviewRevenueRecognition::getProductOwnId, contractProductIds)
                    .eq(ContractReviewRevenueRecognition::getDefaultConfirm, 1)
                    .eq(ContractReviewRevenueRecognition::getDelFlag, 0));
            Map<String, ContractReviewRevenueRecognition> recognitionMap = ListUtils.emptyIfNull(recognitions).stream().collect(Collectors.toMap(ContractReviewRevenueRecognition::getProductOwnId, item -> item));
            reProduct.forEach(item -> {
                String recordId = item.getRecordId();
                ContractReviewProductOwn contractReviewProductOwn = contractProductByRecordId.get(recordId);
                String id = contractReviewProductOwn.getId();
                ContractReviewRevenueRecognition recognition = recognitionMap.get(id);
                if (recognition != null) {
                    // 循环插入
                    recognition.setId(null);
                    recognition.setReturnExchangeProcessInstanceId(processInstanceId);
                    recognition.setProductOwnId(null);
                    recognition.setContractReviewMainId(null);
                    // 退换货产品的id
                    recognition.setReturnExchangeOwnId(item.getId());
                    contractReviewRevenueRecognitionService.save(recognition);
                }
            });
        }
    }

    private void saveOldProductThirdRecognition(List<ReturnExchangeProductThird> reProductThirds, Map<String, ContractReviewProductThird> contractThirdProductByRecordId, String processInstanceId) {
        if (CollectionUtils.isNotEmpty(reProductThirds)) {
            List<String> contractProductIds = new ArrayList<>();
            reProductThirds.forEach(item -> {
                String recordId = item.getRecordId();
                ContractReviewProductThird contractReviewProductThird = contractThirdProductByRecordId.get(recordId);
                String id = contractReviewProductThird.getId();
                contractProductIds.add(id);
            });
            List<ContractReviewRevenueRecognition> recognitions = contractReviewRevenueRecognitionService.list(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                    .in(ContractReviewRevenueRecognition::getProductThirdId, contractProductIds)
                    .eq(ContractReviewRevenueRecognition::getDefaultConfirm, 1)
                    .eq(ContractReviewRevenueRecognition::getDelFlag, 0));
            Map<String, ContractReviewRevenueRecognition> recognitionMap = ListUtils.emptyIfNull(recognitions).stream().collect(Collectors.toMap(ContractReviewRevenueRecognition::getProductThirdId, item -> item));
            reProductThirds.forEach(item -> {
                String recordId = item.getRecordId();
                ContractReviewProductThird contractReviewProductThird = contractThirdProductByRecordId.get(recordId);
                String id = contractReviewProductThird.getId();
                ContractReviewRevenueRecognition recognition = recognitionMap.get(id);
                if (recognition != null) {
                    // 循环插入
                    recognition.setId(null);
                    recognition.setReturnExchangeProcessInstanceId(processInstanceId);
                    recognition.setProductThirdId(null);
                    recognition.setContractReviewMainId(null);
                    // 退换货产品的id
                    recognition.setReturnExchangeThirdId(item.getId());
                    contractReviewRevenueRecognitionService.save(recognition);
                }
            });
        }
    }

    private void launchProductLine(String contractId, String projectId, List<String> projectProductIds, List<String> stuffCodes, String contractNumber, String personId, String processInstanceId) {
        ThreeProcurementProductLineReviewLaunchVo lineReviewLaunchVo = new ThreeProcurementProductLineReviewLaunchVo();
        ThreeProcurementProductLineReviewLaunchVo.ThreeProcurementProductLineBaseInfoLaunchDto lineReviewBaseInfo = new ThreeProcurementProductLineReviewLaunchVo.ThreeProcurementProductLineBaseInfoLaunchDto();
        lineReviewBaseInfo.setContractId(contractId);
        lineReviewBaseInfo.setProjectId(projectId);
        lineReviewBaseInfo.setProductIds(projectProductIds);
        lineReviewBaseInfo.setStuffCodes(stuffCodes);
        lineReviewBaseInfo.setContractNumber(contractNumber);
        lineReviewBaseInfo.setReturnAndExchangeProcessId(processInstanceId);
        lineReviewBaseInfo.setReturnAndExchange(true);
        lineReviewLaunchVo.setBaseInfo(lineReviewBaseInfo);
        lineReviewLaunchVo.setCreatorPersonId(personId);
        threeProcurementProductLineReviewMainService.launch(lineReviewLaunchVo);
    }

    private void launchSmReview(String contractId, String projectId, String contractNumber, String personId, String processInstanceId) {
        ThreeProcurementSmReviewLaunchVo smReviewLaunchVo = new ThreeProcurementSmReviewLaunchVo();
        ThreeProcurementSmReviewLaunchVo.ThreeProcurementSmBaseInfoLaunchDto smBaseInfo = new ThreeProcurementSmReviewLaunchVo.ThreeProcurementSmBaseInfoLaunchDto();
        smBaseInfo.setContractId(contractId);
        smBaseInfo.setProjectId(projectId);
        smBaseInfo.setContractNumber(contractNumber);
        smBaseInfo.setReturnAndExchange(true);
        smBaseInfo.setReturnAndExchangeProcessId(processInstanceId);
        smReviewLaunchVo.setBaseInfo(smBaseInfo);
        smReviewLaunchVo.setCreatorPersonId(personId);
        threeProcurementSmReviewMainService.launch(smReviewLaunchVo);
    }

    private void launchCostTransferMinus(String projectId,
                                         String contractId,
                                         String contractNumber,
                                         String processInstanceId,
                                         Integer type,
                                         CrmContractExecuteVO contractExecuteVO,
                                         BigDecimal newProduct,
                                         BigDecimal oldProduct,
                                         BigDecimal splitOutPrice) {
        CostTransferMinusFlowLaunchDTO flowLaunchDTO = new CostTransferMinusFlowLaunchDTO();
        flowLaunchDTO.setCreatorPersonId("b051b2e8a22aaa814cd0ad384bffa318");
        CostTransferMinusFlowLaunchDTO.CostTransferMinusBaseInfoLaunchDTO baseInfoLaunchDTO = new CostTransferMinusFlowLaunchDTO.CostTransferMinusBaseInfoLaunchDTO();
        baseInfoLaunchDTO.setProjectId(projectId);
        baseInfoLaunchDTO.setContractId(contractId);
        baseInfoLaunchDTO.setContractNumber(contractNumber);
        baseInfoLaunchDTO.setRefundProcessInstanceId(processInstanceId);
        switch (type) {
            case 1:
                baseInfoLaunchDTO.setContractScene(1);
                break;
            case 2:
                baseInfoLaunchDTO.setContractScene(0);
                break;
        }
        baseInfoLaunchDTO.setOutsourcePrice(splitOutPrice);
        // 这个地方多查了一遍
        BigDecimal contractAmount = contractExecuteVO.getContractAmount();
        // 取本次退换货的金额
        BigDecimal after = contractAmount.add(newProduct).subtract(oldProduct);
        baseInfoLaunchDTO.setBeforeRefundMoney(contractAmount);
        baseInfoLaunchDTO.setAfterRefundMoney(after);
        flowLaunchDTO.setBaseInfo(baseInfoLaunchDTO);
        JsonObject<List<String>> result = tfsNodeClient.selectSpecifyNodeAssigneeList(costTransferMinusProcessService.processType().getProcessDefinitionKey().getValue(), "sid-E6891D98-B3C1-4BFC-A297-00BA45E4222E");
        if (result.isSuccess()) {
            flowLaunchDTO.setAssigneeList(new HashSet<>(result.getObjEntity()));
        }
        costTransferMinusProcessService.launch(flowLaunchDTO);
    }


    private void launchSealApplication(String processInstanceId, ContractReviewReturnExchange returnExchangeBaseVO){
        // 退换货印鉴
        SealApplicationFlowLaunchDTO flowLaunchDTO = new SealApplicationFlowLaunchDTO();
        SealApplicationFlowLaunchDTO.SealApplicationBeanInfoLaunchDTO base = new SealApplicationFlowLaunchDTO.SealApplicationBeanInfoLaunchDTO();
        flowLaunchDTO.setCreatorPersonId("b051b2e8a22aaa814cd0ad384bffa318");
        flowLaunchDTO.setParentProcessInstanceId(processInstanceId);
        base.setSignCompanyId(returnExchangeBaseVO.getSignCompanyId());
        base.setParentProcessInstanceId(processInstanceId);
        ContractSignCompanyVO signCompanyVO = remoteContractReviewConfigService.getBySignCompanyId(returnExchangeBaseVO.getSignCompanyId()).getObjEntity();
        base.setSignCompanyNameShort(signCompanyVO == null ? "" : signCompanyVO.getCompanyNameShort());


        ApplySealTypeInfoVO applySealTypeInfoVO = new ApplySealTypeInfoVO();
        applySealTypeInfoVO.setSignCompanyId(returnExchangeBaseVO.getSignCompanyId());
        applySealTypeInfoVO.setSignCompanyName(signCompanyVO == null ? "" : signCompanyVO.getCompanyName());
        applySealTypeInfoVO.setSignCompanyNameShort(signCompanyVO == null ? "" : signCompanyVO.getCompanyNameShort());
        ApplySealTypeInfoVO.SealTypeVO sealTypeVO = new ApplySealTypeInfoVO.SealTypeVO();
        sealTypeVO.setSealType("合同章");
        applySealTypeInfoVO.setSealTypeVOS(List.of(sealTypeVO));
        base.setApplySealType(List.of(applySealTypeInfoVO));
        // 退换货的终稿
        List<String> finalDocId =new ArrayList<>(SetUtils.emptyIfNull(returnExchangeBaseVO.getFinalDocId())) ;
        Map<String, CrmFsmDoc> docMap = ListUtils.emptyIfNull(remoteFsmDocService.batchSelectByDocId(finalDocId).getObjEntity()).stream().collect(Collectors.toMap(CrmFsmDoc::getDocId, v -> v));
        List<ProcessFileInfoVO> processFileInfoVOS = processFileInfoService.queryByProcessInstanceId(processInstanceId, finalDocId, null);
        List<SealFileInfoVO> list = processFileInfoVOS.stream().map(item -> {
            SealFileInfoVO sealFileInfoVO = HyperBeanUtils.copyProperties(item, SealFileInfoVO::new);
            sealFileInfoVO.setFileName(item.getName());
            sealFileInfoVO.setArchivedFileType(1);
            String extendInfo = item.getExtendInfo();
            JSONObject jsonObject = JSON.parseObject(extendInfo);
            sealFileInfoVO.setDuplicate(jsonObject.getInteger("duplicate"));
            sealFileInfoVO.setCopies(jsonObject.getInteger("copies"));
            return sealFileInfoVO;
        }).toList();
        base.setFsmDocId(list);
        flowLaunchDTO.setBeanInfoLaunchDTO(base);

        sealApplicationReturnOrExchangeProcessService.launch(flowLaunchDTO);
    }


    @Override
    protected String afterEngineLaunch(ProcessExtensionInfo processInfo, ContractReviewReturnExchangeLaunchDTO launchable) {
        return saveOrUpdateData(processInfo.getProcessInstanceId(), launchable, false);
    }

    @Override
    public void handleProcessing(FlowStateInfoVo flowInfo) {
        // 流程审批中
        String processInstanceId = flowInfo.getProcessInstanceId();
        List<ReturnExchangeProduct> reProduct = returnExchangeProductService.listOldProductByProcessInstanceId(processInstanceId);
        List<ReturnExchangeProductThird> reProductThirds = returnExchangeProductThirdService.listOldProductByProcessInstanceId(processInstanceId);
        List<ReturnExchangeProduct> newProduct = returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId);
        List<ReturnExchangeProductThird> newProductThird = returnExchangeProductThirdService.listNewProductByProcessInstanceId(processInstanceId);
        String projectId = processExtensionInfoMapper.selectOne(new LambdaQueryWrapper<ProcessExtensionInfo>()
                .eq(ProcessExtensionInfo::getProcessInstanceId, processInstanceId).last("limit 1")
                .select(ProcessExtensionInfo::getProjectId)
        ).getProjectId();
        ContractReviewReturnExchange contractReviewReturnExchange = contractReviewReturnExchangeMapper.selectOne(new LambdaQueryWrapper<ContractReviewReturnExchange>()
                .eq(ContractReviewReturnExchange::getProcessInstanceId, processInstanceId)
                .last("limit 1"));
        String contractNumber = contractReviewReturnExchange.getContractNumber();
        String returnChangeId = contractReviewReturnExchange.getId();
        // 随机取一个id作为新产品的合同id
        String contractId = contractReviewReturnExchange.getContractId();
        ContractReviewMainBaseInfoDTO contractReviewMainBaseInfoDTO = mainService.getByContractId(contractId);
        String contractProcessInstanceIdNewProduct = contractReviewMainBaseInfoDTO.getProcessInstanceId();
        if (node00Id.equals(flowInfo.getCurrentActivityId())) {
            /**
             * 2、合同评审退换货00发01步时，退换货的产品需要写入项目中，写入规则如下：
             *      a.退货产品在项目中需要将对应数量做调减；
             *      b.换新产品在项目中需要增加对应产品行;
             *      c.公司项目中更新产品信息后，根据现有价审失效规则自动判段是否失效现有价审，当价审失效原因为【整单合同折扣变低】时，价审未办结，退换货流程无法进行审批，否则，价审流程不影响退换货流程审批（特别说明：价格审批中时，退换货流程不可编辑产品或删除流程）；
             */
            returnExchangeProductService.updateProjectProductOwn(reProduct,projectId);
            returnExchangeProductThirdService.updateProjectProductThird(reProductThirds, projectId);
        }

        if (node02Id.equals(flowInfo.getCurrentActivityId())) {
            // 发到03 写入特殊代码
            contractReviewReturnExchangeService.writeSpecialCode(processInstanceId);
        }

        if (node04Id.equals(flowInfo.getTargetActivityId()) && node03Id.equals(flowInfo.getCurrentActivityId())) {
            /**
             * 合同评审退换货03发04步时，退换货的产品需要写入合同中，写入规则如下：
             *      a. 在原合同评审中【产品列表】和【收入确认条款】生成负行数据并增加【退货】标记，合同评审中需要显示该标记；
             *      b.换新产品在合同评审中【产品列表】和【收入确认条款】需要同步增加的产品行，并增加【换新】标记，合同评审中需要显示该标记； 换新产品存在合同发货信息，也需要同步更新至合同评审中；
             *      c. 进行【未确认明细】数据汇总时，负行数据需与原产品行数据进行计算后再更新未确认明细信息；
             *      d.合同执行退换货产品信息及退换货流程信息在退换货流程03步办结后才能显示；
             * 4、合同评审退换货流程被删除，需要还原写入项目及合同中的产品行数据及【未确认明细】数据；
             * 5、合同评审退换货流程办结后，如果退换货协议选择【是】且存在确认的终稿，需要自动在合同原件列表生成【上交原件待办】同时自动触发【印鉴流程】，详见【退换货印鉴申请】；
             */
            Map<String, String> pidByCid = new HashMap<>();
            // key-退换货产品id  value 合同产品id
            Map<String, String> ownMap = new HashMap<>();
            // key-退换货产品id  value 合同产品id
            Map<String, String> thirdMap = new HashMap<>();
            // 1.将退换货中的产品写到合同里面
            // 1.1旧产品
            if (CollectionUtils.isNotEmpty(reProduct)) {
                List<String> recordIds = reProduct.stream().map(ReturnExchangeProduct::getRecordId).collect(Collectors.toList());
                List<ContractReviewProductOwn> contractReviewProductOwns = contractReviewProductOwnMapper.selectList(new LambdaQueryWrapper<ContractReviewProductOwn>()
                        .in(ContractReviewProductOwn::getProjectProductOwnId, recordIds)
                        .gt(ContractReviewProductOwn::getProductNum, 0)
                        .eq(ContractReviewProductOwn::getDelFlag, false));
                Map<String, ContractReviewProductOwn> ownByRecordId = contractReviewProductOwns.stream().collect(Collectors.toMap(ContractReviewProductOwn::getProjectProductOwnId, v -> v, (newV, oldV) -> newV));
                // 退的产品

                List<ContractReviewProductOwn> reContractOwn = new ArrayList<>();
                reProduct.forEach(re -> {
                    // flow服务 产品表
                    ContractReviewProductOwn parentOwn = ownByRecordId.get(re.getRecordId());
                    ContractReviewProductOwn own = new ContractReviewProductOwn();
                    own.setProjectProductOwnId(re.getRecordId());
                    own.setProductId(re.getProductId());
                    own.setProjectId(parentOwn.getProjectId());
                    own.setContractReviewId(parentOwn.getContractReviewId());
                    own.setStuffCode(re.getStuffCode());
                    own.setRowType(parentOwn.getRowType());
                    own.setProductNum(re.getProductNum() * -1L);
                    own.setTaxRate(re.getTaxRate());
                    own.setProductPeriodStart(re.getProductPeriodStart());
                    own.setProductPeriodEnd(re.getProductPeriodEnd());
                    own.setValidDate(LocalDateTime.now());
                    own.setLabel("退货");
                    own.setBarterParentId(parentOwn.getId());
                    own.setPnCode(parentOwn.getPnCode());
                    own.setProductCategory(parentOwn.getProductCategory());
                    own.setReturnExchangeProcessInstanceId(processInstanceId);
                    own.setReturnExchangeProductId(re.getId());
                    contractReviewProductOwnMapper.insert(own);
                    // 拿到这个id
                    String contractReviewProductOwnId = own.getId();
                    // 退的产品 也要改收入确认条款
                    // 收入确认条款
                    contractReviewRevenueRecognitionService.update(new LambdaUpdateWrapper<ContractReviewRevenueRecognition>()
                            .eq(ContractReviewRevenueRecognition::getReturnExchangeOwnId, re.getId())
                            .eq(ContractReviewRevenueRecognition::getDelFlag, 0)
                            .set(ContractReviewRevenueRecognition::getProductOwnId, contractReviewProductOwnId)
                            .set(ContractReviewRevenueRecognition::getContractReviewMainId, contractId));
                });

                // 合同服务产品表
                List<ContractReviewProductOwn> reProductOwns = contractReviewProductOwnService.list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                        .eq(ContractReviewProductOwn::getReturnExchangeProcessInstanceId, processInstanceId)
                        .eq(ContractReviewProductOwn::getLabel, "退货")
                        .eq(ContractReviewProductOwn::getDelFlag, false));
                List<ContractProductOwnDTO> contractProductOwnDTOS = contractReviewProductOwnService.buildProductOwnTile(reProductOwns, "", true);
                // 可能存在多个合同评审的 先group by一下
                Map<String, List<ContractProductOwnDTO>> ownsByContractId = ListUtils.emptyIfNull(contractProductOwnDTOS).stream().collect(Collectors.groupingBy(ContractProductOwnDTO::getContractReviewId));

                MapUtils.emptyIfNull(ownsByContractId).forEach((contractReviewId, own) -> {
                    // 把contractReviewId 转换成 processInstanceId
                    String contractProcessInstanceId = pidByCid.get(contractReviewId);
                    if (contractProcessInstanceId == null) {
                        ContractReviewMainBaseInfoDTO byContractId = mainService.getByContractId(contractReviewId);
                        pidByCid.put(contractReviewId, byContractId.getProcessInstanceId());
                        contractProcessInstanceId = byContractId.getProcessInstanceId();
                    }
                    // 价格用单价乘以数量作为总价
                    postHandleOwnPrice(own);
                    remoteContractProductOwnService.saveTileBatch(contractProcessInstanceId, own);
                });

                // 旧产品序列号解绑
                List<String> contractProductRecordIds = contractReviewProductOwns.stream().map(ContractReviewProductOwn::getId).toList();
                contractProductSnService.unBind(processInstanceId, contractProductRecordIds);
                // todo  重点行业的进货的序列号 解绑 或者是改状态
                remoteContractReviewService.unBind(processInstanceId, contractProductRecordIds);
            }

            if (CollectionUtils.isNotEmpty(reProductThirds)) {
                List<String> recordIds = reProductThirds.stream().map(ReturnExchangeProductThird::getRecordId).collect(Collectors.toList());
                List<ContractReviewProductThird> contractReviewProductThirds = contractReviewProductThirdMapper.selectList(new LambdaQueryWrapper<ContractReviewProductThird>()
                        .in(ContractReviewProductThird::getProjectProductThirdId, recordIds)
                        .gt(ContractReviewProductThird::getProductNum, 0)
                        .eq(ContractReviewProductThird::getDelFlag, false));
                Map<String, ContractReviewProductThird> thirdByRecordId = contractReviewProductThirds.stream().collect(Collectors.toMap(ContractReviewProductThird::getProjectProductThirdId, v -> v, (newV, oldV) -> newV));
                reProductThirds.forEach(re -> {
                    ContractReviewProductThird parentThird = thirdByRecordId.get(re.getRecordId());
                    ContractReviewProductThird third = new ContractReviewProductThird();
                    third.setProjectProductThirdId(re.getRecordId());
                    third.setProjectId(parentThird.getProjectId());
                    third.setProductId(re.getProductId());
                    third.setContractReviewId(parentThird.getContractReviewId());
                    third.setStuffCode(re.getStuffCode());
                    third.setTaxRate(re.getTaxRate());
                    third.setProductNum(re.getProductNum() * -1L);
                    third.setSupplierId(parentThird.getSupplierId());
                    third.setSupplierName(parentThird.getSupplierName());
                    third.setPurchasePrice(parentThird.getPurchasePrice());
                    third.setBarterParentId(parentThird.getId());
                    third.setProductPeriodStart(parentThird.getProductPeriodStart());
                    third.setProductPeriodEnd(parentThird.getProductPeriodEnd());
                    third.setLabel("退货");
                    third.setReturnExchangeProcessInstanceId(processInstanceId);
                    third.setReturnExchangeProductId(re.getId());
                    contractReviewProductThirdMapper.insert(third);
                    // 拿到这个id
                    String contractReviewProductThirdId = third.getId();
                    // 收入确认条款
                    contractReviewRevenueRecognitionService.update(new LambdaUpdateWrapper<ContractReviewRevenueRecognition>()
                            .eq(ContractReviewRevenueRecognition::getReturnExchangeThirdId, re.getId())
                            .eq(ContractReviewRevenueRecognition::getDelFlag, 0)
                            .set(ContractReviewRevenueRecognition::getReturnExchangeThirdId, contractReviewProductThirdId)
                            .set(ContractReviewRevenueRecognition::getContractReviewMainId, contractId));
                });

                // 合同服务产品
                List<ContractReviewProductThird> reContractProductThirds = contractReviewProductThirdMapper.selectList(new LambdaQueryWrapper<ContractReviewProductThird>()
                        .eq(ContractReviewProductThird::getReturnExchangeProcessInstanceId, processInstanceId)
                        .eq(ContractReviewProductThird::getLabel, "退货")
                        .eq(ContractReviewProductThird::getDelFlag, false));
                List<ContractProductThirdDTO> contractProductThirdDTOS = contractReviewProductThirdService.buildProductThird(reContractProductThirds, true);
                // 可能存在多个合同评审的 先group by一下
                Map<String, List<ContractProductThirdDTO>> thirdsByContractId = ListUtils.emptyIfNull(contractProductThirdDTOS).stream().collect(Collectors.groupingBy(ContractProductThirdDTO::getContractReviewId));

                MapUtils.emptyIfNull(thirdsByContractId).forEach((contractReviewId, thirds) -> {
                    // 把contractReviewId 转换成 processInstanceId
                    String contractProcessInstanceId = pidByCid.get(contractReviewId);
                    if (contractProcessInstanceId == null) {
                        ContractReviewMainBaseInfoDTO byContractId = mainService.getByContractId(contractReviewId);
                        pidByCid.put(contractReviewId, byContractId.getProcessInstanceId());
                        contractProcessInstanceId = byContractId.getProcessInstanceId();
                    }
                    postHandleThirdPrice(thirds);
                    remoteContractProductThirdService.saveBatch(contractProcessInstanceId, thirds);
                });
            }

            // 1.2新产品
            if (CollectionUtils.isNotEmpty(newProduct)) {
                newProduct.forEach(new1 -> {
                    ContractReviewProductOwn own = new ContractReviewProductOwn();
                    own.setProjectProductOwnId(new1.getRecordId());
                    own.setProductId(new1.getProductId());
                    own.setProjectId(projectId);
                    own.setContractReviewId(contractId);
                    own.setStuffCode(new1.getStuffCode());
                    own.setTaxRate(new1.getTaxRate());
                    own.setProductPeriodStart(new1.getProductPeriodStart());
                    own.setProductPeriodEnd(new1.getProductPeriodEnd());
                    own.setValidDate(LocalDateTime.now());
                    own.setLabel("换新");
                    own.setProductNum(new1.getProductNum() == null ? null : Long.valueOf(new1.getProductNum()));
                    own.setProductCategory(new1.getProductCategory());
                    own.setPnCode(new1.getPnCode());
                    own.setReturnExchangeProcessInstanceId(processInstanceId);
                    contractReviewProductOwnMapper.insert(own);
                    // 拿到这个id
                    String contractReviewProductOwnId = own.getId();
                    String returnExchangeProductOwnId = new1.getId();
                    ownMap.put(new1.getId(), contractReviewProductOwnId);
                    // 收入确认条款
                    contractReviewRevenueRecognitionService.update(new LambdaUpdateWrapper<ContractReviewRevenueRecognition>()
                            .eq(ContractReviewRevenueRecognition::getReturnExchangeOwnId, returnExchangeProductOwnId)
                            .eq(ContractReviewRevenueRecognition::getDelFlag, 0)
                            .set(ContractReviewRevenueRecognition::getProductOwnId, contractReviewProductOwnId)
                            .set(ContractReviewRevenueRecognition::getContractReviewMainId, contractId));
                    // 序列号
                    List<CrmProjectProductSnVO> psn = new1.getPsn();
                    ListUtils.emptyIfNull(psn).forEach(sn -> {
                        ContractProductSn productSn = new ContractProductSn();
                        productSn.setContractId(contractId);
                        productSn.setReturnExchangeProcessInstanceId(processInstanceId);
                        productSn.setSn(sn.getPsn());
                        productSn.setProjectProbationDeviceId(sn.getDeviceId());
                        productSn.setContractRecordId(contractReviewProductOwnId);
                        productSn.setProjectRecordId(new1.getRecordId());
                        productSn.setType(sn.getType());
                        contractProductSnService.save(productSn);
                    });

                    // 特殊代码
                    contractReviewSpecialCodeService.update(new LambdaUpdateWrapper<ContractReviewSpecialCode>()
                            .eq(ContractReviewSpecialCode::getReturnProductOwnId, returnExchangeProductOwnId)
                            .eq(ContractReviewSpecialCode::getDelFlag, 0)
                            .set(ContractReviewSpecialCode::getProductOwnId, contractReviewProductOwnId)
                            .set(ContractReviewSpecialCode::getContractReviewId, contractId));
                }
                );

            }

            if (CollectionUtils.isNotEmpty(newProductThird)) {
                newProductThird.forEach(new1 -> {
                            ContractReviewProductThird third = new ContractReviewProductThird();
                            third.setProjectProductThirdId(new1.getRecordId());
                            third.setProductId(new1.getProductId());
                            third.setProjectId(projectId);
                            third.setContractReviewId(contractId);
                            third.setStuffCode(new1.getStuffCode());
                            third.setTaxRate(new1.getTaxRate());
                            third.setProductPeriodStart(new1.getProductPeriodStart());
                            third.setProductPeriodEnd(new1.getProductPeriodEnd());
                            third.setValidDate(LocalDateTime.now());
                            third.setLabel("换新");
                            third.setProductNum(new1.getProductNum() == null ? null : Long.valueOf(new1.getProductNum()));
                            third.setSupplierId(new1.getSupplierId());
                            third.setSupplierName(new1.getSupplierName());
                            third.setPurchasePrice(new1.getPurchasePrice());
                            third.setReturnExchangeProcessInstanceId(processInstanceId);
                            contractReviewProductThirdMapper.insert(third);
                            // 拿到这个id
                            String contractReviewProductThirdId = third.getId();
                            String returnExchangeProductThirdId = new1.getId();
                            thirdMap.put(new1.getId(), contractReviewProductThirdId);
                            // 收入确认条款
                            contractReviewRevenueRecognitionService.update(new LambdaUpdateWrapper<ContractReviewRevenueRecognition>()
                                    .eq(ContractReviewRevenueRecognition::getReturnExchangeThirdId, returnExchangeProductThirdId)
                                    .eq(ContractReviewRevenueRecognition::getDelFlag, 0)
                                    .set(ContractReviewRevenueRecognition::getReturnExchangeThirdId, contractReviewProductThirdId)
                                    .set(ContractReviewRevenueRecognition::getContractReviewMainId, contractId));
                        }
                );
            }

            // 付款条款
            List<ContractReviewPaymentProvision> payments = contractReviewPaymentProvisionService.list(new LambdaQueryWrapper<ContractReviewPaymentProvision>()
                    .eq(ContractReviewPaymentProvision::getReturnExchangeProcessInstanceId, processInstanceId).eq(ContractReviewPaymentProvision::getDelFlag, 0));
            contractReviewPaymentProvisionService.update(new LambdaUpdateWrapper<ContractReviewPaymentProvision>()
                    .eq(ContractReviewPaymentProvision::getReturnExchangeProcessInstanceId, processInstanceId)
                    .set(ContractReviewPaymentProvision::getContractReviewMainId, contractId));

            // 同步 新产品至contract 服务中 新自有产品是树结构，并且把序列号和产品行的绑定关系写到合同服务中 直接共用合同的这个方法
            List<ContractReviewProductThird> productThirdNews = contractReviewProductThirdService.list(new LambdaUpdateWrapper<ContractReviewProductThird>()
                    .eq(ContractReviewProductThird::getReturnExchangeProcessInstanceId, processInstanceId)
                            .eq(ContractReviewProductThird::getLabel, "换新")
                    .eq(ContractReviewProductThird::getDelFlag, false));
            List<ContractReviewProductOwn> productOwnNews = contractReviewProductOwnService.list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                    .eq(ContractReviewProductOwn::getReturnExchangeProcessInstanceId, processInstanceId)
                    .eq(ContractReviewProductOwn::getLabel, "换新")
                    // 查出父节点
                    .isNull(ContractReviewProductOwn::getParentId)
                    .eq(ContractReviewProductOwn::getDelFlag, false));
            // 过滤出新产品
            List<ContractProductOwnDTO> productOwnDTOS = contractReviewProductOwnService.buildProductOwn(productOwnNews, contractId, true);
            List<ContractProductThirdDTO> productThirdDTOS = contractReviewProductThirdService.buildProductThird(productThirdNews, true);
            // 新产品新增的序列号
            CompletableFuture<List<ContractProductSnVO>> productSnTask = FlowThreadExecutor.supplyAsync(() ->
                    HyperBeanUtils.copyListProperties(contractProductSnService.list(new LambdaQueryWrapper<ContractProductSn>()
                            .eq(ContractProductSn::getReturnExchangeProcessInstanceId, processInstanceId)
                            .eq(ContractProductSn::getDelFlag, false)), ContractProductSnVO::new));
            // 这个地方必须串行取 并行取 此方法未结束，事务并未提交 并行取 取不到前面update的内容
            List<ContractReviewPaymentProvision> paymentProvisionTask = contractReviewPaymentProvisionService.list(new LambdaQueryWrapper<ContractReviewPaymentProvision>()
                    .eq(ContractReviewPaymentProvision::getReturnExchangeProcessInstanceId, processInstanceId)
                    .eq(ContractReviewPaymentProvision::getDelFlag, 0));
            List<ContractReviewRevenueRecognition> revenueRecognitionTask = contractReviewRevenueRecognitionService.list(new LambdaQueryWrapper<ContractReviewRevenueRecognition>()
                    .eq(ContractReviewRevenueRecognition::getReturnExchangeProcessInstanceId, processInstanceId)
                    .eq(ContractReviewRevenueRecognition::getDelFlag, 0));

            JsonObject<CrmContractBaseInfoVO> contractInfoByProcessInstanceId = remoteContractReviewService.getContractInfoByProcessInstanceId(contractProcessInstanceIdNewProduct);
            String contractServiceId = contractInfoByProcessInstanceId.getObjEntity().getId();
            ContractReviewMainBaseInfoDTO baseDTO = new ContractReviewMainBaseInfoDTO();
            baseDTO.setId(contractServiceId);

            ContractReviewMainFlowLaunchDTO productDTO = new ContractReviewMainFlowLaunchDTO();
            productDTO.setBaseInfoDTO(baseDTO);
            productDTO.setContractProductOwnDTOS(productOwnDTOS);
            productDTO.setContractProductThirdDTOS(productThirdDTOS);
            productDTO.setContractProductSnVOS(productSnTask.join());
            List<ContractFlowProductMappingDTO> mappingDTOS = remoteContractReviewService.saveProductData(productDTO, false).getObjEntity();
            Map<String, String> mappingMap = ListUtils.emptyIfNull(mappingDTOS).stream().collect(Collectors.toMap(ContractFlowProductMappingDTO::getFlowContractProductId,
                    ContractFlowProductMappingDTO::getContractProductId));

            // 保存条款
            ContractReviewTermDTO contractReviewTermDTO = new ContractReviewTermDTO();
            contractReviewTermDTO.setContractId(contractServiceId);
            contractReviewTermDTO.setPaymentProvisionDTOS(HyperBeanUtils.copyListProperties(paymentProvisionTask, PaymentProvisionDTO::new));
            contractReviewTermDTO.setRevenueRecognitionDTOS(HyperBeanUtils.copyListProperties(revenueRecognitionTask, RevenueRecognitionDTO::new));
            List<RevenueRecognitionDTO> revenueRecognitionDTOS = contractReviewTermDTO.getRevenueRecognitionDTOS();
            revenueRecognitionDTOS.forEach(revenueRecognitionDTO -> {
                if (revenueRecognitionDTO.getProductOwnId() != null) {
                    revenueRecognitionDTO.setProductOwnId(mappingMap.get(revenueRecognitionDTO.getProductOwnId()));
                }
                if (revenueRecognitionDTO.getProductThirdId() != null) {
                    revenueRecognitionDTO.setProductThirdId(mappingMap.get(revenueRecognitionDTO.getProductThirdId()));
                }
            });
            remoteContractReviewService.saveTermData(contractReviewTermDTO, false);

            // 没有新产品 证明不需要发货
            if (CollectionUtils.isNotEmpty(newProduct) || CollectionUtils.isNotEmpty(newProductThird)) {
                //  合同发货
                contractReviewDeliveryService.handleReturnChangeEffect(ownMap, thirdMap, returnChangeId, contractId);
            }
            // 未确认明细 根据之前确认的数量 确认5个 退6个 确认数量为-5 确认5个 退1个 确认数量为 -1 并写明港的接口
            remoteContractReviewService.saveReturnDetailByReturnExchange(processInstanceId);

            launchSealApplication(processInstanceId, contractReviewReturnExchange);

            remoteContractReviewService.saveOrRefreshContractExecute(contractNumber);
        }




    }

    @Override
    public void handlePass(FlowStateInfoVo flowInfo) {
        String processInstanceId = flowInfo.getProcessInstanceId();
        ContractReviewReturnExchange returnExchangeBaseVO = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        assert returnExchangeBaseVO != null;
        String contractId = returnExchangeBaseVO.getContractId();
        ContractReviewMainBaseInfoDTO baseInfoDTO = mainService.getByContractId(contractId);
        String contractReviewProcessInstanceId = baseInfoDTO.getProcessInstanceId();
        CrmContractBaseInfoVO baseInfoVO = remoteContractReviewService.getContractInfoByProcessInstanceId(contractReviewProcessInstanceId).getObjEntity();
        // 合同服务的id
        String contractServiceId = baseInfoVO.getId();

        // 合同评审退换货流程办结后，如果退换货协议选择【是】且存在确认的终稿，需要自动在合同原件列表生成【上交原件待办】同时自动触发【印鉴流程】，详见【退换货印鉴申请】；
        NodeSaveInfoVO documentVO = new NodeSaveInfoVO();
        // 退换货是否要传这个id
        documentVO.setContractReviewMainId(contractServiceId);
        documentVO.setContractNumber(returnExchangeBaseVO.getContractNumber());
        documentVO.setContractProcessNumber(returnExchangeBaseVO.getProcessNumber());
        if (returnExchangeBaseVO.getType() == 3) {
            documentVO.setOriginalType(OriginalTypeEnum.CONTRACT_INVALIDATION.getCode());
        } else {
            documentVO.setOriginalType(OriginalTypeEnum.RETURN.getCode());
        }
        documentVO.setContractProcessInstanceId(processInstanceId);
        remoteContractOriginalDocumentService.nodeSaveInfo(documentVO);
    }

    @Override
    public void handlePassBack(FlowStateInfoVo flowInfo) {

    }

    @Override
    public void handleProcessingBack(FlowStateInfoVo flowInfo) {
        String processInstanceId = flowInfo.getProcessInstanceId();
        List<ReturnExchangeProduct> newProduct = returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId);
        List<ReturnExchangeProductThird> newProductThird = returnExchangeProductThirdService.listNewProductByProcessInstanceId(processInstanceId);
        ContractReviewReturnExchange contractReviewReturnExchange = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        String returnChangeId = contractReviewReturnExchange.getId();
        String projectId = processExtensionInfoMapper.selectOne(new LambdaQueryWrapper<ProcessExtensionInfo>()
                .eq(ProcessExtensionInfo::getProcessInstanceId, processInstanceId).last("limit 1")
                .select(ProcessExtensionInfo::getProjectId)
        ).getProjectId();
        // 退回00 回滚项目
        if (node00Id.equals(flowInfo.getTargetActivityId())) {
            // rollback
            returnExchangeProductService.rollbackProjectProductOwn(newProduct, projectId);
            returnExchangeProductThirdService.rollbackProjectProductThird(newProductThird, projectId);
        }

        if (node04Id.equals(flowInfo.getCurrentActivityId())) {
            // rollback
            contractReviewProductOwnMapper.update(new LambdaUpdateWrapper<ContractReviewProductOwn>()
                    .eq(ContractReviewProductOwn::getReturnExchangeProcessInstanceId, processInstanceId)
                    .set(ContractReviewProductOwn::getDelFlag, 1));
            contractReviewProductThirdMapper.update(new LambdaUpdateWrapper<ContractReviewProductThird>()
                    .eq(ContractReviewProductThird::getReturnExchangeProcessInstanceId, processInstanceId)
                    .set(ContractReviewProductThird::getDelFlag, 1));
            // 把id置空即可
            contractReviewPaymentProvisionService.update(new LambdaUpdateWrapper<ContractReviewPaymentProvision>()
                    .eq(ContractReviewPaymentProvision::getReturnExchangeProcessInstanceId, processInstanceId)
                    .set(ContractReviewPaymentProvision::getContractReviewMainId, null));
            contractReviewRevenueRecognitionService.update(new LambdaUpdateWrapper<ContractReviewRevenueRecognition>()
                    .eq(ContractReviewRevenueRecognition::getReturnExchangeProcessInstanceId, processInstanceId)
                    .set(ContractReviewRevenueRecognition::getContractReviewMainId, null)
                    .set(ContractReviewRevenueRecognition::getProductThirdId, null)
                    .set(ContractReviewRevenueRecognition::getProductOwnId, null));
            contractReviewSpecialCodeService.update(new LambdaUpdateWrapper<ContractReviewSpecialCode>()
                    .eq(ContractReviewSpecialCode::getReturnExchangeProcessInstanceId, processInstanceId)
                    .set(ContractReviewSpecialCode::getReturnExchangeProcessInstanceId, null)
                    .set(ContractReviewSpecialCode::getProductOwnId, null));
            // rollback 合同发货
            contractReviewDeliveryService.handleReturnChangeRollback(returnChangeId);
            // 还原解绑的
            contractProductSnService.cancelUnBind(processInstanceId);
            // 还原新产品绑定的
            contractProductSnService.update(new LambdaUpdateWrapper<ContractProductSn>()
                    .eq(ContractProductSn::getReturnExchangeProcessInstanceId, processInstanceId)
                    .eq(ContractProductSn::getDelFlag, 0)
                    .set(ContractProductSn::getDelFlag, 1));

            // 还原退换货生效的 合同服务
            remoteContractReviewService.rollbackByReturnExchange(processInstanceId);
            remoteContractReviewService.cancelUnBind(processInstanceId);

            // 还原收入确认
            remoteContractUnconfirmedDetailService.deleteReturnDetail(processInstanceId);
        }

    }

    @Override
    public ProcessTypeEnum processType() {
        return ProcessTypeEnum.CONTRACT_REVIEW_RETURN_EXCHANGE;
    }

    public boolean show03F(String processInstanceId) {
        // 重点行业换货 返回true
        ContractReviewReturnExchange contractReviewReturnExchange = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        Integer type = contractReviewReturnExchange.getType();
        if (type != 3) {
            return false;
        }
        // 旧产品是否有重点行业的
        List<ReturnExchangeProduct> returnExchangeProducts = returnExchangeProductService.listOldProductByProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(returnExchangeProducts)) {
            return false;
        }
        return returnExchangeProducts.stream().filter(item -> item.getPsn() != null).flatMap(item -> item.getPsn().stream()).anyMatch(item -> "重点行业".equals(item.getType()));
    }

    @Override
    public Pair<Boolean, String> checkFilling(TfsTaskVo taskVo) {
        String processInstanceId = taskVo.getProcessInstanceId();
        ActionTypeEnum actionTypeEnum = taskVo.getActionTypeEnum();
        ContractReviewReturnExchange returnExchangeBaseVO = contractReviewReturnExchangeService.returnExchangeContractInfo(processInstanceId);
        assert returnExchangeBaseVO != null;
        List<ReturnExchangeProductThird> thirdNews = returnExchangeProductThirdService.listNewProductByProcessInstanceId(processInstanceId);
        List<ReturnExchangeProduct> ownNews = returnExchangeProductService.listNewProductByProcessInstanceId(processInstanceId);
        if (ActionTypeEnum.APPROVAL.equals(actionTypeEnum)) {
            if (node03Id.equals(taskVo.getCurrentNodeId()) && node04Id.equals(taskVo.getTargetActId())) {
                // 合同评审退换货03发04时，如果存在无物料代码第三方产品，需要校验该产品物料代码是否已存在，否则，给予报错提示：“存在第三方产品未分配物料代码，不可办结审批！”
                if (CollectionUtils.isNotEmpty(thirdNews)) {
                    List<ReturnExchangeProductThird> thirds = thirdNews.stream().filter(third -> StringUtils.isBlank(third.getStuffCode())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(thirds)) {
                        return Pair.of(false, "存在第三方产品未分配物料代码，不可办结审批！");
                    }
                }
            }
            if (node04Id.equals(taskVo.getCurrentNodeId())) {

                // 当存在新产品时，新产品合同发货信息，01步办结前必须填完，否则给予报错：“新产品合同发货信息未填写，不可办结审批！”
                if (CollectionUtils.isNotEmpty(ownNews) ||  CollectionUtils.isNotEmpty(thirdNews)) {
                    Boolean b = contractReviewDeliveryService.returnChangeIsAllDelivery(processInstanceId, returnExchangeBaseVO.getId());
                    if (!b) {
                        throw new CrmException("新产品合同发货信息未填写，不可办结审批！");
                    }
                }
            }
            if (node01Id.equals(taskVo.getCurrentNodeId())) {
                //是否签退换货协议选择“是”：01办结前必须上传附件，选择否可以不添加附件
                Boolean signAgreement = returnExchangeBaseVO.getSignAgreement();
                if (signAgreement != null && signAgreement) {
                    // 校验附件
                    List<ProcessFileInfoVO> processFileInfoVOS = processFileInfoService.queryByProcessInstanceId(taskVo.getProcessInstanceId(), null, null);
                    if (CollectionUtils.isEmpty(processFileInfoVOS)) {
                        throw new CrmException("请上传附件！");
                    }
                }
            }
        }
        return Pair.of(true, null);
    }

    @Override
    public Map<String, ProductLockState> query(String projectId, Set<String> productRecordIdSet) {
        ProcessExtensionInfoService processExtensionInfoService = SpringUtil.getBean(ProcessExtensionInfoService.class);
        List<ProcessExtensionInfo> list = processExtensionInfoService.list(new LambdaQueryWrapper<ProcessExtensionInfo>()
                .eq(ProcessExtensionInfo::getProjectId, projectId)
                .and(wrapper -> {
                    wrapper.eq(ProcessExtensionInfo::getProcessDefinitionKey, ProcessTypeEnum.CONTRACT_REVIEW_RETURN_EXCHANGE.getProcessDefinitionKey().getValue())
                            .or()
                            .eq(ProcessExtensionInfo::getProcessDefinitionKey, ProcessTypeEnum.PERFORMANCE_REPORT_RETURN_EXCHANGE.getProcessDefinitionKey().getValue());
                })
                .eq(ProcessExtensionInfo::getDelFlag, 0));
        Set<String> processInstanceIds = ListUtils.emptyIfNull(list).stream().map(ProcessExtensionInfo::getProcessInstanceId).collect(Collectors.toSet());
        Map<String, String> productMap = new HashMap<>();
        if (CollectionUtils.isEmpty(processInstanceIds)) {
            // 为空 都没被锁定
            return SetUtils.emptyIfNull(productRecordIdSet).stream().map(recordId -> {
                ProductLockState productLockState = new ProductLockState();
                productLockState.setRecordId(recordId);
                productLockState.setState(0);
                return productLockState;
            }).collect(Collectors.toMap(ProductLockState::getRecordId, v -> v));
        } else {
            // 查自有产品和第三方产品的集合
            CompletableFuture.allOf(Arrays.asList(
                    FlowThreadExecutor.supplyAsync(() -> returnExchangeProductService.list(new LambdaQueryWrapper<ReturnExchangeProduct>()
                            .in(ReturnExchangeProduct::getProcessInstanceId, processInstanceIds)
                            // 锁定新产品
                            .eq(ReturnExchangeProduct::getType, 4)
                            .eq(ReturnExchangeProduct::getDelFlag, 0))).thenAccept((result) -> {
                        productMap.putAll(ListUtils.emptyIfNull(result).stream().collect(Collectors.toMap(ReturnExchangeProduct::getRecordId, ReturnExchangeProduct::getProcessInstanceId, (oldV, newV) -> oldV)));
                    }),
                    FlowThreadExecutor.supplyAsync(() -> returnExchangeProductThirdService.list(new LambdaQueryWrapper<ReturnExchangeProductThird>()
                            .in(ReturnExchangeProductThird::getProcessInstanceId, processInstanceIds)
                            // 锁定新产品
                            .eq(ReturnExchangeProductThird::getType, 4)
                            .eq(ReturnExchangeProductThird::getDelFlag, 0))).thenAccept((result) -> {
                        productMap.putAll(ListUtils.emptyIfNull(result).stream().collect(Collectors.toMap(ReturnExchangeProductThird::getRecordId, ReturnExchangeProductThird::getProcessInstanceId, (oldV, newV) -> oldV)));
                    })).toArray(new CompletableFuture[0])).join();
            return SetUtils.emptyIfNull(productRecordIdSet).stream().map(recordId -> {
                ProductLockState productLockState = new ProductLockState();
                productLockState.setRecordId(recordId);
                String processInstanceId = productMap.get(recordId);
                if (StringUtils.isNotEmpty(processInstanceId)) {
                    productLockState.setProcessInstanceIdLockedBy(processInstanceId);
                    productLockState.setState(1);
                } else {
                    productLockState.setState(0);
                }
                return productLockState;
            }).collect(Collectors.toMap(ProductLockState::getRecordId, v -> v));
        }
    }

    @Override
    public Boolean isProductLocked(String projectId) {
        return false;
    }

    private void postHandleOwnPrice(List<ContractProductOwnDTO> owns) {
        if (CollectionUtils.isEmpty(owns)) {
            return;
        }
        owns.forEach(own -> {
            BigDecimal dealPrice = own.getDealPrice() == null ? BigDecimal.ZERO : own.getDealPrice();
            BigDecimal quotedPrice = own.getQuotedPrice() == null ? BigDecimal.ZERO : own.getQuotedPrice();
            BigDecimal finalPrice = own.getFinalPrice() == null ? BigDecimal.ZERO : own.getFinalPrice();
            long productNum = own.getProductNum() == null ? 0L : own.getProductNum();
            own.setDealTotalPrice(dealPrice.multiply(new BigDecimal(productNum)));
            own.setQuotedTotalPrice(quotedPrice.multiply(new BigDecimal(productNum)));
            own.setFinalTotalPrice(finalPrice.multiply(new BigDecimal(productNum)));
        });
    };

    private void postHandleThirdPrice(List<ContractProductThirdDTO> thirds) {
        if (CollectionUtils.isEmpty(thirds)) {
            return;
        }
        thirds.forEach(third -> {
            BigDecimal dealPrice = third.getDealPrice();
            BigDecimal quotedPrice = third.getFinalPrice();
            long productNum = third.getProductNum();
            third.setDealTotalPrice(dealPrice.multiply(new BigDecimal(productNum)));
            third.setFinalTotalPrice(quotedPrice.multiply(new BigDecimal(productNum)));
        });
    }
}
