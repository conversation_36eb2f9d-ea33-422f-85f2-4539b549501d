package com.topsec.crm.flow.core.controller.borrowForSell;

import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.flow.api.dto.borrowForSell.*;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.process.impl.BorrowForSellProcessService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.client.RemoteProjectMemberClient;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.vo.TfsFormContentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 借转销信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/borrowForSell")
@Tag(name = "借转销流程接口", description = "/borrowForSell")
public class BorrowForSellController extends BaseController {

    @Autowired
    private BorrowForSellProcessService borrowedForSellProcessService;

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @Autowired
    private RemoteProjectMemberClient remoteProjectMemberClient;

    @Autowired
    private RemoteAgentService remoteAgentService;

    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;

    @Resource
    private TfsFormContentClient tfsformContentClient;

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell")
    @PostMapping("/launch")
    @Operation(summary = "发起借转销流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody BorrowForSellFlowLaunchDTO launchDTO) {
        /*JsonObject<CrmProjectDirectlyVo> projectInfo = remoteProjectDirectlyClient.getProjectInfo(launchDTO.getProjectId());
        if(projectInfo.isSuccess() && null != projectInfo.getObjEntity()){
            launchDTO.setMatter(String.format("【%s】借转销申请", projectInfo.getObjEntity().getProjectName()));
        }*/
        JsonObject<List<FlowPerson>> flowPersonListByProjectId = remoteProjectMemberClient.getFlowPersonListByProjectId(launchDTO.getProjectId());
        if(flowPersonListByProjectId.isSuccess() && CollectionUtils.isNotEmpty(flowPersonListByProjectId.getObjEntity())){
            List<String> flowPersonList = flowPersonListByProjectId.getObjEntity().stream().map(FlowPerson::getPersonId).toList();
            if(flowPersonList.contains(launchDTO.getSalesmanPersonId())){
                return new JsonObject<>(borrowedForSellProcessService.launch(launchDTO));
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @GetMapping("/detail/{processInstanceId}")
    @Operation(summary = "查看借转销流程详情")
    public JsonObject<BorrowForSellFlowLaunchDTO> borrowForSellDetail(@PathVariable String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<BorrowForSellFlowLaunchDTO>(borrowForSellService.borrowForSellDetail(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/detailOfAgent/{processInstanceId}")
    @Operation(summary = "查看借转销流程详情(国代视角)")
    public JsonObject<BorrowForSellFlowLaunchDTO> borrowForSellDetailOfAgent(@PathVariable String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<BorrowForSellFlowLaunchDTO>(borrowForSellService.borrowForSellDetailOfAgent(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/agent/list")
    @Operation(summary = "查看借转销国代信息")
    public JsonObject<List<CrmAgentVo>> getGDAgent(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        JsonObject<List<CrmAgentVo>> gdAgentIdAndNames = remoteAgentService.getGDAgentIdAndNames();
        if(gdAgentIdAndNames.isSuccess() && CollectionUtils.isNotEmpty(gdAgentIdAndNames.getObjEntity())){
            Set<String> agentIds = gdAgentIdAndNames.getObjEntity().stream().map(CrmAgentVo::getId).collect(Collectors.toSet());
            JsonObject<Map<String, Boolean>> inBlacklist = remoteAgentService.isInBlacklist(agentIds);
            if(inBlacklist.isSuccess() && null != inBlacklist.getObjEntity()){
                gdAgentIdAndNames.getObjEntity().forEach(agent -> {
                    agent.setInBlacklist(inBlacklist.getObjEntity().getOrDefault(agent.getId(), false));
                });
            }
        }
        return gdAgentIdAndNames;
    }

    @PreFlowPermission
    @GetMapping("/checkSignCompany")
    @Operation(summary = "校验签订公司（true:在签订公司字典中存在，false：在签订公司字典中不存在）")
    public JsonObject<Boolean> checkSignCompany(@RequestParam String processInstanceId, @RequestParam String signCompanyName) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<Boolean>(borrowForSellService.checkSignCompany(signCompanyName));
    }

    @PreFlowPermission
    @GetMapping("/updateSignCompany")
    @Operation(summary = "修改签订公司")
    public JsonObject<Boolean> updateSignCompany(@RequestParam String processInstanceId, @RequestParam String signCompanyId, @RequestParam String signCompanyName) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<Boolean>(borrowForSellService.updateSignCompany(processInstanceId,signCompanyId,signCompanyName));
    }

    @PreFlowPermission
    @GetMapping("/directly/{projectId}")
    @Operation(summary = "根据项目ID获取项目信息")
    JsonObject<CrmProjectDirectlyVo> getProjectInfo(@PathVariable String projectId, String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(projectId.equals(tfsFormContentVo.getProjectId())){
                return remoteProjectDirectlyClient.getProjectInfo(projectId);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }
}

