package com.topsec.crm.flow.core.validator.returnexchange;

import com.deepoove.poi.util.FourthConsumer;
import com.topsec.crm.flow.api.dto.returnexchange.ReturnExchangeProductThirdVO;
import com.topsec.crm.flow.api.dto.returnexchange.ReturnExchangeProductVO;
import com.topsec.crm.flow.api.dto.returnexchange.ReturnExchangeSnVO;
import com.topsec.crm.framework.common.exception.CrmException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;

import java.util.Arrays;
import java.util.List;
import java.util.function.BiPredicate;

public class ReturnValidator {

    // 定义规则对象
    public static class ValidationRule {
        private final BiPredicate<Integer, Integer> condition;
        private final FourthConsumer<List<ReturnExchangeProductVO>, List<ReturnExchangeProductVO>, List<ReturnExchangeProductThirdVO>, List<ReturnExchangeProductThirdVO>> action;

        public ValidationRule(BiPredicate<Integer, Integer> condition,
                              FourthConsumer<List<ReturnExchangeProductVO>, List<ReturnExchangeProductVO>, List<ReturnExchangeProductThirdVO>, List<ReturnExchangeProductThirdVO>> action) {
            this.condition = condition;
            this.action = action;
        }

        public boolean applies(int type, int returnReason) {
            return condition.test(type, returnReason);
        }

        public void execute(List<ReturnExchangeProductVO> oldProducts, List<ReturnExchangeProductVO> newProducts, List<ReturnExchangeProductThirdVO> oldThirdProducts, List<ReturnExchangeProductThirdVO> newThirdProducts) {
            action.accept(oldProducts, newProducts, oldThirdProducts, newThirdProducts);
        }
    }

    // 规则集合
    public static final List<ValidationRule> RULES = Arrays.asList(
            // Rule 1: type=1 && returnReason in [1,2,13]
            new ValidationRule(
                    (type, reason) -> type == 1 && (reason == 1 || reason == 2 || reason == 13),
                    (oldProducts, newProducts, oldThirdProducts, newThirdProducts) -> {
                        validateNewProductsRequired(newProducts, newThirdProducts);
                        validateSnCountEqualsProductNum(oldProducts);
                    }
            ),

            // Rule 2: type=1 && returnReason in [3,4,6]
            new ValidationRule(
                    (type, reason) -> type == 1 && (reason == 3 || reason == 4 || reason == 6),
                    (oldProducts, newProducts, oldThirdProducts, newThirdProducts) -> {
                        validateSnCountLessThanOrEqualProductNum(oldProducts);
                    }
            ),

            // Rule 3: type=1 && returnReason in [5,7,8]
            new ValidationRule(
                    (type, reason) -> type == 1 && (reason == 5 || reason == 7 || reason == 8),
                    (oldProducts, newProducts, oldThirdProducts, newThirdProducts) -> {
                        validateNewProductsRequired(newProducts, newThirdProducts);
                        validateSnCountLessThanOrEqualProductNum(oldProducts);
                    }
            ),

            // Rule 4: type=2 && returnReason=9
            new ValidationRule(
                    (type, reason) -> type == 2 && reason == 9,
                    (oldProducts, newProducts, oldThirdProducts, newThirdProducts) -> {
                        validateNewProductsNotAllowed(newProducts, newThirdProducts);
                        validateSnCountEqualsProductNum(oldProducts);
                    }
            ),

            // Rule 5: type=2 && returnReason=10
            new ValidationRule(
                    (type, reason) -> type == 2 && reason == 10,
                    (oldProducts, newProducts, oldThirdProducts, newThirdProducts) -> {
                        validateNewProductsNotAllowed(newProducts, newThirdProducts);
                        validateSnCountLessThanOrEqualProductNum(oldProducts);
                    }
            )
    );

    // 校验方法实现
    private static void validateNewProductsRequired(List<ReturnExchangeProductVO> newProducts, List<ReturnExchangeProductThirdVO> newThirdProducts) {
        if (CollectionUtils.isEmpty(newProducts) && CollectionUtils.isEmpty(newThirdProducts)) {
            throw new CrmException("请选择新品");
        }
    }

    private static void validateNewProductsNotAllowed(List<ReturnExchangeProductVO> newProducts, List<ReturnExchangeProductThirdVO> newThirdProducts) {
        if (CollectionUtils.isNotEmpty(newProducts) || CollectionUtils.isNotEmpty(newThirdProducts)) {
            throw new CrmException("不能选择新品");
        }
    }

    private static void validateSnCountEqualsProductNum(List<ReturnExchangeProductVO> oldProducts) {
        if (CollectionUtils.isEmpty(oldProducts)) {
            return;
        }
        oldProducts.forEach(product -> {
            List<ReturnExchangeSnVO> snList = product.getPsn();
            int snCount = ListUtils.emptyIfNull(snList).size();
            if (snCount != product.getProductNum()) {
                throw new CrmException("旧产品关联序列号数量和退换货数量不一致，请检查");
            }
        });
    }

    private static void validateSnCountLessThanOrEqualProductNum(List<ReturnExchangeProductVO> oldProducts) {
        if (CollectionUtils.isEmpty(oldProducts)) {
            return;
        }
        oldProducts.forEach(product -> {
            List<ReturnExchangeSnVO> snList = product.getPsn();
            if (snList != null) {
                int snCount = snList.size();
                if (snCount > product.getProductNum()) {
                    throw new CrmException("旧产品关联序列号数量必须小于产品数量");
                }
            }
        });
    }

    // 入口方法
    public static void validate(int type, int returnReason,
                                List<ReturnExchangeProductVO> oldProducts,
                                List<ReturnExchangeProductVO> newProducts,
                                List<ReturnExchangeProductThirdVO> oldThirdProducts,
                                List<ReturnExchangeProductThirdVO> newThirdProducts) {
        RULES.stream()
                .filter(rule -> rule.applies(type, returnReason))
                .findFirst()
                .ifPresent(rule -> rule.execute(oldProducts, newProducts, oldThirdProducts, newThirdProducts));
    }
}
