package com.topsec.crm.flow.core.controllerhidden.contractInvoice;

import com.topsec.crm.contract.api.entity.invoice.InvoiceProductDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.ContractMakeInvoiceFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.MakeInvoicePageVO;
import com.topsec.crm.flow.core.process.impl.ContractMakeInvoiceProcessService;
import com.topsec.crm.flow.core.process.impl.ContractReturnInvoiceProcessService;
import com.topsec.crm.flow.core.service.ContractInvoiceExtendService;
import com.topsec.crm.flow.core.service.ContractMakeInvoiceService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/hidden/contractInvoice")
@RequiredArgsConstructor
@Tag(name = "合同开票")
public class HiddenContractInvoiceController extends BaseController {

    private final ContractMakeInvoiceService contractMakeInvoiceService;
    private final ContractMakeInvoiceProcessService makeProcessService;
    private final ContractInvoiceExtendService invoiceExtendService;
    private final ContractReturnInvoiceProcessService returnProcessService;

    @GetMapping("/getPageByBatchFileId")
    @Operation(hidden = true)
    public JsonObject<TableDataInfo> getPageByBatchFileId(@RequestParam String batchFileId){
        return new JsonObject<>(contractMakeInvoiceService.getPageByBatchFileId(batchFileId));
    }

    @PostMapping("/batchLaunch")
    @Operation(hidden = true,description = "批量发起合同开票流程")
    @Transactional(rollbackFor = Exception.class)
    public JsonObject<Boolean> batchLaunch(@RequestBody List<ContractMakeInvoiceFlowLaunchDTO> list){
        list.forEach(makeProcessService::launch);
        return new JsonObject<>(true);
    }

    @GetMapping("/getMakeInvoiceListByContractNumber")
    @Operation(hidden = true,description = "根据合同号获取合同开票列表")
    public JsonObject<List<MakeInvoicePageVO>> getMakeInvoiceListByContractNumber(@RequestParam String contractNumber){
        return new JsonObject<>(contractMakeInvoiceService.getListByContractNumber(contractNumber));
    }

    @GetMapping("/getInvoiceProcessPage")
    @Operation(hidden = true,description = "合同开票退票流程-分页列表")
    public JsonObject<TableDataInfo> getInvoiceProcessPage(@RequestParam String contractNumber,@RequestParam Integer pageSize, @RequestParam Integer pageNum){
        return new JsonObject<>(invoiceExtendService.getInvoiceProcessPage(contractNumber,pageSize,pageNum));
    }

    @GetMapping("/getMakeInvoiceAmountTotal")
    @Operation(hidden = true,description = "获取合同开票金额合计")
    public JsonObject<BigDecimal> getMakeInvoiceAmountTotal(@RequestParam String contractNumber){
        return new JsonObject<>(invoiceExtendService.getMakeInvoiceAmountTotal(contractNumber));
    }

    @GetMapping("/getReturnInvoiceAmountTotal")
    @Operation(hidden = true,description = "获取合同退票金额合计")
    public JsonObject<BigDecimal> getReturnInvoiceAmountTotal(@RequestParam String contractNumber){
        return new JsonObject<>(invoiceExtendService.getReturnInvoiceAmountTotal(contractNumber));
    }

    @GetMapping("/autoReturnInvoiceProcess")
    @Operation(hidden = true,description = "自动发起合同退票流程")
    public JsonObject<Boolean> autoReturnInvoiceProcess(List<InvoiceProductDTO> req,String contractNumber){
        return new JsonObject<>(returnProcessService.autoLaunch(req, contractNumber));
    }

}
