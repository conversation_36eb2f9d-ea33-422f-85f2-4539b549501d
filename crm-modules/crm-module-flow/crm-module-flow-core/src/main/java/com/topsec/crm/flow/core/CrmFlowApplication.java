package com.topsec.crm.flow.core;

import cn.hutool.extra.spring.EnableSpringUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.retry.annotation.EnableRetry;

/**
 * SpringBoot启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableCaching(proxyTargetClass = true)
@EnableRetry(proxyTargetClass = true)
@EnableFeignClients(basePackages = "com.topsec")
@EnableSpringUtil
public class CrmFlowApplication {
    public static void main(String[] args) {
        SpringApplication.run(CrmFlowApplication.class, args);
    }
}
