package com.topsec.crm.flow.core.controller.borrowForSellRevoke;


import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellProductDTO;
import com.topsec.crm.flow.api.dto.borrowForSellRevoke.RevokePageQuery;
import com.topsec.crm.flow.api.dto.borrowForSellRevoke.RevokePageVO;
import com.topsec.crm.flow.api.dto.borrowForSellRevoke.RevokeProcessDetailVO;
import com.topsec.crm.flow.core.entity.BorrowForSellProduct;
import com.topsec.crm.flow.core.process.impl.BorrowForSellRevokeProcessService;
import com.topsec.crm.flow.core.service.BorrowForSellRevokeService;
import com.topsec.crm.flow.core.service.IBorrowForSellProductService;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/business/borrowForSellRevoke")
@Tag(name = "借转销撤销业务", description = "/borrowForSellRevoke")
@RequiredArgsConstructor
@Validated
public class BorrowForSellRevokeBusinessController extends BaseController {


    private final BorrowForSellRevokeService borrowForSellRevokeService;

    private final BorrowForSellRevokeProcessService borrowForSellRevokeProcessService;

    private final IBorrowForSellProductService borrowForSellProductService;









    @GetMapping("/detail/{processInstanceId}")
    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_revoke",dataScope = "crm_flow_borrow_for_sell_revoke")
    @Operation(summary = "查看借转销撤销详情")

    public JsonObject<RevokeProcessDetailVO> borrowForSellRevokeDetail(@PathVariable String processInstanceId) {
        return new JsonObject<>(borrowForSellRevokeService.borrowForSellRevokeDetail(processInstanceId));
    }


    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_revoke",dataScope = "crm_flow_borrow_for_sell_revoke")
    @GetMapping("/borrowForSellRevokeDetailByBorrowId/{borrowId}")
    @Operation(summary = "查看借转销撤销流程详情根据撤销借转销Id")
    public JsonObject<RevokeProcessDetailVO> borrowForSellRevokeDetailByBorrowId(@PathVariable String borrowId) {
        return new JsonObject<>(borrowForSellRevokeService.borrowForSellRevokeDetailByBorrowId(borrowId));
    }


    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_revoke",dataScope = "crm_flow_borrow_for_sell_revoke")
    @PostMapping("/page")
    @Operation(summary = "借转销撤销列表分页查询")
    public JsonObject<PageUtils<RevokePageVO>> borrowForSellRevokePage(@RequestBody RevokePageQuery query) {
        return new JsonObject<>(borrowForSellRevokeService.borrowForSellRevokePage(query));

    }


    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_revoke",dataScope = "crm_flow_borrow_for_sell_revoke")
    @GetMapping("/queryBorrowForSellProduct")
    @Operation(summary = "查询借转销产品信息")
    public JsonObject<List<BorrowForSellProductDTO>> queryBorrowForSellProduct(@RequestParam String borrowId) {
        List<BorrowForSellProduct> result =  borrowForSellProductService.queryBorrowForSellProduct(borrowId);
        List<BorrowForSellProduct> filteredResult = result.stream()
                .filter(product -> product.getTopsecCount() != 0)
                .toList();
        List<BorrowForSellProductDTO> borrowForSellProductDTOS = HyperBeanUtils.copyListPropertiesByJackson(filteredResult, BorrowForSellProductDTO.class);
        borrowForSellProductDTOS.stream().filter(item -> StringUtils.isNotBlank(item.getId()))
                .forEach(item -> item.setRevokeNum(borrowForSellRevokeService.queryRevokedCount(item.getId())));
        return new JsonObject<List<BorrowForSellProductDTO>>(borrowForSellProductDTOS);
    }


}

