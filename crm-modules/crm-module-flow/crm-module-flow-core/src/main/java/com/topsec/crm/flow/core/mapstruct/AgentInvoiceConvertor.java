package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.agentInvoice.AgentInvioceProductDTO;
import com.topsec.crm.flow.api.dto.agentInvoice.AgentInvoiceMainDTO;
import com.topsec.crm.flow.core.entity.AgentInvioceProduct;
import com.topsec.crm.flow.core.entity.AgentInvoiceMain;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface AgentInvoiceConvertor
{
    AgentInvoiceConvertor INSTANCE = Mappers.getMapper(AgentInvoiceConvertor.class);
    @Mapping(target = "dataSource", constant = "1")
    AgentInvoiceMain toEntity(AgentInvoiceMainDTO agentInvoiceMainDTO);

    List<AgentInvioceProduct> toProductList(List<AgentInvioceProductDTO> productDTOs);
}
