package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.performancereport.PerformanceExecuteVO;
import com.topsec.crm.flow.core.entity.PerformanceExecute;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface performanceExcuteConvertor {

    performanceExcuteConvertor INSTANCE = Mappers.getMapper(performanceExcuteConvertor.class);
    PerformanceExecuteVO toExecuteVO(PerformanceExecute baseInfo);
}
