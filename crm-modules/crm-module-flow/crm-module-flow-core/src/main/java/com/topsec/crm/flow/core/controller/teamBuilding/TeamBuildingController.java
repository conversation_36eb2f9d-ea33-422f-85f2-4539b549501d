package com.topsec.crm.flow.core.controller.teamBuilding;

import com.topsec.crm.flow.api.dto.teamBuilding.*;
import com.topsec.crm.flow.api.dto.teamBuilding.input.TeamBuildingProjectManagerVO;
import com.topsec.crm.flow.api.dto.teamBuilding.input.TeamBuildingVO;
import com.topsec.crm.flow.api.enums.ProductsAndServicesInvolved;
import com.topsec.crm.flow.api.enums.TeamBuildingProjectType;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.mapstruct.TeamBuildingMainConvert;
import com.topsec.crm.flow.core.process.impl.TeamBuildingProcessService;
import com.topsec.crm.flow.core.service.TeamBuildingService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.tos.common.vo.tree.Triple;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2024-06-13  10:37
 * @Description:
 */
@RestController
@RequestMapping("/teamBuilding")
@Tag(name = "团队组建", description = "/teamBuilding")
@RequiredArgsConstructor
@Validated
public class TeamBuildingController extends BaseController {

    private final TeamBuildingService teamBuildingService;

    private final TeamBuildingProcessService teamBuildingProcessService;

    private final RemoteProjectDirectlyClient remoteProjectDirectlyClient;




    @PostMapping("/launch")
    @PreAuthorize(hasPermission = "crm_flow_team_building",dataScope = "crm_flow_team_building")
    @Operation(summary = "发起团队组建流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody TeamBuildingFlowLaunchDTO launchDTO) {
        return new JsonObject<>(teamBuildingProcessService.launch(launchDTO));
    }

    @PostMapping("/launchAndReturnProcessInstanceId")
    @PreAuthorize(hasPermission = "crm_flow_team_building",dataScope = "crm_flow_team_building")
    @Operation(summary = "发起团队组建流程并返回流程id")
    public JsonObject<String> launchAndReturnProcessInstanceId(@Valid @RequestBody TeamBuildingFlowLaunchDTO launchDTO) {
        return new JsonObject<>(teamBuildingProcessService.launchAndReturnProcessInstanceId(launchDTO));
    }

    @GetMapping ("/getTeamBuildingProcessInfo")
    @PreFlowPermission
    @Operation(summary = "获取流程信息")
    public JsonObject<TeamBuildingProcessVO> getTeamBuildingProcessInfo(@RequestParam String processInstanceId) {
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, headerValue);
        return new JsonObject<>(teamBuildingService.getTeamBuildingProcessInfo(processInstanceId));
    }




    @GetMapping("/projectBaseInfo")
    @PreFlowPermission
    @Operation(summary = "项目基本信息")
    public JsonObject<ProjectInfoVO> getProjectInfoVO(@RequestParam String projectId){
        CrmAssert.notNull("项目id不能为null",projectId);
        return new JsonObject<>(teamBuildingService.getProjectInfoVO(projectId));
    }


    @GetMapping("/teamBuildingBaseInfo")
    @PreFlowPermission
    @Operation(summary = "团队组建资料")
    public JsonObject<TeamBuildingDetailVO> teamBuildingBaseInfo(@RequestParam String teamBuildingId){
        CrmAssert.notNull("团队组建id不能为null",teamBuildingId);
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = teamBuildingService.getById(teamBuildingId).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(teamBuildingService.teamBuildingBaseInfo(teamBuildingId));
    }


    @GetMapping("/departmentsInvolved")
    @PreFlowPermission
    @Operation(summary = "00呈报人:预计涉及部门列表")
    public JsonObject<List<TeamBuildingDepartmentInfoVO>> getDepartmentsInvolved(){
        List<TeamBuildingDepartmentInfoVO> collect = teamBuildingService.listNotDeleted().stream().filter(t->t.getBelongingSystem()!=null).filter(teamBuilding -> teamBuilding.getBelongingSystem().contains("产品体系") || teamBuilding.getBelongingSystem().contains("运营体系")).collect(Collectors.toList());
        return new JsonObject<>(collect);
    }


    @GetMapping("/getBranchesInvolved")
    @PreFlowPermission
    @Operation(summary = "00呈报人:预计分支机构列表")
    public JsonObject<List<TeamBuildingDepartmentInfoVO>> getBranchesInvolved(){
        List<TeamBuildingDepartmentInfoVO> collect = teamBuildingService.listNotDeleted().stream().filter(t->t.getBelongingSystem()!=null).filter(teamBuilding -> teamBuilding.getBelongingSystem().contains("营销体系")).collect(Collectors.toList());
        return new JsonObject<>(collect);
    }



    @GetMapping("/getProductsAndServicesInvolvedEnumValues")
    @PreFlowPermission
    @Operation(summary = "00呈报人:获取所有涉及的产品和服务")
    public JsonObject<List<Map<String, Object>>> getProductsAndServicesInvolvedEnumValues(){
        return new JsonObject<>(ProductsAndServicesInvolved.getProductsAndServicesInvolvedEnumValues());
    }

    @GetMapping("/getTeamBuildingProjectTypeEnumValues")
    @PreFlowPermission
    @Operation(summary = "00呈报人:获取所有项目类型")
    public JsonObject<List<Map<String, Object>>> getTeamBuildingProjectTypeEnumValues(){
        return new JsonObject<>(TeamBuildingProjectType.getTeamBuildingProjectTypeEnumValues());
    }

    @GetMapping("/getPreSales")
    @PreFlowPermission
    @Operation(summary = "00呈报人:售前人员列表")
    public JsonObject< List<EmployeeVO>> getPreSales(@RequestParam String departmentId){
        return new JsonObject<>(teamBuildingService.getPreSales(departmentId));
    }

    @GetMapping("/isProductLineApproval")
    @PreFlowPermission
    @Operation(summary = "01上二级领导审批:是否需要产品线审批(01B,否则01C)")
    public JsonObject<Boolean> isProductLineApproval(@RequestParam String teamBuildingId){
        CrmAssert.notNull("团队组建id不能为null",teamBuildingId);
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = teamBuildingService.getById(teamBuildingId).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(teamBuildingService.isProductLineApproval(teamBuildingId));
    }


    @PostMapping("/update")
    @PreFlowPermission(hasAnyNodes = {"sid-A73C3AFF-D135-4921-AC61-02171F5B5825","sid-1BA2B404-F79E-4E8B-B2E3-21E155AA7939","sid-DA716421-9E0C-4821-90B8-D92A987B2A92"})
    @Operation(summary = "团队组建修改")
    public JsonObject<Boolean> update(@Valid @RequestBody TeamBuildingDTO launchDTO) {
        if (StringUtils.isNotBlank(launchDTO.getBaseInfo().getId())){
            String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
            String processInstanceId = teamBuildingService.getById(launchDTO.getBaseInfo().getId()).getProcessInstanceId();
            PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        }
        return new JsonObject<>(teamBuildingService.update(launchDTO));
    }
    @GetMapping("/proposeProjectManagers")
    @PreFlowPermission
    @Operation(summary = "02QA建议项目经理出自部门列表")
    public JsonObject<List<TeamBuildingProjectManagerVO>> proposeProjectManagers(@RequestParam String teamBuildingId){
        CrmAssert.notNull("团队组建id不能为null",teamBuildingId);
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = teamBuildingService.getById(teamBuildingId).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(teamBuildingService.proposeProjectManagers(teamBuildingId));
    }



    @PostMapping("/proposeProjectManagerDept")
    @PreFlowPermission(hasAnyNodes = {"sid-A73C3AFF-D135-4921-AC61-02171F5B5825","sid-9818449C-DD9B-49A9-9F25-A2F5552E5345","sid-DA716421-9E0C-4821-90B8-D92A987B2A92"})
    @Operation(summary = "02QA建议项目经理出自部门")
    public JsonObject<Boolean> proposeProjectManagerDept(@RequestBody TeamBuildingProjectManagerVO teamBuildingProjectManagerVO){
        CrmAssert.notNull("团队组建id不能为null",teamBuildingProjectManagerVO.getTeamBuildingId());
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = teamBuildingService.getById(teamBuildingProjectManagerVO.getTeamBuildingId()).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        CrmAssert.notNull("团队任命id不能为null",teamBuildingProjectManagerVO.getId());
        return new JsonObject<>(teamBuildingService.proposeProjectManagerDept(teamBuildingProjectManagerVO));
    }


    @GetMapping("/jurisdictionalDeptList")
    @PreFlowPermission
    @Operation(summary = "03体系领导指定部门(管辖部门,分支下拉)")
    public JsonObject<Map<String,List<Triple<String,String,String>>>> jurisdictionalDeptList(String teamBuildingId, String personId){
        CrmAssert.notNull("团队组建id不能为null",teamBuildingId);
        String headerValue = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        String processInstanceId = teamBuildingService.getById(teamBuildingId).getProcessInstanceId();
        PreFlowPermissionAspect.checkProcessInstanceId(headerValue, processInstanceId);
        return new JsonObject<>(teamBuildingService.jurisdictionalDeptList(teamBuildingId, personId));
    }





    @GetMapping("/applicantStepOfThird")
    @PreFlowPermission
    @Operation(summary = "03办理人信息")
    public JsonObject<List<FlowPerson>> applicantStepOfThird(@RequestParam String processInstanceId){
        CrmAssert.notNull("流程实例id不能为null",processInstanceId);
        PreFlowPermissionAspect.checkProcessInstanceId(HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID), processInstanceId);
        return new JsonObject<>(TeamBuildingMainConvert.INSTANCE.toFlowPersonList(teamBuildingService.applicantStepOfThird(processInstanceId)));
    }


    @GetMapping("/applicantStepOfFourth")
    @PreFlowPermission
    @Operation(summary = "04办理人信息")
    public JsonObject<List<FlowPerson>> applicantStepOfFourth(@RequestParam String teamBuildingId){
        CrmAssert.notNull("团队组建id不能为null",teamBuildingId);
        PreFlowPermissionAspect.checkProcessInstanceId(HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID), teamBuildingService.getById(teamBuildingId).getProcessInstanceId());
        return new JsonObject<>(TeamBuildingMainConvert.INSTANCE.toFlowPersonList(teamBuildingService.applicantStepOfFourth(teamBuildingId)));
    }


    @GetMapping("/applicantStepOfFifth")
    @PreFlowPermission
    @Operation(summary = "05办理人")
    public JsonObject<FlowPerson> getProjectManagerApproved(@RequestParam String processInstanceId){
        CrmAssert.notNull("流程实例id不能为null",processInstanceId);
        PreFlowPermissionAspect.checkProcessInstanceId(HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID), processInstanceId);
        return new JsonObject<>(TeamBuildingMainConvert.INSTANCE.toFlowPerson(teamBuildingService.getProjectManagerApproved(processInstanceId)));
    }

    @GetMapping("/getDepartmentLeaders")
    @PreFlowPermission
    @Operation(summary = "06各部门负责人列表")
    public JsonObject<List<TeamBuildingFlowPersonVO>> getDepartmentLeaders(@RequestParam String processInstanceId){
        CrmAssert.notNull("流程实例id不能为null",processInstanceId);
        PreFlowPermissionAspect.checkProcessInstanceId(HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID), processInstanceId);
        List<FlowPerson> departmentLeaders = TeamBuildingMainConvert.INSTANCE.toFlowPersonList(teamBuildingService.getDepartmentLeaders(processInstanceId));
        List<FlowPerson> departmentLeadersNoAssign = TeamBuildingMainConvert.INSTANCE.toFlowPersonList(teamBuildingService.getDepartmentLeadersNoAssign(processInstanceId));
        List<TeamBuildingFlowPersonVO> result = departmentLeaders.stream().map(flowPerson -> {
            TeamBuildingFlowPersonVO teamBuildingFlowPersonVO = new TeamBuildingFlowPersonVO();
            teamBuildingFlowPersonVO.setFlowPerson(flowPerson);
            teamBuildingFlowPersonVO.setAssignFlag(departmentLeadersNoAssign.contains(flowPerson) ? 1 : 0);
            return teamBuildingFlowPersonVO;
        }).collect(Collectors.toList());
        return new JsonObject<>(result);
    }


    @GetMapping("/reasonForUnapproved")
    @PreFlowPermission
    @Operation(summary = "06验证是否能办理完毕")
    public JsonObject<ReasonForUnableVO> reasonForUnapproved(String teamBuildingId, String personId){
        CrmAssert.notNull("团队组建id不能为null",teamBuildingId);
        PreFlowPermissionAspect.checkProcessInstanceId(HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID), teamBuildingService.getById(teamBuildingId).getProcessInstanceId());
        return new JsonObject<>(teamBuildingService.reasonForUnapproved(teamBuildingId, personId));
    }

    @GetMapping("/applicantStepOfSeventh")
    @PreFlowPermission
    @Operation(summary = "07办理人")
    public JsonObject<FlowPerson> applicantStepOfSeventh(@RequestParam String processInstanceId){
        CrmAssert.notNull("流程id不能为null",processInstanceId);
        PreFlowPermissionAspect.checkProcessInstanceId(HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID), processInstanceId);
        return new JsonObject<>(TeamBuildingMainConvert.INSTANCE.toFlowPerson(teamBuildingService.applicantStepOfSeventh(processInstanceId)));
    }

    @GetMapping("/applicantStepOfEighth")
    @PreFlowPermission
    @Operation(summary = "08办理人")
    public JsonObject<FlowPerson> applicantStepOfEighth(@RequestParam String processInstanceId){
        CrmAssert.notNull("流程id不能为null",processInstanceId);
        PreFlowPermissionAspect.checkProcessInstanceId(HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID), processInstanceId);
        return new JsonObject<>(TeamBuildingMainConvert.INSTANCE.toFlowPerson(teamBuildingService.applicantStepOfEighth(processInstanceId)));
    }


    @PostMapping("/summaryDescription")
    @PreFlowPermission(hasAnyNodes = {"sid-E03CAB3C-B3B4-4B6A-85D9-AE1CACA335D3"})
    @Operation(summary = "08QA项目汇总")
    public JsonObject<Boolean> summaryDescription(@RequestBody TeamBuildingVO teamBuildingVO){
        CrmAssert.notNull("团队组建id不能为null",teamBuildingVO.getId());
        PreFlowPermissionAspect.checkProcessInstanceId(HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID), teamBuildingService.getById(teamBuildingVO.getId()).getProcessInstanceId());
        return new JsonObject<>(teamBuildingService.summaryDescription(teamBuildingVO));
    }




    @GetMapping("/teamBuildingProcessIsLaunch")
    @Operation(summary = "团队组建流程是否发起")
    @PreAuthorize(hasPermission = "crm_project_directly")
    public JsonObject<TeamBuildingProcessVO> teamBuildingProcessIsLaunch(@RequestParam String projectId){
        CrmAssert.notNull("项目id不能为null",projectId);
        Boolean objEntity = Optional.ofNullable(remoteProjectDirectlyClient.hasRight(projectId,getCurrentPersonId())).orElseThrow(() -> new CrmException(ResultEnum.NULL_OBJ_ENTITY)).getObjEntity();
        if (!objEntity) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
        return new JsonObject<>(teamBuildingService.teamBuildingProcessIsLaunch(projectId));
    }



}
