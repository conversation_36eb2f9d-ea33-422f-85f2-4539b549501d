package com.topsec.crm.flow.core.controller.borrowForSell;


import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellDeliverySnDTO;
import com.topsec.crm.flow.core.entity.BorrowForSell;
import com.topsec.crm.flow.core.entity.BorrowForSellDelivery;
import com.topsec.crm.flow.core.entity.BorrowForSellDeliverySn;
import com.topsec.crm.flow.core.service.IBorrowForSellDeliveryService;
import com.topsec.crm.flow.core.service.IBorrowForSellDeliverySnService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 借转销发货信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/business/borrowForSellDelivery")
@Tag(name = "借转销流程发货信息", description = "/business/borrowForSellDelivery")
public class BorrowForSellDeliveryBusinessController {

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @Autowired
    private IBorrowForSellDeliveryService borrowForSellDeliveryService;

    @Autowired
    private IBorrowForSellDeliverySnService borrowForSellDeliverySnService;

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_gd_details",dataScope = "crm_flow_borrow_for_sell_gd_details")
    @PostMapping("/addExpressData")
    @Operation(summary = "添加快递信息")
    public JsonObject<Boolean> addExpressData(@RequestBody BorrowForSellDeliverySnDTO deliverySnData) {
        BorrowForSellDelivery byId = borrowForSellDeliveryService.getById(deliverySnData.getDeliveryId());
        if(null != byId){
            BorrowForSell borrowForSell = borrowForSellService.getById(byId.getBorrowId());
            if (null != borrowForSell){
                if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList())
                        || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(borrowForSell.getSalesmanPersonId())
                        || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(borrowForSell.getCreateUser())){
                    BorrowForSellDeliverySn borrowForSellDeliverySn = HyperBeanUtils.copyPropertiesByJackson(deliverySnData, BorrowForSellDeliverySn.class);
                    return new JsonObject<Boolean>(borrowForSellDeliverySnService.addExpressData(borrowForSellDeliverySn));
                }
            }
        }
        throw new CrmException(ResultEnum.AUTH_ERROR_500006);
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_gd_details",dataScope = "crm_flow_borrow_for_sell_gd_details")
    @PostMapping("/deleteExpressData")
    @Operation(summary = "删除快递信息")
    public JsonObject<Boolean> deleteExpressData(@RequestParam String deliverySnId) {
        BorrowForSellDeliverySn deliverySnData = borrowForSellDeliverySnService.getById(deliverySnId);
        if(null != deliverySnData){
            BorrowForSellDelivery byId = borrowForSellDeliveryService.getById(deliverySnData.getDeliveryId());
            if(null != byId){
                BorrowForSell borrowForSell = borrowForSellService.getById(byId.getBorrowId());
                if (null != borrowForSell){
                    if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList())
                            || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(borrowForSell.getSalesmanPersonId())
                            || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(borrowForSell.getCreateUser())){
                        return new JsonObject<Boolean>(borrowForSellDeliverySnService.deleteExpressData(deliverySnId));
                    }
                }
            }
        }
        throw new CrmException(ResultEnum.AUTH_ERROR_500006);
    }

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_gd_details",dataScope = "crm_flow_borrow_for_sell_gd_details")
    @PostMapping("/queryDeliverySnByIds")
    @Operation(summary = "根据发货单ID查询发货单详细信息")
    public JsonObject<List<BorrowForSellDeliverySnDTO>> queryDeliverySnByIds(@RequestParam String borrowId, @RequestBody List<String> ids) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            if(CollectionUtils.isEmpty(PreAuthorizeAspect.getDataScopeParam().getPersonIdList())
                    || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(byId.getSalesmanPersonId())
                    || PreAuthorizeAspect.getDataScopeParam().getPersonIdList().contains(byId.getCreateUser())){
                List<BorrowForSellDeliverySn> borrowForSellDeliverySns = borrowForSellDeliverySnService.listByIds(ids);
                return new JsonObject<List<BorrowForSellDeliverySnDTO>>(HyperBeanUtils.copyListPropertiesByJackson(borrowForSellDeliverySns, BorrowForSellDeliverySnDTO.class));
            }
        }
        throw new CrmException(ResultEnum.AUTH_ERROR_500006);
    }
}

