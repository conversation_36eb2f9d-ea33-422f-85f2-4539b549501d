package com.topsec.crm.flow.core.controller.sealManagement;

import com.topsec.crm.flow.api.dto.sealApplication.SealApplicationFlowLaunchDTO;
import com.topsec.crm.flow.core.process.impl.SealApplicationReturnOrExchangeProcessService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/seal-application-return-exchange")
@RequiredArgsConstructor
@Validated
public class SealApplicationReturnOrExchangeController extends BaseController {
    private final SealApplicationReturnOrExchangeProcessService sealApplicationReturnOrExchangeProcessService;
    @PostMapping("/launch")
    @Operation(summary = "发起退货换货印鉴流程")
    @PreAuthorize(hasPermission = "crm_flow_seal")
    public JsonObject<Boolean> launch(@Valid @RequestBody SealApplicationFlowLaunchDTO launchDTO) {
        return new JsonObject<>(sealApplicationReturnOrExchangeProcessService.launch(launchDTO));
    }
}
