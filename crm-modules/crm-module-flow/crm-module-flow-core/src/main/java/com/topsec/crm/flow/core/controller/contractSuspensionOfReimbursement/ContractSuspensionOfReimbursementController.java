package com.topsec.crm.flow.core.controller.contractSuspensionOfReimbursement;

import com.topsec.crm.contract.api.entity.CrmProofOfDebtCollectionQuery;
import com.topsec.crm.flow.api.dto.contractSuspensionOfReimbursement.ContractSuspensionOfReimbursementQuery;
import com.topsec.crm.flow.api.dto.contractSuspensionOfReimbursement.ContractSuspensionOfReimbursementVO;
import com.topsec.crm.flow.core.service.ContractSuspensionOfReimbursementService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.domain.BaseEntity;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.utils.AuthorizeUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contractSuspensionOfReimbursement")
@Tag(name = "暂停报销", description = "暂停报销")
@RequiredArgsConstructor
@Validated
@Slf4j
public class ContractSuspensionOfReimbursementController {

    private final ContractSuspensionOfReimbursementService contractSuspensionOfReimbursementService;


    @PostMapping("/export")
    @Operation(summary = "暂停报销导出列表")
    @PreAuthorize(hasPermission = "crm_contract_receivable_sus_re_export")
    public void export(@RequestBody CrmProofOfDebtCollectionQuery query, HttpServletResponse response){
        // query.setPageSize(Integer.MAX_VALUE);
        // query.setPageNum(1);
        // ExportParams exportParam=new ExportParams();
        // exportParam.setSheetName("暂停报销");
        // exportParam.setTitle("暂停报销");
        // exportParam.setType(ExcelType.XSSF);
        // PageUtils<CrmContractProofOfDebtCollectionVO> crmContractProofOfDebtCollectionVOPageUtils = executeService.pageProofOfDebtCollection(query);
        // List<CrmContractProofOfDebtCollectionVO> crmContractProofOfDebtCollectionVOS = Optional.ofNullable(crmContractProofOfDebtCollectionVOPageUtils).map(PageUtils::getList).orElse(Collections.emptyList());
        // try (Workbook workbook = ExcelExportUtil.exportExcel(exportParam, CrmContractProofOfDebtCollectionVO.class, crmContractProofOfDebtCollectionVOS)){
        //     response.addHeader(HttpHeaders.CONTENT_DISPOSITION, WebFilenameUtils.disposition("催款证据.xlsx"));
        //     response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        //     workbook.write(response.getOutputStream());
        // }catch (Exception e){
        //     log.error("导出失败",e);
        //     throw new CrmException("导出失败", ResultEnum.FAIL.getResult(), e);
        // }
    }
    @PostMapping("/deptExport")
    @Operation(summary = "暂停报销导出列表")
    @PreAuthorize(hasPermission = "crm_contract_receivable_sus_re_dept_export")
    public void deptExport(@RequestBody CrmProofOfDebtCollectionQuery query, HttpServletResponse response){

    }



    // 页面需要增加数据范围约束，销售不可见，
    // 销售领导能看所属及管辖部门的，合管负责人、财务专员和财务部经理可以看全部
    @PostMapping("/page")
    @Operation(summary = "暂停报销分页")
    @PreAuthorize(hasPermission = "crm_contract_receivable_sus_re", dataScope = "crm_contract_receivable_sus_re")
    public JsonObject<PageUtils<ContractSuspensionOfReimbursementVO>> page(@RequestBody ContractSuspensionOfReimbursementQuery query){
        Set<String> deptIds = AuthorizeUtil.mergeDeptIds(Set.of(query.getContractOwnerDeptId()));
        query.setContractOwnerDeptIds(deptIds);
        return new JsonObject<>(contractSuspensionOfReimbursementService.page(query));
    }

    @GetMapping("/queryLastPeriods")
    @Operation(summary = "最近一期")
    public JsonObject<LocalDate> queryLastPeriods(){
        return new JsonObject<>(contractSuspensionOfReimbursementService.queryLastPeriods());
    }

    @GetMapping("/queryDeptIdList")
    @Operation(summary = "查询存在暂停报销的部门id列表")
    @PreAuthorize(hasPermission = "crm_contract_receivable_sus_re", dataScope = "crm_contract_receivable_sus_re")
    public JsonObject<Set<String>> queryDeptIdList(@Schema(hidden = true) BaseEntity baseEntity){
        Set<String> personIds = Optional.ofNullable(baseEntity.getDataScopeParam())
                .map(DataScopeParam::getPersonIdList)
                .orElse(Collections.emptySet());
        Map<String, String> pdMap = contractSuspensionOfReimbursementService.queryPersonIdAndDeptId();

        if (CollectionUtils.isNotEmpty(personIds)){
            for (Map.Entry<String, String> entry : pdMap.entrySet()) {
                String personId = entry.getKey();
                if (!personIds.contains(personId)){
                    pdMap.remove(personId);
                }
            }
        }
        Collection<String> deptIds = pdMap.values();
        return new JsonObject<>(new HashSet<>(deptIds));
    }

}
