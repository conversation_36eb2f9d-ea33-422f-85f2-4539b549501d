package com.topsec.crm.flow.core.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.topsec.crm.contract.api.RemoteContractProductSnInService;
import com.topsec.crm.contract.api.entity.response.CrmProductSnInDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnSntVO;
import com.topsec.crm.flow.api.dto.contractreview.request.ContractOwnQuery;
import com.topsec.crm.flow.api.dto.contractreview.sninfo.ContractBorrowForProbationDeviceVO;
import com.topsec.crm.flow.api.dto.contractreview.sninfo.ContractProductSnVO;
import com.topsec.crm.flow.api.dto.targetedinventorypreparation.vo.TargetedInventoryPreparationOwnVO;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.ContractReviewProductOwnMapper;
import com.topsec.crm.flow.core.mapper.ContractReviewRevenueRecognitionMapper;
import com.topsec.crm.flow.core.mapstruct.ContractReviewProductConvertor;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.flow.core.util.ContractUtils;
import com.topsec.crm.framework.common.bean.CrmProjectProductDeviceSnVO;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnVO;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CommonUtils;
import com.topsec.crm.framework.common.util.PageIterator;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.page.PageDomain;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.common.web.page.TableSupport;
import com.topsec.crm.operation.api.RemoteContractReviewConfigService;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractSignCompanyVO;
import com.topsec.crm.product.api.RemoteProductService;
import com.topsec.crm.product.api.entity.CrmProductRowTypeVO;
import com.topsec.crm.product.api.entity.CrmProductSoftHardwareIdentificationVO;
import com.topsec.crm.project.api.client.RemoteBorrowForProbationClient;
import com.topsec.crm.project.api.client.RemoteProjectProductOwnClient;
import com.topsec.crm.project.api.client.RemoteProjectProductSnClient;
import com.topsec.crm.project.api.dto.BorrowForProbationDevicePageQuery;
import com.topsec.crm.project.api.dto.ProjectProductOwnPageQuery;
import com.topsec.crm.project.api.dto.ProjectProductSnPageQuery;
import com.topsec.crm.project.api.entity.CrmBorrowForProbationDeviceVO;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyPriceStatisticsVO;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnUpdateVO;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.BiPredicate;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/16 16:33
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ContractReviewProductOwnServiceImpl extends ServiceImpl<ContractReviewProductOwnMapper, ContractReviewProductOwn> implements ContractReviewProductOwnService {

    private final RemoteProjectProductOwnClient remoteProjectProductOwnClient;

    private final ContractProductSnService contractProductSnService;

    private final RemoteBorrowForProbationClient remoteBorrowForProbationClient;

    private final RemoteProductService remoteProductService;

    private final ContractReviewRevenueRecognitionMapper reviewRevenueRecognitionMapper;

    private final RemoteContractProductSnInService remoteContractProductSnInService;

    private final RemoteContractReviewConfigService remoteContractReviewConfigService;

    private final PerformanceReportService performanceReportService;

    private final RemoteProjectProductSnClient remoteProjectProductSnClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveProductInfo(List<ContractProductOwnDTO> contractProductInfoDTO, String contractId, String projectId) {
        Set<String> contractCompanyIdSet = new HashSet<>();
        // 保存产品相关信息
        contractProductInfoDTO.forEach(contractProductOwnDTO -> {
            Integer keyIndustrySerialNumberCount = contractProductOwnDTO.getKeyIndustrySerialNumberCount();
            if (keyIndustrySerialNumberCount != null && contractProductOwnDTO.getProductNum() != null
                    && !keyIndustrySerialNumberCount.equals(contractProductOwnDTO.getProductNum().intValue())) {
                throw new CrmException("重点行业产品数量和关联序列号数量不一致，请检查");
            }
            contractCompanyIdSet.add(contractProductOwnDTO.getContractCompanyId());
            ContractReviewProductOwn productOwn = HyperBeanUtils.copyProperties(contractProductOwnDTO, ContractReviewProductOwn::new);
            if (!StringUtils.isEmpty(productOwn.getProjectProductOwnId())) {
                productOwn.setContractReviewId(contractId);
                productOwn.setProjectId(projectId);
                productOwn.setSplitOutsourcePrice(contractProductOwnDTO.getSplitOutsourcePrice());
                if (StringUtils.isNotEmpty(contractProductOwnDTO.getProjectParentProductId())){
                    productOwn.setProjectParentProductId(contractProductOwnDTO.getProjectParentProductId());
                }
                save(productOwn);
            }
            Optional.ofNullable(contractProductOwnDTO.getContractProductOwnComponentDTOS())
                    .ifPresent(contractProductOwnComponentDTOS -> contractProductOwnComponentDTOS.forEach(contractProductOwnComponentDTO -> {
                        contractCompanyIdSet.add(contractProductOwnComponentDTO.getContractCompanyId());
                        Integer keyIndustrySerialNumberCountComponent = contractProductOwnComponentDTO.getKeyIndustrySerialNumberCount();
                        if (keyIndustrySerialNumberCountComponent != null && contractProductOwnDTO.getProductNum() != null
                                && !keyIndustrySerialNumberCountComponent.equals(contractProductOwnDTO.getProductNum().intValue())) {
                            throw new CrmException("重点行业产品数量和关联序列号数量不一致，请检查");
                        }
                        ContractReviewProductOwn contractReviewProductOwnComponent = HyperBeanUtils.copyProperties(contractProductOwnComponentDTO, ContractReviewProductOwn::new);
                        contractReviewProductOwnComponent.setContractReviewId(contractId);
                        contractReviewProductOwnComponent.setParentId(productOwn.getId());
                        contractReviewProductOwnComponent.setProjectId(projectId);
                        contractReviewProductOwnComponent.setSplitOutsourcePrice(contractProductOwnComponentDTO.getSplitOutsourcePrice());
                        save(contractReviewProductOwnComponent);
                    }));
            if (contractCompanyIdSet.size() > 1) {
                // 假如不是一个签约单位的 则报错
                throw new CrmException("若有重点行业的产品，不允许与其他普通产品或者其他签约单位的重点行业产品一起下单");
            }
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProductInfo(List<ContractProductOwnDTO> contractProductOwnDTOs, String contractId, String projectId) {
        Set<String> contractCompanyIdSet = new HashSet<>();
        contractProductOwnDTOs.forEach(contractProductOwnDTO -> {
            contractCompanyIdSet.add(contractProductOwnDTO.getContractCompanyId());
            Optional.ofNullable(contractProductOwnDTO.getContractProductOwnComponentDTOS())
                    .ifPresent(contractProductOwnComponentDTOS -> contractProductOwnComponentDTOS.forEach(contractProductOwnComponentDTO -> {
                        contractCompanyIdSet.add(contractProductOwnComponentDTO.getContractCompanyId());
                    }));
            if (contractCompanyIdSet.size() > 1) {
                // 假如不是一个签约单位的 则报错
                throw new CrmException("若有重点行业的产品，不允许与其他普通产品或者其他签约单位的重点行业产品一起下单");
            }
        });
        // 根据contractId 查出这个合同的有效的产品记录
        List<ContractReviewProductOwn> dbRows = list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                .eq(ContractReviewProductOwn::getContractReviewId, contractId)
                .eq(ContractReviewProductOwn::getDelFlag, 0)
        );

        List<ContractReviewProductOwn> dbComponentRows = dbRows.stream().filter(contractReviewProductOwn -> contractReviewProductOwn.getParentId() != null).collect(Collectors.toList());

        List<String> dbRowIds = dbRows.stream().map(ContractReviewProductOwn::getId).collect(Collectors.toList());
        List<String> dbComponentIds = dbComponentRows.stream().map(ContractReviewProductOwn::getId).collect(Collectors.toList());

        // 前端传过来的数据集合
        List<String> projectProductIds = contractProductOwnDTOs.stream().map(ContractProductOwnDTO::getId).collect(Collectors.toList());

        // 平铺出配件 方便计算
        List<String> projectProductComponentIds = contractProductOwnDTOs.stream().map(ContractProductOwnDTO::getContractProductOwnComponentDTOS).flatMap(List::stream)
                .map(ContractProductOwnDTO::getId).collect(Collectors.toList());

        projectProductIds.addAll(projectProductComponentIds);
        // 1.不存在数据库的记录删除，
        List<ContractReviewProductOwn> deleteRows = dbRows.stream()
                .filter(contractReviewProductOwn -> !projectProductIds.contains(contractReviewProductOwn.getId()))
                .collect(Collectors.toList());
        deleteRows.forEach(contractReviewProductOwn -> {
            update(new LambdaUpdateWrapper<ContractReviewProductOwn>().set(ContractReviewProductOwn::getDelFlag, 1)
                    .eq(ContractReviewProductOwn::getId, contractReviewProductOwn.getId())
            );
            // 关联的配件删除
            update(new LambdaUpdateWrapper<ContractReviewProductOwn>()
                    .set(ContractReviewProductOwn::getDelFlag, 1)
                    .eq(ContractReviewProductOwn::getParentId, contractReviewProductOwn.getId())
            );
            // 删除对应的收入确认条款
            reviewRevenueRecognitionMapper.update(null, new LambdaUpdateWrapper<ContractReviewRevenueRecognition>()
                    .eq(ContractReviewRevenueRecognition::getProductOwnId, contractReviewProductOwn.getId())
                    .eq(ContractReviewRevenueRecognition::getContractReviewMainId, contractId)
                    .set(ContractReviewRevenueRecognition::getDelFlag, 1));
        });
        ContractReviewDeliveryService deliveryService = SpringUtil.getBean(ContractReviewDeliveryService.class);
        deliveryService.deleteByProductIds(deleteRows.stream().map(ContractReviewProductOwn::getId).toList(), null);

        // 2.新增产品
        List<ContractProductOwnDTO> insertRows = contractProductOwnDTOs.stream().filter(contractProductOwnDTO -> contractProductOwnDTO.getId() == null).collect(Collectors.toList());
        saveProductInfo(insertRows, contractId, projectId);

        // 3.修改产品
        List<ContractProductOwnDTO> updateRows = contractProductOwnDTOs.stream().filter(contractProductOwnDTO -> dbRowIds.contains(contractProductOwnDTO.getId())).collect(Collectors.toList());
        updateRows.forEach(contractProductOwnDTO -> {
            // 修改产品信息 根据projectProductId
            update(new LambdaUpdateWrapper<ContractReviewProductOwn>()
                    .set(contractProductOwnDTO.getTaxRate() != null, ContractReviewProductOwn::getTaxRate, contractProductOwnDTO.getTaxRate())
                    .set(contractProductOwnDTO.getProductPeriodStart() != null, ContractReviewProductOwn::getProductPeriodStart, contractProductOwnDTO.getProductPeriodStart())
                    .set(contractProductOwnDTO.getProductPeriodEnd() != null, ContractReviewProductOwn::getProductPeriodEnd, contractProductOwnDTO.getProductPeriodEnd())
                    .eq(ContractReviewProductOwn::getId, contractProductOwnDTO.getId())
                    .eq(ContractReviewProductOwn::getContractReviewId, contractId)
                    .eq(ContractReviewProductOwn::getDelFlag, 0)
            );

            // 判断这个产品的配件是否有修改
            List<ContractProductOwnDTO> componentDTOS = contractProductOwnDTO.getContractProductOwnComponentDTOS();
            // 要修改的配件
            List<ContractProductOwnDTO> updateComponentRows = componentDTOS.stream().filter(contractProductOwnComponentDTO -> dbComponentIds.contains(contractProductOwnComponentDTO.getId())).collect(Collectors.toList());
            updateComponentRows.forEach(contractProductOwnComponentDTO -> {
                update(new LambdaUpdateWrapper<ContractReviewProductOwn>()
                        .set(contractProductOwnComponentDTO.getTaxRate() != null, ContractReviewProductOwn::getTaxRate, contractProductOwnComponentDTO.getTaxRate())
                        .set(contractProductOwnComponentDTO.getProductPeriodStart() != null, ContractReviewProductOwn::getProductPeriodStart, contractProductOwnComponentDTO.getProductPeriodStart())
                        .set(contractProductOwnComponentDTO.getProductPeriodEnd() != null, ContractReviewProductOwn::getProductPeriodEnd, contractProductOwnComponentDTO.getProductPeriodEnd())
                        .eq(ContractReviewProductOwn::getId, contractProductOwnComponentDTO.getId())
                        .eq(ContractReviewProductOwn::getParentId, contractProductOwnDTO.getId())
                );
            });
            List<ContractProductOwnDTO> insertComponentRows = componentDTOS.stream().filter(contractProductOwnComponentDTO -> contractProductOwnComponentDTO.getId() == null).collect(Collectors.toList());
            insertComponentRows.forEach(contractProductOwnComponentDTO -> {
                ContractReviewProductOwn contractReviewProductOwnComponent = HyperBeanUtils.copyProperties(contractProductOwnComponentDTO, ContractReviewProductOwn::new);
                contractReviewProductOwnComponent.setContractReviewId(contractId);
                contractReviewProductOwnComponent.setParentId(contractProductOwnDTO.getId());
                contractReviewProductOwnComponent.setSplitOutsourcePrice(contractProductOwnComponentDTO.getSplitOutsourcePrice());
                save(contractReviewProductOwnComponent);
            });
        });
        return true;
    }

    @Override
    public TableDataInfo productInfoByProjectId(String projectId, String contractId) {
        // 这个地方改为分页，拼接序列号进去
        PageDomain pageDomain = TableSupport.buildPageRequest();
        List<CrmProjectProductOwnVO> objEntity = getProjectProductByProjectId(projectId);
        // 过滤已经被添加的
        List<String> projectProductIds = objEntity.stream().map(CrmProjectProductOwnVO::getId).toList();
        if (CollectionUtils.isEmpty(projectProductIds)) {
            return new TableDataInfo();
        }
        List<String> performanceOwnIds = performanceReportService.selectCrmProjectProductOwnByProjectId(projectId);
        // 其他有效的合同已添加的产品 带配件信息
        List<ContractReviewProductOwn> dbOwns = queryProductOwnIdsInContract(projectId, contractId);
        Map<String, ContractReviewProductOwn> dbOwnMap;
        if (StringUtils.isNotEmpty(contractId)) {
            // 修改
            List<ContractReviewProductOwn> ownInCurrContract = list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                    .eq(ContractReviewProductOwn::getContractReviewId, contractId)
                    .eq(ContractReviewProductOwn::getDelFlag, 0));
            dbOwnMap = ownInCurrContract.stream().collect(Collectors.toMap(ContractReviewProductOwn::getProjectProductOwnId, item -> item, (v1, v2) -> v1));
        } else {
            dbOwnMap = new HashMap<>();
        }
        objEntity = filterProductOwn(objEntity, performanceOwnIds, dbOwns);

        // 分页
        List<CrmProjectProductOwnVO> ownVOS = CommonUtils.subListPage(objEntity, pageDomain.getPageSize(), pageDomain.getPageNum());


        // 过滤出重点行业的序列号 批量取出序列号相关的信息
        List<String> sns = ownVOS.stream().flatMap(item -> item.getCrmProjectProductSn().stream())
                .filter(item -> ContractProductSnVO.IMPORTANT_CONTRACT.equals(item.getType())).map(CrmProjectProductSnVO::getPsn).toList();
        Map<String, CrmProductSnInDTO> contractIdMap = new HashMap<>();
        Map<String, ContractSignCompanyVO> signCompanyVOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sns)) {
            JsonObject<List<CrmProductSnInDTO>> resultSn = remoteContractProductSnInService.getBySn(sns);
            // 批量查询出对应的重点行业序列号对应的签约单位信息
            if (resultSn.isSuccess() && resultSn.getObjEntity() != null) {
                contractIdMap.putAll(resultSn.getObjEntity().stream().collect(Collectors.toMap(CrmProductSnInDTO::getSn, item -> item , (oldV, newV) -> newV)));
                List<String> companyNames = resultSn.getObjEntity().stream().map(CrmProductSnInDTO::getContractCompanyName).toList();
                if (CollectionUtils.isEmpty(companyNames)) {
                    throw new CrmException("重点行业序列号信息查询失败!");
                }
                JsonObject<List<ContractSignCompanyVO>> resultCompany = remoteContractReviewConfigService.getBySignCompanyNames(companyNames);
                if (resultCompany.isSuccess() && resultCompany.getObjEntity() != null) {
                    signCompanyVOMap.putAll(resultCompany.getObjEntity().stream().collect(Collectors.toMap(ContractSignCompanyVO::getCompanyName, item -> item)));
                }
            }
        }

        BiPredicate<Map<String, TargetedInventoryPreparationOwnVO>, String> tipPredicate = ContractUtils::tipPredicate;

        Predicate<List<Integer>> channelPredicate = (saleAccess) -> saleAccess.contains(1) || saleAccess.contains(3);

        List<String> recordIds = getAllRecordIdsByOwnVO(ownVOS);
        Map<String, TargetedInventoryPreparationOwnVO> tipByRecordId  = getTipProductByProjectProductOwn(recordIds);

        Map<String, TargetedInventoryPreparationOwnVO> finalTipByRecordId = tipByRecordId;
        List<ContractProductOwnDTO> list = ownVOS.stream().map(crmProjectProductOwnVo -> {
            ContractProductOwnDTO own = ContractReviewProductConvertor.INSTANCE.toOwn(crmProjectProductOwnVo);

            fillContractProductOwnDTO(own, crmProjectProductOwnVo, channelPredicate, dbOwnMap, contractIdMap, signCompanyVOMap, tipPredicate, finalTipByRecordId);

            // 过滤配件
            List<CrmProjectProductOwnVO> crmProjectProductOwnComponent = crmProjectProductOwnVo.getChildren();
            if (CollectionUtils.isEmpty(crmProjectProductOwnComponent)) {
                return own;
            }
            crmProjectProductOwnComponent = filterProductOwn(crmProjectProductOwnComponent, performanceOwnIds, dbOwns);

            own.setContractProductOwnComponentDTOS(ListUtils.emptyIfNull(crmProjectProductOwnComponent).stream().map(contractProductOwnComponentVo -> {
                ContractProductOwnDTO componentDTO = ContractReviewProductConvertor.INSTANCE.toOwn(contractProductOwnComponentVo);
                fillContractProductOwnDTO(componentDTO, contractProductOwnComponentVo, channelPredicate, dbOwnMap, contractIdMap, signCompanyVOMap, tipPredicate, finalTipByRecordId);
                componentDTO.setProjectParentProductId(crmProjectProductOwnVo.getId());
                return componentDTO;
            }).collect(Collectors.toList()));
            return own;
        }).collect(Collectors.toList());
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setList(list);
        tableDataInfo.setTotalCount(objEntity.size());
        return tableDataInfo;
    }

    private Map<String, TargetedInventoryPreparationOwnVO> getTipProductByProjectProductOwn(List<String> recordIds){
        Map<String, TargetedInventoryPreparationOwnVO> tipByRecordId  = new HashMap<>();
        try {
            // 获取专项备货的产品
            ITargetedInventoryPreparationMainService iTargetedInventoryPreparationMainService = SpringUtil.getBean(ITargetedInventoryPreparationMainService.class);
            tipByRecordId = iTargetedInventoryPreparationMainService.getTargetedInventoryPreparationOwnVOMapByRowIds(recordIds);
        } catch (Exception e) {
            // 避免影响合同评审的正常流程
            log.error("获取专项备货产品失败");
        }
        return tipByRecordId;
    }

    /**
     * 根据vo 获取所有的产品id集合，包括配件
     * @return
     */
    private List<String> getAllRecordIdsByOwnVO(List<CrmProjectProductOwnVO> ownVOS) {
        List<String> recordIds = new ArrayList<>();
        ownVOS.forEach(crmProjectProductOwnVo -> {
            recordIds.add(crmProjectProductOwnVo.getId());
            if (CollectionUtils.isNotEmpty(crmProjectProductOwnVo.getChildren())) {
                crmProjectProductOwnVo.getChildren().forEach(crmProjectProductOwnComponent -> {
                    recordIds.add(crmProjectProductOwnComponent.getId());
                });
            }
        });
        return recordIds;
    }

    private List<String> getAllRecordIdsByContractOwnVO(List<ContractProductOwnDTO> ownVOS) {
        List<String> recordIds = new ArrayList<>();
        ownVOS.forEach(item -> {
            recordIds.add(item.getProjectProductOwnId());
            if (CollectionUtils.isNotEmpty(item.getContractProductOwnComponentDTOS())) {
                item.getContractProductOwnComponentDTOS().forEach(component -> {
                    recordIds.add(component.getProjectProductOwnId());
                });
            }
        });
        return recordIds;
    }

    private List<CrmProjectProductOwnVO> filterProductOwn(List<CrmProjectProductOwnVO> objEntity, List<String> performanceOwnIds, List<ContractReviewProductOwn> finalDbOwns){
        // 过滤已经被添加的
        if (CollectionUtils.isNotEmpty(performanceOwnIds)) {
            objEntity = objEntity.stream().filter(item -> !performanceOwnIds.contains(item.getId())).collect(Collectors.toList());
        }
        objEntity = objEntity.stream().filter(own -> finalDbOwns.stream().noneMatch(dbOwn -> dbOwn.getProjectProductOwnId().equals(own.getId()))).collect(Collectors.toList());
        return objEntity;
    }

    private void fillContractProductOwnDTO(ContractProductOwnDTO own,
                                           CrmProjectProductOwnVO crmProjectProductOwnVo,
                                           Predicate<List<Integer>> channelPredicate,
                                           Map<String, ContractReviewProductOwn> dbOwnMap,
                                           Map<String, CrmProductSnInDTO> contractIdMap,
                                           Map<String, ContractSignCompanyVO> signCompanyVOMap,
                                           BiPredicate<Map<String, TargetedInventoryPreparationOwnVO>, String> tipPredicate,
                                           Map<String, TargetedInventoryPreparationOwnVO> tipByRecordId){
        // 判断产品的通路 是否是 1 3
        if (CollectionUtils.isNotEmpty(crmProjectProductOwnVo.getSaleAccess())) {
            own.setIsDirectSalesChannel(channelPredicate.test(crmProjectProductOwnVo.getSaleAccess()));
        } else {
            own.setIsDirectSalesChannel(false);
        }
        if (CollectionUtils.isNotEmpty(crmProjectProductOwnVo.getCrmProjectProductOwnService())) {
            own.setCrmProjectProductOwnService((JSON) JSON.toJSON(crmProjectProductOwnVo.getCrmProjectProductOwnService()));
        }
        if (CollectionUtils.isNotEmpty(crmProjectProductOwnVo.getCrmProjectProductOwnServiceRange())) {
            own.setCrmProjectProductOwnServiceRange((JSON) JSON.toJSON(crmProjectProductOwnVo.getCrmProjectProductOwnServiceRange()));
        }
        own.setIsTipUnValidProduct(tipPredicate.test(tipByRecordId, own.getProjectProductOwnId()));
        TargetedInventoryPreparationOwnVO targetedInventoryPreparationOwnVO = tipByRecordId.get(own.getProjectProductOwnId());
        if (targetedInventoryPreparationOwnVO != null) {
            own.setIsTip(true);
        }
        own.setCrmProjectProductSn(crmProjectProductOwnVo.getCrmProjectProductSn());
        ContractReviewProductOwn productOwn = dbOwnMap.get(own.getProjectProductOwnId());
        if (productOwn != null) {
            initContractProductOwn(productOwn, own);
        }
        // 判断是否有重点行业的序列号 取值
        List<CrmProjectProductSnVO> snVOList = crmProjectProductOwnVo.getCrmProjectProductSn().stream()
                .filter(crmProjectProductSnVO -> ContractProductSnVO.IMPORTANT_CONTRACT.equals(crmProjectProductSnVO.getType())).toList();
        if (CollectionUtils.isNotEmpty(snVOList)) {
            CrmProjectProductSnVO crmProjectProductSnVO = snVOList.get(0);
            CrmProductSnInDTO crmProductSnInDTO = contractIdMap.get(crmProjectProductSnVO.getPsn());
            if (crmProductSnInDTO != null) {
                own.setContractCompanyName(crmProductSnInDTO.getContractCompanyName());
                own.setContractCompanyId(Optional.ofNullable(signCompanyVOMap.get(crmProductSnInDTO.getContractCompanyName())).map(ContractSignCompanyVO::getId).orElse(null));
            }
            own.setKeyIndustrySerialNumberCount(snVOList.size());
        }
    }

    /**
     * 根据物料代码和人员id查询借试用序列号明细
     *
     * @param stuffCode 物料代码
     * @param personId  人员id
     * @return vos
     */
    private List<ContractBorrowForProbationDeviceVO> getDeviceByStuffCode(String stuffCode, String personId) {
        List<CrmBorrowForProbationDeviceVO> crmBorrowForProbationDeviceVOS = PageIterator.iteratePageToList(PageIterator.DEFAULT_PAGE_SIZE, (pageNo, pageSize) -> {
            BorrowForProbationDevicePageQuery pageQuery = new BorrowForProbationDevicePageQuery();
            pageQuery.setStuffCode(stuffCode);
            pageQuery.setPersonId(personId);
            pageQuery.setPageNum(pageNo);
            pageQuery.setPageSize(pageSize);
            return Optional.ofNullable(remoteBorrowForProbationClient.deptBorrowForProbationDevicePage(pageQuery)).map(JsonObject::getObjEntity).orElse(null);
        }, true);
        return HyperBeanUtils.copyListProperties(crmBorrowForProbationDeviceVOS, ContractBorrowForProbationDeviceVO::new);
    }

    @Override
    public BigDecimal amountTotal(String contractMainId) {
        List<ContractProductOwnDTO> contractProductOwnDTOS = this.productInfoByContractId(contractMainId, true);
        // 计算金额
        return contractProductOwnDTOS.stream().map(this::amountOne).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public ContractProductOwnDTO getByOwnId(String id) {
        ContractReviewProductOwn own = getById(id);
        if (own.getDelFlag()) {
            return null;
        }
        // 假如是配件 则要先查出主产品
        if (own.getParentId() != null) {
            ContractReviewProductOwn parent = getById(own.getParentId());
            ContractProductOwnDTO parentDTO = buildProductOwn(Collections.singletonList(own), own.getContractReviewId(), false).get(0);
            if (parentDTO == null || CollectionUtils.isEmpty(parentDTO.getContractProductOwnComponentDTOS())) {
                return new ContractProductOwnDTO();
            }
            return parentDTO.getContractProductOwnComponentDTOS().stream().filter(item -> item.getId().equals(id)).findFirst().orElse(null);
        }
        return buildProductOwn(Collections.singletonList(own), own.getContractReviewId(), true).get(0);
    }

    public List<ContractProductOwnDTO> getByOwnIdBatch(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<ContractReviewProductOwn> list = list(new LambdaQueryWrapper<ContractReviewProductOwn>().in(ContractReviewProductOwn::getId, ids)
                .eq(ContractReviewProductOwn::getDelFlag, false));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return buildProductOwnTile(list, list.get(0).getContractReviewId(), true);
    }

    private void initContractProductOwn(ContractReviewProductOwn own, ContractProductOwnDTO contractProductOwnDTO) {
        if (contractProductOwnDTO == null) {
            return;
        }
        contractProductOwnDTO.setId(own.getId());
        contractProductOwnDTO.setParentId(own.getParentId());
        contractProductOwnDTO.setTaxRate(own.getTaxRate());
        contractProductOwnDTO.setProductPeriodStart(own.getProductPeriodStart());
        contractProductOwnDTO.setProductPeriodEnd(own.getProductPeriodEnd());
        contractProductOwnDTO.setProjectParentProductId(own.getProjectParentProductId());
        contractProductOwnDTO.setSplitOutsourcePrice(own.getSplitOutsourcePrice());
        contractProductOwnDTO.setTaxLoss(own.getTaxLoss());
        contractProductOwnDTO.setSoftHardIdent(own.getSoftHardIdent());
        // 有负行 这个地方不能用项目的
        if (own.getProductNum() != null) {
            contractProductOwnDTO.setProductNum(own.getProductNum());
        }
        contractProductOwnDTO.setContractReviewId(own.getContractReviewId());
        contractProductOwnDTO.setLabel(own.getLabel());
        contractProductOwnDTO.setReturnExchangeProcessInstanceId(own.getReturnExchangeProcessInstanceId());
        contractProductOwnDTO.setBarterParentId(own.getBarterParentId());
        contractProductOwnDTO.setRowType(own.getRowType());
        contractProductOwnDTO.setReturnExchangeProductId(own.getReturnExchangeProductId());
        BigDecimal dealPrice = contractProductOwnDTO.getDealPrice();
        BigDecimal finalPrice = contractProductOwnDTO.getFinalPrice();
        BigDecimal quotedPrice = contractProductOwnDTO.getQuotedPrice();
        long productNum = own.getProductNum() != null ? own.getProductNum() : 0;
        contractProductOwnDTO.setDealTotalPrice(dealPrice.multiply(BigDecimal.valueOf(productNum)));
        contractProductOwnDTO.setFinalTotalPrice(finalPrice.multiply(BigDecimal.valueOf(productNum)));
        contractProductOwnDTO.setQuotedTotalPrice(quotedPrice.multiply(BigDecimal.valueOf(productNum)));
    }

    @Override
    public List<ContractProductOwnDTO> convertToProductOwn(List<ContractProductOwnDTO> contractProductOwnDTOS, String projectId, boolean isUpdate) {
        if (CollectionUtils.isEmpty(contractProductOwnDTOS)) {
            return Collections.emptyList();
        }
        // 产品id集合
        List<String> productIds = contractProductOwnDTOS.stream().map(ContractProductOwnDTO::getProjectProductOwnId).collect(Collectors.toList());
        Set<String> list = contractProductOwnDTOS.stream().map(ContractProductOwnDTO::getParentId).filter(Objects::nonNull).collect(Collectors.toSet());
        productIds.addAll(list);
        // 未释放的情况
        Set<String> collect = contractProductOwnDTOS.stream().map(ContractProductOwnDTO::getProjectParentProductId).filter(Objects::nonNull).collect(Collectors.toSet());
        productIds.addAll(collect);
        // 取项目提供的接口，根据项目id和产品id集合，获取产品属性
        String currentPersonId;
        try {
            currentPersonId = UserInfoHolder.getCurrentPersonId();
        } catch (Exception e) {
            currentPersonId = "";
        }
        JsonObject<List<CrmProjectProductOwnVO>> result = remoteProjectProductOwnClient.productByRecordIds(projectId, currentPersonId, productIds);
        if (!result.isSuccess()) {
            log.error("获取项目产品失败! {}", result.getMessage());
            throw new CrmException("获取项目产品失败!");
        }
        List<CrmProjectProductOwnVO> crmProjectProductOwnVos = result.getObjEntity();
        // 转换成map
        Map<String, CrmProjectProductOwnVO> projectProductByProductId = getStringCrmProjectProductOwnVOMap(result);
        // 转换赋值给dto
        return contractProductOwnDTOS.stream().map(contractProductInfo -> {
            CrmProjectProductOwnVO crmProjectProductOwnVo = projectProductByProductId.get(contractProductInfo.getProjectProductOwnId());
            ContractProductOwnDTO own = ContractReviewProductConvertor.INSTANCE.toOwn(crmProjectProductOwnVo);
            if (own == null) {
                throw new CrmException("项目产品不存在");
            }
            // 合同的自定义的字段
            own.setTaxRate(contractProductInfo.getTaxRate());
            own.setProductPeriodStart(contractProductInfo.getProductPeriodStart());
            own.setProductPeriodEnd(contractProductInfo.getProductPeriodEnd());
            if (isUpdate) {
                own.setId(contractProductInfo.getId());
            }
            // 把项目中的父产品id赋值出来
            own.setProjectParentProductId(contractProductInfo.getProjectParentProductId());

            List<CrmProjectProductOwnVO> crmProjectProductOwnComponent = crmProjectProductOwnVo.getChildren();
            Map<String, CrmProjectProductOwnVO> projectProductOwnComponentVoMap = ListUtils.emptyIfNull(crmProjectProductOwnComponent).stream().collect(Collectors.toMap(CrmProjectProductOwnVO::getId, (crmProjectProductOwnComponentVo) -> crmProjectProductOwnComponentVo));
            if (contractProductInfo.getContractProductOwnComponentDTOS() == null) {
                contractProductInfo.setContractProductOwnComponentDTOS(Collections.emptyList());
            }
            Optional.ofNullable(contractProductInfo.getContractProductOwnComponentDTOS())
                    .ifPresent(components -> own.setContractProductOwnComponentDTOS(
                            components.stream().map(contractProductOwnComponentDTO -> {
                                ContractProductOwnDTO component = ContractReviewProductConvertor.INSTANCE.toOwn(
                                        projectProductOwnComponentVoMap.get(contractProductOwnComponentDTO.getProjectProductOwnId()));
                                if (isUpdate) {
                                    component.setId(contractProductOwnComponentDTO.getId());
                                }
                                component.setTaxRate(contractProductOwnComponentDTO.getTaxRate());
                                component.setProductPeriodStart(contractProductOwnComponentDTO.getProductPeriodStart());
                                component.setProductPeriodEnd(contractProductOwnComponentDTO.getProductPeriodEnd());
                                return component;
                            }).collect(Collectors.toList())
                    ));
            return own;
        }).collect(Collectors.toList());
    }


    @Override
    public List<ContractProductOwnDTO> productOwnInfoTileByContractId(String contractId, Boolean needProductInfo) {
        List<ContractReviewProductOwn> owns = list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                .eq(ContractReviewProductOwn::getContractReviewId, contractId)
                .eq(ContractReviewProductOwn::getDelFlag, 0));
        // 平铺
        return buildProductOwnTile(owns, contractId, needProductInfo);
    }

    @Override
    public Boolean hasUnDistribution(List<ContractProductOwnDTO> owns) {
        if (CollectionUtils.isEmpty(owns)) {
            return false;
        }
        return owns.stream().anyMatch(own -> {
            List<Integer> saleAccess = own.getSaleAccess();
            // 判断每个产品的通路是不是包含2 包含2 就是分销
            return saleAccess.contains(2);
        });
    }

    // 此方法是复用了树逻辑，拆成平铺的，效率有问题，优化成直接从数据库查平铺数据
    @Override
    @Deprecated
    public List<ContractProductOwnDTO> tileProduct(List<ContractProductOwnDTO> contractProductOwnDTOS) {
        // 平铺
        contractProductOwnDTOS.addAll(ListUtils.emptyIfNull(contractProductOwnDTOS).stream().flatMap(contractProductOwnDTO -> contractProductOwnDTO.getContractProductOwnComponentDTOS().stream()).collect(Collectors.toList()));
        contractProductOwnDTOS.forEach(contractProductOwnDTO -> {
            contractProductOwnDTO.setContractProductOwnComponentDTOS(null);
        });
        return contractProductOwnDTOS;
    }

    @Override
    public TableDataInfo pageOwnInfoByContractId(String contractId) {
        List<ContractReviewProductOwn> owns = list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                .eq(ContractReviewProductOwn::getContractReviewId, contractId)
                .isNull(ContractReviewProductOwn::getParentId)
                .eq(ContractReviewProductOwn::getDelFlag, 0));
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setTotalCount(owns.size());
        List<ContractProductOwnDTO> ownProducts = buildProductOwn(owns, contractId, true);
        tableDataInfo.setList(ownProducts);
        return tableDataInfo;
    }

    @Override
    public List<ContractProductOwnDTO> initOtherDetail(List<ContractProductOwnDTO> contractProductOwnDTOS, String contractId, boolean isTile) {
        // 序列号
        List<ContractProductSn> contractProductSns = contractProductSnService.selectContractProductSn(contractId);
        // 专项备货
        List<String> recordIds = getAllRecordIdsByContractOwnVO(contractProductOwnDTOS);
        Map<String, TargetedInventoryPreparationOwnVO> tipProductByProjectProductOwn = getTipProductByProjectProductOwn(recordIds);
        // 根据projectProductId分组
        Map<String, List<ContractProductSn>> serialByProjectProductId = ListUtils.emptyIfNull(contractProductSns).stream().collect(Collectors.groupingBy(ContractProductSn::getProjectRecordId));
        contractProductOwnDTOS.forEach(contractProductOwnDTO -> {
            contractProductOwnDTO.setSerialDetail(serialByProjectProductId.getOrDefault(contractProductOwnDTO.getProjectProductOwnId(), Collections.emptyList()).stream().map(ContractProductSn::getSn).collect(Collectors.joining(",")));
            contractProductOwnDTO.setIsTip(tipProductByProjectProductOwn.containsKey(contractProductOwnDTO.getProjectProductOwnId()));
            if (!isTile) {
                ListUtils.emptyIfNull(contractProductOwnDTO.getContractProductOwnComponentDTOS()).forEach(contractProductOwnComponentDTO -> {
                    contractProductOwnDTO.setIsTip(tipProductByProjectProductOwn.containsKey(contractProductOwnComponentDTO.getProjectProductOwnId()));
                    contractProductOwnComponentDTO.setSerialDetail(serialByProjectProductId.getOrDefault(contractProductOwnComponentDTO.getProjectProductOwnId(), Collections.emptyList()).stream().map(ContractProductSn::getSn).collect(Collectors.joining(",")));
                });
            }
        });

        return contractProductOwnDTOS;
    }

    @Override
    public TableDataInfo productOwnSnByContractId(String contractId) {
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setTotalCount(0);
        tableDataInfo.setList(new ArrayList<>());
        List<ContractReviewProductOwn> productOwns = list(new QueryWrapper<ContractReviewProductOwn>().lambda()
                .eq(ContractReviewProductOwn::getContractReviewId, contractId).eq(ContractReviewProductOwn::getDelFlag, 0).isNull(ContractReviewProductOwn::getParentId));
        if (CollectionUtils.isEmpty(productOwns)) {
            return tableDataInfo;
        }
        List<String> projectProductOwnIds = productOwns.stream().map(ContractReviewProductOwn::getProjectProductOwnId).collect(Collectors.toList());
        String currentPersonId;
        try {
            currentPersonId = UserInfoHolder.getCurrentPersonId();
        } catch (Exception e) {
            currentPersonId = "";
        }
        JsonObject<List<CrmProjectProductOwnVO>> result = remoteProjectProductOwnClient.productByRecordIds(productOwns.get(0).getProjectId(), currentPersonId, projectProductOwnIds);
        if (!result.isSuccess()) {
            log.error("获取项目产品失败! {}", result.getMessage());
            return tableDataInfo;
        }
        List<CrmProjectProductOwnVO> productOwnVOS = result.getObjEntity();
        Map<String, CrmProjectProductOwnVO> map = productOwnVOS.stream()
                .collect(Collectors.toMap(CrmProjectProductOwnVO::getId, (crmProjectProductOwnVo -> crmProjectProductOwnVo)));
        List<ContractProductOwnSntVO> productOwnSntVOS = productOwns.stream().map(ows -> {
            ContractProductOwnSntVO productOwnSntVO = new ContractProductOwnSntVO();
            productOwnSntVO.setContractProductOwnId(ows.getProjectProductOwnId());
            productOwnSntVO.setContractReviewId(ows.getContractReviewId());
            productOwnSntVO.setPnCode(map.get(ows.getProjectProductOwnId()).getPnCode());
            productOwnSntVO.setProductName(map.get(ows.getProjectProductOwnId()).getProductName());
            productOwnSntVO.setProductNum(map.get(ows.getProjectProductOwnId()).getProductNum());
            productOwnSntVO.setStuffCode(map.get(ows.getProjectProductOwnId()).getStuffCode());
            productOwnSntVO.setDealPrice(map.get(ows.getProjectProductOwnId()).getDealPrice());
            productOwnSntVO.setListSn(contractProductSnService.selectContractProductSn(ows.getId()).stream().map(productSn -> {
                String sn = productSn.getSn();
                return sn;
            }).collect(Collectors.toList()));
            return productOwnSntVO;
        }).collect(Collectors.toList());
        tableDataInfo.setTotalCount(new PageInfo(productOwnSntVOS).getTotal());
        tableDataInfo.setList(productOwnSntVOS);
        return tableDataInfo;
    }

    @Override
    public TableDataInfo productOwnSnByContractId(ContractProductOwnSntVO contractProductOwnSntVOP) {
        TableDataInfo tableDataInfo = new TableDataInfo();
        String contractId = contractProductOwnSntVOP.getContractId();
        List<ContractProductOwnDTO> owns = productOwnInfoTileByContractId(contractId, true);

        if (CollectionUtils.isEmpty(owns)) {
            return tableDataInfo;
        }
        Map<String, ContractProductOwnDTO> contractProductOwnDTOMap = owns.stream()
                .collect(Collectors.toMap(ContractProductOwnDTO::getProjectProductOwnId, (item -> item), (newV, oldV) -> newV));

        ProjectProductSnPageQuery query = new ProjectProductSnPageQuery();
        query.setProductRecordIds(contractProductOwnDTOMap.keySet());
        query.setProjectId(owns.get(0).getProjectId());
        query.setPsn(contractProductOwnSntVOP.getSn());
        query.setStuffCode(contractProductOwnSntVOP.getStuffCode());
        query.setType(contractProductOwnSntVOP.getSource());
        PageDomain pageDomain = TableSupport.buildPageRequest();
        query.setPageNum(pageDomain.getPageNum());
        query.setPageSize(pageDomain.getPageSize());
        JsonObject<PageUtils<CrmProjectProductDeviceSnVO>> result = remoteProjectProductSnClient.pageProductSn(query);


        if (!result.isSuccess()) {
            log.error("获取项目序列号产品失败! {}", result.getMessage());
            return tableDataInfo;
        }
        PageUtils<CrmProjectProductDeviceSnVO> objEntity = result.getObjEntity();
        if (CollectionUtils.isEmpty(objEntity.getList())) {
            return tableDataInfo;
        }
        tableDataInfo.setTotalCount(objEntity.getTotalCount());
        List<ContractProductOwnSntVO> list = objEntity.getList().stream().map(item -> {
            ContractProductOwnSntVO contractProductOwnSntVO = new ContractProductOwnSntVO();
            contractProductOwnSntVO.setSn(item.getPsn());
            contractProductOwnSntVO.setSource(item.getType());
            ContractProductOwnDTO ownDTO = contractProductOwnDTOMap.get(item.getRecordId());
            if (null != ownDTO) {
                contractProductOwnSntVO.setStuffCode(ownDTO.getStuffCode());
                contractProductOwnSntVO.setProductName(ownDTO.getProductName());
                contractProductOwnSntVO.setPnCode(ownDTO.getPnCode());
                contractProductOwnSntVO.setDealPrice(ownDTO.getDealPrice());
            }
            return contractProductOwnSntVO;
        }).toList();
        tableDataInfo.setList(list);
        return tableDataInfo;
    }

    /**
     * 将合同下的产品转换成dto
     *
     * @param contractMainId 合同id
     * @return dto
     */
    @Override
    public List<ContractProductOwnDTO> productInfoByContractId(String contractMainId, Boolean needProductInfo) {
        // 查询所有产品的集合
        List<ContractReviewProductOwn> owns = list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                .eq(ContractReviewProductOwn::getContractReviewId, contractMainId)
                .isNull(ContractReviewProductOwn::getParentId)
                .eq(ContractReviewProductOwn::getDelFlag, 0));
        return buildProductOwn(owns, contractMainId, needProductInfo);
    }

    @Override
    public List<ContractProductOwnDTO> buildProductOwn(List<ContractReviewProductOwn> owns, String contractMainId, Boolean needProductInfo) {
        if (CollectionUtils.isEmpty(owns)) {
            return Collections.emptyList();
        }
        if (needProductInfo) {
            // 需要项目中的产品信息才取
            Set<String> projectProductOwnIds = new HashSet<>();
            owns.forEach(own -> {
                projectProductOwnIds.add(own.getProjectProductOwnId());
                if (StringUtils.isNotEmpty(own.getProjectParentProductId())) {
                    projectProductOwnIds.add(own.getProjectParentProductId());
                }
            });

            String currentPersonId;
            try {
                currentPersonId = UserInfoHolder.getCurrentPersonId();
            } catch (Exception e) {
                currentPersonId = "";
            }
            JsonObject<List<CrmProjectProductOwnVO>> result = remoteProjectProductOwnClient.productByRecordIds(owns.get(0).getProjectId(), currentPersonId , new ArrayList<>(projectProductOwnIds));
            if (!result.isSuccess()) {
                log.error("获取项目产品失败! {}", result.getMessage());
                return Collections.emptyList();
            }
            // 转换成Map
            Map<String, CrmProjectProductOwnVO> projectProductByProductId = getStringCrmProjectProductOwnVOMap(result);

            Map<String, ReturnExchangeProduct> returnExchangeProductMap = new HashMap<>();
            // 取退的产品的序列号 看看有没有退货的
            Set<String> returnExchangeProductId = owns.stream().filter(item -> "退货".equals(item.getLabel())).map(ContractReviewProductOwn::getReturnExchangeProductId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(returnExchangeProductId)) {
                ReturnExchangeProductService productService = SpringUtil.getBean(ReturnExchangeProductService.class);
                List<ReturnExchangeProduct> oldByRecordIdBatch = productService.listByReturnExchangeId(new ArrayList<>(returnExchangeProductId));
                returnExchangeProductMap = ListUtils.emptyIfNull(oldByRecordIdBatch).stream().collect(Collectors.toMap(ReturnExchangeProduct::getId, i -> i, (newV, oldV) -> newV));
            }


            Map<String, ReturnExchangeProduct> finalReturnExchangeProductMap = returnExchangeProductMap;
            List<ContractProductOwnDTO> ownDTOS = owns.stream().map(own -> {
                CrmProjectProductOwnVO crmProjectProductOwnVo = projectProductByProductId.get(own.getProjectProductOwnId());
                // 转换dto
                ContractProductOwnDTO contractProductOwnDTO = ContractReviewProductConvertor.INSTANCE.toOwn(crmProjectProductOwnVo);
                if (contractProductOwnDTO == null) return new ContractProductOwnDTO();

                // 合同的自定义的字段
                initContractProductOwn(own, contractProductOwnDTO);
                if ("退货".equals(contractProductOwnDTO.getLabel())) {
                    ReturnExchangeProduct returnExchangeProduct = finalReturnExchangeProductMap.get(own.getReturnExchangeProductId());
                    List<CrmProjectProductSnVO> psn = returnExchangeProduct.getPsn();
                    ListUtils.emptyIfNull(psn).forEach(item -> {
                        item.setType("退换货");
                    });
                    contractProductOwnDTO.setCrmProjectProductSn(psn);
                }
                initProductOwnService(contractProductOwnDTO, crmProjectProductOwnVo);

                // 查出这个产品的配件
                List<ContractReviewProductOwn> components = list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                        .eq(ContractReviewProductOwn::getDelFlag, 0)
                        .eq(ContractReviewProductOwn::getParentId, own.getId())
                        .eq(ContractReviewProductOwn::getContractReviewId, contractMainId));

                List<ContractProductOwnDTO> componentDTOS = ListUtils.emptyIfNull(components).stream().map(component -> {
                    ContractProductOwnDTO componentDTO = ContractReviewProductConvertor.INSTANCE.toOwn(projectProductByProductId.get(component.getProjectProductOwnId()));
                    initContractProductOwn(component, componentDTO);
                    initProductOwnService(componentDTO, projectProductByProductId.get(component.getProjectProductOwnId()));
                    return componentDTO;
                }).collect(Collectors.toList());
                contractProductOwnDTO.setContractProductOwnComponentDTOS(componentDTOS);

                return contractProductOwnDTO;
            }).collect(Collectors.toList());
            // 毛利取值改变
            ContractReviewMainService mainService = SpringUtil.getBean(ContractReviewMainService.class);
            ContractReviewMainFlowLaunchDTO launchDTO = new ContractReviewMainFlowLaunchDTO();
            launchDTO.setContractProductOwnDTOS(ownDTOS);
            CrmProjectDirectlyPriceStatisticsVO priceStatisticsVO = mainService.initContractPriceStatistics(launchDTO, false);
            mainService.initRowGross(priceStatisticsVO, ownDTOS, null);
            return ownDTOS;
        } else {
            return owns.stream().map(own -> {
                // 转换dto
                ContractProductOwnDTO contractProductOwnDTO = HyperBeanUtils.copyProperties(own, ContractProductOwnDTO::new);

                // 查出这个产品的配件
                List<ContractReviewProductOwn> components = list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                        .eq(ContractReviewProductOwn::getDelFlag, 0)
                        .eq(ContractReviewProductOwn::getParentId, own.getId())
                        .eq(ContractReviewProductOwn::getContractReviewId, contractMainId));

                List<ContractProductOwnDTO> componentDTOS = ListUtils.emptyIfNull(components).stream().map(component -> HyperBeanUtils.copyProperties(component, ContractProductOwnDTO::new)).collect(Collectors.toList());
                contractProductOwnDTO.setContractProductOwnComponentDTOS(componentDTOS);
                return contractProductOwnDTO;
            }).collect(Collectors.toList());
        }
    }

    @Override
    public List<ContractProductOwnDTO> buildProductOwnTile(List<ContractReviewProductOwn> owns, String contractMainId,  Boolean needProductInfo) {
        if (CollectionUtils.isEmpty(owns)) {
            return Collections.emptyList();
        }
        if (needProductInfo) {
            // 需要项目中的产品信息才取
            Set<String> projectProductOwnIds = owns.stream().map(ContractReviewProductOwn::getProjectProductOwnId).collect(Collectors.toSet());
            // 如果有配件，需要拿到主产品的id才能查到
            List<String> parentIds = owns.stream().map(ContractReviewProductOwn::getParentId).filter(Objects::nonNull).toList();
            List<ContractReviewProductOwn> parentOwns = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(parentIds)) {
                parentOwns = list(new LambdaQueryWrapper<ContractReviewProductOwn>().eq(ContractReviewProductOwn::getDelFlag, false)
                    .in(ContractReviewProductOwn::getId, parentIds));
            }
            ListUtils.emptyIfNull(parentOwns).forEach(parentOwn -> {
                // 用set去重
                projectProductOwnIds.add(parentOwn.getProjectProductOwnId());
            });
            // 未释放的情况
            owns.forEach(own -> {
                if (StringUtils.isNotEmpty(own.getProjectParentProductId())) {
                    projectProductOwnIds.add(own.getProjectParentProductId());
                }
            });
            String currentPersonId;
            try {
                currentPersonId = UserInfoHolder.getCurrentPersonId();
            } catch (Exception e) {
                currentPersonId = "";
            }
            JsonObject<List<CrmProjectProductOwnVO>> result = remoteProjectProductOwnClient.productByRecordIds(owns.get(0).getProjectId(), currentPersonId, new ArrayList<>(projectProductOwnIds));
            if (!result.isSuccess()) {
                log.error("获取项目产品失败! {}", result.getMessage());
                return Collections.emptyList();
            }
            Map<String, CrmProjectProductOwnVO> projectProductByProductId = getStringCrmProjectProductOwnVOMap(result);

            return owns.stream().map(own -> {
                CrmProjectProductOwnVO crmProjectProductOwnVo = projectProductByProductId.get(own.getProjectProductOwnId());
                // 转换dto
                ContractProductOwnDTO contractProductOwnDTO = ContractReviewProductConvertor.INSTANCE.toOwn(crmProjectProductOwnVo);
                // 合同的自定义的字段
                initContractProductOwn(own, contractProductOwnDTO);
                initProductOwnService(contractProductOwnDTO, crmProjectProductOwnVo);
                return contractProductOwnDTO;
            }).collect(Collectors.toList());
        } else {
            return owns.stream().map(own -> HyperBeanUtils.copyProperties(own, ContractProductOwnDTO::new)).collect(Collectors.toList());
        }
    }

    private void initProductOwnService(ContractProductOwnDTO contractProductOwnDTO, CrmProjectProductOwnVO crmProjectProductOwnVo) {
        if (contractProductOwnDTO == null || crmProjectProductOwnVo == null) {
            return;
        }
        if (CollectionUtils.isNotEmpty(crmProjectProductOwnVo.getCrmProjectProductOwnService())) {
            contractProductOwnDTO.setCrmProjectProductOwnService((JSON)JSON.toJSON(crmProjectProductOwnVo.getCrmProjectProductOwnService()));
        }
        if (CollectionUtils.isNotEmpty(crmProjectProductOwnVo.getCrmProjectProductOwnServiceRange())) {
            contractProductOwnDTO.setCrmProjectProductOwnServiceRange((JSON) JSON.toJSON(crmProjectProductOwnVo.getCrmProjectProductOwnServiceRange()));
        }
    }

    private Map<String, CrmProjectProductOwnVO> getStringCrmProjectProductOwnVOMap(JsonObject<List<CrmProjectProductOwnVO>> result) {
        List<CrmProjectProductOwnVO> crmProjectProductOwnVos = result.getObjEntity();

        // 转换成Map
        Map<String, CrmProjectProductOwnVO> projectProductByProductId = new HashMap<>();
        crmProjectProductOwnVos.forEach(crmProjectProductOwnVo -> {
            projectProductByProductId.put(crmProjectProductOwnVo.getId(), crmProjectProductOwnVo);
            if (CollectionUtils.isNotEmpty(crmProjectProductOwnVo.getChildren())) {
                crmProjectProductOwnVo.getChildren().forEach(child -> projectProductByProductId.put(child.getId(), child));
         }});
        return projectProductByProductId;
    }

    // 项目中可关联的序列号赋值 循环rpc存在效率问题 暂时保留 不删除
    @Deprecated
    private void initProjectDevice(List<ContractProductOwnDTO> owns) {
        ListUtils.emptyIfNull(owns).forEach(own -> {
            own.setDeviceVOS(getDeviceByStuffCode(own.getStuffCode(), UserInfoHolder.getCurrentPersonId()));
            if (CollectionUtils.isNotEmpty(own.getContractProductOwnComponentDTOS())) {
                own.getContractProductOwnComponentDTOS().forEach(component -> {
                    component.setDeviceVOS(getDeviceByStuffCode(component.getStuffCode(), UserInfoHolder.getCurrentPersonId()));
                });
            }
        });
    }

    @Override
    public boolean deleteProductProjectNotExist(String contractId, String projectId) {
        List<ContractReviewProductOwn> owns = list(new LambdaQueryWrapper<ContractReviewProductOwn>().eq(ContractReviewProductOwn::getDelFlag, 0)
                // 忽略退换货的
                .ne(ContractReviewProductOwn::getLabel, "退货")
                .eq(ContractReviewProductOwn::getContractReviewId, contractId));
        List<CrmProjectProductOwnVO> vos = getProjectProductByProjectId(projectId);

        List<String> projectOwnProductIds = new ArrayList<>();
        ListUtils.emptyIfNull(vos).forEach(own -> {
            projectOwnProductIds.add(own.getId());
            if (!CollectionUtils.isEmpty(own.getChildren())) {
                own.getChildren().forEach(child -> projectOwnProductIds.add(child.getId()));
            }
        });

        // 不存在项目中的产品需要删除
        ListUtils.emptyIfNull(owns).forEach(own -> {
            if (!ListUtils.emptyIfNull(projectOwnProductIds).contains(own.getProjectProductOwnId())) {
                own.setDelFlag(true);
                updateById(own);
            reviewRevenueRecognitionMapper.update(null, new LambdaUpdateWrapper<ContractReviewRevenueRecognition>()
                    .eq(ContractReviewRevenueRecognition::getProductOwnId, own.getId())
                    .eq(ContractReviewRevenueRecognition::getContractReviewMainId, contractId)
                    .set(ContractReviewRevenueRecognition::getDelFlag, 1));
            }
        });
        return true;
    }

    @Override
    public boolean writeBackProject(String contractId, Integer status) {
        // 查出这个合同的所有产品
        List<ContractReviewProductOwn> owns = list(new LambdaQueryWrapper<ContractReviewProductOwn>().eq(ContractReviewProductOwn::getDelFlag, 0)
                .eq(ContractReviewProductOwn::getContractReviewId, contractId));
        List<CrmProjectProductOwnUpdateVO> crmProjectProductOwnUpdateVOS = ListUtils.emptyIfNull(owns).stream().map(own -> {
            CrmProjectProductOwnUpdateVO crmProjectProductOwnUpdateVO = new CrmProjectProductOwnUpdateVO();
            crmProjectProductOwnUpdateVO.setId(own.getProjectProductOwnId());
            crmProjectProductOwnUpdateVO.setReportStatus(status);
            return crmProjectProductOwnUpdateVO;
        }).collect(Collectors.toList());
        JsonObject<Boolean> result = remoteProjectProductOwnClient.writeBack(crmProjectProductOwnUpdateVOS);
        if (!result.isSuccess() || !result.getObjEntity()) {
            throw new CrmException("写入项目产品已发起合同评审状态失败");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changePeriod(List<ContractProductOwnDTO> contractProductOwnDTOS) {
        // 保修时间校验
        BiPredicate<Integer, Integer> inTimePredicate = (diff, total) -> Math.abs(diff - total) <= 3;
        boolean b = ListUtils.emptyIfNull(contractProductOwnDTOS).stream().allMatch(contractProductOwnDTO -> {
            int diff = (int) ChronoUnit.DAYS.between(contractProductOwnDTO.getProductPeriodStart(),contractProductOwnDTO.getProductPeriodEnd());
            BigDecimal productPeriod = contractProductOwnDTO.getProductPeriod();
            productPeriod = productPeriod == null ? BigDecimal.ZERO : productPeriod;
            BigDecimal periodDay = productPeriod.multiply(new BigDecimal(ContractReviewMainServiceImpl.ONE_MONTH)).setScale(0, RoundingMode.HALF_UP);
            int total = periodDay.intValue();
            return inTimePredicate.test(diff, total);
        });
        if (!b) {
            throw new CrmException("保修期与保修月份不符");
        }
        List<ContractReviewProductOwn> owns = HyperBeanUtils.copyListProperties(contractProductOwnDTOS, ContractReviewProductOwn::new);
        return updateBatchById(owns);
    }

    @Override
    public List<ContractProductOwnDTO> queryOwnByCondition(ContractOwnQuery contractOwnQuery) {
        List<ContractReviewProductOwn> list = list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                .in(CollectionUtils.isNotEmpty(contractOwnQuery.getStuffCodes()), ContractReviewProductOwn::getStuffCode, contractOwnQuery.getStuffCodes())
                .eq(ContractReviewProductOwn::getContractReviewId, contractOwnQuery.getContractId())
                .eq(ContractReviewProductOwn::getDelFlag, 0));
        return buildProductOwnTile(list, contractOwnQuery.getContractId(), true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initRowType(List<ContractProductOwnDTO> owns, String signCompany) {
        // 构建参数
        List<CrmProductSoftHardwareIdentificationVO> query = ListUtils.emptyIfNull(owns).stream().map(own -> {
            CrmProductSoftHardwareIdentificationVO identificationVO = new CrmProductSoftHardwareIdentificationVO();
            identificationVO.setProductId(own.getProductId());
            identificationVO.setContractSignCompanyId(signCompany);
            return identificationVO;
        }).collect(Collectors.toList());
        JsonObject<List<CrmProductRowTypeVO>> result = remoteProductService.selectProductsRowType(query);
        if (!result.isSuccess()) {
            throw new CrmException("获取产品行类型失败");
        }
        Map<String, CrmProductRowTypeVO> collect = ListUtils.emptyIfNull(result.getObjEntity()).stream().collect(Collectors.toMap(CrmProductRowTypeVO::getProductId, v -> v, (i1, i2) -> i1));

        List<ContractReviewProductOwn> updateRows = ListUtils.emptyIfNull(owns).stream().map(own -> {
            ContractReviewProductOwn productOwn = new ContractReviewProductOwn();
            productOwn.setId(own.getId());
            productOwn.setRowType(Optional.ofNullable(collect.get(own.getProductId())).map(CrmProductRowTypeVO::getRowType).orElse(null));
            return productOwn;
        }).filter(productOwn -> productOwn.getRowType() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateRows)) {
            return true;
        }
        return updateBatchById(updateRows);
    }

    @Override
    public List<CrmProjectProductOwnVO> getProjectProductByProjectId(String projectId) {
        return ListUtils.emptyIfNull(PageIterator.iteratePageToList(PageIterator.DEFAULT_PAGE_SIZE, (pageNo, pageSize) -> {
            ProjectProductOwnPageQuery ownPageQuery = new ProjectProductOwnPageQuery();
            ownPageQuery.setProjectId(projectId);
            ownPageQuery.setPageSize(pageSize);
            ownPageQuery.setPageNum(pageNo);
            return Optional.ofNullable(remoteProjectProductOwnClient.pageOwn(ownPageQuery)).map(JsonObject::getObjEntity).orElse(null);
        }, true));
    }

    @Override
    public List<String> queryProductOwnIdsInContract(String projectId) {
        List<ContractReviewProductOwn> owns = queryProductOwnIdsInContract(projectId, null);
        return ListUtils.emptyIfNull(owns).stream().map(ContractReviewProductOwn::getProjectProductOwnId).toList();
    }

    @Override
    public Map<String, Boolean> querySnInContract(List<String> sns) {
        if (CollectionUtils.isEmpty(sns)) {
            return Collections.emptyMap();
        }
        // 初始化返回值
        Map<String, Boolean> result = sns.stream().collect(Collectors.toMap(i -> i, i -> false));
        // 获取项目的序列号
        List<CrmProjectProductSnVO> crmProjectProductSnVOS = remoteProjectProductSnClient.getProductSnBatchBySns(sns).getObjEntity();
        if (CollectionUtils.isEmpty(crmProjectProductSnVOS)) {
            return result;
        }
        // 1.key sn  value projectProductId
        Map<String, String> snProjectProductId = crmProjectProductSnVOS.stream().collect(Collectors.toMap(CrmProjectProductSnVO::getPsn, CrmProjectProductSnVO::getRecordId, (i1, i2) -> i1));
        Set<String> projectProductId = new HashSet<>(snProjectProductId.values());
        List<ContractReviewProductOwn> owns = list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                .in(ContractReviewProductOwn::getProjectProductOwnId, projectProductId)
                .eq(ContractReviewProductOwn::getDelFlag, 0));
        if (CollectionUtils.isEmpty(owns)) {
            return result;
        }
        // 2 key projectProductId value contractId
        Map<String, String> projectProductIdContractId = owns.stream().collect(Collectors.toMap(ContractReviewProductOwn::getProjectProductOwnId, ContractReviewProductOwn::getContractReviewId, (i1, i2) -> i1));
        Set<String> contractIds = new HashSet<>(projectProductIdContractId.values());
        if (CollectionUtils.isEmpty(contractIds)) {
            return result;
        }
        ContractReviewMainService mainService = SpringUtil.getBean(ContractReviewMainService.class);
        List<ContractReviewMain> contractReviewMains = mainService.list(new LambdaQueryWrapper<ContractReviewMain>()
                .in(ContractReviewMain::getId, contractIds)
                .eq(ContractReviewMain::getDelFlag, 0));
        if (CollectionUtils.isEmpty(contractReviewMains)) {
            return result;
        }
        // 3.key contractId value contractReviewMain
        Map<String, ContractReviewMain> contractIdContractReviewMain = contractReviewMains.stream().collect(Collectors.toMap(ContractReviewMain::getId, i -> i));
        sns.forEach(sn -> {
            String recordId = snProjectProductId.get(sn);
            if (recordId == null) {
                return;
            }
            String contractId = projectProductIdContractId.get(recordId);
            if (contractId == null) {
                return;
            }
            ContractReviewMain contractReviewMain = contractIdContractReviewMain.get(contractId);
            if (contractReviewMain == null) {
                return;
            }
            Integer processState = contractReviewMain.getProcessState();
            if (processState != null && processState != 0) {
                result.put(sn, true);
            }
        });
        return result;
    }

    private List<ContractReviewProductOwn> queryProductOwnIdsInContract(String projectId, String contractId){
        // 查询已经发起合同评审的产品
        ContractReviewMainService mainService = SpringUtil.getBean(ContractReviewMainService.class);
        List<ContractReviewMain> list = mainService.list(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getProjectId, projectId)
                .ne(ContractReviewMain::getProcessState, 0)
                .eq(ContractReviewMain::getDelFlag, 0));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<String> contractIds = list.stream().map(ContractReviewMain::getId).toList();
        if (StringUtils.isNotBlank(contractId)){
            contractIds = contractIds.stream().filter(id -> !id.equals(contractId)).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(contractIds)) {
            return Collections.emptyList();
        }

        return list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                .in(ContractReviewProductOwn::getContractReviewId, contractIds)
                .eq(ContractReviewProductOwn::getDelFlag, 0));
    }

    /**
     * 计算一个产品的金额 包括这个产品下的配件
     *
     * @return 这个产品以及下面配件的总金额
     */
    private BigDecimal amountOne(ContractProductOwnDTO contractProductOwnDTO) {
        // 产品的成交总价
        BigDecimal dealTotalPrice = contractProductOwnDTO.getDealTotalPrice();
        // 产品下配件的成交总结
        BigDecimal componentTotalPrice = ListUtils.emptyIfNull(contractProductOwnDTO.getContractProductOwnComponentDTOS()).stream().map(ContractProductOwnDTO::getDealTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);

        return dealTotalPrice.add(componentTotalPrice);
    }

    @Getter
    enum TaxLossBySoftHardByIdentEnum {

        SOFT_HARD_IDENT_ONE(Collections.singletonList("H"), new TaxLossStrategyOne()),
        SOFT_HARD_IDENT_TWO(Arrays.asList("NQ&H", "S", "S&H", "THD&NQ", "安全S,科技H"), new TaxLossStrategyTwo()),
        SOFT_HARD_IDENT_THREE(Arrays.asList("纯NQ&H", "纯S", "纯S&H", "纯THD&NQ"), new TaxLossStrategyThree());

        private List<String> softHardIdents;
        private TaxLossStrategy taxLossStrategy;

        TaxLossBySoftHardByIdentEnum(List<String> softHardIdent, TaxLossStrategy taxLossStrategy) {
            this.softHardIdents = softHardIdent;
            this.taxLossStrategy = taxLossStrategy;
        }

        public static TaxLossStrategy getTaxLossStrategy(String softHardIdent) {
            for (TaxLossBySoftHardByIdentEnum taxLossBySoftHardByIdent : TaxLossBySoftHardByIdentEnum.values()) {
                if (taxLossBySoftHardByIdent.getSoftHardIdents().contains(softHardIdent)) {
                    return taxLossBySoftHardByIdent.taxLossStrategy;
                }
            }
            log.error("没有找到对应的税损策略! 软硬件标识为: {}", softHardIdent);
            throw new CrmException("没有找到对应的税损策略!");
        }
    }

    @Data
    static class TaxLossCalculate {
        // 当期报价
        private BigDecimal price;

        // 成本
        private BigDecimal cost;
    }

    /**
     * 税损策略类 根据不同的软硬件标识 通过不同策略计算税损
     */
    interface TaxLossStrategy {
        /**
         * 计算税损
         *
         * @param contractProductOwnDTO 产品
         * @return 税损
         */
        BigDecimal taxLoss(TaxLossCalculate contractProductOwnDTO);

    }

    static class TaxLossStrategyOne implements TaxLossStrategy {

        @Override
        public BigDecimal taxLoss(TaxLossCalculate taxLossCalculate) {
            if (taxLossCalculate.getPrice() == null) {
                return BigDecimal.ZERO;
            }
            // 算法： 报价/1.13*0.22*0.13
            return taxLossCalculate.getPrice().divide(BigDecimal.valueOf(1.13), RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(0.22)).multiply(BigDecimal.valueOf(0.13));
        }
    }

    static class TaxLossStrategyTwo implements TaxLossStrategy {

        @Override
        public BigDecimal taxLoss(TaxLossCalculate taxLossCalculate) {
            // 算法： （报价/1.13*0.22-（产品成本*1.1））*0.03+产品成本*1.1*0.13
            if (taxLossCalculate.getPrice() == null || taxLossCalculate.getCost() == null) {
                return BigDecimal.ZERO;
            }
            return taxLossCalculate.getPrice().divide(BigDecimal.valueOf(1.13), RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(0.22)).subtract(taxLossCalculate.getCost().multiply(BigDecimal.valueOf(1.1))).multiply(BigDecimal.valueOf(0.03))
                    .add(taxLossCalculate.getCost().multiply(BigDecimal.valueOf(1.1)).multiply(BigDecimal.valueOf(0.13)));
        }
    }

    static class TaxLossStrategyThree implements TaxLossStrategy {

        @Override
        public BigDecimal taxLoss(TaxLossCalculate taxLossCalculate) {
            if (taxLossCalculate.getPrice() == null) {
                return BigDecimal.ZERO;
            }
            // 算法： 报价/1.13*0.22*0.03
            return taxLossCalculate.getPrice().divide(BigDecimal.valueOf(1.13), RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(0.22)).multiply(BigDecimal.valueOf(0.03));
        }
    }

}
