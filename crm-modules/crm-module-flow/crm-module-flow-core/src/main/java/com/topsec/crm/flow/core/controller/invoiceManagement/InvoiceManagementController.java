package com.topsec.crm.flow.core.controller.invoiceManagement;

import com.topsec.crm.contract.api.entity.crmInvoiceManagement.CrmInvoiceManagementReVO;
import com.topsec.crm.flow.api.dto.invoiceManagement.InvoiceManagementFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.invoiceManagement.VO.InvoiceManagementExportVO;
import com.topsec.crm.flow.api.dto.invoiceManagement.VO.InvoiceManagementQueryVO;
import com.topsec.crm.flow.api.dto.invoiceManagement.VO.InvoiceManagementVO;
//import com.topsec.crm.flow.core.process.impl.InvoiceManagementProcessService;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.process.impl.InvoiceManagementProcessService;
import com.topsec.crm.flow.core.service.InvoiceManagementReService;
import com.topsec.crm.flow.core.service.InvoiceManagementService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.util.poi.ExcelUtil;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.io.IOException;
import java.util.List;

import static com.github.pagehelper.page.PageMethod.startPage;

@RestController
@RequestMapping("/invoiceManagement")
@Tag(name = "【票据管理-流程接口】", description = "invoiceManagement")
@RequiredArgsConstructor
@Validated
public class InvoiceManagementController extends BaseController {

    private final InvoiceManagementProcessService invoiceManagementProcessService;

    private final InvoiceManagementService invoiceManagementService;

    private final InvoiceManagementReService invoiceManagementReService;


    @PreAuthorize(hasPermission = "crm_bill_add")
    @PostMapping("/launch")
    @Operation(summary = "发起票据管理流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody InvoiceManagementFlowLaunchDTO flowLaunchDTO){
        return new JsonObject<>(invoiceManagementProcessService.launch(flowLaunchDTO));
    }

    @PreFlowPermission
    @GetMapping("/getInvoiceManagementVOByProcessInstanceId")
    @Operation(summary = "根据流程ID查询票据管理")
    public JsonObject<InvoiceManagementVO> getInvoiceManagementVOByProcessInstanceId(@RequestParam(value = "processInstanceId") String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(invoiceManagementService.getInvoiceManagementVOByProcessInstanceId(processInstanceId));
    }

//    @PreAuthorize(hasPermission = "crm_bill")
//    @PostMapping("/page")
//    @Operation(summary = "分页查询票据管理")
//    public JsonObject<TableDataInfo> getInvoiceManagementVOByQuery(@RequestBody InvoiceManagementQueryVO invoiceManagementQueryVO){
//        startPage();
//        return new JsonObject<>(invoiceManagementService.getInvoiceManagementVOByQuery(invoiceManagementQueryVO));
//    }
//
//    @PreAuthorize(hasPermission = "crm_bill")
//    @GetMapping("/getInvoiceManagementVOById")
//    @Operation(summary = "根据ID查询票据管理")
//    public JsonObject<InvoiceManagementVO> getInvoiceManagementVOById(@RequestParam(value = "invoiceManagementId") String invoiceManagementId){
//        return new JsonObject<>(invoiceManagementService.getInvoiceManagementVOById(invoiceManagementId));
//    }
//
//    @PreAuthorize(hasPermission = "crm_bill")
//    @GetMapping("/getInvoiceManagementReByMainTableId")
//    @Operation(summary = "获取关联合同信息")
//    public JsonObject<List<CrmInvoiceManagementReVO>> getInvoiceIndustryReByMainTableId(@RequestParam(value = "processInstanceId") String processInstanceId){
//        return new JsonObject<>(invoiceManagementReService.getInvoiceManagementReByMainTableId(processInstanceId));
//    }
//
//    @PreAuthorize(hasPermission = "crm_bill_add")
//    @PostMapping("/updateInvoiceManagement")
//    @Operation(summary = "修改票据管理信息")
//    public JsonObject<Boolean> updateInvoiceManagement(@RequestBody InvoiceManagementVO invoiceManagementVO){
//        return new JsonObject<>(invoiceManagementService.updateInvoiceManagement(invoiceManagementVO));
//    }
//
//    @PreAuthorize(hasPermission = "crm_bill")
//    @PostMapping("/exportInvoiceManagementInfo")
//    @Operation(summary = "导出票据管理列表数据")
//    public void exportInvoiceManagementInfo(@RequestBody InvoiceManagementQueryVO invoiceManagementQueryVO) throws IOException {
//        List<InvoiceManagementExportVO> exportList = invoiceManagementService.exportInvoiceManagementInfo(invoiceManagementQueryVO);
//        ExcelUtil<InvoiceManagementExportVO> excelUtil = new ExcelUtil<>(InvoiceManagementExportVO.class);
//        excelUtil.exportExcel(response,exportList,"票据管理列表数据");
//    }

}
