package com.topsec.crm.flow.core.controllerhidden.contractoriginalexpediting;




import com.topsec.crm.flow.core.service.ContractOriginalExpeditingService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 催交合同原件表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@RestController
@RequestMapping("/hidden/contractExpediting")
@Tag(name = "催交合同原件", description = "contractExpediting")
@RequiredArgsConstructor
@Validated
public class HiddenContractOriginalExpeditingController extends BaseController {

     @Resource
     private ContractOriginalExpeditingService contractOriginalExpeditingService;


    @PostMapping("/executeBacthLauch")
    @Operation(summary = "批量处理没有上交原件的发起催交流程")
    public JsonObject<Boolean> executeBacthLauch(@RequestBody Map<String, Object> param){
        contractOriginalExpeditingService.executeBacthLauch(param);
        return new JsonObject<>(true);
    }

}

