package com.topsec.crm.flow.core.controller.borrowforprobation;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.api.dto.borrowforprobation.BorrowForProbationPenaltyDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.BorrowForProbation;
import com.topsec.crm.flow.core.entity.BorrowForProbationPenalty;
import com.topsec.crm.flow.core.service.BorrowForProbationPenaltyService;
import com.topsec.crm.flow.core.service.BorrowForProbationService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.project.api.dto.BorrowForProbationPenaltyPageQuery;
import com.topsec.crm.project.api.entity.CrmBorrowForProbationDeviceVO;
import com.topsec.crm.project.api.entity.CrmBorrowForProbationPenaltyVO;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@RestController
@RequestMapping("/borrowForProbationPenalty")
@Tag(name = "借试用流程罚款信息", description = "/borrowForProbationPenalty")
public class BorrowForProbationPenaltyController extends BaseController {

    @Autowired
    private BorrowForProbationPenaltyService borrowForProbationPenaltyService;

    @Autowired
    private BorrowForProbationService borrowForProbationService;

    @PreFlowPermission
    @PostMapping("/flow/personal/penaltyProductPage")
    @Operation(summary = "查询已生效借试用设备中需要罚款的设备信息-借试用")
    JsonObject<PageUtils<CrmBorrowForProbationDeviceVO>> penaltyProductPage(@RequestBody BorrowForProbationPenaltyPageQuery borrowForProbationPenaltyPageQuery) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        BorrowForProbation borrowForProbation = borrowForProbationService.getOne(new LambdaQueryWrapper<BorrowForProbation>().eq(BorrowForProbation::getProcessInstanceId, processInstanceId));
        if(null != borrowForProbation && borrowForProbation.getPersonId().equals(borrowForProbationPenaltyPageQuery.getPersonId())){
            return new JsonObject<PageUtils<CrmBorrowForProbationDeviceVO>>(borrowForProbationPenaltyService.penaltyProductPage(borrowForProbationPenaltyPageQuery));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission(hasAnyNodes = {"borrowingTrial_04"})
    @PutMapping("/add/penalty")
    @Operation(summary = "添加借试用流程罚款信息")
    public JsonObject<Boolean> addBorrowForProbationPenalty(@RequestBody BorrowForProbationPenaltyDTO penalty) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        BorrowForProbation borrowForProbation = borrowForProbationService.getOne(new LambdaQueryWrapper<BorrowForProbation>().eq(BorrowForProbation::getProcessInstanceId, processInstanceId));
        if(null != borrowForProbation && borrowForProbation.getId().equals(penalty.getBorrowId())){
            BorrowForProbationPenalty borrowForProbationPenalty = HyperBeanUtils.copyPropertiesByJackson(penalty, BorrowForProbationPenalty.class);
            return new JsonObject<Boolean>(borrowForProbationPenaltyService.addBorrowForProbationPenalty(borrowForProbationPenalty));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission(hasAnyNodes = {"borrowingTrial_04","sid-0AB87E7B-D486-4B9F-AA71-EBBFCB0B9E36"})
    @PutMapping("/update/penalty")
    @Operation(summary = "修改借试用流程罚款信息")
    public JsonObject<Boolean> updateBorrowForProbationPenalty(@RequestBody BorrowForProbationPenaltyDTO penalty) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        BorrowForProbation borrowForProbation = borrowForProbationService.getOne(new LambdaQueryWrapper<BorrowForProbation>().eq(BorrowForProbation::getProcessInstanceId, processInstanceId));
        if(null != borrowForProbation && borrowForProbation.getId().equals(penalty.getBorrowId())){
            BorrowForProbationPenalty borrowForProbationPenalty = HyperBeanUtils.copyPropertiesByJackson(penalty, BorrowForProbationPenalty.class);
            return new JsonObject<Boolean>(borrowForProbationPenaltyService.updateBorrowForProbationPenalty(borrowForProbationPenalty));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @GetMapping("/list/penalty")
    @Operation(summary = "获取借试用流程罚款信息")
    public JsonObject<List<CrmBorrowForProbationPenaltyVO>> listBorrowForProbationPenalty(@RequestParam String borrowId) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        BorrowForProbation borrowForProbation = borrowForProbationService.getOne(new LambdaQueryWrapper<BorrowForProbation>().eq(BorrowForProbation::getProcessInstanceId, processInstanceId));
        if(null != borrowForProbation && borrowForProbation.getId().equals(borrowId)){
            List<BorrowForProbationPenalty> list = borrowForProbationPenaltyService.listBorrowForProbationPenalty(borrowId);
            return new JsonObject<List<CrmBorrowForProbationPenaltyVO>>(HyperBeanUtils.copyListPropertiesByJackson(list, CrmBorrowForProbationPenaltyVO.class));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

}
