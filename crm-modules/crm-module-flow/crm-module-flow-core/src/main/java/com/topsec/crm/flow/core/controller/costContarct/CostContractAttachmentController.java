package com.topsec.crm.flow.core.controller.costContarct;

import com.topsec.crm.flow.api.dto.costContarct.CostContractAttachmentInfoDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.CostContractAttachment;
import com.topsec.crm.flow.core.service.CostContractAttachmentService;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.security.annotation.PreAuthorize;

import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 费用合同附件
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/costContractAttachment")
@Tag(name = "【费用附件】", description = "costContractAttachment")
@RequiredArgsConstructor
@Validated
public class CostContractAttachmentController {

}
