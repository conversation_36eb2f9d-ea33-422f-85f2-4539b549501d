package com.topsec.crm.flow.core.controllerhidden.agentPrepaymentUsage;

import com.topsec.crm.flow.api.dto.agentPrepaymentUsage.AgentPrepaymentUsageDTO;
import com.topsec.crm.flow.core.service.AgentPrepaymentUsageService;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/hidden/agentPrepaymentUsage")
@Tag(name = "预付款使用-内部")
public class HiddenAgentPrepaymentUsageController {
    @Autowired
    private AgentPrepaymentUsageService agentPrepaymentUsageService;
    @GetMapping("/list")
    @Operation(summary = "预付款使用list")
    public JsonObject<List<AgentPrepaymentUsageDTO>> list(@RequestParam String paymentVerificationId) {
        return new JsonObject<>(agentPrepaymentUsageService.list(paymentVerificationId));
    }
}
