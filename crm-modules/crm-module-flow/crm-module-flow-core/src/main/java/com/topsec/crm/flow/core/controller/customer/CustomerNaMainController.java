package com.topsec.crm.flow.core.controller.customer;

import com.topsec.crm.flow.api.dto.customer.CustomerNaMainVo;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CustomerNaMain;
import com.topsec.crm.flow.core.entity.CustomerNaSaleman;
import com.topsec.crm.flow.core.service.ICustomerNaMainService;
import com.topsec.crm.flow.core.service.ICustomerNaSalemanService;
import com.topsec.crm.flow.core.service.ICustomerNaService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;

import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.constant.TbsConstants;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.constants.TosConstants;
import com.topsec.tos.common.vo.BriefInfoVO;
import com.topsec.tos.common.vo.EmployeeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/customerNaMain")
@Tag(name = "NA客户申请记录相关Controller", description = "/customerNaMain/flow")
@RequiredArgsConstructor
@Validated
public class CustomerNaMainController extends BaseController {
    @Autowired
    private ICustomerNaMainService customerNaMainService;
    @Autowired
    private ICustomerNaService customerNaService;
    @Autowired
    private ICustomerNaSalemanService customerNaSalemanService;
    @Autowired
    private TosDepartmentClient tosDepartmentClient;
    @Autowired
    private TosEmployeeClient tosEmployeeClient;

    @PostMapping("/deptSelectCustomerNaMainList")
    @Operation(summary = "F7.部门负责人查询NA客户申请记录")
    @PreAuthorize(hasPermission = "crm_customer_dept_na_apply")
    public JsonObject<PageUtils<CustomerNaMainVo>> deptSelectCustomerNaMainList(@RequestBody CustomerNaMainVo customerNaMainVo) {
        //查询当前用户管理的部门IDs
        List<String> deptIds = queryDeptIdList(UserInfoHolder.getCurrentPersonId(), TbsConstants.Datapurview.DEPTDATA);

        startPage();
        List<CustomerNaMain> list = customerNaMainService.query()
                .eq(StringUtils.isNotEmpty(customerNaMainVo.getApplyUserId()), "apply_user_id", customerNaMainVo.getApplyUserId())
                .in("dept_id", deptIds)
                .orderByDesc("create_time")
                .list();

        //查询用户信息
        JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(list.stream().map(CustomerNaMain::getApplyUserId).collect(Collectors.toList()));
        if(byIds.isSuccess()) {
            List<EmployeeVO> employeeVOS = byIds.getObjEntity();
            List<CustomerNaMainVo> customerNaMainVos = new ArrayList<CustomerNaMainVo>();
            list.stream().forEach(customerNaMain -> {
                CustomerNaMainVo cnmv = HyperBeanUtils.copyPropertiesByJackson(customerNaMain, CustomerNaMainVo.class);
                //查询申请人信息
                EmployeeVO employeeVO = employeeVOS.stream().filter(e -> e.getUuid().equals(customerNaMain.getApplyUserId())).findFirst().orElse(null);
                cnmv.setApplyUserName(employeeVO.getName());
                cnmv.setDeptId(employeeVO.getDept() != null ? employeeVO.getDept().getUuid() : "");
                cnmv.setDeptName(employeeVO.getDept() != null ? employeeVO.getDept().getName() : "");

                //查询当前流程审批中的申请人数
                List<CustomerNaSaleman> flowList = customerNaSalemanService.selectApplyCountByCustomer(cnmv);
                cnmv.setApplyNum(flowList.size());

                customerNaMainVos.add(cnmv);
            });

            //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
            PageUtils dataTable = getDataTable(list,customerNaMainVos);
            return new JsonObject<>(dataTable);
        }

        return new JsonObject<>(null);
    }

    @PostMapping("/adminSelectCustomerNaMainList")
    @Operation(summary = "F8.管理员查询NA客户申请记录")
    @PreAuthorize(hasPermission = "crm_customer_admin_na_apply_list")
    public JsonObject<PageUtils<CustomerNaMainVo>> adminSelectCustomerNaMainList(@RequestBody CustomerNaMainVo customerNaMainVo) {
        startPage();
        List<CustomerNaMain> list = customerNaMainService.query()
                .eq(StringUtils.isNotEmpty(customerNaMainVo.getApplyUserId()), "apply_user_id", customerNaMainVo.getApplyUserId())
                .orderByDesc("create_time")
                .list();

        //查询用户信息
        JsonObject<List<EmployeeVO>> byIds = tosEmployeeClient.findByIds(list.stream().map(CustomerNaMain::getApplyUserId).collect(Collectors.toList()));
        if(byIds.isSuccess()){
            List<EmployeeVO> employeeVOS = byIds.getObjEntity();
            List<CustomerNaMainVo> customerNaMainVos = new ArrayList<CustomerNaMainVo>();
            list.stream().forEach(customerNaMain -> {
                    CustomerNaMainVo cnmv = HyperBeanUtils.copyPropertiesByJackson(customerNaMain, CustomerNaMainVo.class);
                    EmployeeVO employeeVO = employeeVOS.stream().filter(e -> e.getUuid().equals(customerNaMain.getApplyUserId())).findFirst().orElse(null);
                    cnmv.setApplyUserName(employeeVO.getName());
                    cnmv.setDeptId(employeeVO.getDept() != null ? employeeVO.getDept().getUuid() : "");
                    cnmv.setDeptName(employeeVO.getDept() != null ? employeeVO.getDept().getName() : "");
                    customerNaMainVos.add(cnmv);

                    //查询当前流程审批中的申请人数
                    List<CustomerNaSaleman> flowList = customerNaSalemanService.selectApplyCountByCustomer(cnmv);
                    cnmv.setApplyNum(flowList.size());
                }
            );

            //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
            PageUtils dataTable = getDataTable(list,customerNaMainVos);
            return new JsonObject<>(dataTable);
        }

        return new JsonObject<>(null);
    }

    public List<String> queryDeptIdList(String userId,Integer dataRange) {
        StringBuilder stringBuilder = new StringBuilder();
        //查询管辖部门
        if (TbsConstants.Datapurview.DEPTDATA.equals(dataRange)||TbsConstants.Datapurview.MANAGEDEPTDATA.equals(dataRange)) {
            List<String> stringList = new ArrayList<>();
            stringList.add(TosConstants.RelationType.LEADER);
            stringList.add(TosConstants.RelationType.BP);
            stringList.add(TosConstants.RelationType.REL_PROCESSOR);
            stringList.add(TosConstants.RelationType.ASSISTANT);
            JsonObject<List<BriefInfoVO>> jsonObject = tosDepartmentClient.queryManagedDept(userId, stringList, true);
            if (jsonObject != null && ResultEnum.SUCCESS.getResult().equals(jsonObject.getResult())) {
                String fDepts= jsonObject.getObjEntity().stream().map(BriefInfoVO::getUuid).collect(Collectors.joining(","));
                if (StringUtils.isNotBlank(fDepts)){
                    stringBuilder.append(fDepts);
                }
            }
        }

        //查询所在部门
        if (TbsConstants.Datapurview.DEPTDATA.equals(dataRange)||TbsConstants.Datapurview.OWNDEPTDATA.equals(dataRange)) {
            JsonObject<Set<String>> jsonDeptObject = tosDepartmentClient.findEmployeeDeptIds(userId, false);
            if (jsonDeptObject != null && ResultEnum.SUCCESS.getResult().equals(jsonDeptObject.getResult())) {
                Set<String> set = jsonDeptObject.getObjEntity();
                if (!CollectionUtils.isEmpty(set)) {
                    stringBuilder.append("," + String.join(",", set));
                }
            }
        }

        List<String> deptIds = Arrays.asList(stringBuilder.toString().split(","));
        return deptIds;
    }

    @PostMapping("/findAllSalemanId")
    @Operation(summary = "F10.查询当前流程所有销售人员ID")
    @PreAuthorize
//    @Audit(eventName = "adminSelectCustomerNaMainList" ,eventDesc = "查询当前流程所有销售人员ID", eventType = "客户管理",getMethodData = true)
    public JsonObject<List<String>> adminSelectCustomerNaMainList(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        List<CustomerNaSaleman> cnss = customerNaSalemanService.query().eq("process_instance_id", processInstanceId).list();
        CustomerNaMain cn = customerNaMainService.query().eq("process_instance_id", processInstanceId).one();
        List<String> collect = cnss.stream().map(CustomerNaSaleman::getSalemanId).collect(Collectors.toList());
        collect.remove(cn.getCreateUser());//将流程创建人不当做销售处理
        return new JsonObject<>(collect);
    }
}
