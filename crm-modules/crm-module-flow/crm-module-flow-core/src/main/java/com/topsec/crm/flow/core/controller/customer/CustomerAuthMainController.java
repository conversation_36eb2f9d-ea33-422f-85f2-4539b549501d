package com.topsec.crm.flow.core.controller.customer;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.topsec.crm.customer.api.RemoteCustomerAnotherService;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerAnotherVo;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.customer.api.entity.CrmLinkmanVo;
import com.topsec.crm.flow.api.dto.customer.CustomerAnotherNameMainVo;
import com.topsec.crm.flow.api.dto.customer.CustomerAuthApproveFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.customer.CustomerAuthMainVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CustomerAnotherNameMain;
import com.topsec.crm.flow.core.entity.CustomerAuthMain;
import com.topsec.crm.flow.core.process.impl.CustomerAuthProcessService;
import com.topsec.crm.flow.core.service.ICustomerAnotherNameMainService;
import com.topsec.crm.flow.core.service.ICustomerAuthMainService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.constant.web.CustomerConstants;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.page.PageDomain;
import com.topsec.crm.framework.common.web.page.TableSupport;
import com.topsec.crm.operation.api.RemoteIndustryService;
import com.topsec.crm.operation.api.entity.CrmIndustryVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbsapi.client.TbsPersonClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.PersonVO;
import com.topsec.tfs.api.client.TfsHistoryClient;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.node.ApproveNode;
import com.topsec.vo.task.Approved;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/customerAuthMain")
@Tag(name = "客户认证相关Controller", description = "/customerAuthMain/flow")
@RequiredArgsConstructor
@Validated
public class CustomerAuthMainController extends BaseController {
    @Autowired
    private ICustomerAuthMainService customerAuthMainService;
    @Autowired
    private CustomerAuthProcessService customerAuthProcessService;
    @Autowired
    private RemoteCustomerService remoteCustomerService;
    @Autowired
    private TbsPersonClient tbsPersonClient;
    @Autowired
    private RemoteCustomerAnotherService remoteCustomerAnotherService;
    @Autowired
    private ICustomerAnotherNameMainService customerAnotherNameMainService;
    @Autowired
    private RemoteIndustryService remoteIndustryService;
    @Autowired
    private TfsNodeClient tfsNodeClient;
    @Autowired
    private TfsHistoryClient tfsHistoryClient;

    @PostMapping("/launch")
    @Operation(summary = "发起客户认证审批流程")
    @PreAuthorize(hasAnyPermission = {"crm_customer_noauth","crm_customer_noauth_agent"})
    public JsonObject<Boolean> launch(@Valid @RequestBody CustomerAuthApproveFlowLaunchDTO launchDTO) {
        //1.客户重名校验
        JsonObject<CrmCustomerVo> byName = remoteCustomerService.findByName(launchDTO.getName());
        if(byName.isSuccess()){
            Assert.isFalse(byName.getObjEntity() != null, "系统中已存在重名客户，请重新编辑后提交！");
        }

        //2.客户别名重名校验
        List<CrmCustomerAnotherVo> names = launchDTO.getCustomerAnothers();
        if(CollectionUtil.isNotEmpty(names)){
            for (int i=0;i<names.size();++i) {
                String newName = names.get(i).getAnotherName();
                Assert.isFalse(newName.equals(launchDTO.getName()), "别名不可与公司名称相同。");
                //查询系统中是否有重名的别名（已生效）
                JsonObject<List<CrmCustomerAnotherVo>> byName1 = remoteCustomerAnotherService.findByName(newName);
                Assert.isFalse(byName1.isSuccess() && CollectionUtil.isNotEmpty(byName1.getObjEntity()), "系统中存在相同名称的客户别名。");
                //查询系统中是否有重名的别名（流程中）
                List<CustomerAnotherNameMain> existFlows = customerAnotherNameMainService.selectFlowName(newName);
                Assert.isFalse(CollectionUtil.isNotEmpty(existFlows), "审批流程中存在同名的客户别名。");

                for (int j = i+1; j< names.size(); ++j) {
                    if(newName.equals(names.get(j).getAnotherName())){
                        Assert.isFalse(true, "该客户存在相同别名，请重新编辑。");
                    }
                }
            }
        }

        //3.联系人信息重复校验
        if(CollectionUtil.isNotEmpty(launchDTO.getLinkmans())){
            List<CrmLinkmanVo> linkmans = launchDTO.getLinkmans();
            //提交的数据做重复校验
            for (int i=0;i<linkmans.size();i++) {
                for (int j=i+1;j<linkmans.size();j++) {
                    if(linkmans.get(i).getName().equals(linkmans.get(j).getName()) && linkmans.get(i).getMobile().equals(linkmans.get(j).getMobile())
                            && linkmans.get(i).getEmail().equals(linkmans.get(j).getEmail())){
                        Assert.isFalse(true,"存在联系人名称、邮箱、手机信息完全一致，请勿重复添加。");
                    }
                }
            }
        }

        //发起认证流程
        customerAuthProcessService.launch(launchDTO);

        return new JsonObject<>(true);
    }

    /**
     * 销售查询
     *
     * 普通销售-查看自己的
     * 部门负责人-查看整个部门的
     */
    @PostMapping("/selectPage")
    @Operation(summary = "销售查询查询客户认证申请记录")
    @PreAuthorize(hasPermission = "crm_customer_noauth",dataScope = "crm_customer_noauth")
//    @Audit(eventName = "salemanSelectList" ,eventDesc = "公司人员查询客户认证申请记录", eventType = "客户认证相关管理",getMethodData = true)
    public JsonObject<PageUtils<CustomerAuthMainVo>> selectPage(@RequestBody CustomerAuthMainVo customerAuthMainVo) {
        //查询符合条件的客户信息
        CrmCustomerVo customerVo = customerAuthMainVo.getCustomerVo();

        String nameForSearch;
        if(StringUtils.isNotBlank(customerVo.getName())){
            nameForSearch = StringUtils.replaceBlank(customerVo.getName())
                    .replaceAll("（","(")
                    .replaceAll("）",")");
        } else {
            nameForSearch = "";
        }

        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        startPage();
        List<CustomerAuthMain> list = customerAuthMainService.query()
                .in(CollectionUtil.isNotEmpty(dataScopeParam.getPersonIdList()), "apply_user_id", dataScopeParam.getPersonIdList())
                .and(StringUtils.isNotBlank(customerVo.getName()),wrapper -> {
                    wrapper.like("name_for_search", nameForSearch)
                            .or().apply( "JSON_CONTAINS(another_names,CONCAT('\"',{0},'\"'))", customerVo.getName());
                })
                .orderByDesc("create_time")
                .list();

        if(CollectionUtil.isNotEmpty(list)){
            //查询客户信息
            JsonObject<Map<String,CrmCustomerVo>> ciObj = remoteCustomerService.batchGetCustomerInfo(list.stream().map(CustomerAuthMain::getCustomerId).toList(),false,false);
            if(ciObj.isSuccess()){
                Map<String,CrmCustomerVo> customerInfos = ciObj.getObjEntity();
                List<CustomerAuthMainVo> customerAuthMainVos = new ArrayList<CustomerAuthMainVo>();
                list.stream().forEach(customerAuthMain -> {
                            CustomerAuthMainVo cnmv = HyperBeanUtils.copyPropertiesByJackson(customerAuthMain, CustomerAuthMainVo.class);
                            //补充客户信息
                            CrmCustomerVo info = customerInfos.get(customerAuthMain.getCustomerId());
                            cnmv.setCustomerVo(info);
                            if(info != null) {
                                //补充审批人信息
                                //查询历史审批通过信息
                                JsonObject<List<Approved>> historyApproved = tfsHistoryClient.findHistoryApproved(customerAuthMain.getProcessInstanceId(), "sid-56E7F4FB-72F8-4A2F-8703-1533757181EB");
                                if(historyApproved.isSuccess() && historyApproved.getObjEntity() != null && historyApproved.getObjEntity().size() > 0){
                                    cnmv.setPassUserId(historyApproved.getObjEntity().get(0).getPersonId());
                                    cnmv.setPassUserName(historyApproved.getObjEntity().get(0).getName());
                                }
                            }
                            //补充审批节点
                            JsonObject<Set<ApproveNode>> setJsonObject = tfsNodeClient.queryNodeByProcessInstanceId(cnmv.getProcessInstanceId());
                            if(setJsonObject.isSuccess() && setJsonObject.getObjEntity() != null){
                                Set<ApproveNode> objEntity = setJsonObject.getObjEntity();
                                cnmv.setApproveNodes(objEntity);
                            }
                            customerAuthMainVos.add(cnmv);
                        }
                );

                //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
                PageUtils dataTable = getDataTable(list,customerAuthMainVos);
                return new JsonObject<>(dataTable);
            }
        }
        return new JsonObject<>(new PageUtils<CustomerAuthMainVo>());
    }

    /**
     * 管理员查询客户认证申请记录
     *
     * 部门负责人-查看管辖部门
     * 管理员-查看所有
     */
    @PostMapping("/adminSelectPage")
    @Operation(summary = "管理员查询客户认证申请记录")
    @PreAuthorize(hasPermission = "crm_customer_admin_auth_manage",dataScope = "crm_customer_admin_auth_manage")
//    @Audit(eventName = "salemanSelectList" ,eventDesc = "公司人员查询客户认证申请记录", eventType = "客户认证相关管理",getMethodData = true)
    public JsonObject<PageUtils<CustomerAuthMainVo>> adminSelectPage(@RequestBody CustomerAuthMainVo customerAuthMainVo) {
        CrmCustomerVo params = HyperBeanUtils.copyPropertiesByJackson(customerAuthMainVo.getCustomerVo(),CrmCustomerVo.class);
        //查询符合条件的客户信息
        CrmCustomerVo customerVo = customerAuthMainVo.getCustomerVo();

        String nameForSearch;
        if(StringUtils.isNotBlank(customerVo.getName())){
            nameForSearch = StringUtils.replaceBlank(customerVo.getName())
                    .replaceAll("（","(")
                    .replaceAll("）",")");
        } else {
            nameForSearch = "";
        }

        startPage();
        List<CustomerAuthMain> list = customerAuthMainService.query()
                .eq(StringUtils.isNotNull(customerVo.getStatus()),"process_state", customerVo.getStatus())
                .eq(StringUtils.isNotNull(customerVo.getDataSource()),"data_source", customerVo.getDataSource())
                .in(CollectionUtil.isNotEmpty(customerVo.getIndustries()),"industry_id_two", customerVo.getIndustries())
                .in(CollectionUtil.isNotEmpty(customerVo.getAreas()),"county_code", customerVo.getAreas())
                .and(StringUtils.isNotBlank(nameForSearch), wrapper -> {
                    wrapper.like("name_for_search", nameForSearch)
                            .or().like("apply_user_name", params.getName())
                            .or().apply("JSON_CONTAINS(another_names,CONCAT('\"',{0},'\"'))", customerVo.getName());
                }).orderByDesc("create_time")
                .list();

        if(CollectionUtil.isNotEmpty(list)){
            //查询客户信息
            JsonObject<Map<String,CrmCustomerVo>> ciObj = remoteCustomerService.batchGetCustomerInfo(list.stream().map(CustomerAuthMain::getCustomerId).toList(),false,false);
            if(ciObj.isSuccess()){
                Map<String,CrmCustomerVo> customerInfos = ciObj.getObjEntity();
                List<CustomerAuthMainVo> customerAuthMainVos = new ArrayList<CustomerAuthMainVo>();
                list.stream().forEach(customerAuthMain -> {
                            CustomerAuthMainVo cnmv = HyperBeanUtils.copyPropertiesByJackson(customerAuthMain, CustomerAuthMainVo.class);
                            //补充客户信息
                            CrmCustomerVo info = customerInfos.get(customerAuthMain.getCustomerId());
                            cnmv.setCustomerVo(info);
                            if(info != null) {
                                //补充审批人信息
                                //查询历史审批通过信息
                                JsonObject<List<Approved>> historyApproved = tfsHistoryClient.findHistoryApproved(customerAuthMain.getProcessInstanceId(), "sid-56E7F4FB-72F8-4A2F-8703-1533757181EB");
                                if(historyApproved.isSuccess() && historyApproved.getObjEntity() != null && historyApproved.getObjEntity().size() > 0){
                                    cnmv.setPassUserId(historyApproved.getObjEntity().get(0).getPersonId());
                                    cnmv.setPassUserName(historyApproved.getObjEntity().get(0).getName());
                                }
                            }
                            //补充审批节点
                            JsonObject<Set<ApproveNode>> setJsonObject = tfsNodeClient.queryNodeByProcessInstanceId(cnmv.getProcessInstanceId());
                            if(setJsonObject.isSuccess() && setJsonObject.getObjEntity() != null){
                                Set<ApproveNode> objEntity = setJsonObject.getObjEntity();
                                cnmv.setApproveNodes(objEntity);
                            }
                            customerAuthMainVos.add(cnmv);
                        }
                );

                //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
                PageUtils dataTable = getDataTable(list,customerAuthMainVos);
                return new JsonObject<>(dataTable);
            }
        }
        return new JsonObject<>(new PageUtils<CustomerAuthMainVo>());
    }

    /**
     * 渠道人员查询认证记录
     *
     * 都是只能查看自己的
     */
    @PostMapping("/agentSelectPage")
    @Operation(summary = "渠道人员查询认证记录")
    @PreAuthorize(hasPermission = "crm_customer_noauth_agent")
    public JsonObject<PageUtils<CustomerAuthMainVo>> agentSelectPage(@RequestBody CustomerAuthMainVo customerAuthMainVo) {
        //查询符合条件的客户信息
        CrmCustomerVo customerVo = customerAuthMainVo.getCustomerVo();

        String nameForSearch;
        if(StringUtils.isNotBlank(customerVo.getName())){
            nameForSearch = StringUtils.replaceBlank(customerVo.getName())
                    .replaceAll("（","(")
                    .replaceAll("）",")");
        } else {
            nameForSearch = "";
        }

        startPage();
        List<CustomerAuthMain> list = customerAuthMainService.query()
                .eq("apply_user_id", UserInfoHolder.getCurrentPersonId())
                .and(StringUtils.isNotBlank(nameForSearch), wrapper -> {
                    wrapper.like("name_for_search", nameForSearch)
                            .or().apply( "JSON_CONTAINS(another_names,CONCAT('\"',{0},'\"'))", customerVo.getName());
                }).orderByDesc("create_time")
                .list();

        if(CollectionUtil.isNotEmpty(list)){
            //查询用户信息
            List<String> collect = list.stream().map(CustomerAuthMain::getApplyUserId).collect(Collectors.toList());
            JsonObject<List<PersonVO>> byIds = tbsPersonClient.listByIds(collect.toArray(new String[]{}));
            //查询客户信息
            JsonObject<Map<String,CrmCustomerVo>> ciObj = remoteCustomerService.batchGetCustomerInfo(list.stream().map(CustomerAuthMain::getCustomerId).toList(),false,false);
            if(byIds.isSuccess() && ciObj.isSuccess()){
                List<PersonVO> personVOS = byIds.getObjEntity();
                Map<String,CrmCustomerVo> customerInfos = ciObj.getObjEntity();
                List<CustomerAuthMainVo> customerAuthMainVos = new ArrayList<CustomerAuthMainVo>();
                list.stream().forEach(customerAuthMain -> {
                            CustomerAuthMainVo cnmv = HyperBeanUtils.copyPropertiesByJackson(customerAuthMain, CustomerAuthMainVo.class);
                            PersonVO personVO = personVOS.stream().filter(e -> e.getUuid().equals(customerAuthMain.getApplyUserId())).findFirst().orElse(null);
                            cnmv.setApplyUserName(personVO != null ? personVO.getName() : "");
                            //补充客户信息
                            CrmCustomerVo info = customerInfos.get(customerAuthMain.getCustomerId());
                            cnmv.setCustomerVo(info);
                            //补充审批人信息
                            if(info != null) {
                                //补充审批人信息
                                //查询历史审批通过信息
                                JsonObject<List<Approved>> historyApproved = tfsHistoryClient.findHistoryApproved(customerAuthMain.getProcessInstanceId(), "sid-56E7F4FB-72F8-4A2F-8703-1533757181EB");
                                if(historyApproved.isSuccess() && historyApproved.getObjEntity() != null && historyApproved.getObjEntity().size() > 0){
                                    cnmv.setPassUserId(historyApproved.getObjEntity().get(0).getPersonId());
                                    cnmv.setPassUserName(historyApproved.getObjEntity().get(0).getName());
                                }
                            }
                            //补充审批节点
                            JsonObject<Set<ApproveNode>> setJsonObject = tfsNodeClient.queryNodeByProcessInstanceId(cnmv.getProcessInstanceId());
                            if(setJsonObject.isSuccess() && setJsonObject.getObjEntity() != null){
                                Set<ApproveNode> objEntity = setJsonObject.getObjEntity();
                                cnmv.setApproveNodes(objEntity);
                            }
                            customerAuthMainVos.add(cnmv);
                        }
                );

                //list对象为分页后的代理对象，如果转成VO对象，代理对象会失效，即total获取不到，需要重新封装
                PageUtils dataTable = getDataTable(list,customerAuthMainVos);
                return new JsonObject<>(dataTable);
            }
        }
        return new JsonObject<>(new PageUtils<CustomerAuthMainVo>());
    }

    @PostMapping("/selectInfo")
    @Operation(summary = "查询认证审批申请详情")
    @PreFlowPermission
    public JsonObject<CustomerAuthMainVo> selectInfo(@RequestBody CustomerAuthMainVo customerAnotherNameMainVo){
        PreFlowPermissionAspect.checkProcessInstanceId(customerAnotherNameMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询流程信息
        CustomerAuthMain customerAuthMain = customerAuthMainService.getOne(new QueryWrapper<CustomerAuthMain>().eq("process_instance_id", customerAnotherNameMainVo.getProcessInstanceId()));
        CustomerAuthMainVo result = HyperBeanUtils.copyPropertiesByJackson(customerAuthMain, CustomerAuthMainVo.class);

        if(customerAuthMain != null){
            //2.查询客户信息
            JsonObject<CrmCustomerVo> jsonObject = remoteCustomerService.getCustomerInfo(customerAuthMain.getCustomerId(), UserInfoHolder.getCurrentPersonId(),false);

            if(jsonObject.isSuccess() && jsonObject.getObjEntity() != null){
                CrmCustomerVo crmCustomerVo = jsonObject.getObjEntity();
                //查询行业类型
                if(StringUtils.isNotEmpty(crmCustomerVo.getIndustryId())) {
                    JsonObject<CrmIndustryVO> jObject = remoteIndustryService.industryByUuid(crmCustomerVo.getIndustryId());
                    if (jObject.isSuccess()) {
                        crmCustomerVo.setIndustryType(jObject.getObjEntity().getType());
                    }
                }
                result.setCustomerVo(crmCustomerVo);
            }
        }

        return new JsonObject<>(result);
    }

    /*@PostMapping("/applyUpdateCustomerInfo")
    @Operation(summary = "申请人修改客户信息")
    @PreFlowPermission
    public JsonObject<Boolean> applyUpdateCustomerInfo(@Valid @RequestBody CustomerAuthApproveFlowLaunchDTO launchDTO){
//        PreFlowPermissionAspect.checkProcessInstanceId(customerAnotherNameMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.客户重名校验
        JsonObject<CrmCustomerVo> byName = remoteCustomerService.findByName(launchDTO.getName());
        if(byName.isSuccess()){
            Assert.isFalse(byName.getObjEntity() != null && byName.getObjEntity().getId().equals(launchDTO.getCustomerId()), "系统中已存在重名客户，请重新编辑后提交！");
        }

        //2.客户别名重名校验
        List<CrmCustomerAnotherVo> names = launchDTO.getCustomerAnothers();
        if(CollectionUtil.isNotEmpty(names)){
            for (int i=0;i<names.size();++i) {
                String newName = names.get(i).getAnotherName();
                Assert.isFalse(newName.equals(launchDTO.getName()), "别名不可与公司名称相同。");
                //查询系统中是否有重名的别名（已生效）
                JsonObject<List<CrmCustomerAnotherVo>> byName1 = remoteCustomerAnotherService.findByName(newName);
                Assert.isFalse(byName1.isSuccess() && CollectionUtil.isNotEmpty(byName1.getObjEntity()), "系统中存在相同名称的客户别名。");
                //查询系统中是否有重名的别名（流程中）
                List<CustomerAnotherNameMain> existFlows = customerAnotherNameMainService.selectFlowName(newName);
                Assert.isFalse(CollectionUtil.isNotEmpty(existFlows) && !existFlows.get(0).getProcessInstanceId().equals(launchDTO.getProcessInstanceId()), "审批流程中存在同名的客户别名。");

                for (int j = i+1; j< names.size(); ++j) {
                    if(newName.equals(names.get(j).getAnotherName())){
                        Assert.isFalse(true, "该客户存在相同别名，请重新编辑。");
                    }
                }
            }
        }

        //3.联系人信息重复校验
        if(CollectionUtil.isNotEmpty(launchDTO.getLinkmans())){
            List<CrmLinkmanVo> linkmans = launchDTO.getLinkmans();
            //提交的数据做重复校验
            for (int i=0;i<linkmans.size();i++) {
                for (int j=i+1;j<linkmans.size();j++) {
                    if(linkmans.get(i).getName().equals(linkmans.get(j).getName()) && linkmans.get(i).getMobile().equals(linkmans.get(j).getMobile())
                            && linkmans.get(i).getEmail().equals(linkmans.get(j).getEmail())){
                        Assert.isFalse(true,"存在联系人名称、邮箱、手机信息完全一致，请勿重复添加。");
                    }
                }
            }
        }

        //修改客户信息
        CrmCustomerVo customerVo =
        remoteCustomerService.insertCrmCustomer(customerVo);
        return remoteCustomerService.flowUpdateCustomerInfo(customerAnotherNameMainVo.getCustomerVo());
    }
*/
    @PostMapping("/updateCustomerInfo")
    @Operation(summary = "查询认证审批申请详情")
    @PreFlowPermission
    public JsonObject<Boolean> updateCustomerInfo(@RequestBody CustomerAuthMainVo customerAnotherNameMainVo){
        PreFlowPermissionAspect.checkProcessInstanceId(customerAnotherNameMainVo.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        //修改客户信息
        return remoteCustomerService.flowUpdateCustomerInfo(customerAnotherNameMainVo.getCustomerVo());
    }

}
