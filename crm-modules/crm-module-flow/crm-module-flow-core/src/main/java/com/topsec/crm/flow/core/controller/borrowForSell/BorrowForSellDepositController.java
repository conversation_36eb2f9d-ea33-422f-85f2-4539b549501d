package com.topsec.crm.flow.core.controller.borrowForSell;

import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellDepositDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BorrowForSell;
import com.topsec.crm.flow.core.entity.BorrowForSellDeposit;
import com.topsec.crm.flow.core.service.IBorrowForSellDepositService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.seata.common.util.CollectionUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 借转销押金信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/borrowForSellDeposit")
@Tag(name = "借转销付款信息", description = "/borrowForSellDeposit")
public class BorrowForSellDepositController extends BaseController {

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @Autowired
    private IBorrowForSellDepositService borrowForSellDepositService;

    @PreFlowPermission
    @GetMapping("/queryBorrowForSellDepositList")
    @Operation(summary = "查询借转销付款信息")
    public JsonObject<List<BorrowForSellDepositDTO>> queryBorrowForSellDepositList(@RequestParam String borrowId, @RequestParam(required = false) String depositType) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            List<BorrowForSellDeposit> result =  borrowForSellDepositService.queryBorrowForSellDepositList(borrowId,depositType);
            return new JsonObject<List<BorrowForSellDepositDTO>>(HyperBeanUtils.copyListPropertiesByJackson(result, BorrowForSellDepositDTO.class));
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @PostMapping("/saveBorrowForSellDeposit")
    @Operation(summary = "保存借转销付款信息")
    public JsonObject<Boolean> saveBorrowForSellDeposit(@RequestParam String borrowId, @RequestParam String depositType, @RequestBody List<BorrowForSellDepositDTO> depositList) {
        BorrowForSell byId = borrowForSellService.getById(borrowId);
        if(null != byId){
            PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            Boolean result =  borrowForSellDepositService.saveBorrowForSellDeposit(borrowId,depositType,depositList);
            return new JsonObject<Boolean>(result);
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @PostMapping("/updateBorrowForSellDeposit")
    @Operation(summary = "更新借转销付款信息")
    public JsonObject<Boolean> updateBorrowForSellDeposit(@RequestBody List<BorrowForSellDepositDTO> depositList) {
        if(CollectionUtils.isNotEmpty(depositList)){
            long count = depositList.stream().map(BorrowForSellDepositDTO::getBorrowId).count();
            if (count == 1){
                String borrowId = depositList.get(0).getBorrowId();
                BorrowForSell byId = borrowForSellService.getById(borrowId);
                if(null != byId){
                    PreFlowPermissionAspect.checkProcessInstanceId(byId.getProcessInstanceId(), request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
                    Boolean result =  borrowForSellDepositService.updateBorrowForSellDeposit(depositList);
                    return new JsonObject<Boolean>(result);
                }
            }
        }
        throw new CrmException(ResultEnum.AUTH_ERROR_500006);
    }

}

