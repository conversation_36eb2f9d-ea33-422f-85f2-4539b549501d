package com.topsec.crm.flow.core.controller.borrowForForward;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.topsec.crm.flow.api.dto.borrowForCompensate.BorrowForCompensateProductDTO;
import com.topsec.crm.flow.api.dto.borrowForForward.BorrowForForwardDTO;
import com.topsec.crm.flow.api.dto.borrowForForward.BorrowForForwardFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.borrowForForward.BorrowForForwardProductDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BorrowForForward;
import com.topsec.crm.flow.core.entity.BorrowForProbation;
import com.topsec.crm.flow.core.entity.BorrowForProbationAttachment;
import com.topsec.crm.flow.core.process.impl.BorrowForForwardProcessService;
import com.topsec.crm.flow.core.service.BorrowForProbationAttachmentService;
import com.topsec.crm.flow.core.service.BorrowForProbationService;
import com.topsec.crm.flow.core.service.IBorrowForForwardService;
import com.topsec.crm.framework.common.constant.BorrowForProbationConstants;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.project.api.client.RemoteBorrowForProbationClient;
import com.topsec.crm.project.api.entity.CrmBorrowForProbationCommonVO;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.project.api.client.RemoteProjectDirectlyClient;
import com.topsec.crm.project.api.entity.CrmBorrowForProbationSituationVO;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsHistoryClient;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.TosDepartmentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 转借流程控制器
 */
@RestController
@RequestMapping("/borrowForForward")
@Tag(name = "转借流程", description = "/borrowForForward")
public class BorrowForForwardController extends BaseController {

    @Autowired
    private BorrowForForwardProcessService borrowedForForwardProcessService;
    @Autowired
    private IBorrowForForwardService borrowForForwardService;
    @Autowired
    private BorrowForProbationService borrowForProbationService;
    @Autowired
    private RemoteProjectDirectlyClient remoteProjectDirectlyClient;
    @Autowired
    private BorrowForProbationAttachmentService borrowForProbationAttachmentService;
    @Autowired
    private TfsHistoryClient tfsHistoryClient;
    @Resource
    private TfsNodeClient tfsNodeClient;
    @Autowired
    private RemoteBorrowForProbationClient remoteBorrowForProbationClient;

    @PostMapping("/launch")
    @Operation(summary = "发起转借流程")
    @PreAuthorize(hasPermission = "crm_device_probation_forward")
    public JsonObject<Boolean> launch(@Valid @RequestBody BorrowForForwardFlowLaunchDTO launchDTO) {
        //权限校验
        List<BorrowForForwardProductDTO> productList = launchDTO.getProductList();
        Assert.isFalse(CollectionUtil.isEmpty(productList),"产品信息不能为空");

        Set<String> ids = productList.stream().map(BorrowForForwardProductDTO::getProjectProbationDeviceId).collect(Collectors.toSet());
        JsonObject<Map<String, Boolean>> mapJsonObject = remoteBorrowForProbationClient.checkBorrowForProbationDeviceByIds(ids, UserInfoHolder.getCurrentPersonId());
        if(mapJsonObject.isSuccess() && mapJsonObject.getObjEntity() != null){
            if(mapJsonObject.getObjEntity().containsValue(false)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        return new JsonObject<>(borrowedForForwardProcessService.launch(launchDTO));
    }

    @GetMapping("/info")
    @Operation(summary = "查看转借流程详情")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<BorrowForForwardDTO> info(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        //1.查询转借流程信息
        BorrowForForward bff = borrowForForwardService.query()
                .eq("process_instance_id", processInstanceId).one();

        //2.补充信息
        //2.1补充项目信息
        if(StringUtils.isNotEmpty(bff.getProjectId())) {
            JsonObject<CrmProjectDirectlyVo> jsonObject = remoteProjectDirectlyClient.getProjectInfo(bff.getProjectId());
            if (jsonObject.isSuccess()) {
                CrmProjectDirectlyVo projectInfo = jsonObject.getObjEntity();
                bff.setProjectNo(projectInfo.getProjectNo());
                bff.setProjectName(projectInfo.getProjectName());
                bff.setCustomerName(projectInfo.getCrmProjectSigningInfo() != null ? projectInfo.getCrmProjectSigningInfo().getCustomerName() : "");
                bff.setSigningType(projectInfo.getSigningType());
                bff.setSmType(projectInfo.getSmType());
            }
        }
        //2.2补充借试用信息
        CrmBorrowForProbationCommonVO common = borrowForProbationService.borrowForProbationDetailCommonByProcessNumber(bff.getFromProbationNum());
        if(common != null) {
            bff.setProbationBorrowType(common.getBorrowType());
            bff.setProbationBorrowerID(common.getPersonId());
            bff.setProbationBorrower(common.getBorrower());
            TosDepartmentVO departmentVO = getDepartmentVO(common.getPersonId());
            bff.setProbationDeptId(departmentVO != null ? departmentVO.getUuid() : "");
            bff.setProbationDeptName(departmentVO != null ? departmentVO.getName() : "");
            bff.setFromProcessInstanceId(common.getProcessInstanceId());
            bff.setFromProbationBorrowProjectId(common.getProjectId());
        }
        //2.3补充接收方信息
        TosDepartmentVO recDept = getDepartmentVO(bff.getPersonId());
        bff.setDeptId(recDept != null ? recDept.getUuid() : "");
        bff.setDeptName(recDept != null ? recDept.getName() : "");

        //3.查询附件信息
        List<BorrowForProbationAttachment> attachments = borrowForProbationAttachmentService.query().eq("borrow_id", bff.getId()).list();
        bff.setAttachments(attachments);

        //4.数据权限处理
        filterData(bff);

        return new JsonObject<>(HyperBeanUtils.copyPropertiesByJackson(bff, BorrowForForwardDTO.class));
    }

    private void filterData(BorrowForForward bff) {
        List<String> activityIds = BorrowForProbationConstants.BORROW_FOR_FORWARD_RECEVER;
        //不可见ID
        JsonObject<Set<String>> setJsonObject = tfsNodeClient.queryAssigneeAccountIdList(bff.getProcessInstanceId(), activityIds);
        if(setJsonObject.isSuccess()){
            if(setJsonObject.getObjEntity().contains(UserInfoHolder.getCurrentAccountId()) ||
                    bff.getCreateUser().equals(UserInfoHolder.getCurrentPersonId()) ){
                bff.setProjectNo(null);
                bff.setProjectName(null);
                bff.setSmType(null);
                bff.setSigningType(null);
                bff.setSignCompany(null);
                bff.setCustomerName(null);
                bff.setLinkMan(null);
                bff.setLinkManPhone(null);
//                bff.setReceiver(null);
//                bff.setDeptName(null);
                bff.setSigningType(null);
                bff.setBorrowType(null);
                bff.setBorrowProductType(null);
                bff.setMiddleman(null);
                bff.setMiddlemanName(null);
                bff.setMiddlemanLinkMan(null);
                bff.setMiddlemanLinkManPhone(null);
                bff.setEstimatedReturnTime(null);
            }
        }
    }

    @PostMapping("/editForwardInfo")
    @Operation(summary = "01.添加借用信息")
    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"sid-A8F0C20B-6A24-4649-A735-EEE9C3E96133"})
    public JsonObject<Boolean> editForwardInfo(@RequestBody BorrowForForwardDTO borrowForForwardDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(borrowForForwardDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        if (!remoteProjectDirectlyClient.hasRight(borrowForForwardDTO.getProjectId(), UserInfoHolder.getCurrentPersonId()).getObjEntity()) {
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }

        BorrowForForward borrowForForward = HyperBeanUtils.copyPropertiesByJackson(borrowForForwardDTO, BorrowForForward.class);
        borrowForForwardService.update(new UpdateWrapper<BorrowForForward>().eq("process_instance_id",borrowForForward.getProcessInstanceId())
                .set("project_id", borrowForForward.getProjectId())
                .set("link_man", borrowForForward.getLinkMan())
                .set("link_man_phone", borrowForForward.getLinkManPhone())
                .set(StringUtils.isNotEmpty(borrowForForward.getPersonId()),"person_id", borrowForForward.getPersonId())
                .set(StringUtils.isNotEmpty(borrowForForward.getReceiver()),"receiver", borrowForForward.getReceiver())
                .set("sign_company", borrowForForward.getSignCompany())
                .set("borrow_type", borrowForForward.getBorrowType())
                .set("borrow_product_type", borrowForForward.getBorrowProductType())
                .set("middleman", borrowForForward.getMiddleman())
                .set("middleman_name", borrowForForward.getMiddlemanName())
                .set(StringUtils.isNotEmpty(borrowForForward.getMiddlemanLinkMan()),"middleman_link_man", borrowForForward.getMiddlemanLinkMan())
                .set(StringUtils.isNotEmpty(borrowForForward.getMiddlemanLinkManPhone()),"middleman_link_man_phone", borrowForForward.getMiddlemanLinkManPhone())
                .set("estimated_return_time", borrowForForward.getEstimatedReturnTime())
        );
        return new JsonObject<Boolean>(ResultEnum.SUCCESS.getResult(), ResultEnum.SUCCESS.getMessage(),true);
    }

    @GetMapping("/detail/situation")
    @Operation(summary = "当前流程 查询用户借试用情况")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<CrmBorrowForProbationSituationVO> borrowForProbationSituation(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        /**
         * 转借流程接收方可见节点
         * 转借流程04、07节点ID 可见
         */
        List<String> activityIds = BorrowForProbationConstants.BORROW_FOR_FORWARD;
        JsonObject<Set<String>> setJsonObject = tfsNodeClient.queryAssigneeAccountIdList(processInstanceId, activityIds);
        if(setJsonObject.isSuccess() && setJsonObject.getObjEntity().contains(UserInfoHolder.getCurrentAccountId())) {
            return new JsonObject<CrmBorrowForProbationSituationVO>(borrowForForwardService.borrowForForwardSituation(processInstanceId));
        }
        return new JsonObject<CrmBorrowForProbationSituationVO>(new CrmBorrowForProbationSituationVO());
    }

    @PostMapping("/addTransportNum")
    @Operation(summary = "07.填写搬运单号")
    @PreAuthorize
    @PreFlowPermission(hasAnyNodes = {"sid-C296C812-35C6-47FA-AD56-79004534CD70"})
    public JsonObject<Boolean> addTransportNum(@RequestBody BorrowForForwardDTO borrowForForwardDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(borrowForForwardDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        BorrowForForward borrowForForward = HyperBeanUtils.copyPropertiesByJackson(borrowForForwardDTO, BorrowForForward.class);
        borrowForForwardService.update(new UpdateWrapper<BorrowForForward>().eq("process_instance_id",borrowForForward.getProcessInstanceId())
                .set("transport_num", borrowForForward.getTransportNum())
                .set("remark", borrowForForward.getRemark())
        );
        return new JsonObject<Boolean>(ResultEnum.SUCCESS.getResult(), ResultEnum.SUCCESS.getMessage(),true);
    }

    @GetMapping("/isShowReceveInfo")
    @Operation(summary = "是否展示借用方相关信息")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<Map<String,Boolean>> isShowReceveInfo(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));

        Map<String,Boolean> result = new HashMap<String,Boolean>();
        /**
         * 接收方信息不可见ID
         * 发起人也不可见
         * 转借流程03、05节点ID 不可见
         */
        List<String> activityIds = BorrowForProbationConstants.BORROW_FOR_FORWARD_RECEVER;
        //1.查询转借流程信息
        BorrowForForward bff = borrowForForwardService.query().eq("process_instance_id", processInstanceId).one();
        //不可见对应的用户ID
        JsonObject<Set<String>> setJsonObject = tfsNodeClient.queryAssigneeAccountIdList(processInstanceId, activityIds);
        if(setJsonObject.isSuccess()){
            if(setJsonObject.getObjEntity().contains(UserInfoHolder.getCurrentAccountId()) ||
                    bff.getCreateUser().equals(UserInfoHolder.getCurrentPersonId()) ){
                result.put("isShowReceveInfo",false);
            }else{
                result.put("isShowReceveInfo",true);
            }
        }

        /**
         * 转借流程接收方借用情况可见节点
         * 转借流程04、07节点ID 可见
         */
        List<String> forActivityIds = BorrowForProbationConstants.BORROW_FOR_FORWARD;
        JsonObject<Set<String>> setJO = tfsNodeClient.queryAssigneeAccountIdList(processInstanceId, forActivityIds);
        if(setJO.isSuccess() && setJO.getObjEntity().contains(UserInfoHolder.getCurrentAccountId())) {
            result.put("isShowPer",true);
        }else {
            result.put("isShowPer", false);
        }

        return new JsonObject<>(result);
    }

    @GetMapping("/getProjectInfo")
    @Operation(summary = "01.添加借用信息")
    @PreAuthorize
    @PreFlowPermission
    public JsonObject<CrmProjectDirectlyVo> getProjectInfo(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        BorrowForForward bff = borrowForForwardService.query().eq("process_instance_id", processInstanceId).one();
        return remoteProjectDirectlyClient.getProjectInfo(bff.getProjectId());
    }
}
