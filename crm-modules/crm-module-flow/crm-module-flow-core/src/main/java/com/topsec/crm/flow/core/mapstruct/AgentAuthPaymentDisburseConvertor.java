package com.topsec.crm.flow.core.mapstruct;

import com.topsec.crm.flow.api.dto.agentauthentication.AgentAuthPaymentDisburseDetailInfo;
import com.topsec.crm.flow.api.dto.agentauthentication.AgentAuthPaymentDisburseLaunchDTO;
import com.topsec.crm.flow.core.entity.AgentAuthPaymentDisburse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AgentAuthPaymentDisburseConvertor
{
    AgentAuthPaymentDisburseConvertor INSTANCE = Mappers.getMapper(AgentAuthPaymentDisburseConvertor.class);


    @Mapping(target = "receiptNumber", ignore = true)
    @Mapping(target = "receiptDate", ignore = true)
    @Mapping(target = "dataScopeParam", ignore = true)
    @Mapping(target = "processState", ignore = true)
    @Mapping(target = "processNumber", ignore = true)
    @Mapping(target = "updateUser", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createUser", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    AgentAuthPaymentDisburse toEntity(AgentAuthPaymentDisburseLaunchDTO launchDTO);


    @Mapping(target = "attachments", ignore = true)
    @Mapping(target = "bankName", ignore = true)
    @Mapping(target = "bankAccount", ignore = true)
    @Mapping(target = "address", ignore = true)
    AgentAuthPaymentDisburseDetailInfo toDetailInfo(AgentAuthPaymentDisburse entity);
}
