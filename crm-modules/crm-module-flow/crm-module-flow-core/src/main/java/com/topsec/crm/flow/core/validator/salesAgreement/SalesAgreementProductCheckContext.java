package com.topsec.crm.flow.core.validator.salesAgreement;

import com.topsec.crm.flow.api.vo.salesAgreement.SalesAgreementProductVo;
import lombok.Getter;

import java.util.List;

@Getter
public class SalesAgreementProductCheckContext {
    private final List<SalesAgreementProductVo> salesAgreementProductVoList;

    private final List<SalesAgreementProductVo> salesAgreementProductSnapshotVoList;


    public SalesAgreementProductCheckContext(List<SalesAgreementProductVo> salesAgreementProductVoList, List<SalesAgreementProductVo> salesAgreementProductSnapshotVoList) {
        this.salesAgreementProductVoList = salesAgreementProductVoList;
        this.salesAgreementProductSnapshotVoList = salesAgreementProductSnapshotVoList;
    }
}
