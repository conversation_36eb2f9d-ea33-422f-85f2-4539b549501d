package com.topsec.crm.flow.core.controllerhidden.customer;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.RemoteSalemanService;
import com.topsec.crm.flow.api.dto.customer.CustomerNaVo;
import com.topsec.crm.flow.api.dto.customer.CustomerNaApproveFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.customer.CustomerVo;
import com.topsec.crm.flow.core.entity.CustomerNa;
import com.topsec.crm.flow.core.entity.CustomerNaConflictSnapshot;
import com.topsec.crm.flow.core.entity.CustomerNaMain;
import com.topsec.crm.flow.core.service.ICustomerNaConflictSnapshotService;
import com.topsec.crm.flow.core.service.ICustomerNaMainService;
import com.topsec.crm.flow.core.service.ICustomerNaSalemanService;
import com.topsec.crm.flow.core.service.ICustomerNaService;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/hidden/customerNa")
@Tag(name = "NA客户审批流程相关Controller", description = "/hidden/customerNa/flow")
@RequiredArgsConstructor
@Validated
public class HiddenCustomerNaController extends BaseController {
    @Autowired
    private ICustomerNaSalemanService customerNaSalemanService;
    @Autowired
    private ICustomerNaService customerNaService;
    @Autowired
    private ICustomerNaConflictSnapshotService customerNaConflictSnapshotService;
    @Autowired
    private ICustomerNaMainService customerNaMainService;

    @PostMapping("/selecCustomerNaList")
    @Operation(summary = "查询流程中是否存在该销售和所关联的客户")
    public JsonObject<List<CustomerNaVo>> selecCustomerNaList(@RequestBody CustomerNaApproveFlowLaunchDTO launchDTO) {
        List<CustomerNa> list = customerNaService.list(new QueryWrapper<CustomerNa>()
                .eq("customer_id", launchDTO.getCustomerId())
                .eq("na_saleman_id", launchDTO.getSalemanId()));

        List<CustomerNaVo> customerNaVos = new ArrayList<>();
        list.stream().forEach(customerNa -> customerNaVos.add(HyperBeanUtils.copyPropertiesByJackson(customerNa, CustomerNaVo.class)));
        return new JsonObject<>(customerNaVos);
    }

    @GetMapping("/selectBySalemanId")
    @Operation(summary = "查询该销售正在审批的NA客户信息-排除掉传入的流程ID")
    public JsonObject<List<CustomerNaVo>> selectBySalemanId(@RequestParam String salemanId,@RequestParam String processInstanceId) {
        List<CustomerNa> list = customerNaService.list(new QueryWrapper<CustomerNa>()
                .eq("na_saleman_id", salemanId)
                .eq("is_flow", 1)
                .ne(StringUtils.isNotEmpty(processInstanceId),"process_instance_id", processInstanceId));

        List<CustomerNaVo> customerNaVos = new ArrayList<>();
        list.stream().forEach(customerNa -> customerNaVos.add(HyperBeanUtils.copyPropertiesByJackson(customerNa, CustomerNaVo.class)));
        return new JsonObject<>(customerNaVos);
    }

    //数据同步-清洗快照数据
    @GetMapping("/cleanCustomerNaSnapshot")
    public JsonObject<Boolean> cleanCustomerNaSnapshot() {
        List<CustomerNaMain> list = customerNaMainService.list();

        for (CustomerNaMain customerNaMain : list) {
            //保存客户冲突快照
            CustomerNaApproveFlowLaunchDTO launchable = new CustomerNaApproveFlowLaunchDTO();
            launchable.setProcessInstanceId(customerNaMain.getProcessInstanceId());

            List<CustomerNaConflictSnapshot> customerNaConflictSnapshots = new ArrayList<CustomerNaConflictSnapshot>();
            List<CustomerVo> customerInfos = customerNaMainService.selecConflictList(launchable);
            for (CustomerVo customerInfo : customerInfos) {
                CustomerNaConflictSnapshot n = HyperBeanUtils.copyProperties(customerInfo, CustomerNaConflictSnapshot::new);
                n.setId(UUID.randomUUID().toString());
                n.setProcessInstanceId(customerNaMain.getProcessInstanceId());
                n.setCustomerId(customerInfo.getId());
                n.setCustomerName(customerInfo.getName());
                customerNaConflictSnapshots.add(n);
            }
            customerNaConflictSnapshotService.saveBatch(customerNaConflictSnapshots);
        }

        return new JsonObject<>(true);
    }


}
