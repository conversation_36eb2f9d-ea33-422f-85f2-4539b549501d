package com.topsec.crm.flow.core.validator;

import com.topsec.crm.flow.core.util.FlowThreadExecutor;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;

/**
 *
 * 超级验证器
 * <p>支持以下三种验证模式：</p>
 * <ul>
 *     <li>顺序快速失败：顺序检查，一旦检查失败，则立即返回，不再继续检查</li>
 *     <li>并行快速失败：并行检查，一旦检查失败，则立即返回，不再继续检查</li>
 *     <li>完整并行：并行检查，全部检查完成后，返回结果</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Slf4j
public class HyperValidator<T> {
    private final List<CheckCondition<T>> checkList;

    public enum CHECK_MODE {
        SEQUENTIAL_FAIL_FAST,
        PARALLEL_FAIL_FAST,
        PARALLEL_FULL
    }

    @Getter
    private String failMessage;



    private final T context;
    private boolean executed=false;
    private boolean success=true;


    private HyperValidator(T context, List<CheckCondition<T>> checkList) {

        this.checkList=checkList;
        this.context =context;
    }

    /**
     * 检查ConditionCheckStrategy 是否满足，若存在未满足的，则失效
     * @param context context
     * @param checkList checkList
     * @return 返回该对象本身，用于后续failPostProcess 、successPostProcess链式调用
     */
    public static<T> HyperValidator<T> check(T context, List<CheckCondition<T>> checkList) {
        return check(context,checkList,CHECK_MODE.SEQUENTIAL_FAIL_FAST);
    }

    public static<T> HyperValidator<T> check(T context, List<CheckCondition<T>> checkList, CHECK_MODE checkMode) {
        HyperValidator<T> invalidator = new HyperValidator<>(context, checkList);
        if (checkMode==CHECK_MODE.SEQUENTIAL_FAIL_FAST) {
            return invalidator.sequentialFailFastCheck();
        }else if (checkMode==CHECK_MODE.PARALLEL_FAIL_FAST) {
            return invalidator.parallelFailFastCheck();
        }else if (checkMode==CHECK_MODE.PARALLEL_FULL) {
            return invalidator.parallelFullCheck();
        }
        throw new IllegalArgumentException("Invalid checkMode");
    }


    //ParallelFailFast 并行快速失败
    @SneakyThrows
    private HyperValidator<T> parallelFailFastCheck() {
        if (executed) return this;
        List<CompletableFuture<Void>> futures=new ArrayList<>();
        CountDownLatch completeCountDownLatch = new CountDownLatch(checkList.size());
        for (CheckCondition<T> strategy : checkList) {
            CompletableFuture<Void> future = FlowThreadExecutor
                    .runAsync(() ->  strategy.checkWithThrow(context));
            futures.add(future);
        }
        for (CompletableFuture<Void> future : futures) {
            future.whenComplete((v, t) -> {
                // synchronized (this) {
                if (success) {
                    if (t != null ) {
                        Throwable cause = t.getCause();
                        failMessage = Optional.ofNullable(cause).map(Throwable::getMessage)
                                .orElse(t.getMessage());
                        success = false;
                        for (CompletableFuture<Void> item : futures) {
                            if (!item.isDone()) {
                                item.cancel(true);
                            }
                        }
                    }
                }
                // }
                completeCountDownLatch.countDown();
            });
        }

        completeCountDownLatch.await();
        executed = true;
        return this;
    }


    //顺序快速失败
    private HyperValidator<T> sequentialFailFastCheck() {
        if (executed) return this;
        for (CheckCondition<T> strategy : checkList) {
            try {
                if (!strategy.check(context)) {
                    failMessage=strategy.defaultFailureReason();
                    success = false;
                    break;
                }
            } catch (Exception e) {
                failMessage=e.getMessage();
                success = false;
                break;
            }
        }
        executed=true;
        return this;
    }


    /**
        并行完整检查
     */
    private HyperValidator<T> parallelFullCheck() {
        if (executed) return this;
        List<CompletableFuture<Void>> futures=new ArrayList<>();
        List<String> failMessageList=new CopyOnWriteArrayList<>();
        for (CheckCondition<T> strategy : checkList) {
            CompletableFuture<Void> voidCompletableFuture = FlowThreadExecutor
                    .runAsync(() -> {
                        if (!strategy.check(context)) {
                            failMessageList.add(strategy.defaultFailureReason());
                            success = false;
                        }
                    })
                    .exceptionally(ex -> {
                        failMessageList.add(ex.getMessage());
                        success = false;
                        return null;
                    });
            futures.add(voidCompletableFuture);
        }
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        allFutures.join();
        failMessage= String.join(";", failMessageList);
        executed=true;
        return this;
    }

    /**
     * 失效后的动作
     * @param failFunction failFunction
     * @return R
     */
    public <R> R failPostProcess(Function<T,R> failFunction){
        if (!success){
            return failFunction.apply(context);
        }
        return null;
    }

    /**
     * 成功后的动作
     * @param successFunction successFunction
     * @return R
     */
    public <R> R successPostProcess(Function<T,R> successFunction){
        if (success){
            return successFunction.apply(context);
        }
        return null;
    }

    public boolean getResult(){
        if (executed){
            return success;
        }else {
            throw new IllegalStateException("HyperValidator has not been executed yet");
        }
    }


}
