package com.topsec.crm.flow.core.controller.borrowForSell;

import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellAttachmentDTO;
import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellElectricContractDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.service.IBorrowForSellAttachmentService;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 借转销附件信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/borrowForSellAttachment")
@Tag(name = "借转销流程", description = "/borrowForSellAttachment")
public class BorrowForSellAttachmentController {

    @Autowired
    private IBorrowForSellAttachmentService borrowForSellAttachmentService;

    @PreAuthorize
    @PreFlowPermission
    @PostMapping("/saveBorrowForSellAttachment")
    @Operation(summary = "保存借转销流程附件信息")
    public JsonObject<Boolean> saveBorrowForSellAttachment(@RequestParam String borrowId,@RequestParam Integer attachmentGroup, @RequestBody List<BorrowForSellAttachmentDTO> attachments) {
        return new JsonObject<>(borrowForSellAttachmentService.saveBorrowForSellAttachment(borrowId,attachmentGroup,attachments));
    }

    //@PreAuthorize
    //@PreFlowPermission
    @PostMapping("/generateBorrowForSellElectricContract")
    @Operation(summary = "生成借转销电子合同信息并上传到附件")
    public JsonObject<String> generateBorrowForSellElectricContract(@RequestParam String borrowId,@RequestBody BorrowForSellElectricContractDTO fillData) {
        return new JsonObject<>(borrowForSellAttachmentService.generateBorrowForSellElectricContract(borrowId,fillData,false));
    }

    //@PreAuthorize
    //@PreFlowPermission
    @PostMapping("/preViewBorrowForSellElectricContract")
    @Operation(summary = "预览借转销电子合同")
    public JsonObject<String> preViewBorrowForSellElectricContract(@RequestParam String borrowId,@RequestBody BorrowForSellElectricContractDTO fillData) {
        return new JsonObject<>(borrowForSellAttachmentService.generateBorrowForSellElectricContract(borrowId,fillData, true));
    }
}

