package com.topsec.crm.flow.core.controller.borrowForSellClean;

import com.topsec.crm.flow.api.dto.borrowForSellClean.BorrowForSellCleanDeviceDTO;
import com.topsec.crm.flow.api.dto.borrowForSellClean.BorrowForSellCleanLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.entity.BorrowForSellCleanDevice;
import com.topsec.crm.flow.core.process.impl.BorrowForSellCleanProcessService;
import com.topsec.crm.flow.core.service.BorrowForSellCleanDeviceService;
import com.topsec.crm.flow.core.service.BorrowForSellCleanService;
import com.topsec.crm.flow.core.service.ContractReviewProductOwnService;
import com.topsec.crm.flow.core.service.PerformanceReportService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: zuo_changchun
 * @CreateTime: 2025-03-04  13:34
 * @Description:
 */

@RestController
@RequestMapping("/borrowForSellClean")
@Tag(name = "借转销催办", description = "/borrowForSellClean")
@RequiredArgsConstructor
@Validated
public class BorrowForSellCleanController extends BaseController {

    private final BorrowForSellCleanService borrowForSellCleanService;

    private final BorrowForSellCleanProcessService borrowForSellCleanProcessService;
    private final BorrowForSellCleanDeviceService borrowForSellCleanDeviceService;

    private final ContractReviewProductOwnService contractReviewProductOwnService;

    private final PerformanceReportService performanceReportService;

    @PostMapping("/launch")
    @Operation(summary = "发起借转销催办流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody BorrowForSellCleanLaunchDTO launchDTO) {
        return new JsonObject<>(borrowForSellCleanProcessService.launch(launchDTO));
    }


    //@PreAuthorize
    @GetMapping("/detail/{processInstanceId}")
    @Operation(summary = "查看借转销催办流程详情")
    @PreFlowPermission
    public JsonObject<BorrowForSellCleanLaunchDTO> borrowForSellCleanDetail(@PathVariable String processInstanceId) {
        processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        return new JsonObject<>(borrowForSellCleanService.borrowForSellCleanDetail(processInstanceId));
    }





@PostMapping("/updateFeedback")
@Operation(summary = "修改清理反馈")
@PreFlowPermission
public JsonObject<Boolean> updateFeedback(@RequestBody List<BorrowForSellCleanDeviceDTO> updateList) {
    List<BorrowForSellCleanDevice> borrowForSellCleanDevices = updateList.stream()
        .filter(Objects::nonNull)
        .flatMap(dto -> {
            String psn = dto.getPsn();
            if (psn == null) {
                return Stream.empty(); // 跳过 psn 为空的项
            }
            Boolean snInPerformanceReport = performanceReportService.snInPerformanceReport(psn);
            Boolean snInContract = contractReviewProductOwnService.querySnInContract(List.of(psn)).getOrDefault(psn, false);
            if ("已转销".equals(dto.getFeedback()) && !snInPerformanceReport && !snInContract) {
                throw new CrmException(psn + "序列号反馈为【已转销】，但未查询到对应的合同！");
            }
            return Stream.of(HyperBeanUtils.copyPropertiesByJackson(dto, BorrowForSellCleanDevice.class));
        })
        .collect(Collectors.toList());
    boolean result = borrowForSellCleanDeviceService.updateBatchById(borrowForSellCleanDevices);
    return new JsonObject<>(result);
}


//    @GetMapping("/isDeviceCleaned")
//    @Operation(summary = "判断设备是否已经清理")
//    public JsonObject<Boolean> isDeviceCleaned(@RequestParam String psn) {
//        BorrowForSellCleanDevice device = borrowForSellCleanDeviceService.getDeviceByPsn(psn);
//        if (device != null) {
//            return new JsonObject<>(true);
//        } else {
//            return new JsonObject<>(false);
//        }
//    }




}