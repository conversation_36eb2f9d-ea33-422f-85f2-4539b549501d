package com.topsec.crm.flow.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.RemoteContractInvoiceService;
import com.topsec.crm.contract.api.RemoteContractOriginalDocumentService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.contract.api.entity.invoice.InvoiceProductDTO;
import com.topsec.crm.contract.api.entity.invoice.InvoiceProductQueryVO;
import com.topsec.crm.contract.api.entity.originaldocument.OriginalDocumentVO;
import com.topsec.crm.contract.api.entity.request.CrmContractAfterQuery;
import com.topsec.crm.flow.api.dto.contractInvoice.ContractInfoDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.ContractInvoicePageBase;
import com.topsec.crm.flow.api.dto.contractInvoice.InvoiceDocDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.InvoiceProcessPageVO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.ContractMakeInvoiceDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.MakeInvoicePageVO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.MakeInvoiceProductDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.returnInvoice.ContractReturnInvoiceDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.returnInvoice.MakeInvoiceInfoVO;
import com.topsec.crm.flow.api.dto.contractInvoice.returnInvoice.ReturnInvoicePageVO;
import com.topsec.crm.flow.api.dto.contractInvoice.returnInvoice.ReturnInvoiceProductDTO;
import com.topsec.crm.flow.core.entity.ContractMakeInvoice;
import com.topsec.crm.flow.core.entity.ContractReturnInvoice;
import com.topsec.crm.flow.core.mapstruct.InvoiceProductConvertor;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.CommonUtils;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyPriceStatisticsVO;
import com.topsec.tos.common.HyperBeanUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ContractInvoiceExtendServiceImpl implements ContractInvoiceExtendService {

    private final RemoteContractExecuteService remoteContractExecuteService;
    private final RemoteContractOriginalDocumentService remoteContractOriginalDocumentService;
    private final RemoteContractInvoiceService remoteContractInvoiceService;
    private final ContractMakeInvoiceService makeInvoiceService;
    private final ContractMakeInvoiceProductService makeInvoiceProductService;
    private final ContractReturnInvoiceService returnInvoiceService;
    private final ContractReturnInvoiceProductService returnInvoiceProductService;

    @Override
    public TableDataInfo getInvoiceProcessPage(String contractNumber, Integer pageSize, Integer pageNum) {
        PageHelper.clearPage();
        TableDataInfo tableDataInfo = new TableDataInfo();
        try {
            // 1.异步获取开票数据
            CompletableFuture<List<MakeInvoicePageVO>> makeInvoiceFuture = CompletableFuture.supplyAsync(() ->
                    Optional.ofNullable(makeInvoiceService.getListByContractNumber(contractNumber)).orElse(Collections.emptyList())
            );
            // 2.1 异步获取退票数据
            CompletableFuture<List<ReturnInvoicePageVO>> returnInvoiceFuture = CompletableFuture.supplyAsync(() ->
                    Optional.ofNullable(returnInvoiceService.getListByContractNumber(contractNumber)).orElse(Collections.emptyList())
            );

            // 2.2 获取最新开票数据
            CompletableFuture<Map<String, ContractMakeInvoice>> latestMakeInvoiceBatch = returnInvoiceFuture.thenApply(returnInvoicePageVOS -> {
                Map<String, List<String>> collect = returnInvoicePageVOS.stream()
                        .filter(dto -> dto.getProcessInstanceId() != null)
                        .collect(Collectors.toMap(ContractInvoicePageBase::getProcessInstanceId, ReturnInvoicePageVO::getMakeInvoiceId));
                return getLatestMakeInvoiceBatch(collect);
            });

            // 等待任务完成
            CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(makeInvoiceFuture, returnInvoiceFuture,latestMakeInvoiceBatch);
            combinedFuture.join();

            // 获取结果
            List<MakeInvoicePageVO> makeInvoicePageVOS = makeInvoiceFuture.get();
            List<ReturnInvoicePageVO> returnInvoicePageVOS = returnInvoiceFuture.get();
            Map<String, ContractMakeInvoice> lastMakeInvoice = latestMakeInvoiceBatch.get();

            // 处理开票数据
            List<InvoiceProcessPageVO> makePage = makeInvoicePageVOS.stream()
                    .map(item -> {
                        InvoiceProcessPageVO vo = HyperBeanUtils.copyProperties(item, InvoiceProcessPageVO::new);
                        vo.setBusinessType(InvoiceProcessPageVO.BusinessType.MAKE_INVOICE.getCode());
                        return vo;
                    }).toList();

            // 处理退票数据
            List<InvoiceProcessPageVO> returnPage = returnInvoicePageVOS.stream()
                    .map(item -> {
                        InvoiceProcessPageVO vo = HyperBeanUtils.copyProperties(item, InvoiceProcessPageVO::new);
                        vo.setBusinessType(InvoiceProcessPageVO.BusinessType.RETURN_INVOICE.getCode());
                        ContractMakeInvoice makeInvoiceInfo = lastMakeInvoice.get(item.getProcessInstanceId());
                        vo.setInvoiceType(makeInvoiceInfo.getInvoiceType());
                        vo.setContractCompanyName(makeInvoiceInfo.getContractCompanyName());
                        vo.setSigningCompanyName(makeInvoiceInfo.getSigningCompanyName());
                        return vo;
                    }).toList();

            // 合并结果
            List<InvoiceProcessPageVO> pageVOS = new ArrayList<>(makePage);
            pageVOS.addAll(returnPage);
            setTableData(tableDataInfo, pageVOS, pageSize, pageNum);
        } catch (Exception e) {
            throw new CrmException("获取发票流程数据失败" + e.getMessage());
        }
        return tableDataInfo;
    }


    private <T> void setTableData(TableDataInfo tableDataInfo, List<T> dataList, Integer pageSize, Integer pageNum) {
        List<T> pagedList = CommonUtils.subListPage(dataList, pageSize, pageNum);
        tableDataInfo.setList(pagedList);
        tableDataInfo.setTotalCount(dataList.size());
    }

    @Override
    public TableDataInfo getMakeInvoiceProduct(String contractNumber,String materialCode) {
        InvoiceProductQueryVO queryVO = new InvoiceProductQueryVO();
        queryVO.setContractNumber(contractNumber);
        queryVO.setMaterialCode(materialCode);
        TableDataInfo page = remoteContractInvoiceService.getUnInvoicedPageByQuery(queryVO).getObjEntity();
        List<MakeInvoiceProductDTO> list = InvoiceProductConvertor.INSTANCE.pageToMakeInvoiceProductList((List<Map<String, Object>>) page.getList());
        page.setList(list);
        page.setTotalCount(page.getTotalCount());
        return page;
    }

    @Override
    public List<ReturnInvoiceProductDTO> getReturnInvoiceProduct(List<String> processInstanceIds) {
        // 根据开票流程实例ID获取开票ID列表
        List<ContractMakeInvoiceDTO> makeInvoiceDTOS = makeInvoiceService.getListByProcessInstanceIds(processInstanceIds);
        if (CollectionUtils.isEmpty(makeInvoiceDTOS)) {
            return Collections.emptyList();
        }

        // 开票流程ID列表
        List<String> makeInvoiceIds = makeInvoiceDTOS.stream()
                .map(ContractMakeInvoiceDTO::getId)
                .collect(Collectors.toList());

        // 所有已开票产品
        List<MakeInvoiceProductDTO> makeInvoiceProducts = makeInvoiceProductService.getListByMakeInvoiceIds(makeInvoiceIds);
        if (CollectionUtils.isEmpty(makeInvoiceProducts)) {
            return Collections.emptyList();
        }

        // 查询历史退票数据
        List<String> productIds = makeInvoiceProducts.stream()
                .map(MakeInvoiceProductDTO::getId)
                .toList();

        List<ReturnInvoiceProductDTO> returnInvoiceProducts = returnInvoiceProductService.getListByMakeInvoiceProductIds(productIds);

        // 根据开票明细ID分组
        Map<String, List<ReturnInvoiceProductDTO>> returnMap = returnInvoiceProducts.stream()
                .collect(Collectors.groupingBy(ReturnInvoiceProductDTO::getMakeInvoiceProductId));

        // 预先计算每个 item 的已退金额
        Map<String, BigDecimal> returnedAmountMap = new HashMap<>();
        for (MakeInvoiceProductDTO item : makeInvoiceProducts) {
            List<ReturnInvoiceProductDTO> returnGroup = returnMap.get(item.getId());
            BigDecimal returnedAmount = BigDecimal.ZERO;
            if (returnGroup != null) {
                returnedAmount = returnGroup.stream()
                        .map(ReturnInvoiceProductDTO::getReturnInvoiceProductAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            returnedAmountMap.put(item.getId(), returnedAmount);
        }

        // 构建结果
        List<ReturnInvoiceProductDTO> result = new ArrayList<>(makeInvoiceProducts.size());
        for (MakeInvoiceProductDTO item : makeInvoiceProducts) {
            BigDecimal returnedAmount = returnedAmountMap.getOrDefault(item.getId(), BigDecimal.ZERO);
            if (returnedAmount.compareTo(item.getFinalInvoiceAmount()) < 0) {
                ReturnInvoiceProductDTO dto = createReturnInvoiceProductDTO(item);
                dto.setReturnableInvoiceAmount(item.getFinalInvoiceAmount().subtract(returnedAmount));
                result.add(dto);
            }
        }
        return result;
    }

    // 提取公共字段赋值逻辑
    private ReturnInvoiceProductDTO createReturnInvoiceProductDTO(MakeInvoiceProductDTO item) {
        ReturnInvoiceProductDTO dto = new ReturnInvoiceProductDTO();
        dto.setMakeInvoiceProductId(item.getId());
        dto.setUnInvoicedAmount(item.getUnInvoicedAmount());
        dto.setContractNumber(item.getContractNumber());
        dto.setMaterialCode(item.getMaterialCode());
        dto.setProductName(item.getProductName());
        dto.setPn(item.getPn());
        dto.setSpecification(item.getSpecification());
        dto.setProductQuantity(item.getProductQuantity());
        dto.setTaxRate(item.getTaxRate());
        dto.setDealPrice(item.getDealPrice());
        dto.setDealTotalPrice(item.getDealTotalPrice());
        dto.setInvoiceProductId(item.getInvoiceProductId());
        dto.setInvoicedAmount(item.getInvoicedAmount());
        return dto;
    }

    @Override
    public List<MakeInvoiceProductDTO> getMakeInvoiceProductList(String contractNumber) {
        InvoiceProductQueryVO queryVO = new InvoiceProductQueryVO();
        queryVO.setContractNumber(contractNumber);
        List<InvoiceProductDTO> productDTOS = remoteContractInvoiceService.getUnInvoicedListByContractNumber(queryVO).getObjEntity();
        if (productDTOS != null){
            return InvoiceProductConvertor.INSTANCE.toMakeInvoiceProductList(productDTOS);
        }
        return Collections.emptyList();
    }

    @Override
    public ContractInfoDTO getContractInfo(String contractNumber) {
        CrmContractExecuteVO contractExecuteVO = remoteContractExecuteService.getByContractNumber(contractNumber).getObjEntity();
        if (contractExecuteVO == null) {
            throw new CrmException("合同信息异常");
        }

        ContractInfoDTO contractInfoDTO = HyperBeanUtils.copyProperties(contractExecuteVO, ContractInfoDTO::new);
        String contractNum = contractExecuteVO.getContractNumber();

        // 异步任务0：先获取 contractList（供后续两个任务使用）
        CompletableFuture<PageUtils<CrmContractExecuteVO>> contractListFuture = CompletableFuture.supplyAsync(() -> {
            CrmContractAfterQuery crmContractAfterQuery = new CrmContractAfterQuery();
            CrmContractAfterQuery.BaseQuery baseQuery = new CrmContractAfterQuery.BaseQuery();
            baseQuery.setContractCompanyId(contractExecuteVO.getContractCompanyId());
            crmContractAfterQuery.setNeedContractType(true);
            crmContractAfterQuery.setBaseQuery(baseQuery);

            return Optional.ofNullable(
                    remoteContractExecuteService.pageByCondition(crmContractAfterQuery).getObjEntity()
            ).orElse(new PageUtils<>());
        });

        // 异步任务1：计算签约单位欠款金额
        CompletableFuture<BigDecimal> debtFuture = contractListFuture.thenApply(contractList ->
                contractList.getList().stream()
                        .map(CrmContractExecuteVO::getDebtAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );

        // 异步任务2：计算超期应收金额
        CompletableFuture<BigDecimal> overdueFuture = contractListFuture.thenApply(contractList ->
                contractList.getList().stream()
                        .map(CrmContractExecuteVO::getOverdueAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );

        // 异步任务3：是否合同原件确认
        CompletableFuture<Integer> originalConfirmFuture = CompletableFuture.supplyAsync(() -> {
            List<OriginalDocumentVO> originalDocumentVOS = Optional.ofNullable(
                    remoteContractOriginalDocumentService.listDocumentByContractNumbers(List.of(contractNum), false).getObjEntity()
            ).orElse(Collections.emptyList());
            return originalDocumentVOS.isEmpty() ? 2 : 1;
        });

        // 异步任务4：获取合同价格统计
        CompletableFuture<Void> priceStatisticsFuture = CompletableFuture.runAsync(() -> {
            CrmProjectDirectlyPriceStatisticsVO priceStatisticsVO = Optional.ofNullable(
                    remoteContractExecuteService.getContractPriceStatistics(contractNum).getObjEntity()
            ).orElse(new CrmProjectDirectlyPriceStatisticsVO());

            Optional.ofNullable(priceStatisticsVO.getTotal()).ifPresent(total -> {
                contractInfoDTO.setDealPrice(total.getDealPrice());
                contractInfoDTO.setDealPriceNoTax(total.getDealPriceNoTax());
                contractInfoDTO.setGrossMargin(total.getGrossMargin());
                contractInfoDTO.setGrossMarginRatio(total.getGrossMarginRatio());
                contractInfoDTO.setDiscount(total.getDiscount());
                contractInfoDTO.setFinalPrice(total.getFinalPrice());
            });
        });

        // 异步任务5：合同已开票总金额
        CompletableFuture<BigDecimal> invoicedTotalFuture = CompletableFuture.supplyAsync(() -> getMakeInvoiceAmountTotal(contractNum));

        // 异步任务6：合同已退票总金额
        CompletableFuture<BigDecimal> refundTotalFuture = CompletableFuture.supplyAsync(() -> getReturnInvoiceAmountTotal(contractNum));

        // 等待所有异步任务完成
        CompletableFuture.allOf(
                contractListFuture,
                debtFuture,
                overdueFuture,
                originalConfirmFuture,
                priceStatisticsFuture,
                invoicedTotalFuture,
                refundTotalFuture
        ).join();

        // 设置最终结果
        try {
            contractInfoDTO.setCompanyNameDebtAmount(debtFuture.get());
            contractInfoDTO.setCompanyNameOverDueAmount(overdueFuture.get());
            contractInfoDTO.setIsContractOriginalConfirm(originalConfirmFuture.get());

            // 属性转换
            contractInfoDTO.setContractVerificationAmount(contractExecuteVO.getReturnedAmount());
            contractInfoDTO.setContractDebtAmount(contractExecuteVO.getDebtAmount());

            // 计算回款完成率 & 开票比例
            BigDecimal contractAmount = contractExecuteVO.getContractAmount();
            BigDecimal returnedAmount = contractExecuteVO.getReturnedAmount();
            BigDecimal invoicedAmount = contractExecuteVO.getInvoicedAmount();
            BigDecimal paymentRate = calculateRatio(returnedAmount, contractAmount);
            BigDecimal invoiceRate = calculateRatio(invoicedAmount, contractAmount);

            // 回款比例
            contractInfoDTO.setPaymentVerificationRate(paymentRate);
            // 开票比例
            contractInfoDTO.setInvoiceRate(invoiceRate);
            // 已开票金额
            contractInfoDTO.setInvoicedAmount(invoicedAmount);
            // 开票总金额
            contractInfoDTO.setMakeInvoiceAmount(invoicedTotalFuture.get());
            // 退票总金额
            contractInfoDTO.setReturnInvoiceAmount(refundTotalFuture.get());
        } catch (Exception e) {
            throw new CrmException("获取合同信息失败");
        }

        return contractInfoDTO;
    }


    @Override
    public ContractMakeInvoiceDTO getLastMakeInvoiceInfo(List<String> processInstanceIds) {
        if (CollectionUtils.isEmpty(processInstanceIds)) {
            return null;
        }
        ContractMakeInvoice one = makeInvoiceService.getOne(new LambdaQueryWrapper<ContractMakeInvoice>()
                .in(ContractMakeInvoice::getProcessInstanceId, processInstanceIds)
                .last("LIMIT 1")
                .orderByDesc(ContractMakeInvoice::getCreateTime)
        );
        return HyperBeanUtils.copyProperties(one, ContractMakeInvoiceDTO::new);
    }

    @Override
    public MakeInvoiceInfoVO getLastMakeInvoiceInfo(String processInstanceId) {
        if (StringUtils.isBlank(processInstanceId)) {
            return null;
        }

        ContractReturnInvoice contractReturnInvoice = returnInvoiceService.getOne(
                new QueryWrapper<ContractReturnInvoice>().lambda()
                        .eq(ContractReturnInvoice::getProcessInstanceId, processInstanceId)
                        .eq(ContractReturnInvoice::getDelFlag, false)
        );

        if (contractReturnInvoice == null) {
            return null;
        }

        List<String> makeInvoiceId = contractReturnInvoice.getMakeInvoiceId();
        if (makeInvoiceId == null || makeInvoiceId.isEmpty()) {
            return null;
        }

        // 获取 processNumbers
        List<String> processNumbers = makeInvoiceService.lambdaQuery()
                .in(ContractMakeInvoice::getId, makeInvoiceId)
                .eq(ContractMakeInvoice::getDelFlag, false)
                .list().stream().map(ContractMakeInvoice::getProcessNumber).collect(Collectors.toList());

        // 获取总金额
        Map<String, BigDecimal> amountMap = makeInvoiceProductService.getTotalAmountByMakeInvoiceId(makeInvoiceId);
        BigDecimal totalAmount = Optional.ofNullable(amountMap)
                .map(Map::values).orElse(Collections.emptySet())
                .stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        // 获取最新的一条 ContractMakeInvoice
        ContractMakeInvoice latestInvoice = makeInvoiceService.getOne(
                new LambdaQueryWrapper<ContractMakeInvoice>()
                        .in(ContractMakeInvoice::getId, makeInvoiceId)
                        .orderByDesc(ContractMakeInvoice::getCreateTime)
                        .last("LIMIT 1")
        );

        MakeInvoiceInfoVO makeInvoiceInfoVO = HyperBeanUtils.copyProperties(latestInvoice, MakeInvoiceInfoVO::new);
        makeInvoiceInfoVO.setMakeInvoiceProcessNumber(processNumbers);
        makeInvoiceInfoVO.setMakeInvoiceAmount(totalAmount);
        return makeInvoiceInfoVO;
    }

    /**
     * 批量获取最新的一条开票记录
     * @param makeInvoiceIdGroup map<退票流程实例ID, 退票关联的开票IDList>
     * @return map<退票流程实例ID, 最新开票记录>
     */
    public Map<String, ContractMakeInvoice> getLatestMakeInvoiceBatch(Map<String, List<String>> makeInvoiceIdGroup) {
        Map<String, ContractMakeInvoice> result = new HashMap<>();

        if (makeInvoiceIdGroup == null || makeInvoiceIdGroup.isEmpty()) {
            return result;
        }

        // 收集所有开票ID
        Set<String> allInvoiceIds = makeInvoiceIdGroup.values().stream()
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toSet());

        if (!allInvoiceIds.isEmpty()) {
            // 查询所有相关开票记录，并按创建时间倒序排列
            List<ContractMakeInvoice> invoices = makeInvoiceService.list(new QueryWrapper<ContractMakeInvoice>().lambda()
                    .in(ContractMakeInvoice::getId, allInvoiceIds)
                    .eq(ContractMakeInvoice::getDelFlag, false)
                    .orderByDesc(ContractMakeInvoice::getCreateTime));

            // 构建每个开票ID对应的第一条记录（即最新）
            Map<String, ContractMakeInvoice> latestInvoiceMap = new HashMap<>();
            for (ContractMakeInvoice invoice : invoices) {
                latestInvoiceMap.putIfAbsent(invoice.getId(), invoice);
            }

            // 填充结果
            for (Map.Entry<String, List<String>> entry : makeInvoiceIdGroup.entrySet()) {
                String processInstanceId = entry.getKey();
                List<String> invoiceIds = entry.getValue();

                if (CollectionUtils.isEmpty(invoiceIds)) {
                    result.put(processInstanceId, null);
                    continue;
                }

                ContractMakeInvoice latestInvoice = latestInvoiceMap.get(invoiceIds.get(0));
                result.put(processInstanceId, latestInvoice);
            }
        } else {
            makeInvoiceIdGroup.forEach((k, v) -> result.put(k, null));
        }

        return result;
    }


    @Override
    public List<InvoiceDocDTO> getMakeInvoiceDoc(String contractNumber) {
        if (StringUtils.isBlank(contractNumber)) {
            return Collections.emptyList();
        }
        List<InvoiceDocDTO> invoiceDocDTOS = new ArrayList<>();
        List<ContractMakeInvoice> makeInvoices = makeInvoiceService.lambdaQuery()
                .eq(ContractMakeInvoice::getContractNumber, contractNumber)
                .eq(ContractMakeInvoice::getDelFlag, false)
                .list();
        if (!CollectionUtils.isEmpty(makeInvoices)) {
            for (ContractMakeInvoice makeInvoice : makeInvoices) {
                List<InvoiceDocDTO> invoiceDoc = makeInvoice.getInvoiceDoc();
                if (invoiceDoc != null) {
                    invoiceDoc.forEach(item -> {
                        item.setCreateUser(makeInvoice.getCreateUser());
                        item.setCreateTime(makeInvoice.getCreateTime());
                    });
                    invoiceDocDTOS.addAll(invoiceDoc);
                }
            }
        }
        NameUtils.setName( invoiceDocDTOS);
        return invoiceDocDTOS;
    }

    @Override
    public BigDecimal getMakeInvoiceAmountTotal(String contractNumber) {
        List<MakeInvoicePageVO> invoiceList = makeInvoiceService.getListByContractNumber(contractNumber);
        List<String> invoiceIds = Optional.ofNullable(invoiceList).orElse(Collections.emptyList()).stream()
                .map(ContractInvoicePageBase::getId)
                .collect(Collectors.toList());

        Map<String, BigDecimal> invoiceAmountMap = makeInvoiceProductService.getTotalAmountByMakeInvoiceId(invoiceIds);
        return Optional.ofNullable(invoiceAmountMap).map(Map::values)
                .orElse(Collections.emptySet()).stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getReturnInvoiceAmountTotal(String contractNumber) {
        List<ContractReturnInvoiceDTO> returnList = returnInvoiceService.getReturnListByContractNumber(contractNumber);
        List<String> returnIds = Optional.ofNullable(returnList).orElse(Collections.emptyList()).stream()
                .map(ContractReturnInvoiceDTO::getId)
                .collect(Collectors.toList());

        Map<String, BigDecimal> refundAmountMap = returnInvoiceProductService.getTotalAmountByReturnInvoiceId(returnIds);
        return Optional.ofNullable(refundAmountMap).map(Map::values)
                .orElse(Collections.emptySet()).stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算比例
     */
    public BigDecimal calculateRatio(BigDecimal numerator, BigDecimal denominator) {
        if (numerator == null) {
            numerator = BigDecimal.ZERO;
        }
        if (denominator == null || denominator.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return numerator.divide(denominator, 10, RoundingMode.HALF_UP);
    }

}
