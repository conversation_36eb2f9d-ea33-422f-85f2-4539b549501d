package com.topsec.crm.flow.core.controller.contractreceivable;

import com.topsec.crm.flow.api.dto.contractreceivable.ContractReceivableChildDTO;
import com.topsec.crm.flow.api.dto.contractreceivable.ContractReceivableDetailDTO;
import com.topsec.crm.flow.api.dto.contractreceivable.ContractReceivableMainDTO;
import com.topsec.crm.flow.api.dto.contractreceivable.check.ReceivableConditionDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.ContractReceivableDetail;
import com.topsec.crm.flow.core.entity.ContractReceivableMain;
import com.topsec.crm.flow.core.error.ErrorDetail;
import com.topsec.crm.flow.core.service.ContractReceivableDetailService;
import com.topsec.crm.flow.core.service.ContractReceivableFollowService;
import com.topsec.crm.flow.core.service.ContractReceivableMainService;
import com.topsec.crm.flow.core.service.ContractReceivableService;
import com.topsec.crm.flow.core.service.impl.ContractReceivableServiceImpl;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosDepartmentClient;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 应收催款
 *
 * <AUTHOR>
 * @date 2024/12/16 15:31
 */
@RestController
@RequestMapping("/contractReceivable")
@Tag(name = "应收催款流程", description = "/contractReceivable")
@RequiredArgsConstructor
public class ContractReceivableController extends BaseController {

    private final ContractReceivableMainService mainService;

    private final ContractReceivableService receivableService;

    private final ContractReceivableFollowService followService;;

    private final TosDepartmentClient tosDepartmentClient;

    private final ContractReceivableDetailService detailService;

    @GetMapping("/receivableMainBasicInfo")
    @Operation(summary = "主应收催款基础信息", method = "GET")
    @PreFlowPermission
    public JsonObject<ContractReceivableMainDTO> receivableMainBasicInfo(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ContractReceivableMainDTO receivableMainDTO = mainService.getByProcessInstanceId(processInstanceId);
        ContractReceivableServiceImpl.initDeptLeaderName(receivableMainDTO, tosDepartmentClient);
        return new JsonObject<>(receivableMainDTO);
    }

    @GetMapping("/pageReceivableMainDetail")
    @Operation(summary = "主应收催款分页查询催缴跟进页面数据", method = "GET")
    @PreFlowPermission
    public JsonObject<PageUtils<ContractReceivableDetailDTO>> pageReceivableMainDetail(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        ContractReceivableMainDTO mainDTO = mainService.getByProcessInstanceId(processInstanceId);
        startPage();
        return new JsonObject<>(receivableService.pageReceivableMainDetail(mainDTO.getId()));
    }

    @PostMapping("/saveOrUpdateReceivableFollow")
    @Operation(summary = "主应收催款保存或更新催缴跟进信息", method = "POST")
    @PreFlowPermission
    public JsonObject<Boolean> saveOrUpdateReceivableFollow(@RequestBody ContractReceivableDetailDTO dto) {
        ContractReceivableDetail detail = detailService.getById(dto.getId());
        String contractReceivableMainId = detail.getContractReceivableMainId();
        ContractReceivableMain receivableMain = mainService.getById(contractReceivableMainId);
        if (receivableMain == null) {
            throw new CrmException("应收催款不存在");
        }
        PreFlowPermissionAspect.checkProcessInstanceId(receivableMain.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(receivableService.saveOrUpdateReceivableFollow(dto));
    }

    @PostMapping("/checkReceivableCondition")
    @Operation(summary = "主应收催款审批校验")
    @PreFlowPermission
    JsonObject<List<ErrorDetail>> checkReceivableCondition(@RequestBody ReceivableConditionDTO receivableConditionDTO){
        PreFlowPermissionAspect.checkProcessInstanceId(receivableConditionDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        List<ErrorDetail> errorDetails = receivableService.checkReceivableCondition(receivableConditionDTO);
        return new JsonObject<>(errorDetails);
    }

    @GetMapping("/pageChildReceivable")
    @Operation(summary = "分页查询子应收催款流程信息（主页面的子应收催款列表）")
    @PreFlowPermission
    public JsonObject<PageUtils<ContractReceivableChildDTO>> pageChildReceivable(@RequestParam String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        startPage();
        return new JsonObject<>(receivableService.pageChildReceivable(processInstanceId));
    }

    @GetMapping("/pageReceivableDetailByContractNumber")
    @Operation(summary = "根据合同号查询这个合同所有的催缴跟进记录（历史催缴）")
    @PreFlowPermission
    public JsonObject<PageUtils<ContractReceivableDetailDTO>> pageReceivableDetailByContractNumber(@RequestParam String contractNumber) {
        // todo 这块的权限需要产品确定
        startPage();
        return new JsonObject<>(receivableService.pageReceivableDetailByContractNumber(contractNumber));
    }

    @GetMapping("/getReceivableLatelyByContractNumber")
    @Operation(summary = "根据合同号查询这个合同最新的催缴信息(目前是给催款证据用)")
    @PreFlowPermission
    public JsonObject<ContractReceivableDetailDTO> getReceivableLatelyByContractNumber(@RequestParam String contractNumber) {
        // todo 这块的权限需要产品确定
        List<ContractReceivableDetailDTO> receivableLatelyByContractNumberBatch = receivableService.getReceivableLatelyByContractNumberBatch(Set.of(contractNumber));
        if (CollectionUtils.isNotEmpty(receivableLatelyByContractNumberBatch)) {
            return new JsonObject<>(receivableLatelyByContractNumberBatch.get(0));
        }
        return new JsonObject<>(new ContractReceivableDetailDTO());
    }


}
