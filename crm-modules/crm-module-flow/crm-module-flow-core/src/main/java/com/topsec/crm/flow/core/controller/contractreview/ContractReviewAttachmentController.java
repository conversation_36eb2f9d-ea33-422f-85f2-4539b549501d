package com.topsec.crm.flow.core.controller.contractreview;

import com.topsec.crm.flow.api.dto.contractreview.fileinfo.ContractElectricContractDTO;
import com.topsec.crm.flow.api.dto.contractreview.fileinfo.ContractReviewAttachmentDTO;
import com.topsec.crm.flow.api.dto.contractreview.response.ContractAttachmentStatus;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.service.ContractReviewAttachmentService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合同评审附件
 *
 * <AUTHOR>
 * @date 2024/7/26 14:41
 */
@RestController
@RequestMapping("/contractAttachment")
@Tag(name = "合同评审附件相关", description = "/contractAttachment")
@RequiredArgsConstructor
@Validated
public class ContractReviewAttachmentController extends BaseController {

     private final ContractReviewAttachmentService contractReviewAttachmentService;


}
