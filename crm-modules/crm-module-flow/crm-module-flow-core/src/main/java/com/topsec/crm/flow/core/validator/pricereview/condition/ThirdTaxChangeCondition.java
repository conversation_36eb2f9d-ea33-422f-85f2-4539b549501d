package com.topsec.crm.flow.core.validator.pricereview.condition;

import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductThirdDTO;
import com.topsec.crm.flow.core.validator.CheckCondition;
import com.topsec.crm.flow.core.validator.pricereview.PriceReviewCheckContext;
import com.topsec.crm.flow.core.validator.pricereview.ProductCompareUtil;

import java.util.List;
import java.util.Map;


/**
 * 第三方税率、成交价、数量变化，价审失效。
 * <AUTHOR>
 */
public class ThirdTaxChangeCondition implements CheckCondition<PriceReviewCheckContext> {


    @Override
    public boolean check(PriceReviewCheckContext context)  {
        Map<String, List<PriceReviewProductThirdDTO>> currentMap = ProductCompareUtil.convert(context.getProjectDetail().getProductThirdList());
        Map<String, List<PriceReviewProductThirdDTO>> snapshotMap = ProductCompareUtil.convert(context.getProjectSnapshot().getProductThirdList());
        return ProductCompareUtil.validate(currentMap, snapshotMap, "taxRate")
                && ProductCompareUtil.validate(currentMap, snapshotMap, "dealPrice");
    }


    @Override
    public String defaultFailureReason() {
        return "第三方产品税率、成交价或数量变化";
    }
}
