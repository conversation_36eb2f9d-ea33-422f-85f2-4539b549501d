package com.topsec.crm.flow.core.controllerhidden.deemedDirectRecord;

import com.topsec.crm.flow.core.service.DeemedDirectRecordService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/hidden/flow/deemedDirectRecord")
@Tag(name = "签约单位备案流程相关Controller", description = "/hidden/deemedDirectRecord/flow")
@RequiredArgsConstructor
@Validated
public class HiddenDeemedDirectRecordController extends BaseController {
    @Resource
    private DeemedDirectRecordService deemedDirectRecordService;

    /**
     * 通过合同id判断的是不是有在走的
     * */
    @GetMapping("/IsExistByContractId")
    @Operation(summary = "通过合同评审id查询是否存在")
    public JsonObject<Boolean> IsExistByContractId(String contractId){
        return new JsonObject<>(deemedDirectRecordService.IsExistByContractId(contractId));
    }

    /**
     * 通过签约单位判断是不是有生效的
     * */
    @GetMapping("/IsExistByCompanyName")
    @Operation(summary = "通过签约单位查询是否已审")
    public JsonObject<Boolean> IsExistByCompanyName(String companyName){
        return new JsonObject<>(deemedDirectRecordService.IsExistByCompanyName(companyName));
    }

    @GetMapping("/IsExistByCompanyNameBatch")
    @Operation(summary = "通过签约单位查询是否已审")
    public JsonObject<Map<String, Boolean>> IsExistByCompanyNameBatch(@RequestParam Set<String> companyNames){
        return new JsonObject<>(deemedDirectRecordService.IsExistByCompanyNameBatch(companyNames));
    }
}
