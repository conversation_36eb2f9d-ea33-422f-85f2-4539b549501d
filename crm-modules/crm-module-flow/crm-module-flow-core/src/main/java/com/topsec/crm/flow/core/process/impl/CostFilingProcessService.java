package com.topsec.crm.flow.core.process.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.contract.api.RemoteCostFilingService;
import com.topsec.crm.contract.api.entity.costFiling.CrmCostFilingDetailDismantle;
import com.topsec.crm.contract.api.entity.costFiling.CrmCostFilingDetailVO;
import com.topsec.crm.contract.api.entity.costFiling.CrmCostFilingVO;
import com.topsec.crm.flow.api.dto.costFiling.CostFilingFlowLaunchDTO;
import com.topsec.crm.flow.core.entity.CostFiling;
import com.topsec.crm.flow.core.entity.CostFilingDetail;
import com.topsec.crm.flow.core.entity.ProcessExtensionInfo;
import com.topsec.crm.flow.core.mapper.CostFilingDetailMapper;
import com.topsec.crm.flow.core.process.AbstractProcessService;
import com.topsec.crm.flow.core.process.ProcessTypeEnum;
import com.topsec.crm.flow.core.service.CostContractAttachmentService;
import com.topsec.crm.flow.core.service.CostContractService;
import com.topsec.crm.flow.core.service.ICostFilingDetailService;
import com.topsec.crm.flow.core.service.ICostFilingService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.enums.ProcessDefinitionKeyEnum;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.vo.TosDepartmentVO;
import com.topsec.vo.FlowStateInfoVo;
import com.topsec.vo.task.TfsTaskVo;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class CostFilingProcessService extends AbstractProcessService<CostFilingFlowLaunchDTO> {

    private final ICostFilingService iCostFilingService;

    private final ICostFilingDetailService iCostFilingDetailService;

    @Resource
    private CostFilingDetailMapper costFilingDetailMapper;

    private final CostContractAttachmentService costContractAttachmentService;

    private final RemoteCostFilingService remoteCostFilingService;

    private final CostContractService costContractService;

    @Override
    public ProcessTypeEnum processType() {
        return ProcessTypeEnum.COST_FILING;
    }

    @Override
    protected void preProcess(CostFilingFlowLaunchDTO launchable) {
        Boolean automatic = StringUtils.isNotEmpty(launchable.getCostFilingBeanInfoLaunchDTO().getContractNumber())? true:false;
        launchable.setMatter("费用备案");
        Map<String,Object> variableMap= new HashMap<>();
        //该人员为测试用正式流程无需传值
//        variableMap.put("assigneeList", Collections.singleton("931e383fb30649a090da016ad9447f50"));
        //是否自动生成流程
        variableMap.put("coverCharge",automatic);
        if (automatic){
//            TfsNodeVo tfsNodeVo = new TfsNodeVo();
//            tfsNodeVo.setProcessDefinitionKey(ProcessDefinitionKeyEnum.EXPENSEFILING_KEY);
//            JsonObject<Map<String, List<String>>> mapJsonObject = tfsNodeClient.queryNodeAssigneeList(tfsNodeVo);
            JsonObject<List<String>> listJsonObject = tfsNodeClient.selectSpecifyNodeAssigneeList(ProcessDefinitionKeyEnum.EXPENSE_FILING_KEY.getValue(), "expenseFiling_02");
            if(listJsonObject.isSuccess()){
//                Map<String, List<String>> objEntity = mapJsonObject.getObjEntity();
                List<String> assigneeList = listJsonObject.getObjEntity();
                launchable.setAssigneeList(CollectionUtil.toTreeSet(assigneeList, Comparator.naturalOrder()));
            }
        }

        launchable.setVariables(variableMap);
//        launchable.setIsSkip(false);
    }

    @Override
    protected String afterEngineLaunch(ProcessExtensionInfo processInfo, CostFilingFlowLaunchDTO launchable) {
        CostFiling costFiling = HyperBeanUtils.copyProperties(launchable.getCostFilingBeanInfoLaunchDTO(),CostFiling::new);
        if(StringUtils.isEmpty(costFiling.getContractNumber())){
            FlowPerson flowPerson = AccountAccquireUtils.convertGetAccountSingle(UserInfoHolder.getCurrentPersonId());
            List<TosDepartmentVO> departmentVOS = costContractService.getTosDepartmentVOByPersonId(flowPerson.getPersonId());
            departmentVOS = departmentVOS.stream().filter(entity-> entity.getUuid().equals(costFiling.getDeptId())).toList();
            if (CollectionUtils.isEmpty(departmentVOS)){
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }

        if(CollectionUtils.isEmpty(launchable.getCostFilingBeanInfoLaunchDTO().getDetailInfoDTOS())){
            throw new CrmException("至少添加一行明细！");
        }
        if (iCostFilingService.save(costFiling)){
            iCostFilingDetailService.saveCostFilingDetail(launchable.getCostFilingBeanInfoLaunchDTO().getDetailInfoDTOS(),costFiling.getId());
            if(!CollectionUtils.isEmpty(launchable.getCostFilingBeanInfoLaunchDTO().getCostContractAttachmentInfoDTOS())){
                costContractAttachmentService.saveAttachments(launchable.getCostFilingBeanInfoLaunchDTO().getCostContractAttachmentInfoDTOS(),costFiling.getId(),"费用备案");
            }
        }
        return costFiling.getId();
    }

    @Override
    public void handleProcessing(FlowStateInfoVo flowInfo) {

    }

    @Override
    public void handlePass(FlowStateInfoVo flowInfo) {
        CostFiling costFiling = iCostFilingService.getOne(new QueryWrapper<CostFiling>().lambda().eq(CostFiling::getDelFlag,0).eq(CostFiling::getProcessInstanceId,flowInfo.getProcessInstanceId()));
        CrmCostFilingVO crmCostFilingVO = HyperBeanUtils.copyProperties(costFiling,CrmCostFilingVO::new);
        List<CostFilingDetail> costFilingDetails = iCostFilingDetailService.list(new QueryWrapper<CostFilingDetail>().lambda()
                .eq(CostFilingDetail::getCostFilingId,costFiling.getId())
                .eq(CostFilingDetail::getDelFlag,0)
                .isNull(CostFilingDetail::getParentId).orderByDesc(CostFilingDetail::getCreateTime));
        List<CrmCostFilingDetailVO> crmCostFilingDetailVOS = HyperBeanUtils.copyListProperties(costFilingDetails,CrmCostFilingDetailVO::new);
        crmCostFilingDetailVOS.stream().forEach(crmCostFilingDetailVO -> {
            List<CostFilingDetail> details = iCostFilingDetailService.list(new QueryWrapper<CostFilingDetail>().lambda()
                    .eq(CostFilingDetail::getParentId,crmCostFilingDetailVO.getId()).eq(CostFilingDetail::getDelFlag,0));
            List<CrmCostFilingDetailDismantle> dismantleInfos = HyperBeanUtils.copyListProperties(details,CrmCostFilingDetailDismantle::new);
            crmCostFilingDetailVO.setCrmCostFilingDetailDismantles(dismantleInfos);
        });
        crmCostFilingVO.setDetailVOS(crmCostFilingDetailVOS);
        remoteCostFilingService.saveCostFiling(crmCostFilingVO);
    }



    @Override
    public void handlePassBack(FlowStateInfoVo flowInfo) {

    }

    @Override
    public void handleProcessingBack(FlowStateInfoVo flowInfo) {

    }

    @Override
        public Pair<Boolean, String> checkFilling(TfsTaskVo taskVo) {
        return Pair.of(true, null);
    }
}
