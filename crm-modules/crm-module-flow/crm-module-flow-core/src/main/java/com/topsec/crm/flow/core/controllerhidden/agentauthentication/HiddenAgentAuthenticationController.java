package com.topsec.crm.flow.core.controllerhidden.agentauthentication;

import com.topsec.crm.flow.api.dto.agentauthentication.AgentAuthenticationDetailVO;
import com.topsec.crm.flow.core.service.AgentAuthenticationService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/hidden/agentAuthentication")
@Tag(name = "业绩上报")
@RequiredArgsConstructor
public class HiddenAgentAuthenticationController extends BaseController {

    private final AgentAuthenticationService agentAuthenticationService;

    @GetMapping("/getAgentAuthenticationDetailByProcessInstanceId")
    @Operation(summary = "根据渠道认证实例ID查询流程详细信息")
    public JsonObject<AgentAuthenticationDetailVO> getAgentAuthenticationDetailByProcessInstanceId(@RequestParam String processInstanceId, @RequestParam String agentId){
        return new JsonObject<>(agentAuthenticationService.selectCrmAgentAuthenticationDetailByProcessInstanceId(processInstanceId, agentId));
    }
}
