package com.topsec.crm.flow.core.controller.handoverProcess;

import com.topsec.crm.flow.api.vo.handoverProcess.HandoverInitiateVO;
import com.topsec.crm.flow.api.vo.handoverProcess.HandoverStaffVO;
import com.topsec.crm.flow.core.service.HandoverProcessInitiateService;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import com.topsec.vo.node.ApproveNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/handoverProcessInitiate")
@Tag(name = "交接流程发起Controller", description = "/handoverProcessInitiate")
@RequiredArgsConstructor
@Validated
public class HandoverProcessInitiateController {

    private final HandoverProcessInitiateService handoverProcessInitiateService;

    @Operation(summary = "交接流程发起(同步天勤勿用)")
    @GetMapping("/saveHandoverProcessReviewMain")
    @PreAuthorize()
    public JsonObject<Boolean> saveHandoverProcessReviewMain(){
        handoverProcessInitiateService.saveHandoverProcessReviewMain();
        return new JsonObject<>(true);
    }

    @Operation(summary = "离职交接流程发起(手动添加)")
    @GetMapping("/saveHandoverProcessQuitDetail")
    @PreAuthorize()
    public JsonObject<Boolean> saveHandoverProcessQuitDetail(@RequestParam String personId){
        handoverProcessInitiateService.saveHandoverProcessQuitDetail(personId);
        return new JsonObject<>(true);
    }

    @Operation(summary = "调动交接流程发起(手动添加)")
    @GetMapping("/saveHandoverProcessTransferDetail")
    @PreAuthorize()
    public JsonObject<Boolean> saveHandoverProcessTransferDetail(@RequestParam String personId){
        handoverProcessInitiateService.saveHandoverProcessTransferDetail(personId);
        return new JsonObject<>(true);
    }

    @Operation(summary = "获取人员是否可以发起交接")
    @PostMapping("/getHandoverInitiateQueryMap")
    @PreAuthorize()
    public JsonObject<Map<String, HandoverInitiateVO>> getHandoverInitiateQueryMap(@RequestBody List<HandoverStaffVO> handoverStaffs){
        return new JsonObject<>(handoverProcessInitiateService.getHandoverInitiateQueryMap(handoverStaffs));
    }

    @Operation(summary = "获取交接流程审批节点")
    @GetMapping("/getHandoverApproveNodeMap")
    @PreAuthorize()
    public JsonObject<Map<String, Set<ApproveNode>>> getHandoverApproveNodeMap(@RequestParam List<String> platformIds){
        return new JsonObject<>(handoverProcessInitiateService.getHandoverApproveNodeMap(platformIds));
    }


}
