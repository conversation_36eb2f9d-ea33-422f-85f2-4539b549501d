package com.topsec.crm.flow.core.controller.flow;

import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.query.FormListQuery;
import com.topsec.query.FormQuery;
import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsCcFormClient;
import com.topsec.vo.cc.TfsCcFormVo;
import com.topsec.vo.task.Approved;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/toCc")
@Tag(name = "流程抄送相关Controller", description = "/toCc")
@RequiredArgsConstructor
@Validated
public class FlowCcController extends BaseController {

    private final TfsCcFormClient tfsCcFormClient;


    @PostMapping("/cclist")
    @Operation(summary = "根基流程实例id查询抄送人")
    @PreFlowPermission
    public JsonObject<List<Approved>> cclist(@RequestBody FormListQuery formListQuery){
        if(Objects.isNull(formListQuery)){
            throw new CrmException("参数不能为空");
        }
        String processInstanceId = formListQuery.getProcessInstanceId();
        if(Objects.isNull(processInstanceId)){
            throw new CrmException("参数不能为空");
        }
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return  tfsCcFormClient.cclist(formListQuery);
    }


    @PostMapping("/queryToccListPage")
    @Operation(summary = "分页查询抄送我的")
    @PreAuthorize
    public JsonObject<PageUtils<TfsCcFormVo>> queryToccListPage(@RequestBody FormQuery formQuery){
        formQuery.setPlatformCode(getCurrentAudience());
        formQuery.setPersonId(getCurrentPersonId());
        return  tfsCcFormClient.list(formQuery).convert(PageUtils::new);
    }



}
