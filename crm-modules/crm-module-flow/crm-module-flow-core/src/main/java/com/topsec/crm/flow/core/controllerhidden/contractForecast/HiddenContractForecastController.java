package com.topsec.crm.flow.core.controllerhidden.contractForecast;

import com.topsec.crm.flow.core.service.IContractForecastService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @version V1.0
 * @Description: 合同预测内部调用接口
 * @ClassName: com.topsec.crm.flow.core.controllerhidden.contractForecast.HiddenContractForecastController.java
 * @Copyright 天融信 - Powered By 企业软件研发中心
 * @author: leo
 * @date: 2025-07-26 11:47
 */
@RestController
@RequestMapping("/hidden/contractForecast")
@Validated
public class HiddenContractForecastController extends BaseController {

    @Resource
    private IContractForecastService contractForecastService;

    // 发起合同预测流程
    @GetMapping("/sendContractForecastFlow")
    public JsonObject<Boolean> sendContractForecastFlow(@RequestParam String month) {
        Boolean result = contractForecastService.sendContractForecastFlow(month);
        return new JsonObject<>(result);
    }
}
