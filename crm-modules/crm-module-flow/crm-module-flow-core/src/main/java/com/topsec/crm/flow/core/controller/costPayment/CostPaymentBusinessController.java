package com.topsec.crm.flow.core.controller.costPayment;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.api.dto.costContarct.CostContractAttachmentInfoDTO;
import com.topsec.crm.flow.api.dto.costPayment.VO.CostContractInfoVo;
import com.topsec.crm.flow.api.dto.costPayment.VO.CostPaymentInfoVO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CostPayment;
import com.topsec.crm.flow.core.service.CostContractAttachmentService;
import com.topsec.crm.flow.core.service.CostContractService;
import com.topsec.crm.flow.core.service.CostPaymentService;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;

import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@RestController
@RequestMapping("/business/costPayment")
@Tag(name = "【费用支付-业务接口】", description = "costPayment")
@RequiredArgsConstructor
@Validated
public class CostPaymentBusinessController extends BaseController {

    private final CostPaymentService costPaymentService;

    private final CostContractService costContractService;

    private final CostContractAttachmentService costContractAttachmentService;

    @PreAuthorize(hasPermission="crm_cost_payment")
    @PostMapping("/getContractConfirm")
    @Operation(summary = "根据人员选择费用合同")
    public JsonObject<TableDataInfo> getContractConfirm(@RequestBody CostContractInfoVo costContractInfoVo){
        CrmAssert.hasText(costContractInfoVo.getPersonId(),"创建人ID不能为空");
        startPage();
        return new JsonObject<>(costContractService.getContractConfirm(costContractInfoVo));
    }

    @PreAuthorize(hasPermission="crm_cost_payment",dataScope="crm_cost_payment")
    @PostMapping("/page")
    @Operation(summary = "费用支付列表查询")
    public JsonObject<TableDataInfo> getPaymentInfo(@RequestBody CostPaymentInfoVO costPaymentInfoVO){
        startPage();
        DataScopeParam dataScopeParam = PreAuthorizeAspect.getDataScopeParam();
        Set<String> personIdList = dataScopeParam!=null? dataScopeParam.getPersonIdList(): Collections.EMPTY_SET;
        return new JsonObject<>(costPaymentService.getPaymentInfo(costPaymentInfoVO,personIdList));
    }

    @PreAuthorize(hasPermission="crm_cost_payment")
    @GetMapping("/getPaymentInfoById")
    @Operation(summary = "费用支付详情查询")
    public JsonObject<CostPaymentInfoVO> getPaymentInfoById(String paymentId){
        CrmAssert.hasText(paymentId,"主表ID不能为空");
        return new JsonObject<>(costPaymentService.getPaymentInfoById(paymentId));
    }

    @PreAuthorize(hasPermission="crm_cost_payment")
    @GetMapping("/getPaymentInfoByProcessInstanceId")
    @Operation(summary = "费用支付详情查询(流程ID)")
    public JsonObject<CostPaymentInfoVO> getPaymentInfoByProcessInstanceId(String processInstanceId){
        CrmAssert.hasText(processInstanceId,"流程ID不能为空");
        return new JsonObject<>(costPaymentService.getPaymentInfoByProcessInstanceId(processInstanceId));
    }

    @PreAuthorize(hasPermission="crm_cost_payment")
    @GetMapping("/getAttachmentInfoByPaymentId")
    @Operation(summary = "费用支付合同原件附件查询")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getAttachmentInfoByPaymentId(@RequestParam String paymentId){
        CrmAssert.hasText(paymentId,"ID不能为空");
        return new JsonObject<>(costContractAttachmentService.getByPaymentId(paymentId));
    }

    @PreAuthorize(hasPermission="crm_cost_payment")
    @GetMapping("/getPaymentInfoByCostContractId")
    @Operation(summary = "根据费用合同ID查询最后一次支付信息")
    public JsonObject<CostPaymentInfoVO> getPaymentInfoByCostContractId(@RequestParam String costContractId){
        return new JsonObject<>(costPaymentService.getPaymentInfoByCostContractId(costContractId));
    }

    @PreAuthorize(hasPermission="crm_cost_payment")
    @GetMapping("/getAttachmentByProcessInstanceId")
    @Operation(summary = "查询费用支付附件信息")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getAttachmentByProcessInstanceId(@RequestParam String processInstanceId,@RequestParam(required = false) String isFinalQuery){
        Optional<CostPayment> optional = Optional.ofNullable(costPaymentService.getOne(new QueryWrapper<CostPayment>().lambda()
                .eq(CostPayment::getDelFlag,0).eq(CostPayment::getProcessInstanceId,processInstanceId)));
        if (optional.isPresent()){
            CostPayment costPayment = optional.get();
//            CostContract costContract = costContractService.getById(costContractOriginal.getCostContractId());
            return new JsonObject<>(costContractAttachmentService.getByCostContractId(costPayment.getId(),isFinalQuery));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

//    @PreAuthorize(hasPermission = "crm_cost_payment_add")
//    @PostMapping("/saveAttachment")
//    @Operation(summary = "新增费用备案附件")
//    public JsonObject<Boolean> saveAttachment(@RequestBody CostContractAttachmentInfoDTO attachmentInfoDTO){
//        CrmAssert.hasText(attachmentInfoDTO.getCostId(),"主表ID不能为空");
//        attachmentInfoDTO.setCostType("费用备案");
//        return new JsonObject<>(costContractAttachmentService.saveAttachment(attachmentInfoDTO));
//    }
//
//    @PreAuthorize(hasPermission = "crm_cost_payment_add")
//    @GetMapping("/deleteAttachment")
//    @Operation(summary = "删除费用备案附件信息")
//    public JsonObject<Boolean> deleteAttachment(@RequestParam String id){
//        return new JsonObject<>(costContractAttachmentService.deleteAttachment(id));
//    }
//
//    @PreAuthorize(hasPermission="crm_cost_payment")
//    @GetMapping("/getByCostContractId")
//    @Operation(summary = "查询费用备案附件信息")
//    public JsonObject<List<CostContractAttachmentInfoDTO>> getByCostContractId(@RequestParam String costId,@RequestParam(required = false) String isFinalQuery){
//        return new JsonObject<>(costContractAttachmentService.getByCostContractId(costId,isFinalQuery));
//    }
}
