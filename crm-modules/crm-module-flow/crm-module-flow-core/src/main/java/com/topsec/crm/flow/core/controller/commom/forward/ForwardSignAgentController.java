package com.topsec.crm.flow.core.controller.commom.forward;

import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.operation.api.RemoteContractReviewConfigService;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractSignCompanyVO;
import com.topsec.crm.project.api.client.RemoteProjectSignAgentClient;
import com.topsec.crm.project.api.entity.AgentTreeSelect;
import com.topsec.crm.project.api.entity.CrmProjectSigningAgentVo;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.vo.TfsFormContentVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/forward/sign/agent")
@Tag(name = "转发渠道项目Controller", description = "/forward")
@RequiredArgsConstructor
@Validated
public class ForwardSignAgentController extends BaseController {

    private final RemoteProjectSignAgentClient remoteProjectSignAgentClient;
    private final TfsFormContentClient tfsformContentClient;

    @PreAuthorize
    @PreFlowPermission
    @GetMapping("/tree/{projectId}")
    JsonObject<List<AgentTreeSelect>> tree(@PathVariable String projectId) {
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(projectId.equals(tfsFormContentVo.getProjectId())){
                return remoteProjectSignAgentClient.tree(projectId);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    /**
     * 项目详情中-查询签约渠道详情
     */
    @PreAuthorize
    @PreFlowPermission
    @PostMapping("/selectSigningAgentDetail")
    JsonObject<CrmAgentVo> selectSigningAgentDetail(@RequestBody CrmProjectSigningAgentVo crmProjectSigningAgentVo){
        String processInstanceId = request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
        JsonObject<TfsFormContentVo> byProcessInstanceId = tfsformContentClient.findByProcessInstanceId(processInstanceId);
        if(byProcessInstanceId.isSuccess() && null != byProcessInstanceId.getObjEntity()){
            TfsFormContentVo tfsFormContentVo = byProcessInstanceId.getObjEntity();
            if(crmProjectSigningAgentVo.getProjectId().equals(tfsFormContentVo.getProjectId())){
                return remoteProjectSignAgentClient.selectSigningAgentDetail(crmProjectSigningAgentVo);
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

}
