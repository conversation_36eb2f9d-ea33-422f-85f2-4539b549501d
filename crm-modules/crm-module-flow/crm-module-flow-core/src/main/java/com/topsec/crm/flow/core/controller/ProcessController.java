package com.topsec.crm.flow.core.controller;

import com.topsec.crm.flow.api.vo.ProcessCheckVo;
import com.topsec.crm.flow.api.vo.ProcessFileInfoVO;
import com.topsec.crm.flow.api.vo.ProcessFileInput;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.handler.right.ProjectRightHandler;
import com.topsec.crm.flow.core.handler.right.Step00RemoveEditProcessRightHandler;
import com.topsec.crm.flow.core.process.ProcessStateHandler;
import com.topsec.crm.flow.core.process.ProcessStateHandlerUtils;
import com.topsec.crm.flow.core.process.impl.TfsProcessService;
import com.topsec.crm.flow.core.service.FlowService;
import com.topsec.crm.flow.core.service.ProcessFileInfoService;
import com.topsec.crm.framework.common.bean.FileInput;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.utils.AuthorizeUtil;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.query.BusinessTypeQuery;
import com.topsec.query.CommonProcessQuery;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.constant.TbsConstants;
import com.topsec.tfs.api.client.TfsFormContentClient;
import com.topsec.tfs.api.client.TfsProcessRoleRelClient;
import com.topsec.vo.ProcessEnableVo;
import com.topsec.vo.TfsBusinessTypeVo;
import com.topsec.vo.TfsFormContentVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/process")
@Tag(name = "流程公用的")
@RequiredArgsConstructor
public class ProcessController {
    private final TfsFormContentClient tfsFormContentClient;
    private final FlowService flowService;
    private final TfsProcessService tfsProcessService;
    private final AuthorizeUtil authorizeUtil;
    private final ProcessFileInfoService processFileInfoService;
    private final TfsProcessRoleRelClient tfsProcessRoleRelClient;
    @Resource
    private HttpServletRequest request;

    @GetMapping("/queryFileByFileId")
    @Operation(summary = "根据文件id查询文件信息")
    @PreFlowPermission
    public JsonObject<ProcessFileInfoVO> queryFileByFileId(@RequestParam String fileId) {
        String processInstanceId = request.getHeader("Process-Instance-Id");
        return new JsonObject<>(processFileInfoService.queryByFileId(processInstanceId, fileId));
    }

    @GetMapping("/queryFileByFileIds")
    @Operation(summary = "根据文件id查询文件信息")
    @PreFlowPermission
    public JsonObject<List<ProcessFileInfoVO>> queryFileByFileIds(@RequestParam List<String> fileIds) {
        String processInstanceId = request.getHeader("Process-Instance-Id");
        return new JsonObject<>(processFileInfoService.queryByProcessInstanceId(processInstanceId, fileIds,null ));
    }

    @GetMapping("/queryFileByProcessInstanceId")
    @Operation(summary = "根据流程Id查询所有流程附件")
    @PreFlowPermission
    public JsonObject<List<ProcessFileInfoVO>> queryFileByProcessInstanceId() {
        String processInstanceId = request.getHeader("Process-Instance-Id");
        return new JsonObject<>(processFileInfoService.queryByProcessInstanceId(processInstanceId,null,null));
    }

    @GetMapping("/queryFileByTypeProcessInstanceId")
    @Operation(summary = "根据流程Id查询所有流程对应类型附件")
    @PreFlowPermission
    public JsonObject<List<ProcessFileInfoVO>> queryFileByTypeProcessInstanceId(@RequestParam String type) {
        String processInstanceId = request.getHeader("Process-Instance-Id");
        return new JsonObject<>(processFileInfoService.queryByProcessInstanceId(processInstanceId,null,type));
    }

    @DeleteMapping("/deleteFileByIds")
    @Operation(summary = "删除流程id对应fileId文件")
    @PreFlowPermission
    public JsonObject<Boolean> deleteFileByIds(@RequestParam Set<String> fileIds) {

        String processInstanceId = request.getHeader("Process-Instance-Id");
        return new JsonObject<>(processFileInfoService.delete(processInstanceId, fileIds ));
    }

    @PostMapping(value = "/uploadProcessFileByFileId")
    @Operation(summary = "上传文件-针对进行中的流程")
    @PreFlowPermission
    public JsonObject<Boolean> uploadProcessFileByFileId(@RequestBody List<FileInput> fileInput) {
        String processInstanceId = request.getHeader("Process-Instance-Id");
        ProcessFileInput processFileInput=new ProcessFileInput(processInstanceId,fileInput);
        return new JsonObject<>(processFileInfoService.uploadProcessFileByFileId(processFileInput));
    }



    @PostMapping(value = "/uploadProcessFileDirectly", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传文件-针对进行中的流程")
    @PreFlowPermission
    public JsonObject<Boolean> uploadProcessFileDirectly(   @RequestParam MultipartFile file,
                                                    @RequestParam(required = false) String type,
                                                    @RequestParam(required = false) String extendInfo,
                                                    @RequestParam(required = false) String remark) {
        String processInstanceId = request.getHeader("Process-Instance-Id");
        return new JsonObject<>(processFileInfoService.uploadProcessFileDirectly(file,processInstanceId,type,extendInfo,remark));
    }



    @GetMapping("/findByProcessInstanceId")
    @Operation(summary = "根据流程实例id查询流程信息")
    @PreFlowPermission
    public JsonObject<TfsFormContentVo> findByProcessInstanceId(@RequestParam String processInstanceId) {
        return tfsProcessService.updateCompanyName(tfsFormContentClient.findByProcessInstanceId(processInstanceId));
    }

    @PostMapping({"/queryProcessPage"})
    @Operation(summary = "查询项目流程信息")
    @PreAuthorize(rightHandler = ProjectRightHandler.class,rightHandlerExtractArgsEL = "#commonProcessQuery.projectId")
    public JsonObject<PageUtils<TfsFormContentVo>> queryProcessPage(@RequestBody CommonProcessQuery commonProcessQuery){
        if (StringUtils.isBlank(commonProcessQuery.getProjectId())) {
            throw new CrmException("项目id不能为空");
        }
        commonProcessQuery.setPlatformCode(UserInfoHolder.getCurrentAudience());

        PageUtils<TfsFormContentVo> pageUtils = Optional.ofNullable(tfsFormContentClient.queryProcessPage(commonProcessQuery))
                .map(JsonObject::getObjEntity).map(PageUtils::new)
                .orElse(new PageUtils<>());


        return tfsProcessService.updateBatchCompanyName(new JsonObject<>(pageUtils));
    }
    /**
     * 查询人员的上级营销中心领导
     * @param empId 人员id
     * @return 查询人员的上级营销中心领导
     */
    @PostMapping("/queryCenterLeaderOfEmployee")
    @Operation(summary = "查询人员的上级营销中心领导")
    public JsonObject<List<FlowPerson>> queryCenterLeaderOfEmployee(@RequestParam String empId) {
        return new JsonObject<>(flowService.queryCenterLeaderOfEmployee(empId));
    }



    @PostMapping("/checkCrmProcessPermission")
    @Operation(summary = "流程权限校验接口")
    @PreAuthorize
    public JsonObject<Void> checkCrmProcessPermission(@RequestBody ProcessCheckVo processCheckVo) {
        Set<String> currentRoles = UserInfoHolder.getCurrentRoles();
        List<String> deptIds = authorizeUtil.queryDeptIdList(UserInfoHolder.getCurrentPersonId(), TbsConstants.Datapurview.DEPTDATA);
        processCheckVo.setAccountId(UserInfoHolder.getCurrentAccountId());
        processCheckVo.setRoles(currentRoles);
        processCheckVo.setDeptIds(deptIds);
        return tfsProcessService.checkBeforeFillingJsonObj(processCheckVo);
    }


    @PostMapping("/queryProcessList")
    @Operation(summary = "流程列表接口")
    @PreAuthorize(hasPermission = "crm_role")
    public JsonObject<PageUtils<TfsBusinessTypeVo>> queryProcessList(@RequestBody BusinessTypeQuery businessTypeQuery){
        return tfsProcessRoleRelClient.queryProcessList(businessTypeQuery).convert(PageUtils::new);
    }

    @PostMapping("/batchProcessEnable")
    @Operation(summary = "流程权限批量开启")
    @PreAuthorize(hasPermission = "crm_role")
    public JsonObject<Boolean> batchProcessEnable(@RequestBody ProcessEnableVo processEnableVo){
        processEnableVo.setPersonId(UserInfoHolder.getCurrentPersonId());
        return tfsProcessRoleRelClient.batchProcessEnable(processEnableVo);

    }

    @PostMapping("/updateProcessEnable")
    @Operation(summary = "单个流程权限关闭和开启")
    @PreAuthorize(hasPermission = "crm_role")
    public JsonObject<Boolean> updateProcessEnable(@RequestBody ProcessEnableVo processEnableVo){
        processEnableVo.setPersonId(UserInfoHolder.getCurrentPersonId());
        return tfsProcessRoleRelClient.updateProcessEnable(processEnableVo);

    }
    // @PostMapping("/00removeProcess")
    // @Operation(summary = "00步移除流程")
    // @PreAuthorize(rightHandler = Step00RemoveEditProcessRightHandler.class)
    // public JsonObject<Boolean> removeProcess00(){
    //     String processInstanceId = HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID);
    //     ProcessStateHandler processStateHandler = ProcessStateHandlerUtils.getHandlerByProcessInstanceId(processInstanceId);
    //
    //     return new JsonObject<>(processStateHandler.remove(processInstanceId));
    // }

    @DeleteMapping("/00removeProcess")
    @Operation(summary = "00步移除流程")
    @PreAuthorize(rightHandler = Step00RemoveEditProcessRightHandler.class,rightHandlerExtractArgsEL = "#processInstanceId")
    public JsonObject<Boolean> removeProcess00(@RequestParam String processInstanceId){
        ProcessStateHandler processStateHandler = ProcessStateHandlerUtils.getHandlerByProcessInstanceId(processInstanceId);
        return new JsonObject<>(processStateHandler.remove(processInstanceId));
    }

}
