package com.topsec.crm.flow.core.controllerhidden.agentExamineSd;

import com.topsec.crm.agent.api.entity.CrmAgentExamineSdVO;
import com.topsec.crm.flow.api.dto.agentExamineSd.AgentExamineSdFlowLaunchDTO;
import com.topsec.crm.flow.core.process.impl.AgentExamineSdProcessService;
import com.topsec.crm.flow.core.service.AgentExamineSdService;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/hidden/agentExamineSd")
@Tag(name = "省级总经销商MBO考核")
public class HiddenAgentExamineSdController extends BaseController {

    @Autowired
    private AgentExamineSdService agentExamineSdService;
    @Autowired
    private AgentExamineSdProcessService agentExamineSdProcessService;

    @PostMapping("/launch")
    @Operation(summary = "发起省级总经销商MBO考核")
    JsonObject<Boolean> launch(@RequestBody List<CrmAgentExamineSdVO> crmAgentExamineSdVOS){
        for (CrmAgentExamineSdVO crmAgentExamineSdVO : crmAgentExamineSdVOS) {
            AgentExamineSdFlowLaunchDTO agentExamineSdFlowLaunchDTO = HyperBeanUtils.copyProperties(crmAgentExamineSdVO, AgentExamineSdFlowLaunchDTO::new);
            agentExamineSdFlowLaunchDTO.setHostId(agentExamineSdFlowLaunchDTO.getId());
            agentExamineSdFlowLaunchDTO.setId(null);
            agentExamineSdFlowLaunchDTO.setCreateTime(null);
            agentExamineSdProcessService.launch(agentExamineSdFlowLaunchDTO);
        }
        return new JsonObject<>(true);
    }
}
