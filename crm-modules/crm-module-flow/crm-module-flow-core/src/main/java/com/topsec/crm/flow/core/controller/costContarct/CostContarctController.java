package com.topsec.crm.flow.core.controller.costContarct;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.topsec.crm.flow.api.dto.costContarct.*;
import com.topsec.crm.flow.api.dto.costContarct.VO.ArriveInfoVO;
import com.topsec.crm.flow.api.dto.costContarct.VO.CostContractVo;
import com.topsec.crm.flow.api.dto.costFiling.CostFilingDetailInfo;
import com.topsec.crm.flow.api.dto.costFiling.CostFilingInfo;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.CostContract;
import com.topsec.crm.flow.core.entity.CostContractAttachment;
import com.topsec.crm.flow.core.entity.CostContractOriginal;
import com.topsec.crm.flow.core.entity.CostContractRe;
import com.topsec.crm.flow.core.process.impl.CostContractProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.DataScopeParam;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.CrmAssert;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.crm.framework.security.aspect.PreAuthorizeAspect;
import com.topsec.crm.operation.api.entity.ContractReviewConfig.ContractSignCompanyVO;
import com.topsec.crm.project.api.dto.ProjectDirectlyPersonIdPageQuery;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;

import com.topsec.spring.http.util.HttpUtil;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.vo.TosDepartmentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 费用合同
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/costContract")
@Tag(name = "【费用合同-流程接口】", description = "costContract")
@RequiredArgsConstructor
@Validated
public class CostContarctController extends BaseController {

    private final CostContractService costContractService;

    private final CostContractReService costContractReService;

    private final CostContractProcessService costContractProcessService;

    private final ICostFilingDetailService iCostFilingDetailService;

    private final ICostFilingService iCostFilingService;

    private final CostContractPaymentClauseService costContractPaymentClauseService;

    private final CostContractAttachmentService costContractAttachmentService;

    @PreFlowPermission(hasAnyNodes = {"costContract_02","costContract_02C"})
    @PostMapping("/updateCostContractReInfo")
    @Operation(summary = "修改税率后调整费用合同明细可申请金额")
    public JsonObject<List<CostContractReInfoDTO>> updateCostContractReInfo(@RequestBody List<CostContractReInfoDTO> reInfoDTOS){
        for (CostContractReInfoDTO reInfoDTO : reInfoDTOS) {
            Optional<CostContractRe> optional = Optional.ofNullable(costContractReService.getById(reInfoDTO.getCostContractReId()));
            if (optional.isPresent()){
                CostContractRe re = optional.get();
                CostContract costContract = costContractService.getById(re.getCostContractId());
                PreFlowPermissionAspect.checkProcessInstanceId(costContract.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            }else{
                throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
            }

        }
        return new JsonObject<>(costContractReService.updateCostContractReInfo(reInfoDTOS));
    }

    @PreAuthorize(hasAnyPermission = {"crm_cost_contract_add","crm_flow_cost_contract"})
    @PostMapping("/launch")
    @Operation(summary = "发起费用合同流程")
    public JsonObject<Boolean> launch(@RequestBody CostContractFlowLaunchDTO costContractFlowLaunchDTO){
        return new JsonObject<>(costContractProcessService.launch(costContractFlowLaunchDTO));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @GetMapping("/getTosDepartmentVOByPersonId")
    @Operation(summary = "查询人员所有部门")
    public JsonObject<List<TosDepartmentVO>> getTosDepartmentVOByPersonId(@RequestParam String personId){
        return new JsonObject<>(costContractService.getTosDepartmentVOByPersonId(personId));
    }

    @PreAuthorize(hasPermission="crm_cost_contract")
    @PostMapping("/getProjectDirectlyByPersonId")
    @Operation(summary = "查询人员所有项目")
    public JsonObject<PageUtils<CrmProjectDirectlyVo>> getProjectDirectlyByPersonId(@RequestBody ProjectDirectlyPersonIdPageQuery query){
        return new JsonObject<>(costContractService.getProjectDirectlyByPersonId(query));
    }

    @PreFlowPermission
    @GetMapping("/selectCostContractById")
    @Operation(summary = "根据流程ID查询费用合同详情")
    public JsonObject<CostContractInfoDTO> selectCostContractById(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(costContractService.selectCostContractByProcessInstanceId(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/selectCostContractReByCostContarctId")
    @Operation(summary = "根据主表ID分页查询记录")
    public JsonObject<List<CostContractReInfoDTO>> selectCostContractReByCostContarctId(@RequestParam String costContarctId){
        Optional<CostContract> optional = Optional.ofNullable(costContractService.getById(costContarctId));
        if (optional.isPresent()){
            CostContract costContract = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(costContract.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractReService.selectCostContractReByCostContarctId(costContarctId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }

    }

    @PreFlowPermission
    @GetMapping("/selectPaymentClauseByCostContractId")
    @Operation(summary = "根据主表ID分页查询付款条款")
    public JsonObject<List<CostContractPaymentClauseInfoDTO>> selectPaymentClauseByCostContractId(@RequestParam String costContarctId){
        Optional<CostContract> optional = Optional.ofNullable(costContractService.getById(costContarctId));
        if (optional.isPresent()){
            CostContract costContract = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(costContract.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractPaymentClauseService.selectPaymentClauseByCostContractId(costContarctId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission(hasAnyNodes = {"costContract_02C","costContract_02"})
    @PostMapping("/generateElectronContractInfo")
    @Operation(summary = "03步生成电子合同")
    public JsonObject<String> generateElectronContractInfo(@RequestBody ElectronContractInfoDTO electronContractInfoDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(electronContractInfoDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(costContractService.generateElectronContractInfo(electronContractInfoDTO,false));
    }

    @PreFlowPermission(hasAnyNodes = {"costContract_02C","costContract_02"})
    @PostMapping("/generateElectronContractInfoPreView")
    @Operation(summary = "03步生成电子合同预览")
    public JsonObject<String> generateElectronContractInfoPreView(@RequestBody ElectronContractInfoDTO electronContractInfoDTO) {
        PreFlowPermissionAspect.checkProcessInstanceId(electronContractInfoDTO.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(costContractService.generateElectronContractInfo(electronContractInfoDTO,true));
    }

    @PreFlowPermission
    @GetMapping("/getArriveInfoVOInfo")
    @Operation(summary = "根据ID查询送达信息")
    public JsonObject<ArriveInfoVO> getArriveInfoVOInfo(@RequestParam String costContarctId){
        Optional<CostContract> optional = Optional.ofNullable(costContractService.getById(costContarctId));
        if (optional.isPresent()){
            CostContract costContract = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(costContract.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractAttachmentService.getArriveInfoVOInfo(costContarctId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission(hasAnyNodes = {"costContract_02C","costContract_02"})
    @PostMapping("/updateCostContractInfo")
    @Operation(summary = "02、03步修改费用合同信息")
    public JsonObject<Boolean> updateCostContractInfo(@RequestBody CostContractUpdateDTO costContractUpdateDTO){
        PreFlowPermissionAspect.checkProcessInstanceId(costContractUpdateDTO.getCostContractInfoDTO().getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(costContractService.updateCostContractInfo(costContractUpdateDTO));
    }

    @PreFlowPermission
    @GetMapping("/getCostContractMoneyByProcessInstanceId")
    @Operation(summary = "费用合同校验金额是否超过项目中外包服务费(费用备案自动生成)")
    public JsonObject<Boolean> getCostContractMoneyByProcessInstanceId(@RequestParam String processInstanceId){
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<>(costContractService.getCostContractMoneyByProcessInstanceId(processInstanceId));
    }

    @PreFlowPermission
    @GetMapping("/getByCostContractId")
    @Operation(summary = "查询费用合同附件信息")
    public JsonObject<List<CostContractAttachmentInfoDTO>> getByCostContractId(@RequestParam String costId,@RequestParam(required = false) String isFinalQuery){
        Optional<CostContract> optional = Optional.ofNullable(costContractService.getById(costId));
        if (optional.isPresent()){
            CostContract costContract = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(costContract.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractAttachmentService.getByCostContractId(costId,isFinalQuery));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission
    @PostMapping("/costContractSignCompanyPage")
    @Operation(summary = "费用合同选择签订公司")
    public JsonObject<List<ContractSignCompanyVO>> costContractSignCompanyPage(@RequestBody ContractSignCompanyVO contractSignCompanyVO){
        return new JsonObject<>(costContractService.costContractSignCompanyPage(contractSignCompanyVO));
    }

    @PreFlowPermission
    @PostMapping("/saveContractAttachment")
    @Operation(summary = "新增费用合同附件")
    public JsonObject<Boolean> saveAttachment(@RequestBody CostContractAttachmentInfoDTO attachmentInfoDTO){
        CrmAssert.hasText(attachmentInfoDTO.getCostId(),"主表ID不能为空");
        Optional<CostContract> optional = Optional.ofNullable(costContractService.getById(attachmentInfoDTO.getCostId()));
        if (optional.isPresent()){
            CostContract costContract = optional.get();
            PreFlowPermissionAspect.checkProcessInstanceId(costContract.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            attachmentInfoDTO.setCostType("费用合同");
            return new JsonObject<>(costContractAttachmentService.saveAttachment(attachmentInfoDTO));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission
    @GetMapping("/deleteContractAttachment")
    @Operation(summary = "删除费用合同附件信息")
    public JsonObject<Boolean> deleteAttachment(@RequestParam String id){
        Optional<CostContractAttachment> optional = Optional.ofNullable(costContractAttachmentService.getById(id));
        if (optional.isPresent()){
            CostContract costContract = costContractService.getById(optional.get().getCostId());
            PreFlowPermissionAspect.checkProcessInstanceId(costContract.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractAttachmentService.deleteAttachment(id));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

    @PreFlowPermission
    @GetMapping("/updateAttachment")
    @Operation(summary = "修改是否终稿")
    public JsonObject<Boolean> updateAttachment(@RequestParam String docId){
        Optional<CostContractAttachment> optional = Optional.ofNullable(costContractAttachmentService
                .getOne(new QueryWrapper<CostContractAttachment>().lambda().eq(CostContractAttachment::getDelFlag,0)
                        .eq(CostContractAttachment::getDocId,docId)));
        if (optional.isPresent()){
            CostContract costContract = costContractService.getById(optional.get().getCostId());
            PreFlowPermissionAspect.checkProcessInstanceId(costContract.getProcessInstanceId(), HttpUtil.getHeaderValue(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
            return new JsonObject<>(costContractAttachmentService.updateAttachment(docId));
        }else{
            throw new CrmException(ResultEnum.NULL_OBJ_ENTITY);
        }
    }

}
