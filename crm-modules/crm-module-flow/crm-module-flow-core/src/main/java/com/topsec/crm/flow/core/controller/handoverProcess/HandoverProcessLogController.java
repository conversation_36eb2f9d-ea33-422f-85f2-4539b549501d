package com.topsec.crm.flow.core.controller.handoverProcess;

import com.topsec.crm.flow.core.service.HandoverProcessLogService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 * 交接流程日志记录表
 *
 * <AUTHOR>
 * @email 
 * @date 2025-07-22 14:30:20
 */
@RestController
@RequestMapping("/handoverProcessLog")
@Tag(name = "交接流程日志Controller", description = "/handoverProcessLog")
@RequiredArgsConstructor
@Validated
public class HandoverProcessLogController {

    private final HandoverProcessLogService handoverProcessLogService;



}
