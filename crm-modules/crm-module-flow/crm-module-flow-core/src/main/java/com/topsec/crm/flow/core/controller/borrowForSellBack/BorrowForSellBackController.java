package com.topsec.crm.flow.core.controller.borrowForSellBack;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellDeviceDTO;
import com.topsec.crm.flow.api.dto.borrowForSell.BorrowForSellFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.borrowForSellBack.BorrowForSellBackFlowLaunchDTO;
import com.topsec.crm.flow.core.annotation.PreFlowPermission;
import com.topsec.crm.flow.core.aspect.PreFlowPermissionAspect;
import com.topsec.crm.flow.core.entity.BorrowForSell;
import com.topsec.crm.flow.core.entity.BorrowForSellBack;
import com.topsec.crm.flow.core.process.impl.BorrowForSellBackProcessService;
import com.topsec.crm.flow.core.service.IBorrowForSellBackService;
import com.topsec.crm.flow.core.service.IBorrowForSellProductSnService;
import com.topsec.crm.flow.core.service.IBorrowForSellService;
import com.topsec.crm.framework.common.constant.CrmConstants;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.web.controller.BaseController;
import com.topsec.crm.framework.security.annotation.PreAuthorize;
import com.topsec.tbscommon.JsonObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 借转销归还/丢失赔偿信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@RestController
@RequestMapping("/borrowForSellBack")
@Tag(name = "借转销归还/丢失赔偿流程", description = "/borrowForSellBack")
public class BorrowForSellBackController extends BaseController {

    @Autowired
    private BorrowForSellBackProcessService borrowForSellBackProcessService;

    @Autowired
    private IBorrowForSellBackService borrowForSellBackService;

    @Autowired
    private IBorrowForSellService borrowForSellService;

    @Autowired
    private IBorrowForSellProductSnService borrowForSellProductSnService;

    @PreAuthorize(hasPermission = "crm_flow_borrow_for_sell_return_compensate_add")
    @PostMapping("/launch")
    @Operation(summary = "发起借转销归还/丢失赔偿流程")
    public JsonObject<Boolean> launch(@Valid @RequestBody BorrowForSellBackFlowLaunchDTO launchDTO) {
        //launchDTO.setMatter("借转销归还/丢失赔偿申请");
        BorrowForSell one = borrowForSellService.getOne(new LambdaQueryWrapper<BorrowForSell>().eq(BorrowForSell::getProcessInstanceId, launchDTO.getBorrowProcessInstanceId()));
        if(null != one){
            if(getCurrentPersonId().equals(one.getSalesmanPersonId()) && one.getProcessInstanceId().equals(launchDTO.getBorrowProcessInstanceId())){
                // 选择【丢失赔偿】，仅能选择从公司借用的设备，不能选择从国代借的设备。
                Integer shipmentType = null;
                if(launchDTO.getBackType() == 1){
                    shipmentType = 0;
                }
                // 校验借转销归还/丢失赔偿申请的设备信息
                List<BorrowForSellDeviceDTO> borrowForSellDeviceDTOS = borrowForSellProductSnService.queryBorrowForSellDeviceList(one.getId(), shipmentType);
                launchDTO.getDeviceList().forEach(device -> {
                    List<String> list = borrowForSellDeviceDTOS.stream().map(borrowForSellDeviceDTO -> borrowForSellDeviceDTO.getPsn()).toList();
                    if(!list.contains(device.getPsn())){
                        throw new CrmException("设备信息有误");
                    }
                });
                return new JsonObject<>(borrowForSellBackProcessService.launch(launchDTO));
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @GetMapping("/detailByBorrowProcessInstanceId")
    @Operation(summary = "查看借转销流程详情(借转销归还/丢失赔偿详情使用)")
    public JsonObject<BorrowForSellFlowLaunchDTO> detailByBorrowProcessInstanceId(@RequestParam String borrowProcessInstanceId) {
        BorrowForSellBack one = borrowForSellBackService.getOne(new LambdaQueryWrapper<BorrowForSellBack>().eq(BorrowForSellBack::getProcessInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID)));
        if(null != one){
            if(one.getBorrowProcessInstanceId().equals(borrowProcessInstanceId)){
                return new JsonObject<BorrowForSellFlowLaunchDTO>(borrowForSellService.borrowForSellDetail(borrowProcessInstanceId));
            }else{
                throw new CrmException(ResultEnum.AUTH_ERROR_500006);
            }
        }else{
            throw new CrmException(ResultEnum.AUTH_ERROR_500006);
        }
    }

    @PreFlowPermission
    @GetMapping("/detail/{processInstanceId}")
    @Operation(summary = "查看借转销归还/丢失赔偿流程详情")
    public JsonObject<BorrowForSellBackFlowLaunchDTO> borrowForSellBackDetail(@PathVariable String processInstanceId) {
        PreFlowPermissionAspect.checkProcessInstanceId(processInstanceId, request.getHeader(CrmConstants.HeaderName.PROCESS_INSTANCE_ID));
        return new JsonObject<BorrowForSellBackFlowLaunchDTO>(borrowForSellBackService.borrowForSellBackDetail(processInstanceId));
    }

}

