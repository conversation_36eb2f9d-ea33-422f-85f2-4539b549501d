package com.topsec.crm.flow.core.service.impl;

import com.topsec.crm.flow.core.util.InnerProductOwnQuery;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
class PriceReviewProductOwnServiceImplTest {
    @Resource
    private PriceReviewProductOwnServiceImpl priceReviewProductOwnService;
    @Test
    void queryProjectOwnByProjectId() {
        priceReviewProductOwnService.queryProjectOwnByProjectId(new InnerProductOwnQuery("e2c157f6-974b-47bd-81cb-25bd4f25480c"));
    }
}
