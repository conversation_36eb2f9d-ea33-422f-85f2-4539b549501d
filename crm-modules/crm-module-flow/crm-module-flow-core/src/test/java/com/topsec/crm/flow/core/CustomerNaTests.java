package com.topsec.crm.flow.core;

import com.topsec.crm.customer.api.RemoteCustomerNaService;
import com.topsec.crm.customer.api.entity.CrmCustomerNaVo;
import com.topsec.crm.flow.api.RemoteContractUnconfirmedDetailService;
import com.topsec.crm.flow.core.entity.CustomerNa;
import com.topsec.crm.flow.core.process.impl.*;
import com.topsec.crm.flow.core.service.ICustomerNaService;
import com.topsec.crm.framework.common.enums.ResultEnum;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.vo.FlowStateInfoVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest
@ActiveProfiles({"dev","feignDev","redisDev","configDev"})
public class CustomerNaTests {
    @Autowired
    private ICustomerNaService customerNaService;
    @Autowired
    private RemoteCustomerNaService remoteCustomerNaService;
    @Autowired
    private CustomerNaApproveProcessService customerNaApproveProcessService;
    @Autowired
    private ContractBadDebtProcessService contractBadDebtProcessService;
    @Autowired
    private ContractSignVerifyProcessService contractSignVerifyProcessService;
    @Autowired
    private CustomerAuthProcessService customerAuthProcessService;
    @Autowired
    private CustomerAnotherNameProcessService customerAnotherNameProcessService;
    @Autowired
    private RemoteContractUnconfirmedDetailService remoteContractUnconfirmedDetailService;

    @Test
    void queryCenterLeaderApproverList(){
        FlowStateInfoVo flowInfo = new FlowStateInfoVo();
        flowInfo.setProcessInstanceId("a9d9f865-345a-11ef-a0b2-0242ac12000b");

        //审批通过，将数据同步到crm_customer_na,并删除快照表customer_na
        List<CustomerNa> cns = customerNaService.query()
                .eq("process_instance_id", flowInfo.getProcessInstanceId())
                .list();

        List<CrmCustomerNaVo> ccnvs = new ArrayList<CrmCustomerNaVo>();
        cns.stream().forEach(cn -> ccnvs.add(HyperBeanUtils.copyProperties(cn, CrmCustomerNaVo::new)));

        //批量插入crm_customer_na记录
        JsonObject<Boolean> booleanJsonObject = remoteCustomerNaService.batchInsertCustomerNa(ccnvs);
        if(booleanJsonObject.isSuccess()){
            //删除customer_na快照
            customerNaService.removeByIds(cns.stream().map(CustomerNa::getId).collect(Collectors.toList()));
        }
    }

    @Test
    void handleTest(){
        FlowStateInfoVo processInfo = new FlowStateInfoVo();
        processInfo.setProcessInstanceId("9e2ee884-ad5e-11ef-91eb-0242ac12000d");
        customerNaApproveProcessService.handlePass(processInfo);
    }

    @Test
    void syncUnconfirmedForUrge() throws Exception {
        remoteContractUnconfirmedDetailService.syncUnconfirmed();
    }
}
