package com.topsec.crm.flow.core.service.impl;

import com.google.common.collect.Sets;
import com.topsec.crm.flow.core.service.PriceReviewMainService;
import com.topsec.crm.project.api.entity.CrmProjectDirectlyVo;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class PriceReviewMainServiceImplTest {

    @Resource
    private PriceReviewMainService priceReviewMainService;
    @Test
    void baseInfo() {
        priceReviewMainService.baseInfo("2f6a8556-7661-11ef-97ec-0242ac12000d");
    }
    @Test
    void queryPriceState() {
        priceReviewMainService.queryPriceState("a6276478-a695-4d4e-940c-24c50f29dabb", Sets.newHashSet("131680b7-4c0f-469b-aac7-b752029fdc20"));

    }

    @Test
    void querySaleAgreementRelatedProjects() {
        List<CrmProjectDirectlyVo> crmProjectDirectlyVos = priceReviewMainService
                .querySaleAgreementRelatedProjects("e9ca9d01-5fda-4a07-8df3-b2463601db2b");
    }


}
