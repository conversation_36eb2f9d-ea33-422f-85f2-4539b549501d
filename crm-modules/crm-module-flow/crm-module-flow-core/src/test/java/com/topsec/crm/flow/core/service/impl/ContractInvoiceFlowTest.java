package com.topsec.crm.flow.core.service.impl;

import com.topsec.crm.contract.api.RemoteContractInvoiceService;
import com.topsec.crm.contract.api.entity.invoice.InvoiceProductDTO;
import com.topsec.crm.contract.api.entity.invoice.InvoiceProductQueryVO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.ContractMakeInvoiceDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.ContractMakeInvoiceFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.InvoiceDocDTO;
import com.topsec.crm.flow.api.dto.contractInvoice.makeInvoice.MakeInvoiceProductDTO;
import com.topsec.crm.flow.api.dto.returnexchange.ReturnExchangeDetailVO;
import com.topsec.crm.flow.api.dto.returnexchange.ReturnExchangeProductThirdVO;
import com.topsec.crm.flow.api.dto.returnexchange.ReturnExchangeProductVO;
import com.topsec.crm.flow.core.entity.ReturnExchangeProduct;
import com.topsec.crm.flow.core.entity.ReturnExchangeProductThird;
import com.topsec.crm.flow.core.mapstruct.InvoiceProductConvertor;
import com.topsec.crm.flow.core.process.impl.ContractMakeInvoiceProcessService;
import com.topsec.crm.flow.core.service.ContractInvoiceExtendService;
import com.topsec.crm.flow.core.service.ContractMakeInvoiceService;
import com.topsec.crm.flow.core.service.ReturnExchangeProductService;
import com.topsec.crm.flow.core.service.ReturnExchangeProductThirdService;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.tos.common.HyperBeanUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.util.List;

@SpringBootTest
@ActiveProfiles({"dev","feignDev","redisDev","configDev"})
public class ContractInvoiceFlowTest {

    @Resource
    private ContractMakeInvoiceService makeInvoiceService;

    @Resource
    private RemoteContractInvoiceService remoteContractInvoiceService;

    @Resource
    private ContractMakeInvoiceProcessService processService;

    @Resource
    private ContractInvoiceExtendService invoiceExtendService;

    @Resource
    private ReturnExchangeProductService returnExchangeOwnService;

    @Resource
    private ReturnExchangeProductThirdService returnExchangeThirdService;

    @Test
    void saveSnapshotTest(){
        ContractMakeInvoiceFlowLaunchDTO dto = createDTO();
        dto.setProcessNumber("1");
        dto.setProcessInstanceId("1");
        dto.setProcessState(ApprovalStatusEnum.SPZ.getCode());
        makeInvoiceService.saveSnapshot(dto);
    }

    @Test
    void launchTest(){
        ContractMakeInvoiceFlowLaunchDTO dto = createDTO();
        processService.launch(dto);
    }

    @Test
    void getProcessListTest(){
        makeInvoiceService.getListByContractNumber("**************");
    }

    @Test
    void handleMakeInvoice(){
        List<MakeInvoiceProductDTO> invoiceProductList = makeInvoiceService.getProcessInfo("5c648b2a-6613-11f0-966d-0242ac12000d").getInvoiceProductList();
        remoteContractInvoiceService.handleMakeInvoiceData(invoiceProductList);
    }

    @Test
    void handleAutoReturnInvoice(){
        ReturnExchangeDetailVO returnExchangeDetailVO = new ReturnExchangeDetailVO();
        List<ReturnExchangeProduct> newOwn = returnExchangeOwnService.listNewProductByProcessInstanceId("9b84a228-5e0a-11f0-ae95-0242ac12000d");
        List<ReturnExchangeProduct> oldOwn = returnExchangeOwnService.listOldProductByProcessInstanceId("9b84a228-5e0a-11f0-ae95-0242ac12000d");
        List<ReturnExchangeProductThird> newThird = returnExchangeThirdService.listNewProductByProcessInstanceId("9b84a228-5e0a-11f0-ae95-0242ac12000d");
        List<ReturnExchangeProductThird> oldThird = returnExchangeThirdService.listOldProductByProcessInstanceId("9b84a228-5e0a-11f0-ae95-0242ac12000d");
        returnExchangeDetailVO.setContractNumber("23520250600037");
        returnExchangeDetailVO.setReProduct(HyperBeanUtils.copyListProperties(oldOwn, ReturnExchangeProductVO::new));
        returnExchangeDetailVO.setNewProduct(HyperBeanUtils.copyListProperties(newOwn, ReturnExchangeProductVO::new));
        returnExchangeDetailVO.setReThirdProduct(HyperBeanUtils.copyListProperties(oldThird, ReturnExchangeProductThirdVO::new));
        returnExchangeDetailVO.setNewThirdProduct(HyperBeanUtils.copyListProperties(newThird, ReturnExchangeProductThirdVO::new));
        Boolean objEntity = remoteContractInvoiceService.handleReturnChangeInvoiceData(returnExchangeDetailVO).getObjEntity();
    }

    private ContractMakeInvoiceFlowLaunchDTO createDTO(){
        ContractMakeInvoiceFlowLaunchDTO flowLaunchDTO = new ContractMakeInvoiceFlowLaunchDTO();
        ContractMakeInvoiceDTO contractMakeInvoiceDTO = new ContractMakeInvoiceDTO();
        contractMakeInvoiceDTO.setContractNumber("**************");
        contractMakeInvoiceDTO.setSigningCompanyId("1");
        contractMakeInvoiceDTO.setSigningCompanyName("上海顶企");
        //contractMakeInvoiceDTO.setInvoiceClassification(1);
        contractMakeInvoiceDTO.setInvoiceType(1);
        contractMakeInvoiceDTO.setInvoiceMethod(1);
        contractMakeInvoiceDTO.setEnterpriseTaxpayerNumber("*********");
        contractMakeInvoiceDTO.setEnterpriseAddress("上海");
        contractMakeInvoiceDTO.setTelephone("*********");
        contractMakeInvoiceDTO.setBankName("上海银行");
        contractMakeInvoiceDTO.setBankAccount("*********");
        contractMakeInvoiceDTO.setEmail("<EMAIL>");
        contractMakeInvoiceDTO.setRemark("备注");
        InvoiceDocDTO invoiceDocDTO = new InvoiceDocDTO();
//        invoiceDocDTO.setDocType(1);
        invoiceDocDTO.setFsmDocId("1");
        contractMakeInvoiceDTO.setInvoiceDoc(List.of(invoiceDocDTO));
        InvoiceProductQueryVO invoiceProductQueryVO = new InvoiceProductQueryVO();
        invoiceProductQueryVO.setContractNumber("**************");
        List<InvoiceProductDTO> list = remoteContractInvoiceService.getFullListByContractNumber(invoiceProductQueryVO).getObjEntity();
        List<MakeInvoiceProductDTO> makeInvoiceProductList = InvoiceProductConvertor.INSTANCE.toMakeInvoiceProductList(list);
        makeInvoiceProductList.forEach(item->{
            item.setMakeInvoiceProductQuantity(BigDecimal.ONE);
            item.setMakeInvoiceProductAmount(BigDecimal.ONE);
            item.setFinalInvoiceAmount(BigDecimal.ZERO);
            item.setFinancialAdjustmentAmount(BigDecimal.ZERO);
            item.setFinancialAdjustmentRemainAmount(BigDecimal.ZERO);
        });
        contractMakeInvoiceDTO.setInvoiceProductList(makeInvoiceProductList);
        flowLaunchDTO.setContractMakeInvoiceDTO(contractMakeInvoiceDTO);
        return flowLaunchDTO;
    }

}
