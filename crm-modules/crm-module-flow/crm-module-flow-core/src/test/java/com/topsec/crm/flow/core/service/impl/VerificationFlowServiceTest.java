package com.topsec.crm.flow.core.service.impl;

import com.topsec.crm.contract.api.RemotePaymentVerificationService;
import com.topsec.crm.flow.api.dto.industryPaymentReceipt.IndustryPaymentReceiptVO;
import com.topsec.crm.flow.core.process.impl.VerificationConvertGoodsProcessService;
import com.topsec.crm.flow.core.process.impl.VerificationRefundProcessService;
import com.topsec.crm.flow.core.service.IndustryPaymentReceiptService;
import com.topsec.vo.FlowStateInfoVo;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

@SpringBootTest
@ActiveProfiles({"dev","feignDev","redisDev","configDev"})
public class VerificationFlowServiceTest {

    @Resource
    private VerificationRefundProcessService refundProcessService;

    @Resource
    private VerificationConvertGoodsProcessService convertGoodsProcessService;

    @Resource
    private RemotePaymentVerificationService remotePaymentVerificationService;

    @Resource
    private IndustryPaymentReceiptService industryPaymentReceiptService;

    @Test
    void refundProcessHandlePass(){
        FlowStateInfoVo flowStateInfoVo = new FlowStateInfoVo();
        flowStateInfoVo.setProcessInstanceId("84e02e05-ffe6-11ef-b032-0242ac12000a");
        refundProcessService.handlePass(flowStateInfoVo);
    }

    @Test
    void convertGoodsProcessHandlePassBack(){
        FlowStateInfoVo flowStateInfoVo = new FlowStateInfoVo();
        flowStateInfoVo.setProcessInstanceId("2b896cef-ffd8-11ef-b032-0242ac12000a");
        convertGoodsProcessService.handlePass(flowStateInfoVo);
    }

    @Test
    void getCollectionReceiptMapByContractNumberTest(){
        Map<String, List<String>> contractNumber = remotePaymentVerificationService.getCollectionReceiptMapByContractNumber(List.of("12320240900003", "12320240900003")).getObjEntity();
        System.out.println(contractNumber);
    }

    @Test
    void handleIndustryPaymentReceiptProcess(){
        IndustryPaymentReceiptVO industryPaymentReceiptVO = industryPaymentReceiptService.queryDetailByProcessInstanceId("07cce86e-0ad0-11f0-841a-0242ac12000d");
        remotePaymentVerificationService.handleIndustryProcessApprovalData(industryPaymentReceiptVO).getObjEntity();
    }
}
