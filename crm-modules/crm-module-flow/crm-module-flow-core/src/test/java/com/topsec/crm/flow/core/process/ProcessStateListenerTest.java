package com.topsec.crm.flow.core.process;

import com.topsec.crm.flow.core.FlowApplicationTests;
import com.topsec.enums.ApprovalStatusEnum;
import com.topsec.enums.ProcessDefinitionKeyEnum;
import com.topsec.enums.TfsConstants;
import com.topsec.vo.FlowStateInfoVo;
import org.apache.rocketmq.common.message.MessageConst;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.Test;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
class ProcessStateListenerTest extends FlowApplicationTests {
    @Resource
    public ProcessStateListener processStateListener;
    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Test
    void pass(){
        FlowStateInfoVo flowStateInfoVo=new FlowStateInfoVo();
        flowStateInfoVo.setProcessInstanceId("32b8fa95-4c0b-11f0-8d4a-0242ac12000d");
        flowStateInfoVo.setProcessState(ApprovalStatusEnum.YSP.getCode());
        flowStateInfoVo.setChangeTime(LocalDateTime.parse("2025-06-18T14:17:07.074"));
        flowStateInfoVo.setProcessDefinitionKey(ProcessDefinitionKeyEnum.PRICE_APPROVAL_KEY.getValue());
        flowStateInfoVo.setProcessNumber("JS2025061800009");
        processStateListener.onMessage(flowStateInfoVo);
    }


    /**
     *
     * 记得把 producer 和 consumer 的 namespace 一齐改为一个独一无二的。
     * 去掉 @Profile("!dev")
     * 然后启动flow
     * 最后跑这个test
     */
    @Test
    void handlePass() {
        FlowStateInfoVo stateInfoVo=new FlowStateInfoVo();
        stateInfoVo.setProcessInstanceId("7f15c9f9-8781-11ef-aef6-0242ac120004");
        stateInfoVo.setProcessDefinitionKey("priceApproval");
        stateInfoVo.setProcessState(2);
        stateChangeSend(stateInfoVo);
        // priceReviewProcessService.saveSnapshot("a39d9e3b-f80d-4494-8440-ff09ab2db587","abce4cad-885b-11ef-ad3e-0242ac120004");
        // priceReviewProcessService.querySpecialCodeApproverList(null,null);
    }

    private void stateChangeSend(FlowStateInfoVo stateInfoVo) {
        Message<?> message = MessageBuilder.withPayload(stateInfoVo)
                .setHeader(MessageConst.PROPERTY_KEYS, stateInfoVo.getProcessInstanceId())
                .build();
        rocketMQTemplate.send(TfsConstants.MqTopic.PROCESS_STATE_CHANGE + ":" + stateInfoVo.getProcessDefinitionKey(), message);
    }

}
