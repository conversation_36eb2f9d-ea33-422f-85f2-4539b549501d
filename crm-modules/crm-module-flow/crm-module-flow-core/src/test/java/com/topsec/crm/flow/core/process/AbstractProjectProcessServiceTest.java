package com.topsec.crm.flow.core.process;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.flow.core.FlowApplicationTests;
import com.topsec.crm.flow.core.entity.PriceReviewMain;
import com.topsec.crm.flow.core.mapper.PriceReviewMainMapper;
import com.topsec.crm.flow.core.mapper.ProcessExtensionInfoMapper;
import com.topsec.crm.flow.core.process.impl.ContractReviewProcessService;
import com.topsec.crm.project.api.RemoteProjectAgentService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class AbstractProjectProcessServiceTest extends FlowApplicationTests {

    @Resource
    private ContractReviewProcessService contractReviewProcessService;

    @Resource
    private ProcessExtensionInfoMapper processExtensionInfoMapper;
    @Resource
    private PriceReviewMainMapper priceReviewMainMapper;
    @Resource
    protected RemoteProjectAgentService remoteProjectAgentService;

    @Test
    void priceReviewProject_info() {

        // CrmProjectDirectlyVo projectInfo = new CrmProjectDirectlyVo();
        // projectInfo.setProjectNo("gggds");
        // projectInfo.setUpdateTime(LocalDateTime.now());
        // PriceReviewMain priceReviewMain=new PriceReviewMain();
        // priceReviewMain.setProjectInfo(projectInfo);
        // priceReviewMain.setProcessInstanceId("5d57c276-b12a-11ef-91eb-0242ac12000d");

        //
        // priceReviewMainMapper.update(priceReviewMain,
        //         new LambdaQueryWrapper<PriceReviewMain>()
        //                 .eq(FlowBaseEntity::getProcessInstanceId, "5d57c276-b12a-11ef-91eb-0242ac12000d"));

        PriceReviewMain priceReviewMain1 = priceReviewMainMapper
                .selectOne(new LambdaQueryWrapper<PriceReviewMain>()
                        .eq(PriceReviewMain::getProcessInstanceId, "5d57c276-b12a-11ef-91eb-0242ac12000d"));
        System.out.println(priceReviewMain1);
    }
}
