package com.topsec.crm.flow.core;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.packagescan.resource.ClassPathResource;
import com.alibaba.nacos.common.packagescan.resource.Resource;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.topsec.crm.account.api.client.RemoteAccountService;
import com.topsec.crm.agent.api.RemoteAccountVpnService;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentAccountVpnVo;
import com.topsec.crm.agent.api.entity.CrmAgentContactsVo;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.customer.api.entity.CrmLinkmanVo;
import com.topsec.crm.flow.core.entity.AgentRegistration;
import com.topsec.crm.flow.core.entity.AgentRegistrationContact;
import com.topsec.crm.flow.core.mapstruct.AgentConvertor;
import com.topsec.crm.flow.core.service.AgentBusinessInfoService;
import com.topsec.crm.flow.core.service.AgentBusinessSpecialInfoService;
import com.topsec.crm.flow.core.service.AgentRegistrationContactService;
import com.topsec.crm.flow.core.service.AgentRegistrationService;
import com.topsec.crm.framework.common.bean.AreaVO;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.name.NameUtils;
import com.topsec.crm.framework.common.util.StringUtils;
import com.topsec.crm.operation.api.RemoteAreaService;
import com.topsec.crm.tyc.api.RemoteCustomerBussinessInfoService;
import com.topsec.crm.tyc.api.RemoteTycSelectService;
import com.topsec.msg.api.client.NotifyClient;
import com.topsec.msg.common.constants.MessageConstant;
import com.topsec.msg.common.dto.EmailCommon;
import com.topsec.msg.common.vo.NotifyMessageVo;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.dto.crm.CrmAgentAccountAddDTO;
import com.topsec.tbscommon.vo.crm.CrmAgentAccountVO;
import com.topsec.tos.common.HyperBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ActiveProfiles;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@SpringBootTest
@ActiveProfiles({"dev","feignDev","redisDev","configDev"})
@Slf4j
public class AgentTests {
    @Autowired
    private AgentRegistrationService agentRegistrationService;
    @Autowired
    private AgentRegistrationContactService agentRegistrationContactService;
    @Autowired
    private RemoteAgentService remoteAgentService;
    @Autowired
    private RemoteTycSelectService remoteTycSelectService;
    @Autowired
    private NotifyClient notifyClient;
    @Autowired
    private RemoteAccountService remoteAccountService;
    protected static final String VPN_HANDBOOK_PATH = "classpath:handbook/VPN使用手册.pdf";
    @Autowired
    private RemoteAccountVpnService accountVpnService;
    @Autowired
    private RemoteCustomerService remoteCustomerService;
    @Autowired
    private RemoteAreaService remoteAreaService;

    @Test
    void query(){
        AgentRegistration agentRegistration = agentRegistrationService.getOne(new LambdaQueryWrapper<AgentRegistration>().eq(AgentRegistration::getProcessInstanceId, "b80e695c-4824-11f0-8d4a-0242ac12000d"));
        agentRegistration.setApprovalPassTime(LocalDateTime.now());
        agentRegistrationService.updateById(agentRegistration);
        CrmAgentVo crmAgentVo = HyperBeanUtils.copyPropertiesByJackson(agentRegistration, CrmAgentVo.class);
        //根据渠道名称查询客户ID，如果没有自动生成
        CrmCustomerVo crmCustomerVo = remoteCustomerService.findByName(agentRegistration.getAgentName()).getObjEntity();
        if (crmCustomerVo != null) {
            crmAgentVo.setId(crmCustomerVo.getId());
        } else {
            crmAgentVo.setId(null);
        }

        List<AgentRegistrationContact> contactList = agentRegistrationContactService.list(new LambdaQueryWrapper<AgentRegistrationContact>().eq(AgentRegistrationContact::getAgentRegistrationId, agentRegistration.getId()));
        if (CollectionUtils.isNotEmpty(contactList)) {
            List<CrmAgentContactsVo> crmAgentContactsVos = HyperBeanUtils.copyListPropertiesByJackson(contactList, CrmAgentContactsVo.class);
            if (StringUtils.isNotEmpty(crmAgentVo.getReferrerId())) {
                crmAgentContactsVos.stream().forEach(item ->
                        item.setCreateUser(crmAgentVo.getReferrerId())
                );
            }
            crmAgentContactsVos.forEach(item -> item.setId(null));
            crmAgentVo.setContacts(crmAgentContactsVos);
        }

        /**
         * 1.查询企业信息修改表，有数据则保存到agent数据，没有数据则查询天眼查企业信息，如果成功，则保存到agent数据
         */

        JSONObject businessInfoUpdateByAgentId = agentRegistrationService.getBusinessInfoByAgentId(agentRegistration.getId());
        JSONObject businessInfoObj = null;
        JsonObject<JSONObject> infoObj = remoteTycSelectService.getBusinessInfoByName(agentRegistration.getAgentName());
        if (infoObj.isSuccess() && infoObj.getObjEntity() != null) {
            businessInfoObj = infoObj.getObjEntity();
        }

        JSONObject businessObj = businessInfoUpdateByAgentId != null ? businessInfoUpdateByAgentId : businessInfoObj;

        if (businessObj != null) {
            crmAgentVo.setCrmAgentBusinessInfoVo(businessObj);
        }

//        CrmAgentVo agentVo = null;
        CrmAgentVo agentVo = remoteAgentService.getAgentInfo("98f73cd721db603d543e34eb1eeb0d7c").getObjEntity();
//        try {
//            agentVo = remoteAgentService.saveAgentRegistrationPassData(crmAgentVo).getObjEntity();
//        } catch (Exception e) {
//            log.error("渠道注册信息同步到渠道表处理失败: {}", crmAgentVo.getAgentName());
//            throw new CrmException("渠道注册信息同步到渠道表处理失败");
//        }

        String provinceName = Optional.ofNullable(remoteAreaService.queryByCode(agentVo.getProvinceCode()))
                .map(JsonObject::getObjEntity)
                .map(AreaVO::getName)
                .orElse("");
        String cityName = Optional.ofNullable(remoteAreaService.queryByCode(agentVo.getCityCode()))
                .map(JsonObject::getObjEntity)
                .map(AreaVO::getName)
                .orElse("");
        String countyName = Optional.ofNullable(remoteAreaService.queryByCode(agentVo.getCountyCode()))
                .map(JsonObject::getObjEntity)
                .map(AreaVO::getName)
                .orElse("");
        String area = String.format("%s/%s/%s", provinceName, cityName, countyName);

        // 同步客户信息
        CrmCustomerVo customerVo = AgentConvertor.INSTANCE.toCustomerVo(agentVo);
        Optional.ofNullable(area).ifPresent(customerVo::setArea);
        Optional.ofNullable(agentVo.getCreateUser()).map(NameUtils::getName).ifPresent(customerVo::setCreateUserName);

        List<CrmLinkmanVo> linkmanVos = Optional.ofNullable(agentVo.getContacts())
                .orElse(Collections.emptyList())
                .stream()
                .map(item -> {
                    CrmLinkmanVo linkmanVo = AgentConvertor.INSTANCE.toLinkmanVo(item);
                    linkmanVo.setCreateUser(item.getCreateUser());
                    linkmanVo.setCreateTime(item.getCreateTime());
                    return linkmanVo;
                })
                .collect(Collectors.toList());
        customerVo.setLinkmans(linkmanVos);
        customerVo.setBusinessInfo(agentVo.getCrmAgentBusinessInfoVo());

        try {
            remoteCustomerService.syncCustomer(customerVo);
        } catch (Exception e) {
            log.error("同步客户信息失败: {}", crmAgentVo.getAgentName(), e);
        }

        CrmAgentAccountAddDTO account = AgentConvertor.INSTANCE.toAccount(agentVo);

        JsonObject<CrmAgentAccountVO> jsonObject = remoteAccountService.addAgentAccount(account);
        boolean success = jsonObject.isSuccess();
        CrmAgentAccountVO agentAccountVO = jsonObject.getObjEntity();
        if (!success) {
            log.error("添加代理账户失败: {}", crmAgentVo.getAgentName(), jsonObject.getMessage());
            return;
        }
        // 更新 VPN 账号
        CrmAgentAccountVpnVo accountVpnVo;
        try {
            accountVpnVo = accountVpnService.updateAccountId(agentAccountVO.getAccountId()).getObjEntity();
        } catch (Exception e) {
            log.error("更新 VPN 账号失败: {}", crmAgentVo.getAgentName(), e);
            return;
        }
        // 发送通知
        try {
            sendNotification(agentVo, agentAccountVO, accountVpnVo);
        } catch (Exception e) {
            log.error("发送通知失败: {}", crmAgentVo.getAgentName(), e);
        }
    }
    private void sendNotification(CrmAgentVo objEntity, CrmAgentAccountVO agentAccountVO, CrmAgentAccountVpnVo accountVpnVo) {

        NotifyMessageVo emailEntity = createNotificationEntity(
                "【天慧代理商管理系统】_注册成功通知",
                "<table width='84.5%' align='center' style='margin-top:10px;'><tr><td align='left'  style='font-size:14px; font-weight:bold;' colspan='2'>尊敬的合作伙伴您好：</td></tr>" +
                        "<tr><td width='1%'></td><td width='99%'  align='left' style='font-size:14px; line-height:25px;'>" + objEntity.getAgentName() + ",恭喜您成功注册【天融信天慧合作伙伴管理系统】，" +
                        "以下账号在您后续的使用过程中至关重要，请牢记以下账号信息，在您第一次登陆系统的时候，请务必修改密码以保证安全,请勿向其他第三方透露账户及密码。" +
                        "</td></tr></table><br><table width='85%' align='center' style='font-weight:bold;border-bottom:1px solid #76869F; color:#FF6600;margin-top:10px;'>" +
                        "<tr><td >账户相关信息</td></tr></table><table align='center' style='margin-top:5px;width:85%;border-collapse:collapse;border:1px solid #BEBDBD;'>" +
                        "<tr style='height:23px;text-align:center;margin:0px;'><td style='text-align:center;vertical-align:middle;border:1px solid #BEBDBD;color:#000000;" +
                        "font-size:14px;background-color:#F2F4F3;height:30px;' align='center' width='16%'>系统账号及密码</td>  <td align='left' width='84%'" +
                        "style='border: 1px solid #BEBDBD;color:#000000;font-size: 14px;word-break:break-all;word-wrap:break-word;'  >账号：" + agentAccountVO.getUserName() + " ," +
                        "密码：" + agentAccountVO.getPassword() + "</td></tr><tr style='height:23px;text-align:center;margin:0px;'> <td style='text-align:center;" +
                        "vertical-align:middle;border:1px solid #BEBDBD;color:#000000;font-size:14px;background-color:#F2F4F3;height:30px;' align='center' width='16%'>" +
                        "VPN账号及密码</td><td align='left' width='84%' style='border: 1px solid #BEBDBD;color:#000000;font-size: 14px;word-break:break-all;word-wrap:break-word;'  >" +
                        "账号：" + accountVpnVo.getAccountVpn() + " ,密码：" + accountVpnVo.getAccountPassword() + " </td></tr><tr style='height:23px;text-align:center;margin:0px;'>" +
                        "<td style='text-align:center;vertical-align:middle;border: 1px solid #BEBDBD;color:#000000;font-size: 14px;background-color:#F2F4F3;height:30px;'" +
                        "align='center' width='16%'>注意事项</td> <td align='left' width='84%' style='border: 1px solid #BEBDBD;color:#000000;" +
                        "font-size: 14px;word-break:break-all;word-wrap:break-word;'><p>&nbsp;</p><p>1、您需要先登陆vpn才可以登录天慧合作伙伴管理系统，vpn登录方式见附件；</p><p>" +
                        "2、登录系统或者VPN之后，第一时间请修改密码，保证账户安全；</p><p>&nbsp;</p></td></tr></table><table width='84.5%' align='center' style='margin-top:30px;'>" +
                        "<tr><td align='right'  style='font-size:18px; font-weight:bold;' colspan='2'></td></tr><tr><td width='50%'>" + "</td><td width='50%' style='font-size:14px; font-weight:bold;' align='right' >北京天融信公司.渠道生态合作中心</td></tr><tr><td width='50%'>" +
                        "</td><td width='50%' style='font-size:14px; font-weight:bold;' align='right' >" + LocalDate.now() + "</td></tr></table>",
                MessageConstant.EMAIL,
                objEntity.getRegisterEmail());
        NotifyMessageVo smsEntity = createNotificationEntity(
                "",
                "您的注册申请流程已通过，详情已发至您的公司邮箱，请查收。如有问题，可随时联系渠道经理处理，谢谢。",
                MessageConstant.SMS,
                objEntity.getRegisterPhone());
        //4.自定义邮件服务通用数据实体
        EmailCommon emailCommon = new EmailCommon();
        EmailCommon.EmailData emailData = new EmailCommon.EmailData();
        List<EmailCommon.EmailFile> attachmentsData = new ArrayList<>();

        Resource resource = new ClassPathResource("handbook/VPN使用手册.pdf");
        try {
            InputStream inputStream = resource.getInputStream();
            attachmentsData.add(new EmailCommon.EmailFile("VPN使用手册.pdf", inputStream.readAllBytes()));
        } catch (IOException e) {
            log.error("读取文件 {} 失败: {}", VPN_HANDBOOK_PATH, e.getMessage(), e);
        }
        emailData.setAttachmentsData(attachmentsData);

        emailCommon.setEmailData(emailData);
        emailEntity.setEmailCommon(emailCommon);

        notifyClient.send(JSONObject.toJSONString(emailEntity));
        notifyClient.send(JSONObject.toJSONString(smsEntity));
    }

    private NotifyMessageVo createNotificationEntity(String subject, String content, String notifyType, String receiver) {
        NotifyMessageVo entity = new NotifyMessageVo();
        entity.setSubject(subject);
        entity.setContent(content);
        entity.setNotifyType(notifyType);
        List<String> receivers = new ArrayList<>();
        // todo zlw 正式上线前进行修改
        if (notifyType == "email") {
            receivers.add("<EMAIL>");
        } else if (notifyType == "sms") {
            entity.setIsCaptcha(false);
            receivers.add("15512716835");
        }
        receivers.add(receiver);
        entity.setReceiver(receivers);
        return entity;
    }

}
