package com.topsec.crm.flow.core.process.impl;

import com.google.common.collect.Lists;
import com.topsec.crm.agent.api.RemoteAgentService;
import com.topsec.crm.agent.api.entity.CrmAgentVo;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductPriceGapVO;
import com.topsec.crm.flow.core.entity.PriceReviewSigningAgent;
import com.topsec.crm.flow.core.service.PriceReviewFlowService;
import com.topsec.crm.flow.core.service.PriceReviewProductOwnService;
import com.topsec.crm.flow.core.service.PriceReviewSigningAgentService;
import com.topsec.crm.flow.core.service.impl.DeemedDirectRecordServiceImpl;
import com.topsec.crm.flow.core.util.InnerProductOwnQuery;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.jwt.UserInfoHolder;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.vo.EmployeeVO;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SpringBootTest
class PriceReviewProcessServiceTest {
    @Resource
    private  PriceReviewSigningAgentService priceReviewSigningAgentService;
    @Resource
    private RemoteAgentService remoteAgentService;
    @Resource
    private  DeemedDirectRecordServiceImpl deemedDirectRecordService;
    @Resource
    private RedisTemplate<String,String> stringRedisTemplate;
    @Resource
    private PriceReviewProcessService priceReviewProcessService;
    @Resource
    private PriceReviewFlowService priceReviewFlowService;
    @Resource
    private PriceReviewProductOwnService priceReviewProductOwnService;
    @Resource
    private TosEmployeeClient tosEmployeeClient;

    @Test
    public void syncSaleAgreementToContract() {
        String processInstanceId="e090bb86-4dbb-11f0-8d4a-0242ac12000d";
        String projectId="5d6168d8-f7ff-4861-ad6b-69f716559bca";
        priceReviewProcessService.syncSaleAgreementToContract(processInstanceId, projectId);
    }

    @Test
    void anfu() {
        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        // priceReviewProcessService
        //         .doAnFuGatewayCondition(objectObjectHashMap,Set.of("f4533dff6c614150846f862a300d756d",
        //                 "9a3c02191fc14172bc08a6222e0c0a92", "5bf04bc4dbd1460ab386b691c5f8c6ca"));
        priceReviewProcessService
                .doAnFuGatewayCondition(objectObjectHashMap, Set.of("324e020cd94044db9b19631dd01c61bf"));

        System.out.println(objectObjectHashMap);
    }

    @Test
    void cc(){
        String keyt = "flow:testCc";
        List<String> list = Lists.newArrayList("1111","22");
        stringRedisTemplate.opsForList().rightPushAll(keyt,list);

        List<String> list1 = stringRedisTemplate.opsForList().range(keyt, 0, -1);
        System.out.println(list1);
        List<String> list2 = stringRedisTemplate.opsForList().range("222", 0, -1);
        System.out.println(list2);


        String key = "selfIncr:%s".formatted("stringRedisTemplate");
        Long incr = stringRedisTemplate.opsForValue().increment(key);
        if (incr == null){
            throw new IllegalStateException("自增序号生成器异常");
        }
        if (incr == 1L) {
            // 如果是第一次增加，则设置过期时间 48小时
            stringRedisTemplate.expire(key, 48, TimeUnit.HOURS);
        }
    }


    @Test
    void chaoqiyingshouSVP() {
        String projectId="105242c7-bab4-4cac-82bb-3cda0064c91b";
        List<PriceReviewProductOwnDTO> ownDTOS = priceReviewProductOwnService.queryProjectOwnByProjectId(new InnerProductOwnQuery(projectId));
        List<PriceReviewSigningAgent> priceReviewSigningAgents = ListUtils.emptyIfNull(priceReviewSigningAgentService.queryListByProjectId(projectId));
        Set<String> agentIds = priceReviewSigningAgents
                .stream().map(PriceReviewSigningAgent::getAgentId).collect(Collectors.toSet());
        for (String agentId : agentIds) {
            CrmAgentVo crmAgentVo = Optional.ofNullable(remoteAgentService.getAgentInfo(agentId))
                    .map(JsonObject::getObjEntity)
                    .orElseThrow(() -> new CrmException("渠道商信息不存在"));
            String agentName = crmAgentVo.getAgentName();
            BigDecimal overdueReceivables = crmAgentVo.getOverdueReceivables();

            if (ObjectUtils.compare(overdueReceivables, BigDecimal.ZERO) > 0 &&
                !deemedDirectRecordService.IsExistByCompanyName(agentName)) {
                EmployeeVO employeeVO = Optional.ofNullable(tosEmployeeClient.queryEmpReportByPosition(UserInfoHolder.getCurrentPersonId(), "高级副总裁", null))
                        .map(JsonObject::getObjEntity)
                        .orElse(Collections.emptyList())
                        .stream().findFirst().orElseThrow(() -> new CrmException("汇报线不存在SVP"));
                String uuid = employeeVO.getUuid();
                List<PriceReviewProductPriceGapVO> list = ownDTOS.stream().map(PriceReviewProductPriceGapVO::new).toList();
                boolean b = priceReviewFlowService.includeDiscountAndPriceGap(uuid, list);

            }
        }
    }




}
