package com.topsec.crm.flow.core;

import com.topsec.crm.flow.api.RemoteContractUnconfirmedDetailService;
import com.topsec.crm.flow.api.dto.BorrowForProbationDeviceFlowVO;
import com.topsec.crm.flow.api.dto.BorrowForProbationDeviceOccupyStateVO;
import com.topsec.crm.flow.core.process.impl.BorrowForForwardProcessService;
import com.topsec.crm.flow.core.process.impl.BorrowForProbationBackProcessService;
import com.topsec.crm.flow.core.process.impl.BorrowForProbationRenewProcessService;
import com.topsec.crm.flow.core.process.impl.MilitaryProductInspectionProcessService;
import com.topsec.crm.flow.core.service.impl.lockstatequerier.PerformanceReportLockStateQuerier;
import com.topsec.vo.FlowStateInfoVo;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SpringBootTest
@ActiveProfiles({"dev","feignDev","redisDev","configDev"})
public class ForwardProductTests {
    @Autowired
    private BorrowForForwardProcessService borrowForForwardProcessService;

    @Autowired
    private BorrowForProbationBackProcessService borrowForProbationBackProcessService;

    @Autowired
    private BorrowForProbationRenewProcessService borrowForProbationRenewProcessService;

    @Autowired
    private MilitaryProductInspectionProcessService militaryProductInspectionProcessService;

    @Resource
    private PerformanceReportLockStateQuerier performanceReportLockStateQuerier;

    @Resource
    private RemoteContractUnconfirmedDetailService remoteContractUnconfirmedDetailService;

    @Test
    void queryCenterLeaderApproverList(){
        Set<String> deviceIds = new HashSet<String>();
        deviceIds.add("286b336200c741f78828d0fdc88badfd");
        Map<String, BorrowForProbationDeviceOccupyStateVO> map = borrowForForwardProcessService.checkDeviceOccupyState(deviceIds);
        System.out.println(1);
    }


    @Test
    void handleTest(){
        FlowStateInfoVo processInfo = new FlowStateInfoVo();
        processInfo.setProcessInstanceId("170f3b41-d49e-11ef-a654-0242ac12000d");
        borrowForForwardProcessService.handlePass(processInfo);
    }

    @Test
    void handleTest1(){
        remoteContractUnconfirmedDetailService.syncFlow();

//        FlowStateInfoVo processInfo = new FlowStateInfoVo();
//        processInfo.setProcessInstanceId("4eb895e8-b212-11ef-91eb-0242ac12000d");
//        borrowForProbationBackProcessService.handlePass(processInfo);
    }

    @Test
    void handleTestRenew(){
        FlowStateInfoVo processInfo = new FlowStateInfoVo();
        processInfo.setProcessInstanceId("cff4792d-b209-11ef-91eb-0242ac12000d");
        borrowForProbationRenewProcessService.handlePass(processInfo);
    }

    @Test
    void showBorrowForProbationDeviceFlow(){
        List<BorrowForProbationDeviceFlowVO> renew =  borrowForProbationRenewProcessService.showBorrowForProbationDeviceFlow("5b6ea73e137b4e4c92678dd4cf766223");
        renew.get(0);
    }

    @Test
    void query(){
       String project = "47ca1fcd-24bd-438e-a00a-86e011065168";
        Set<String> productRecordIdSet = new HashSet<>();
        productRecordIdSet.add("a0eaecab-b0c7-4350-b0dc-ffe4a02d5ff1");
        productRecordIdSet.add("ce99ad7d-0aac-4f94-868b-1df7f635d766");
        productRecordIdSet.add("76a67a88-0c5f-49a2-9d31-cec718e970f8");
//        militaryProductInspectionProcessService.query(project,productRecordIdSet);
    }
    @Test
    void performanceReportLockStateQuerier(){
        String project = "1fbb5501-d05e-4c6a-b99d-87c7925ed898";
        Set<String> productRecordIdSet = new HashSet<>();
        productRecordIdSet.add("41df825c-17d3-4476-9af8-3c57079c5aad");
        productRecordIdSet.add("01f35683-87cd-4790-9c98-41925020cd4b");
        productRecordIdSet.add("b40aa3b9-6129-41ae-b55d-e154023e9083");
        System.out.println(performanceReportLockStateQuerier.query(project, productRecordIdSet));
    }

}
