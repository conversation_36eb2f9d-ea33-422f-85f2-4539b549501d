package com.topsec.crm.flow.core.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Sets;
import com.topsec.crm.contract.api.RemoteContractInvoiceService;
import com.topsec.crm.contract.api.RemoteContractReviewService;
import com.topsec.crm.contract.api.entity.CrmContractProductOwnVO;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractProcessInfoVO;
import com.topsec.crm.contract.api.entity.invoice.InvoiceProductQueryVO;
import com.topsec.crm.customer.api.RemoteCustomerService;
import com.topsec.crm.customer.api.entity.CrmCustomerVo;
import com.topsec.crm.flow.api.RemoteContractReviewFlowService;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainBaseInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.ContractReviewMainFlowLaunchDTO;
import com.topsec.crm.flow.api.dto.contractreview.baseinfo.ContractBasicInfoDTO;
import com.topsec.crm.flow.api.dto.contractreview.fileinfo.ContractElectricContractDTO;
import com.topsec.crm.flow.api.dto.contractreview.fileinfo.ContractElectricFillDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductOwnDTO;
import com.topsec.crm.flow.api.dto.contractreview.productinfo.ContractProductThirdDTO;
import com.topsec.crm.flow.api.dto.contractreview.request.ContractOwnQuery;
import com.topsec.crm.flow.api.dto.contractreview.sninfo.ChangeSaleSnQuery;
import com.topsec.crm.flow.api.dto.contractreview.sninfo.ChangeSaleSnVO;
import com.topsec.crm.flow.core.FlowApplicationTests;
import com.topsec.crm.flow.core.entity.*;
import com.topsec.crm.flow.core.mapper.ContractProductSnMapper;
import com.topsec.crm.flow.core.process.impl.ContractReviewProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.crm.framework.common.util.PageUtils;
import com.topsec.crm.framework.common.web.page.TableDataInfo;
import com.topsec.crm.operation.api.entity.SupplierVO;
import com.topsec.crm.product.api.entity.CrmProductCostVo;
import com.topsec.crm.product.api.entity.CrmProductSoftHardwareIdentificationVO;
import com.topsec.crm.product.api.entity.CrmProductVo;
import com.topsec.crm.project.api.client.RemoteProjectProductOwnClient;
import com.topsec.crm.project.api.entity.CrmProjectProductMaintainVO;
import com.topsec.crm.project.api.entity.CrmProjectProductOwnVO;
import com.topsec.enums.ActionTypeEnum;
import com.topsec.tbsapi.client.TbsAccountClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.AccountVO;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.vo.FlowStateInfoVo;
import com.topsec.vo.node.ApproveNode;
import com.topsec.vo.task.TfsTaskVo;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.springframework.test.annotation.Rollback;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2024/8/13 16:44
 */
class ContractReviewFlowServiceImplTest extends FlowApplicationTests {

    @Resource
    public ContractReviewProcessService contractReviewProcessService;

    @Resource
    public ContractReviewProductOwnService contractReviewProductOwnService;

    @Resource
    public ContractReviewAttachmentService contractReviewAttachmentService;

    @Resource
    public RemoteContractReviewFlowService contractReviewMainService;

    @Resource
    public ContractReviewFlowService contractReviewFlowService;

    @Resource
    public ContractReviewMainService contractReviewMainService1;

    @Test
    void pass(){
        FlowStateInfoVo flowStateInfoVo = new FlowStateInfoVo();
        flowStateInfoVo.setProcessInstanceId("ad9a8813-4ff6-11f0-8b0a-0242ac12000a");
        flowStateInfoVo.setCurrentActivityId("contractReview_03123");
        contractReviewProcessService.handlePass(flowStateInfoVo);
    }


    @Resource
    RemoteContractInvoiceService remoteContractInvoiceService;
    @Test
    void handleTest(){
        List<ContractProductOwnDTO> contractProductOwnDTOS = contractReviewProductOwnService.productInfoByContractId("3fe01e362791def39449e02a88e1fce3", true);
        List<ContractProductThirdDTO> contractProductThirdDTOS = contractReviewProductThirdService.productThirdInfoByContractId("3fe01e362791def39449e02a88e1fce3", true);
        InvoiceProductQueryVO invoiceProductQueryVO = new InvoiceProductQueryVO();
        invoiceProductQueryVO.setContractNumber("12320250600003");
        invoiceProductQueryVO.setOwnList(contractProductOwnDTOS);
        // 过滤掉没有物料代码的
        List<ContractProductThirdDTO> invoiceThirds = contractProductThirdDTOS.stream().filter(productThirdDTO -> productThirdDTO.getStuffCode() != null).collect(Collectors.toList());
        invoiceProductQueryVO.setThirdList(invoiceThirds);
        remoteContractInvoiceService.handleContractEffectiveData(invoiceProductQueryVO);
    }

    @Test
    void generateContractNumber(){
        String contractNumber = contractReviewMainService1.generateContractNumber("195781d0596d90df148a8bb31e61dbfe", false);
        System.out.println(contractNumber);
    }

    @Resource
    public ContractAutoOrderService contractAutoOrderService;

    @Test
    void createContractLaunch(){
        contractAutoOrderService.createContractLaunch("1892538896981995521", "04c6e1241559472e9ac312a9d4c701ad");
    }

    @Test
    void testAgent(){
        contractReviewProcessService.launchByAgent("1913128452690644993", "",List.of());
    }

    @Resource
    private RemoteProjectProductOwnClient ownClient;

    @Resource
    private ContractReviewProductOwnService ownService;

    @Test
    void testContractProductOwn(){
        List<ContractProductOwnDTO> contractProductOwnDTOS = ownService.productOwnInfoTileByContractId("4583ae5f192028c8fa082b5888f441e2", true);
        System.out.println(JSON.toJSONString(contractProductOwnDTOS, true));
    }

    @Test
    void testContractAttachment(){
        ContractElectricContractDTO dto = new ContractElectricContractDTO();
        dto.setContractId("6d27ade185e778f73d94fd2eee7a1fda");
        dto.setFillDTO(new ContractElectricFillDTO());
        contractReviewAttachmentService.generateElectricContract(dto, true, "");
    }

    @Test
    void testSn(){
        Map<String, Integer> q12345 = contractReviewFlowService.queryStateBySnIndustry(List.of("Q12345"));
        System.out.println(q12345);
    }

    @Resource
    public ContractReviewSpecialCodeService contractReviewSpecialCodeService;

    @Test
    void testSpecialCode(){
        Map<String, CrmProjectProductMaintainVO> stringPriceReviewProductOwnDTOMap = contractReviewSpecialCodeService.specialCodeInfo(Arrays.asList("fde079b6-f0fe-4e12-bd2d-f096321e7735"), "e60f64eb-9861-4632-82ad-d76552c8657f");
        System.out.println(stringPriceReviewProductOwnDTOMap);
    }


    @Test
    void testInsertSpecialCode(){
        contractReviewFlowService.writeSpecialCode("5f85e3da2777d0545bb07f668be757f4");
    }

    @Resource
    private ContractReviewProductThirdService contractReviewProductThirdService;
    @Test
    void querySupplierByContractId() {
        List<SupplierVO> supplierVOS = contractReviewProductThirdService.querySupplierByContractId("9869a620b58dc195dcf96340e458ce65");
        System.out.println(JSON.toJSONString(supplierVOS));
    }

    @Test
    void page() {
        TableDataInfo data = contractReviewProductThirdService.pageBySupplierId("b79756af2d229f99615dd482f9d536ae");
        System.out.println(JSON.toJSONString(data));
    }

    @Test
    void basicInfo(){
        JsonObject<ContractBasicInfoDTO> contractBasicInfoDTOJsonObject =
                contractReviewMainService.contractBasicInfo("70d2194d5a4029eb51e575763a73c7c0");
        System.out.println(JSON.toJSONString(contractBasicInfoDTOJsonObject, true));
    }

    @Test
    void testLockProduct(){
        Map<String, ProductLockState> query = contractReviewProcessService.query("b5aacab0-4cc2-41bb-9968-08f03b54649a"
                , Sets.newHashSet("bf45f807-fb4a-4742-b352-18cad08577be"));
        System.out.println(JSON.toJSONString(query, true));
    }

    @Test
    void queryOwnByCondition(){
        ContractOwnQuery contractOwnQuery = new ContractOwnQuery();
        contractOwnQuery.setContractId("70d2194d5a4029eb51e575763a73c7c0");
        List<ContractProductOwnDTO> ownDTOS = contractReviewProductOwnService.queryOwnByCondition(contractOwnQuery);
        System.out.println(JSON.toJSONString(ownDTOS, true));
    }

    @Test
    void testProductOwnInfoTileByContractId(){
        List<ContractProductOwnDTO> data = contractReviewProductOwnService.productOwnInfoTileByContractId("dc7c711cd96c6413e73f804af6129a03", true);
        System.out.println(JSON.toJSONString(data, true));
    }

    @Test
    void getByContractId(){
        ContractReviewMainBaseInfoDTO dto = contractReviewMainService1.getByContractId("fe61036532a4fb2f948e66ab560dfc0c");
        System.out.println(JSON.toJSONString(dto));
    }

    @Resource
    private TosEmployeeClient tosEmployeeClient;

    @Test
    void testSale(){

        JsonObject<EmployeeVO> byId = tosEmployeeClient.findById("fb0a925b155142fc81d650e40841c487");
        System.out.println(JSON.toJSONString(byId));
    }

    @Test
    void queryStateSn(){
        Map<String, Integer> q2 = contractReviewFlowService.queryStateBySnIndustry(List.of("Q2"));

        List<CrmProjectProductOwnVO> vos = contractReviewProductOwnService.getProjectProductByProjectId("88db9e65-9cc2-480d-99ee-5642611e46e7");
        System.out.println(vos);
        System.out.println(q2);
    }

    @Test
    void getByOwnId(){
        ContractReviewMainFlowLaunchDTO contractReviewMainFlowLaunchDTO =
                contractReviewMainService1.contractInfo("5f85e3da2777d0545bb07f668be757f4", false, true);
        List<ContractProductOwnDTO> ownDTOS = contractReviewProductOwnService.productOwnInfoTileByContractId("5f85e3da2777d0545bb07f668be757f4", true);
        System.out.println(JSON.toJSONString(ownDTOS, true));
        System.out.println(JSON.toJSONString(contractReviewMainFlowLaunchDTO, true));
    }

    @Test
    void testProductInfo(){
        TableDataInfo tableDataInfo = contractReviewProductOwnService.productInfoByProjectId("0b9687ff-fa42-43d3-bcd1-a78c6928243c", "df0c8ff2b496fb156e785a9b6f2f2366");
        System.out.println(JSON.toJSONString(tableDataInfo, true));
    }

    @Test
    void testThirdProduct(){
        TableDataInfo tableDataInfo1 = contractReviewProductThirdService.productInfoByProjectId("5db60e0f-3507-486e-81f6-cec04043278f", "92349db015ce179ae55bdb8ffd0d3565");
        System.out.println(JSON.toJSONString(tableDataInfo1, true));

    }



    @Test
    void testProductInfoByContractId(){
        ContractReviewMainFlowLaunchDTO launchDTO = contractReviewMainService1.contractInfoByProcessInstanceId("23a77b03-6094-11f0-b3f6-0242ac12000a");
        System.out.println(JSON.toJSONString(launchDTO, true));
    }

    @Resource
    private RemoteCustomerService remoteCustomerService;
    @Test
    void getCustomer(){
        JsonObject<CrmCustomerVo> customerInfoForAgentProject = remoteCustomerService.getCustomerInfoForAgentProject("11");
        System.out.println(customerInfoForAgentProject);
    }

    @Resource
    private TbsAccountClient tbsAccountClient;

    @Test
    void tbsTest(){
        JsonObject<AccountVO> account = tbsAccountClient.selectByUsername("王亚欣11542");
        System.out.println(account);
        JsonObject<EmployeeVO> accountVOJsonObject = tosEmployeeClient.findById(account.getObjEntity().getPersonId());
        System.out.println(accountVOJsonObject);

    }

    @Resource
    private TfsNodeClient tfsNodeClient;
    @Test
    void tfsTest(){
        JsonObject<Map<String, Set<ApproveNode>>> setJsonObject = tfsNodeClient.queryNodeByProcessInstanceIdList(List.of("f5b1d53e-c35a-11ef-93ba-0242ac12000d"));
        System.out.println(setJsonObject);
    }

    /*@Test
    void testGetProcessInfoByProcessInstanceIds(){
        List<ContractReviewMainBaseInfoDTO> processInfoByProcessInstanceIds = contractReviewFlowService.getProcessInfoByPerformanceReportId("7dccda4d3f6c05cd4d2663d496e76c4f");
        System.out.println(processInfoByProcessInstanceIds);
    }*/

    @Test
    void saveValidData(){
        String message = "{\"processInstanceId\":\"b2cf6e0e-1e54-11f0-9b72-0242ac12000d\",\"processState\":1,\"processDefinitionKey\":\"contractReview\",\"actionTypeEnum\":\"APPROVAL\",\"currentActivityId\":\"contractReview_03\",\"targetActivityId\":\"contractReview_04A\",\"processNumber\":\"***************\",\"comment\":null,\"changeTime\":\"2025-04-21T10:03:16.107\"}";
        FlowStateInfoVo flowStateInfoVo = JSON.parseObject(message, FlowStateInfoVo.class);
        contractReviewProcessService.handleProcessing(flowStateInfoVo);
    }

    @Test
    void back00(){
        String message = "{\"processInstanceId\":\"b13a443f-0a37-11f0-841a-0242ac12000d\",\"processState\":1,\"processDefinitionKey\":\"contractReview\",\"actionTypeEnum\":\"BACK\",\"currentActivityId\":\"contractReview_04\",\"targetActivityId\":\"__initiator__\",\"processNumber\":\"HT2025032600009\",\"comment\":null,\"changeTime\":\"2025-04-17T19:53:51.683\"}";
        FlowStateInfoVo flowStateInfoVo = JSON.parseObject(message, FlowStateInfoVo.class);
        flowStateInfoVo.setActionTypeEnum(ActionTypeEnum.BACK);
        contractReviewProcessService.handleProcessingBack(flowStateInfoVo);
    }

    @Resource
    private RemoteContractReviewService remoteContractReviewService;

    @Test
    void saveOrRefreshExecute(){
        remoteContractReviewService.saveOrRefreshContractExecute("12320241200019");
    }

    @Test
    void testGetProcessInfoByProcessInstanceIds(){
        List<ContractReviewMainBaseInfoDTO> processInfoByProcessInstanceIds = contractReviewFlowService.getProcessInfoByPerformanceReportId("9a6cd5fa79b21b61006a6b676351f900", null);
        System.out.println(JSON.toJSONString(processInfoByProcessInstanceIds, true));
    }

    @Test
    void testDeleteAtte(){
        contractReviewAttachmentService.delete("34394405ac35310104df3605b7387853");
    }

    @Test
    @Rollback
    void testTabTaxLoss(){
        Set<String> productOwnIds = Set.of("e24d1523ce4337053387753ab46cd650");
        List<ContractReviewProductOwn> list = ownService.list(new LambdaQueryWrapper<ContractReviewProductOwn>()
                .in(ContractReviewProductOwn::getId, productOwnIds)
                .eq(ContractReviewProductOwn::getDelFlag, 0));
        Optional.ofNullable(list).orElseThrow(() -> new CrmException("合同产品不存在"));
        List<ContractProductOwnDTO> owns = ownService.buildProductOwnTile(list, list.get(0).getContractReviewId(), true);
        // 查询签订公司的名称
        ContractReviewMain contractReviewMain = contractReviewMainService1.getOne(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getId, list.get(0).getContractReviewId()).eq(ContractReviewMain::getDelFlag, 0).last("limit 1"));
        List<ContractProductOwnDTO> resultDTO = new ArrayList<>();
        //  取zcc的接口 将软硬件标识 和 计算出来的税损一起写入表中
        owns.forEach(contractProductOwnDTO -> {
            String productId = contractProductOwnDTO.getProductId();
            // 软硬件标识
            CrmProductSoftHardwareIdentificationVO query = new CrmProductSoftHardwareIdentificationVO();
            query.setProductId(productId);
            query.setContractSignCompanyId(contractReviewMain.getSigningCompany());
            BigDecimal taxLoss = null;

            String identificationName = "S&H";

            // 构建计算对象
            ContractReviewProductOwnServiceImpl.TaxLossCalculate calculate = new ContractReviewProductOwnServiceImpl.TaxLossCalculate();
            calculate.setCost(BigDecimal.valueOf(100));
            calculate.setPrice(BigDecimal.valueOf(200));
            ContractReviewProductOwnServiceImpl.TaxLossStrategy taxLossStrategy = ContractReviewProductOwnServiceImpl.TaxLossBySoftHardByIdentEnum.getTaxLossStrategy(identificationName);
            // 税损
            taxLoss = taxLossStrategy.taxLoss(calculate);

            ownService.update(new LambdaUpdateWrapper<ContractReviewProductOwn>().set(ContractReviewProductOwn::getTaxLoss, taxLoss)
                    .set(ContractReviewProductOwn::getSoftHardIdent, 0)
                    .eq(ContractReviewProductOwn::getId, contractProductOwnDTO.getId())
            );
            ContractProductOwnDTO data = new ContractProductOwnDTO();
            data.setId(contractProductOwnDTO.getId());
            data.setTaxLoss(taxLoss);
            resultDTO.add(data);
        });
        System.out.println(resultDTO);
    }

    @Resource
    private ContractProductSnService productSnService;

    @Test
    void pageChangeSalesSn(){
        ChangeSaleSnQuery query = new ChangeSaleSnQuery();
        query.setPsn("32143");
        query.setContractNumber("123");
        PageUtils<ChangeSaleSnVO> changeSaleSnVOPageUtils = productSnService.pageChangeSalesSn(query);
        System.out.println(changeSaleSnVOPageUtils);
    }


    @Resource
    private ContractProductSnMapper snMapper;
    @Test
    void pageChangeSaleSn1(){
        ChangeSaleSnQuery query = new ChangeSaleSnQuery();
        query.setProjectId("1");
        query.setType(2);
        List<ChangeSaleSnVO> changeSaleSnVOS = snMapper.queryChangeSalesSn(query);
    }

    @Test
    void exportContractReviewTabulateData(){
        Set processInstanceIds = Set.of("940e5508-2594-11f0-b6b0-0242ac12000d", "fd984830-2b0d-11f0-b6b0-0242ac12000d", "ff111ddc-2aee-11f0-b6b0-0242ac12000d", "ac06eecd-2a6e-11f0-b6b0-0242ac12000d");
        List list = contractReviewFlowService.exportContractReviewTabulateData(processInstanceIds);
        System.out.println(JSON.toJSONString(list, true));
    }

    @Test
    void testDelete(){
        contractReviewFlowService.deleteContract("940d1fada1eb59702173a8a71f427ff3");
    }

    @Test
    void updateProductNum(){
        List<ContractReviewMain> list = contractReviewMainService1.list(new LambdaQueryWrapper<ContractReviewMain>()
                .eq(ContractReviewMain::getDelFlag, false));
        list.forEach(item->{
            List<ContractProductOwnDTO> owns = null;
            try {
                owns = ownService.productInfoByContractId(item.getId(), true);
            } catch (Exception e) {
                return;
            }
            if (owns != null) {
                owns.forEach(own -> {
                    ownService.update(new LambdaUpdateWrapper<ContractReviewProductOwn>()
                        .set(ContractReviewProductOwn::getProductNum, own.getProductNum())
                            .eq(ContractReviewProductOwn::getId, own.getId()));
                });
            }

            List<ContractProductThirdDTO> contractProductThirdDTOS;
            try {
                contractProductThirdDTOS = contractReviewProductThirdService.productThirdInfoByContractId(item.getId(), true);
            } catch (Exception e) {
                return;
            }
            if (contractProductThirdDTOS != null) {
                contractProductThirdDTOS.forEach(third -> {
                    contractReviewProductThirdService.update(new LambdaUpdateWrapper<ContractReviewProductThird>()
                            .set(ContractReviewProductThird::getProductNum, third.getProductNum())
                            .eq(ContractReviewProductThird::getId, third.getId()));
                });
            }

        });
    }


    @Test
    void getContractReviewBySalesAgreementId(){
        contractReviewMainService1.getContractReviewBySalesAgreementId("3223d4a44bf5a1dfbee483b88c282207");
    }

    @Test
    void checkingFill(){
        TfsTaskVo taskVo = JSON.parseObject("{\"currentNodeId\":\"contractReview_04\",\"targetAssignee\":[\"486de80f3d6c426ba1f18101c6f0e404\"],\"comment\":\"123\",\"targetActId\":\"sid-4A8F4D88-C102-4CDC-815B-56453A05CE10\",\"processInstanceId\":\"062d4287-45d7-11f0-b64a-0242ac12000d\",\"taskId\":\"b12c19c6-5c03-11f0-ae95-0242ac12000d\",\"type\":\"78\"}", TfsTaskVo.class);
        taskVo.setActionTypeEnum(ActionTypeEnum.BACK);
        Pair<Boolean, String> booleanStringPair =
                contractReviewProcessService.checkFilling(taskVo);
        System.out.println(booleanStringPair);
    }

    @Test
    void getProcessInfoByContractNumber(){
        //10220250500191
        List<CrmContractProcessInfoVO> processInfoByContractNumber = contractReviewFlowService.getProcessInfoByContractNumber("10220250500191");
        System.out.println(processInfoByContractNumber);

    }

    public static void main(String[] args) {
//        List<ContractReviewPaymentProvision> ps = new ArrayList<>();
//        ContractReviewPaymentProvision paymentProvision = new ContractReviewPaymentProvision();
//        paymentProvision.setDueDate(null);
//        ps.add(paymentProvision);
//        ContractReviewPaymentProvision paymentProvision1 = new ContractReviewPaymentProvision();
//        paymentProvision1.setDueDate(null);
//        ps.add(paymentProvision1);
//
//        ContractReviewPaymentProvision lastPayment = ps.stream().filter(p -> p.getDueDate() != null).max(Comparator.comparing(ContractReviewPaymentProvision::getDueDate)).orElse(null);
//        System.out.println(lastPayment);
        System.out.println(null instanceof String);
    }

    @Test
    void getByContractNumber(){
        List<ContractReviewMainBaseInfoDTO> byContractNumber = contractReviewMainService1.getByContractNumber("13020250600022");
        System.out.println(byContractNumber);
    }

    @Resource
    private ISealApplicationContractReviewService iSealApplicationContractReviewService;

    @Test
    void t123(){
        boolean b = iSealApplicationContractReviewService.QueryExistsSealApplicationProcessState2ByProcessParentInstanceId("87be2091-4d97-11f0-8d4a-0242ac12000d");
        System.out.println(b);
    }

    @Test
    void querySnInContract(){
        Map<String, Boolean> map = contractReviewProductOwnService.querySnInContract(List.of("Q000000000400", "123"));
        System.out.println(map);
    }


}
