package com.topsec.crm.flow.core;

import com.topsec.crm.flow.api.dto.costFiling.CostFilingFlowLaunchDTO;
import com.topsec.crm.flow.core.service.ICostFilingService;
import com.topsec.tbsapi.client.TbsDictClient;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tbscommon.vo.DictItemVO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

@SpringBootTest
@ActiveProfiles({"dev","feignDev","redisDev","configDev"})
public class SealApplicationTests {
    @Resource
    private TbsDictClient tbsDictClient;
    @Resource
    private ICostFilingService iCostFilingService;

    @Test
    public void test1(){
        JsonObject<List<DictItemVO>> object =  tbsDictClient.listDictItemByDictType("matter");
        if (object.isSuccess()){
            List<DictItemVO> dictItemVOS = object.getObjEntity();
            System.out.println(dictItemVOS.size());
        }
        CostFilingFlowLaunchDTO costFilingFlowLaunchDTO = iCostFilingService.autoGenerateCostFiling("dfbc4951-3764-415e-af77-c14321756e04","TX20250612000002","10220250600195");
        String a = costFilingFlowLaunchDTO.getCostFilingBeanInfoLaunchDTO().getCreateUser();
        System.out.println(a);
        System.out.println("A");

    }
//    @Test
    public void test2(){

    }

}
