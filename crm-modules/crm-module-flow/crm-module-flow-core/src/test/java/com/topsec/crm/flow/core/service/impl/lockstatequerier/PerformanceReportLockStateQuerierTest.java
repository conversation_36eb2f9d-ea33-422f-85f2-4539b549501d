package com.topsec.crm.flow.core.service.impl.lockstatequerier;

import com.topsec.crm.flow.core.FlowApplicationTests;
import org.junit.jupiter.api.Test;

import jakarta.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
class PerformanceReportLockStateQuerierTest extends FlowApplicationTests {

    @Resource
    private PerformanceReportLockStateQuerier performanceReportLockStateQuerier;

    @Test
    void isProductLocked() {
        // 0f56efd8-246d-4a9f-9160-fc806d4fb74b
        // 12313123

        Boolean productLocked = performanceReportLockStateQuerier.isProductLocked("0f56efd8-246d-4a9f-9160-fc806d4fb74b");
        System.out.println(productLocked);
        productLocked = performanceReportLockStateQuerier.isProductLocked("12313123");
        System.out.println(productLocked);
        productLocked = performanceReportLockStateQuerier.isProductLocked("123131231");
        System.out.println(productLocked);
    }
}
