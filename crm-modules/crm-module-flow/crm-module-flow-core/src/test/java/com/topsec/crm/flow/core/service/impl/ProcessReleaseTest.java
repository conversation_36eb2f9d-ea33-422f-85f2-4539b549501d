package com.topsec.crm.flow.core.service.impl;

import com.topsec.crm.flow.api.vo.ProcessReleaseVO;
import com.topsec.crm.flow.core.service.ProcessReleaseService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertTrue;


@SpringBootTest
@ActiveProfiles({"dev","feignDev","redisDev","configDev"})
public class ProcessReleaseTest {

    @Autowired
    private ProcessReleaseService processReleaseService;

    @Test
    public void testSaveReturnRelease() {
        ProcessReleaseVO req = new ProcessReleaseVO();
        req.setProcessInstanceId("123456789");
        req.setProcessNumber("P001");
        req.setProcessName("测试流程");
        req.setReleaseBeginTime(LocalDateTime.now());
        req.setReleaseEndTime(LocalDateTime.now().plusDays(7));
        assertTrue(processReleaseService.saveReturnRelease(req));
    }

}
