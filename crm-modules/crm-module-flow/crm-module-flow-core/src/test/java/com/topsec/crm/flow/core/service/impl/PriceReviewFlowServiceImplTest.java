package com.topsec.crm.flow.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Sets;
import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.api.dto.pricereview.ProductOwnServiceRangeVO;
import com.topsec.crm.flow.core.FlowApplicationTests;
import com.topsec.crm.flow.core.entity.PriceReviewProductOwn;
import com.topsec.crm.flow.core.entity.PriceReviewSaleAgreementRel;
import com.topsec.crm.flow.core.mapper.PriceReviewSaleAgreementRelMapper;
import com.topsec.crm.flow.core.mapper.ProcessExtensionInfoMapper;
import com.topsec.crm.flow.core.service.PriceReviewFlowService;
import com.topsec.crm.flow.core.service.PriceReviewProductOwnService;
import com.topsec.crm.flow.core.service.impl.lockstatequerier.PerformanceReportLockStateQuerier;
import com.topsec.crm.flow.core.util.InnerProductOwnQuery;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */

class PriceReviewFlowServiceImplTest extends FlowApplicationTests {

    @Resource
    private PriceReviewFlowService priceReviewFlowService;

    @Resource
    private PerformanceReportLockStateQuerier performanceReportLockStateQuerier;

    @Resource
    private FlowServiceImpl flowService;
    @Resource
    private PriceReviewProductOwnService priceReviewProductOwnService;
    @Resource
    private ProcessExtensionInfoMapper processExtensionInfoMapper;
    @Resource
    private PriceReviewSaleAgreementRelMapper priceReviewSaleAgreementRelMapper;

    @Test
    void queryProjectProductReportStatus() {
        performanceReportLockStateQuerier.query("5a4090c0-cc73-427e-b998-ba4433cf8f02", Sets.newHashSet("0d946b8a-6de9-40a3-bad4-e054b101a51d", "2", "06580de1-d8e7-44a7-9c0a-d65df88efbe3"));
    }

    @Test
    void queryCurrentProjectDetail() {
        priceReviewFlowService.queryProjectDetail("123");
    }


    @Test
    void queryCenterLeaderApproverList() {
        // 62c8f72ea5354d8e8cc36c489f0247da 刘霏
        System.out.println(priceReviewFlowService.queryCenterLeaderApproverList("a124c8df-7958-11ef-97ec-0242ac12000d", "62c8f72ea5354d8e8cc36c489f0247da", true));
    }

    @Test
    void allStandardDiscountProduct() {
        priceReviewFlowService.allStandardDiscountProduct("d342c859-7a56-11ef-97ec-0242ac12000d");
    }

    @Test
    void needPreConfirmation() {
    }


    @Test
    void needToAddTemporaryCosts() {
    }

    @Test
    void needToAddProcurementInfo() {
    }

    @Test
    void needPresidentApproval() {
    }

    @Test
    void needMiddlemanApproval() {
        priceReviewFlowService.needMiddlemanApproval("4c1afe50-7be1-11ef-97ec-0242ac12000d");
    }

    @Test
    void isOneDotOnePercentDiscountOrLess() {
        priceReviewFlowService.isOnePointOneDiscountOrLess("4c1afe50-7be1-11ef-97ec-0242ac12000d");
    }

    @Test
    void queryKeyIndustryApprover() {
        InnerProductOwnQuery query = new InnerProductOwnQuery("fe660678-2f38-40db-b3c6-f1b12480184d");
        query.setIncludeSellinPrice(true);
        List<PriceReviewProductOwnDTO> ownList = priceReviewProductOwnService.queryProjectOwnByProjectId(query);
        priceReviewFlowService.needKeyIndustryApproval(ownList, null, "f3b0131315a3498280e85328731abd3d");
    }

    @Test
    void queryLockState() {
        flowService.queryLockState("a6276478-a695-4d4e-940c-24c50f29dabb", Sets.newHashSet("131680b7-4c0f-469b-aac7-b752029fdc20"));
    }

    @Test
    void globalCollectionConvert() {
        PriceReviewProductOwn priceReviewProductOwn = new PriceReviewProductOwn();
        priceReviewProductOwn.setFinalPrice(new BigDecimal("1"));
        priceReviewProductOwn.setProcessInstanceId("test");
        priceReviewProductOwn.setRecordId("fff");
        priceReviewProductOwn.setProductNum(1);
        ProductOwnServiceRangeVO productOwnServiceRangeVO = new ProductOwnServiceRangeVO();
        productOwnServiceRangeVO.setRecordId("fff");
        priceReviewProductOwn.setServiceRange(List.of(productOwnServiceRangeVO));
        priceReviewProductOwn.setId("fdsgfdgdfg1");
        boolean save = priceReviewProductOwnService.save(priceReviewProductOwn);

        PriceReviewProductOwn priceReviewProductOwn1 = priceReviewProductOwnService.getById("fdsgfdgdfg1");
        System.out.println(priceReviewProductOwn1.getServiceRange().get(0).getRecordId());
    }

    @Test
    void saleAgreementSkipCenterLeaderApproval() {
        String projectId = "d13f1aa5-1264-4766-b40c-41f548b02b18";
        List<PriceReviewProductOwnDTO> ownDTOS = priceReviewProductOwnService.queryProjectOwnByProjectId(new InnerProductOwnQuery(projectId));
        List<String> saleAgreeIds = priceReviewSaleAgreementRelMapper.selectList(new LambdaQueryWrapper<PriceReviewSaleAgreementRel>()
                        .eq(PriceReviewSaleAgreementRel::getPriceReviewProcessInstanceId, "a1fee9d4-678e-11f0-966d-0242ac12000d"))
                .stream().map(PriceReviewSaleAgreementRel::getSaleAgreementId).toList();
        priceReviewFlowService.saleAgreementSkipCenterLeaderApproval(ownDTOS, saleAgreeIds);
    }
}
