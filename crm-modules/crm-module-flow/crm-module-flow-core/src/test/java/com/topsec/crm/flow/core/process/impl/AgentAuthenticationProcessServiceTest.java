package com.topsec.crm.flow.core.process.impl;

import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 */
@SpringBootTest
class AgentAuthenticationProcessServiceTest {
    @Resource
    private AgentAuthenticationProcessService agentAuthenticationProcessService;
    @Test
    void saveAgentAuthenticationArea() {
        agentAuthenticationProcessService.saveAgentAuthenticationArea("104a4454-0ae0-11f0-841a-0242ac12000d","9dd39e7ed33f32da368292f1c264af67");



    }
}
