package com.topsec.crm.flow.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.CrmContractProductOwnVO;
import com.topsec.crm.flow.api.dto.contractreview.sninfo.ContractProductSnVO;
import com.topsec.crm.flow.api.dto.returnexchange.ContractReviewReturnExchangeLaunchDTO;
import com.topsec.crm.flow.api.dto.returnexchange.ReturnExchangeProductVO;
import com.topsec.crm.flow.core.FlowApplicationTests;
import com.topsec.crm.flow.core.entity.ProductLockState;
import com.topsec.crm.flow.core.entity.ReturnExchangeProduct;
import com.topsec.crm.flow.core.process.impl.ContractReviewReturnExchangeProcessService;
import com.topsec.crm.flow.core.service.ContractReviewReturnExchangeService;
import com.topsec.crm.framework.common.bean.CrmProjectProductSnVO;
import com.topsec.crm.framework.common.exception.CrmException;
import com.topsec.vo.FlowStateInfoVo;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.MapUtils;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class ContractReturnExchangeTest extends FlowApplicationTests {

    @Resource
    private ContractReviewReturnExchangeService reviewReturnExchangeService;

    @Resource
    private ContractReviewReturnExchangeProcessService contractReviewReturnExchangeProcessService;

    @Test
    void queryContractProductOwnList(){
        List<ReturnExchangeProductVO> returnExchangeProductVOS = reviewReturnExchangeService.queryContractProductOwnList("23520250600037");
        System.out.println(returnExchangeProductVOS);
    }

    @Test
    void handleProcessingTest(){
//        String message = "{\"processInstanceId\":\"9b84a228-5e0a-11f0-ae95-0242ac12000d\",\"processSt?ate\":1,\"processDefinitionKey\":\"reviewReturnAndExchange\",\"actionTypeEnum\":\"APPROVAL\",\"currentActivityId\":\"reviewReturnAndExchange_03\",\"targetActivityId\":null,\"processNumber\":\"HTPSTHH2025071100008\",\"comment\":null,\"changeTime\":\"2025-07-12T16:21:21.716\"}";
        String message = "{\"processInstanceId\":\"4d24b743-63a9-11f0-947c-0242ac12000a\",\"processState\":1,\"processDefinitionKey\":\"reviewReturnAndExchange\",\"actionTypeEnum\":\"APPROVAL\",\"currentActivityId\":\"reviewReturnAndExchange_03\",\"targetActivityId\":null,\"processNumber\":\"HTPSTHH2025071800001\",\"comment\":null,\"changeTime\":\"2025-07-18T15:41:41.873\"}";
        FlowStateInfoVo flowStateInfoVo = JSON.parseObject(message, FlowStateInfoVo.class);
        flowStateInfoVo.setTargetActivityId("reviewReturnAndExchange_04");
        contractReviewReturnExchangeProcessService.handleProcessing(flowStateInfoVo);
    }

    @Test
    void handleProcessBackTest(){
        String message = "{\"processInstanceId\":\"9b84a228-5e0a-11f0-ae95-0242ac12000d\",\"processState\":1,\"processDefinitionKey\":\"reviewReturnAndExchange\",\"actionTypeEnum\":\"BACK\",\"currentActivityId\":\"reviewReturnAndExchange_04\",\"targetActivityId\":\"reviewReturnAndExchange_03\",\"processNumber\":\"HTPSTHH2025071100008\",\"comment\":null,\"changeTime\":\"2025-07-14T09:54:53.465\"}";
        FlowStateInfoVo flowStateInfoVo = JSON.parseObject(message, FlowStateInfoVo.class);
        contractReviewReturnExchangeProcessService.handleProcessingBack(flowStateInfoVo);
    }

    @Test
    void query(){
        Map<String, ProductLockState> query = contractReviewReturnExchangeProcessService.query("a8dbf65d-98c9-4c8c-8e4f-c0a903fa8bc9", Set.of("a1532ea6-11ff-45d8-b7d6-5811f237cb25", "123"));
        System.out.println(query);
    }

    @Resource
    private RemoteContractExecuteService contractExecuteService;


    @Test
    void checkSn(){
        String json = "{\"assigneeList\":[\"72cda494800f41eeb71c930019da99fc\"],\"contractNumber\":\"10320250400157\",\"projectId\":\"5b9a66ef-eeca-4224-a134-5c3fe5ca2950\",\"type\":3,\"returnReason\":12,\"trueFault\":false,\"productFault\":false,\"damageOutOfBox\":false,\"signAgreement\":false,\"bugId\":null,\"faultPhenomenon\":null,\"applyReason\":\"的发送方师傅的发送方师傅的发送方师傅的发送方师傅\",\"reProduct\":[{\"id\":null,\"parentId\":null,\"recordId\":\"bf1082b9-67db-4746-a451-69d0ecdb8735\",\"productId\":\"0548847b2c700b31a13de8a8753a8f48\",\"productName\":\"天融信VPN系统V6\",\"stuffCode\":\"4102016383601\",\"productCategory\":\"TopVPN 6000\",\"pnCode\":\"TV-63836\",\"warehouseDelivery\":null,\"productNum\":1,\"oldProductNum\":1,\"returnNum\":0,\"productLic\":0,\"quotedPrice\":200,\"quotedTotalPrice\":200,\"dealPrice\":200,\"dealTotalPrice\":200,\"finalPrice\":200,\"finalTotalPrice\":200,\"invoicedAmount\":null,\"taxRate\":0.13,\"discount\":null,\"fees\":null,\"psn\":[{\"id\":null,\"recordId\":\"bf1082b9-67db-4746-a451-69d0ecdb8735\",\"stuffCode\":null,\"psn\":\"QSZ00000037\",\"deviceId\":null,\"deviceStuffCode\":null,\"type\":\"生产罐装\",\"extType\":null,\"delFlag\":null,\"tags\":null}],\"createUser\":null,\"createUserName\":null,\"createTime\":null,\"updateUser\":null,\"updateUserName\":null,\"updateTime\":null,\"customizedType\":null,\"child\":null,\"crmProjectProductOwnService\":null,\"crmProjectProductOwnServiceRange\":null,\"productPeriod\":null,\"productPeriodStart\":\"2025-04-19\",\"productPeriodEnd\":\"2026-04-16\",\"shipper\":null,\"psnString\":\"QSZ00000037\"}],\"newProduct\":[],\"attachments\":[{\"docId\":\"24ea5ea4-2e29-427b-bb66-3d8ae4149f5c\",\"fileType\":null,\"remark\":\"22\",\"extendInfo\":\"{\\\"docId\\\":\\\"24ea5ea4-2e29-427b-bb66-3d8ae4149f5c\\\",\\\"docName\\\":\\\"特殊公司信息.xlsx\\\",\\\"duplicate\\\":1,\\\"copies\\\":2,\\\"firstCopies\\\":1,\\\"secondCopies\\\":1,\\\"contractType\\\":\\\"789b8789b86d2356e23f5ab99a1091c9\\\",\\\"remark\\\":\\\"22\\\"}\",\"fileTypeImg\":\"assets/file-icons/elx.png\",\"docName\":\"特殊公司信息.xlsx\",\"docSha256\":null,\"watchUrl\":null,\"docConvert\":null,\"localFilePath\":null,\"fid\":\"24ea5ea4-2e29-427b-bb66-3d8ae4149f5c\",\"name\":\"特殊公司信息\",\"size\":\"11.74KB\",\"type\":\"xlsx\",\"url\":\"/nfs-data/crm/fsm/sourcenull/c69d56dc-dbd6-46e4-b212-c0aa12520e14.xlsx\",\"status\":null,\"createdId\":\"********************************\",\"createdName\":\"张玉卓10092\",\"ctime\":\"2025-07-21T17:09:08.000+08:00\",\"updatedId\":null,\"updatedName\":null,\"utime\":null,\"version\":null,\"duplicate\":1,\"copies\":2,\"firstCopies\":1,\"secondCopies\":1,\"contractType\":\"789b8789b86d2356e23f5ab99a1091c9\"}],\"paymentClause\":[],\"revenueRecognition\":[],\"contractDeliveryList\":[],\"reThirdProduct\":[],\"newThirdProduct\":[]}";
        ContractReviewReturnExchangeLaunchDTO contractReviewReturnExchangeLaunchDTO = JSON.parseObject(json, ContractReviewReturnExchangeLaunchDTO.class);
        List<ReturnExchangeProductVO> reProduct = contractReviewReturnExchangeLaunchDTO.getReProduct();
        List<CrmContractProductOwnVO> ownVOS = contractExecuteService.getProductOwnByContractNumber("10320250400157").getObjEntity();
        Map<String, List<ContractProductSnVO>> sn = contractExecuteService.getSnMapByContractNumber("10320250400157").getObjEntity();
        Set<String> snInContract = MapUtils.emptyIfNull(sn).values().stream().flatMap(List::stream).map(ContractProductSnVO::getSn).collect(Collectors.toSet());
        Set<String> ownInContract = ownVOS.stream().map(CrmContractProductOwnVO::getProjectProductOwnId).collect(Collectors.toSet());
        // 判断前端传过来的旧产品和序列号
        Set<String> ownInWeb = reProduct.stream().map(ReturnExchangeProductVO::getRecordId).collect(Collectors.toSet());
        Set<String> snInWeb = reProduct.stream().map(ReturnExchangeProductVO::getPsn).flatMap(List::stream).map(CrmProjectProductSnVO::getPsn).collect(Collectors.toSet());
        if (!snInContract.equals(snInWeb) || !ownInContract.equals(ownInWeb)) {
            throw new CrmException("合同产品与产品序列号不匹配");
        }
    }
}
