package com.topsec.crm.flow.core;

import com.topsec.crm.flow.api.dto.renew.RenewVO;
import com.topsec.crm.flow.core.process.impl.*;
import com.topsec.crm.flow.core.service.BorrowForProbationRenewService;
import com.topsec.vo.FlowStateInfoVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest
@ActiveProfiles({"dev","feignDev","redisDev","configDev"})
//@ActiveProfiles({"test","feignTest","redisTest","configTest"})
public class FlowApplicationTests {
    @Autowired
    private ProjectRadioProcessService projectRadioProcessService;

    @Autowired
    private ContractBadDebtProcessService contractBadDebtProcessService;

    @Autowired
    private CustomerNaApproveProcessService customerNaApproveProcessService;

    @Autowired
    private BorrowForProbationRenewProcessService borrowForProbationRenewProcessService;

    @Autowired
    private BorrowForProbationRenewService borrowForProbationRenewService;
    @Autowired
    private ContractSignVerifyProcessService contractSignVerifyProcessService;

    @Autowired
    private MaterialApplyProcessService materialApplyProcessService;
    @Autowired
    private TargetedInventoryPreparationQuashProcessService targetedInventoryPreparationQuashProcessService;
    @Autowired
    private ContractUrgeSignVerifySonProcessService contractUrgeSignVerifySonProcessService;

    @Test
    void handleTest(){
        FlowStateInfoVo processInfo = new FlowStateInfoVo();
        processInfo.setProcessInstanceId("c0fa906d-5d3d-11f0-ae95-0242ac12000d");
        contractUrgeSignVerifySonProcessService.handlePass(processInfo);
    }


    @Test
    void handleTest1(){
//        FlowStateInfoVo processInfo = new FlowStateInfoVo();
//        processInfo.setProcessInstanceId("83bd7040-fb15-11ef-a37d-0242ac12000d");
//        borrowForProbationRenewProcessService.handlePass(processInfo);
        RenewVO renewVO = borrowForProbationRenewService.renewalInfo("b8acd6ba-6920-11f0-b0e2-0242ac120008");
        System.out.println(renewVO);

    }

    @Test
    void handleTest2(){
        FlowStateInfoVo processInfo = new FlowStateInfoVo();
        processInfo.setProcessInstanceId("cc66741d-21bc-11f0-93c2-0242ac12000a");
        targetedInventoryPreparationQuashProcessService.handlePass(processInfo);
    }




}
