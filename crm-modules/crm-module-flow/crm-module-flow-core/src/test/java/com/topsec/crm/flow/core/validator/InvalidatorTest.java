package com.topsec.crm.flow.core.validator;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
class InvalidatorTest {


    @SneakyThrows
    @Test
    void checkTest() {
        CompletableFuture<String> completableFuture = CompletableFuture.supplyAsync(() -> {
            try {
                Thread.sleep(1000L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            return "dd";
        });
        completableFuture.whenComplete((s, throwable) -> {
            if (throwable != null) {
                System.out.println("got exception: " + throwable.getClass() + ", " + throwable.getCause());
                return ;
            }
            System.out.println("got result: " + s);
        });

        completableFuture.cancel(true);

        Thread.sleep(3000L);
    }


    @Test
    void check() {
        CheckCondition<Void> c1 = o -> {
            // if (true) {
            //     throw new CrmException("c11111");
            // }
            return true;
        };
        CheckCondition<Void> c2 = o -> {
            // try {
            //     Thread.sleep(1000L);
            // } catch (InterruptedException e) {
            //     throw new RuntimeException(e);
            // }
            // if (true) {
            //     throw new CrmException("c22222");
            // }
            return true;
        };
        CheckCondition<Void> c3 = o -> {
            return true;
        };
        List<CheckCondition<Void>> all = List.of(c1, c2, c3);

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("PARALLEL_FAIL_FAST");
        HyperValidator<Void> check = HyperValidator.check(null, all, HyperValidator.CHECK_MODE.PARALLEL_FAIL_FAST);
        System.out.println(check.getResult());
        System.out.println(check.getFailMessage());
        stopWatch.stop();

        stopWatch.start("PARALLEL_FULL");
        check = HyperValidator.check(null, all, HyperValidator.CHECK_MODE.PARALLEL_FULL);
        System.out.println(check.getResult());
        System.out.println(check.getFailMessage());
        stopWatch.stop();
        System.out.println(stopWatch.prettyPrint());
    }
}
