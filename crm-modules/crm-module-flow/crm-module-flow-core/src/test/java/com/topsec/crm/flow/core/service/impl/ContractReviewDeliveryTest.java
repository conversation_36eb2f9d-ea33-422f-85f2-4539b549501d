package com.topsec.crm.flow.core.service.impl;

import com.topsec.crm.flow.api.dto.performancereport.ContractDeliveryProductVO;
import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportContractDeliveryDTO;
import com.topsec.crm.flow.api.dto.performancereport.ScarceGoodsOrderDeliveryProductDTO;
import com.topsec.crm.flow.core.service.ContractReviewDeliveryService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
@ActiveProfiles({"dev","feignDev","redisDev","configDev"})
public class ContractReviewDeliveryTest{

    @Resource
    private ContractReviewDeliveryService contractReviewDeliveryService;

    @Test
    public void autoOrderDeliverySaveTest(){
        ArrayList<ScarceGoodsOrderDeliveryProductDTO> deliveryProductDTOS = new ArrayList<>();

        // 产品1-1877602162616946689 发货地点1-85fb386303ea873b0b53c68d2f62ce02
        ScarceGoodsOrderDeliveryProductDTO productOne = new ScarceGoodsOrderDeliveryProductDTO();
        productOne.setProjectDynastyId("1877602162616946689");
        productOne.setDeliveryQuantity(1);
        productOne.setDeliveryRecordId("85fb386303ea873b0b53c68d2f62ce02");

        // 产品1 发货地点2
        ScarceGoodsOrderDeliveryProductDTO productTwo = new ScarceGoodsOrderDeliveryProductDTO();
        productTwo.setProjectDynastyId("1877602162616946689");
        productTwo.setDeliveryQuantity(5);
        productTwo.setDeliveryRecordId("adff415a093c1511e37cc1f56a646ad1");

        // 产品2- 发货地点1
        ScarceGoodsOrderDeliveryProductDTO productThree = new ScarceGoodsOrderDeliveryProductDTO();
        productThree.setProjectDynastyId("1877602162612752386");
        productThree.setDeliveryQuantity(1);
        productThree.setDeliveryRecordId("85fb386303ea873b0b53c68d2f62ce02");

        // 产品2 发货地点2
        ScarceGoodsOrderDeliveryProductDTO productFour = new ScarceGoodsOrderDeliveryProductDTO();
        productFour.setProjectDynastyId("1877602162612752386");
        productFour.setDeliveryQuantity(6);
        productFour.setDeliveryRecordId("adff415a093c1511e37cc1f56a646ad1");

        deliveryProductDTOS.add(productOne);
        deliveryProductDTOS.add(productTwo);
        deliveryProductDTOS.add(productThree);
        deliveryProductDTOS.add(productFour);

        contractReviewDeliveryService.autoOrderDeliverySave(
                "92341aa8080d27efb099fb4ecd668123",
                "f61a36fa-ce3b-11ef-9afe-0242ac12000c",
                deliveryProductDTOS);
    }

    @Test
    public void deleteByProductIdsTest(){
        contractReviewDeliveryService.deleteByProductIds(List.of("f8a185259b13712e1674e5fef2cb5b43"),null);
    }

    @Test
    public void returnChangeLaunchSaveTest(){

        ArrayList<PerformanceReportContractDeliveryDTO> deliveryDTOS = new ArrayList<>();
        String returnChangeId = "111";

        PerformanceReportContractDeliveryDTO deliveryDTO1 = new PerformanceReportContractDeliveryDTO();
        deliveryDTO1.setDeliveryDate(LocalDate.now());
        deliveryDTO1.setConsigneeAddress("上海");
        deliveryDTO1.setConsigneeName("张三");
        deliveryDTO1.setConsigneePhone("12345678901");
        deliveryDTO1.setConsigneeEmail("<EMAIL>");
        deliveryDTO1.setConsigneeId("12345678901");
        deliveryDTO1.setDeliveryLeadTime(1);
        deliveryDTO1.setDeliveryMethod(1);
        deliveryDTO1.setIsContractWaived(0);
        deliveryDTO1.setConsigneeAreaCode("310000");
        deliveryDTO1.setOriginalContractNo("12345678901");
        deliveryDTO1.setRemarks("无");

        ArrayList<ContractDeliveryProductVO> productVOS = new ArrayList<>();

        ContractDeliveryProductVO productVO1 = new ContractDeliveryProductVO();
        productVO1.setNum(1);
        productVO1.setProjectProductRecordId("11111");
        productVO1.setProductType("1");
        productVOS.add(productVO1);

        ContractDeliveryProductVO productVO2 = new ContractDeliveryProductVO();
        productVO2.setNum(1);
        productVO2.setProjectProductRecordId("22222");
        productVO2.setProductType("2");
        productVOS.add(productVO2);

        deliveryDTO1.setDeliveryProducts(productVOS);

        PerformanceReportContractDeliveryDTO deliveryDTO2 = new PerformanceReportContractDeliveryDTO();
        deliveryDTO2.setDeliveryDate(LocalDate.now());
        deliveryDTO2.setConsigneeAddress("上海");
        deliveryDTO2.setConsigneeName("张三");
        deliveryDTO2.setConsigneePhone("12345678901");
        deliveryDTO2.setConsigneeEmail("<EMAIL>");
        deliveryDTO2.setConsigneeId("12345678901");
        deliveryDTO2.setDeliveryLeadTime(1);
        deliveryDTO2.setDeliveryMethod(1);
        deliveryDTO2.setIsContractWaived(0);
        deliveryDTO2.setOriginalContractNo("12345678901");
        deliveryDTO2.setRemarks("无");
        deliveryDTO2.setDeliveryProducts(productVOS);

        deliveryDTOS.add(deliveryDTO1);
        deliveryDTOS.add(deliveryDTO2);
        contractReviewDeliveryService.returnChangeLaunchSave(deliveryDTOS,returnChangeId);
    }

}
