package com.topsec.crm.flow.core.service.impl;

import com.topsec.crm.contract.api.RemoteContractExecuteService;
import com.topsec.crm.contract.api.entity.contractexecute.CrmContractExecuteVO;
import com.topsec.crm.flow.api.dto.collectionhandle.CollectionLettersHandleDTO;
import com.topsec.crm.flow.api.dto.contractreceivable.ContractDebtDTO;
import com.topsec.crm.flow.api.dto.legalAffairsMain.VO.LegalAffairsMainInfoVO;
import com.topsec.crm.flow.core.FlowApplicationTests;
import com.topsec.crm.flow.core.entity.ContractCollectionLetterMain;
import com.topsec.crm.flow.core.entity.ContractDebt;
import com.topsec.crm.flow.core.entity.ContractReceivableDetail;
import com.topsec.crm.flow.core.entity.ContractReceivableFollow;
import com.topsec.crm.flow.core.process.ProcessTypeEnum;
import com.topsec.crm.flow.core.process.impl.ContractReceivableMainProcessService;
import com.topsec.crm.flow.core.service.*;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.framework.common.constant.ReceivableConstants;
import com.topsec.crm.framework.common.enums.ReceivableEnum;
import com.topsec.crm.framework.common.util.AccountAccquireUtils;
import com.topsec.tbscommon.JsonObject;
import com.topsec.tfs.api.client.TfsNodeClient;
import com.topsec.tos.api.client.TosDepartmentClient;
import com.topsec.tos.api.client.TosEmployeeClient;
import com.topsec.tos.common.HyperBeanUtils;
import com.topsec.tos.common.constants.TosConstants;
import com.topsec.tos.common.vo.EmployeeVO;
import com.topsec.tos.common.vo.TosDepartmentVO;
import com.topsec.vo.FlowStateInfoVo;
import com.topsec.vo.node.ApproveNode;
import org.apache.commons.collections4.ListUtils;
import org.junit.jupiter.api.Test;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2024/12/18 15:55
 */
public class ContractReceivableServiceTest extends FlowApplicationTests {

    @Resource
    private ContractReceivableService contractReceivableService;

    @Test
    public void generateReceivable() {
        contractReceivableService.generateReceivable();
    }


    @Resource
    private TosDepartmentClient tosDepartmentClient;
    @Test
    void tosTest(){
        JsonObject<TosDepartmentVO> byId = tosDepartmentClient.findById("365");
        System.out.println(byId);
    }

    @Resource
    private ContractReceivableDetailService contractReceivableDetailService;
    @Resource
    private ContractReceivableFollowService followService;
    @Resource
    private RemoteContractExecuteService remoteContractExecuteService;
    @Resource
    private ContractReceivableMainProcessService processService;

    @Test
    public void generateReceivableChild() {
        List<ContractReceivableDetail> contractReceivableDetails = contractReceivableDetailService.getByReceivableMainId("6c96b175eafa36236a15fd7ecdff0429");
        contractReceivableService.generateReceivableChild("e897f636-be87-11ef-83d4-0242ac12000b", contractReceivableDetails);
    }

    @Test
    public void generateCollectionLetter(){
        FlowStateInfoVo flowInfo = new FlowStateInfoVo();
        flowInfo.setProcessInstanceId("7629e201-ef6b-11ef-ab60-0242ac12000a");
        flowInfo.setCurrentActivityId(ReceivableConstants.RECEIVABLE_MAIN_02);
        processService.handleProcessing(flowInfo);
    }

    @Test
    public void generateLegalAffairs(){
        FlowStateInfoVo flowInfo = new FlowStateInfoVo();
        flowInfo.setProcessInstanceId("dff9f0e7-cf05-11ef-999b-0242ac12000a");
        flowInfo.setCurrentActivityId(ReceivableConstants.RECEIVABLE_MAIN_02);
        processService.handleProcessing(flowInfo);
    }

    @Test
    public void handlePassBack(){
        contractReceivableService.syncReceivableMainFollowByChild("7345d2e7-be88-11ef-83d4-0242ac12000b");
    }

    @Test
    public void handlePass(){
        contractReceivableService.syncReceivableMainFollowByChild("7345d2e7-be88-11ef-83d4-0242ac12000b");
    }

    @Resource
    private TosEmployeeClient tosEmployeeClient;

    @Test
    void tosTest1(){
        JsonObject<List<EmployeeVO>> result = tosEmployeeClient.queryDeptChainPrincipalByLevel(null, "f3b0131315a3498280e85328731abd3d", 1, TosConstants.RelationType.LEADER);
        if (result.isSuccess()) {
            EmployeeVO employeeVO = result.getObjEntity().get(0);
            if (employeeVO != null) {
                String s = Optional.ofNullable(
                        AccountAccquireUtils.convertGetAccountSingle(String.valueOf(employeeVO.getUuid()))).map(FlowPerson::getAccountId).orElse("e762a850b8c8562ea9bc206a471fab70");
                System.out.println(s);
            }
        }
    }

    @Resource
    private RemoteContractExecuteService contractExecuteService;

    @Test
    void tfsTest2(){
        JsonObject<CrmContractExecuteVO> byContractNumber = contractExecuteService.getByContractNumber("**************");
        JsonObject<List<EmployeeVO>> result = tosEmployeeClient.queryDeptChainPrincipalByLevel(null, byContractNumber.getObjEntity().getContractOwnerId(), 1, TosConstants.RelationType.LEADER);
        if (result.isSuccess()) {
            EmployeeVO employeeVO = result.getObjEntity().get(0);
            System.out.println(employeeVO);
        }
    }

    @Resource
    private TfsNodeClient tfsNodeClient;
    @Test
    void tfsTest(){
        JsonObject<Set<ApproveNode>> setJsonObject = tfsNodeClient.queryNodeByProcessInstanceId("69b5712f-ee8e-11ef-8c71-0242ac12000a");
        System.out.println(setJsonObject);
    }

    @Test
    void tfsTest1(){
        JsonObject<List<String>> listJsonObject = tfsNodeClient.selectSpecifyNodeAssigneeList(ProcessTypeEnum.RECEIVABLE_COLLECTION.getProcessDefinitionKey().getValue(), ReceivableConstants.RECEIVABLE_MAIN_01);
        System.out.println(listJsonObject);
    }

    @Resource
    private ContractDebtService debtService;

    @Test
    void calcReceivableDetail(){
        ContractDebt byId = debtService.getById("6cde76733baa12af346e5d49ade1295e");
        remoteContractExecuteService.calcReceivableDetail(List.of(HyperBeanUtils.copyProperties(byId, ContractDebtDTO::new)));
    }


    @Test
    void test(){
        Set<String> contractNumbers = Set.of("12320241100013");
        // 取这批数据 是否发起过律师函、催款函
        Map<String, List<LegalAffairsMainInfoVO>> legalAffairsMainInfoVOMap = selectLegalAffairs(contractNumbers);
        // 发过催款函的
        Map<String, CollectionLettersHandleDTO> letterHandleDTOMap = selectCollectionLetters(contractNumbers);

        for(String contractNumber : contractNumbers) {
            // 计算已发的
            List<LegalAffairsMainInfoVO> legalAffairsMainInfoVO = legalAffairsMainInfoVOMap.get(contractNumber);
            // 律师函
            Optional<LegalAffairsMainInfoVO> first = ListUtils.emptyIfNull(legalAffairsMainInfoVO).stream().filter(item -> item.getCollectionSuggestion() == 0).findFirst();
            // 诉讼
            Optional<LegalAffairsMainInfoVO> second = ListUtils.emptyIfNull(legalAffairsMainInfoVO).stream().filter(item -> item.getCollectionSuggestion() == 1).findFirst();
            CollectionLettersHandleDTO handleDTO = letterHandleDTOMap.get(contractNumber);

            if (handleDTO != null && handleDTO.getLetterDate() != null) {
                // 已发催款函 最低优先级
//                saveReceivableFollow(detail, ReceivableEnum.ReceivableHandleSuggest.SEND_COLLECTION_LETTER, docs, promisePaymentDate);
//                saveFlag = true;
                System.out.println("已发催款函");
            }
            if (first.isPresent() && first.get().getSendLetterTime() != null && first.get().getCollectionSuggestion() == 0) {
                // 已发律师函 SEND_LAWYER_LETTER
//                saveReceivableFollow(detail, ReceivableEnum.ReceivableHandleSuggest.SEND_LAWYER_LETTER, docs, promisePaymentDate);
//                saveFlag = true;
                System.out.println("已发律师函");
            }
            if (second.isPresent() && second.get().getSendLetterTime() != null && second.get().getCollectionSuggestion() == 1) {
                // 已发诉讼 SEND_LAWSUIT
//                saveReceivableFollow(detail, ReceivableEnum.ReceivableHandleSuggest.SEND_LAWSUIT, docs, promisePaymentDate);
//                saveFlag = true;
                System.out.println("已发诉讼");
            }
        }
    }

    @Resource
    private LegalAffairsMainService legalAffairsMainService;

    private Map<String, List<LegalAffairsMainInfoVO>> selectLegalAffairs(Set<String> contractNumbers){
        List<LegalAffairsMainInfoVO> legalAffairsMainInfoVOS = legalAffairsMainService.getByContractNumberBatch(contractNumbers, true);
        return legalAffairsMainInfoVOS.stream()
                .collect(Collectors.groupingBy(LegalAffairsMainInfoVO::getContractNumber));
    }

    @Resource
    private ContractCollectionLettersHandleService contractCollectionLettersHandleService;
    @Resource
    private ContractCollectionLetterMainService contractCollectionLetterMainService;

    public Map<String, CollectionLettersHandleDTO> selectCollectionLetters(Set<String> contractNumbers){
        List<ContractCollectionLetterMain> contractCollectionLetterMains = contractCollectionLetterMainService.getByContractNumberBatch(contractNumbers);
        Map<String, ContractCollectionLetterMain> letterMainMap = contractCollectionLetterMains.stream()
                .collect(Collectors.toMap(ContractCollectionLetterMain::getProcessInstanceId, item -> item, (i1, i2) -> i2));

        Set<String> processInstanceIds = contractCollectionLetterMains.stream().map(ContractCollectionLetterMain::getProcessInstanceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<CollectionLettersHandleDTO> handleDTOS = contractCollectionLettersHandleService.getByProcessInstanceIdBatch(processInstanceIds);
        // key contractNumber value handle
        Map<String, CollectionLettersHandleDTO> handleDTOMap = new HashMap<>();
        handleDTOS.forEach(handleDTO -> {
            ContractCollectionLetterMain contractCollectionLetterMain = letterMainMap.get(handleDTO.getProcessInstanceId());
            handleDTOMap.put(contractCollectionLetterMain.getContractNumber(), handleDTO);
        });
        return handleDTOMap;
    }



}
