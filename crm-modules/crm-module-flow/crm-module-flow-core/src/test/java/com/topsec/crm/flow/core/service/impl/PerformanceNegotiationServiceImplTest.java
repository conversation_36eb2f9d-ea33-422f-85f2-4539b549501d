package com.topsec.crm.flow.core.service.impl;

import com.topsec.crm.flow.core.FlowApplicationTests;
import com.topsec.crm.flow.core.process.impl.PerformanceNegotiationProcessService;
import com.topsec.crm.framework.common.bean.FlowPerson;
import com.topsec.crm.project.api.client.RemotePerformanceNegotiationClient;
import com.topsec.crm.project.api.client.RemoteProjectMemberClient;
import com.topsec.vo.FlowStateInfoVo;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

@SpringBootTest
@ActiveProfiles({"dev","feignDev","redisDev","configDev"})
public class PerformanceNegotiationServiceImplTest extends FlowApplicationTests {

    @Resource
    private PerformanceNegotiationProcessService processService;

    @Resource
    private RemoteProjectMemberClient memberClient;

    @Resource
    private RemotePerformanceNegotiationClient performanceNegotiationClient;

    @Test
    void handlePass(){
        FlowStateInfoVo flowStateInfoVo = new FlowStateInfoVo();
        flowStateInfoVo.setProcessInstanceId("7ea6fb88-c748-11ef-99c6-0242ac12000c");
        flowStateInfoVo.setProcessNumber("YJXS2024123100001");
        processService.handlePass(flowStateInfoVo);
    }

    @Test
    public void getTest(){
        List<FlowPerson> objEntity = memberClient.getFlowPersonListByProjectId("25756c01-8518-436c-8170-65fea4c4e6a8").getObjEntity();
        FlowPerson objEntity1 = performanceNegotiationClient.getNegotiatorByProjectId("25756c01-8518-436c-8170-65fea4c4e6a8").getObjEntity();
        System.out.println(objEntity.size());
        System.out.println(objEntity1.getPersonName());
        for (FlowPerson flowPerson : objEntity) {
            System.out.println(flowPerson.getPersonName());
        }
    }
}
