package com.topsec.crm.flow.core.service.impl;

import com.topsec.crm.flow.api.dto.pricereview.PriceReviewProductOwnDTO;
import com.topsec.crm.flow.core.util.InnerProductOwnQuery;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@SpringBootTest
class PriceReviewSpecialCodeServiceImplTest {
    @Resource
    private PriceReviewSpecialCodeServiceImpl priceReviewSpecialCodeService;
    @Resource
    private PriceReviewProductOwnServiceImpl priceReviewProductOwnServiceImpl;
    @Test
    void mergeSpecialCodeApproverByGroup() {
        List<PriceReviewProductOwnDTO> ownDTOS = priceReviewProductOwnServiceImpl
                .queryProjectOwnByProjectId(new InnerProductOwnQuery("74663f38-a9ca-4408-8b95-2415bf7df68e"));
        Map<String, Set<String>> stringSetMap = priceReviewSpecialCodeService.mergeSpecialCodeApproverByGroup(ownDTOS, null, false);
        System.out.println(stringSetMap);
    }
}
