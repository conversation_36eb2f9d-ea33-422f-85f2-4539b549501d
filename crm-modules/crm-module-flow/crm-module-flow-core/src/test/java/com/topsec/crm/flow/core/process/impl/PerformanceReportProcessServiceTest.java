package com.topsec.crm.flow.core.process.impl;

import com.topsec.crm.flow.api.dto.performancereport.PerformanceReportFlowLaunchDTO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class PerformanceReportProcessServiceTest {
    @Resource
    private PerformanceReportProcessService performanceReportProcessService;

    @Test
    public void validateTipProduct() {
        PerformanceReportFlowLaunchDTO launchable = new PerformanceReportFlowLaunchDTO();
        List<PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO> productList =
                new ArrayList<>();
        PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO product =
                new PerformanceReportFlowLaunchDTO.PerformanceReportProductOwnLaunchDTO();
        product.setProjectRecordId("00813dc8-5729-46ef-b5af-2601bf49826d");
        productList.add(product);
        launchable.setProductList(productList);

        performanceReportProcessService.validateTipProduct(launchable);
    }
}
