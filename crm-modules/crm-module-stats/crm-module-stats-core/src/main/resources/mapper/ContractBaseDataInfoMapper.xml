<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.topsec.crm.stats.core.mapper.ContractBaseDataInfoMapper">

    <resultMap id="BaseResultMap" type="com.topsec.crm.stats.core.entity.ContractBaseDataInfo">
            <result property="id" column="id" />
            <result property="annual" column="annual" />
            <result property="contractNumber" column="contract_number" />
            <result property="contractDate" column="contract_date" />
            <result property="contractStatus" column="contract_status" />
            <result property="finalUserName" column="final_user_name" />
            <result property="industry1" column="industry_1" />
            <result property="industry2" column="industry_2" />
            <result property="industry3" column="industry_3" />
            <result property="region0" column="region_0" />
            <result property="region1" column="region_1" />
            <result property="region2" column="region_2" />
            <result property="region3" column="region_3" />
            <result property="signCompany" column="sign_company" />
            <result property="signUnit" column="sign_unit" />
            <result property="contractOwner" column="contract_owner" />
            <result property="contractOwnerId" column="contract_owner_id" />
            <result property="department1" column="department_1" />
            <result property="department2" column="department_2" />
            <result property="department3" column="department_3" />
            <result property="originalSalesUnit" column="original_sales_unit" />
            <result property="originalSalesPerson" column="original_sales_person" />
            <result property="industryRegion" column="industry_region" />
            <result property="materialCode" column="material_code" />
            <result property="productModel" column="product_model" />
            <result property="productPn" column="product_pn" />
            <result property="productLineCategory1" column="product_line_category_1" />
            <result property="productLineCategory2" column="product_line_category_2" />
            <result property="productLineCategory3" column="product_line_category_3" />
            <result property="productLineCategory4" column="product_line_category_4" />
            <result property="productLineAttribute1" column="product_line_attribute_1" />
            <result property="productLineAttribute2" column="product_line_attribute_2" />
            <result property="productCategory1" column="product_category_1" />
            <result property="productCategory2" column="product_category_2" />
            <result property="productCategory3" column="product_category_3" />
            <result property="productCategory4" column="product_category_4" />
            <result property="commerceCategory" column="commerce_category" />
            <result property="arrearsAmount" column="arrears_amount" />
            <result property="overdueReceivables" column="overdue_receivables" />
            <result property="newContract" column="new_contract" />
            <result property="newInvoice" column="new_invoice" />
            <result property="newPayment" column="new_payment" />
            <result property="carryoverAmount" column="carryover_amount" />
            <result property="carryoverContract" column="carryover_contract" />
            <result property="totalGrossProfit" column="total_gross_profit" />
            <result property="grossProfit" column="gross_profit" />
            <result property="carryoverGrossProfit" column="carryover_gross_profit" />
            <result property="carryoverOverdueReceivable" column="carryover_overdue_receivable" />
            <result property="newGrossProfit" column="new_gross_profit" />
            <result property="newOverdueReceivable" column="new_overdue_receivable" />
            <result property="contractAmount" column="contract_amount" />
            <result property="contractAmountFreeProduct" column="contract_amount_free_product" />
            <result property="grossProfitFreeProduct" column="gross_profit_free_product" />
            <result property="paymentContractAmount" column="payment_contract_amount" />
            <result property="directSalesNewContract" column="direct_sales_new_contract" />
            <result property="distributionNewContract" column="distribution_new_contract" />
            <result property="paymentGrossProfitUnit" column="payment_gross_profit_unit" />
            <result property="paymentGrossProfitPerson" column="payment_gross_profit_person" />
            <result property="performanceReportNumber" column="performance_report_number" />
            <result property="newReturnExchangeAmount" column="new_return_exchange_amount" />
            <result property="newReturnExchangeProfit" column="new_return_exchange_profit" />
            <result property="salesSettlementRatio" column="sales_settlement_ratio" />
            <result property="departmentSettlementRatio" column="department_settlement_ratio" />
            <result property="teamSettlementRatio" column="team_settlement_ratio" />
            <result property="newGrossProfitProductLine" column="new_gross_profit_product_line" />
            <result property="carryoverGrossProfitProductLine" column="carryover_gross_profit_product_line" />
            <result property="newReturnExchangeProfitProductLine" column="new_return_exchange_profit_product_line" />
            <result property="grossProfitFreeProductProductLine" column="gross_profit_free_product_product_line" />
            <result property="totalGrossProfitProductLine" column="total_gross_profit_product_line" />
            <result property="paymentGrossProfitProductLine" column="payment_gross_profit_product_line" />
            <result property="paymentGrossProfitUnitProductLine" column="payment_gross_profit_unit_product_line" />
            <result property="paymentGrossProfitPersonProductLine" column="payment_gross_profit_person_product_line" />
            <result property="cloudComputingFactor" column="cloud_computing_factor" />
            <result property="industrialControlFactor" column="industrial_control_factor" />
            <result property="businessType" column="business_type" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="createUser" column="create_user" />
            <result property="updateUser" column="update_user" />
            <result property="delFlag" column="del_flag" />
    </resultMap>

    <!-- ContractSummaryVO的resultMap -->
    <resultMap id="ContractSummaryResultMap" type="com.topsec.crm.stats.api.entity.business.ContractSummaryVO">
        <result property="signCompany" column="sign_company" />
        <result property="annual" column="annual" />
        <result property="platformName" column="platform_name" />
        <result property="companyIndustryDivisionName" column="company_industry_division_name" />
        <result property="regionalMarketingUnitName" column="regional_marketing_unit_name" />
        <result property="regionalIndustryDivisionName" column="regional_industry_division_name" />
        <result property="salesPersonName" column="sales_person_name" />
        <result property="salesPersonId" column="sales_person_id" />
        <result property="productLineCategory1" column="product_line_category_1" />
        <result property="productLineCategory2" column="product_line_category_2" />
        <result property="productLineCategory3" column="product_line_category_3" />
        <result property="taskAmount" column="task_amount" />
        <result property="newContractCompletionRate" column="new_contract_completion_rate" />
        <result property="paymentAmountCompletionRate" column="payment_amount_completion_rate" />
        <result property="carryoverContract" column="carryover_contract" />
        <result property="carryoverGrossProfit" column="carryover_gross_profit" />
        <result property="carryoverOverdueReceivable" column="carryover_overdue_receivable" />
        <result property="newContract" column="new_contract" />
        <result property="newGrossProfit" column="new_gross_profit" />
        <result property="newOverdueReceivable" column="new_overdue_receivable" />
        <result property="directSalesNewContract" column="direct_sales_new_contract" />
        <result property="distributionNewContract" column="distribution_new_contract" />
        <result property="newReturnExchangeAmount" column="new_return_exchange_amount" />
        <result property="newReturnExchangeProfit" column="new_return_exchange_profit" />
        <result property="contractAmount" column="contract_amount" />
        <result property="totalGrossProfit" column="total_gross_profit" />
        <result property="totalGrossMargin" column="total_gross_margin" />
        <result property="overdueReceivables" column="overdue_receivables" />
        <result property="contractAmountFreeProduct" column="contract_amount_free_product" />
        <result property="grossProfitFreeProduct" column="gross_profit_free_product" />
        <result property="grossMarginFreeProduct" column="gross_margin_free_product" />
        <result property="paymentAmount" column="payment_amount" />
        <result property="paymentRate" column="payment_rate" />
        <result property="paymentGrossProfit" column="payment_gross_profit" />
        <result property="arrearsAmount" column="arrears_amount" />
        <result property="newInvoice" column="new_invoice" />
    </resultMap>

    <!-- 字段名映射SQL片段 - 仅支持指定的25个经营数据字段排序 -->
    <sql id="fieldMapping">
        <choose>
            <!-- 任务和完成率相关字段 -->
            <when test="fieldName == 'taskAmount'">task_amount</when>
            <when test="fieldName == 'newContractCompletionRate'">new_contract_completion_rate</when>
            <when test="fieldName == 'paymentAmountCompletionRate'">payment_amount_completion_rate</when>

            <!-- 结转合同相关字段 -->
            <when test="fieldName == 'carryoverContract'">carryover_contract</when>
            <when test="fieldName == 'carryoverGrossProfit'">carryover_gross_profit</when>
            <when test="fieldName == 'carryoverOverdueReceivable'">carryover_overdue_receivable</when>

            <!-- 新增合同相关字段 -->
            <when test="fieldName == 'newContract'">new_contract</when>
            <when test="fieldName == 'newGrossProfit'">new_gross_profit</when>
            <when test="fieldName == 'newOverdueReceivable'">new_overdue_receivable</when>
            <when test="fieldName == 'directSalesNewContract'">direct_sales_new_contract</when>
            <when test="fieldName == 'distributionNewContract'">distribution_new_contract</when>

            <!-- 退换货相关字段 -->
            <when test="fieldName == 'newReturnExchangeAmount'">new_return_exchange_amount</when>
            <when test="fieldName == 'newReturnExchangeProfit'">new_return_exchange_profit</when>

            <!-- 合计相关字段 -->
            <when test="fieldName == 'contractAmount'">contract_amount</when>
            <when test="fieldName == 'totalGrossProfit'">total_gross_profit</when>
            <when test="fieldName == 'totalGrossMargin'">total_gross_margin</when>
            <when test="fieldName == 'overdueReceivables'">overdue_receivables</when>

            <!-- 自有产品相关字段 -->
            <when test="fieldName == 'contractAmountFreeProduct'">contract_amount_free_product</when>
            <when test="fieldName == 'grossProfitFreeProduct'">gross_profit_free_product</when>
            <when test="fieldName == 'grossMarginFreeProduct'">gross_margin_free_product</when>

            <!-- 回款相关字段 -->
            <when test="fieldName == 'paymentAmount'">payment_amount</when>
            <when test="fieldName == 'paymentRate'">payment_rate</when>
            <when test="fieldName == 'paymentGrossProfit'">payment_gross_profit</when>

            <!-- 其他财务字段 -->
            <when test="fieldName == 'arrearsAmount'">arrears_amount</when>
            <when test="fieldName == 'newInvoice'">new_invoice</when>

            <!-- 默认字段 -->
            <otherwise>task_amount</otherwise>
        </choose>
    </sql>

    <!-- 多字段排序SQL片段 -->
    <sql id="orderByClause">
        <if test="query.sortFields != null and query.sortFields.size() > 0">
            ORDER BY
            <foreach collection="query.sortFields" item="sortField" separator=", ">
                <choose>
                    <!-- 任务和完成率相关字段 -->
                    <when test="sortField.fieldName == 'taskAmount'">task_amount</when>
                    <when test="sortField.fieldName == 'newContractCompletionRate'">new_contract_completion_rate</when>
                    <when test="sortField.fieldName == 'paymentAmountCompletionRate'">payment_amount_completion_rate</when>
                    <when test="sortField.fieldName == 'paymentRate'">payment_rate</when>

                    <!-- 结转相关字段 -->
                    <when test="sortField.fieldName == 'carryoverContract'">carryover_contract</when>
                    <when test="sortField.fieldName == 'carryoverGrossProfit'">carryover_gross_profit</when>
                    <when test="sortField.fieldName == 'carryoverOverdueReceivable'">carryover_overdue_receivable</when>

                    <!-- 新增相关字段 -->
                    <when test="sortField.fieldName == 'newContract'">new_contract</when>
                    <when test="sortField.fieldName == 'newGrossProfit'">new_gross_profit</when>
                    <when test="sortField.fieldName == 'newOverdueReceivable'">new_overdue_receivable</when>
                    <when test="sortField.fieldName == 'directSalesNewContract'">direct_sales_new_contract</when>
                    <when test="sortField.fieldName == 'distributionNewContract'">distribution_new_contract</when>
                    <when test="sortField.fieldName == 'newReturnExchangeAmount'">new_return_exchange_amount</when>
                    <when test="sortField.fieldName == 'newReturnExchangeProfit'">new_return_exchange_profit</when>

                    <!-- 合同和毛利相关字段 -->
                    <when test="sortField.fieldName == 'contractAmount'">contract_amount</when>
                    <when test="sortField.fieldName == 'totalGrossProfit'">total_gross_profit</when>
                    <when test="sortField.fieldName == 'totalGrossMargin'">total_gross_margin</when>
                    <when test="sortField.fieldName == 'overdueReceivables'">overdue_receivables</when>

                    <!-- 免费产品相关字段 -->
                    <when test="sortField.fieldName == 'contractAmountFreeProduct'">contract_amount_free_product</when>
                    <when test="sortField.fieldName == 'grossProfitFreeProduct'">gross_profit_free_product</when>
                    <when test="sortField.fieldName == 'grossMarginFreeProduct'">gross_margin_free_product</when>

                    <!-- 回款相关字段 -->
                    <when test="sortField.fieldName == 'paymentAmount'">payment_amount</when>
                    <when test="sortField.fieldName == 'paymentGrossProfit'">payment_gross_profit</when>
                    <when test="sortField.fieldName == 'arrearsAmount'">arrears_amount</when>
                    <when test="sortField.fieldName == 'newInvoice'">new_invoice</when>

                    <!-- 默认排序字段 -->
                    <otherwise>task_amount</otherwise>
                </choose>
                <choose>
                    <when test="sortField.direction != null and sortField.direction.toUpperCase() == 'DESC'">DESC</when>
                    <otherwise>ASC</otherwise>
                </choose>
            </foreach>
        </if>
    </sql>

    <sql id="Base_Column_List">
        id,annual,contract_number,contract_date,contract_status,final_user_name,
        industry_1,industry_2,industry_3,region_0,region_1,
        region_2,region_3,sign_company,sign_unit,contract_owner,
        contract_owner_id,department_1,department_2,department_3,original_sales_unit,original_sales_person,industry_region,
        material_code,product_model,product_pn,product_line_category_1,product_line_category_2,
        product_line_category_3,product_line_category_4,product_line_attribute_1,product_line_attribute_2,product_category_1,
        product_category_2,product_category_3,product_category_4,commerce_category,arrears_amount,
        overdue_receivables,new_contract,new_invoice,new_payment,carryover_amount,
        carryover_contract,total_gross_profit,gross_profit,carryover_gross_profit,carryover_overdue_receivable,
        new_gross_profit,new_overdue_receivable,contract_amount,contract_amount_free_product,gross_profit_free_product,
        payment_contract_amount,direct_sales_new_contract,distribution_new_contract,payment_gross_profit_unit,payment_gross_profit_person,
        performance_report_number,new_return_exchange_amount,new_return_exchange_profit,sales_settlement_ratio,department_settlement_ratio,
        team_settlement_ratio,new_gross_profit_product_line,carryover_gross_profit_product_line,new_return_exchange_profit_product_line,gross_profit_free_product_product_line,
        total_gross_profit_product_line,payment_gross_profit_product_line,payment_gross_profit_unit_product_line,payment_gross_profit_person_product_line,cloud_computing_factor,
        industrial_control_factor,business_type,create_time,update_time,create_user,update_user,
        del_flag
    </sql>



    <select id="listSignCompanyData" resultMap="ContractSummaryResultMap" >
        SELECT
        sign_company AS sign_company,
        SUM(carryover_contract) AS carryover_contract,
        SUM(carryover_gross_profit) AS carryover_gross_profit,
        SUM(carryover_overdue_receivable) AS carryover_overdue_receivable,

        SUM(new_contract) AS new_contract,
        SUM(new_gross_profit) AS new_gross_profit,
        SUM(new_overdue_receivable) as new_overdue_receivable,
        SUM(direct_sales_new_contract) as direct_sales_new_contract,
        SUM(distribution_new_contract) as distribution_new_contract,

        SUM(new_return_exchange_amount) AS new_return_exchange_amount,
        SUM(new_return_exchange_profit) AS new_return_exchange_profit,

        SUM(contract_amount) AS contract_amount,
        SUM(total_gross_profit) as total_gross_profit,
        CASE
            WHEN SUM(contract_amount) IS NULL OR SUM(contract_amount) = 0 THEN 0
            ELSE SUM(total_gross_profit) / SUM(contract_amount)
            END AS total_gross_margin,

        SUM(overdue_receivables) AS overdue_receivables,

        SUM(contract_amount_free_product) AS contract_amount_free_product,
        SUM(gross_profit_free_product) AS gross_profit_free_product,
        CASE
            WHEN SUM(contract_amount_free_product) IS NULL OR SUM(contract_amount_free_product) = 0 THEN 0
            ELSE SUM(gross_profit_free_product) / SUM(contract_amount_free_product)
            END AS gross_margin_free_product,

        SUM(payment_contract_amount) AS payment_amount,
        SUM(payment_gross_profit_unit) AS payment_gross_profit,

        SUM(arrears_amount) AS arrears_amount,
        SUM(new_invoice) AS new_invoice

        FROM contract_base_data_info
        <where>
            del_flag = 0
        </where>
        GROUP BY sign_company
        ORDER BY sign_company

    </select>


    <select id="listYearData" resultMap="ContractSummaryResultMap" >
        SELECT
        month(contract_base_data_info.`contract_date`) AS annual,
        SUM(carryover_contract) AS carryover_contract,
        SUM(carryover_gross_profit) AS carryover_gross_profit,
        SUM(carryover_overdue_receivable) AS carryover_overdue_receivable,

        SUM(new_contract) AS new_contract,
        SUM(new_gross_profit) AS new_gross_profit,
        SUM(new_overdue_receivable) as new_overdue_receivable,
        SUM(direct_sales_new_contract) as direct_sales_new_contract,
        SUM(distribution_new_contract) as distribution_new_contract,

        SUM(new_return_exchange_amount) AS new_return_exchange_amount,
        SUM(new_return_exchange_profit) AS new_return_exchange_profit,

        SUM(contract_amount) AS contract_amount,
        SUM(total_gross_profit) as total_gross_profit,
        CASE
            WHEN SUM(contract_amount) IS NULL OR SUM(contract_amount) = 0 THEN 0
            ELSE SUM(total_gross_profit) / SUM(contract_amount)
        END AS total_gross_margin,

        SUM(overdue_receivables) AS overdue_receivables,

        SUM(contract_amount_free_product) AS contract_amount_free_product,
        SUM(gross_profit_free_product) AS gross_profit_free_product,
        CASE
            WHEN SUM(contract_amount_free_product) IS NULL OR SUM(contract_amount_free_product) = 0 THEN 0
            ELSE SUM(gross_profit_free_product) / SUM(contract_amount_free_product)
        END AS gross_margin_free_product,

        SUM(payment_contract_amount) AS payment_amount,
        SUM(payment_gross_profit_unit) AS payment_gross_profit,

        SUM(arrears_amount) AS arrears_amount,
        SUM(new_invoice) AS new_invoice

        FROM contract_base_data_info
        <where>
            del_flag = 0 and annual = year(now())
        </where>
        GROUP BY annual, month(contract_base_data_info.`contract_date`)

    </select>


    <select id="listPlatformData" resultMap="ContractSummaryResultMap">
        select dept_id as platform_name,addition_profit_tasks as task_amount,
               CASE
                   WHEN addition_profit_tasks IS NULL OR addition_profit_tasks = 0 THEN 0
                   ELSE new_contract / addition_profit_tasks END as new_contract_completion_rate,
               CASE
                   WHEN addition_profit_tasks IS NULL OR addition_profit_tasks = 0 THEN 0
                   ELSE new_gross_profit / addition_profit_tasks END as payment_amount_completion_rate,
               carryover_contract,carryover_gross_profit,carryover_overdue_receivable,
               new_contract,new_gross_profit,new_overdue_receivable,direct_sales_new_contract,distribution_new_contract,
               new_return_exchange_amount,new_return_exchange_profit,contract_amount,total_gross_profit,total_gross_margin,
               overdue_receivables,contract_amount_free_product,gross_profit_free_product,gross_margin_free_product,
               payment_amount,payment_gross_profit,arrears_amount,new_invoice,
               CASE
                   WHEN addition_profit_tasks IS NULL OR addition_profit_tasks = 0 THEN 0
                   ELSE payment_amount / addition_profit_tasks END as payment_rate
        from annual_tasks
                 left join (
            SELECT department_1                                                                    as department_1,
                   SUM(carryover_contract)                                                         AS carryover_contract,
                   SUM(carryover_gross_profit)                                                     AS carryover_gross_profit,
                   SUM(carryover_overdue_receivable)                                               AS carryover_overdue_receivable,
                   SUM(new_contract)                                                               AS new_contract,
                   SUM(new_gross_profit)                                                           AS new_gross_profit,
                   SUM(new_overdue_receivable)                                                     as new_overdue_receivable,
                   SUM(direct_sales_new_contract)                                                  as direct_sales_new_contract,
                   SUM(distribution_new_contract)                                                  as distribution_new_contract,
                   SUM(new_return_exchange_amount)                                                 AS new_return_exchange_amount,
                   SUM(new_return_exchange_profit)                                                 AS new_return_exchange_profit,
                   SUM(contract_amount)                                                            AS contract_amount,
                   SUM(total_gross_profit)                                                         as total_gross_profit,
                   CASE
                       WHEN SUM(contract_amount) IS NULL OR SUM(contract_amount) = 0 THEN 0
                       ELSE SUM(total_gross_profit) / SUM(contract_amount) END                     AS total_gross_margin,
                   SUM(overdue_receivables)                                                        AS overdue_receivables,
                   SUM(contract_amount_free_product)                                               AS contract_amount_free_product,
                   SUM(gross_profit_free_product)                                                  AS gross_profit_free_product,
                   CASE
                       WHEN SUM(contract_amount_free_product) IS NULL OR SUM(contract_amount_free_product) = 0 THEN 0
                       ELSE SUM(gross_profit_free_product) / SUM(contract_amount_free_product) END AS gross_margin_free_product,
                   SUM(payment_contract_amount)                                                    AS payment_amount,
                   SUM(payment_gross_profit_unit)                                                  AS payment_gross_profit,
                   SUM(arrears_amount)                                                             AS arrears_amount,
                   SUM(new_invoice)                                                                AS new_invoice
            FROM contract_base_data_info
            where
            del_flag = 0
            GROUP BY department_1
        ) as contract_base_data_info on dept_id COLLATE utf8mb4_0900_ai_ci = department_1
        WHERE annual_tasks.del_flag = 0
          and annual_tasks.type = 0
        <if test="query.firstDeptId != null and query.firstDeptId != ''">
            AND dept_id = #{query.firstDeptId}
        </if>
        <include refid="orderByClause"/>
    </select>

    <select id="pageCompanyIndustryData" resultMap="ContractSummaryResultMap" >
        select dept_id as company_industry_division_name,addition_profit_tasks as task_amount,
               CASE
                   WHEN addition_profit_tasks IS NULL OR addition_profit_tasks = 0 THEN 0
                   ELSE new_contract / addition_profit_tasks END as new_contract_completion_rate,
               CASE
                   WHEN addition_profit_tasks IS NULL OR addition_profit_tasks = 0 THEN 0
                   ELSE new_gross_profit / addition_profit_tasks END as payment_amount_completion_rate,
               carryover_contract,carryover_gross_profit,carryover_overdue_receivable,
               new_contract,new_gross_profit,new_overdue_receivable,direct_sales_new_contract,distribution_new_contract,
               new_return_exchange_amount,new_return_exchange_profit,contract_amount,total_gross_profit,total_gross_margin,
               overdue_receivables,contract_amount_free_product,gross_profit_free_product,gross_margin_free_product,
               payment_amount,payment_gross_profit,arrears_amount,new_invoice,
               CASE
                   WHEN addition_profit_tasks IS NULL OR addition_profit_tasks = 0 THEN 0
                   ELSE payment_amount / addition_profit_tasks END as payment_rate
        from annual_tasks
                 left join (
            SELECT department_1                                                                    as department_1,
                   SUM(carryover_contract)                                                         AS carryover_contract,
                   SUM(carryover_gross_profit)                                                     AS carryover_gross_profit,
                   SUM(carryover_overdue_receivable)                                               AS carryover_overdue_receivable,
                   SUM(new_contract)                                                               AS new_contract,
                   SUM(new_gross_profit)                                                           AS new_gross_profit,
                   SUM(new_overdue_receivable)                                                     as new_overdue_receivable,
                   SUM(direct_sales_new_contract)                                                  as direct_sales_new_contract,
                   SUM(distribution_new_contract)                                                  as distribution_new_contract,
                   SUM(new_return_exchange_amount)                                                 AS new_return_exchange_amount,
                   SUM(new_return_exchange_profit)                                                 AS new_return_exchange_profit,
                   SUM(contract_amount)                                                            AS contract_amount,
                   SUM(total_gross_profit)                                                         as total_gross_profit,
                   CASE
                       WHEN SUM(contract_amount) IS NULL OR SUM(contract_amount) = 0 THEN 0
                       ELSE SUM(total_gross_profit) / SUM(contract_amount) END                     AS total_gross_margin,
                   SUM(overdue_receivables)                                                        AS overdue_receivables,
                   SUM(contract_amount_free_product)                                               AS contract_amount_free_product,
                   SUM(gross_profit_free_product)                                                  AS gross_profit_free_product,
                   CASE
                       WHEN SUM(contract_amount_free_product) IS NULL OR SUM(contract_amount_free_product) = 0 THEN 0
                       ELSE SUM(gross_profit_free_product) / SUM(contract_amount_free_product) END AS gross_margin_free_product,
                   SUM(payment_contract_amount)                                                    AS payment_amount,
                   SUM(payment_gross_profit_unit)                                                  AS payment_gross_profit,
                   SUM(arrears_amount)                                                             AS arrears_amount,
                   SUM(new_invoice)                                                                AS new_invoice
            FROM contract_base_data_info
            where
            del_flag = 0

            GROUP BY department_1
        ) as contract_base_data_info on dept_id COLLATE utf8mb4_0900_ai_ci = department_1
        WHERE annual_tasks.del_flag = 0
          and annual_tasks.type = 5
        <if test="query.firstDeptId != null and query.firstDeptId != ''">
            AND dept_id = #{query.firstDeptId}
        </if>
        <include refid="orderByClause"/>
    </select>


    <select id="pageRegionalMarketingData" resultMap="ContractSummaryResultMap" >
        select dept_id as regional_marketing_unit_name,addition_profit_tasks as task_amount,
               department_2,
               CASE
                   WHEN addition_profit_tasks IS NULL OR addition_profit_tasks = 0 THEN 0
                   ELSE new_contract / addition_profit_tasks END as new_contract_completion_rate,
               CASE
                   WHEN addition_profit_tasks IS NULL OR addition_profit_tasks = 0 THEN 0
                   ELSE new_gross_profit / addition_profit_tasks END as payment_amount_completion_rate,
               carryover_contract,carryover_gross_profit,carryover_overdue_receivable,
               new_contract,new_gross_profit,new_overdue_receivable,direct_sales_new_contract,distribution_new_contract,
               new_return_exchange_amount,new_return_exchange_profit,contract_amount,total_gross_profit,total_gross_margin,
               overdue_receivables,contract_amount_free_product,gross_profit_free_product,gross_margin_free_product,
               payment_amount,payment_gross_profit,arrears_amount,new_invoice,
               CASE
                   WHEN addition_profit_tasks IS NULL OR addition_profit_tasks = 0 THEN 0
                   ELSE payment_amount / addition_profit_tasks END as payment_rate
        from annual_tasks
                 left join (
            SELECT department_3                                                                    as department_3,
                   max(department_2)                                                               as department_2,
                   SUM(carryover_contract)                                                         AS carryover_contract,
                   SUM(carryover_gross_profit)                                                     AS carryover_gross_profit,
                   SUM(carryover_overdue_receivable)                                               AS carryover_overdue_receivable,
                   SUM(new_contract)                                                               AS new_contract,
                   SUM(new_gross_profit)                                                           AS new_gross_profit,
                   SUM(new_overdue_receivable)                                                     as new_overdue_receivable,
                   SUM(direct_sales_new_contract)                                                  as direct_sales_new_contract,
                   SUM(distribution_new_contract)                                                  as distribution_new_contract,
                   SUM(new_return_exchange_amount)                                                 AS new_return_exchange_amount,
                   SUM(new_return_exchange_profit)                                                 AS new_return_exchange_profit,
                   SUM(contract_amount)                                                            AS contract_amount,
                   SUM(total_gross_profit)                                                         as total_gross_profit,
                   CASE
                       WHEN SUM(contract_amount) IS NULL OR SUM(contract_amount) = 0 THEN 0
                       ELSE SUM(total_gross_profit) / SUM(contract_amount) END                     AS total_gross_margin,
                   SUM(overdue_receivables)                                                        AS overdue_receivables,
                   SUM(contract_amount_free_product)                                               AS contract_amount_free_product,
                   SUM(gross_profit_free_product)                                                  AS gross_profit_free_product,
                   CASE
                       WHEN SUM(contract_amount_free_product) IS NULL OR SUM(contract_amount_free_product) = 0 THEN 0
                       ELSE SUM(gross_profit_free_product) / SUM(contract_amount_free_product) END AS gross_margin_free_product,
                   SUM(payment_contract_amount)                                                    AS payment_amount,
                   SUM(payment_gross_profit_unit)                                                  AS payment_gross_profit,
                   SUM(arrears_amount)                                                             AS arrears_amount,
                   SUM(new_invoice)                                                                AS new_invoice
            FROM contract_base_data_info
        where
            del_flag = 0
            GROUP BY department_3
        ) as contract_base_data_info on dept_id COLLATE utf8mb4_0900_ai_ci = department_3
        WHERE annual_tasks.del_flag = 0
          and annual_tasks.type = 4
        <if test="query.secondDeptId != null and query.secondDeptId != ''">
            AND department_2 = #{query.secondDeptId}
        </if>
        <include refid="orderByClause"/>
    </select>


    <select id="pageRegionalIndustryData" resultMap="ContractSummaryResultMap" >
        select dept_id as regional_marketing_unit_name,addition_profit_tasks as task_amount,department_2,
               CASE
                   WHEN addition_profit_tasks IS NULL OR addition_profit_tasks = 0 THEN 0
                   ELSE new_contract / addition_profit_tasks END as new_contract_completion_rate,
               CASE
                   WHEN addition_profit_tasks IS NULL OR addition_profit_tasks = 0 THEN 0
                   ELSE new_gross_profit / addition_profit_tasks END as payment_amount_completion_rate,
               carryover_contract,carryover_gross_profit,carryover_overdue_receivable,
               new_contract,new_gross_profit,new_overdue_receivable,direct_sales_new_contract,distribution_new_contract,
               new_return_exchange_amount,new_return_exchange_profit,contract_amount,total_gross_profit,total_gross_margin,
               overdue_receivables,contract_amount_free_product,gross_profit_free_product,gross_margin_free_product,
               payment_amount,payment_gross_profit,arrears_amount,new_invoice,
               CASE
                   WHEN addition_profit_tasks IS NULL OR addition_profit_tasks = 0 THEN 0
                   ELSE payment_amount / addition_profit_tasks END as payment_rate
        from annual_tasks
                 left join  (
            SELECT department_3                                                                    as department_3,
                   max(department_2)                                                               as department_2,
                   SUM(carryover_contract)                                                         AS carryover_contract,
                   SUM(carryover_gross_profit)                                                     AS carryover_gross_profit,
                   SUM(carryover_overdue_receivable)                                               AS carryover_overdue_receivable,
                   SUM(new_contract)                                                               AS new_contract,
                   SUM(new_gross_profit)                                                           AS new_gross_profit,
                   SUM(new_overdue_receivable)                                                     as new_overdue_receivable,
                   SUM(direct_sales_new_contract)                                                  as direct_sales_new_contract,
                   SUM(distribution_new_contract)                                                  as distribution_new_contract,
                   SUM(new_return_exchange_amount)                                                 AS new_return_exchange_amount,
                   SUM(new_return_exchange_profit)                                                 AS new_return_exchange_profit,
                   SUM(contract_amount)                                                            AS contract_amount,
                   SUM(total_gross_profit)                                                         as total_gross_profit,
                   CASE
                       WHEN SUM(contract_amount) IS NULL OR SUM(contract_amount) = 0 THEN 0
                       ELSE SUM(total_gross_profit) / SUM(contract_amount) END                     AS total_gross_margin,
                   SUM(overdue_receivables)                                                        AS overdue_receivables,
                   SUM(contract_amount_free_product)                                               AS contract_amount_free_product,
                   SUM(gross_profit_free_product)                                                  AS gross_profit_free_product,
                   CASE
                       WHEN SUM(contract_amount_free_product) IS NULL OR SUM(contract_amount_free_product) = 0 THEN 0
                       ELSE SUM(gross_profit_free_product) / SUM(contract_amount_free_product) END AS gross_margin_free_product,
                   SUM(payment_contract_amount)                                                    AS payment_amount,
                   SUM(payment_gross_profit_unit)                                                  AS payment_gross_profit,
                   SUM(arrears_amount)                                                             AS arrears_amount,
                   SUM(new_invoice)                                                                AS new_invoice
            FROM contract_base_data_info
        where
            del_flag = 0
            GROUP BY department_3
        ) as contract_base_data_info on dept_id COLLATE utf8mb4_0900_ai_ci = department_3
        WHERE annual_tasks.del_flag = 0
          and annual_tasks.type = 1
        <if test="query.secondDeptId != null and query.secondDeptId != ''">
            AND department_2 = #{query.secondDeptId}
        </if>
        <include refid="orderByClause"/>
    </select>


    <select id="pageTeamData" resultMap="ContractSummaryResultMap" >
        SELECT
        contract_owner as sales_person_name,
        contract_owner_id as sales_person_id,
        SUM(carryover_contract) AS carryover_contract,
        SUM(carryover_gross_profit) AS carryover_gross_profit,
        SUM(carryover_overdue_receivable) AS carryover_overdue_receivable,

        SUM(new_contract) AS new_contract,
        SUM(new_gross_profit) AS new_gross_profit,
        SUM(new_overdue_receivable) as new_overdue_receivable,
        SUM(direct_sales_new_contract) as direct_sales_new_contract,
        SUM(distribution_new_contract) as distribution_new_contract,

        SUM(new_return_exchange_amount) AS new_return_exchange_amount,
        SUM(new_return_exchange_profit) AS new_return_exchange_profit,

        SUM(contract_amount) AS contract_amount,
        SUM(total_gross_profit) as total_gross_profit,
        CASE
            WHEN SUM(contract_amount) IS NULL OR SUM(contract_amount) = 0 THEN 0
            ELSE SUM(total_gross_profit) / SUM(contract_amount)
            END AS total_gross_margin,

        SUM(overdue_receivables) AS overdue_receivables,

        SUM(contract_amount_free_product) AS contract_amount_free_product,
        SUM(gross_profit_free_product) AS gross_profit_free_product,
        CASE
            WHEN SUM(contract_amount_free_product) IS NULL OR SUM(contract_amount_free_product) = 0 THEN 0
            ELSE SUM(gross_profit_free_product) / SUM(contract_amount_free_product)
            END AS gross_margin_free_product,

        SUM(payment_contract_amount) AS payment_amount,
        SUM(payment_gross_profit_unit) AS payment_gross_profit,

        SUM(arrears_amount) AS arrears_amount,
        SUM(new_invoice) AS new_invoice

        FROM contract_base_data_info
        <where>
            del_flag = 0
            <if test="query.firstDeptId != null and query.firstDeptId != ''">
                AND department_1 = #{query.firstDeptId}
            </if>
            <if test="query.secondDeptId != null and query.secondDeptId != ''">
                AND department_2 = #{query.secondDeptId}
            </if>
        </where>
        GROUP BY contract_owner_id,contract_owner
        <include refid="orderByClause"/>
    </select>


    <select id="pageSalesData" resultMap="ContractSummaryResultMap" >
        select sales_person_id,sales_person_name,addition_profit_tasks,carryover_contract,carryover_gross_profit,carryover_overdue_receivable,new_contract,new_gross_profit,new_overdue_receivable,direct_sales_new_contract,distribution_new_contract
        new_return_exchange_amount,new_return_exchange_profit,contract_amount,total_gross_profit,total_gross_margin,overdue_receivables,contract_amount_free_product,gross_profit_free_product,gross_margin_free_product,
        payment_amount,payment_gross_profit,arrears_amount,new_invoice
        from annual_tasks
        left join (
        SELECT
        department_1 as department_1,
        department_2 as department_2,
        contract_owner_id as sales_person_id,
        contract_owner as sales_person_name,
        SUM(carryover_contract) AS carryover_contract,
        SUM(carryover_gross_profit) AS carryover_gross_profit,
        SUM(carryover_overdue_receivable) AS carryover_overdue_receivable,

        SUM(new_contract) AS new_contract,
        SUM(new_gross_profit) AS new_gross_profit,
        SUM(new_overdue_receivable) as new_overdue_receivable,
        SUM(direct_sales_new_contract) as direct_sales_new_contract,
        SUM(distribution_new_contract) as distribution_new_contract,

        SUM(new_return_exchange_amount) AS new_return_exchange_amount,
        SUM(new_return_exchange_profit) AS new_return_exchange_profit,

        SUM(contract_amount) AS contract_amount,
        SUM(total_gross_profit) as total_gross_profit,
        CASE
        WHEN SUM(contract_amount) IS NULL OR SUM(contract_amount) = 0 THEN 0
        ELSE SUM(total_gross_profit) / SUM(contract_amount)
        END AS total_gross_margin,

        SUM(overdue_receivables) AS overdue_receivables,

        SUM(contract_amount_free_product) AS contract_amount_free_product,
        SUM(gross_profit_free_product) AS gross_profit_free_product,
        CASE
        WHEN SUM(contract_amount_free_product) IS NULL OR SUM(contract_amount_free_product) = 0 THEN 0
        ELSE SUM(gross_profit_free_product) / SUM(contract_amount_free_product)
        END AS gross_margin_free_product,

        SUM(payment_contract_amount) AS payment_amount,
        SUM(payment_gross_profit_unit) AS payment_gross_profit,
        SUM(arrears_amount) AS arrears_amount,
        SUM(new_invoice) AS new_invoice
        FROM contract_base_data_info
        where
        del_flag = 0
        GROUP BY contract_owner_id,contract_owner,department_1,department_2
        ) as contract_base_data_info on  person_id COLLATE utf8mb4_0900_ai_ci = contract_base_data_info.sales_person_id
        where annual_tasks.year = year(now()) and annual_tasks.del_flag = 0 and annual_tasks.type = 3
            <if test="query.firstDeptId != null and query.firstDeptId != ''">
                AND contract_base_data_info.department_1 = #{query.firstDeptId}
            </if>
            <if test="query.secondDeptId != null and query.secondDeptId != ''">
                AND contract_base_data_info.department_2 = #{query.secondDeptId}
            </if>
        <include refid="orderByClause"/>
    </select>


    <select id="pageProductLineData" resultMap="ContractSummaryResultMap" >
        SELECT
        product_line_category_1 AS product_line_category_1,
        product_line_category_2 AS product_line_category_2,
        product_line_category_3 AS product_line_category_3,
        SUM(carryover_contract) AS carryover_contract,
        SUM(carryover_gross_profit) AS carryover_gross_profit,
        SUM(carryover_overdue_receivable) AS carryover_overdue_receivable,

        SUM(new_contract) AS new_contract,
        SUM(new_gross_profit) AS new_gross_profit,
        SUM(new_overdue_receivable) as new_overdue_receivable,
        SUM(direct_sales_new_contract) as direct_sales_new_contract,
        SUM(distribution_new_contract) as distribution_new_contract,

        SUM(new_return_exchange_amount) AS new_return_exchange_amount,
        SUM(new_return_exchange_profit) AS new_return_exchange_profit,

        SUM(contract_amount) AS contract_amount,
        SUM(total_gross_profit) as total_gross_profit,
        CASE
            WHEN SUM(contract_amount) IS NULL OR SUM(contract_amount) = 0 THEN 0
            ELSE SUM(total_gross_profit) / SUM(contract_amount)
            END AS total_gross_margin,

        SUM(overdue_receivables) AS overdue_receivables,

        SUM(contract_amount_free_product) AS contract_amount_free_product,
        SUM(gross_profit_free_product) AS gross_profit_free_product,
        CASE
            WHEN SUM(contract_amount_free_product) IS NULL OR SUM(contract_amount_free_product) = 0 THEN 0
            ELSE SUM(gross_profit_free_product) / SUM(contract_amount_free_product)
            END AS gross_margin_free_product,

        SUM(payment_contract_amount) AS payment_amount,
        SUM(payment_gross_profit_unit) AS payment_gross_profit,

        SUM(arrears_amount) AS arrears_amount,
        SUM(new_invoice) AS new_invoice

        FROM contract_base_data_info
        <where>
            del_flag = 0
            <if test="query.firstDeptId != null and query.firstDeptId != ''">
                AND department_1 = #{query.firstDeptId}
            </if>
            <if test="query.secondDeptId != null and query.secondDeptId != ''">
                AND department_2 = #{query.secondDeptId}
            </if>
        </where>
        GROUP BY product_line_category_1,product_line_category_2,product_line_category_3
        <include refid="orderByClause"/>
    </select>


    <select id="pageTeamDataWithSql" resultMap="ContractSummaryResultMap">
        SELECT
            cbdi.contract_owner_id as sales_person_id,
            cbdi.department_1 as company_industry_division_name,
            team_info.team_name as regional_marketing_unit_name,
            team_info.addition_profit_tasks as task_amount,
            SUM(cbdi.carryover_contract) AS carryover_contract,
            SUM(cbdi.carryover_gross_profit) AS carryover_gross_profit,
            SUM(cbdi.carryover_overdue_receivable) AS carryover_overdue_receivable,
            SUM(cbdi.new_contract) AS new_contract,
            SUM(cbdi.new_gross_profit) AS new_gross_profit,
            SUM(cbdi.new_overdue_receivable) as new_overdue_receivable,
            SUM(cbdi.direct_sales_new_contract) as direct_sales_new_contract,
            SUM(cbdi.distribution_new_contract) as distribution_new_contract,
            SUM(cbdi.new_return_exchange_amount) AS new_return_exchange_amount,
            SUM(cbdi.new_return_exchange_profit) AS new_return_exchange_profit,
            SUM(cbdi.contract_amount) AS contract_amount,
            SUM(cbdi.total_gross_profit) as total_gross_profit,
            CASE
                WHEN SUM(cbdi.contract_amount) IS NULL OR SUM(cbdi.contract_amount) = 0 THEN 0
                ELSE SUM(cbdi.total_gross_profit) / SUM(cbdi.contract_amount)
                END AS total_gross_margin,
            SUM(cbdi.overdue_receivables) AS overdue_receivables,
            SUM(cbdi.contract_amount_free_product) AS contract_amount_free_product,
            SUM(cbdi.gross_profit_free_product) AS gross_profit_free_product,
            CASE
                WHEN SUM(cbdi.contract_amount_free_product) IS NULL OR SUM(cbdi.contract_amount_free_product) = 0 THEN 0
                ELSE SUM(cbdi.gross_profit_free_product) / SUM(cbdi.contract_amount_free_product)
                END AS gross_margin_free_product,
            SUM(cbdi.payment_contract_amount) AS payment_amount,
            SUM(cbdi.payment_gross_profit_unit) AS payment_gross_profit,
            SUM(cbdi.arrears_amount) AS arrears_amount,
            SUM(cbdi.new_invoice) AS new_invoice
        FROM contract_base_data_info cbdi
                 INNER JOIN (
            SELECT DISTINCT
                stm.person_id,
                st.team_name,
                at.addition_profit_tasks
            FROM annual_tasks at
            inner join sale_team st on at.sale_team_id = st.id
                INNER JOIN sale_team_member stm ON st.id = stm.team_id
            WHERE at.type = 2
              AND at.del_flag = 0
              AND stm.del_flag = 0 and at.year = YEAR(now())
        ) team_info ON cbdi.contract_owner_id COLLATE utf8mb4_0900_ai_ci = team_info.person_id
        <where>
            cbdi.del_flag = 0
            <if test="query.firstDeptId != null and query.firstDeptId != ''">
                AND department_1 = #{query.firstDeptId}
            </if>
            <if test="query.secondDeptId != null and query.secondDeptId != ''">
                AND department_2 = #{query.secondDeptId}
            </if>
        </where>
        GROUP BY cbdi.contract_owner_id, cbdi.department_1, team_info.team_name, team_info.addition_profit_tasks
        <include refid="orderByClause"/>
    </select>

</mapper>
