package com.topsec.crm.stats.api.entity.business;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Schema(description = "经营数据信息")
@Data
@EqualsAndHashCode(callSuper = false)
public class ContractBaseDataInfoVO implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 年度
     */
    @Schema(description = "年度")
    private Integer annual;

    /**
     * 合同号
     */
    @Schema(description = "合同号")
    private String contractNumber;

    /**
     * 合同时间
     */
    @Schema(description = "合同时间")
    private LocalDate contractDate;

    /**
     * 合同状态
     */
    @Schema(description = "合同状态")
    private String contractStatus;

    /**
     * 最终用户名称
     */
    @Schema(description = "最终用户名称")
    private String finalUserName;

    /**
     * 行业1
     */
    @Schema(description = "行业1")
    private String industry1;

    /**
     * 行业2
     */
    @Schema(description = "行业2")
    private String industry2;

    /**
     * 行业3
     */
    @Schema(description = "行业3")
    private String industry3;

    /**
     * 大区0
     */
    @Schema(description = "大区0")
    private String region0;

    /**
     * 大区1
     */
    @Schema(description = "大区1")
    private String region1;

    /**
     * 大区2
     */
    @Schema(description = "大区2")
    private String region2;

    /**
     * 大区3
     */
    @Schema(description = "大区3")
    private String region3;

    /**
     * 签订公司
     */
    @Schema(description = "签订公司")
    private String signCompany;

    /**
     * 签订单位
     */
    @Schema(description = "签订单位")
    private String signUnit;

    /**
     * 合同负责人
     */
    @Schema(description = "合同负责人")
    private String contractOwner;

    /**
     * 合同负责人员id
     */
    @Schema(description = "合同负责人员id")
    private String contractOwnerId;

    /**
     * 一级部门
     */
    @Schema(description = "一级部门")
    private String department1;

    /**
     * 二级部门
     */
    @Schema(description = "二级部门")
    private String department2;


    /**
     * 三级部门
     */
    @Schema(description = "三级部门")
    private String department3;

    /**
     * 原销售单位
     */
    @Schema(description = "原销售单位")
    private String originalSalesUnit;

    /**
     * 原销售人员
     */
    @Schema(description = "原销售人员")
    private String originalSalesPerson;

    /**
     * 行业区域
     */
    @Schema(description = "行业区域")
    private String industryRegion;

    /**
     * 物料代码
     */
    @Schema(description = "物料代码")
    private String materialCode;

    /**
     * 产品型号
     */
    @Schema(description = "产品型号")
    private String productModel;

    /**
     * 产品pn
     */
    @Schema(description = "产品pn")
    private String productPn;

    /**
     * 产品线分类1
     */
    @Schema(description = "产品线分类1")
    private String productLineCategory1;

    /**
     * 产品线分类2
     */
    @Schema(description = "产品线分类2")
    private String productLineCategory2;

    /**
     * 产品线分类3
     */
    @Schema(description = "产品线分类3")
    private String productLineCategory3;

    /**
     * 产品线分类4
     */
    @Schema(description = "产品线分类4")
    private String productLineCategory4;

    /**
     * 产品线属性1
     */
    @Schema(description = "产品线属性1")
    private String productLineAttribute1;

    /**
     * 产品线属性2
     */
    @Schema(description = "产品线属性2")
    private String productLineAttribute2;

    /**
     * 产品分类1
     */
    @Schema(description = "产品分类1")
    private String productCategory1;

    /**
     * 产品分类2
     */
    @Schema(description = "产品分类2")
    private String productCategory2;

    /**
     * 产品分类3
     */
    @Schema(description = "产品分类3")
    private String productCategory3;

    /**
     * 产品分类4
     */
    @Schema(description = "产品分类4")
    private String productCategory4;

    /**
     * 商务部分类
     */
    @Schema(description = "商务部分类")
    private String commerceCategory;

    /**
     * 欠款金额
     */
    @Schema(description = "欠款金额")
    private BigDecimal arrearsAmount;

    /**
     * 合计超期应收
     */
    @Schema(description = "欠款金额")
    private BigDecimal overdueReceivables;

    /**
     * 新增合同
     */
    @Schema(description = "新增合同")
    private BigDecimal newContract;

    /**
     * 开票金额
     */
    @Schema(description = "开票金额")
    private BigDecimal newInvoice;

    /**
     * 回款金额
     */
    @Schema(description = "回款金额")
    private BigDecimal newPayment;

    /**
     * 结转金额
     */
    @Schema(description = "结转金额")
    private BigDecimal carryoverAmount;

    /**
     * 结转合同
     */
    @Schema(description = "结转合同")
    private BigDecimal carryoverContract;

    /**
     * 合计合同毛利
     */
    @Schema(description = "合计合同毛利")
    private BigDecimal totalGrossProfit;

    /**
     * 合同毛利
     */
    @Schema(description = "合同毛利")
    private BigDecimal grossProfit;

    /**
     * 结转合同毛利
     */
    @Schema(description = "结转合同毛利")
    private BigDecimal carryoverGrossProfit;

    /**
     * 结转超期应收
     */
    @Schema(description = "结转超期应收")
    private BigDecimal carryoverOverdueReceivable;

    /**
     * 新增合同毛利
     */
    @Schema(description = "新增合同毛利")
    private BigDecimal newGrossProfit;

    /**
     * 新增超期应收
     */
    @Schema(description = "新增超期应收")
    private BigDecimal newOverdueReceivable;

    /**
     * 合计合同金额
     */
    @Schema(description = "合计合同金额")
    private BigDecimal contractAmount;

    /**
     * 合同金额-自由产品
     */
    @Schema(description = "合同金额-自由产品")
    private BigDecimal contractAmountFreeProduct;

    /**
     * 合同毛利-自由产品
     */
    @Schema(description = "合同毛利-自由产品")
    private BigDecimal grossProfitFreeProduct;

    /**
     * 回款合同金额
     */
    @Schema(description = "回款合同金额")
    private BigDecimal paymentContractAmount;

    /**
     * 直销新增合同
     */
    @Schema(description = "直销新增合同")
    private BigDecimal directSalesNewContract;

    /**
     * 分销新增合同
     */
    @Schema(description = "分销新增合同")
    private BigDecimal distributionNewContract;

    /**
     * 回款毛利销售单位
     */
    @Schema(description = "回款毛利销售单位")
    private BigDecimal paymentGrossProfitUnit;

    /**
     * 回款毛利销售人员
     */
    @Schema(description = "回款毛利销售人员")
    private BigDecimal paymentGrossProfitPerson;

    /**
     * 业绩上报单号
     */
    @Schema(description = "业绩上报单号")
    private String performanceReportNumber;

    /**
     * 新增退货换货额
     */
    @Schema(description = "新增退货换货额")
    private BigDecimal newReturnExchangeAmount;

    /**
     * 新增退货换货毛利
     */
    @Schema(description = "新增退货换货毛利")
    private BigDecimal newReturnExchangeProfit;

    /**
     * na 销售结算比例
     */
    @Schema(description = "na 销售结算比例")
    private BigDecimal salesSettlementRatio;

    /**
     * na 部门结算比例
     */
    @Schema(description = "na 部门结算比例")
    private BigDecimal departmentSettlementRatio;

    /**
     * na 团队结算比例
     */
    @Schema(description = "na 团队结算比例")
    private BigDecimal teamSettlementRatio;

    /**
     * 新增合同毛利-产品线
     */
    @Schema(description = "新增合同毛利-产品线")
    private BigDecimal newGrossProfitProductLine;

    /**
     * 结转合同毛利-产品线
     */
    @Schema(description = "结转合同毛利-产品线")
    private BigDecimal carryoverGrossProfitProductLine;

    /**
     * 新增退换货毛利-产品线
     */
    @Schema(description = "新增退换货毛利-产品线")
    private BigDecimal newReturnExchangeProfitProductLine;

    /**
     * 合同毛利-自由产品-产品线
     */
    @Schema(description = "合同毛利-自由产品-产品线")
    private BigDecimal grossProfitFreeProductProductLine;

    /**
     * 合计合同毛利-产品线
     */
    @Schema(description = "合计合同毛利-产品线")
    private BigDecimal totalGrossProfitProductLine;

    /**
     * 回款毛利-产品线
     */
    @Schema(description = "回款毛利-产品线")
    private BigDecimal paymentGrossProfitProductLine;

    /**
     * 回款毛利销售单位-产品线
     */
    @Schema(description = "回款毛利销售单位-产品线")
    private BigDecimal paymentGrossProfitUnitProductLine;

    /**
     * 回款毛利销售人员-产品线
     */
    @Schema(description = "回款毛利销售人员-产品线")
    private BigDecimal paymentGrossProfitPersonProductLine;

    /**
     * 云计算系数
     */
    @Schema(description = "云计算系数")
    private BigDecimal cloudComputingFactor;

    /**
     * 工控系数
     */
    @Schema(description = "工控系数")
    private BigDecimal industrialControlFactor;

    /**
     * 经营数据类型
     */
    @Schema(description = "经营数据类型")
    private String businessType;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}
