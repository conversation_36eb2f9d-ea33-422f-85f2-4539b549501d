package com.topsec.crm.agent.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.topsec.crm.agent.api.entity.CrmAgentExamineSdQuery;
import com.topsec.crm.agent.api.entity.CrmAgentExamineSdVO;
import com.topsec.crm.agent.core.entity.CrmAgentExamineSd;

import java.util.List;

public interface CrmAgentExamineSdMapper extends BaseMapper<CrmAgentExamineSd> {
    List<CrmAgentExamineSdVO> selectCrmAgentExamineSdList(CrmAgentExamineSdQuery crmAgentExamineSdQuery);

    List<CrmAgentExamineSdVO> selectCrmAgentExamineSdHistory(String hostId);

    CrmAgentExamineSdVO selectAgentExamineSdById(String id);
}
