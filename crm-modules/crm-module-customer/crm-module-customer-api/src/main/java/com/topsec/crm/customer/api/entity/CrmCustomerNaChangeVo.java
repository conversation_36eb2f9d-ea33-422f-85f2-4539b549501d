package com.topsec.crm.customer.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.topsec.crm.framework.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 【NA客户指名记录】对象 crm_customer_na_change
 * 
 * <AUTHOR>
 * @date 2023-01-09
 */
@Data
@Schema(description = "NA客户指名记录")
public class CrmCustomerNaChangeVo
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @Schema(description = "ID")
    private String id;

    /** 客户ID */
    @Schema(description = "客户ID")
    private String customerId;

    /** 记录状态（1：新增指名、2：变更指名、3：删除指名） */
    @Schema(description = "记录状态（1：新增指名、2：变更指名、3：删除指名）")
    private Integer status;

    /** 原指名人 */
    @Schema(description = "原指名人")
    private String oldNaPerson;

    /** 新指名人 */
    @Schema(description = "新指名人")
    private String newNaPerson;

    /** 操作人 */
    @Schema(description = "操作人")
    private String operator;

    /** 操作时间 */
    @Schema(description = "操作时间")
    private LocalDateTime operatorTime;

    //业务字段
    @Schema(description = "关键词（姓名或工号）")
    private String keyword;
}
